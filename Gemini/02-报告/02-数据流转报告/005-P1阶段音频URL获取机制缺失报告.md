# 数据溯源报告：P1阶段音频URL获取机制缺失

## 1. 溯源目标

本次溯源的目标是，当 `AudioStatusManager` 通过轮询成功确认TTS音频（特别是P1阶段的补充音频）在服务器端生成后，客户端（`WordResultView`）如何获取并更新包含可播放 `audioUrl` 的 `wordData` 数据。

## 2. 溯源路径

1.  **起点：视图呈现 (`.onAppear`)**
    *   在 `WordResultView.swift` 中，当视图出现时，`.onAppear` 修饰符会调用 `startAudioStatusMonitoring()` 方法。

2.  **检查与启动轮询**
    *   `startAudioStatusMonitoring()` 方法检查传入的 `wordData` 对象的 `phoneticSymbols` 数组。
    *   如果发现有任何一个音标的 `audioUrl` 为 `nil` 或为空，它就会调用 `audioStatusManager.startMonitoring(...)` 来启动后台轮询，以检查音频的生成状态。

3.  **后台轮询**
    *   `AudioStatusManager` 开始按照预设的间隔（传统轮询或P1阶段轮询）调用 `audioAPIAdapter.getAudioStatus(...)`，向后端查询音频状态。

4.  **轮询成功**
    *   当后端返回音频已就绪的状态（如 `.completed` 或 `.wordReady`）时，`AudioStatusManager` 会执行以下操作：
        *   将其内部的 `@Published var status` 属性更新为 `.ready`。
        *   调用 `stopPolling()` 停止定时器，结束轮询。

5.  **数据流中断点**
    *   **关键发现**：`AudioStatusManager` 在成功后，仅仅更新了自身的内部状态。
    *   `WordResultView` **没有**任何代码来监听 `audioStatusManager.status` 的变化。例如，缺少类似 `.onChange(of: audioStatusManager.status)` 的处理逻辑。
    *   因此，`WordResultView` 对 `AudioStatusManager` 的成功状态一无所知。

6.  **终点：UI无更新**
    *   由于 `WordResultView` 不知道音频已准备好，它不会触发任何数据重新加载的逻辑。
    *   UI继续显示旧的 `wordData`，其中的 `audioUrl` 仍然是 `nil`。
    *   结果是，即使用户等待了足够长的时间，并且音频已经在服务器上准备就绪，UI上的播放按钮依然无法播放音频，因为客户端的数据模型没有被更新。

## 3. 单一真相来源 (Single Source of Truth)

*   **音频就绪状态的SSOT**：是**后端API**。`AudioStatusManager` 成功地从这个SSOT获取了信息。
*   **UI播放能力的SSOT**：是 `WordResultView` 中持有的 **`wordData` 状态副本**。UI上的播放逻辑完全依赖于此副本中的 `audioUrl` 是否有值。

**核心问题**在于，从第一个SSOT（后端API）获取的状态变化，未能传递并更新第二个SSOT（视图的状态副本）。

## 4. 问题与发现

**数据同步机制缺失**：

`AudioStatusManager` 与 `WordResultView` 之间存在明显的**数据流鸿沟**。`AudioStatusManager` 作为一个状态报告器，其报告（`status` 属性的变化）没有被信息的消费者（`WordResultView`）接收和处理。这导致轮询机制的目的（在音频就绪后使其可播放）无法达成，功能链路中断。

## 5. 优化建议

为了修复这个数据流中断的问题，必须在 `WordResultView` 中建立一个机制，来响应 `AudioStatusManager` 的状态变化，并触发后续的数据刷新操作。

**建议方案：使用 `.onChange` 修饰符**

在 `WordResultView` 的 `body` 中，添加对 `audioStatusManager.status` 的监听。

```swift
.onChange(of: audioStatusManager.status) { newStatus in
    if newStatus == .ready {
        // 当音频状态变为 ready 时，意味着服务器端已生成好 audioUrl
        // 此时需要重新拉取单词数据以获取最新的 audioUrl
        print("✅ Audio status is ready. Reloading word data for: \(wordData.word)")
        reloadWordData() 
    }
}

// ...

/// 重新加载单词数据的方法
private func reloadWordData() {
    // 这里应该调用一个在 ViewModel 或 Service 中定义的方法，
    // 例如 wordService.fetchWord(word: wordData.word)，
    // 然后用返回的新数据更新 self.wordData。
    //
    // 伪代码如下：
    // Task {
    //     if let updatedWordData = await wordService.fetchWord(word: self.wordData.word) {
    //         self.wordData = updatedWordData
    //     }
    // }
}
```

通过这种方式，当轮询成功并将状态置为 `.ready` 时，`WordResultView` 将能够捕获到这一变化，并执行必要的数据重新加载操作，从而获取到可播放的 `audioUrl`，修复功能链路。
