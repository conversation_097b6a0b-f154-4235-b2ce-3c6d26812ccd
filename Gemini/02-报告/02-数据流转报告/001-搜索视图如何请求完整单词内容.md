# 数据溯源报告：搜索视图如何请求完整单词内容

## 1. 溯源目标

本次溯源的目标是：搜索视图如何将用户输入的单词发起请求完整单词内容。

## 2. 溯源路径

数据从用户在 `SearchView` 中的输入开始，经过 `SearchViewModel` 的处理，通过 `SearchService` 协调，最终由 `WordAPIAdapter` 和 `APIClient` 发起网络请求获取完整单词内容。

1.  **`SearchView.swift` (视图层)**
    *   用户在 `TextField` 中输入单词，该输入通过 `@Published var searchQuery: String` 绑定到 `SearchViewModel`。
    *   当用户提交搜索（例如，按下键盘上的搜索按钮）时，`viewModel.performSearch()` 方法被调用。
    *   当用户选择一个搜索建议时，`viewModel.selectSuggestionAndDismiss()` 方法被调用，该方法内部会调用 `selectSuggestion()`，进而触发 `loadWordContent()`。

2.  **`SearchViewModel.swift` (视图模型层)**
    *   `performSearch()` 方法：主要负责获取搜索建议，它会调用 `searchService.getSuggestions()`。
    *   `selectSuggestion(suggestion: SearchSuggestion)` 方法：当用户选择一个搜索建议时，此方法被调用。它会更新 `selectedWord` 和 `searchQuery`，并调用 `loadWordContent(word: String)`。
    *   `loadWordContent(word: String)` 方法：此方法负责加载完整单词内容。它会设置 `contentLoadingState` 为 `.loading`，然后调用 `searchService.getWordContent(word: word, language: .chinese)` 发起请求。

3.  **`SearchService.swift` (业务服务层)**
    *   `getWordContent(word: String, language: LanguageCode)` 方法：这是获取完整单词内容的业务入口。
    *   它首先通过 `localIndexService.checkCache()` 检查本地缓存。
    *   如果缓存中没有完整内容，它会调用 `fetchFullContent(word: String, language: LanguageCode, startTime: Date)`。
    *   `fetchFullContent` 方法内部，会通过 `wordAPIAdapter.getWord(word, language: language)` 实际发起网络请求。

4.  **`WordAPIAdapter.swift` (API 转译层)**
    *   `getWord(word: String, language: LanguageCode?)` 方法：此方法负责构建获取单词定义的 API 请求。
    *   它根据传入的 `word` 和 `language` 参数，构建出类似 `/api/v1/word/{word}?lang={language}` 的 API 端点。
    *   最终，它调用 `apiClient.request()` 方法来执行 HTTP 请求。

5.  **`APIClient.swift` (网络基础设施层)**
    *   `request<T: Codable>(endpoint: String, method: HTTPMethod, headers: [String: String]?, body: Data?)` 方法：这是执行所有 HTTP 请求的核心方法。
    *   它负责构建完整的 `URL` (`baseURL + endpoint`)。
    *   创建 `URLRequest` 对象，设置 HTTP 方法、请求体和请求头（包括 `APIConfig.staticHeaders`）。
    *   使用 `URLSession.shared.data(for: request)` 执行实际的网络请求。
    *   处理网络响应，包括验证 HTTP 状态码（200-299 成功，401 未授权，403 无效 API Key，其他为服务器错误）。
    *   使用 `JSONDecoder` 将返回的 `Data` 解析为 `WordDefinitionResponse` 类型的数据。
    *   捕获并抛出 `APIError` 或其他网络错误。

## 3. 单一真相来源

“完整单词内容”的**原始数据**单一真相来源是 **后端 API 接口 `/api/v1/word/{word}`**。

尽管 `SearchService` 会首先检查本地缓存，但缓存中的数据也是从该 API 接口获取的副本。因此，该 API 接口是完整单词内容的最终权威来源。

## 4. 问题与发现

1.  **`WordAPIAdapterProtocol` 中 `getWord` 和 `getWordDefinition` 方法的冗余**：
    *   `WordAPIAdapterProtocol` 定义了两个功能相同的协议方法 `getWord` 和 `getWordDefinition`。
    *   在 `WordAPIAdapter` 的实现中，`getWordDefinition` 只是简单地调用了 `getWord`。这导致了协议和实现上的冗余，增加了不必要的复杂性。

2.  **`APIConfig.staticHeaders` 的硬编码使用**：
    *   `WordAPIAdapter` 在构建请求时直接使用了 `APIConfig.staticHeaders`。虽然这可能适用于静态 API 密钥，但如果未来需要更灵活的认证机制（例如，基于用户会话的动态 Token），这种硬编码方式会限制扩展性。

3.  **`APIClient` 错误处理的通用性**：
    *   `APIClient` 中的错误处理（如 `APIError.serverError`）是相对通用的。虽然能够捕获并抛出错误，但对于前端来说，如果能从后端获取更细粒度的错误码或错误信息，将有助于提供更精确的用户反馈和更智能的错误恢复策略。

## 5. 优化建议

1.  **✅ 已完成：简化 `WordAPIAdapterProtocol`**：
    *   已从 `WordAPIAdapterProtocol` 中移除冗余的 `getWordDefinition` 方法，只保留 `getWord` 方法。
    *   已更新 `SearchService` 中的调用，直接使用 `wordAPIAdapter.getWord()`。协议更加简洁，减少了混淆，提高了代码的可维护性。

2.  **优化 API 配置和认证**：
    *   考虑将 `APIConfig.staticHeaders` 作为依赖注入到 `APIClient` 中，而不是让 `WordAPIAdapter` 直接访问全局配置。这使得 `APIClient` 更加独立和可测试。
    *   如果未来需要支持动态认证，可以设计一个更灵活的认证机制，例如通过 `APIClient` 的初始化参数或一个独立的认证服务来管理 Token。

3.  **增强错误处理的粒度**：
    *   与后端团队协作，定义一套标准化的错误码和错误信息。
    *   在 `APIClient` 中，根据后端返回的错误码，映射到更具体的 `APIError` 类型或包含详细信息的错误对象。这将使 `SearchViewModel` 能够根据不同的错误类型，向用户展示更准确的提示信息（例如，“单词不存在”、“服务器维护中”等），而不是笼统的“搜索失败”。

## 6. 文件保存

