## 1. 溯源目标

本次溯源的目标是分析 iOS 应用中搜索面板在用户输入内容并回车后，为什么不会直接以输入单词作为查询词，而是一直停留在“无搜索建议”状态的行为。

## 2. 溯源路径

1.  **视图层 (`SearchView.swift`)：**
    *   `SearchView` 包含一个 `TextField` 用于用户输入搜索查询。
    *   该 `TextField` 绑定到 `SearchViewModel` 的 `searchQuery` 属性。
    *   `TextField` 的 `.onSubmit` 修饰符配置为在用户回车时调用 `viewModel.performSearch()` 方法。

    ```swift
    // iOS/SensewordApp/Views/Search/SearchView.swift
    TextField("", text: $viewModel.searchQuery)
        .focused($isSearchFieldFocused)
        .textFieldStyle(PlainTextFieldStyle())
        .font(.system(size: 18))
        .submitLabel(.search)
        .onSubmit {
            viewModel.performSearch()
        }
    ```

2.  **视图模型层 (`SearchViewModel.swift`)：**
    *   `SearchViewModel` 是搜索功能的核心，管理搜索状态和逻辑。
    *   `searchQuery` 属性的 `didSet` 观察者会触发 `handleSearchQueryChange()`。
    *   `setupBindings()` 方法中，`$searchQuery` 使用了 `debounce` 操作符，引入了 `0.3` 秒的防抖延迟，然后调用 `handleDebouncedSearch(query: String)`。
    *   `handleDebouncedSearch` 方法中，只有当 `trimmedQuery.count >= 2` 时，才会调用 `performSearch(query: trimmedQuery)`。

    ```swift
    // iOS/SensewordApp/ViewModels/SearchViewModel.swift
    @Published var searchQuery: String = "" {
        didSet {
            handleSearchQueryChange()
        }
    }

    // ...

    func handleDebouncedSearch(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)

        if trimmedQuery.isEmpty {
            clearSuggestions()
        } else if trimmedQuery.count >= 2 { // 最小查询长度
            performSearch(query: trimmedQuery)
        }
    }
    ```

3.  **`performSearch()` 方法 (`SearchViewModel.swift`)：**
    *   此方法的核心职责是调用 `searchService.getSuggestions(query: searchText)` 来获取**搜索建议**。
    *   它会更新 `searchState` 为 `.searching`，并设置 `showSuggestions` 为 `true`。
    *   如果 `searchService.getSuggestions` 返回空数据 (`response.data.isEmpty`)，则会调用 `showEmptyState()`，导致显示“无搜索建议”的状态。

    ```swift
    // iOS/SensewordApp/ViewModels/SearchViewModel.swift
    func performSearch(query: String? = nil) {
        let searchText = query ?? searchQuery.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !searchText.isEmpty else {
            clearSuggestions()
            return
        }

        // 取消之前的搜索任务
        currentSearchTask?.cancel()

        // 更新状态
        searchState = .searching
        showSuggestions = true

        // 执行搜索 (获取搜索建议)
        currentSearchTask = Task {
            do {
                let response = await searchService.getSuggestions(query: searchText)

                // ...

                await MainActor.run {
                    self.suggestions = response.data
                    self.searchState = .showingResults(response.data)

                    // 如果没有结果，显示提示
                    if response.data.isEmpty {
                        self.showEmptyState()
                    }
                }

            } catch {
                // ...
            }
        }
    }
    ```

4.  **`selectSuggestion()` 和 `loadWordContent()` 方法 (`SearchViewModel.swift`)：**
    *   实际的单词内容查询（即获取单词的详细定义和信息）是在用户从搜索建议列表中选择一个建议时触发的。
    *   `selectSuggestion(_ suggestion: SearchSuggestion)` 方法会调用 `loadWordContent(word: suggestion.word)`。
    *   `loadWordContent` 才是真正调用 `searchService.getWordContent` 来获取单词详细内容的方法。

    ```swift
    // iOS/SensewordApp/ViewModels/SearchViewModel.swift
    func selectSuggestion(_ suggestion: SearchSuggestion) {
        selectedWord = suggestion.word
        searchQuery = suggestion.word
        showSuggestions = false

        // 加载完整内容
        loadWordContent(word: suggestion.word)
    }

    func loadWordContent(word: String) {
        // ...
        // 执行加载 (获取单词详细内容)
        currentContentTask = Task {
            do {
                let response = try await searchService.getWordContent(
                    word: word,
                    language: .chinese
                )
                // ...
            } catch {
                // ...
            }
        }
    }
    ```

## 3. 单一真相来源

*   **搜索建议的来源：** `SearchService.getSuggestions(query: String)` 方法。
*   **单词详细内容的来源：** `SearchService.getWordContent(word: String, language: Language)` 方法。
*   **搜索面板回车行为的逻辑控制：** `SearchViewModel` 中的 `performSearch()` 方法，以及 `onSubmit` 和 `searchQuery` 的 `didSet` 链式调用。

## 4. 问题与发现

*   **职责分离导致的用户体验问题：** 当前设计中，`performSearch()` 仅负责获取搜索建议，而真正的单词查询（加载详细内容）被延迟到用户选择建议之后。这导致了用户在输入完整单词并回车后，如果该单词没有出现在建议列表中，系统不会直接进行查询，而是显示“无搜索建议”状态，与用户的直观期望不符。
*   **防抖和最小查询长度限制：** `searchQuery` 的防抖处理和最小查询长度（2个字符）限制了 `performSearch` 的触发时机，进一步影响了回车后的即时查询行为。

## 5. 优化建议

为了实现用户期望的“回车后直接以输入单词作为查询词”的功能，可以考虑以下优化方案：

1.  **修改 `performSearch()` 逻辑：**
    *   在 `performSearch()` 方法中，除了获取搜索建议外，可以增加一个逻辑分支：如果搜索建议为空，则尝试直接调用 `loadWordContent(word: searchText)` 来查询用户输入的完整单词。
    *   这需要在 `SearchViewModel` 中引入一个状态来区分是“正在获取建议”还是“正在查询单词内容”，以便在 UI 上进行相应的展示。

2.  **在 `onSubmit` 时直接触发单词查询：**
    *   在 `SearchView.swift` 的 `TextField` 的 `.onSubmit` 修饰符中，除了调用 `viewModel.performSearch()`，还可以根据 `viewModel.searchQuery` 的内容，直接调用 `viewModel.loadWordContent(word: viewModel.searchQuery)`。
    *   这种方式可能需要更精细的逻辑来处理何时显示建议，何时显示单词内容，以及如何处理建议和内容同时存在的情况。

3.  **引入新的搜索触发机制：**
    *   可以考虑在 `SearchViewModel` 中引入一个新的方法，例如 `performDirectSearch(word: String)`，专门用于直接查询单词内容。
    *   在 `SearchView` 的 `onSubmit` 中，根据用户输入和当前状态，决定是调用 `performSearch()`（获取建议）还是 `performDirectSearch()`（直接查询）。

**推荐方案：** 方案 1 相对更平滑，因为它在现有 `performSearch` 的基础上进行扩展，减少了对视图层逻辑的修改。在 `performSearch` 中，如果获取建议为空，则尝试直接查询单词内容。