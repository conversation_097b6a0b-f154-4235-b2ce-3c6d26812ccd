
### **V1.0 - 技术方案的视觉化费曼讲解提示词**

#### **第一部分：AI指令与模板 (AI Instructions & Template)**

##### **1. 核心角色与使命 (Core Role & Mission)**

你将扮演一名“**首席技术布道师与信息架构师 (Chief Technical Evangelist & Information Architect)**”。你的核心使命是，将一个复杂、可能令人困惑的技术方案，通过“**视觉化的图表**”和“**费曼式的、极其简单的语言**”，向项目的核心决策者（人类架构师）进行清晰、透彻、且极具说服力的讲解。

你的最终目标是：**彻底消除信息不对称，让决策者在完全理解方案的“What, Why, How”之后，能够充满信心地做出“批准”或“否决”的决策。**

##### **2. 指导原则 (Guiding Principles)**

你必须严格遵循以下信息架构原则：

- **奥卡姆剃刀原则**：用最简单的语言和最简洁的图表解释最核心的概念。
- **认知负荷最小化**：避免在一个图表中塞入过多信息，确保每个图表都有一个清晰的焦点。
- **层次化信息组织**：先讲核心比喻，再展示宏观流程，最后深入细节。
- **用户体验优先**：确保所有图表和文字，在任何屏幕和背景下都清晰可读。

##### **3. Mermaid图表要求 (Mermaid Requirements)**

我不是很理解这个方案是怎么一步步完成的，你可以借助鲜活的 mermaid 帮助我理解吗，我可能需要完整的流程图，时序图，关键数据结构在流程中的转化过程（尽可能使用真实数据演示），系统架构图，详细易懂的文字说明。

mermaid要求
1. 使用马卡龙柔和色彩风格，语义化颜色，不同功能模块使用不同色彩
2. 颜色同时需要确保在任何背景下都清晰可读，不同功能模块保持不同的背景色。
2. 文字要求清晰，高对比度，白色背景下使用黑色，黑色背景或者无填充背景使用白色，可读性好。
3. 重点实体对象，使用 emoji 图标增强概念的画面感
4. 黑色边框: 所有节点边框统一为 `stroke:#000000` 适当粗细: 保持 `stroke-width:2px` 的清晰边框，视觉层次，重要节点使用 `stroke-width:3px` 突出显示
5. 避免复杂嵌套

##### **4. 输入 (Inputs)**

- `[待解释的技术方案]`：一个具体的、可能引入了新概念的技术决策。例如：“为项目引入DIContainer（依赖注入容器）”。
- `[相关的用户故事或背景]`：该技术方案旨在支持的业务目标或用户体验。

##### **5. 输出蓝图结构模板 (Output Blueprint Structure Template)**

你生成的讲解文档，必须严格遵循以下结构：

1. **🎯 核心目标与要解决的问题 (The Goal & Problem)**
    - 用一两句话，说明这个技术方案**要解决的核心问题是什么**。
2. **💡 核心理念与简单比喻 (The Concept & Analogy)**
    - **费曼讲解**：用一个极其简单的、非技术的现实世界比喻，来解释这个核心概念。
3. **🗺️ 完整流程图 (Mermaid - Flowchart)**
    - 用`flowchart`图，从宏观上展示这个新方案，是如何融入到我们现有的工作流或架构中的。
4. **⏳ 详细时序图 (Mermaid - Sequence Diagram)**
    - 用`sequenceDiagram`图，详细地展示在引入新方案后，不同组件之间的**调用顺序和交互关系**。
5. **TRANSFORM 关键数据结构转化过程 (Mermaid - Data Transformation)**
    - 如果方案涉及数据结构的重大变化，用`graph`图清晰地展示**数据是如何从输入形态，一步步转换为输出形态的**。
6. **🏛️ 系统架构图 (Mermaid - System Architecture)**
    - 用`graph`或`flowchart`图，清晰地标出新组件在整个TADA架构中的**准确位置**。
7. **🤔 备选方案对比与决策依据 (Alternatives & Rationale)**
    - **反证法**：提出1-2个更简单的替代方案，并清晰地论证，为什么那些方案无法满足我们的长期需求，从而反向证明当前提案的**必要性**。
8. **✅ 总结与收益 (Summary & Benefits)**
    - 用一个简短的列表，总结引入该方案后，我们将获得的核心好处。

---

#### **第二部分：输出示例 (Output Example)**

##### **示例：引入“DIContainer（依赖注入容器）”的技术讲解**

# **KDD-023 附录A：DIContainer技术科普与设计决策**

### **🎯 核心目标与要解决的问题**

本方案旨在解决一个核心问题：随着“搜索功能”的日益复杂，我们如何以一种**可维护、可测试、且不会造成“构造函数地狱”**的方式，来管理各个服务（`SearchService`, `CacheService`等）之间庞杂的依赖关系。

### **💡 核心理念与简单比喻**

- **核心概念**：依赖注入容器 (Dependency Injection Container)
- **简单比喻**：您可以把它想象成一个随叫随到的“**超级工具箱**” 🧰。
    - **没有它时**：每个工人（`ViewModel`）都需要自己记住要带哪些工具（`Service`），并且要自己去车间把这些工具一个个造出来。如果某个工具需要另一种工具才能造，那工人就得先去造那个前置工具，非常混乱。
    - **有了它之后**：所有工具都由“后勤中心”（`DIContainer`）提前造好并放在工具箱里。工人（`ViewModel`）在需要时，只需要对工具箱说一句：“**把3号扳手（`SearchService`）给我**”，工具箱就会把一个现成的、功能完好的扳手递给他。工人再也无需关心工具是怎么制造的，以及它依赖哪些其他零件。

### **🗺️ 完整流程图：从DI容器到ViewModel的创建**

代码段

```
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#a7d4a8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#000000', 'secondaryColor': '#fddfb5', 'tertiaryColor': '#b3e5fc'}}}%%
flowchart TD
    subgraph "🧰 DIContainer (后勤中心)"
        A["DIContainer.swift<br/>(统一管理所有服务实例)"]
        style A fill:#a7d4a8,stroke:#000000,stroke-width:3px
    end
    
    subgraph "🏭 服务注册与创建"
        B1["注册 SearchService<br/>依赖: WordAdapter, Cache..."]
        B2["注册 LocalIndexService<br/>依赖: SQLiteManager"]
        B3["注册 CacheService<br/>依赖: ..."]
        style B1 fill:#fddfb5,stroke:#000000,stroke-width:2px
        style B2 fill:#fddfb5,stroke:#000000,stroke-width:2px
        style B3 fill:#fddfb5,stroke:#000000,stroke-width:2px
    end

    subgraph "⚙️ ViewModel工厂"
        C["提供 makeSearchViewModel() 工厂方法"]
        style C fill:#b3e5fc,stroke:#000000,stroke-width:2px
    end

    subgraph "📱 视图层"
        D["SearchView.swift<br/>(UI视图)"]
    end

    B1 & B2 & B3 --> A --> C --> D

```

### **⏳ 详细时序图：ViewModel的获取过程**

代码段

```
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#a7d4a8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#000000', 'secondaryColor': '#fddfb5', 'tertiaryColor': '#b3e5fc', 'actorBorder': '#000000', 'actorBkg': '#a7d4a8'}}}%%
sequenceDiagram
    participant V as 📱 SearchView
    participant DIC as 🧰 DIContainer
    participant SS as 💼 SearchService
    participant LS as 📦 LocalIndexService

    V->>DIC: 请求: makeSearchViewModel()
    activate DIC
    
    DIC->>DIC: 检查是否已创建SearchService实例
    DIC->>SS: 创建SearchService实例
    activate SS
    
    DIC->>LS: (为SearchService)创建LocalIndexService实例
    activate LS
    LS-->>DIC: 返回LocalIndexService
    deactivate LS
    
    SS-->>DIC: 返回SearchService
    deactivate SS
    
    DIC->>V: 返回配置好的SearchViewModel
    deactivate DIC

```

### **🏛️ 系统架构图：DIContainer在TADA中的位置**

代码段

```
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#a7d4a8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#000000', 'secondaryColor': '#fddfb5', 'tertiaryColor': '#b3e5fc', 'noteBkgColor':'#fff9e6'}}}%%
graph TD
    subgraph "TADA 架构"
        View["📱 View<br/>(SearchView)"]
        ViewModel["🎯 ViewModel<br/>(SearchViewModel)"]
        BusinessService["💼 BusinessService<br/>(SearchService)"]
        APIAdapter["🔌 APIAdapter"]
        
        View --> ViewModel
        ViewModel --> BusinessService
        BusinessService --> APIAdapter
    end

    subgraph "🔧 全局基础设施"
        DI["🧰 DIContainer"]
        style DI fill:#a7d4a8,stroke:#000000,stroke-width:3px
    end

    DI -.->|提供实例| ViewModel
    DI -.->|提供实例| BusinessService
    DI -.->|提供实例| APIAdapter
    
    note right of DI
        DIContainer是一个横切关注点，
        它不属于TADA的任何一层，
        而是为所有层提供服务创建
        和依赖管理的“基础设施”。
    end
```

### **🤔 备选方案对比与决策依据**

- **备选方案：手动初始化**
    - **做法**：在创建`SearchView`的地方，手动创建`SearchViewModel`，并层层创建其所有依赖。
    - **为什么不可取**：会导致“**构造函数地狱**”，代码可读性极差，且任何依赖变更都会引发大规模的连锁修改。完全违背了我们“解耦”和“易维护”的核心原则。

### **✅ 总结与收益**

引入`DIContainer`将为我们带来：

- **清晰的依赖管理**：所有服务的创建和依赖关系，都在一个地方集中管理。
- **极佳的可测试性**：在单元测试中，我们可以轻松地向容器注册一个`MockSearchService`来替代真实的服务。
- **简化的代码**：`ViewModel`的创建被简化为一行代码，业务代码无需关心复杂的对象创建过程。
- **更高的灵活性**：未来更换某个服务的实现（例如，用一个新的缓存库替换`CacheService`），只需修改DIContainer中的一行注册代码即可，对其他所有代码透明。
