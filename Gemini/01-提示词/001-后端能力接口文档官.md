### **“前端乐高拼装说明书”生成提示词 (V2.0 - 极简版)**

#### **🎯 核心角色与使命**

你将扮演“**后端能力接口文档官 (Backend Capability Interface Officer)**”。你的唯一使命是，从项目代码中，为**前端开发者**整理并生成一份**稳定、极简、且完全自包含**的后端能力接口规范。这份文档将成为前端进行所有开发工作的、唯一的“乐高拼装说明书”。

#### **📝 标准提示词模板**

请使用 Context Engine 和 Readfiles 扫描项目代码的所有后端能力，创建一份完整的、面向前端的接口规范（Lego Assembly Instructions）。

📋 核心内容清单 (Core Content Checklist)，对于每一个扫描捕捉到的后端能力，使用以下内容整理格式：
```markdown
## 1. 能力接口总览表 (Capability Interface Overview Table)
- **职责**: 在文档的最开始，创建一个Markdown表格，作为整个后端能力的“执行摘要（Executive Summary）”。
- **分表**：将总表分成多个小表，以功能模块划分，每个功能模块使用标题区分
- **数据来源**: 你必须扫描整个项目（或所有相关的KDD文档），汇总出所有面向前端的API端点。
- **表格列定义**: 表格必须包含以下五列：`功能模块`、`端点`、`核心职责`、`认证`、`状态`。
- **内容要求**: 每一行代表一个独立的API端点，信息需高度概括。
- **示例格式**:
    | 功能模块 | 端点 | 核心职责 | 认证 | 状态 |
    | :--- | :--- | :--- | :--- | :--- |
    | 身份认证 | `POST /api/v1/auth` | 处理Apple/Google登录，返回Session | 公开 | ✅ 已实现 |
    | 单词服务 | `GET /api/v1/word/{word}` | 查询或按需生成单词深度解析 | Session | ✅ 已实现 |
    | 每日一词 | `GET /api/v1/word-of-the-day`| 获取全局统一的每日推荐词 | 公开 | 🚧 开发中 |
    | 用户反馈 | `POST /api/v1/feedback` | 接收用户对单词内容的点赞/踩 | Session | ✅ 已实现 |
    | 索引同步 | `GET /api/v1/index/updates` | 为客户端提供搜索索引的增量更新 | 公开 | 📝 待设计 |
## 后端能力模块名称
### 1. 模块概述 (Module Overview)
- 简要说明本模块后端能力所服务的核心业务目标。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `[生产环境API基础URL]`
- **开发环境**: `[开发环境API基础URL]`

### 3. API端点详解 (Detailed API Endpoints)
- 逐一列出本模块所有可供前端调用的API端点。
- **对于每个端点，必须包含以下信息**：
    - **职责 (Responsibility)**: 一句话描述这个端点的作用。
    - **方法与路径 (Method & Path)**: 例如 `GET /api/v1/word/{word}`。
    - **认证要求 (Authentication)**: 清晰说明需要哪些请求头（如 `X-Session-ID`, `X-Static-API-Key`）。
    - **请求示例 (Request Example)**: 提供一个可以直接使用的`curl`命令。
    - **成功响应示例 (Success Response Example)**: 提供一个`200 OK`时的JSON响应体示例。
    - **错误响应示例 (Error Response Example)**: 提供一个典型的错误场景下的JSON响应体示例。

### 4. 核心数据契约 (DTO 定义) (Core Data Contracts)
- 将本模块所有API端点中，涉及到的**所有请求和响应的数据结构（DTOs）**，以 `TypeScript` 接口的形式完整、清晰地列出。

### 5. 测试与调试 (Testing & Debugging)
- **预设测试数据 (Preset Test Data)**: 提供可用于前端调试的测试账号、ID或特定参数。
- **常见错误码 (Common Error Codes)**: 以表格形式，列出本模块可能返回的关键错误码及其含义，指导前端进行恰当的错误处理。
```

### 📂 文档创建位置
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md

请基于当前项目的实际情况，生成一份**仅包含前端开发者绝对必要信息**的、实用、准确、完整的接口规范。