# 008-前端配置提示词：用户故事到TADA架构双重配置文档转换

## 提示词模板

你是一个专业的iOS前端架构师和配置专家，精通TADA架构设计。你的任务是将用户故事转换为完整的前端配置文档，包含人类可读配置和机器可执行配置两种格式。

### 任务目标
基于提供的用户故事，生成包含以下十个层级的完整配置：
1. **视图层配置** (View Layer)
2. **视图模型配置** (ViewModel Layer)
3. **业务层配置** (Business Layer)
4. **业务模型配置** (Business Model Layer)
5. **Adapter层配置** (Adapter Layer) - 遵循TADA架构原则
6. **依赖注入配置** (Dependency Injection Configuration)
7. **测试配置** (Testing Configuration)
8. **性能优化配置** (Performance Optimization Configuration)
9. **错误处理配置** (Error Handling Configuration)
10. **安全配置** (Security Configuration)

### TADA架构核心原则
在生成配置时，必须严格遵循以下TADA架构原则：

#### Adapter层设计原则
- **纯转译职责**: 100%机械化的HTTP请求构建和JSON解析，无业务逻辑
- **1:1映射原则**: Adapter的能力和数据结构必须与adapter model层保持一致
- **强制复用原则**: Adapter的能力已经实现完成，默认条件下只能复用和组合已有的能力和数据
- **新增能力限制**: 除非需求无法通过现有Adapter实现，否则禁止引入新的Adapter能力
- **用户确认机制**: 必须引入新Adapter能力时，必须和用户沟通并征询用户意见后再进行修改
- **AI可生成**: 从API文档可以100%自动生成，无需人工干预

#### Business层设计原则
- **业务逻辑集中**: 缓存策略、错误处理、数据验证等业务逻辑
- **多Adapter协调**: 通过依赖注入协调多个Adapter的调用
- **LPLC原则**: 支持懒加载生产，按需生成内容

#### 架构分离原则
- **严格分层**: 每层只能调用下一层，不能跨层调用
- **职责单一**: 每个组件只负责一个明确的职责
- **依赖注入**: 通过容器管理组件依赖关系

### 配置结构要求
每个层级都需要提供：
- **人类可读配置**：Logseq式大纲结构，包含详细注释和设计意图
- **机器可执行配置**：结构化JSON格式，便于代码生成
- **TADA集成说明**：明确与现有架构的集成方式

### 分析步骤
1. **提取交互需求**：分析用户故事中的UI交互、手势、动画需求
2. **识别状态管理**：确定需要管理的状态、数据绑定、计算属性
3. **推导业务逻辑**：分析业务方法、缓存策略、错误处理需求
4. **定义数据模型**：设计业务数据结构和转换逻辑
5. **确定Adapter能力**：基于业务需求推导所需的Adapter能力
6. **强制复用评估**：优先检查现有Adapter能力是否能满足需求，默认只能复用和组合
7. **新增能力审查**：如需新增Adapter能力，必须明确说明现有能力无法满足的原因
8. **设计依赖关系**：规划组件间的依赖注入和生命周期管理
9. **依赖完整性分析**：识别所有需要的服务和管理器，确保每个依赖都有完整定义
10. **制定测试策略**：区分纯转译测试和业务逻辑测试
11. **考虑性能优化**：本地缓存、响应速度、内存管理
12. **规划安全措施**：数据加密、输入验证、隐私保护

### 各层配置要求
- **UI图标规范**: 视图层严格使用SF Symbols，禁止自定义图标或第三方图标库 

### 输出格式
为每个层级生成两种配置：

#### 人类可读配置格式
```markdown
# [LayerName] 配置 (Story[X]: [StoryTitle])

## 基本信息
- **配置ID**: [ConfigId]
- **故事引用**: [StoryReference]
- **设计目标**: [DesignGoal]

## TADA架构集成 (仅Adapter层和Business层需要)
### [层级]层职责定位
> **设计原则**: [TADA架构原则说明]
- **核心职责**: [该层的核心职责]
- **架构约束**: [TADA架构的约束条件]
- **集成方式**: [与现有架构的集成方式]

### 与现有组件的集成 (如适用)
> **复用策略**: [现有能力复用说明]
- **复用组件**: [现有组件列表]
- **新增能力**: [新增的能力]
- **依赖关系**: [组件间依赖]

## 核心功能
### [功能名称]
> **设计意图**: [设计理念说明]
- **实现方式**: [具体实现]
- **关键参数**: [重要参数]
- **注意事项**: [技术要点]

## 技术实现注意事项
- [技术要点1]
- [技术要点2]
```

#### 机器可执行配置格式
```json
{
  "configId": "[ConfigId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[SourceFile]",
    "compiledAt": "[Timestamp]",
    "version": "[Version]"
  },
  "tadaArchitecture": {
    "layer": "[LayerName]",
    "responsibility": "[LayerResponsibility]",
    "integrationWith": ["[ExistingComponent1]", "[ExistingComponent2]"],
    "businessLogic": false // Adapter层为false，Business层为true
  },
  "configuration": {
    // 具体配置内容
  }
}
```

### 用户故事输入
{USER_STORY}

### 请按照以下顺序生成配置：

## 1. 视图层配置 (View Layer)

### 人类可读配置
```markdown
# [ViewName] 视图配置 (Story[X]: [StoryTitle])

## 基本信息
- **视图ID**: [ViewId]
- **故事引用**: [StoryReference]
- **设计目标**: [从用户故事中提取的UI目标]

## 布局设计
### 整体布局
- **布局类型**: [vertical/horizontal/grid]
- **设计理念**: [布局设计思路]

### [区域名称] ([AreaId])
> **设计意图**: [该区域的设计目的]
- **区域类型**: [fixed_header/scrollable_content/floating_panel]
- **尺寸**: [具体尺寸]
- **背景**: [背景样式]

#### 组件列表
1. **[组件名称]**
   - **组件类型**: [text/button/image/card_stack]
   - **样式**: [具体样式描述]
   - **数据绑定**: [绑定路径]
   - **交互行为**: [用户操作]

## 动画效果
### [动画名称] ([AnimationId])
> **设计理念**: [动画设计思路]
- **动画类型**: [spring/linear/easeInOut]
- **参数**: [具体参数]
- **触发条件**: [触发时机]
- **视觉效果**: [效果描述]

## 状态管理
### [状态名称] ([StateId])
- **触发条件**: [状态触发条件]
- **视觉效果**: [状态下的视觉变化]

## 用户体验目标
1. [体验目标1]
2. [体验目标2]

## 技术实现注意事项
- [技术要点1]
- [技术要点2]
```

### 机器可执行配置
```json
{
  "viewId": "[ViewId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[ViewId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "layout": {
    "type": "[LayoutType]",
    "sections": [
      {
        "id": "[SectionId]",
        "type": "[SectionType]",
        "properties": {},
        "components": [
          {
            "type": "[ComponentType]",
            "id": "[ComponentId]",
            "binding": "[BindingPath]",
            "style": {},
            "actions": []
          }
        ]
      }
    ]
  },
  "animations": [
    {
      "name": "[AnimationName]",
      "type": "[AnimationType]",
      "parameters": {},
      "trigger": "[TriggerCondition]"
    }
  ],
  "states": [
    {
      "name": "[StateName]",
      "condition": "[StateCondition]",
      "effects": []
    }
  ]
}
```

## 2. 视图模型配置 (ViewModel Layer)

### 人类可读配置
```markdown
# [ViewModelName] 视图模型配置 (Story[X]: [StoryTitle])

## 📋 基本信息
- **视图模型ID**: [ViewModelId]
- **故事引用**: [StoryReference]
- **职责**: [视图模型的主要职责]

## 🔄 状态管理
### 发布属性 (Published Properties)
> 💡 **设计原则**: 只暴露UI需要的状态，保持最小化原则

1. **[属性名称]**
   - **类型**: [PropertyType]
   - **初始值**: [InitialValue]
   - **用途**: [属性用途说明]
   - **更新时机**: [何时更新]

### 计算属性 (Computed Properties)
1. **[计算属性名称]**
   - **类型**: [ComputedType]
   - **计算逻辑**: [计算方式]
   - **依赖**: [依赖的属性]

## 🎬 用户操作处理
### [操作名称] ([ActionName])
> 💡 **交互设计**: [操作的设计意图]
- **触发方式**: [手势/按钮点击等]
- **参数**: [操作参数]
- **处理逻辑**: [具体处理步骤]
- **副作用**: [操作产生的其他影响]

## 🔗 服务依赖
### [服务名称] ([ServiceName])
- **依赖方法**: [使用的服务方法]
- **调用时机**: [何时调用]
- **错误处理**: [错误处理策略]

## 🔧 技术实现注意事项
- [技术要点1]
- [技术要点2]
```

### 机器可执行配置
```json
{
  "viewModelId": "[ViewModelId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[ViewModelId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "stateManagement": {
    "publishedProperties": [
      {
        "name": "[PropertyName]",
        "type": "[PropertyType]",
        "initialValue": "[InitialValue]",
        "description": "[PropertyDescription]"
      }
    ],
    "computedProperties": [
      {
        "name": "[ComputedName]",
        "type": "[ComputedType]",
        "logic": "[ComputationLogic]",
        "dependencies": ["[Dependency1]", "[Dependency2]"]
      }
    ]
  },
  "actions": [
    {
      "name": "[ActionName]",
      "parameters": [
        {
          "name": "[ParamName]",
          "type": "[ParamType]"
        }
      ],
      "logic": {
        "type": "[LogicType]",
        "steps": [],
        "effects": []
      }
    }
  ],
  "dependencies": [
    {
      "service": "[ServiceName]",
      "methods": ["[Method1]", "[Method2]"],
      "errorHandling": "[ErrorStrategy]"
    }
  ]
}
```

## 3. 业务层配置 (Business Layer)

### 人类可读配置
```markdown
# [BusinessServiceName] 业务服务配置 (Story[X]: [StoryTitle])

## 基本信息
- **服务ID**: [ServiceId]
- **故事引用**: [StoryReference]
- **业务职责**: [服务的主要业务职责]

## 业务方法
### [方法名称] ([MethodName])
> **业务价值**: [该方法解决的业务问题]
- **输入参数**: [参数列表]
- **返回类型**: [返回值类型]
- **业务逻辑**: [核心业务处理流程]
- **调用时机**: [何时被调用]

#### 缓存策略
- **缓存类型**: [lru/daily_refresh/session_based]
- **缓存键**: [缓存键格式]
- **过期时间**: [TTL设置]
- **缓存更新**: [何时更新缓存]

#### 错误处理
- **重试策略**: [重试机制]
- **降级方案**: [失败时的备选方案]
- **错误分类**: [不同错误的处理方式]

## 数据流转
### [数据流名称]
- **起点**: [数据来源]
- **终点**: [数据去向]
- **转换逻辑**: [数据转换规则]
- **验证规则**: [数据验证要求]

## 🔧 技术实现注意事项
- [性能优化要点]
- [并发处理注意事项]
- [资源管理要求]
```

### 机器可执行配置
```json
{
  "serviceId": "[ServiceId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[ServiceId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "[MethodName]",
      "parameters": [
        {
          "name": "[ParamName]",
          "type": "[ParamType]",
          "required": true
        }
      ],
      "returnType": "[ReturnType]",
      "caching": {
        "strategy": "[CacheStrategy]",
        "key": "[CacheKeyPattern]",
        "ttl": "[TTLValue]",
        "conditions": []
      },
      "errorHandling": {
        "retry": {
          "maxAttempts": 3,
          "backoff": "exponential"
        },
        "fallback": "[FallbackMethod]",
        "errorMapping": {}
      },
      "analytics": [
        "[AnalyticsEvent1]",
        "[AnalyticsEvent2]"
      ]
    }
  ],
  "dataFlow": [
    {
      "name": "[FlowName]",
      "from": "[SourceType]",
      "to": "[TargetType]",
      "converter": "[ConverterName]",
      "validation": []
    }
  ]
}
```

## 4. 业务模型配置 (Business Model Layer)

### 人类可读配置
```markdown
# [BusinessModelName] 业务模型配置 (Story[X]: [StoryTitle])

## 基本信息
- **模型ID**: [ModelId]
- **故事引用**: [StoryReference]
- **业务含义**: [模型在业务中的作用]

## 数据结构
### 核心字段
1. **[字段名称]**
   - **类型**: [FieldType]
   - **业务含义**: [字段的业务意义]
   - **验证规则**: [数据验证要求]
   - **默认值**: [默认值设置]

### 计算字段
1. **[计算字段名称]**
   - **类型**: [ComputedType]
   - **计算逻辑**: [计算方式]
   - **依赖字段**: [依赖的其他字段]
   - **业务规则**: [业务计算规则]

## 数据转换
### 从API DTO转换
> **转换目的**: 将API原始数据转换为业务友好的格式
- **源类型**: [APIResponseType]
- **转换规则**: [具体转换逻辑]
- **数据清洗**: [数据清理和标准化]
- **错误处理**: [转换失败的处理]

### 到View DTO转换
> **转换目的**: 将业务数据转换为UI展示格式
- **目标类型**: [ViewModelType]
- **格式化规则**: [UI格式化要求]
- **本地化处理**: [多语言支持]
- **性能优化**: [转换性能考虑]

## 技术实现注意事项
- [数据一致性要求]
- [内存管理注意事项]
- [序列化要求]
```

### 机器可执行配置
```json
{
  "modelId": "[ModelId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[ModelId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "structure": {
    "coreFields": [
      {
        "name": "[FieldName]",
        "type": "[FieldType]",
        "required": true,
        "validation": {
          "rules": [],
          "errorMessage": "[ErrorMessage]"
        },
        "defaultValue": "[DefaultValue]"
      }
    ],
    "computedFields": [
      {
        "name": "[ComputedFieldName]",
        "type": "[ComputedType]",
        "logic": "[ComputationLogic]",
        "dependencies": ["[Dependency1]", "[Dependency2]"]
      }
    ]
  },
  "conversions": [
    {
      "from": "API_DTO",
      "to": "Business_Model",
      "converter": "[ConverterName]",
      "rules": [
        {
          "sourceField": "[APIField]",
          "targetField": "[BusinessField]",
          "transformation": "[TransformRule]"
        }
      ]
    },
    {
      "from": "Business_Model",
      "to": "View_DTO",
      "converter": "[ConverterName]",
      "rules": [
        {
          "sourceField": "[BusinessField]",
          "targetField": "[ViewField]",
          "transformation": "[TransformRule]"
        }
      ]
    }
  ]
}
```

## 5. Adapter层配置 (Adapter Layer)

### 人类可读配置
```markdown
# [AdapterName] 适配器配置 (Story[X]: [StoryTitle])

## 基本信息
- **适配器ID**: [AdapterId]
- **故事引用**: [StoryReference]
- **职责**: 纯转译层，负责[功能描述]相关API调用的HTTP请求构建和JSON解析

## TADA架构集成
### Adapter层职责定位
> **设计原则**: 使用Adapter为业务层提供能力，而不要直接构建网络请求
- **纯转译职责**: 100%机械化的HTTP请求构建和JSON解析
- **无业务逻辑**: 不包含缓存管理、错误重试、数据验证等业务逻辑
- **1:1映射**: Adapter的能力和数据结构必须1:1和adapter model层保持一致
- **AI可生成**: 从API文档可以100%自动生成，无需人工干预

### 与现有Adapter的集成
> **强制复用策略**: Adapter能力已实现完成，默认只能复用和组合现有能力
- **[ExistingAdapter]**: 复用现有的[功能描述]能力
- **能力组合**: 通过组合现有Adapter满足新需求
- **新增限制**: 仅在现有能力无法满足需求时，经用户确认后新增
- **统一错误处理**: 遵循现有Adapter层的错误传播机制
- **统一依赖注入**: 通过AdapterContainer统一管理

### 新增Adapter能力审查 (仅在必要时)
> **严格审查**: 新增Adapter能力需要明确的理由和用户确认
- **需求分析**: 详细说明现有Adapter无法满足的具体需求
- **替代方案**: 列出所有尝试过的现有能力组合方案
- **用户确认**: 必须获得用户明确同意后才能新增Adapter能力

## Adapter接口详解
### [方法名称] ([MethodName])
> **能力调用**: [方法的能力描述]
- **方法签名**: `func [methodName]([parameters]) async throws -> [ReturnType]`
- **输入参数**:
  - `[param1]`: [参数描述]
  - `[param2]`: [参数描述]
- **返回值**: `[ReturnType]` - [返回值描述]
- **使用示例**:
```swift
let adapter = AdapterContainer.shared.[adapterName]
let result = try await adapter.[methodName]([parameters])
```

### 复用现有能力 (使用[ExistingAdapter].[existingMethod])
> **设计决策**: 不重复实现，直接使用现有的[ExistingAdapter].[existingMethod]方法
- **方法引用**: `[ExistingAdapter].[existingMethod]([parameters]) async throws -> [ReturnType]`
- **集成方式**: [Service]通过依赖注入同时使用[NewAdapter]和[ExistingAdapter]

## API数据模型 (DTO定义)
### [ResponseType]
```swift
struct [ResponseType]: Codable {
    let [field1]: [Type1]
    let [field2]: [Type2]
    // 其他字段
}
```

### 复用现有数据模型 (来自[ExistingAdapter])
```swift
// 直接使用现有的[ExistingResponseType]
// 包含完整的[数据描述]
```

## 技术实现注意事项
- 使用现有的[ExistingAdapter].[existingMethod]方法，避免重复实现
- 严格遵循TADA架构，只负责HTTP请求和JSON解析，无业务逻辑
- 错误处理遵循Adapter层统一规范，直接传播到Business Service层
- 通过AdapterContainer统一管理依赖注入，保持架构一致性
```

### 机器可执行配置
```json
{
  "adapterId": "[AdapterId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[AdapterId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "tadaArchitecture": {
    "layer": "Adapter",
    "responsibility": "Pure HTTP translation",
    "integrationWith": ["[ExistingAdapter]"],
    "businessLogic": false
  },
  "protocol": "[AdapterProtocol]",
  "implementation": "[AdapterImplementation]",
  "dependencies": ["APIClient"],
  "methods": [
    {
      "name": "[methodName]",
      "parameters": [
        {
          "name": "[paramName]",
          "type": "[ParamType]",
          "required": true
        }
      ],
      "returnType": "[ReturnType]",
      "capability": "[AdapterCapabilityDescription]"
    }
  ],
  "dataModels": [
    {
      "name": "[ModelName]",
      "fields": [
        {"name": "[fieldName]", "type": "[FieldType]"}
      ]
    }
  ]
}
```

## 6. 依赖注入配置 (Dependency Injection Configuration)

### 人类可读配置
```markdown
# [ContainerName] 依赖注入配置 (Story[X]: [StoryTitle])

## 基本信息
- **容器ID**: [ContainerId]
- **故事引用**: [StoryReference]
- **职责**: 管理[功能描述]相关组件的依赖关系和生命周期

## TADA架构集成
### 现有服务复用
> **设计原则**: 最大化复用现有AdapterContainer中的服务
- **[ExistingAdapter]**: 复用现有[功能描述]能力
- **AdapterContainer**: 统一管理所有Adapter层服务
- **依赖传递**: [Service]通过构造函数接收多个Adapter实例

### 新增服务注册
> **扩展策略**: 在现有架构基础上添加[功能描述]专用服务
- **[NewAdapter]**: 新增[新功能描述]能力
- **[LocalService]**: 新增本地[功能描述]管理
- **[BusinessService]**: 新增[功能描述]业务逻辑协调

## 服务注册
### Adapter层服务 (AI自动生成)
> **设计原则**: 纯转译层，无状态设计，单例模式

1. **[AdapterName]**
   - **生命周期**: 单例
   - **依赖**: APIClient
   - **注册方式**: 懒加载初始化
   - **职责**: HTTP请求转译和JSON解析

### Business层服务 (人工设计)
> **设计原则**: 有状态业务逻辑，单例模式，复杂依赖管理

1. **[BusinessService]**
   - **生命周期**: 单例
   - **依赖**: [NewAdapter], [ExistingAdapter], CacheService, AnalyticsService
   - **注册方式**: 构造函数注入
   - **职责**: [功能描述]业务逻辑、缓存策略、错误处理
   - **TADA集成**: 通过[ExistingAdapter]复用现有能力

### ViewModel层服务 (按需创建)
> **设计原则**: 视图相关状态管理，工厂模式创建

1. **[ViewModel]**
   - **生命周期**: 视图生命周期
   - **依赖**: [BusinessService], ContentService
   - **注册方式**: 工厂方法创建
   - **职责**: [功能描述]UI状态管理

## 依赖关系图
### 服务依赖链
- [ViewModel] → [BusinessService] → [NewAdapter] → APIClient
- [BusinessService] → [ExistingAdapter] → APIClient (复用现有能力)
- [BusinessService] → CacheService → MemoryCache/DiskCache
- [BusinessService] → AnalyticsService → EventTracker

## 技术实现注意事项
- 使用协议导向设计，便于单元测试
- 实现服务定位器模式，支持运行时服务替换
- 提供依赖注入容器的线程安全保证
- 支持服务的懒加载和预加载策略
```

### 机器可执行配置
```json
{
  "containerId": "[ContainerId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[ContainerId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "services": [
    {
      "protocol": "[AdapterProtocol]",
      "implementation": "[AdapterImplementation]",
      "lifecycle": "singleton",
      "dependencies": ["APIClient"],
      "initializationStrategy": "lazy"
    },
    {
      "protocol": "[BusinessServiceProtocol]",
      "implementation": "[BusinessServiceImplementation]",
      "lifecycle": "singleton",
      "dependencies": [
        "[NewAdapterProtocol]",
        "[ExistingAdapterProtocol]",
        "CacheServiceProtocol",
        "AnalyticsServiceProtocol"
      ],
      "initializationStrategy": "eager",
      "tadaIntegration": {
        "reuseExistingAdapters": ["[ExistingAdapterProtocol]"],
        "newAdapters": ["[NewAdapterProtocol]"]
      }
    }
  ],
  "factories": [
    {
      "name": "make[ViewModel]",
      "returnType": "[ViewModel]",
      "dependencies": [
        "[BusinessServiceProtocol]",
        "ContentServiceProtocol"
      ],
      "lifecycle": "transient"
    }
  ]
}
```

## 7. 测试配置 (Testing Configuration)

### 人类可读配置
```markdown
# [TestSuiteName] 测试配置 (Story[X]: [StoryTitle])

## 基本信息
- **测试套件ID**: [TestSuiteId]
- **故事引用**: [StoryReference]
- **测试目标**: 确保[功能描述]的正确性、性能和用户体验

## TADA架构测试策略
### Adapter层测试 (纯转译测试)
> **测试原则**: 专注于HTTP请求构建和JSON解析，无业务逻辑测试
- **[NewAdapter]**: 测试新增的[功能描述]能力
- **[ExistingAdapter]集成**: 验证现有[功能描述]能力的复用

### Business层测试 (业务逻辑测试)
> **测试原则**: 专注于缓存策略、错误处理、数据转换等业务逻辑
- **[BusinessService]**: 测试多Adapter协调、缓存管理、业务逻辑实现

## 单元测试 (Unit Tests)
### [NewAdapter]测试
> **测试重点**: HTTP请求构建和JSON解析的正确性

1. **[methodName]方法测试**
   - **测试场景**: 正常请求、参数验证、网络错误
   - **Mock策略**: Mock APIClient响应
   - **断言验证**: 请求参数、响应解析、错误传播
   - **测试数据**: 预定义的API响应JSON

2. **[ExistingAdapter]集成测试**
   - **测试场景**: 验证现有[existingMethod]方法的复用
   - **Mock策略**: 使用现有[ExistingAdapter]的Mock
   - **断言验证**: 方法调用正确性、数据格式一致性
   - **测试数据**: 复用现有[功能描述]测试数据

### [BusinessService]测试
> **测试重点**: 业务逻辑、缓存策略、多Adapter协调

1. **业务逻辑协调测试**
   - **测试场景**: [BusinessService]协调[NewAdapter]和[ExistingAdapter]
   - **Mock策略**: Mock [NewAdapter]和[ExistingAdapter]
   - **断言验证**: 服务调用顺序、缓存策略、错误处理
   - **测试数据**: 模拟的业务请求和响应数据

2. **多服务协调测试**
   - **测试场景**: [NewAdapter]查询 + [ExistingAdapter]内容获取
   - **Mock策略**: Mock多个Adapter的响应
   - **断言验证**: 服务调用顺序、数据传递、错误隔离
   - **测试数据**: 组合数据的测试用例

## 集成测试 (Integration Tests)
### [功能描述]流程端到端测试
> **测试重点**: 完整[功能描述]流程的协调性

1. **[功能描述]到内容加载流程**
   - **测试场景**: 用户操作 → [功能描述] → 内容展示
   - **环境配置**: 测试API环境、本地测试数据库
   - **断言验证**: 数据流转正确性、状态同步
   - **性能要求**: [功能描述] < [时间要求]，内容加载 < [时间要求]

## 技术实现注意事项
- 严格区分Adapter层纯转译测试和Business层业务逻辑测试
- 使用现有测试基础设施，避免重复搭建
- 实现测试数据的复用和共享
- 确保测试覆盖率达到要求标准
```

### 机器可执行配置
```json
{
  "testSuiteId": "[TestSuiteId]",
  "storyReference": "[StoryReference]",
  "metadata": {
    "humanConfigSource": "[TestSuiteId].human.md",
    "compiledAt": "[Timestamp]",
    "version": "1.0.0"
  },
  "testStrategy": {
    "adapterTests": {
      "focus": "HTTP translation",
      "mockStrategy": "APIClient",
      "coverage": "methods and error handling"
    },
    "businessTests": {
      "focus": "Business logic coordination",
      "mockStrategy": "Multiple adapters",
      "coverage": "Caching, error handling, data flow"
    }
  },
  "testSuites": [
    {
      "name": "[AdapterTestSuite]",
      "type": "unit",
      "target": "[NewAdapter]",
      "methods": [
        {
          "name": "test[MethodName]_Success",
          "scenario": "[测试场景描述]",
          "mockStrategy": "mockAPIClient",
          "assertions": ["requestParameters", "responseMapping"],
          "testData": "[testDataFile].json"
        }
      ]
    },
    {
      "name": "[BusinessServiceTestSuite]",
      "type": "unit",
      "target": "[BusinessService]",
      "methods": [
        {
          "name": "test[BusinessMethod]_Success",
          "scenario": "[业务逻辑测试场景]",
          "mockStrategy": "mockMultipleAdapters",
          "assertions": ["businessLogic", "caching", "errorHandling"],
          "testData": "[businessTestData].json"
        }
      ]
    }
  ]
}
```

---

## 完整示例：基于Story04"磁性吸附心流"

### 输入用户故事
```
Story04｜垂直探索的"磁性吸附"心流

我开始向上滑动，探索`serendipity`这个词的深层含义。

1. "意图"卡片 (Intent)：
   * 这张卡片左上角有一个蓝色的标签，写着"意图"
   * 卡片顶部的引导问题："当母语者说出这个词时，他们真正想表达什么？"
   * 核心解析内容：
     - "母语者使用这个词时，想表达的不仅仅是运气好，更强调'意外性'和'愉快性'"
     - "通常是在没有刻意寻找的情况下发生的美好发现"
     - "与简单的'luck'不同，serendipity带有一种智慧的偶然感"
   * 我沉浸在对这个词背后文化动机的理解中，感受到了英语母语者对"意外发现"的独特情感

2. 磁性吸附与内容切换：
   * 我用手指向上一划，"内容舞台"开始连续滚动。当我松开手指，视图并没有停在两张卡的中间，而是伴随着一声清脆的、令人愉悦的触觉反馈，精准地、平滑地"吸附"到位。
   * 现在，"情绪" (Emotion) 这张橙色标签的卡片，完整地进入了"聚光灯"区域，清晰地展示着这个词所附带的情感共鸣 (`emotional_resonance`)。

3. 沉浸式探索：
   * 我不断地向上滑动，每一次都触发一次精准的"吸附"。我感觉自己像在操作一个制作精良的物理卷轴。我依次体验了"想象"(Vivid Imagery)、"词源"(Etymological Essence)等所有维度的内容卡片，彻底构建起对这个单词的立体认知。

4. 舞台聚光灯，每一次只会打在当前正在阅读的卡片上，而其他卡片是暗淡的，而且还带有微微的模糊，但是大体上还来辨认上面的数字。
```

### 输出示例

## 1. 视图层配置 (View Layer)

### 人类可读配置
```markdown
# SpotlightView 视图配置 (Story04: 垂直探索的磁性吸附心流)

## 基本信息
- **视图ID**: SpotlightView
- **故事引用**: Story04 - 垂直探索的磁性吸附心流
- **设计目标**: 实现单词学习的沉浸式体验，通过磁性吸附提供流畅的内容探索

## 布局设计
### 整体布局
- **布局类型**: vertical
- **设计理念**: 分为"单词锚点"和"内容舞台"两个核心区域

### 单词锚点区域 (wordAnchor)
> **设计意图**: 固定在顶部的信息展示区，让用户始终知道当前学习的单词
- **区域类型**: fixed_header
- **尺寸**: 120pt高度
- **背景**: 半透明毛玻璃效果

#### 组件列表
1. **当前单词显示**
   - **组件类型**: text
   - **样式**: 大标题，28pt，粗体，主题蓝色
   - **数据绑定**: `displayModel.currentWord`
   - **交互行为**: 无

2. **发音显示**
   - **组件类型**: text
   - **样式**: 说明文字，16pt，常规，灰色
   - **数据绑定**: `displayModel.pronunciation`
   - **交互行为**: 无

### 内容舞台区域 (contentStage)
> **设计意图**: 可滚动的卡片区域，展示单词的多维度解析内容
- **区域类型**: scrollable_cards
- **尺寸**: 填充剩余空间
- **背景**: 透明

#### 组件列表
1. **单词卡片堆叠**
   - **组件类型**: card_stack
   - **样式**: 圆角卡片，16pt间距
   - **数据绑定**: `displayModel.cards`
   - **交互行为**: 垂直滑动手势

## 动画效果
### 磁性吸附动画 (magneticSnap)
> **设计理念**: 模拟物理世界的磁性吸附感，提供精准的定位反馈
- **动画类型**: spring
- **参数**: 响应时间0.6秒，阻尼系数0.8
- **触发条件**: 手势滑动达到50pt阈值时
- **视觉效果**: 平滑吸附到最近的卡片位置

### 聚光灯效果 (spotlight)
> **设计理念**: 突出当前焦点卡片，营造舞台聚光灯的视觉效果
- **动画类型**: easeInOut
- **参数**: 0.3秒过渡时间
- **触发条件**: 卡片切换时
- **视觉效果**: 当前卡片清晰，其他卡片60%透明度+轻微模糊

## 状态管理
### 聚光灯状态 (spotlight)
- **触发条件**: `!displayModel.isTransitioning`
- **视觉效果**: 高亮当前卡片，调暗其他卡片

### 过渡状态 (transitioning)
- **触发条件**: `displayModel.isTransitioning`
- **视觉效果**: 所有卡片模糊处理，降低整体透明度

## 用户体验目标
1. 沉浸感：用户完全专注于当前单词的学习
2. 流畅性：手势操作自然流畅，无卡顿感
3. 反馈性：每个操作都有明确的视觉和触觉反馈
4. 探索性：鼓励用户深度探索单词的多个维度

## 技术实现注意事项
- 卡片内容懒加载，避免内存占用过大
- 手势冲突处理，确保垂直滑动不与系统手势冲突
- 无障碍支持，为视觉障碍用户提供VoiceOver支持
```

### 机器可执行配置
```json
{
  "viewId": "SpotlightView",
  "storyReference": "Story04",
  "metadata": {
    "humanConfigSource": "SpotlightView.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "layout": {
    "type": "vertical",
    "sections": [
      {
        "id": "wordAnchor",
        "type": "fixed_header",
        "height": 120,
        "background": {
          "type": "blur",
          "style": "systemMaterial"
        },
        "components": [
          {
            "type": "text",
            "id": "currentWord",
            "binding": "displayModel.currentWord",
            "style": {
              "font": "SF Pro Display",
              "size": 28,
              "weight": "bold",
              "color": "#0D47A1"
            }
          },
          {
            "type": "text",
            "id": "pronunciation",
            "binding": "displayModel.pronunciation",
            "style": {
              "font": "SF Pro Text",
              "size": 16,
              "weight": "regular",
              "color": "#666666"
            }
          }
        ]
      },
      {
        "id": "contentStage",
        "type": "scrollable_cards",
        "gesture": {
          "type": "vertical_swipe",
          "action": "magneticSnap",
          "threshold": 50,
          "haptic": "medium"
        },
        "components": [
          {
            "type": "card_stack",
            "id": "wordCards",
            "binding": "displayModel.cards",
            "spacing": 16
          }
        ]
      }
    ]
  },
  "animations": [
    {
      "name": "magneticSnap",
      "type": "spring",
      "response": 0.6,
      "dampingFraction": 0.8,
      "trigger": "gesture_threshold"
    },
    {
      "name": "spotlight",
      "type": "easeInOut",
      "duration": 0.3,
      "trigger": "card_change"
    }
  ],
  "states": [
    {
      "name": "spotlight",
      "condition": "!displayModel.isTransitioning",
      "effects": [
        {
          "target": "current_card",
          "property": "opacity",
          "value": 1.0
        },
        {
          "target": "other_cards",
          "property": "opacity",
          "value": 0.6
        },
        {
          "target": "other_cards",
          "property": "blur",
          "value": 2.0
        }
      ]
    }
  ]
}
```

## 使用说明

### 提示词使用步骤
1. **准备用户故事**：将完整的用户故事文本替换到 `{USER_STORY}` 位置
2. **执行生成**：按照1-10的顺序，逐层生成配置
3. **TADA架构验证**：检查Adapter层和Business层是否符合TADA架构原则
4. **验证配置**：检查生成的配置是否完整、一致
5. **迭代优化**：根据需要调整和完善配置内容

### TADA架构质量检查清单
- [ ] Adapter层严格遵循纯转译原则，无业务逻辑
- [ ] Adapter能力与adapter model层保持1:1映射
- [ ] 强制复用现有Adapter能力，默认只能复用和组合已有能力
- [ ] 新增Adapter能力已获得用户明确确认（如适用）
- [ ] Business层正确协调多个Adapter的调用
- [ ] 依赖注入配置正确管理组件生命周期
- [ ] 测试策略区分纯转译测试和业务逻辑测试

### 配置质量检查清单
- [ ] 每个配置都有明确的ID和故事引用
- [ ] 人类可读配置包含充分的设计意图说明
- [ ] 机器可执行配置结构完整，字段齐全
- [ ] 数据绑定路径正确，命名规范统一
- [ ] 动画参数合理，符合iOS设计规范
- [ ] 错误处理策略完善，覆盖主要异常情况
- [ ] 性能优化考虑充分，避免常见性能问题
- [ ] 安全配置覆盖数据保护和隐私要求

### 依赖完整性检查清单
- [ ] **依赖声明完整性**：所有在依赖注入中提到的服务都有完整的定义
- [ ] **服务定义完整性**：每个服务都包含人类可读配置和机器可执行配置
- [ ] **协议一致性**：服务的协议名称与依赖注入配置中的引用完全一致
- [ ] **方法签名完整性**：所有业务方法都有明确的参数类型和返回类型定义
- [ ] **数据模型完整性**：所有引用的数据结构都有完整的字段定义
- [ ] **依赖链完整性**：检查依赖链中的每个环节都有对应的服务定义
- [ ] **生命周期一致性**：服务的生命周期配置与其职责和使用场景匹配
- [ ] **初始化策略合理性**：服务的初始化策略（eager/lazy）符合性能要求
- [ ] **循环依赖检查**：确保依赖关系图中没有循环依赖
- [ ] **基础设施服务**：SQLiteManager、CacheService等基础服务定义完整
- [ ] **业务支持服务**：AnalyticsService、ContentService等支持服务定义完整
- [ ] **适配器依赖**：所有Adapter的依赖（如APIClient）都有明确定义

### 注意事项
1. **TADA架构遵循**：严格区分AI自动生成的转译层和人工设计的业务层
2. **现有能力复用**：优先使用现有Adapter，避免重复实现相同功能
3. **保持一致性**：确保十个层级的配置在命名、引用、数据流方面保持一致
4. **注重可读性**：人类可读配置应该像技术文档一样清晰易懂
5. **确保完整性**：机器可执行配置应该包含代码生成所需的所有信息
6. **考虑扩展性**：配置结构应该便于后续功能扩展和修改
7. **遵循规范**：遵循iOS开发和SwiftUI的最佳实践
8. **移除符号**：禁止在文档中添加任何符号

### 常见问题处理
- **Adapter能力不足**：当现有Adapter无法满足需求时，首先尝试能力组合，如仍无法满足则向用户说明情况并征询意见
- **架构违反**：当发现违反TADA架构原则时，重新设计组件职责分离
- **配置冲突**：当不同层级配置出现冲突时，优先保证数据流的一致性
- **依赖缺失**：当发现依赖注入中提到但未定义的服务时，必须补充完整的服务定义（包括人类可读配置和机器可执行配置）
- **依赖循环**：当发现循环依赖时，重新设计服务职责分离或引入中介者模式
- **基础设施缺失**：确保SQLiteManager、CacheService、FileManager等基础设施服务有完整定义
- **缺失信息**：当用户故事信息不足时，基于常见模式进行合理推断
- **复杂交互**：对于复杂的用户交互，分解为多个简单的状态转换
- **性能考虑**：在配置中明确标注性能敏感的操作和优化建议
- **安全风险**：识别潜在的安全风险并在配置中提供相应的防护措施

---