# 042｜TADA架构 - SenseWord项目架构说明书

## TADA架构经典文件结构

```
SensewordApp/
├── Views/                          # 📱 UI层
│   ├── SpotlightView.swift         # 主搜索界面
│   ├── WordDetailView.swift        # 单词详情页
│   └── Components/                 # 可复用组件
│       ├── SearchBar.swift
│       └── WordCard.swift
│
├── ViewModels/                     # 🎯 视图模型层
│   ├── SpotlightViewModel.swift    # UI状态管理
│   ├── WordDetailViewModel.swift   # 详情页状态
│   └── Base/
│       └── BaseViewModel.swift     # 通用UI状态
│
├── Services/                       # 💼 双层服务架构
│   ├── Business/                   # 业务逻辑层 (人工设计)
│   │   ├── WordBusinessService.swift      # 单词业务逻辑
│   │   ├── CacheBusinessService.swift     # 缓存策略
│   │   └── AnalyticsBusinessService.swift # 用户行为分析
│   │
│   └── Adapters/                   # 转译适配层 (AI生成)
│       ├── WordAPIAdapter.swift           # 单词API转译
│       ├── UserAPIAdapter.swift           # 用户API转译
│       └── AuthAPIAdapter.swift           # 认证API转译
│
├── Models/                         # 📊 数据模型层
│   ├── Business/                   # 业务模型 (人工设计)
│   │   ├── WordBusinessModel.swift        # 业务单词模型
│   │   ├── UserBusinessModel.swift        # 业务用户模型
│   │   └── CacheBusinessModel.swift       # 缓存业务模型
│   │
│   ├── Display/                    # 显示模型 (人工设计)
│   │   ├── WordDisplayModel.swift         # UI显示模型
│   │   └── SearchDisplayModel.swift       # 搜索显示模型
│   │
│   └── API/                        # API模型 (AI生成)
│       ├── WordAPIResponse.swift          # API响应模型
│       ├── UserAPIResponse.swift          # 用户API模型
│       └── AuthAPIResponse.swift          # 认证API模型
│
├── DI/                             # 🔧 依赖注入层
│   ├── DIContainer.swift           # 依赖注入容器
│   ├── ServiceFactory.swift       # 服务工厂
│   └── Protocols/                  # 依赖注入协议
│       ├── DIContainerProtocol.swift     # 容器协议
│       └── ServiceFactoryProtocol.swift  # 工厂协议
│
├── Network/                        # 🌐 网络基础设施
│   ├── APIClient.swift             # HTTP客户端
│   ├── NetworkConfig.swift         # 网络配置
│   └── NetworkError.swift          # 网络错误定义
│
├── Storage/                        # 💾 存储基础设施
│   ├── Cache/                      # 缓存层 (临时存储)
│   │   ├── MemoryCache.swift              # 内存缓存管理
│   │   ├── DiskCache.swift               # 磁盘缓存管理
│   │   └── CachePolicy.swift             # 缓存策略
│   │
│   └── Persistence/                # 持久化层 (永久存储)
│       ├── UserDefaultsStorage.swift     # 用户偏好存储
│       ├── CoreDataStack.swift           # Core Data栈
│       └── FileStorage.swift             # 文件存储
│
├── Core/                           # 🏗️ 核心基础设施
│   ├── App.swift                   # 应用入口
│   ├── AppDelegate.swift           # 应用代理
│   ├── Extensions/                 # 扩展
│   │   ├── String+Extensions.swift
│   │   └── Date+Extensions.swift
│   └── Utils/                      # 工具类
│       ├── Logger.swift
│       └── Constants.swift
│
├── SensewordAppTests/              # 🧪 单元测试 (项目根目录)
│   ├── ViewModelTests/             # ViewModel层测试
│   │   ├── SpotlightViewModelTests.swift
│   │   └── WordDetailViewModelTests.swift
│   ├── BusinessServiceTests/       # 业务服务层测试
│   │   ├── WordBusinessServiceTests.swift
│   │   ├── CacheBusinessServiceTests.swift
│   │   └── AnalyticsBusinessServiceTests.swift
│   ├── AdapterTests/               # 转译适配层测试
│   │   ├── WordAPIAdapterTests.swift
│   │   ├── UserAPIAdapterTests.swift
│   │   └── AuthAPIAdapterTests.swift
│   ├── ModelTests/                 # 数据模型测试
│   │   ├── BusinessModelTests.swift
│   │   ├── DisplayModelTests.swift
│   │   └── APIModelTests.swift
│   ├── NetworkTests/               # 网络层测试
│   │   ├── APIClientTests.swift
│   │   └── NetworkConfigTests.swift
│   ├── StorageTests/               # 存储层测试
│   │   ├── CacheTests.swift
│   │   └── PersistenceTests.swift
│   └── Mocks/                      # 测试Mock对象
│       ├── MockAPIAdapter.swift
│       ├── MockBusinessService.swift
│       └── MockNetworkClient.swift
│
└── SensewordAppUITests/            # 🧪 UI自动化测试 (项目根目录)
    ├── SpotlightViewUITests.swift
    ├── WordDetailViewUITests.swift
    └── NavigationUITests.swift
```

## 文件结构详细说明

### 🔧 DI/ - 依赖注入层
**作用**：统一管理应用中所有组件的依赖关系，实现松耦合设计

- **DIContainer.swift**：依赖注入容器，集中创建和配置所有服务实例
- **ServiceFactory.swift**：服务工厂，负责复杂对象的创建逻辑
- **Protocols/**：定义依赖注入相关的协议，便于测试和扩展

**核心价值**：
- 集中管理依赖关系，避免硬编码
- 支持单例、瞬态等不同生命周期管理
- 便于单元测试时注入mock对象
- 提供清晰的服务创建和配置入口

### 💾 Storage/ - 存储基础设施
**重新设计**：将原来的Cache/重构为更完整的存储层，区分缓存和持久化

#### Cache/ - 缓存层 (临时存储)
- **MemoryCache.swift**：内存缓存，快速访问，应用重启后丢失
- **DiskCache.swift**：磁盘缓存，容量更大，但访问稍慢
- **CachePolicy.swift**：缓存策略，定义TTL、LRU等缓存规则

#### Persistence/ - 持久化层 (永久存储)
- **UserDefaultsStorage.swift**：用户偏好设置，简单键值对存储
- **CoreDataStack.swift**：Core Data栈，复杂关系型数据存储
- **FileStorage.swift**：文件系统存储，大文件或自定义格式数据

**设计原则**：
- **缓存**：临时性、易失性、快速访问
- **持久化**：永久性、可靠性、数据安全

### 🏗️ Core/ - 核心基础设施
**新增**：应用级别的核心组件和工具

- **App.swift**：SwiftUI应用入口，配置应用生命周期
- **AppDelegate.swift**：UIKit应用代理，处理系统级事件
- **Extensions/**：Swift扩展，增强基础类型功能
- **Utils/**：工具类，提供通用功能和常量定义

### 🧪 SensewordAppTests/ - 单元测试框架
**重要**：测试文件必须位于iOS项目根目录，确保编译正确性

#### ViewModelTests/ - ViewModel层测试
- **SpotlightViewModelTests.swift**：搜索界面状态管理测试
- **WordDetailViewModelTests.swift**：单词详情页状态测试

#### BusinessServiceTests/ - 业务服务层测试
- **WordBusinessServiceTests.swift**：单词业务逻辑测试，包括缓存策略、错误处理
- **CacheBusinessServiceTests.swift**：缓存管理逻辑测试
- **AnalyticsBusinessServiceTests.swift**：用户行为分析测试

#### AdapterTests/ - 转译适配层测试
- **WordAPIAdapterTests.swift**：单词API转译测试，验证HTTP请求和JSON解析
- **UserAPIAdapterTests.swift**：用户API转译测试
- **AuthAPIAdapterTests.swift**：认证API转译测试

#### ModelTests/ - 数据模型测试
- **BusinessModelTests.swift**：业务模型数据结构测试
- **DisplayModelTests.swift**：显示模型转换测试
- **APIModelTests.swift**：API响应模型解析测试

#### NetworkTests/ - 网络层测试
- **APIClientTests.swift**：HTTP客户端功能测试
- **NetworkConfigTests.swift**：网络配置测试

#### StorageTests/ - 存储层测试
- **CacheTests.swift**：缓存机制测试
- **PersistenceTests.swift**：持久化存储测试

#### Mocks/ - 测试Mock对象
- **MockAPIAdapter.swift**：API适配器Mock，用于业务层测试
- **MockBusinessService.swift**：业务服务Mock，用于ViewModel测试
- **MockNetworkClient.swift**：网络客户端Mock，用于适配器测试

### 🧪 SensewordAppUITests/ - UI自动化测试
**功能**：端到端UI自动化测试，验证用户交互流程

- **SpotlightViewUITests.swift**：搜索界面UI自动化测试
- **WordDetailViewUITests.swift**：单词详情页UI测试
- **NavigationUITests.swift**：应用导航流程测试

**测试策略**：
- **单元测试**：专注单一组件功能验证，快速反馈
- **UI测试**：验证完整用户流程，确保端到端功能正确
- **Mock策略**：每层独立测试，避免外部依赖影响测试稳定性

## 背景

用户询问对前端双层服务架构设计的理解，希望通过详细的 mermaid 图表来理解整个流程，包括完整的流程图、时序图、关键数据结构转化过程、系统架构图和详细易懂的文字说明。

## 核心问题

用户想了解：
1. 这个计划是怎么一步步完成的
2. 与标准MVVM有什么不同，解决了哪些问题
3. 完整的架构设计原理和实施方案

## TADA架构命名

经过讨论，我们为这个双层服务架构确定了一个简洁的名称：

**TADA架构** = **Translation-Adapter & Domain-Adapter**

- **TA层**：Translation-Adapter（转译适配器）- AI自动生成
- **DA层**：Domain-Adapter（业务领域适配器）- 人工精心设计

### 命名优势
1. **简洁易记**：4个字母，发音简单
2. **语义清晰**：直接体现双层分离的核心思想
3. **技术内涵**：强调适配器模式和职责分离
4. **便于传播**：类似"tada"的完成感，符合解决问题的特性

## TADA架构解决的传统MVVM问题

### 1. 职责混乱问题的根本解决

传统MVVM架构最大的问题是**职责边界模糊**。在标准MVVM中，ViewModel往往承担了过多责任：

**传统MVVM的问题：**
- ViewModel既要管理UI状态，又要处理业务逻辑
- Service层职责不清，API调用、数据转换、缓存管理混在一起
- 数据模型层次不清，API响应直接用于UI显示
- 测试困难，因为各层耦合严重

**TADA架构的解决方案：**

TADA通过**严格的职责分离**彻底解决了这些问题：

1. **ViewModel专注UI状态**：只负责UI相关的状态管理，如loading、error、数据展示状态
2. **BusinessService专注业务逻辑**：缓存策略、数据验证、业务规则、用户行为分析
3. **APIAdapter专注数据转换**：纯粹的API调用和数据格式转换，无状态设计
4. **三层数据模型**：API模型→业务模型→显示模型，每层职责明确

### 2. 可维护性的显著提升

**传统MVVM维护困难的原因：**
- API变更影响整个调用链
- 业务逻辑散落在多个文件中
- 测试需要mock整个复杂的依赖链
- 新功能开发需要修改多个现有文件

**TADA架构的维护优势：**

1. **API变更隔离**：API变更只影响对应的APIAdapter，业务逻辑层完全不受影响
2. **业务逻辑集中**：所有业务规则集中在BusinessService中，易于理解和修改
3. **测试友好**：每层都可以独立测试，mock简单清晰
4. **扩展性强**：新增功能只需添加新的Adapter和BusinessService，不影响现有代码

### 3. 开发效率的革命性提升

**TADA架构的开发效率优势：**

1. **AI自动化生成**：APIAdapter层可以完全由AI根据API文档自动生成
2. **并行开发**：前端和后端可以基于API契约并行开发，互不阻塞
3. **代码复用**：BusinessService可以在不同UI界面间复用
4. **快速迭代**：UI变更只影响ViewModel和View，业务逻辑保持稳定

### 4. 团队协作的优化

**传统MVVM的团队协作问题：**
- 前后端开发相互依赖，阻塞严重
- 代码review困难，因为单个文件职责过多
- 新人上手困难，需要理解整个复杂的调用链

**TADA架构的团队协作优势：**
- **清晰的分工**：AI负责机械转换，人工负责智能决策
- **独立开发**：各层可以独立开发和测试
- **代码review高效**：每个文件职责单一，review重点明确
- **新人友好**：架构层次清晰，学习曲线平缓

## 系统架构总览

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
graph TB
    subgraph "🖼️ UI层 - 用户界面"
        A["📱 SpotlightView<br/>用户交互界面"]
    end

    subgraph "🧠 协调层 - 状态管理"
        B["🎯 SpotlightViewModel<br/>UI状态协调器"]
    end

    subgraph "🎯 业务服务层 - 复杂业务逻辑"
        C["💼 WordBusinessService<br/>缓存·重试·验证"]
        D["📚 BookmarkBusinessService<br/>本地状态管理"]
        E["🔐 AuthBusinessService<br/>认证状态处理"]
    end

    subgraph "🔄 纯转译层 - 机械转换"
        F["🔌 WordAPIAdapter<br/>HTTP请求转换"]
        G["🔌 BookmarkAPIAdapter<br/>JSON解析处理"]
        H["🔌 AuthAPIAdapter<br/>数据类型转换"]
    end

    subgraph "🛰️ 网络基础设施"
        I["🌐 APIClient<br/>统一网络请求"]
    end

    subgraph "☁️ 后端服务"
        J["🏢 api.senseword.app<br/>核心业务API"]
        K["🔒 auth.senseword.app<br/>认证服务"]
    end

    A -.->|"用户操作事件"| B
    B -.->|"UI状态更新"| A

    B -->|"调用业务逻辑"| C
    B -->|"调用业务逻辑"| D
    B -->|"调用业务逻辑"| E

    C -->|"纯数据请求"| F
    D -->|"纯数据请求"| G
    E -->|"纯数据请求"| H

    F -->|"HTTP调用"| I
    G -->|"HTTP调用"| I
    H -->|"HTTP调用"| I

    I -->|"网络请求"| J
    I -->|"认证请求"| K
```

## 用户查词完整时序流程

```mermaid
%%{init: {'theme':'dark', 'themeVariables': { 'primaryColor': '#4a90e2', 'primaryTextColor': '#ffffff', 'primaryBorderColor': '#ffffff', 'lineColor': '#ffffff', 'secondaryColor': '#5cb85c', 'tertiaryColor': '#f0ad4e', 'actorBorder': '#ffffff', 'actorBkg': '#2d2d2d', 'actorTextColor': '#ffffff', 'actorLineColor': '#ffffff', 'signalColor': '#ffffff', 'signalTextColor': '#ffffff', 'labelBoxBkgColor': '#2d2d2d', 'labelBoxBorderColor': '#ffffff', 'labelTextColor': '#ffffff', 'loopTextColor': '#ffffff', 'noteBorderColor': '#ffffff', 'noteBkgColor': '#2d2d2d', 'noteTextColor': '#ffffff'}}}%%
sequenceDiagram
    participant U as 👤 用户
    participant V as 📱 View
    participant VM as 🎯 ViewModel
    participant BS as 💼 BusinessService
    participant Cache as 💾 Cache
    participant Adapter as 🔌 APIAdapter
    participant API as 🌐 APIClient
    participant Backend as ☁️ 后端

    Note over U,Backend: 查询单词 "eloquence" 的完整流程

    %% 用户输入阶段
    U->>V: 输入 "eloquence"
    V->>VM: searchWord("eloquence")
    VM->>V: 设置 isLoading = true

    %% 业务逻辑处理
    VM->>BS: fetchWord("eloquence")
    BS->>BS: 输入验证：word.lowercased().trim()

    %% 缓存检查
    BS->>Cache: getWord("eloquence")
    Cache->>BS: 返回缓存结果

    alt 缓存命中且未过期
        Note over BS,Cache: 🎯 缓存策略生效
        BS->>BS: 更新访问统计
        BS->>VM: 返回 WordBusinessModel
        VM->>VM: 转换为 WordDisplayModel
        VM->>V: 更新UI，isLoading = false
        V->>U: 显示单词信息
    else 缓存未命中或已过期
        Note over BS,Backend: 🌐 网络请求流程

        %% API调用链
        BS->>Adapter: fetchWordRaw("eloquence")
        Adapter->>Adapter: 构建HTTP请求
        Adapter->>API: get("/api/v1/word/eloquence")
        API->>Backend: HTTP GET请求

        %% 响应处理
        Backend->>API: JSON响应
        Note over Backend,API: {"word":"eloquence","pronunciation":"/ˈeləkwəns/"}
        API->>Adapter: HTTP响应
        Adapter->>Adapter: JSON解析为WordAPIResponse
        Adapter->>BS: 返回 WordAPIResponse

        %% 业务数据处理
        Note over BS: 🎯 业务数据处理
        BS->>BS: 转换为 WordBusinessModel
        BS->>BS: 添加业务字段（访问时间等）

        %% 缓存更新
        BS->>Cache: setWord("eloquence", model, 24h)
        Cache->>BS: 缓存保存完成
        BS->>BS: 记录分析数据

        %% 返回结果
        BS->>VM: 返回 WordBusinessModel
        VM->>VM: 转换为 WordDisplayModel
        VM->>V: 更新UI，isLoading = false
        V->>U: 显示单词信息
    end

    %% 错误处理流程
    Note over U,Backend: 🚨 错误处理流程

    Backend-->>API: 网络错误
    API-->>Adapter: 抛出异常
    Adapter-->>BS: 传递异常
    BS->>BS: 错误分析和处理

    alt 可重试错误
        BS->>BS: 重试逻辑（最多3次）
        BS->>Adapter: 重新请求
    else 不可重试错误
        BS->>Cache: getStaleWord("eloquence")
        Cache->>BS: 返回过期缓存结果

        alt 有过期缓存
            BS->>VM: 返回过期数据+警告
            VM->>V: 显示数据+网络提示
            V->>U: 显示内容（带网络警告）
        else 无可用数据
            BS->>VM: 抛出业务异常
            VM->>V: 显示错误页面
            V->>U: 显示错误信息
        end
    end
```

## 数据结构转换生命周期

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
graph TD
    subgraph "☁️ 后端API响应"
        A["📄 JSON原始数据<br/>{<br/>  'word': 'eloquence',<br/>  'pronunciation': '/ˈeləkwəns/',<br/>  'definitions': [...],<br/>  'createdAt': '2024-01-15T10:30:00Z',<br/>  'metadata': {<br/>    'aiReviewScore': 8,<br/>    'wordFrequency': 1200<br/>  }<br/>}"]
    end

    subgraph "🔄 转译层数据 (API DTO)"
        B["🔌 WordAPIResponse<br/>struct WordAPIResponse {<br/>  let word: String<br/>  let pronunciation: String?<br/>  let definitions: [DefinitionAPIResponse]<br/>  let createdAt: String  // ISO8601<br/>  let metadata: MetadataAPIResponse?<br/>}"]
    end

    subgraph "🎯 业务层数据 (Business DTO)"
        C["💼 WordBusinessModel<br/>struct WordBusinessModel {<br/>  let word: String<br/>  let pronunciation: String?<br/>  let definitions: [DefinitionBusinessModel]<br/>  let createdAt: Date  // 转换为Date<br/>  let metadata: WordMetadata<br/>  // 业务增强字段<br/>  let lastAccessTime: Date?<br/>  let accessCount: Int<br/>  let isCached: Bool<br/>  var difficultyLevel: DifficultyLevel<br/>}"]
    end

    subgraph "📱 视图层数据 (View DTO)"
        D["🖼️ WordDisplayModel<br/>class WordDisplayModel: ObservableObject {<br/>  @Published var word: String<br/>  @Published var pronunciationText: String<br/>  @Published var mainDefinition: String<br/>  @Published var difficultyBadge: DifficultyBadge<br/>  @Published var isLoading: Bool<br/>  // UI计算属性<br/>  var formattedCreatedDate: String<br/>  var difficultyColor: Color<br/>  var pronunciationSymbol: String<br/>}"]
    end

    subgraph "🖼️ UI界面展示"
        E["📱 SwiftUI视图<br/>Text(model.word)<br/>Text(model.pronunciationText)<br/>Badge(model.difficultyBadge)<br/>Text(model.formattedCreatedDate)<br/>Button(model.pronunciationSymbol)"]
    end

    A -->|"JSON解析<br/>Codable自动转换"| B
    B -->|"业务转换器<br/>WordBusinessModelConverter"| C
    C -->|"视图转换器<br/>WordDisplayModelConverter"| D
    D -->|"SwiftUI绑定<br/>@Published属性"| E

    subgraph "🔄 转换过程详解"
        F["🔧 API→Business转换<br/>• String日期 → Date对象<br/>• 添加业务字段<br/>• 枚举类型转换<br/>• 计算属性生成"]

        G["🎨 Business→View转换<br/>• 选择主要定义<br/>• 生成难度徽章<br/>• 格式化显示文本<br/>• 初始化UI状态"]
    end

    B -.->|"转换逻辑"| F
    F -.-> C
    C -.->|"转换逻辑"| G
    G -.-> D
```

## 双层服务架构职责分离

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
graph LR
    subgraph "🔄 纯转译层 - 100%机械化"
        A["🔌 WordAPIAdapter<br/><br/>✅ 职责范围：<br/>• HTTP请求构建<br/>• JSON数据解析<br/>• 数据类型转换<br/>• 1:1映射API端点<br/><br/>🚫 不包含：<br/>• 业务逻辑<br/>• 缓存管理<br/>• 错误重试<br/>• 数据验证<br/><br/>🤖 AI可自动生成<br/>📄 从API文档生成<br/>⚡ 无状态设计"]
    end

    subgraph "🎯 业务服务层 - 复杂业务逻辑"
        B["💼 WordBusinessService<br/><br/>✅ 职责范围：<br/>• 输入验证和标准化<br/>• 智能缓存策略<br/>• 错误处理和重试<br/>• 数据增强和计算<br/>• 性能监控分析<br/>• 用户行为追踪<br/>• 降级策略处理<br/><br/>🧠 需要人工设计<br/>📊 业务逻辑复杂<br/>🔄 有状态管理"]
    end

    A -->|"纯数据传递"| B
```

## 与标准MVVM的核心区别

### 标准MVVM架构的问题

```mermaid
%%{init: {'theme':'dark', 'themeVariables': { 'primaryColor': '#4a90e2', 'primaryTextColor': '#ffffff', 'primaryBorderColor': '#ffffff', 'lineColor': '#ffffff', 'secondaryColor': '#5cb85c', 'tertiaryColor': '#f0ad4e', 'background': '#1a1a1a', 'mainBkg': '#2d2d2d', 'secondBkg': '#3d3d3d', 'tertiaryBkg': '#4d4d4d'}}}%%
graph TB
    subgraph "❌ 标准MVVM架构问题"
        A1["📱 View<br/>UI界面"]
        B1["🧠 ViewModel<br/>UI状态 + 部分业务逻辑"]
        C1["🔧 Service<br/>混合职责：<br/>• HTTP请求构建<br/>• JSON解析<br/>• 缓存管理<br/>• 错误处理<br/>• 重试机制<br/>• 数据验证<br/><br/>❌ 问题：<br/>• AI难以生成（逻辑复杂）<br/>• 维护困难（职责混合）<br/>• 测试复杂（需mock多层）<br/>• API变更影响业务逻辑"]
        D1["☁️ 后端API"]
    end

    subgraph "✅ 双层服务架构优势"
        A2["📱 View<br/>UI界面"]
        B2["🧠 ViewModel<br/>纯UI状态管理"]
        C2["💼 BusinessService<br/>业务逻辑：<br/>• 缓存策略<br/>• 错误处理<br/>• 数据验证<br/>• 重试机制<br/>• 性能监控<br/><br/>🧠 人工精心设计"]
        D2["🔌 APIAdapter<br/>纯转译：<br/>• HTTP请求<br/>• JSON解析<br/>• 数据转换<br/><br/>🤖 AI自动生成"]
        E2["☁️ 后端API"]
    end

    A1 --> B1
    B1 --> C1
    C1 --> D1

    A2 --> B2
    B2 --> C2
    C2 --> D2
    D2 --> E2
```

## AI自动生成与人工设计分工

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
flowchart TD
    subgraph "📋 输入阶段"
        A["📄 API文档<br/>OpenAPI/Swagger<br/>• 端点定义<br/>• 数据结构<br/>• 请求/响应格式"]
        B["📝 业务需求<br/>产品规格说明<br/>• 缓存策略<br/>• 错误处理<br/>• 用户体验要求"]
    end

    subgraph "🤖 AI自动生成区域"
        C["🔧 AI代码生成器<br/>• 解析API文档<br/>• 生成数据模型<br/>• 创建网络请求代码<br/>• 自动化测试用例"]

        D["📦 自动生成产物<br/>• WordAPIAdapter.swift<br/>• BookmarkAPIAdapter.swift<br/>• AuthAPIAdapter.swift<br/>• APIResponse数据模型<br/>• 基础网络测试"]
    end

    subgraph "🧠 人工设计区域"
        E["👨‍💻 架构师设计<br/>• 业务逻辑分析<br/>• 缓存策略设计<br/>• 错误处理方案<br/>• 性能优化策略"]

        F["🏗️ 人工实现产物<br/>• WordBusinessService.swift<br/>• 缓存管理器<br/>• 错误处理器<br/>• 分析服务<br/>• 业务测试用例"]
    end

    subgraph "🔄 集成与验证"
        G["🔗 系统集成<br/>• 连接转译层和业务层<br/>• 配置依赖注入<br/>• 端到端测试<br/>• 性能基准测试"]

        H["✅ 质量保证<br/>• 代码审查<br/>• 自动化测试<br/>• 性能监控<br/>• 用户验收测试"]
    end

    subgraph "📊 开发效率对比"
        I["⚡ 效率提升<br/>• 减少70%模板代码<br/>• 5分钟生成转译层<br/>• 消除API对接错误<br/>• 专注高价值工作"]
    end

    A -->|"标准化输入"| C
    B -->|"需求分析"| E

    C -->|"5分钟完成"| D
    E -->|"专业设计"| F

    D -->|"机械转换层"| G
    F -->|"业务逻辑层"| G

    G --> H
    H --> I
```

## TADA架构命名示意图

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
graph TB
    subgraph "🎯 TADA架构 (Translation-Adapter & Domain-Adapter)"
        A["📱 View Layer<br/>UI界面层"]
        B["🧠 ViewModel Layer<br/>视图模型层"]

        subgraph "DA - Domain Adapter 业务领域适配器"
            C["💼 WordBusinessService<br/>🧠 人工精心设计<br/>• 缓存策略<br/>• 错误处理<br/>• 业务逻辑<br/>• 性能监控"]
        end

        subgraph "TA - Translation Adapter 转译适配器"
            D["🔌 WordAPIAdapter<br/>🤖 AI自动生成<br/>• HTTP请求<br/>• JSON解析<br/>• 数据转换<br/>• 类型映射"]
        end

        E["🌐 Network Layer<br/>网络基础层"]
        F["☁️ Backend API<br/>后端服务"]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
```

## 解决的核心问题详解

### 1. 职责混合问题 ❌→✅

**标准MVVM的问题：**
```swift
// 传统Service - 职责混合
class WordService {
    func fetchWord(_ word: String) async throws -> WordModel {
        // 🔴 HTTP请求构建（机械转译）
        let url = "/api/v1/word/\(word)"
        let request = URLRequest(url: URL(string: url)!)

        // 🔴 JSON解析（机械转译）
        let (data, _) = try await URLSession.shared.data(for: request)
        let apiResponse = try JSONDecoder().decode(WordAPIResponse.self, from: data)

        // 🔴 业务逻辑（复杂决策）
        let normalizedWord = word.lowercased().trim()
        if let cached = cache.getWord(normalizedWord) { return cached }

        // 🔴 错误处理（复杂决策）
        // 🔴 重试机制（复杂决策）
        // 🔴 缓存策略（复杂决策）

        // ❌ 问题：AI无法区分哪些可以自动生成，哪些需要人工设计
    }
}
```

**双层架构的解决方案：**
```swift
// ✅ 转译层 - 100%机械化，AI可自动生成
class WordAPIAdapter {
    func fetchWordRaw(_ word: String) async throws -> WordAPIResponse {
        let url = "/api/v1/word/\(word)"
        let response = try await apiClient.get(url)
        return try JSONDecoder().decode(WordAPIResponse.self, from: response)
    }
}

// ✅ 业务层 - 100%业务逻辑，人工精心设计
class WordBusinessService {
    func fetchWord(_ word: String) async throws -> WordModel {
        let normalizedWord = word.lowercased().trim()
        if let cached = await cache.getWord(normalizedWord) { return cached }

        let apiResponse = try await adapter.fetchWordRaw(normalizedWord)
        // 复杂的业务逻辑处理...
    }
}
```

### 2. AI生成困难问题 ❌→✅

**标准MVVM：**
- AI无法判断哪些代码可以自动生成
- 业务逻辑和网络代码混合，生成质量低
- 需要大量人工修改和调试

**双层架构：**
- **转译层**：从API文档100%自动生成，无需人工干预
- **业务层**：专注业务逻辑，人工精心设计
- **清晰分工**：AI负责机械转换，人工负责智能决策

### 3. 维护困难问题 ❌→✅

```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e8f5e8', 'primaryTextColor': '#000000', 'primaryBorderColor': '#000000', 'lineColor': '#333333', 'secondaryColor': '#fff3e0', 'tertiaryColor': '#e1f5fe'}}}%%
graph TD
    subgraph "❌ 标准MVVM维护问题"
        A1["🔧 WordService<br/>混合职责"]
        B1["API端点变更"]
        C1["业务逻辑变更"]
        D1["❌ 影响范围：<br/>• 需要同时修改网络代码<br/>• 需要同时修改业务逻辑<br/>• 测试需要mock多层<br/>• 容易引入bug"]

        B1 --> A1
        C1 --> A1
        A1 --> D1
    end

    subgraph "✅ 双层架构维护优势"
        A2["🔌 APIAdapter<br/>纯转译"]
        B2["💼 BusinessService<br/>纯业务"]
        C2["API端点变更"]
        D2["业务逻辑变更"]
        E2["✅ 隔离影响：<br/>• API变更只影响转译层<br/>• 业务变更只影响业务层<br/>• 独立测试，互不干扰<br/>• 并行开发，提升效率"]

        C2 --> A2
        D2 --> B2
        A2 --> E2
        B2 --> E2
    end
```

### 4. 测试复杂性问题 ❌→✅

**标准MVVM测试困难：**
```swift
// ❌ 需要同时mock网络和业务逻辑
class WordServiceTests: XCTestCase {
    func testFetchWord() {
        // 需要mock HTTP请求
        // 需要mock缓存逻辑
        // 需要mock错误处理
        // 需要mock重试机制
        // 测试用例复杂，维护困难
    }
}
```

**双层架构测试简化：**
```swift
// ✅ 转译层测试：纯网络mock
class WordAPIAdapterTests: XCTestCase {
    func testFetchWordRaw() {
        // 只需要mock HTTP响应
        // 验证JSON解析正确性
        // 测试简单，专注单一职责
    }
}

// ✅ 业务层测试：业务逻辑验证
class WordBusinessServiceTests: XCTestCase {
    func testCacheStrategy() {
        // Mock APIAdapter（简单）
        // 验证缓存逻辑
        // 验证错误处理
        // 测试专注业务价值
    }
}
```

### 5. 开发效率问题 ❌→✅

**标准MVVM开发流程：**
1. 分析API文档 → 手写网络代码 → 手写业务逻辑 → 调试网络问题 → 调试业务问题
2. **时间消耗**：大量时间花在重复的网络代码编写上
3. **错误率高**：API对接容易出错，影响业务逻辑开发

**双层架构开发流程：**
1. API文档 → **AI自动生成转译层（5分钟）** → 专注业务逻辑设计 → 快速集成测试
2. **时间节省**：减少70%模板代码编写
3. **质量提升**：消除API对接错误，专注业务价值创造


## 总结：核心价值

这个TADA架构不是对MVVM的替代，而是对MVVM的**精确增强**：

1. **保留MVVM优势**：View-ViewModel的绑定关系不变
2. **解决Service层问题**：通过分层彻底分离职责
3. **引入AI自动化**：让机器做机器擅长的事（转译）
4. **突出人工价值**：让人专注人擅长的事（业务逻辑）

这是一个**面向AI时代的前端架构设计**，既保持了传统架构的稳定性，又充分利用了AI的自动化能力，实现了开发效率和代码质量的双重提升。

**TADA架构** 既体现了技术内涵，又便于记忆和传播，是一个很好的命名选择！

## SenseWord单词搜索完整示例代码

### 1. API模型层 (AI自动生成)

```swift
// WordAPIResponse.swift - API响应模型
struct WordAPIResponse: Codable {
    let word: String
    let pronunciation: String
    let definitions: [DefinitionAPIResponse]
    let examples: [String]
    let createdAt: String
    let metadata: WordMetadataAPIResponse
}

struct DefinitionAPIResponse: Codable {
    let partOfSpeech: String
    let meaning: String
    let contextualExplanation: String
}

struct WordMetadataAPIResponse: Codable {
    let aiReviewScore: Int
    let wordFrequency: Int
    let isWordOfTheDayCandidate: Bool
    let relatedConcepts: [String]
}
```

### 2. 转译适配层 (AI自动生成)

```swift
// WordAPIAdapter.swift - 纯转译层
import Foundation

protocol WordAPIAdapterProtocol {
    func fetchWord(_ word: String) async throws -> WordAPIResponse
}

class WordAPIAdapter: WordAPIAdapterProtocol {
    private let apiClient: APIClient

    init(apiClient: APIClient) {
        self.apiClient = apiClient
    }

    func fetchWord(_ word: String) async throws -> WordAPIResponse {
        // 构建HTTP请求
        let endpoint = "/api/v1/word/\(word.lowercased())"
        let headers = [
            "X-Static-API-Key": APIConfig.staticAPIKey,
            "Accept": "application/json"
        ]

        // 执行HTTP请求
        let response: WordAPIResponse = try await apiClient.get(
            endpoint: endpoint,
            headers: headers
        )

        return response
    }
}
```

### 3. 业务模型层 (人工设计)

```swift
// WordBusinessModel.swift - 业务模型
import Foundation

struct WordBusinessModel {
    let word: String
    let pronunciation: String
    let definitions: [DefinitionBusinessModel]
    let examples: [String]
    let metadata: WordMetadataBusinessModel

    // 业务字段
    let accessedAt: Date
    let accessCount: Int
    let isBookmarked: Bool
    let cacheExpiresAt: Date
}

struct DefinitionBusinessModel {
    let partOfSpeech: String
    let meaning: String
    let contextualExplanation: String
    let isMainDefinition: Bool  // 业务逻辑字段
}

struct WordMetadataBusinessModel {
    let aiReviewScore: Int
    let wordFrequency: Int
    let isWordOfTheDayCandidate: Bool
    let relatedConcepts: [String]
    let difficultyLevel: DifficultyLevel  // 业务计算字段
}

enum DifficultyLevel: String, CaseIterable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"
    case expert = "expert"
}
```

### 4. 业务逻辑层 (人工设计)

```swift
// WordBusinessService.swift - 业务逻辑层
import Foundation

protocol WordBusinessServiceProtocol {
    func fetchWord(_ word: String) async throws -> WordBusinessModel
}

class WordBusinessService: WordBusinessServiceProtocol {
    private let apiAdapter: WordAPIAdapterProtocol
    private let cacheService: CacheBusinessService
    private let analyticsService: AnalyticsBusinessService

    init(
        apiAdapter: WordAPIAdapterProtocol,
        cacheService: CacheBusinessService,
        analyticsService: AnalyticsBusinessService
    ) {
        self.apiAdapter = apiAdapter
        self.cacheService = cacheService
        self.analyticsService = analyticsService
    }

    func fetchWord(_ word: String) async throws -> WordBusinessModel {
        // 1. 输入验证和标准化
        let normalizedWord = word.lowercased().trimmingCharacters(in: .whitespacesAndNewlines)
        guard !normalizedWord.isEmpty else {
            throw WordBusinessError.invalidInput
        }

        // 2. 缓存策略检查
        if let cachedWord = await cacheService.getWord(normalizedWord),
           !cacheService.isExpired(cachedWord) {
            // 更新访问统计
            await analyticsService.recordWordAccess(normalizedWord, source: .cache)
            return cachedWord
        }

        // 3. API调用
        do {
            let apiResponse = try await apiAdapter.fetchWord(normalizedWord)

            // 4. 业务数据转换
            let businessModel = convertToBusinessModel(apiResponse)

            // 5. 缓存更新
            await cacheService.setWord(normalizedWord, model: businessModel, ttl: .hours(24))

            // 6. 分析数据记录
            await analyticsService.recordWordAccess(normalizedWord, source: .api)

            return businessModel

        } catch {
            // 7. 错误处理和降级策略
            return try await handleFetchError(normalizedWord, error: error)
        }
    }

    // MARK: - 私有方法

    private func convertToBusinessModel(_ apiResponse: WordAPIResponse) -> WordBusinessModel {
        let definitions = apiResponse.definitions.enumerated().map { index, def in
            DefinitionBusinessModel(
                partOfSpeech: def.partOfSpeech,
                meaning: def.meaning,
                contextualExplanation: def.contextualExplanation,
                isMainDefinition: index == 0  // 业务逻辑：第一个定义为主要定义
            )
        }

        let metadata = WordMetadataBusinessModel(
            aiReviewScore: apiResponse.metadata.aiReviewScore,
            wordFrequency: apiResponse.metadata.wordFrequency,
            isWordOfTheDayCandidate: apiResponse.metadata.isWordOfTheDayCandidate,
            relatedConcepts: apiResponse.metadata.relatedConcepts,
            difficultyLevel: calculateDifficultyLevel(
                score: apiResponse.metadata.aiReviewScore,
                frequency: apiResponse.metadata.wordFrequency
            )
        )

        return WordBusinessModel(
            word: apiResponse.word,
            pronunciation: apiResponse.pronunciation,
            definitions: definitions,
            examples: apiResponse.examples,
            metadata: metadata,
            accessedAt: Date(),
            accessCount: 1,
            isBookmarked: false,  // 默认值，后续可通过其他服务更新
            cacheExpiresAt: Date().addingTimeInterval(24 * 3600)
        )
    }

    private func calculateDifficultyLevel(score: Int, frequency: Int) -> DifficultyLevel {
        // 业务逻辑：根据AI评分和词频计算难度等级
        switch (score, frequency) {
        case (9...10, 2000...):
            return .beginner
        case (7...8, 1000..<2000):
            return .intermediate
        case (5...6, 500..<1000):
            return .advanced
        default:
            return .expert
        }
    }

    private func handleFetchError(_ word: String, error: Error) async throws -> WordBusinessModel {
        // 错误处理策略
        if let networkError = error as? NetworkError,
           networkError.isRetryable {
            // 可重试错误：尝试获取过期缓存
            if let staleWord = await cacheService.getStaleWord(word) {
                await analyticsService.recordWordAccess(word, source: .staleCache)
                return staleWord
            }
        }

        // 不可恢复错误
        await analyticsService.recordError(word, error: error)
        throw WordBusinessError.fetchFailed(underlying: error)
    }
}

// 业务错误定义
enum WordBusinessError: Error {
    case invalidInput
    case fetchFailed(underlying: Error)
    case cacheError
}
```

### 5. 显示模型层 (人工设计)

```swift
// WordDisplayModel.swift - UI显示模型
import Foundation

struct WordDisplayModel {
    let word: String
    let pronunciationText: String
    let mainDefinition: String
    let allDefinitions: [DefinitionDisplayModel]
    let examples: [String]
    let difficultyBadge: DifficultyBadge
    let isBookmarked: Bool
    let accessInfo: String

    // UI特定字段
    let shouldShowPronunciation: Bool
    let shouldShowExamples: Bool
    let backgroundColor: String
    let textColor: String
}

struct DefinitionDisplayModel {
    let partOfSpeech: String
    let meaning: String
    let explanation: String
    let isExpanded: Bool
    let badgeColor: String
}

struct DifficultyBadge {
    let text: String
    let color: String
    let icon: String
}
```

### 6. ViewModel层 (人工设计)

```swift
// SpotlightViewModel.swift - UI状态管理
import Foundation
import Combine

@MainActor
class SpotlightViewModel: ObservableObject {
    // UI状态
    @Published var searchText: String = ""
    @Published var isLoading: Bool = false
    @Published var currentWord: WordDisplayModel?
    @Published var errorMessage: String?
    @Published var showError: Bool = false

    // 依赖注入
    private let wordBusinessService: WordBusinessServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    init(wordBusinessService: WordBusinessServiceProtocol) {
        self.wordBusinessService = wordBusinessService
        setupSearchDebounce()
    }

    // MARK: - 公共方法

    func searchWord(_ word: String) {
        guard !word.isEmpty else {
            clearCurrentWord()
            return
        }

        Task {
            await performSearch(word)
        }
    }

    func clearSearch() {
        searchText = ""
        clearCurrentWord()
    }

    func retryLastSearch() {
        guard !searchText.isEmpty else { return }
        searchWord(searchText)
    }

    // MARK: - 私有方法

    private func setupSearchDebounce() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] searchText in
                self?.searchWord(searchText)
            }
            .store(in: &cancellables)
    }

    private func performSearch(_ word: String) async {
        isLoading = true
        errorMessage = nil
        showError = false

        do {
            let businessModel = try await wordBusinessService.fetchWord(word)
            let displayModel = convertToDisplayModel(businessModel)

            currentWord = displayModel

        } catch {
            handleSearchError(error)
        }

        isLoading = false
    }

    private func convertToDisplayModel(_ businessModel: WordBusinessModel) -> WordDisplayModel {
        let definitions = businessModel.definitions.map { def in
            DefinitionDisplayModel(
                partOfSpeech: def.partOfSpeech,
                meaning: def.meaning,
                explanation: def.contextualExplanation,
                isExpanded: def.isMainDefinition,  // 主要定义默认展开
                badgeColor: getBadgeColor(for: def.partOfSpeech)
            )
        }

        let difficultyBadge = DifficultyBadge(
            text: businessModel.metadata.difficultyLevel.rawValue.capitalized,
            color: getDifficultyColor(businessModel.metadata.difficultyLevel),
            icon: getDifficultyIcon(businessModel.metadata.difficultyLevel)
        )

        return WordDisplayModel(
            word: businessModel.word,
            pronunciationText: businessModel.pronunciation,
            mainDefinition: businessModel.definitions.first?.meaning ?? "",
            allDefinitions: definitions,
            examples: businessModel.examples,
            difficultyBadge: difficultyBadge,
            isBookmarked: businessModel.isBookmarked,
            accessInfo: formatAccessInfo(businessModel.accessedAt, businessModel.accessCount),
            shouldShowPronunciation: !businessModel.pronunciation.isEmpty,
            shouldShowExamples: !businessModel.examples.isEmpty,
            backgroundColor: getBackgroundColor(businessModel.metadata.difficultyLevel),
            textColor: getTextColor(businessModel.metadata.difficultyLevel)
        )
    }

    private func clearCurrentWord() {
        currentWord = nil
        errorMessage = nil
        showError = false
    }

    private func handleSearchError(_ error: Error) {
        if let businessError = error as? WordBusinessError {
            switch businessError {
            case .invalidInput:
                errorMessage = "请输入有效的单词"
            case .fetchFailed:
                errorMessage = "网络连接失败，请检查网络设置"
            case .cacheError:
                errorMessage = "数据加载失败，请重试"
            }
        } else {
            errorMessage = "未知错误，请重试"
        }
        showError = true
    }

    // MARK: - UI辅助方法

    private func getBadgeColor(for partOfSpeech: String) -> String {
        switch partOfSpeech.lowercased() {
        case "noun": return "#4A90E2"
        case "verb": return "#5CB85C"
        case "adjective": return "#F0AD4E"
        case "adverb": return "#D9534F"
        default: return "#6C757D"
        }
    }

    private func getDifficultyColor(_ level: DifficultyLevel) -> String {
        switch level {
        case .beginner: return "#28A745"
        case .intermediate: return "#FFC107"
        case .advanced: return "#FD7E14"
        case .expert: return "#DC3545"
        }
    }

    private func getDifficultyIcon(_ level: DifficultyLevel) -> String {
        switch level {
        case .beginner: return "🟢"
        case .intermediate: return "🟡"
        case .advanced: return "🟠"
        case .expert: return "🔴"
        }
    }

    private func getBackgroundColor(_ level: DifficultyLevel) -> String {
        switch level {
        case .beginner: return "#F8F9FA"
        case .intermediate: return "#FFF3CD"
        case .advanced: return "#FFE5B4"
        case .expert: return "#F8D7DA"
        }
    }

    private func getTextColor(_ level: DifficultyLevel) -> String {
        switch level {
        case .beginner: return "#155724"
        case .intermediate: return "#856404"
        case .advanced: return "#B45309"
        case .expert: return "#721C24"
        }
    }

    private func formatAccessInfo(_ accessedAt: Date, _ accessCount: Int) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return "访问 \(accessCount) 次 · 最近访问：\(formatter.string(from: accessedAt))"
    }
}
```

### 7. View层 (SwiftUI)

```swift
// SpotlightView.swift - UI界面
import SwiftUI

struct SpotlightView: View {
    @StateObject private var viewModel: SpotlightViewModel

    init(viewModel: SpotlightViewModel) {
        self._viewModel = StateObject(wrappedValue: viewModel)
    }

    var body: some View {
        VStack(spacing: 20) {
            // 搜索栏
            SearchBar(
                text: $viewModel.searchText,
                isLoading: viewModel.isLoading,
                onClear: viewModel.clearSearch
            )

            // 内容区域
            if viewModel.isLoading {
                LoadingView()
            } else if let word = viewModel.currentWord {
                WordDetailCard(word: word)
            } else if viewModel.searchText.isEmpty {
                EmptyStateView()
            }

            Spacer()
        }
        .padding()
        .alert("搜索错误", isPresented: $viewModel.showError) {
            Button("重试") {
                viewModel.retryLastSearch()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text(viewModel.errorMessage ?? "")
        }
    }
}

// 搜索栏组件
struct SearchBar: View {
    @Binding var text: String
    let isLoading: Bool
    let onClear: () -> Void

    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索单词...", text: $text)
                .textFieldStyle(RoundedBorderTextFieldStyle())

            if !text.isEmpty {
                Button(action: onClear) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }

            if isLoading {
                ProgressView()
                    .scaleEffect(0.8)
            }
        }
    }
}

// 单词详情卡片
struct WordDetailCard: View {
    let word: WordDisplayModel

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题区域
            HStack {
                VStack(alignment: .leading) {
                    Text(word.word)
                        .font(.largeTitle)
                        .fontWeight(.bold)

                    if word.shouldShowPronunciation {
                        Text(word.pronunciationText)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // 难度标签
                DifficultyBadgeView(badge: word.difficultyBadge)
            }

            // 主要定义
            Text(word.mainDefinition)
                .font(.body)
                .padding()
                .background(Color(word.backgroundColor))
                .foregroundColor(Color(word.textColor))
                .cornerRadius(8)

            // 所有定义
            ForEach(word.allDefinitions.indices, id: \.self) { index in
                DefinitionRow(definition: word.allDefinitions[index])
            }

            // 例句
            if word.shouldShowExamples {
                VStack(alignment: .leading, spacing: 8) {
                    Text("例句")
                        .font(.headline)

                    ForEach(word.examples.indices, id: \.self) { index in
                        Text("• \(word.examples[index])")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // 访问信息
            Text(word.accessInfo)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

// 难度标签视图
struct DifficultyBadgeView: View {
    let badge: DifficultyBadge

    var body: some View {
        HStack(spacing: 4) {
            Text(badge.icon)
            Text(badge.text)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color(badge.color))
        .foregroundColor(.white)
        .cornerRadius(12)
    }
}

// 定义行视图
struct DefinitionRow: View {
    let definition: DefinitionDisplayModel

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(definition.partOfSpeech)
                    .font(.caption)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color(definition.badgeColor))
                    .foregroundColor(.white)
                    .cornerRadius(4)

                Spacer()
            }

            Text(definition.meaning)
                .font(.body)

            if definition.isExpanded {
                Text(definition.explanation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 2)
            }
        }
        .padding(.vertical, 4)
    }
}
```

### 8. 依赖注入配置

```swift
// DIContainer.swift - 依赖注入容器
import Foundation

class DIContainer {
    static let shared = DIContainer()

    private init() {}

    // MARK: - 网络层
    lazy var apiClient: APIClient = {
        APIClient(baseURL: "https://api.senseword.app")
    }()

    // MARK: - 适配器层 (AI生成)
    lazy var wordAPIAdapter: WordAPIAdapterProtocol = {
        WordAPIAdapter(apiClient: apiClient)
    }()

    // MARK: - 业务服务层 (人工设计)
    lazy var cacheBusinessService: CacheBusinessService = {
        CacheBusinessService()
    }()

    lazy var analyticsBusinessService: AnalyticsBusinessService = {
        AnalyticsBusinessService()
    }()

    lazy var wordBusinessService: WordBusinessServiceProtocol = {
        WordBusinessService(
            apiAdapter: wordAPIAdapter,
            cacheService: cacheBusinessService,
            analyticsService: analyticsBusinessService
        )
    }()

    // MARK: - ViewModel层
    func makeSpotlightViewModel() -> SpotlightViewModel {
        SpotlightViewModel(wordBusinessService: wordBusinessService)
    }
}

// App.swift - 应用入口
import SwiftUI

@main
struct SenseWordApp: App {
    var body: some Scene {
        WindowGroup {
            SpotlightView(
                viewModel: DIContainer.shared.makeSpotlightViewModel()
            )
        }
    }
}
```

## 示例代码总结

这个完整的示例展示了TADA架构的核心优势：

### 1. 清晰的职责分离
- **API模型**：纯数据结构，AI自动生成
- **转译适配器**：纯转换逻辑，AI自动生成
- **业务模型**：领域概念，人工设计
- **业务服务**：业务逻辑，人工设计
- **显示模型**：UI概念，人工设计
- **ViewModel**：UI状态，人工设计

### 2. 高度可测试性
每一层都可以独立测试，依赖关系清晰，mock简单。

### 3. 优秀的可维护性
- API变更只影响适配器层
- 业务逻辑变更只影响业务层
- UI变更只影响显示层和ViewModel

### 4. AI自动化友好
转译层完全可以由AI根据API文档自动生成，大幅提升开发效率。

这就是TADA架构在实际项目中的完整应用示例！