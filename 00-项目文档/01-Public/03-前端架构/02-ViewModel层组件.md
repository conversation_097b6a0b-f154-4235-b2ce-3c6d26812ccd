# ViewModel层组件能力文档

> **前端视图模型规范** - 面向前端开发者的完整视图模型层组件规范
> 生成时间: 2025-06-25
> 版本: v1.0 (双层服务架构版)

---

## 1. ViewModel层职责定义

### 核心职责
- **状态管理**: 管理视图的所有状态数据
- **业务协调**: 调用Business Service层处理业务逻辑
- **数据转换**: Business DTO → View DTO的数据转换
- **用户交互**: 处理来自View层的用户操作

### 设计原则
- **MVVM模式**: 严格遵循Model-View-ViewModel架构
- **响应式编程**: 使用@Published属性进行状态发布
- **单向数据流**: View → ViewModel → Business Service
- **无UI依赖**: 不包含任何UIKit/SwiftUI相关代码

---

## 2. 核心ViewModel组件目录

### 2.1 主要页面ViewModel

#### AuthViewModel.swift
- **职责**: 管理用户认证和登录状态
- **依赖服务**: AuthBusinessService, UserBusinessService
- **发布状态**: 
  - @Published var isAuthenticated: Bool
  - @Published var currentUser: UserDisplayModel?
  - @Published var isLoading: Bool
  - @Published var errorMessage: String?
- **核心方法**: 
  - loginWithApple() async
  - loginWithGoogle() async
  - logout()
  - refreshUserInfo() async
- **数据转换**: UserBusinessModel → UserDisplayModel
- **错误处理**: 登录失败、网络错误、用户取消

#### SpotlightViewModel.swift
- **职责**: 管理Spotlight视图的状态和业务逻辑
- **依赖服务**: WordBusinessService, BookmarkBusinessService
- **发布状态**:
  - @Published var currentWord: WordDisplayModel?
  - @Published var contentStream: [ContentDisplayModel]
  - @Published var isLoading: Bool
  - @Published var canNavigateNext: Bool
- **核心方法**:
  - fetchDailyWord() async
  - navigateToNextWord() async
  - toggleBookmark() async
  - submitFeedback(isPositive: Bool) async
- **数据转换**: WordBusinessModel → WordDisplayModel
- **状态协调**: 管理单词切换、内容流更新

#### SearchViewModel.swift
- **职责**: 管理搜索功能的状态和逻辑
- **依赖服务**: SearchBusinessService, WordBusinessService
- **发布状态**:
  - @Published var searchText: String
  - @Published var searchResults: [WordDisplayModel]
  - @Published var searchHistory: [String]
  - @Published var isSearching: Bool
- **核心方法**:
  - performSearch(query: String) async
  - selectSearchResult(word: WordDisplayModel)
  - clearSearchHistory()
  - addToSearchHistory(query: String)
- **数据转换**: SearchBusinessModel → SearchDisplayModel
- **搜索优化**: 防抖动、缓存、历史记录

#### BookmarkListViewModel.swift
- **职责**: 管理生词本列表的状态和操作
- **依赖服务**: BookmarkBusinessService
- **发布状态**:
  - @Published var bookmarks: [BookmarkDisplayModel]
  - @Published var isLoading: Bool
  - @Published var isEmpty: Bool
  - @Published var searchText: String
- **核心方法**:
  - loadBookmarks() async
  - removeBookmark(id: String) async
  - searchBookmarks(query: String)
  - refreshBookmarks() async
- **数据转换**: BookmarkBusinessModel → BookmarkDisplayModel
- **列表管理**: 分页、搜索、排序

#### PurchaseViewModel.swift
- **职责**: 管理购买流程和Pro功能状态
- **依赖服务**: PurchaseBusinessService, UserBusinessService
- **发布状态**:
  - @Published var isPro: Bool
  - @Published var availableProducts: [ProductDisplayModel]
  - @Published var isPurchasing: Bool
  - @Published var purchaseError: String?
- **核心方法**:
  - loadProducts() async
  - purchaseProduct(productId: String) async
  - restorePurchases() async
  - validateProStatus() async
- **数据转换**: PurchaseBusinessModel → PurchaseDisplayModel
- **支付处理**: App Store集成、收据验证

### 2.2 专用功能ViewModel

#### WordDetailViewModel.swift
- **职责**: 管理单词详情页面的状态
- **依赖服务**: WordBusinessService, AudioBusinessService
- **发布状态**:
  - @Published var word: WordDisplayModel?
  - @Published var isBookmarked: Bool
  - @Published var audioStatus: AudioStatusDisplayModel
- **核心方法**:
  - loadWordDetail(word: String) async
  - toggleBookmark() async
  - playAudio(type: AudioType) async
- **数据转换**: WordBusinessModel → WordDisplayModel
- **音频管理**: 播放状态、缓存状态

#### AudioPlayerViewModel.swift
- **职责**: 管理音频播放的状态和控制
- **依赖服务**: AudioBusinessService
- **发布状态**:
  - @Published var isPlaying: Bool
  - @Published var currentTime: TimeInterval
  - @Published var duration: TimeInterval
  - @Published var isLoading: Bool
- **核心方法**:
  - play(audioUrl: URL) async
  - pause()
  - stop()
  - seek(to: TimeInterval)
- **数据转换**: AudioBusinessModel → AudioDisplayModel
- **播放控制**: 进度管理、状态同步

#### FeedbackViewModel.swift
- **职责**: 管理用户反馈的状态和提交
- **依赖服务**: FeedbackBusinessService
- **发布状态**:
  - @Published var feedbackStatus: FeedbackStatus
  - @Published var isSubmitting: Bool
  - @Published var hasSubmitted: Bool
- **核心方法**:
  - submitPositiveFeedback(wordId: String) async
  - submitNegativeFeedback(wordId: String) async
  - resetFeedbackStatus()
- **数据转换**: FeedbackBusinessModel → FeedbackDisplayModel
- **状态管理**: 防重复提交、状态重置

### 2.3 协调器ViewModel

#### ContentStreamCoordinator.swift
- **职责**: 协调内容流的导航和状态管理
- **依赖服务**: WordBusinessService, NavigationService
- **发布状态**:
  - @Published var currentIndex: Int
  - @Published var contentItems: [ContentDisplayModel]
  - @Published var canGoNext: Bool
  - @Published var canGoPrevious: Bool
- **核心方法**:
  - navigateNext() async
  - navigatePrevious()
  - jumpToIndex(index: Int)
  - preloadNextContent() async
- **数据转换**: ContentBusinessModel → ContentDisplayModel
- **导航协调**: 预加载、状态同步

#### NavigationCoordinator.swift
- **职责**: 管理应用级别的导航状态
- **依赖服务**: NavigationService
- **发布状态**:
  - @Published var currentTab: AppTab
  - @Published var navigationPath: NavigationPath
  - @Published var presentedSheet: SheetType?
- **核心方法**:
  - navigateToTab(tab: AppTab)
  - pushView(destination: ViewDestination)
  - presentSheet(sheet: SheetType)
  - dismissSheet()
- **导航管理**: 深度链接、状态恢复

---

## 3. ViewModel层设计规范

### 3.1 基础ViewModel协议
```swift
protocol BaseViewModel: ObservableObject {
    var isLoading: Bool { get set }
    var errorMessage: String? { get set }
    
    func handleError(_ error: Error)
    func resetState()
}
```

### 3.2 状态管理规范
- 使用@Published发布所有需要UI响应的状态
- 使用@MainActor确保UI更新在主线程
- 避免在ViewModel中直接操作UI
- 使用Combine进行复杂的状态组合

### 3.3 异步操作规范
```swift
@MainActor
class SpotlightViewModel: BaseViewModel {
    @Published var currentWord: WordDisplayModel?
    @Published var isLoading = false
    
    func fetchDailyWord() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let businessModel = try await wordBusinessService.fetchDailyWord()
            currentWord = WordDisplayModelConverter.convert(from: businessModel)
        } catch {
            handleError(error)
        }
    }
}
```

### 3.4 数据转换规范
- 每个ViewModel负责Business DTO → View DTO的转换
- 使用专门的Converter类进行数据转换
- 保持转换逻辑的可测试性
- 处理转换过程中的异常情况

### 3.5 依赖注入规范
```swift
class SpotlightViewModel: BaseViewModel {
    private let wordBusinessService: WordBusinessService
    private let bookmarkBusinessService: BookmarkBusinessService
    
    init(
        wordBusinessService: WordBusinessService = WordBusinessService(),
        bookmarkBusinessService: BookmarkBusinessService = BookmarkBusinessService()
    ) {
        self.wordBusinessService = wordBusinessService
        self.bookmarkBusinessService = bookmarkBusinessService
    }
}
```

---

## 4. 开发状态跟踪

### 4.1 主要ViewModel状态
| 组件名称 | 开发状态 | 复杂度 | 优先级 | 备注 |
|---------|---------|--------|--------|------|
| AuthViewModel.swift | 📝 待开发 | 中 | P0 | 登录核心功能 |
| SpotlightViewModel.swift | 🔧 逻辑开发中 | 高 | P0 | 核心体验逻辑 |
| SearchViewModel.swift | 📝 待开发 | 中 | P1 | 搜索功能 |
| BookmarkListViewModel.swift | 📝 待开发 | 低 | P1 | 列表管理 |
| PurchaseViewModel.swift | 📝 待开发 | 高 | P2 | 支付集成 |
| WordDetailViewModel.swift | 🔧 逻辑开发中 | 中 | P0 | 详情页逻辑 |

### 4.2 专用ViewModel状态
| 组件名称 | 开发状态 | 复用度 | 优先级 | 备注 |
|---------|---------|--------|--------|------|
| AudioPlayerViewModel.swift | 📝 待开发 | 高 | P1 | 音频播放 |
| FeedbackViewModel.swift | 📝 待开发 | 中 | P2 | 用户反馈 |
| ContentStreamCoordinator.swift | 🔧 逻辑开发中 | 中 | P0 | 内容流协调 |
| NavigationCoordinator.swift | 📝 待开发 | 高 | P1 | 导航管理 |

---

## 5. 质量保证

### 5.1 代码审查清单
- [ ] 正确使用@Published和@MainActor
- [ ] 异步操作正确处理错误
- [ ] 无直接UI操作代码
- [ ] 依赖注入正确实现
- [ ] 状态管理逻辑清晰
- [ ] 数据转换逻辑正确

### 5.2 测试策略
- **单元测试**: 测试ViewModel的业务逻辑
- **状态测试**: 验证@Published属性的状态变化
- **异步测试**: 测试async/await方法的正确性
- **Mock测试**: 使用Mock Service进行隔离测试

### 5.3 性能优化
- 避免不必要的状态更新
- 使用debounce处理频繁的用户输入
- 合理使用缓存减少重复计算
- 监控内存泄漏和循环引用

---

## 6. 未来规划

### 6.1 状态管理增强
- 引入Redux-like状态管理模式
- 实现状态持久化和恢复
- 支持状态时间旅行调试

### 6.2 响应式编程
- 更深度集成Combine框架
- 实现复杂的状态组合和变换
- 优化异步操作的响应性

### 6.3 开发工具
- 提供ViewModel状态调试工具
- 实现状态变化的可视化
- 支持热重载和实时预览

---
