# Business Service层组件能力文档

> **前端业务服务规范** - 面向前端开发者的完整业务服务层组件规范
> 生成时间: 2025-06-25
> 版本: v1.0 (双层服务架构版)

---

## 1. Business Service层职责定义

### 核心职责
- **业务逻辑**: 处理复杂的业务规则和流程
- **缓存管理**: 实现智能缓存策略和数据持久化
- **错误处理**: 提供健壮的错误处理和重试机制
- **数据转换**: API DTO → Business DTO的数据转换

### 设计原则
- **业务封装**: 将复杂业务逻辑封装在Service层
- **缓存优先**: 优先使用缓存，减少网络请求
- **错误恢复**: 提供多种错误恢复策略
- **性能优化**: 通过预加载、批处理等方式优化性能

---

## 2. 核心Business Service组件目录

### 2.1 认证和用户服务

#### AuthBusinessService.swift
- **职责**: 处理用户认证相关的业务逻辑
- **依赖适配器**: AuthAPIAdapter
- **缓存策略**: Session信息本地持久化，24小时有效期
- **重试机制**: 网络失败时自动重试，最多3次，指数退避
- **错误处理**: 
  - 登录失败：提供用户友好的错误信息
  - 网络超时：自动重试并提供离线模式
  - Token过期：自动刷新或引导重新登录
- **核心方法**:
  - loginWithApple(identityToken: String) async throws -> UserBusinessModel
  - loginWithGoogle(idToken: String) async throws -> UserBusinessModel
  - logout() async
  - validateSession() async throws -> Bool
- **数据转换**: AuthAPIResponse → UserBusinessModel
- **安全处理**: Token安全存储、生物识别验证

#### UserBusinessService.swift
- **职责**: 管理用户信息和偏好设置
- **依赖适配器**: UserAPIAdapter
- **缓存策略**: 用户信息缓存1小时，偏好设置永久缓存
- **重试机制**: 用户信息更新失败时重试
- **错误处理**:
  - 用户信息获取失败：使用缓存数据
  - 偏好设置同步失败：本地优先，后台同步
- **核心方法**:
  - getCurrentUser() async throws -> UserBusinessModel
  - updateUserPreferences(preferences: UserPreferences) async throws
  - syncUserData() async throws
- **数据转换**: UserAPIResponse → UserBusinessModel
- **同步策略**: 本地优先，云端同步

### 2.2 单词和内容服务

#### WordBusinessService.swift
- **职责**: 处理单词相关的核心业务逻辑
- **依赖适配器**: WordAPIAdapter
- **缓存策略**: 
  - 单词内容：24小时TTL，LRU淘汰策略
  - 每日推荐：缓存到次日凌晨
  - 热门单词：1小时TTL
- **重试机制**: 网络失败时最多重试3次，使用指数退避算法
- **错误处理**:
  - 单词未找到：提供相似单词建议
  - 网络超时：返回缓存数据或降级内容
  - API限流：使用本地缓存，延迟重试
- **核心方法**:
  - fetchDailyWord() async throws -> WordBusinessModel
  - fetchWord(word: String) async throws -> WordBusinessModel
  - searchWords(query: String) async throws -> [WordBusinessModel]
  - preloadRelatedWords(word: String) async
- **数据转换**: WordAPIResponse → WordBusinessModel
- **智能预加载**: 基于用户行为预测并预加载内容

#### ContentStreamBusinessService.swift
- **职责**: 管理内容流的生成和导航逻辑
- **依赖适配器**: WordAPIAdapter
- **缓存策略**: 内容流缓存30分钟，支持离线浏览
- **重试机制**: 内容加载失败时从缓存恢复
- **错误处理**:
  - 内容生成失败：使用备用内容源
  - 网络中断：切换到离线模式
- **核心方法**:
  - generateContentStream(seedWord: String) async throws -> [ContentBusinessModel]
  - getNextContent(currentIndex: Int) async throws -> ContentBusinessModel?
  - preloadContentBatch(startIndex: Int) async
- **数据转换**: ContentAPIResponse → ContentBusinessModel
- **流式加载**: 支持无限滚动和智能预加载

### 2.3 用户交互服务

#### BookmarkBusinessService.swift
- **职责**: 管理用户的生词本和收藏功能
- **依赖适配器**: BookmarkAPIAdapter
- **缓存策略**: 
  - 收藏列表：实时同步，本地永久缓存
  - 收藏状态：内存缓存，快速响应
- **重试机制**: 收藏操作失败时自动重试
- **错误处理**:
  - 添加收藏失败：本地先缓存，后台同步
  - 删除收藏失败：乐观更新，失败时回滚
  - 同步冲突：以服务端数据为准
- **核心方法**:
  - addBookmark(wordId: String) async throws
  - removeBookmark(wordId: String) async throws
  - getBookmarks() async throws -> [BookmarkBusinessModel]
  - isBookmarked(wordId: String) -> Bool
  - syncBookmarks() async throws
- **数据转换**: BookmarkAPIResponse → BookmarkBusinessModel
- **冲突解决**: 处理多设备同步冲突

#### FeedbackBusinessService.swift
- **职责**: 处理用户反馈和评分逻辑
- **依赖适配器**: FeedbackAPIAdapter
- **缓存策略**: 反馈状态本地缓存，防止重复提交
- **重试机制**: 反馈提交失败时后台重试
- **错误处理**:
  - 提交失败：本地记录，后台重试
  - 重复提交：客户端防重复逻辑
- **核心方法**:
  - submitFeedback(wordId: String, isPositive: Bool) async throws
  - getFeedbackStatus(wordId: String) -> FeedbackStatus
  - batchSubmitFeedback() async throws
- **数据转换**: FeedbackAPIResponse → FeedbackBusinessModel
- **防重复**: 客户端和服务端双重防重复机制

### 2.4 媒体和支付服务

#### AudioBusinessService.swift
- **职责**: 管理音频播放和缓存逻辑
- **依赖适配器**: AudioAPIAdapter
- **缓存策略**: 
  - 音频文件：本地缓存，LRU淘汰
  - 播放状态：内存缓存
- **重试机制**: 音频加载失败时重试
- **错误处理**:
  - 音频未生成：显示生成状态，轮询检查
  - 播放失败：提供文本替代
- **核心方法**:
  - getAudioStatus(wordId: String) async throws -> AudioStatusBusinessModel
  - playAudio(audioUrl: URL) async throws
  - preloadAudio(wordId: String) async
- **数据转换**: AudioAPIResponse → AudioBusinessModel
- **播放优化**: 预加载、格式优化、断点续传

#### PurchaseBusinessService.swift
- **职责**: 处理应用内购买和Pro功能管理
- **依赖适配器**: PurchaseAPIAdapter
- **缓存策略**: Pro状态本地缓存，定期验证
- **重试机制**: 购买验证失败时重试
- **错误处理**:
  - 购买失败：提供详细错误信息和解决方案
  - 收据验证失败：本地缓存，后台验证
  - 恢复购买失败：多次尝试不同策略
- **核心方法**:
  - verifyPurchase(receiptData: Data) async throws -> PurchaseBusinessModel
  - restorePurchases() async throws -> [PurchaseBusinessModel]
  - checkProStatus() async throws -> Bool
- **数据转换**: PurchaseAPIResponse → PurchaseBusinessModel
- **收据管理**: 安全存储和验证购买收据

### 2.5 搜索和导航服务

#### SearchBusinessService.swift
- **职责**: 处理搜索功能的业务逻辑
- **依赖适配器**: SearchAPIAdapter
- **缓存策略**: 
  - 搜索结果：5分钟TTL
  - 搜索历史：本地永久存储
  - 热门搜索：1小时TTL
- **重试机制**: 搜索失败时重试
- **错误处理**:
  - 搜索无结果：提供搜索建议
  - 网络失败：使用本地索引搜索
- **核心方法**:
  - searchWords(query: String) async throws -> [SearchResultBusinessModel]
  - getSearchSuggestions(query: String) async throws -> [String]
  - addToSearchHistory(query: String)
  - getSearchHistory() -> [String]
- **数据转换**: SearchAPIResponse → SearchBusinessModel
- **搜索优化**: 防抖动、智能提示、模糊匹配

#### NavigationBusinessService.swift
- **职责**: 管理应用导航和深度链接
- **依赖适配器**: 无（纯客户端逻辑）
- **缓存策略**: 导航状态内存缓存
- **错误处理**: 无效链接的降级处理
- **核心方法**:
  - handleDeepLink(url: URL) -> NavigationAction
  - saveNavigationState() 
  - restoreNavigationState() -> NavigationState?
- **状态管理**: 导航栈状态的保存和恢复

---

## 3. Business Service层设计规范

### 3.1 基础Service协议
```swift
protocol BaseBusinessService {
    associatedtype BusinessModel
    associatedtype APIResponse
    
    func handleError(_ error: Error) -> BusinessError
    func shouldRetry(_ error: Error) -> Bool
    func getCacheKey(for request: Any) -> String
}
```

### 3.2 缓存管理规范
```swift
class WordBusinessService: BaseBusinessService {
    private let cache = CacheManager<String, WordBusinessModel>()
    private let apiAdapter: WordAPIAdapter
    
    func fetchWord(word: String) async throws -> WordBusinessModel {
        let cacheKey = getCacheKey(for: word)
        
        // 1. 检查缓存
        if let cachedWord = await cache.get(cacheKey),
           !cache.isExpired(cachedWord) {
            return cachedWord
        }
        
        // 2. 网络请求
        let apiResponse = try await apiAdapter.fetchWordRaw(word)
        let businessModel = WordBusinessModelConverter.convert(from: apiResponse)
        
        // 3. 更新缓存
        await cache.set(cacheKey, businessModel, ttl: .hours(24))
        
        return businessModel
    }
}
```

### 3.3 错误处理规范
```swift
enum BusinessError: Error {
    case networkError(underlying: Error)
    case dataCorruption
    case authenticationRequired
    case rateLimited(retryAfter: TimeInterval)
    case businessLogicError(message: String)
}

extension WordBusinessService {
    func handleWordFetchError(_ error: Error, word: String) async throws -> WordBusinessModel {
        switch error {
        case APIError.networkTimeout:
            // 重试逻辑
            return try await retryFetchWord(word, maxRetries: 3)
        case APIError.wordNotFound:
            // 提供建议
            throw BusinessError.businessLogicError(message: "单词未找到，您可以尝试搜索相似单词")
        default:
            throw BusinessError.networkError(underlying: error)
        }
    }
}
```

### 3.4 数据转换规范
- 每个Service负责API DTO → Business DTO的转换
- 使用专门的Converter类进行数据转换
- 处理转换过程中的数据验证和清洗
- 提供默认值和容错机制

---

## 4. 开发状态跟踪

### 4.1 核心Service状态
| 组件名称 | 开发状态 | 复杂度 | 优先级 | 备注 |
|---------|---------|--------|--------|------|
| AuthBusinessService.swift | 📝 待开发 | 高 | P0 | 认证核心 |
| UserBusinessService.swift | 📝 待开发 | 中 | P0 | 用户管理 |
| WordBusinessService.swift | 🎯 业务层开发中 | 高 | P0 | 核心业务 |
| ContentStreamBusinessService.swift | 🎯 业务层开发中 | 高 | P0 | 内容流 |
| BookmarkBusinessService.swift | ✅ 已完成 | 中 | P1 | 收藏功能 |

### 4.2 辅助Service状态
| 组件名称 | 开发状态 | 复杂度 | 优先级 | 备注 |
|---------|---------|--------|--------|------|
| FeedbackBusinessService.swift | 📝 待开发 | 低 | P2 | 用户反馈 |
| AudioBusinessService.swift | 📝 待开发 | 中 | P1 | 音频播放 |
| PurchaseBusinessService.swift | 📝 待开发 | 高 | P2 | 支付功能 |
| SearchBusinessService.swift | 📝 待开发 | 中 | P1 | 搜索功能 |
| NavigationBusinessService.swift | 📝 待开发 | 低 | P1 | 导航管理 |

---

## 5. 质量保证

### 5.1 代码审查清单
- [ ] 正确实现缓存策略
- [ ] 错误处理覆盖所有场景
- [ ] 重试机制合理配置
- [ ] 数据转换逻辑正确
- [ ] 性能优化措施到位
- [ ] 线程安全保证

### 5.2 测试策略
- **单元测试**: 测试业务逻辑的正确性
- **集成测试**: 测试与API Adapter的集成
- **缓存测试**: 验证缓存策略的有效性
- **错误测试**: 模拟各种错误场景
- **性能测试**: 监控响应时间和内存使用

### 5.3 监控和日志
- 记录关键业务操作的日志
- 监控缓存命中率和性能指标
- 追踪错误发生频率和类型
- 分析用户行为模式

---

## 6. 未来规划

### 6.1 智能化增强
- 基于用户行为的智能预加载
- 自适应缓存策略
- 智能错误恢复机制

### 6.2 性能优化
- 实现更高效的缓存算法
- 优化网络请求的批处理
- 减少内存占用和CPU消耗

### 6.3 可观测性
- 增强日志和监控能力
- 实现分布式追踪
- 提供性能分析工具

---
