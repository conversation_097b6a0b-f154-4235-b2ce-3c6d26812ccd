# View层组件能力文档

> **前端UI组件规范** - 面向前端开发者的完整视图层组件规范
> 生成时间: 2025-06-25
> 版本: v1.0 (双层服务架构版)

---

## 1. View层职责定义

### 核心职责
- **UI展示**: 负责用户界面的渲染和展示
- **用户交互**: 处理用户的点击、滑动、输入等操作
- **状态绑定**: 与ViewModel层进行数据绑定
- **动画效果**: 实现视觉过渡和交互反馈

### 设计原则
- **单一职责**: 只负责UI展示，不包含业务逻辑
- **数据驱动**: 通过@StateObject/@ObservedObject绑定ViewModel
- **组件化**: 可复用的UI组件设计
- **响应式**: 支持不同屏幕尺寸和方向

---

## 2. 核心视图组件目录

### 2.1 主要页面视图

#### OnboardingView.swift
- **职责**: 用户首次使用的引导页面
- **输入数据**: AuthDisplayModel
- **用户交互**: Apple登录、Google登录、隐私政策查看
- **状态管理**: @StateObject authViewModel
- **子组件**: LoginButtonView, PrivacyLinkView
- **动画效果**: 品牌logo呼吸动画、卡片流动效果
- **导航**: 登录成功后跳转到SpotlightView

#### SpotlightView.swift
- **职责**: 每日推荐单词的主视图，应用核心体验
- **输入数据**: WordDisplayModel, ContentStreamDisplayModel
- **用户交互**: 向上滑动探索、收藏、点赞/踩、发音播放
- **状态管理**: @StateObject spotlightViewModel
- **子组件**: WordAnchorView, ContentStageView, WordActionMenuView
- **动画效果**: 卡片磁性吸附、垂直滚动过渡、聚光灯效果
- **导航**: 可跳转到SearchView、BookmarkListView

#### SearchView.swift
- **职责**: 主动搜索单词的界面
- **输入数据**: SearchDisplayModel, WordDisplayModel
- **用户交互**: 搜索输入、搜索结果选择、历史记录
- **状态管理**: @StateObject searchViewModel
- **子组件**: SearchBarView, SearchResultListView, SearchHistoryView
- **动画效果**: 搜索框展开、结果列表淡入
- **导航**: 搜索结果跳转到WordDetailView

#### BookmarkListView.swift
- **职责**: 生词本收藏列表管理
- **输入数据**: BookmarkListDisplayModel
- **用户交互**: 查看收藏、删除收藏、搜索收藏
- **状态管理**: @StateObject bookmarkListViewModel
- **子组件**: BookmarkItemView, EmptyStateView
- **动画效果**: 列表项滑动删除、空状态动画
- **导航**: 点击收藏项跳转到WordDetailView

#### PurchaseView.swift
- **职责**: 付费升级和购买管理
- **输入数据**: PurchaseDisplayModel, UserDisplayModel
- **用户交互**: 购买Pro版本、恢复购买、查看功能对比
- **状态管理**: @StateObject purchaseViewModel
- **子组件**: FeatureComparisonView, PurchaseButtonView
- **动画效果**: 功能对比展示、购买成功反馈
- **导航**: 购买成功后返回主界面

### 2.2 详情和辅助视图

#### WordDetailView.swift
- **职责**: 单词详细信息展示页面
- **输入数据**: WordDisplayModel
- **用户交互**: 发音播放、收藏、分享、返回
- **状态管理**: @StateObject wordDetailViewModel
- **子组件**: WordHeaderView, DefinitionListView, ExampleListView
- **动画效果**: 页面进入过渡、内容展开动画
- **导航**: 返回到调用页面

### 2.3 可复用组件

#### WordAnchorView.swift
- **职责**: 单词锚点区域，固定在顶部显示当前单词
- **输入数据**: WordDisplayModel
- **用户交互**: 发音播放、收藏状态切换
- **状态管理**: 通过父视图传递
- **动画效果**: 音频状态指示器、收藏状态切换
- **复用性**: 可在SpotlightView、WordDetailView中使用

#### ContentStageView.swift
- **职责**: 内容舞台区域，展示单词的多维度解析
- **输入数据**: ContentDisplayModel[]
- **用户交互**: 垂直滚动、卡片点击
- **状态管理**: 通过父视图传递
- **动画效果**: 磁性吸附、聚光灯效果、卡片过渡
- **复用性**: 主要用于SpotlightView

#### WordActionMenuView.swift
- **职责**: 单词操作菜单，提供收藏、反馈等功能
- **输入数据**: WordDisplayModel, UserActionDisplayModel
- **用户交互**: 收藏、点赞、踩、分享
- **状态管理**: 通过父视图传递
- **动画效果**: 按钮点击反馈、状态切换动画
- **复用性**: 可在多个单词展示页面使用

#### AudioPlayerView.swift
- **职责**: 音频播放控件，支持单词和例句发音
- **输入数据**: AudioDisplayModel
- **用户交互**: 播放/暂停、进度控制
- **状态管理**: @StateObject audioPlayerViewModel
- **动画效果**: 播放状态指示器、音波动画
- **复用性**: 可在任何需要音频播放的地方使用

---

## 3. 视图层设计规范

### 3.1 数据绑定规范
```swift
// 标准的ViewModel绑定模式
struct SpotlightView: View {
    @StateObject private var viewModel = SpotlightViewModel()
    
    var body: some View {
        // UI实现
    }
}
```

### 3.2 状态管理规范
- 使用@StateObject管理主要ViewModel
- 使用@ObservedObject接收传递的ViewModel
- 使用@State管理纯UI状态（如动画、临时状态）
- 避免在View中直接调用Business Service

### 3.3 组件复用规范
- 提取可复用的UI组件到独立文件
- 使用ViewModifier封装通用样式
- 通过协议定义组件接口
- 支持主题和样式定制

### 3.4 动画设计规范
- 使用SwiftUI原生动画API
- 保持动画时长一致性（0.3s标准，0.6s慢速）
- 提供动画开关选项
- 考虑无障碍访问需求

### 3.5 导航管理规范
- 使用NavigationStack进行页面导航
- 通过ViewModel管理导航状态
- 支持深度链接和状态恢复
- 处理导航栈的内存管理

---

## 4. 开发状态跟踪

### 4.1 主要页面视图状态
| 组件名称 | 开发状态 | 负责人 | 预计完成时间 | 备注 |
|---------|---------|--------|-------------|------|
| OnboardingView.swift | 📝 待开发 | - | - | 需要UI设计稿 |
| SpotlightView.swift | 🎨 UI开发中 | - | - | 核心体验页面 |
| SearchView.swift | 📝 待开发 | - | - | 依赖搜索功能 |
| BookmarkListView.swift | 📝 待开发 | - | - | 依赖生词本功能 |
| PurchaseView.swift | 📝 待开发 | - | - | 依赖支付功能 |
| WordDetailView.swift | 🎨 UI开发中 | - | - | 详情页面 |

### 4.2 可复用组件状态
| 组件名称 | 开发状态 | 复用度 | 优先级 | 备注 |
|---------|---------|--------|--------|------|
| WordAnchorView.swift | 🎨 UI开发中 | 高 | P0 | 核心组件 |
| ContentStageView.swift | 🎨 UI开发中 | 中 | P0 | 核心体验 |
| WordActionMenuView.swift | 🔗 待联调 | 高 | P1 | 交互组件 |
| AudioPlayerView.swift | 📝 待开发 | 高 | P1 | 音频功能 |

---

## 5. 质量保证

### 5.1 代码审查清单
- [ ] 遵循SwiftUI最佳实践
- [ ] 正确使用@StateObject/@ObservedObject
- [ ] 无直接调用Business Service
- [ ] 支持Dark Mode
- [ ] 支持无障碍访问
- [ ] 性能优化（避免不必要的重绘）

### 5.2 测试策略
- **UI测试**: 使用XCUITest进行关键用户流程测试
- **快照测试**: 确保UI在不同设备上的一致性
- **性能测试**: 监控视图渲染性能
- **无障碍测试**: 确保VoiceOver等功能正常

### 5.3 设计一致性
- 遵循SenseWord设计系统
- 使用统一的颜色、字体、间距
- 保持动画和交互的一致性
- 支持多语言本地化

---

## 6. 未来规划

### 6.1 组件库建设
- 建立SenseWord UI组件库
- 提供Storybook式的组件预览
- 支持主题定制和品牌化

### 6.2 性能优化
- 实现视图懒加载
- 优化大列表渲染性能
- 减少不必要的状态更新

### 6.3 用户体验增强
- 添加更丰富的动画效果
- 支持手势操作
- 提供个性化设置选项

---
