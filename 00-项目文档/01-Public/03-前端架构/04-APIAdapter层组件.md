# 前端服务层架构说明书 (Frontend Service Layer Architecture Instructions)

> **前端Adapter能力接口规范** - 面向业务服务层开发者的完整接口规范
> 生成时间: 2025-06-26
> 版本: v2.0 (双层服务架构版)

---

## 1. Adapter能力接口总览表 (Adapter Capability Interface Overview Table)

### 核心业务模块 (Core Business Modules)

| 功能模块 | Adapter类名 | 核心职责 | 主要方法 | 数据转换 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 身份认证 | `AuthAPIAdapter` | Apple登录API转译 | login, logout, logoutAll, healthCheck | LoginRequest → UserSession | ✅ 已实现 |
| 用户管理 | `UserAPIAdapter` | 用户资料管理API转译 | getCurrentUser, deleteAccount | SessionID → UserProfile | ✅ 已实现 |
| 单词服务 | `WordAPIAdapter` | 单词查询(含AI生成)API转译 | getWord(含AI生成), getDailyWord, submitFeedback | String → WordDefinition | ✅ 已实现 |

### 辅助功能模块 (Auxiliary Feature Modules)

| 功能模块 | Adapter类名 | 核心职责 | 主要方法 | 数据转换 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 生词本管理 | `BookmarkAPIAdapter` | 生词本CRUD操作API转译 | addBookmark, removeBookmark, getBookmarks, healthCheck | BookmarkRequest → BookmarkList | ✅ 已实现 |
| 购买验证 | `PurchaseAPIAdapter` | IAP购买验证API转译 | verifyPurchase, restorePurchase | ReceiptData → PurchaseStatus | ✅ 已实现 |
| 搜索索引 | `SearchAPIAdapter` | 搜索索引同步API转译 | getWordIndexUpdates | LanguageCode → WordIndex | ✅ 已实现 |

---

## 身份认证模块 (Authentication Module)

### 1. 模块概述 (Module Overview)
身份认证模块负责处理Apple ID登录、用户登出、全设备登出和服务健康检查。在双层服务架构中，该模块作为纯转译层，将前端认证凭证转换为后端API调用，不包含任何业务逻辑。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `AuthAPIAdapterProtocol`
- **实现类**: `AuthAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 用户登录认证
- **职责**: 处理Apple ID登录请求，将前端认证凭证转译为后端API调用
- **方法签名**: `func login(idToken: String, provider: AuthProvider) async throws -> SessionLoginSuccessResponse`
- **输入参数**:
  - `idToken`: Apple ID Token (JWT格式)
  - `provider`: 认证提供方（必须显式传入，通常为.apple）
- **返回值**: `SessionLoginSuccessResponse` - 包含会话信息和用户数据
- **使用示例**:
```swift
let adapter = AdapterContainer.shared.authAPIAdapter
let response = try await adapter.login(idToken: appleIDToken, provider: .apple)
let sessionId = response.session.sessionId
```
- **错误处理**: 可能抛出网络错误、认证失败等异常

#### 3.2 用户登出
- **职责**: 处理用户登出请求，撤销当前Session实现安全登出
- **方法签名**: `func logout(sessionId: String, reason: LogoutReason?) async throws -> LogoutSuccessResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `reason`: 登出原因（可选，传nil表示无特定原因）
- **返回值**: `LogoutSuccessResponse` - 登出操作结果
- **使用示例**:
```swift
let response = try await adapter.logout(sessionId: currentSessionId, reason: .userInitiated)
// 或者无特定原因
let response = try await adapter.logout(sessionId: currentSessionId, reason: nil)
```

#### 3.3 全设备登出
- **职责**: 撤销用户所有设备的Session，实现全设备安全登出
- **方法签名**: `func logoutAll(sessionId: String, reason: LogoutAllReason?) async throws -> LogoutAllSuccessResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `reason`: 全设备登出原因（可选，传nil表示无特定原因）
- **返回值**: `LogoutAllSuccessResponse` - 包含撤销的Session数量
- **使用示例**:
```swift
let response = try await adapter.logoutAll(sessionId: currentSessionId, reason: .securityConcern)
// 或者无特定原因
let response = try await adapter.logoutAll(sessionId: currentSessionId, reason: nil)
```

#### 3.4 健康检查
- **职责**: 检查认证服务状态，提供服务版本和环境信息
- **方法签名**: `func healthCheck() async throws -> HealthCheckResponse`
- **输入参数**: 无
- **返回值**: `HealthCheckResponse` - 服务状态信息
- **使用示例**:
```swift
let healthStatus = try await adapter.healthCheck()
```

### 4. 核心数据契约 (DTO 定义) (Core Data Contracts)

#### API DTO - 认证相关
```swift
// 认证提供方
enum AuthProvider: String, Codable {
    case apple = "apple"
}

// 登录请求体
struct LoginRequestBody: Codable {
    let idToken: String
    let provider: AuthProvider
}

// Session登录成功响应
struct SessionLoginSuccessResponse: Codable {
    let success: Bool
    let session: SessionInfo
}

// Session信息
struct SessionInfo: Codable {
    let sessionId: String
    let user: UserInfo
}

// 用户信息
struct UserInfo: Codable {
    let id: String
    let email: String
    let displayName: String
    let isPro: Bool
}
```

#### 登出相关数据结构
```swift
// 登出原因
enum LogoutReason: String, Codable {
    case userInitiated = "user_initiated"
    case security = "security"
    case admin = "admin"
}

// 登出请求体
struct LogoutRequest: Codable {
    let reason: LogoutReason?              // 可选的登出原因
}

// 登出成功响应
struct LogoutSuccessResponse: Codable {
    let success: Bool                      // 固定为true
    let message: String                    // 用户友好的提示信息
    let sessionRevoked: Bool               // Session是否成功撤销
    let timestamp: String                  // 操作时间戳 (ISO 8601)

    // 确保success字段在成功响应中始终为true
    init(message: String, sessionRevoked: Bool, timestamp: String) {
        self.success = true
        self.message = message
        self.sessionRevoked = sessionRevoked
        self.timestamp = timestamp
    }
}

// 全设备登出原因
enum LogoutAllReason: String, Codable {
    case securityConcern = "security_concern"
    case deviceLost = "device_lost"
    case userRequest = "user_request"
}

// 全设备登出请求体
struct LogoutAllRequest: Codable {
    let reason: LogoutAllReason?           // 可选的登出原因
}

// 全设备登出成功响应
struct LogoutAllSuccessResponse: Codable {
    let success: Bool                      // 固定为true
    let message: String                    // 用户友好的提示信息
    let sessionsRevoked: Int               // 撤销的Session数量
    let timestamp: String                  // 操作时间戳 (ISO 8601)

    // 确保success字段在成功响应中始终为true
    init(message: String, sessionsRevoked: Int, timestamp: String) {
        self.success = true
        self.message = message
        self.sessionsRevoked = sessionsRevoked
        self.timestamp = timestamp
    }
}

// 健康检查响应
struct HealthCheckResponse: Codable {
    let service: String                    // 服务名称
    let status: String                     // 服务状态
    let timestamp: String                  // 检查时间戳
    let version: String                    // 服务版本
    let environment: String                // 运行环境
}
```

### 5. 测试与调试 (Testing & Debugging)

#### Mock数据示例
```swift
// 成功登录Mock数据
let mockLoginResponse = SessionLoginSuccessResponse(
    session: SessionInfo(
        sessionId: "sess_1234567890abcdef1234",
        user: UserInfo(
            id: "user_123",
            email: "<EMAIL>",
            displayName: "Test User",
            isPro: false
        )
    )
)
```

#### 常见错误场景
| 错误场景 | 错误代码 | 处理方式 | 重试策略 |
| :--- | :--- | :--- | :--- |
| 无效Apple ID Token | INVALID_TOKEN | 提示用户重新登录 | 不重试 |
| Session已过期 | SESSION_EXPIRED | 自动跳转登录页 | 不重试 |
| 网络连接失败 | NETWORK_ERROR | 显示网络错误提示 | 指数退避重试 |
| 服务器内部错误 | INTERNAL_ERROR | 显示通用错误提示 | 线性重试3次 |

#### 性能考量
- **调用频率限制**: 登录操作建议限制为每分钟最多5次尝试
- **缓存策略**: Session信息应缓存在安全存储中，避免重复登录
- **超时设置**: 网络请求超时时间建议设置为30秒

### 6. 架构集成指南 (Architecture Integration Guide)

#### 与业务服务层的集成
```swift
// 在Business Service中使用AuthAPIAdapter
class AuthBusinessService {
    private let authAdapter: AuthAPIAdapterProtocol

    init(authAdapter: AuthAPIAdapterProtocol) {
        self.authAdapter = authAdapter
    }

    func performLogin(appleIDToken: String) async throws -> UserSession {
        let response = try await authAdapter.login(idToken: appleIDToken)
        // 业务逻辑处理...
        return convertToUserSession(response)
    }
}
```

#### 错误传播
- Adapter层的网络错误直接传播到Business Service层
- Business Service层负责将技术错误转换为用户友好的业务错误
- ViewModel层接收业务错误并更新UI状态

#### 日志记录
- Adapter层记录所有HTTP请求和响应的详细信息
- 敏感信息（如Token）在日志中进行脱敏处理
- 错误日志包含完整的调用栈和上下文信息

## 用户管理模块 (User Management Module)

### 1. 模块概述 (Module Overview)
用户管理模块负责获取用户资料信息和处理账户删除请求。该模块依赖有效的Session进行身份验证，提供用户相关的核心数据访问功能。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `UserAPIAdapterProtocol`
- **实现类**: `UserAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 获取当前用户资料
- **职责**: 获取当前认证用户的完整资料信息
- **方法签名**: `func getCurrentUser(sessionId: String) async throws -> UserProfileResponse`
- **输入参数**: `sessionId` - 用户会话ID（双重认证）
- **返回值**: `UserProfileResponse` - 包含用户详细信息
- **使用示例**:
```swift
let userProfile = try await adapter.getCurrentUser(sessionId: currentSessionId)
let userInfo = userProfile.user
```
- **错误处理**: 可能抛出Session过期、权限不足等异常

#### 3.2 删除用户账户
- **职责**: 处理用户账户删除请求，执行完整的数据清理
- **方法签名**: `func deleteAccount(sessionId: String, confirmation: String) async throws -> AccountDeletionSuccessResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `confirmation`: 确认字符串（通常为"DELETE"）
- **返回值**: `AccountDeletionSuccessResponse` - 删除操作结果
- **使用示例**:
```swift
let result = try await adapter.deleteAccount(sessionId: sessionId, confirmation: "DELETE")
```
- **错误处理**: 确认字符串错误、权限不足等异常

### 4. 核心数据契约 (DTO 定义)

#### API DTO - 用户管理相关
```swift
// 用户资料响应
struct UserProfileResponse: Codable {
    let success: Bool
    let user: UserDetailInfo
}

// 用户详细信息
struct UserDetailInfo: Codable {
    let id: String
    let email: String
    let displayName: String
    let isPro: Bool
    let createdAt: String
}

// 账户删除请求
struct AccountDeletionRequest: Codable {
    let confirmation: String
}

// 账户删除成功响应
struct AccountDeletionSuccessResponse: Codable {
    let success: Bool
    let message: String
    let dataCleared: DataClearedInfo
    let timestamp: String
}

// 数据清理信息
struct DataClearedInfo: Codable {
    let sessions: Int                      // 清理的Session数量
    let bookmarks: Int                     // 清理的生词本数量
    let userRecord: Bool                   // 用户记录是否清理
}
```

### 5. 测试与调试 (Testing & Debugging)

#### Mock数据示例
```swift
// 用户资料Mock数据
let mockUserProfile = UserProfileResponse(
    success: true,
    user: UserDetailInfo(
        id: "user_123",
        email: "<EMAIL>",
        displayName: "Test User",
        isPro: true,
        createdAt: "2024-01-01T00:00:00Z"
    )
)
```

#### 常见错误场景
| 错误场景 | 错误代码 | 处理方式 | 重试策略 |
| :--- | :--- | :--- | :--- |
| Session无效 | UNAUTHORIZED | 重新登录 | 不重试 |
| 确认字符串错误 | INVALID_CONFIRMATION | 提示用户重新输入 | 不重试 |
| 账户不存在 | USER_NOT_FOUND | 显示错误信息 | 不重试 |

### 6. 架构集成指南 (Architecture Integration Guide)

#### 与业务服务层的集成
```swift
class UserBusinessService {
    private let userAdapter: UserAPIAdapterProtocol

    func getUserProfile() async throws -> BusinessUserProfile {
        let response = try await userAdapter.getCurrentUser(sessionId: currentSession)
        return convertToBusinessModel(response.user)
    }
}
```

---

## 单词服务模块 (Word Service Module)

### 1. 模块概述 (Module Overview)
单词服务模块是SenseWord应用的核心功能模块，负责单词查询（包含AI生成）、每日一词获取和用户反馈提交。该模块采用LPLC（Lazy Production, Lazy Caching）策略，在查询不存在的单词时自动触发AI生成，实现了查询和生成的统一接口。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `WordAPIAdapterProtocol`
- **实现类**: `WordAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 单词定义查询（包含AI生成能力）
- **职责**: 处理单词查询请求，支持多语言单词定义获取。采用LPLC（Lazy Production, Lazy Caching）策略：
  - 如果数据库中已存在该单词定义，直接返回缓存结果
  - 如果不存在，自动触发AI生成新的单词内容并保存到数据库
- **方法签名**: `func getWord(_ word: String, language: LanguageCode?) async throws -> WordDefinitionResponse`
- **输入参数**:
  - `word`: 要查询的单词
  - `language`: 目标语言（可选，传nil使用服务器默认语言）
- **返回值**: `WordDefinitionResponse` - 完整的单词定义信息
- **后端处理流程**:
  1. 查询数据库是否已存在该单词定义
  2. 如果存在，直接返回缓存结果
  3. 如果不存在，调用Gemini AI生成完整单词内容
  4. 将AI生成的内容保存到数据库
  5. 异步触发TTS音频生成
  6. 返回完整的单词定义响应
- **使用示例**:
```swift
// 查询已存在的单词（从缓存返回）
let wordDef = try await adapter.getWord("hello", language: .chinese)
let definition = wordDef.content.coreDefinition

// 查询新单词（触发AI生成）
let newWordDef = try await adapter.getWord("serendipity", language: .chinese)
let newDefinition = newWordDef.content.coreDefinition

// 使用服务器默认语言
let wordDefDefault = try await adapter.getWord("hello", language: nil)
```
- **性能特点**:
  - 首次查询新单词：较慢（需要AI生成，约2-5秒）
  - 后续查询相同单词：极快（从数据库缓存返回，约100-300ms）

#### 3.2 每日一词获取
- **职责**: 获取当日推荐单词信息
- **方法签名**: `func getDailyWord() async throws -> DailyWordResponse`
- **输入参数**: 无
- **返回值**: `DailyWordResponse` - 每日推荐单词
- **使用示例**:
```swift
let dailyWord = try await adapter.getDailyWord()
let todayWord = dailyWord.word
```

#### 3.3 用户反馈提交
- **职责**: 处理用户对单词内容的反馈（喜欢/不喜欢）
- **方法签名**: `func submitFeedback(word: String, language: LanguageCode, action: FeedbackAction) async throws -> FeedbackSuccessResponse`
- **输入参数**:
  - `word`: 反馈的单词
  - `language`: 单词语言
  - `action`: 反馈动作（like/dislike）
- **返回值**: `FeedbackSuccessResponse` - 反馈结果
- **使用示例**:
```swift
let feedback = try await adapter.submitFeedback(word: "hello", language: .chinese, action: .like)
```



### 4. 核心数据契约 (DTO 定义)

#### API DTO - 单词服务相关
```swift
// 反馈请求体
struct FeedbackRequest: Codable {
    let word: String
    let language: LanguageCode
    let action: FeedbackAction
}

// 反馈动作枚举
enum FeedbackAction: String, Codable {
    case like = "like"
    case dislike = "dislike"
}

// 单词定义响应
struct WordDefinitionResponse: Codable {
    let word: String
    let metadata: WordMetadata
    let content: WordContent
}

// 单词元数据
struct WordMetadata: Codable {
    let wordFrequency: String
    let relatedConcepts: [String]
}

// 单词内容
struct WordContent: Codable {
    let difficulty: String
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
    let contextualExplanation: ContextualExplanation
    let usageExamples: [UsageExampleCategory]
    let usageScenarios: [UsageScenario]
    let collocations: [Collocation]
    let usageNotes: [UsageNote]
    let synonyms: [Synonym]
}

// 音标符号
struct PhoneticSymbol: Codable {
    let type: String
    let symbol: String
}

// 语境解释
struct ContextualExplanation: Codable {
    let nativeSpeakerIntent: String
    let emotionalResonance: String
    let vividImagery: String
    let etymologicalEssence: String
}

// 使用示例分类
struct UsageExampleCategory: Codable {
    let category: String
    let examples: [String]
}

// 使用场景
struct UsageScenario: Codable {
    let scenario: String
    let description: String
}

// 搭配词组
struct Collocation: Codable {
    let type: String
    let words: [String]
}

// 使用注释
struct UsageNote: Codable {
    let type: String
    let note: String
}

// 同义词
struct Synonym: Codable {
    let word: String
    let nuance: String
}

// 每日一词响应
struct DailyWordResponse: Codable {
    let word: String
    let date: String
}

// 反馈成功响应
struct FeedbackSuccessResponse: Codable {
    let success: Bool
    let newScore: Int
    let message: String

    // 确保success字段在成功响应中始终为true
    init(newScore: Int, message: String) {
        self.success = true
        self.newScore = newScore
        self.message = message
    }
}
```

#### 共享模型 - 语言代码
```swift
// 引用SharedModels包中的LanguageCode
enum LanguageCode: String, Codable, CaseIterable {
    case english = "en"
    case chinese = "zh"
    case japanese = "ja"
    case german = "de"
    case french = "fr"
    case spanish = "es"
    case italian = "it"
    case portuguese = "pt"
    case polish = "pl"
    case swedish = "sv"
    case danish = "da"
    case norwegian = "no"
    case finnish = "fi"
    case korean = "ko"
    case russian = "ru"
    case arabic = "ar"
    case hindi = "hi"
    case thai = "th"
    case vietnamese = "vi"
    case turkish = "tr"
    case dutch = "nl"
    case indonesian = "id"

    /// 提供用户友好的显示名称
    var displayName: String {
        switch self {
        case .english: return "English"
        case .chinese: return "中文"
        case .japanese: return "日本語"
        case .german: return "Deutsch"
        case .french: return "Français"
        case .spanish: return "Español"
        case .italian: return "Italiano"
        case .portuguese: return "Português"
        case .polish: return "Polski"
        case .swedish: return "Svenska"
        case .danish: return "Dansk"
        case .norwegian: return "Norsk"
        case .finnish: return "Suomi"
        case .korean: return "한국어"
        case .russian: return "Русский"
        case .arabic: return "العربية"
        case .hindi: return "हिन्दी"
        case .thai: return "ไทย"
        case .vietnamese: return "Tiếng Việt"
        case .turkish: return "Türkçe"
        case .dutch: return "Nederlands"
        case .indonesian: return "Bahasa Indonesia"
        }
    }
}
```

### 5. 测试与调试 (Testing & Debugging)

#### Mock数据示例
```swift
// 单词定义Mock数据
let mockWordDefinition = WordDefinitionResponse(
    word: "hello",
    metadata: WordMetadata(
        wordFrequency: "high",
        relatedConcepts: ["greeting", "communication"]
    ),
    content: WordContent(
        difficulty: "beginner",
        phoneticSymbols: [PhoneticSymbol(type: "IPA", symbol: "/həˈloʊ/")],
        coreDefinition: "A greeting used when meeting someone",
        // ... 其他字段
    )
)
```

#### 常见错误场景
| 错误场景 | 错误代码 | 处理方式 | 重试策略 |
| :--- | :--- | :--- | :--- |
| 单词不存在 | WORD_NOT_FOUND | 提示用户检查拼写 | 不重试 |
| 语言不支持 | UNSUPPORTED_LANGUAGE | 提示选择其他语言 | 不重试 |

### 6. 架构集成指南 (Architecture Integration Guide)

#### 与业务服务层的集成
```swift
class WordBusinessService {
    private let wordAdapter: WordAPIAdapterProtocol

    func searchWord(_ word: String) async throws -> BusinessWordModel {
        let response = try await wordAdapter.getWord(word, language: .chinese)
        return convertToBusinessModel(response)
    }
}
```

---

## 生词本管理模块 (Bookmark Management Module)

### 1. 模块概述 (Module Overview)
生词本管理模块负责用户生词本的CRUD操作，包括添加生词、移除生词、获取生词列表和服务健康检查。该模块采用乐观处理策略，重复操作返回成功而非错误。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `BookmarkAPIAdapterProtocol`
- **实现类**: `BookmarkAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 添加生词到生词本
- **职责**: 处理生词添加请求，支持多语言生词管理
- **方法签名**: `func addBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `word`: 要添加的单词
  - `language`: 单词语言
- **返回值**: `BookmarkCRUDResponse` - 操作结果
- **使用示例**:
```swift
let result = try await adapter.addBookmark(sessionId: sessionId, word: "hello", language: .chinese)
```

#### 3.2 从生词本移除生词
- **职责**: 处理生词移除请求，安全删除用户生词记录
- **方法签名**: `func removeBookmark(sessionId: String, word: String, language: LanguageCode) async throws -> BookmarkCRUDResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `word`: 要移除的单词
  - `language`: 单词语言
- **返回值**: `BookmarkCRUDResponse` - 操作结果
- **使用示例**:
```swift
let result = try await adapter.removeBookmark(sessionId: sessionId, word: "hello", language: .chinese)
```

#### 3.3 获取用户生词本
- **职责**: 获取当前用户的完整生词本列表，包含时间戳信息
- **方法签名**: `func getBookmarks(sessionId: String) async throws -> GetBookmarksResponse`
- **输入参数**: `sessionId` - 用户会话ID
- **返回值**: `GetBookmarksResponse` - 完整的生词本信息列表
- **使用示例**:
```swift
let response = try await adapter.getBookmarks(sessionId: sessionId)
let bookmarks = response.bookmarks
// 获取单词列表: let wordList = bookmarks.map { $0.word }
// 按时间排序: let sortedByTime = bookmarks.sorted { $0.createdAt > $1.createdAt }
```

#### 3.4 生词本服务健康检查
- **职责**: 检查生词本服务状态，提供服务版本和环境信息
- **方法签名**: `func healthCheck() async throws -> BookmarkHealthResponse`
- **输入参数**: 无
- **返回值**: `BookmarkHealthResponse` - 服务状态信息
- **使用示例**:
```swift
let health = try await adapter.healthCheck()
```

### 4. 核心数据契约 (DTO 定义)

#### API DTO - 生词本管理相关
```swift
// 添加生词请求
struct AddBookmarkRequest: Codable {
    let word: String
    let language: LanguageCode
}

// 移除生词请求
struct RemoveBookmarkRequest: Codable {
    let word: String
    let language: LanguageCode
}

// 生词本CRUD操作响应
struct BookmarkCRUDResponse: Codable {
    let success: Bool
    let message: String

    // 确保success字段在成功响应中始终为true
    init(message: String, isSuccess: Bool = true) {
        self.success = isSuccess
        self.message = message
    }
}

// 生词本条目详细信息
struct BookmarkItem: Codable {
    let word: String
    let language: LanguageCode
    let createdAt: String
}

// 获取生词本响应 (扩展版：包含完整生词信息)
struct GetBookmarksResponse: Codable {
    let success: Bool
    let bookmarks: [BookmarkItem]

    // 确保success字段正确设置
    init(bookmarks: [BookmarkItem], isSuccess: Bool = true) {
        self.success = isSuccess
        self.bookmarks = bookmarks
    }
}

// 生词本健康检查响应
struct BookmarkHealthResponse: Codable {
    let service: String
    let status: String
    let timestamp: String
    let version: String
}
```



## 购买验证模块 (Purchase Verification Module)

### 1. 模块概述 (Module Overview)
购买验证模块负责处理App Store内购(IAP)的验证和恢复功能，确认用户的Pro状态。该模块与Apple的收据验证系统集成，提供安全的购买验证服务。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `PurchaseAPIAdapterProtocol`
- **实现类**: `PurchaseAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 验证App Store购买
- **职责**: 处理IAP购买验证，确认用户Pro状态
- **方法签名**: `func verifyPurchase(sessionId: String, receiptData: String, productId: ProductId, transactionId: String) async throws -> VerifyPurchaseResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `receiptData`: App Store收据数据
  - `productId`: 产品ID
  - `transactionId`: 交易ID
- **返回值**: `VerifyPurchaseResponse` - 验证结果
- **使用示例**:
```swift
let result = try await adapter.verifyPurchase(
    sessionId: sessionId,
    receiptData: receiptData,
    productId: .monthlyPremium,
    transactionId: transactionId
)
```

#### 3.2 恢复App Store购买
- **职责**: 处理购买恢复请求，重新激活用户Pro状态
- **方法签名**: `func restorePurchase(sessionId: String, receiptData: String) async throws -> RestorePurchaseResponse`
- **输入参数**:
  - `sessionId`: 用户会话ID
  - `receiptData`: App Store收据数据
- **返回值**: `RestorePurchaseResponse` - 恢复结果
- **使用示例**:
```swift
let result = try await adapter.restorePurchase(sessionId: sessionId, receiptData: receiptData)
```

### 4. 核心数据契约 (DTO 定义)

#### API DTO - 购买验证相关
```swift
// 产品ID枚举
enum ProductId: String, Codable {
    case monthlyPremium = "com.senseword.premium.monthly"
    case yearlyPremium = "com.senseword.premium.yearly"
}

// 购买验证请求
struct VerifyPurchaseRequest: Codable {
    let receiptData: String
    let productId: ProductId
    let transactionId: String
}

// 购买验证响应
struct VerifyPurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}

// 恢复购买请求
struct RestorePurchaseRequest: Codable {
    let receiptData: String
}

// 恢复购买响应
struct RestorePurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}
```

---

## 搜索索引模块 (Search Index Module)

### 1. 模块概述 (Module Overview)
搜索索引模块负责同步单词索引数据，支持离线搜索功能。该模块提供增量同步机制，确保本地搜索索引与服务器保持一致。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `SearchAPIAdapterProtocol`
- **实现类**: `SearchAPIAdapter`
- **注入方式**: 构造函数注入
- **生命周期**: 单例 (通过AdapterContainer管理)

### 3. Adapter接口详解 (Detailed Adapter Interfaces)

#### 3.1 获取单词索引更新
- **职责**: 获取指定语言的单词索引增量更新
- **方法签名**: `func getWordIndexUpdates(language: LanguageCode, since: Int?) async throws -> WordIndexResponse`
- **输入参数**:
  - `language`: 目标语言
  - `since`: 上次同步的ID，用于增量更新（传nil获取全部数据）
- **返回值**: `WordIndexResponse` - 索引更新数据
- **使用示例**:
```swift
// 增量更新（指定since参数）
let updates = try await adapter.getWordIndexUpdates(language: .chinese, since: lastSyncId)
let newWords = updates.data

// 全量更新（传nil）
let allUpdates = try await adapter.getWordIndexUpdates(language: .chinese, since: nil)
```

### 4. 核心数据契约 (DTO 定义)

#### API DTO - 搜索索引相关
```swift
// 单词索引响应
struct WordIndexResponse: Codable {
    let success: Bool
    let data: [WordIndexItem]
    let lastSyncId: Int
    let metadata: WordIndexMetadata

    // 确保success字段在成功响应中始终为true
    init(data: [WordIndexItem], lastSyncId: Int, metadata: WordIndexMetadata) {
        self.success = true
        self.data = data
        self.lastSyncId = lastSyncId
        self.metadata = metadata
    }
}

// 单词索引项
struct WordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
}

// 音标符号
struct PhoneticSymbol: Codable {
    let type: String  // "BrE" 或 "NAmE"
    let symbol: String
}

// 索引元数据
struct WordIndexMetadata: Codable {
    let totalWords: Int
    let lastUpdated: String
}
```

---

## 全局架构集成指南 (Global Architecture Integration Guide)

### 1. 依赖注入容器使用

#### AdapterContainer配置
```swift
// 获取Adapter实例
let container = AdapterContainer.shared

// 使用各个Adapter
let authAdapter = container.authAPIAdapter
let wordAdapter = container.wordAPIAdapter
let userAdapter = container.userAPIAdapter
let bookmarkAdapter = container.bookmarkAPIAdapter
let audioAdapter = container.audioAPIAdapter
let purchaseAdapter = container.purchaseAPIAdapter
let searchAdapter = container.searchAPIAdapter
```

### 2. 错误处理最佳实践

#### 统一错误处理模式
```swift
// 在Business Service中处理Adapter错误
func handleAdapterError(_ error: Error) -> BusinessError {
    switch error {
    case let apiError as APIError:
        return convertAPIErrorToBusiness(apiError)
    case let networkError as URLError:
        return .networkUnavailable
    default:
        return .unknownError
    }
}
```

### 3. 性能优化建议

#### 缓存策略
- **Session信息**: 缓存在Keychain中，避免重复登录
- **用户资料**: 内存缓存30分钟，减少API调用
- **单词定义**: 本地数据库缓存，支持离线访问
- **生词本**: 本地缓存 + 定期同步

#### 网络优化
- **请求合并**: 批量操作时合并多个API请求
- **超时设置**: 根据操作类型设置合适的超时时间
- **重试机制**: 网络错误时使用指数退避重试

### 4. 监控与日志

#### 日志记录规范
```swift
// Adapter层日志示例
print("[AuthAPIAdapter] 开始登录请求: provider=\(provider)")
print("[AuthAPIAdapter] 登录成功: sessionId=\(sessionId.prefix(8))***")
print("[AuthAPIAdapter] 登录失败: error=\(error.localizedDescription)")
```

#### 性能监控
- 记录每个API调用的响应时间
- 监控成功率和错误率
- 跟踪网络请求大小和频率

---

*本文档基于SenseWord iOS应用的实际Adapter实现生成，所有接口签名和数据结构都与实际代码100%一致。文档面向业务服务层开发者，提供完整的API转译层使用指南。*
