## TTS线性处理系统实施要点

### For Azure TTS 400 Bad Request errors: 
- **添加必要的xmlns命名空间**：SSML必须包含`xmlns="http://www.w3.org/2001/10/synthesis"`
- **完善HTTP请求头部**：必须包含`User-Agent`、`Content-Length`和正确的`Content-Type: application/ssml+xml; charset=utf-8`
- **正确处理XML特殊字符**：使用XML实体（&amp; &lt; &gt; &quot; &apos;）而非简单移除
- **简化voice标签格式**：移除重复的xml:lang属性，使用`<voice name="voice-name">text</voice>`

### For TTS音频生成优化：
- **直接使用单词文本而非音标**：让不同语音模型自然产生对应口音（en-GB-AdaMultilingualNeural生成英音，en-US-AndrewMultilingualNeural生成美音）
- **线性处理架构**：用户立即获得文本内容，音频后台异步生成，通过状态查询接口轮询进度
- **奥卡姆剃刀应用**：从复杂的11函数7状态简化为4函数2状态，复用现有数据库字段

### For Cloudflare Workers部署：
- **API Worker配置**：确保STATIC_API_KEY不是占位符，使用实际密钥如`sk-senseword-api-dev-2025-v1`
- **TTS Worker定时任务**：受Cloudflare限制使用1分钟间隔，实际处理效率仍很高
- **R2存储配置**：使用正确的存储桶名称和CDN域名配置

### For Cloudflare Worker 530/1016 DNS解析错误：
- **使用标准Azure TTS端点**：避免自定义域名DNS解析问题，使用`https://${region}.tts.speech.microsoft.com`而非自定义域名
- **清理Cloudflare Worker特殊头部**：移除`cf-workers-preview-token`、`cf-connecting-ip`、`cf-ray`等可能干扰的头部
- **使用干净的Request对象**：创建新的Request对象并清理头部后再调用fetch，避免Worker内部头部污染外部API调用

## 认证系统架构变更

### For SenseWord认证系统从JWT迁移到Session：
- **架构变更**：系统已从JWT认证完全迁移到Session认证系统，JWT相关代码已废弃
- **Session ID格式**：使用`sess_`前缀 + 20位随机字符，如`sess_1a2b3c4d5e6f7g8h9i0j`
- **认证中间件**：使用`session-auth.middleware.ts`替代`auth-verify.middleware.ts`，调用`authenticateSessionRequest()`函数
- **API请求格式**：Authorization头部使用`Bearer <sessionId>`而非`Bearer <jwtToken>`
- **数据库设计**：sessions表存储永久Session记录，支持多设备登录和即时撤销

## iOS开发与KDD工程实践

### For Swift协议默认参数设计模式：
- **使用协议+扩展模式**：Swift协议不能直接定义带默认参数的方法，应该协议定义核心契约，扩展提供默认参数便利方法
- **Apple标准实践**：URLSession.data(from:)、Combine.Publisher.sink()等都使用这种模式
- **KDD契约优化**：契约文档应描述期望的API体验而非具体实现，允许工程采用语言最佳实践
- **验证标准调整**：功能符合度和API体验一致性比1:1字面映射更重要

### For KDD契约制定与工程实现的关系：
- **契约职责**：描述期望的API体验和功能需求，提供实现指导框架
- **工程职责**：在语言约束下选择最优技术方案，确保可维护性和扩展性
- **合理差异**：内部架构优化、语言特性适配、性能优化等属于合理的工程优化
- **验证重点**：功能完整性、API体验一致性、代码质量、可测试性
