# SenseWord 后端API接口能力文档

> **前端乐高拼装说明书** - 面向前端开发者的完整后端能力规范
> 生成时间: 2025-07-25
> 版本: v4.0 (架构升级版 - 基于Hono框架和队列架构)

---

## 🛡️ 安全加固说明 (Security Enhancement)

**重要更新**: 基于KDD-017安全加固计划，所有API端点现已实施静态密钥保护，彻底消除了之前的安全漏洞：

### 安全策略
- **静态密钥**: `sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **双重认证**: 敏感操作需要静态密钥 + Session双重验证
- **零公开端点**: 所有API现在都需要至少静态密钥保护

### 安全收益
- ✅ 防止API滥用和未授权访问
- ✅ 确保请求来自合法的SenseWord应用
- ✅ 建立分层防护机制
- ✅ 统一的安全架构和错误处理

---

## 1. 能力接口总览表 (Capability Interface Overview Table)

### 1.1 身份认证模块 (Authentication Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `POST /api/v1/auth/login` | 处理Apple登录，创建用户Session | 静态密钥 | ✅ 已实现 |
| `POST /api/v1/auth/logout` | 撤销当前Session，用户登出 | 双重认证 | ✅ 已实现 |
| `POST /api/v1/auth/logout-all` | 撤销用户所有Session，退出所有设备 | 双重认证 | ✅ 已实现 |
| `DELETE /api/v1/users/me` | 删除用户账号及所有相关数据 | 双重认证 | ✅ 已实现 |
| `GET /api/v1/users/me` | 获取当前用户详细信息 | 双重认证 | ✅ 已实现 |
| `POST /api/v1/purchase/verify` | 验证App Store购买，更新Pro状态 | 双重认证 | ✅ 已实现 |
| `POST /api/v1/purchase/restore` | 恢复用户购买记录 | 双重认证 | ✅ 已实现 |
| `GET /api/v1/auth/health` | 认证服务健康检查 | 静态密钥 | ✅ 已实现 |

### 1.2 单词服务模块 (Word Service Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `GET /api/v1/word/{word}` | 查询或按需生成单词深度解析 | 静态密钥 | ✅ 已实现 |
| `POST /api/v1/feedback` | 接收用户对单词内容的点赞/踩 | 静态密钥 | ✅ 已实现 |
| `GET /api/v1/audio/{word}/status?lang={lang}` | 查询单词在特定语言下的TTS音频生成状态 | 静态密钥 | ✅ 已实现 |

#

### 1.4 生词本管理模块 (Bookmark Management Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `POST /api/v1/bookmarks` | 添加单词到个人生词本 | 双重认证 | ✅ 已实现 |
| `DELETE /api/v1/bookmarks` | 从个人生词本删除单词 | 双重认证 | ✅ 已实现 |
| `GET /api/v1/bookmarks` | 获取用户完整生词收藏列表 | 双重认证 | ✅ 已实现 |
| `GET /api/v1/bookmarks/health` | 生词本服务健康检查 | 静态密钥 | ✅ 已实现 |

### 1.5 索引同步模块 (Index Sync Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `GET /api/v1/word-index/updates` | 为客户端提供搜索索引增量更新 | 静态密钥 | ✅ 已实现 |

### 1.6 质量管理模块 (Quality Management Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `POST /trigger` | 手动触发低分单词清理任务 | 静态密钥 | ✅ 已实现 |

### 1.7 测试工具模块 (Testing Tools Module)

| 端点 | 核心职责 | 认证 | 状态 |
| :--- | :--- | :--- | :--- |
| `GET /api/v1/test-prompt/{word}` | 测试提示词占位符替换（开发用） | 静态密钥 | ⚠️ 仅开发环境 |



### 认证方式说明
- **公开**: 无需认证头，任何人都可以访问（⚠️ 已废弃，所有API现在都需要至少静态密钥保护）
- **静态密钥**: 需要 `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` 头部，用于应用级别的访问控制
- **Bearer认证**: 需要 `Authorization: Bearer <staticApiKey>` 头部，用于测试和开发工具的访问控制
- **双重认证**: 需要 `X-Static-API-Key` + `Authorization: Bearer <sessionId>` 两个头部，提供最高级别的安全保护

---

## 认证与用户管理模块

### 1. 模块概述 (Module Overview)
- 提供Apple登录认证、Session管理、用户登出、账号删除、用户信息获取和购买验证等完整的用户生命周期管理能力。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `https://auth.senseword.app`
- **开发环境**: `https://senseword-auth-worker-dev.zhouqi-aaha.workers.dev`

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 Apple登录认证
- **职责**: 处理Apple ID Token验证并创建用户Session
- **方法与路径**: `POST /api/v1/auth/login`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X POST https://auth.senseword.app/api/v1/auth/login \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "idToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "provider": "apple"
  }'
```
- **成功响应示例**:
```json
{
  "success": true,
  "session": {
    "sessionId": "sess_1a2b3c4d5e6f7g8h9i0j",
    "user": {
      "id": "usr_abc123",
      "email": "<EMAIL>",
      "displayName": "John Doe",
      "isPro": false
    }
  }
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `X-Response-Time: {responseTime}ms` (实际处理时间)
  - `Cache-Control: no-cache, no-store, must-revalidate`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "Apple ID Token验证失败"
  },
  "timestamp": "2025-06-25T10:30:45.123Z"
}
```
- **可能的错误码**:
  - `INVALID_TOKEN`: Apple ID Token验证失败
  - `USER_CREATION_FAILED`: 用户创建失败
  - `SYSTEM_ERROR`: 系统内部错误
  - `INVALID_REQUEST`: 请求格式无效
  - `INVALID_API_KEY`: 静态API密钥无效
- **静态密钥验证失败响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_API_KEY",
    "message": "静态API密钥无效或缺失"
  },
  "timestamp": "2025-06-25T10:30:45.123Z"
}
```


#### 3.2 获取当前用户信息
- **职责**: 获取已认证用户的详细信息
- **方法与路径**: `GET /api/v1/users/me`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X GET https://auth.senseword.app/api/v1/users/me \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j"
```
- **成功响应示例**:
```json
{
  "success": true,
  "user": {
    "id": "usr_abc123",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "isPro": true,
    "createdAt": "2025-06-25T10:30:00.000Z"
  }
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Cache-Control: no-cache, no-store, must-revalidate`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "认证失败或Session无效"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `UNAUTHORIZED`: 认证失败或Session无效
  - `SESSION_NOT_FOUND`: Session不存在或已失效
  - `SESSION_EXPIRED`: Session已过期
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.3 购买验证
- **职责**: 验证App Store购买收据并更新用户Pro状态
- **方法与路径**: `POST /api/v1/purchase/verify`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X POST https://auth.senseword.app/api/v1/purchase/verify \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "receiptData": "MIITtgYJKoZIhvcNAQcCoIITpzCCE6M...",
    "productId": "com.senseword.premium.monthly",
    "transactionId": "1000000123456789"
  }'
```
- **请求体结构** (必需):
```json
{
  "receiptData": "string",           // Base64编码的App Store收据
  "productId": "string",             // "com.senseword.premium.monthly" 或 "com.senseword.premium.yearly"
  "transactionId": "string"          // Apple交易ID
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "isPro": true,
  "expiresAt": "2025-07-25T10:30:00.000Z",
  "message": "购买验证成功"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `X-Request-ID: <requestId>`
  - `X-Processing-Time: <processingTime>ms`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_INPUT",
    "message": "请求体格式无效"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `INVALID_INPUT`: 请求体格式无效或缺少必需字段
  - `VERIFICATION_FAILED`: Apple收据验证失败
  - `INVALID_RECEIPT`: 收据数据无效
  - `EXPIRED_RECEIPT`: 收据已过期
  - `DUPLICATE_PURCHASE`: 重复的购买记录
  - `ENVIRONMENT_MISMATCH`: 环境不匹配（沙盒/生产）
  - `BUNDLE_ID_MISMATCH`: Bundle ID不匹配
  - `NETWORK_ERROR`: 网络错误，可重试
  - `INTERNAL_ERROR`: 服务器内部错误
  - `UNAUTHORIZED`: 认证失败
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.4 恢复购买
- **职责**: 恢复用户之前的App Store购买记录，验证现有收据并更新订阅状态
- **方法与路径**: `POST /api/v1/purchase/restore`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X POST https://auth.senseword.app/api/v1/purchase/restore \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "receiptData": "MIITtgYJKoZIhvcNAQcCoIITpzCCE6M..."
  }'
```
- **请求体结构** (必需):
```json
{
  "receiptData": "string"            // Base64编码的App Store收据数据
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "isPro": true,
  "expiresAt": "2025-07-25T10:30:00.000Z",
  "message": "购买已成功恢复"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `X-Request-ID: <requestId>`
  - `X-Processing-Time: <processingTime>ms`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "NO_PURCHASES_TO_RESTORE",
    "message": "没有找到可恢复的购买记录"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `INVALID_INPUT`: 请求体格式无效或收据数据缺失
  - `NO_PURCHASES_TO_RESTORE`: 没有找到可恢复的购买记录
  - `RESTORE_FAILED`: 恢复购买失败
  - `INVALID_RECEIPT`: 收据数据无效
  - `EXPIRED_RECEIPT`: 收据已过期
  - `ENVIRONMENT_MISMATCH`: 环境不匹配（沙盒/生产）
  - `BUNDLE_ID_MISMATCH`: Bundle ID不匹配
  - `NETWORK_ERROR`: 网络错误，可重试
  - `INTERNAL_ERROR`: 服务器内部错误
  - `UNAUTHORIZED`: 认证失败
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.5 用户登出
- **职责**: 撤销当前Session，实现安全登出
- **方法与路径**: `POST /api/v1/auth/logout`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X POST https://auth.senseword.app/api/v1/auth/logout \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "user_initiated"
  }'
```
- **请求体结构** (可选):
```json
{
  "reason": "user_initiated"  // 可选: "user_initiated" | "security" | "admin"
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "message": "已成功登出",
  "sessionRevoked": true,
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "SESSION_NOT_FOUND",
    "message": "无效的Session信息"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `SESSION_NOT_FOUND`: Session不存在或已失效
  - `ALREADY_LOGGED_OUT`: Session已失效或不存在
  - `UNAUTHORIZED`: 认证失败，请重新登录
  - `SYSTEM_ERROR`: 登出过程中发生错误
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.6 退出所有设备
- **职责**: 撤销用户的所有Session，从所有设备登出
- **方法与路径**: `POST /api/v1/auth/logout-all`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X POST https://auth.senseword.app/api/v1/auth/logout-all \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "user_request"
  }'
```
- **请求体结构** (可选):
```json
{
  "reason": "user_request"  // 可选: "security_concern" | "device_lost" | "user_request"
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "message": "已从所有设备登出",
  "sessionsRevoked": 3,
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "认证失败，请重新登录"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `UNAUTHORIZED`: 认证失败，请重新登录
  - `SYSTEM_ERROR`: 全设备登出过程中发生错误
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.7 删除用户账号
- **职责**: 永久删除用户账号及所有相关数据
- **方法与路径**: `DELETE /api/v1/users/me`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X DELETE https://auth.senseword.app/api/v1/users/me \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{"confirmation": "DELETE_MY_ACCOUNT"}'
```
- **请求体结构** (必需):
```json
{
  "confirmation": "DELETE_MY_ACCOUNT"  // 必需的确认字符串
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "message": "账户已成功删除",
  "dataCleared": {
    "sessions": 2,
    "bookmarks": 15,
    "userRecord": true
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_CONFIRMATION",
    "message": "请提供正确的确认字符串：DELETE_MY_ACCOUNT"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `INVALID_CONFIRMATION`: 确认字符串无效或缺失
  - `UNAUTHORIZED`: 认证失败，请重新登录
  - `SYSTEM_ERROR`: 账户删除过程中发生错误
  - `INVALID_API_KEY`: 静态API密钥无效

#### 3.8 健康检查
- **职责**: 检查认证服务状态，提供服务版本和环境信息，用于监控和运维
- **方法与路径**: `GET /api/v1/auth/health`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X GET https://auth.senseword.app/api/v1/auth/health \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "status": "healthy",
  "service": "auth-worker",
  "timestamp": "2025-06-25T10:30:00.000Z",
  "environment": "production",
  "version": "1.0.0"
}
```
- **响应头**:
  - `Content-Type: application/json`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_API_KEY",
    "message": "Missing X-Static-API-Key header"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **可能的错误码**:
  - `INVALID_API_KEY`: 静态API密钥无效、缺失或配置错误

### 4. 核心数据契约 (DTO 定义)

```typescript
// 登录请求
interface LoginRequestBody {
  idToken: string;                    // Apple ID Token
  provider: 'apple';                  // 认证提供方
}

// Session登录成功响应
interface SessionLoginSuccessResponse {
  success: true;
  session: {
    sessionId: string;                // 永久Session ID
    user: UserInfo;
  };
}

// 用户信息结构 (登录响应中的用户信息)
interface UserInfo {
  id: string;                         // 用户唯一标识
  email: string;                      // 用户邮箱
  displayName: string;                // 显示名称
  isPro: boolean;                     // Pro状态（计算属性）
}

// 用户详细信息结构 (用户信息获取API响应)
interface UserDetailInfo {
  id: string;                         // 用户唯一标识
  email: string;                      // 用户邮箱
  displayName: string;                // 显示名称
  isPro: boolean;                     // Pro状态（计算属性）
  createdAt: string;                  // 创建时间 (ISO 8601)
}

// 获取用户信息成功响应
interface UserProfileResponse {
  success: true;
  user: UserDetailInfo;
}

// 获取用户信息错误响应
interface UserProfileErrorResponse {
  success: false;
  error: {
    code: 'UNAUTHORIZED' | 'SESSION_NOT_FOUND' | 'SESSION_EXPIRED' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 购买验证请求
interface VerifyPurchaseRequest {
  receiptData: string;                // Base64编码的App Store收据
  productId: string;                  // "com.senseword.premium.monthly" 或 "com.senseword.premium.yearly"
  transactionId: string;              // Apple交易ID
}

// 购买验证成功响应
interface VerifyPurchaseResponse {
  success: true;
  isPro: boolean;                     // 用户Pro状态
  expiresAt?: string;                 // 订阅过期时间 (ISO 8601)
  message: string;                    // 响应消息
}

// 购买验证错误响应
interface VerifyPurchaseErrorResponse {
  success: false;
  error: {
    code: 'INVALID_INPUT' | 'VERIFICATION_FAILED' | 'INVALID_RECEIPT' | 'EXPIRED_RECEIPT' |
          'DUPLICATE_PURCHASE' | 'ENVIRONMENT_MISMATCH' | 'BUNDLE_ID_MISMATCH' |
          'NETWORK_ERROR' | 'INTERNAL_ERROR' | 'UNAUTHORIZED' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 恢复购买请求
interface RestorePurchaseRequest {
  receiptData: string;                // Base64编码的App Store收据数据
}

// 恢复购买成功响应
interface RestorePurchaseResponse {
  success: true;
  isPro: boolean;                     // 恢复后的Pro状态
  expiresAt?: string;                 // 订阅过期时间 (ISO 8601)
  message: string;                    // 恢复结果消息："购买已成功恢复" 或 "没有找到有效的订阅"
}

// 恢复购买错误响应
interface RestorePurchaseErrorResponse {
  success: false;
  error: {
    code: 'INVALID_INPUT' | 'NO_PURCHASES_TO_RESTORE' | 'RESTORE_FAILED' | 'INVALID_RECEIPT' |
          'EXPIRED_RECEIPT' | 'ENVIRONMENT_MISMATCH' | 'BUNDLE_ID_MISMATCH' |
          'NETWORK_ERROR' | 'INTERNAL_ERROR' | 'UNAUTHORIZED' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 登出请求
interface LogoutRequest {
  reason?: 'user_initiated' | 'security' | 'admin';  // 可选的登出原因
}

// 登出成功响应
interface LogoutSuccessResponse {
  success: true;
  message: string;                    // 用户友好的提示信息
  sessionRevoked: boolean;            // Session是否成功撤销
  timestamp: string;                  // 操作时间戳
}

// 退出所有设备请求
interface LogoutAllRequest {
  reason?: 'security_concern' | 'device_lost' | 'user_request';  // 可选的登出原因
}

// 退出所有设备成功响应
interface LogoutAllSuccessResponse {
  success: true;
  message: string;                    // 用户友好的提示信息
  sessionsRevoked: number;            // 撤销的Session数量
  timestamp: string;                  // 操作时间戳
}

// 退出所有设备错误响应
interface LogoutAllErrorResponse {
  success: false;
  error: {
    code: 'UNAUTHORIZED' | 'SYSTEM_ERROR' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 删除账号请求
interface AccountDeletionRequest {
  confirmation: 'DELETE_MY_ACCOUNT';  // 必需的确认字符串
}

// 删除账号成功响应
interface AccountDeletionSuccessResponse {
  success: true;
  message: string;                    // 用户友好的提示信息
  dataCleared: {                      // 清理的数据统计
    sessions: number;                 // 撤销的Session数量
    bookmarks: number;                // 删除的生词数量
    userRecord: boolean;              // 用户记录是否删除
  };
  timestamp: string;                  // 操作时间戳
}

// 删除账号错误响应
interface AccountDeletionErrorResponse {
  success: false;
  error: {
    code: 'INVALID_CONFIRMATION' | 'UNAUTHORIZED' | 'SYSTEM_ERROR' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 登录错误响应
interface LoginErrorResponse {
  success: false;
  error: {
    code: 'INVALID_TOKEN' | 'USER_CREATION_FAILED' | 'SYSTEM_ERROR' | 'INVALID_REQUEST' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 登出错误响应
interface LogoutErrorResponse {
  success: false;
  error: {
    code: 'SESSION_NOT_FOUND' | 'ALREADY_LOGGED_OUT' | 'UNAUTHORIZED' | 'SYSTEM_ERROR' | 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 健康检查成功响应
interface HealthCheckResponse {
  status: 'healthy';
  service: string;                    // 服务名称，如 "auth-worker"
  timestamp: string;                  // ISO 8601格式的时间戳
  environment: string;                // 环境信息，如 "production", "development"
  version: string;                    // 服务版本号
}

// 健康检查错误响应
interface HealthCheckErrorResponse {
  success: false;
  error: {
    code: 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // ISO 8601格式的时间戳
}

// 通用认证错误响应
interface AuthErrorResponse {
  success: false;
  error: {
    code: 'INVALID_TOKEN' | 'USER_NOT_FOUND' | 'SESSION_EXPIRED' | 'SESSION_NOT_FOUND' | 'INVALID_CONFIRMATION' |
          'INVALID_INPUT' | 'VERIFICATION_FAILED' | 'INVALID_RECEIPT' | 'EXPIRED_RECEIPT' | 'DUPLICATE_PURCHASE' |
          'ENVIRONMENT_MISMATCH' | 'BUNDLE_ID_MISMATCH' | 'NETWORK_ERROR' | 'INTERNAL_ERROR' |
          'NO_PURCHASES_FOUND' | 'NO_PURCHASES_TO_RESTORE' | 'RESTORE_FAILED' | 'UNAUTHORIZED' | 'ALREADY_LOGGED_OUT' | 'SYSTEM_ERROR';
    message: string;
  };
  timestamp?: string;                 // 可选的时间戳
}
```

### 5. 测试与调试 (Testing & Debugging)
- **预设测试数据**:
  - 测试用户: `<EMAIL>` (Session ID: `sess_dev_test_001234567`)
  - Pro用户: `<EMAIL>` (Session ID: `sess_pro_test_001234567`)
- **常见错误码**:

| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| INVALID_TOKEN | Apple ID Token无效 | 重新获取Apple ID Token |
| SESSION_EXPIRED | Session已过期 | 重新登录 |
| SESSION_NOT_FOUND | Session不存在或已失效 | 重新登录 |
| ALREADY_LOGGED_OUT | Session已失效或不存在 | 重新登录 |
| UNAUTHORIZED | 认证失败 | 重新登录 |
| SYSTEM_ERROR | 系统内部错误 | 稍后重试或联系技术支持 |
| USER_NOT_FOUND | 用户不存在 | 检查用户ID |
| INVALID_CONFIRMATION | 确认字符串无效或缺失 | 提供正确的确认字符串 |
| INVALID_INPUT | 请求体格式无效或缺少必需字段 | 检查请求格式和必需字段 |
| VERIFICATION_FAILED | Apple收据验证失败 | 检查收据数据或稍后重试 |
| INVALID_RECEIPT | 收据数据无效 | 重新获取有效收据 |
| EXPIRED_RECEIPT | 收据已过期 | 使用最新的收据数据 |
| DUPLICATE_PURCHASE | 重复的购买记录 | 购买已处理，无需重复验证 |
| ENVIRONMENT_MISMATCH | 环境不匹配（沙盒/生产） | 确认使用正确的环境 |
| BUNDLE_ID_MISMATCH | Bundle ID不匹配 | 检查应用配置 |
| NETWORK_ERROR | 网络错误 | 稍后重试 |
| INTERNAL_ERROR | 服务器内部错误 | 稍后重试或联系技术支持 |
| NO_PURCHASES_FOUND | 未找到有效的购买记录 | 确认是否有有效订阅 |
| NO_PURCHASES_TO_RESTORE | 没有找到可恢复的购买记录 | 确认是否有有效的历史购买 |
| RESTORE_FAILED | 恢复购买失败 | 稍后重试或联系技术支持 |

---

## 单词查询与生成模块

### 1. 模块概述 (Module Overview)
- 提供英语单词的AI生成定义、查询、反馈评分和音频状态查询等核心学习功能。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `https://api.senseword.app`
- **开发环境**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 单词查询 (RESTful API v2.0)
- **职责**: 查询指定单词的详细定义和学习内容，支持多语言脚手架架构
- **方法与路径**: `GET /api/v1/words/{learningLanguage}/{scaffoldingLanguage}/{wordName}`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **路径参数**:
  - `learningLanguage` (必需): 学习语言代码，通常为 `en` (英语)
  - `scaffoldingLanguage` (必需): 脚手架语言代码，支持 `zh`, `ja`, `ko`, `fr`, `de`, `es`, `it`, `pt`, `ru`, `ar`, `hi`, `th`, `vi`, `tr`, `pl`, `nl`, `sv`, `da`, `no`, `fi`, `id`
  - `wordName` (必需): 要查询的英语单词
- **请求示例**:
```bash
# 新版RESTful API
curl -X GET "https://api.senseword.app/api/v1/words/en/zh/progressive" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```

#### 3.1.1 向后兼容API (已弃用)
- **方法与路径**: `GET /api/v1/word/{wordName}?lang={lang}` (保持向后兼容，但建议使用新版API)
- **请求示例**:
```bash
# 向后兼容API (将被重定向到新版API)
curl -X GET "https://api.senseword.app/api/v1/word/progressive?lang=zh" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "word": "progressive",
  "metadata": {
    "wordFrequency": "Medium",
    "relatedConcepts": ["advancement", "gradual", "reform", "development"]
  },
  "content": {
    "difficulty": "B2",
    "phoneticSymbols": [
      {"type": "BrE", "symbol": "/prəˈɡresɪv/"},
      {"type": "NAmE", "symbol": "/prəˈɡresɪv/"}
    ],
    "coreDefinition": "逐步发展的；进步的；先进的",
    "contextualExplanation": {
      "nativeSpeakerIntent": "表达事物按阶段逐步发展或改进的概念",
      "emotionalResonance": "积极向上，暗示持续改进和发展",
      "vividImagery": "如同爬楼梯，一步一步向上攀登",
      "etymologicalEssence": "来自拉丁语'progressus'，意为'向前走'"
    }
  }
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
  - `Cache-Control: public, max-age=604800`（已存在单词）或 `public, max-age=86400`（新生成单词）
- **错误响应示例**:
```json
{
  "error": {
    "code": "INVALID_WORD",
    "message": "API密钥无效或缺失"
  }
}
```
- **可能的错误码**:
  - `INVALID_WORD`: 单词参数无效、API密钥无效或缺失、缺少必需参数
  - `DATABASE_ERROR`: 数据库查询时发生错误
- **HTTP状态码**:
  - `200`: 查询成功（仅对已存在的单词）
  - `401`: API密钥无效或缺失（不缓存）
  - `404`: 单词不存在（缓存5分钟）
  - `400/500`: 其他错误（缓存5分钟）
- **缓存策略**:
  - 已存在单词：`max-age=604800`（7天）
  - 新生成单词：此功能已移除
  - 认证错误：`no-cache, no-store, must-revalidate`（不缓存）
  - 其他错误：`max-age=300`（5分钟）


#### 3.2 用户反馈
- **职责**: 收集用户对单词定义质量的反馈评分，更新质量评分用于内容优化
- **方法与路径**: `POST /api/v1/feedback`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X POST https://api.senseword.app/api/v1/feedback \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Content-Type: application/json" \
  -d '{
    "word": "progressive",
    "language": "zh",
    "action": "like"
  }'
```
- **请求体结构** (必需):
```json
{
  "word": "string",                  // 要反馈的单词
  "language": "string",              // 语言代码，如 "zh", "en"
  "action": "like" | "dislike"       // 反馈动作：赞或踩
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "newScore": 6,
  "message": "反馈记录成功。新分数: 6"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_ACTION",
    "message": "请求体格式无效，请提供有效的JSON"
  }
}
```
- **可能的错误码**:
  - `INVALID_ACTION`: 请求体格式无效、缺少必需参数或无效的反馈动作
  - `AUTHENTICATION_REQUIRED`: 静态API密钥无效或缺失
  - `INVALID_WORD`: 单词定义不存在
  - `DATABASE_ERROR`: 处理反馈时发生服务器错误

#### 3.3 音频状态查询 ⚠️ 已在预生产模式下移除
- **状态**: 该功能已在预生产环境中移除，应用现在假设所有音频内容已预生成并可用
- **职责**: 查询指定单词在特定语言下的TTS音频生成状态（仅适用于生产环境）
- **方法与路径**: `GET /api/v1/audio/{wordName}/status?lang={languageCode}`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **查询参数**:
  - `lang` (可选): 语言代码，默认为 `zh`。不同语言的音频状态是独立的
- **状态说明**:
  - `processing`: 音频正在生成中（包含数据库中的 `pending` 和 `failed` 状态）
  - `completed`: 音频生成完成，可以播放
  - `word_ready`: 单词发音音频已生成
  - `all_ready`: 所有音频（单词、例句、短语）均已生成
- **请求示例**:
```bash
curl -X GET "https://api.senseword.app/api/v1/audio/progressive/status?lang=zh" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **处理中响应示例**:
```json
{
  "word": "progressive",
  "audioStatus": "processing",
  "detailedStatus": "retrying",
  "hasWordAudio": false,
  "hasExampleAudio": false,
  "hasPhraseAudio": false,
  "ttsStatus": "failed",
  "lastUpdated": "2025-06-25T10:30:45.123Z"
}
```
- **完成响应示例**:
```json
{
  "word": "progressive",
  "audioStatus": "completed",
  "detailedStatus": "all_ready",
  "hasWordAudio": true,
  "hasExampleAudio": true,
  "hasPhraseAudio": true,
  "ttsStatus": "all_audio_ready",
  "lastUpdated": "2025-06-25T10:31:20.456Z"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_WORD",
    "message": "API密钥无效或缺失"
  }
}
```
- **可能的错误码**:
  - `INVALID_WORD`: 单词参数无效、API密钥无效或缺失、单词记录不存在
  - `DATABASE_ERROR`: 音频状态查询失败
- **HTTP状态码**:
  - `200`: 查询成功
  - `401`: API密钥无效或缺失
  - `404`: 单词记录不存在
  - `500`: 服务器内部错误
- **多语言查询示例**:
```bash
# 查询中文音频状态
curl -X GET "https://api.senseword.app/api/v1/audio/progressive/status?lang=zh" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"

# 查询日语音频状态（独立状态）
curl -X GET "https://api.senseword.app/api/v1/audio/progressive/status?lang=ja" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```

#### 3.4 每日一词
- **职责**: 获取当日推荐学习的单词，支持多层降级策略确保100%可用性
- **方法与路径**: `GET /api/v1/daily-word`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X GET https://api.senseword.app/api/v1/daily-word \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "word": "progressive",
  "date": "2025-06-25"
}
```
- **降级响应示例**（当配置不可用时）:
```json
{
  "word": "welcome",
  "date": "2025-06-25"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
  - `Cache-Control: public, max-age=3600`（可靠数据）或 `public, max-age=300`（降级数据）
- **错误响应示例**:
```json
{
  "error": {
    "code": "INVALID_API_KEY",
    "message": "Missing X-Static-API-Key header"
  }
}
```
- **可能的错误码**:
  - `INVALID_API_KEY`: 静态API密钥无效、缺失或配置错误
- **HTTP状态码**:
  - `200`: 查询成功（包括降级响应）
  - `401`: API密钥无效或缺失
- **降级策略**:
  - 优先级1：当日配置（缓存1小时）
  - 优先级2：昨日配置（缓存1小时）
  - 优先级3：默认词"welcome"（缓存1分钟）
- **缓存策略**:
  - 可靠数据：`max-age=3600`（1小时）
  - 降级数据：`max-age=300`（5分钟）
  - 默认数据：`max-age=60`（1分钟）

### 4. 核心数据契约 (DTO 定义)

```typescript
// 单词查询响应
interface WordDefinitionResponse {
  word: string;
  metadata: WordMetadata;
  content: WordContent;
}

interface WordMetadata {
  wordFrequency: string;              // 词频等级
  relatedConcepts: string[];          // 相关概念
}

interface WordContent {
  difficulty: string;
  phoneticSymbols: PhoneticSymbol[];
  coreDefinition: string;
  contextualExplanation: ContextualExplanation;
  usageExamples: UsageExampleCategory[];
  usageScenarios: UsageScenario[];
  collocations: Collocation[];
  usageNotes: UsageNote[];
  synonyms: Synonym[];
}

// 反馈请求
interface FeedbackRequest {
  word: string;                       // 单词
  language: string;                   // 语言代码
  action: 'like' | 'dislike';        // 反馈动作
}

// 每日一词响应
interface DailyWordResponse {
  word: string;                       // 推荐单词
  date: string;                       // 日期 (YYYY-MM-DD)
}

// 单词查询错误响应
interface WordQueryErrorResponse {
  error: {
    code: 'INVALID_WORD' | 'DATABASE_ERROR';
    message: string;
  };
}

// 用户反馈成功响应
interface FeedbackSuccessResponse {
  success: true;
  newScore: number;                   // 更新后的反馈分数
  message: string;                    // 成功消息
}

// 用户反馈错误响应
interface FeedbackErrorResponse {
  error: {
    code: 'INVALID_ACTION' | 'AUTHENTICATION_REQUIRED' | 'INVALID_WORD' | 'DATABASE_ERROR';
    message: string;
  };
}

// 音频状态查询响应
interface AudioStatusResponse {
  word: string;                       // 查询的单词
  audioStatus: 'processing' | 'completed'; // 音频状态（processing包含pending和failed状态）
  lastUpdated: string;                // 最后更新时间 (ISO 8601)
}

// 音频状态查询错误响应
interface AudioStatusErrorResponse {
  error: {
    code: 'INVALID_WORD' | 'DATABASE_ERROR';
    message: string;
  };
}



// 每日一词响应
interface DailyWordResponse {
  word: string;                       // 今日推荐单词
  date: string;                       // 日期 (YYYY-MM-DD)
}

// 每日一词错误响应
interface DailyWordErrorResponse {
  error: {
    code: 'INVALID_API_KEY';
    message: string;
  };
}
```

### 5. 测试与调试 (Testing & Debugging)
- **预设测试数据**:
  - 测试单词: `progressive`, `innovative`, `sophisticated`
  - 支持语言: `zh` (中文), `ja` (日语), `de` (德语)
- **常见错误码**:

| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| INVALID_WORD | 单词参数无效、API密钥无效或缺失、缺少必需参数、单词记录不存在、请求体格式无效、Authorization头部无效 | 检查单词拼写、API密钥和必需参数，确认单词已存在，检查请求格式和认证头部 |
| DATABASE_ERROR | 数据库查询时发生错误、音频状态查询失败、生词本操作失败、索引同步查询失败 | 重试或联系技术支持 |
| INVALID_LANGUAGE | 不支持的语言代码、索引同步参数格式错误 | 使用支持的语言代码，检查查询参数格式 |
| AI_GENERATION_FAILED | AI生成失败、AI内容生成失败 | 重试或联系技术支持 |
| INVALID_ACTION | 请求体格式无效、缺少必需参数或无效的反馈动作 | 检查请求格式和参数有效性 |
| AUTHENTICATION_REQUIRED | 静态API密钥无效或缺失 | 提供有效的API密钥 |
| INVALID_API_KEY | 静态API密钥无效、缺失或配置错误 | 检查X-Static-API-Key头部和密钥值 |
| UNAUTHORIZED | Session认证失败、Session过期或无效 | 重新登录获取有效的Session |
| SESSION_EXPIRED | Session已过期 | 重新登录获取新的Session |
| AUTHENTICATION_FAILED | 索引同步API密钥无效或缺失 | 检查X-Static-API-Key头部和密钥值 |

---

## 生词本管理模块

### 1. 模块概述 (Module Overview)
- 提供用户个人生词收藏的增删查功能，支持Session认证的个人化学习管理。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `https://api.senseword.app`
- **开发环境**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 添加生词
- **职责**: 将指定单词添加到用户的个人生词本，支持重复添加检查和数据标准化
- **方法与路径**: `POST /api/v1/bookmarks`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X POST https://api.senseword.app/api/v1/bookmarks \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "word": "progressive",
    "language": "zh"
  }'
```
- **请求体结构** (必需):
```json
{
  "word": "string",                  // 要添加的英语单词
  "language": "string"               // 语言代码，如 "zh", "ja", "de"
}
```
- **成功响应示例**:
```json
{
  "success": true,
  "message": "生词添加成功"
}
```
- **重复添加响应示例**:
```json
{
  "success": true,
  "message": "该单词已在生词本中"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key, Authorization`
- **错误响应示例**:
```json
{
  "success": false,
  "message": "请求体格式无效，请提供有效的JSON"
}
```
- **可能的错误码**:
  - 请求体格式无效或缺少必需参数：返回400状态码
  - 认证失败：返回401状态码（静态密钥或Session无效）
  - 方法不支持：返回405状态码
- **HTTP状态码**:
  - `200`: 添加成功（包括重复添加的情况）
  - `400`: 请求体格式无效或缺少必需参数
  - `401`: 认证失败（静态密钥或Session无效）
  - `405`: 不支持的HTTP方法
- **数据处理**:
  - 单词标准化：自动执行 `toLowerCase().trim()` 处理
  - 语言验证：检查是否为支持的语言代码
  - 重复检查：如果单词已存在，返回友好提示而非错误

#### 3.2 删除生词
- **职责**: 从用户的个人生词本中移除指定单词，支持乐观删除处理
- **方法与路径**: `DELETE /api/v1/bookmarks`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **请求示例**:
```bash
curl -X DELETE https://api.senseword.app/api/v1/bookmarks \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j" \
  -H "Content-Type: application/json" \
  -d '{
    "word": "progressive",
    "language": "zh"
  }'
```
- **请求体结构** (必需):
```json
{
  "word": "string",                  // 要删除的英语单词
  "language": "string"               // 语言代码，如 "zh", "ja", "de"
}
```
- **确认删除响应示例**:
```json
{
  "success": true,
  "message": "生词删除成功"
}
```
- **乐观删除响应示例**（单词可能不存在）:
```json
{
  "success": true,
  "message": "操作已完成"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key, Authorization`
- **错误响应示例**:
```json
{
  "success": false,
  "message": "请求体格式无效，请提供有效的JSON"
}
```
- **可能的错误码**:
  - 请求体格式无效或缺少必需参数：返回400状态码
  - 认证失败：返回401状态码（静态密钥或Session无效）
  - 方法不支持：返回405状态码
- **HTTP状态码**:
  - `200`: 删除操作完成（包括乐观删除的情况）
  - `400`: 请求体格式无效或缺少必需参数
  - `401`: 认证失败（静态密钥或Session无效）
  - `405`: 不支持的HTTP方法
- **数据处理**:
  - 单词标准化：自动执行 `toLowerCase().trim()` 处理
  - 语言验证：检查是否为支持的语言代码
  - 乐观删除：即使单词不存在也返回成功，提供友好的用户体验

#### 3.3 获取生词列表
- **职责**: 获取用户的完整生词收藏列表，包含时间戳信息，支持按时间复习功能
- **方法与路径**: `GET /api/v1/bookmarks`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025` + `Authorization: Bearer <sessionId>` (双重认证)
- **查询参数**: 无（直接返回所有收藏，按收藏时间倒序）
- **请求示例**:
```bash
curl -X GET https://api.senseword.app/api/v1/bookmarks \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025" \
  -H "Authorization: Bearer sess_1a2b3c4d5e6f7g8h9i0j"
```
- **成功响应示例**:
```json
{
  "success": true,
  "bookmarks": [
    {
      "word": "progressive",
      "language": "zh",
      "createdAt": "2025-06-27T10:30:00.000Z"
    },
    {
      "word": "innovative",
      "language": "zh",
      "createdAt": "2025-06-26T15:20:00.000Z"
    }
  ]
}
```
- **空列表响应示例**:
```json
{
  "success": true,
  "bookmarks": []
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key, Authorization`
- **错误降级响应示例**（发生错误时）:
```json
{
  "success": false,
  "bookmarks": []
}
```
- **HTTP状态码**:
  - `200`: 查询成功（包括空列表和错误降级的情况）
  - `401`: 认证失败（静态密钥或Session无效）
  - `500`: 数据库错误（但仍返回空列表作为降级处理）
- **数据特性**:
  - 排序逻辑：按收藏时间倒序排列（最新收藏优先）
  - 完整信息：返回单词、语言代码和创建时间戳
  - 时间支持：支持按时间进行复习提醒和学习计划
  - 降级处理：发生错误时返回空列表而非报错，确保用户体验
  - 无分页：直接返回所有收藏单词

#### 3.4 生词本服务健康检查
- **职责**: 检查生词本服务和独立数据库的健康状态，提供服务可用性监控
- **方法与路径**: `GET /api/v1/bookmarks/health`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X GET https://api.senseword.app/api/v1/bookmarks/health \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **健康响应示例**:
```json
{
  "service": "bookmark-api",
  "status": "healthy",
  "timestamp": "2025-06-25T10:30:00.000Z",
  "version": "1.0.0"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
- **错误响应示例**:
```json
{
  "error": {
    "code": "INVALID_API_KEY",
    "message": "Missing X-Static-API-Key header"
  }
}
```
- **HTTP状态码**:
  - `200`: 服务健康
  - `401`: API密钥无效或缺失
- **响应字段说明**:
  - `service`: 服务标识符，固定为 "bookmark-api"
  - `status`: 服务状态，固定为 "healthy"
  - `timestamp`: 检查时间戳（ISO 8601格式）
  - `version`: 服务版本号
- **使用说明**:
  - 用于监控生词本服务的可用性
  - 需要静态API密钥认证以防止滥用
  - 响应时间通常在100ms以内

### 4. 核心数据契约 (DTO 定义)

```typescript
// 添加生词请求
interface AddBookmarkRequest {
  word: string;                       // 要添加的英语单词
  language: string;                   // 语言代码，如 "zh", "ja", "de"
}

// 删除生词请求
interface RemoveBookmarkRequest {
  word: string;                       // 要删除的英语单词
  language: string;                   // 语言代码，如 "zh", "ja", "de"
}

// 生词操作响应
interface BookmarkCRUDResponse {
  success: boolean;
  message: string;                    // 用户友好的提示信息
}

// 生词本条目详细信息
interface BookmarkItem {
  word: string;                       // 单词文本
  language: string;                   // 语言代码
  createdAt: string;                  // 创建时间 (ISO 8601)
}

// 获取生词列表响应
interface GetBookmarksResponse {
  success: boolean;
  bookmarks: BookmarkItem[];          // 完整的生词信息数组
}

// 生词本认证错误响应
interface BookmarkAuthErrorResponse {
  error: {
    code: 'INVALID_API_KEY' | 'UNAUTHORIZED' | 'SESSION_EXPIRED';
    message: string;
  };
}

// 生词本健康检查响应
interface BookmarkHealthResponse {
  service: 'bookmark-api';            // 服务标识符，固定值
  status: 'healthy';                  // 服务状态，固定值
  timestamp: string;                  // 检查时间戳 (ISO 8601)
  version: string;                    // 服务版本号
}

// 生词本健康检查错误响应
interface BookmarkHealthErrorResponse {
  success: false;
  error: {
    code: 'INVALID_API_KEY';
    message: string;
  };
}

// 索引同步请求参数
interface WordIndexRequest {
  lang: string;                       // 目标语言代码，如 "zh", "ja", "de"
  since?: number;                     // 客户端已有的最后一个syncId，默认为0
}

// 索引同步单个词条数据
interface WordIndexItem {
  syncId: number;                     // 同步ID
  word: string;                       // 单词文本
  language: string;                   // 语言代码
  phoneticSymbols: PhoneticSymbol[];  // 音标数组（BrE + NAmE）
  coreDefinition: string;             // 核心释义
}

// 音标符号
interface PhoneticSymbol {
  type: 'BrE' | 'NAmE';              // 音标类型
  symbol: string;                     // 音标符号
}

// 索引同步响应
interface WordIndexResponse {
  success: boolean;
  data: WordIndexItem[];              // 增量更新的单词数据数组
  lastSyncId: number;                 // 本次返回的最后一个syncId
  metadata: {
    count: number;                    // 本次返回的记录数量
    requestTime: number;              // 请求处理时间（毫秒）
    fromSyncId: number;               // 查询起始的syncId
    toSyncId: number;                 // 查询结束的syncId
  };
}

// 索引同步错误响应
interface WordIndexErrorResponse {
  error: {
    code: 'AUTHENTICATION_FAILED' | 'INVALID_LANGUAGE' | 'DATABASE_ERROR';
    message: string;
  };
}

// 质量管理清理结果
interface CleanupResult {
  totalScanned: number;               // 扫描的总记录数
  deletedCount: number;               // 实际删除的记录数
  deletedWords: Array<{               // 被删除的单词详细信息（最多50条）
    word: string;                     // 单词文本
    language: string;                 // 语言代码
    feedbackScore: number;            // 反馈评分
  }>;
  executionTime: number;              // 任务执行时间（毫秒）
  timestamp: string;                  // 任务执行时间戳（ISO 8601格式）
}

// 质量管理清理响应
interface QualityCleanupResponse {
  success: boolean;
  result: CleanupResult;              // 清理结果详情
  message: string;                    // 用户友好的提示信息
}

// 质量管理认证错误响应
interface QualityCleanupAuthErrorResponse {
  success: false;
  error: {
    code: 'INVALID_API_KEY';
    message: string;
  };
  timestamp: string;                  // 错误发生时间戳
}

// 质量管理执行错误响应
interface QualityCleanupExecutionErrorResponse {
  success: false;
  result: null;
  message: string;                    // 错误描述
}

// 测试工具提示词分析结果
interface PromptAnalysis {
  totalLength: number;                // 处理后提示词的总字符数
  placeholderCount: number;           // 检测到的占位符数量
  replacementSuccess: boolean;        // 占位符替换是否成功
  languageVerification: boolean;      // 语言验证是否通过
}

// 测试工具提示词样本
interface PromptSamples {
  firstParagraph: string;             // 提示词前500字符的预览
  targetLanguageUsage: string[];      // 目标语言使用示例
  finalRequest: string;               // 提示词末尾300字符的预览
}

// 测试工具提示词测试响应
interface PromptTestResponse {
  word: string;                       // 测试的单词
  language: {
    code: string;                     // 语言代码
    name: string;                     // 语言名称
  };
  promptAnalysis: PromptAnalysis;     // 提示词分析结果
  samples: PromptSamples;             // 提示词样本
  fullPrompt: string;                 // 完整的处理后提示词内容（包含敏感信息）
}

// 测试工具错误响应
interface PromptTestErrorResponse {
  error: {
    code: 'INVALID_WORD' | 'PROMPT_PROCESSING_FAILED';
    message: string;
  };
}
```

### 5. 测试与调试 (Testing & Debugging)
- **预设测试数据**: 使用认证模块的测试用户Session ID
- **常见错误码**:

| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| NOT_AUTHENTICATED | 用户未认证 | 检查Session ID |
| ALREADY_BOOKMARKED | 单词已收藏 | 提示用户或忽略 |
| NOT_BOOKMARKED | 单词未收藏 | 无法删除未收藏的单词 |
| DATABASE_ERROR | 数据库操作失败 | 重试或联系技术支持 |

---

## 测试工具模块

### 1. 模块概述 (Module Overview)
- 提供开发和测试阶段使用的工具端点，用于验证AI提示词处理和系统功能。

### 2. 服务地址 (Service Addresses)
- **开发环境**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`
- **注意**: 仅在开发环境可用，生产环境不提供此类端点

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 提示词测试
- **职责**: 测试AI提示词的占位符替换和语言处理功能
- **方法与路径**: `GET /api/v1/test-prompt/{wordName}`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **查询参数**:
  - `lang` (可选): 目标语言代码，默认为 "zh"，支持20种语言
- **环境限制**: ⚠️ 仅在开发环境可用，生产环境不提供此端点
- **请求示例**:
```bash
curl -X GET "https://senseword-api-worker-dev.zhouqi-aaha.workers.dev/api/v1/test-prompt/progressive?lang=zh" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "word": "progressive",
  "language": {
    "code": "zh",
    "name": "Chinese"
  },
  "promptAnalysis": {
    "totalLength": 2450,
    "placeholderCount": 8,
    "replacementSuccess": true,
    "languageVerification": true
  },
  "samples": {
    "firstParagraph": "你是一位专业的英语教学专家...",
    "targetLanguageUsage": ["中文", "汉语"],
    "finalRequest": "请确保所有解释都使用中文..."
  },
  "fullPrompt": "完整的处理后提示词内容..."
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
- **错误响应示例**:
```json
{
  "error": {
    "code": "INVALID_WORD",
    "message": "API密钥无效或缺失"
  }
}
```
- **处理失败响应示例**:
```json
{
  "error": {
    "code": "PROMPT_PROCESSING_FAILED",
    "message": "提示词处理测试失败: 不支持的语言代码"
  }
}
```
- **可能的错误码**:
  - `INVALID_WORD`: 单词参数无效或API密钥无效或缺失
  - `PROMPT_PROCESSING_FAILED`: 提示词处理失败、不支持的语言代码、Assets读取失败
- **HTTP状态码**:
  - `200`: 测试成功
  - `401`: API密钥无效或缺失
  - `500`: 提示词处理失败或服务器内部错误
- **响应字段说明**:
  - `promptAnalysis.totalLength`: 处理后提示词的总字符数
  - `promptAnalysis.placeholderCount`: 检测到的占位符数量
  - `promptAnalysis.replacementSuccess`: 占位符替换是否成功
  - `promptAnalysis.languageVerification`: 语言验证是否通过
  - `samples.firstParagraph`: 提示词前500字符的预览
  - `samples.targetLanguageUsage`: 目标语言使用示例
  - `samples.finalRequest`: 提示词末尾300字符的预览
  - `fullPrompt`: 完整的处理后提示词内容（包含敏感信息）
- **支持的语言**:
  - 英语(en)、中文(zh)、日语(ja)、德语(de)、法语(fr)、西班牙语(es)
  - 韩语(ko)、俄语(ru)、阿拉伯语(ar)、印地语(hi)、泰语(th)
  - 越南语(vi)、土耳其语(tr)、意大利语(it)、葡萄牙语(pt)、波兰语(pl)
  - 荷兰语(nl)、瑞典语(sv)、丹麦语(da)、挪威语(no)、芬兰语(fi)、印度尼西亚语(id)
- **技术特性**:
  - 从Assets读取提示词文件，失败时使用内置备用提示词
  - 智能占位符分析和语言映射验证
  - 详细的提示词处理统计信息
  - 完整的错误追踪和日志记录
- **安全说明**:
  - 返回完整提示词内容，包含敏感信息
  - 不应在生产环境暴露
  - 仅用于开发和测试阶段的调试

### 4. 核心数据契约 (DTO 定义)

```typescript
// 提示词测试响应
interface PromptTestResponse {
  word: string;
  language: {
    code: string;
    name: string;
  };
  promptAnalysis: {
    totalLength: number;              // 提示词总长度
    placeholderCount: number;         // 占位符数量
    replacementSuccess: boolean;      // 替换是否成功
    languageVerification: boolean;    // 语言验证是否通过
  };
  samples: {
    firstParagraph: string;           // 提示词开头片段
    targetLanguageUsage: string[];    // 目标语言使用示例
    finalRequest: string;             // 提示词结尾片段
  };
  fullPrompt: string;                 // 完整处理后的提示词
}
```

### 5. 测试与调试 (Testing & Debugging)
- **使用场景**: 开发阶段验证提示词模板、调试AI生成逻辑
- **注意事项**:
  - 仅开发环境可用
  - 返回完整提示词内容，包含敏感信息
  - 不应在生产环境暴露

---

## 索引同步模块

### 1. 模块概述 (Module Overview)
- 为客户端提供搜索索引的增量更新功能，支持本地搜索建议的实时同步。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `https://api.senseword.app`
- **开发环境**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 获取索引更新（固定分页版本）
- **职责**: 为客户端提供搜索索引的固定分页数据，优化缓存命中率
- **方法与路径**: `GET /api/v1/word-index/:learningLang/:scaffoldingLang/page/:pageNumber`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`（本地开发环境跳过验证）
- **路径参数**:
  - `learningLang` (必需): 学习语言代码，如 "en", "es", "fr"
  - `scaffoldingLang` (必需): 脚手架语言代码，如 "zh", "ja", "de"
  - `pageNumber` (必需): 页码，范围 1-100，每页 1000 条记录
- **缓存策略**: CDN 缓存 7天，浏览器缓存 1小时，缓存命中率 95%+
- **请求示例**:
```bash
# 获取英语→中文第2页数据（sync_id: 1001-2000）
curl -X GET "https://api.senseword.app/api/v1/word-index/en/zh/page/2" \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "syncId": 1001,
      "word": "progressive",
      "learningLanguage": "en",
      "scaffoldingLanguage": "zh",
      "coreDefinition": "逐步发展的；进步的"
    },
    {
      "syncId": 1002,
      "word": "innovative",
      "learningLanguage": "en",
      "scaffoldingLanguage": "zh",
      "coreDefinition": "创新的；革新的"
    }
  ],
  "lastSyncId": 1002,
  "metadata": {
    "count": 2,
    "requestTime": 45,
    "fromSyncId": 1000,
    "toSyncId": 1002
  }
}
```
- **空数据响应示例**:
```json
{
  "success": true,
  "data": [],
  "lastSyncId": 1000,
  "metadata": {
    "count": 0,
    "requestTime": 12,
    "fromSyncId": 1000,
    "toSyncId": 1000
  }
}
```
- **分页计算逻辑**:
```javascript
// 客户端页码计算
function calculateRequiredPage(lastSyncId) {
  return Math.floor(lastSyncId / 1000) + 1;
}

// 使用示例
const lastSyncId = 1247;
const pageNumber = calculateRequiredPage(lastSyncId); // 返回 2
const apiUrl = `/api/v1/word-index/en/zh/page/${pageNumber}`;
```

- **语言对组合示例**:
```bash
# 英语→中文
GET /api/v1/word-index/en/zh/page/1

# 英语→日语
GET /api/v1/word-index/en/ja/page/1

# 西班牙语→英语
GET /api/v1/word-index/es/en/page/1

# 法语→中文
GET /api/v1/word-index/fr/zh/page/1
```

- **缓存优化说明**:
  - **固定分页**: 每页 1000 条记录，页码固定，缓存命中率 95%+
  - **CDN 缓存**: 7天长期缓存，显著减少源站请求
  - **浏览器缓存**: 1小时本地缓存，提升用户体验
  - **缓存失效**: 仅在新增单词时清除最后几页缓存

- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: GET, OPTIONS`
  - `Cache-Control: public, max-age=86400` (分页请求)
  - `CDN-Cache-Control: max-age=604800` (CDN 7天缓存)
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
  - `Access-Control-Max-Age: 86400`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "API密钥无效或缺失"
  }
}
```
- **可能的错误码**:
  - `AUTHENTICATION_FAILED`: API密钥无效或缺失
  - `INVALID_LANGUAGE`: 不支持的语言代码或参数格式错误
  - `DATABASE_ERROR`: 数据库查询失败
- **HTTP状态码**:
  - `200`: 查询成功（包括空数据的情况）
  - `400`: 参数格式错误或不支持的语言代码
  - `401`: API密钥无效或缺失
  - `500`: 数据库查询失败或服务器内部错误
- **响应字段说明**:
  - `data`: 增量更新的单词数据数组
  - `lastSyncId`: 本次返回的最后一个syncId，用于下次请求的since参数
  - `metadata.count`: 本次返回的记录数量
  - `metadata.requestTime`: 请求处理时间（毫秒）
  - `metadata.fromSyncId`: 查询起始的syncId
  - `metadata.toSyncId`: 查询结束的syncId
- **使用说明**:
  - 单次最多返回1000条记录
  - 本地开发环境自动跳过API密钥验证
  - 支持增量同步，客户端应保存lastSyncId用于下次请求
  - 专为本地搜索索引构建设计，替代实时搜索建议

### 4. 核心数据契约 (DTO 定义)

```typescript
// 索引更新响应
interface WordIndexUpdatesResponse {
  success: boolean;
  data: WordIndexItem[];
  lastSyncId: number;
  metadata: {
    count: number;
    requestTime: number;
    fromSyncId: number;
    toSyncId: number;
  };
}

interface WordIndexItem {
  syncId: number;
  word: string;
  language: string;
  coreDefinition: string;
}

// 索引同步错误响应
interface WordIndexErrorResponse {
  success: false;
  error: {
    code: 'INVALID_LANGUAGE' | 'INVALID_SYNC_ID' | 'DATABASE_ERROR' | 'AUTHENTICATION_FAILED';
    message: string;
  };
  code?: number;
}
```

---

## 质量管理模块

### 1. 模块概述 (Module Overview)
- 提供单词质量管理功能，包括低分单词清理和质量监控。

### 2. 服务地址 (Service Addresses)
- **生产环境**: `https://word-quality-cleaner.zhouqi-aaha.workers.dev`
- **开发环境**: `https://word-quality-cleaner-development.zhouqi-aaha.workers.dev`

### 3. API端点详解 (Detailed API Endpoints)

#### 3.1 手动触发清理
- **职责**: 手动触发低分单词清理任务执行
- **方法与路径**: `POST /trigger`
- **认证要求**: `X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **请求示例**:
```bash
curl -X POST https://word-quality-cleaner-development.zhouqi-aaha.workers.dev/trigger \
  -H "X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
```
- **成功响应示例**:
```json
{
  "success": true,
  "result": {
    "totalScanned": 150,
    "deletedCount": 3,
    "deletedWords": [
      {
        "word": "badword1",
        "language": "zh",
        "feedbackScore": -5
      },
      {
        "word": "badword2",
        "language": "zh",
        "feedbackScore": -4
      },
      {
        "word": "badword3",
        "language": "en",
        "feedbackScore": -3
      }
    ],
    "executionTime": 245,
    "timestamp": "2025-06-25T10:30:00.000Z"
  },
  "message": "成功清理 3 个低分单词"
}
```
- **空清理响应示例**（无低分单词时）:
```json
{
  "success": true,
  "result": {
    "totalScanned": 0,
    "deletedCount": 0,
    "deletedWords": [],
    "executionTime": 12,
    "timestamp": "2025-06-25T10:30:00.000Z"
  },
  "message": "成功清理 0 个低分单词"
}
```
- **响应头**:
  - `Content-Type: application/json`
  - `Access-Control-Allow-Origin: *`
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, X-Static-API-Key`
- **错误响应示例**:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_API_KEY",
    "message": "Missing X-Static-API-Key header"
  },
  "timestamp": "2025-06-25T10:30:00.000Z"
}
```
- **执行失败响应示例**:
```json
{
  "success": false,
  "result": null,
  "message": "清理任务执行失败"
}
```
- **HTTP状态码**:
  - `200`: 清理任务执行成功（包括无低分单词的情况）
  - `401`: API密钥无效或缺失
  - `500`: 清理任务执行失败或服务器内部错误
- **响应字段说明**:
  - `result.totalScanned`: 扫描的总记录数
  - `result.deletedCount`: 实际删除的记录数
  - `result.deletedWords`: 被删除的单词详细信息（最多50条）
  - `result.executionTime`: 任务执行时间（毫秒）
  - `result.timestamp`: 任务执行时间戳（ISO 8601格式）
- **使用说明**:
  - 分数阈值从环境变量 `SCORE_THRESHOLD` 获取，默认为-3
  - `deletedWords` 数组最多返回50条记录，避免响应过大
  - 使用批量删除API提高性能
  - 清理任务会删除 `feedbackScore <= 阈值` 的所有单词记录

#### 3.2 服务状态查询
- **职责**: 获取质量清理服务的基本信息
- **方法与路径**: `GET /`
- **认证要求**: 无需认证头
- **请求示例**:
```bash
curl -X GET https://word-quality-cleaner-development.zhouqi-aaha.workers.dev/
```
- **成功响应示例**:
```json
{
  "service": "Word Quality Cleaner Worker",
  "version": "2.0.0",
  "description": "KDD-011 众包单词审核系统 - 定期清理低分单词Worker"
}
```

### 4. 核心数据契约 (DTO 定义)

```typescript
// 清理任务结果
interface CleanupResult {
  success: boolean;
  result: {
    totalScanned: number;             // 扫描的单词总数
    deletedCount: number;             // 删除的单词数量
    deletedWords: string[];           // 被删除的单词列表
    executionTime: number;            // 执行时间（毫秒）
    timestamp: string;                // 执行时间戳
  };
  message: string;
}

// 服务信息
interface ServiceInfo {
  service: string;                    // 服务名称
  version: string;                    // 版本号
  description: string;                // 服务描述
}
```

---

## 支持的语言代码

| 语言代码 | 语言名称 | 语言代码 | 语言名称 |
|----------|----------|----------|----------|
| en | English | ko | Korean |
| zh | Chinese | ru | Russian |
| ja | Japanese | ar | Arabic |
| de | German | hi | Hindi |
| fr | French | th | Thai |
| es | Spanish | vi | Vietnamese |
| it | Italian | tr | Turkish |
| pt | Portuguese | id | Indonesian |
| pl | Polish | nl | Dutch |
| sv | Swedish | da | Danish |
| no | Norwegian | fi | Finnish |

---

## 全局错误处理

所有API端点都遵循统一的错误响应格式：

```typescript
interface ErrorResponse {
  success: false;
  error: string;                      // 错误代码
  message: string;                    // 用户友好的错误信息
  details?: Record<string, any>;      // 可选的详细信息
}
```

常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

---

## 环境配置

### 开发环境
- **API Worker**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`
- **Auth Worker**: `https://senseword-auth-worker-dev.zhouqi-aaha.workers.dev`
- **TTS Worker**: `https://senseword-tts-worker-dev.zhouqi-aaha.workers.dev`

### 生产环境
- **API Worker**: `https://api.senseword.app`
- **Auth Worker**: `https://auth.senseword.app`
- **TTS Worker**: `https://senseword-tts-worker.zhouqi-aaha.workers.dev`

### 认证密钥
- **静态API密钥**: `sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025`
- **Session认证**: 使用 `Authorization: Bearer <sessionId>` 头部传递Session ID

---

*本文档由SenseWord后端能力接口文档官自动生成，确保与实际代码实现100%同步。*