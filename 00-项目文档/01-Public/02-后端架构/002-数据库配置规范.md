# 002-数据库配置规范

> **SenseWord Cloudflare D1 数据库配置统一规范**  
> 生成时间: 2025-07-28  
> 版本: v1.0  
> 维护责任人: KDD 开发团队

---

## 🗄️ Cloudflare D1 数据库概览

SenseWord 项目采用 Cloudflare D1 作为主要数据存储解决方案，当前运行两个独立的数据库实例：

### 数据库实例总览

| 数据库名称 | 数据库ID | 用途 | 主要表 | 绑定名称 |
|-----------|----------|------|--------|----------|
| `senseword-word-db` | `9f3369db-eb90-4917-8791-6b7f05e972c5` | 单词定义和内容管理 | `word_definitions` | `DB` / `WORD_DB` |
| `senseword-tts-db` | `253bb3ab-6300-4d92-b0f7-e746ef8885b3` | TTS任务和音频管理 | `tts_tasks`, `azure_batch_jobs` | `TTS_DB` |

### 架构设计原则

- **职责分离**: 单词内容与音频处理分离，避免单一数据库过载
- **独立扩展**: 每个数据库可独立进行性能优化和扩展
- **故障隔离**: 一个数据库的问题不会影响另一个服务的正常运行

---

## 📊 word_definitions 表完整结构

### 表概述
`word_definitions` 是 SenseWord 的核心数据表，存储所有英语单词的 AI 生成定义和学习内容，支持多语言脚手架和增量同步。

### 字段定义

#### 增量同步支持
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| `sync_id` | INTEGER | PRIMARY KEY AUTOINCREMENT | 增量同步ID，用于本地索引更新 |

#### 核心标识符
| 字段名 | 数据类型 | 约束 | 说明 | 示例 |
|--------|----------|------|------|------|
| `word` | TEXT | NOT NULL | 英语单词 | "progressive" |
| `learningLanguage` | TEXT | NOT NULL | 学习语言代码 | "en" |
| `scaffoldingLanguage` | TEXT | NOT NULL | 脚手架语言代码 | "zh", "ja" |

#### AI生成的核心内容
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| `contentJson` | TEXT | NOT NULL | JSON字符串形式的完整AI生成内容 |

#### 搜索优化字段
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| `coreDefinition` | TEXT | NULL | 核心定义提取字段，避免JSON解析开销 |

#### AI分析的元数据字段
| 字段名 | 数据类型 | 约束 | 说明 | 可能值 |
|--------|----------|------|------|--------|
| `difficulty` | TEXT | NULL | CEFR难度等级 | "A1", "A2", "B1", "B2", "C1", "C2" |
| `frequency` | TEXT | NULL | 词频等级 | "High", "Medium", "Low", "Rare" |
| `relatedConcepts` | TEXT | NULL | 相关概念，JSON字符串格式 | ["advancement", "gradual"] |

#### 业务字段
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| `partsOfSpeech` | TEXT | NULL | 词性信息 |
| `culturalRiskRegions` | TEXT | NULL | 文化风险区域 |

#### 标准时间戳
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| `createdAt` | TEXT | NOT NULL DEFAULT (datetime('now')) | 创建时间，ISO格式 |
| `updatedAt` | TEXT | NOT NULL DEFAULT (datetime('now')) | 更新时间，ISO格式 |

### 约束条件
- **唯一性约束**: `UNIQUE(word, learningLanguage, scaffoldingLanguage)`
- **自动更新触发器**: `trigger_word_definitions_updated_at` 自动更新 `updatedAt` 字段

---

## 🔍 索引策略

### 索引概览
word_definitions 表配置了 14 个优化索引，支持高效的多语言查询操作：

#### 核心搜索索引
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_word_search` | `word` | 单词精确查找 |
| `idx_core_definition_search` | `coreDefinition` | 定义内容搜索 |

#### 多语言支持索引
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_scaffolding_language` | `scaffoldingLanguage` | 按脚手架语言筛选 |
| `idx_word_learning_scaffolding` | `word, learningLanguage, scaffoldingLanguage` | 复合查询优化 |

#### 增量同步索引
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_sync_learning_language` | `sync_id, learningLanguage` | 增量同步查询 |
| `idx_learning_language_sync` | `learningLanguage, sync_id` | 语言特定的增量更新 |

#### 业务字段索引
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_parts_of_speech` | `partsOfSpeech` | 按词性筛选 |
| `idx_cultural_risk_regions` | `culturalRiskRegions` | 文化风险区域查询 |

#### 元数据字段索引
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_difficulty` | `difficulty` | 按难度等级筛选 |
| `idx_frequency` | `frequency` | 按词频等级筛选 |

#### 多语言优化索引（KDD-003 新增）
| 索引名 | 字段 | 用途 |
|--------|------|------|
| `idx_scaffolding_language_sync` | `scaffoldingLanguage, sync_id` | 脚手架语言增量同步（最高频） |
| `idx_language_pair_sync` | `learningLanguage, scaffoldingLanguage, sync_id` | 语言对精确查询 |
| `idx_sync_language_pair` | `sync_id, learningLanguage, scaffoldingLanguage` | 全局时间序列查询 |
| `idx_language_pair_range` | `learningLanguage, scaffoldingLanguage, sync_id` | 固定分页范围查询 |

### 查询性能优化

**高频查询模式**:
1. 单词精确查找: `WHERE word = ?`
2. 语言特定查询: `WHERE scaffoldingLanguage = ?`
3. 增量同步: `WHERE sync_id > ? AND learningLanguage = ?`
4. 难度筛选: `WHERE difficulty = ? AND scaffoldingLanguage = ?`

**索引选择策略**:
- 单字段查询优先使用单列索引
- 多条件查询利用复合索引
- 范围查询（sync_id）放在复合索引的最后位置

### 固定分页缓存优化（KDD-003）

#### 分页策略设计
- **分页大小**: 1000 条记录/页
- **页码计算**: `pageNumber = Math.floor(lastSyncId / 1000) + 1`
- **API 端点**: `/api/v1/word-index/:learningLang/:scaffoldingLang/page/:pageNumber`
- **缓存命中率**: 从 ~0% 提升到 ~95%

#### 缓存策略配置
```javascript
// 长期缓存设置
headers['Cache-Control'] = 'public, max-age=86400';           // 浏览器 24小时
headers['CDN-Cache-Control'] = 'max-age=604800';              // CDN 7天
headers['Browser-Cache-Control'] = 'max-age=3600';           // 浏览器 1小时
```

#### 性能提升效果
- **缓存命中率**: 95%+ （vs 旧版 ~0%）
- **响应时间**: 减少 80%+
- **源站请求**: 减少 95%+
- **CDN 成本**: 显著降低

---

## ⚙️ wrangler.toml 配置标准

### API Worker 配置 (`cloudflare/workers/api/wrangler.toml`)
```toml
# D1数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "senseword-word-db"
database_id = "9f3369db-eb90-4917-8791-6b7f05e972c5"
migrations_dir = "../../d1/migrations"

# 生产环境D1数据库绑定
[[env.production.d1_databases]]
binding = "DB"
database_name = "senseword-word-db"
database_id = "9f3369db-eb90-4917-8791-6b7f05e972c5"
```

### TTS Worker 配置 (`cloudflare/workers/tts/wrangler.toml`)
```toml
# TTS数据库绑定
[[d1_databases]]
binding = "TTS_DB"
database_name = "senseword-tts-db"
database_id = "253bb3ab-6300-4d92-b0f7-e746ef8885b3"
migrations_dir = "../../d1/tts-db/migrations"

# 生产环境TTS数据库绑定
[[env.production.d1_databases]]
binding = "TTS_DB"
database_name = "senseword-tts-db"
database_id = "253bb3ab-6300-4d92-b0f7-e746ef8885b3"
```

### 数据库管理器配置
- Word DB Manager: `cloudflare/d1/word-db/wrangler.toml`
- TTS DB Manager: `cloudflare/d1/tts-db/wrangler.toml`

### 配置一致性要求
1. **数据库ID**: 必须与云端实际数据库ID完全一致
2. **绑定名称**: 在代码中使用的环境变量名称
3. **迁移路径**: 相对路径指向正确的迁移文件目录
4. **环境隔离**: 开发和生产环境使用相同的数据库配置

---

## 📝 示例数据

### word_definitions 表典型记录（包含 ttsId）
```sql
INSERT INTO word_definitions (
  sync_id, word, learningLanguage, scaffoldingLanguage,
  contentJson, coreDefinition, difficulty, frequency,
  relatedConcepts, partsOfSpeech, culturalRiskRegions,
  createdAt, updatedAt
) VALUES (
  1001,
  'serendipity',
  'en',
  'zh',
  '{"word":"serendipity","metadata":{"wordFrequency":"Low","relatedConcepts":["discovery","chance","fortune","luck","accident"],"culturalRiskRegions":[]},"content":{"difficulty":"C1","phoneticSymbols":[{"type":"bre","symbol":"/ˌserənˈdɪpəti/","ttsId":"1a2b3c4d5e6f7g8h9i0j1k2l"},{"type":"name","symbol":"/ˌserənˈdɪpəti/","ttsId":"2b3c4d5e6f7g8h9i0j1k2l3m"}],"coreDefinition":"意外发现有价值事物的能力；机缘巧合","contextualExplanation":{"nativeSpeakerIntent":"描述通过偶然机会发现有价值或令人愉快事物的现象","emotionalResonance":"带有积极和神奇的色彩，暗示生活中美好的意外","vividImagery":"如同在寻找一样东西时意外发现了更珍贵的宝藏","etymologicalEssence":"来自波斯童话《锡兰三王子》，指意外发现的智慧"},"usageExamples":[{"category":"描述幸运的意外发现","examples":[{"learningLanguage":"Finding that rare book in a dusty antique shop was a moment of pure serendipity.","scaffoldingLanguage":"在尘土飞扬的古董店里找到那本稀有的书，真是一次纯粹的机缘巧合。","ttsId":"3c4d5e6f7g8h9i0j1k2l3m4n","phraseBreakdown":[{"phrase":"pure serendipity","meaning":"纯粹的机缘巧合","ttsId":"4d5e6f7g8h9i0j1k2l3m4n5o"}]}]},{"category":"科学或学术上的意外发现","examples":[{"learningLanguage":"Penicillin was discovered by serendipity when Fleming noticed mold inhibiting bacterial growth.","scaffoldingLanguage":"盘尼西林是弗莱明在偶然注意到霉菌抑制细菌生长时意外发现的。","ttsId":"6f7g8h9i0j1k2l3m4n5o6p7q","phraseBreakdown":[{"phrase":"discovered by serendipity","meaning":"意外发现","ttsId":"7g8h9i0j1k2l3m4n5o6p7q8r"}]}]}],"collocations":[{"type":"形容词搭配","pattern":"adjective + serendipity","examples":[{"collocation":"pure serendipity","translation":"纯粹的机缘巧合"},{"collocation":"happy serendipity","translation":"幸运的巧合"}]}],"synonyms":[{"word":"chance","similarity":"medium","context":"偶然性方面"},{"word":"fortune","similarity":"medium","context":"幸运方面"}]}}',
  '意外发现有价值事物的能力；机缘巧合',
  'C1',
  'Low',
  '["discovery","chance","fortune","luck","accident"]',
  'noun',
  '[]',
  '2025-07-28T10:30:00.000Z',
  '2025-07-28T10:30:00.000Z'
);
```

### contentJson 完整结构示例（包含 ttsId）
```json
{
  "word": "serendipity",
  "metadata": {
    "wordFrequency": "Low",
    "relatedConcepts": ["discovery", "chance", "fortune", "luck", "accident"],
    "culturalRiskRegions": []
  },
  "content": {
    "difficulty": "C1",
    "phoneticSymbols": [
      {
        "type": "bre",
        "symbol": "/ˌserənˈdɪpəti/",
        "ttsId": "1a2b3c4d5e6f7g8h9i0j1k2l"
      },
      {
        "type": "name",
        "symbol": "/ˌserənˈdɪpəti/",
        "ttsId": "2b3c4d5e6f7g8h9i0j1k2l3m"
      }
    ],
    "coreDefinition": "意外发现有价值事物的能力；机缘巧合",
    "contextualExplanation": {
      "nativeSpeakerIntent": "描述通过偶然机会发现有价值或令人愉快事物的现象",
      "emotionalResonance": "带有积极和神奇的色彩，暗示生活中美好的意外",
      "vividImagery": "如同在寻找一样东西时意外发现了更珍贵的宝藏",
      "etymologicalEssence": "来自波斯童话《锡兰三王子》，指意外发现的智慧"
    },
    "usageExamples": [
      {
        "category": "描述幸运的意外发现",
        "examples": [
          {
            "learningLanguage": "Finding that rare book in a dusty antique shop was a moment of pure serendipity.",
            "scaffoldingLanguage": "在尘土飞扬的古董店里找到那本稀有的书，真是一次纯粹的机缘巧合。",
            "ttsId": "3c4d5e6f7g8h9i0j1k2l3m4n",
            "phraseBreakdown": [
              {
                "phrase": "pure serendipity",
                "meaning": "纯粹的机缘巧合",
                "ttsId": "4d5e6f7g8h9i0j1k2l3m4n5o"
              },
              {
                "phrase": "dusty antique shop",
                "meaning": "尘土飞扬的古董店",
                "ttsId": "5e6f7g8h9i0j1k2l3m4n5o6p"
              }
            ]
          }
        ]
      },
      {
        "category": "科学或学术上的意外发现",
        "examples": [
          {
            "learningLanguage": "Penicillin was discovered by serendipity when Fleming noticed mold inhibiting bacterial growth.",
            "scaffoldingLanguage": "盘尼西林是弗莱明在偶然注意到霉菌抑制细菌生长时意外发现的。",
            "ttsId": "6f7g8h9i0j1k2l3m4n5o6p7q",
            "phraseBreakdown": [
              {
                "phrase": "discovered by serendipity",
                "meaning": "意外发现",
                "ttsId": "7g8h9i0j1k2l3m4n5o6p7q8r"
              },
              {
                "phrase": "inhibiting bacterial growth",
                "meaning": "抑制细菌生长",
                "ttsId": "8h9i0j1k2l3m4n5o6p7q8r9s"
              }
            ]
          }
        ]
      }
    ],
    "collocations": [
      {
        "type": "形容词搭配",
        "pattern": "adjective + serendipity",
        "examples": [
          {
            "collocation": "pure serendipity",
            "translation": "纯粹的机缘巧合"
          },
          {
            "collocation": "happy serendipity",
            "translation": "幸运的巧合"
          }
        ]
      }
    ],
    "synonyms": [
      {
        "word": "chance",
        "similarity": "medium",
        "context": "偶然性方面"
      },
      {
        "word": "fortune",
        "similarity": "medium",
        "context": "幸运方面"
      }
    ]
  }
}
```

### ttsId 字段说明

#### ttsId 的作用
- **唯一标识**: 每个 TTS 音频内容的 24 位唯一哈希标识符
- **音频映射**: 连接 contentJson 中的文本内容与 TTS 数据库中的音频资产
- **类型覆盖**: 涵盖音标发音、例句朗读、短语分解等所有音频类型

#### ttsId 的分布位置
| 位置 | 字段路径 | TTS 类型 | 示例 |
|------|----------|----------|------|
| 音标 | `content.phoneticSymbols[].ttsId` | `phonetic_bre`, `phonetic_name` | "1a2b3c4d5e6f7g8h9i0j1k2l" |
| 例句 | `content.usageExamples[].examples[].ttsId` | `example_sentence` | "3c4d5e6f7g8h9i0j1k2l3m4n" |
| 短语 | `content.usageExamples[].examples[].phraseBreakdown[].ttsId` | `phrase_breakdown` | "4d5e6f7g8h9i0j1k2l3m4n5o" |

#### ttsId 生成规则
- **长度**: 固定 24 位十六进制字符
- **算法**: 基于文本内容、语言、类型的哈希计算
- **唯一性**: 相同文本内容在相同语言和类型下生成相同 ttsId
- **标准化**: 音标类型使用小写 `bre`、`name`、`ipa`

### 字段提取示例
基于上述 contentJson，各字段的提取结果：

| 字段名 | 提取值 | 提取路径 |
|--------|--------|----------|
| `coreDefinition` | "意外发现有价值事物的能力；机缘巧合" | `content.coreDefinition` |
| `difficulty` | "C1" | `content.difficulty` |
| `frequency` | "Low" | `metadata.wordFrequency` |
| `relatedConcepts` | `["discovery","chance","fortune","luck","accident"]` | `metadata.relatedConcepts` |
| `partsOfSpeech` | "noun" | partsOfSpeech |
| `culturalRiskRegions` | `[]` | `culturalRiskRegions` |

---

## 🛠️ 最佳实践

### 配置变更流程
1. **统一修改**: 修改数据库配置时，同步更新所有相关的 wrangler.toml 文件
2. **验证一致性**: 使用 `wrangler d1 list` 验证数据库ID的正确性
3. **测试部署**: 在开发环境验证配置变更后再部署到生产环境

### 数据库迁移指南
```bash
# 查看当前数据库状态
npx wrangler d1 execute senseword-word-db --command="SELECT name FROM sqlite_master WHERE type='table';"

# 检查迁移状态
npx wrangler d1 execute senseword-word-db --command="SELECT * FROM d1_migrations ORDER BY applied_at DESC;"

# 执行迁移
npx wrangler d1 migrations apply senseword-word-db

# 验证表结构
npx wrangler d1 execute senseword-word-db --command="PRAGMA table_info(word_definitions);"
```

### 性能监控建议
```sql
-- 数据统计
SELECT 
  scaffoldingLanguage,
  COUNT(*) as word_count 
FROM word_definitions 
GROUP BY scaffoldingLanguage;

-- 难度分布
SELECT 
  difficulty,
  COUNT(*) as count 
FROM word_definitions 
GROUP BY difficulty 
ORDER BY count DESC;

-- 索引使用情况
EXPLAIN QUERY PLAN 
SELECT * FROM word_definitions 
WHERE word = 'progressive' AND scaffoldingLanguage = 'zh';
```

---

## 🚨 故障排查

### 常见配置问题

#### 1. 数据库连接失败
**症状**: Worker 启动时报错 "Database binding not found"
**原因**: wrangler.toml 中的 binding 名称与代码中使用的不一致
**解决方案**: 检查并统一 binding 名称

#### 2. 迁移文件路径错误
**症状**: 执行迁移时提示 "Migration directory not found"
**原因**: migrations_dir 路径配置错误
**解决方案**: 验证相对路径的正确性

#### 3. 数据库ID不匹配
**症状**: 部署后无法访问数据
**原因**: database_id 与实际云端数据库不符
**解决方案**: 使用 `wrangler d1 list` 获取正确的数据库ID

### 调试命令
```bash
# 列出所有D1数据库
wrangler d1 list

# 查看数据库详细信息
wrangler d1 info senseword-word-db

# 测试数据库连接
wrangler d1 execute senseword-word-db --command="SELECT 1;"

# 查看表结构
wrangler d1 execute senseword-word-db --command="PRAGMA table_info(word_definitions);"
```

---

## 📋 版本控制信息

- **文档版本**: v1.0
- **创建时间**: 2025-07-28
- **最后更新**: 2025-07-28
- **维护责任人**: KDD 开发团队
- **关联任务**: KDD-002
- **数据库Schema版本**: v1.0 (基于2025-07-27重构)
