# SenseWord - 核心用户故事 (V4.0 - 精炼版)
## 引言
本用户故事旨在通过一个连贯的叙事，描绘用户从初次接触SenseWord到深度使用、并最终成为付费会员的全过程。它定义了产品的核心交互、情感体验和价值传递路径，是所有UI/UX设计和功能开发的最高指导。

## Story01｜初见与信任的建立
1. 用户第一次打开产品时，会进入到onbording页面
    2. 屏幕中央浮现出 Senseword 产品品牌的图标，带有轻微的呼吸动画。
    3. 图标下方，依次优雅地浮现出品牌名 Senseword。
    4. 再往下是Senseword的 Slogan：“不止于词，抵达心语。"（根据用户系统语言，自动显示为对应的德语或日语）。
        1. 德语是 Worte erfassen, das Wesen spüren. 
        2. 日语是：言葉を超えて、意味の核心へ。
    5. 中间靠下位置：在背景中，一张张代表不同内容维度（意图、情绪、想象...）的“磁性吸附卡片"，每行有多个，缓慢地呈现向左或者向右移动，呈现出极其丰富的感觉。
    3. 屏幕靠下位置，有两个无密码登录的图标
        1. 一个设计优雅的"sign in with apple"圆角矩形按钮，采用标准白色风格Apple官方组件
        2. 一个浅灰色磨砂玻璃的 sign in with Google 使用彩色的 Google 图标
    4. 屏幕最下方，是两行极小的、半透明的灰色文字链接：“隐私政策" 和 “用户协议"。

## Story02｜由“每日推荐"开启的探索之旅

* 场景: 在非首次打开App时，SenseWord以“每日推荐"作为最自然的引导，邀请我开始当天的学习。
* 故事:
    1.  登录后，主界面直接为我呈现了今天的“每日一词"，例如 `epiphany`。它被置于“聚光灯"下，等待我的探索。
    2.  这个设计完美地解决了“打开App后不知道该做什么"的问题。它没有给我一个需要选择的列表，而是提供了一个充满惊喜的、唯一的起点。
    3.  我知道，这个“每日一词"是由一个智能算法，从一个高质量的动态候选池中为全球用户挑选的，这让我对它的质量充满期待。
    4.  我开始向上滑动，从这个“每日推荐"的“种子"出发，无缝地进入了SenseWord的核心体验——无限内容流。

## Story03｜锚点与舞台的初见

我登录后，主学习页面优雅地展开。我立刻注意到，这个界面被巧妙地分为了两个核心区域：

1.  顶部的“单词锚点" (The Word Anchor)：
    * 这个区域像一个固定的仪表盘，始终停留在屏幕的最上方。
    * 它清晰地展示着当前核心单词（例如 `serendipity`）、它的音标`/ˌser.ənˈdɪp.ə.ti/`，以及一句最核心的释义："意外发现珍宝的运气"。
    * 在单词旁边，有一个扬声器图标。我注意到图标旁出现了一个微小的绿色圆点，这给了我一个清晰的信号：这个单词所有的相关音频资源（包括它本身、所有例句、所有短语的发音）都已经在后台异步加载完毕，我可以随时点击，享受无延迟的即时播放。
    2.  收藏与反馈: 在深入阅读前，我看到了锚点区域右侧的三个小图标：
        * 收藏 (⭐): 我判断`serendipity`这个词对我很重要，便轻点星号。图标被点亮，我知道它已被妥善地加入“我的收藏"中，整个过程没有打断我的阅读心流。
        * 赞/踩 (👍/👎): 在我读完所有解析后，如果觉得内容极具启发性，我会点“赞"，我知道这会为这条内容的质量加分；反之亦然。这是我作为社区一员，帮助系统自我优化的方式。

2.  下方的“内容舞台" (The Content Stage)：
    * 紧接着“单词锚点"下方，是一个可垂直滚动的区域，这里才是真正的好戏上演的地方。
    * 初次加载时，只有第一张带有丰富解释的卡片——“意图"——完全处于“聚光灯"下，清晰明亮。而它的下方，是一张标记着情绪的卡片，但是它被稍稍调暗，仿佛在安静地候场。
## Story04｜Apple Music 风格的缩放模糊滚动心流
我开始向上滑动，探索`serendipity`这个词的深层含义，体验到了类似 Apple Music 歌词滚动的精致效果。

1.  **Apple Music 风格的视觉层次**：
    * 屏幕中央的"意图"卡片以 100% 的大小和清晰度完美呈现，左上角有一个蓝色的标签，写着"意图"
    * 卡片顶部的引导问题："当母语者说出这个词时，他们真正想表达什么？"
    * 核心解析内容清晰可见：
      - "母语者使用这个词时，想表达的不仅仅是运气好，更强调'意外性'和'愉快性'"
      - "通常是在没有刻意寻找的情况下发生的美好发现"
      - "与简单的'luck'不同，serendipity带有一种智慧的偶然感"
    * 在"意图"卡片的上方和下方，我能看到其他内容卡片的轮廓：上方是"核心释义"卡片，下方是"情绪"卡片，但它们都被缩小到约 70% 的大小，并带有轻微的模糊效果，就像 Apple Music 中非当前播放歌词的样子

2.  **流畅的缩放模糊过渡**：
    * 当我用手指向上滑动时，所有卡片开始流畅地移动。"意图"卡片逐渐向上移动并缩小模糊，而"情绪"卡片则从下方逐渐放大清晰，移向屏幕中央
    * 这个过渡过程完全是实时的、连续的，没有任何跳跃感。我能清楚地看到每张卡片的大小和清晰度是如何平滑变化的
    * 当我松开手指时，系统会智能地判断哪张卡片最接近屏幕中央，然后伴随着一声清脆的触觉反馈，精准地将那张卡片"吸附"到中央位置，同时完成最终的缩放和清晰度调整

3.  **沉浸式的聚焦体验**：
    * 现在，"情绪" (Emotion) 这张橙色标签的卡片完美地占据了屏幕中央，以 100% 的大小和清晰度展示着这个词所附带的情感共鸣
    * 我能清楚地阅读其中的内容，而上下的其他卡片则以约 70% 的大小和轻微模糊的状态"候场"，既不会干扰我的阅读，又能让我感知到内容的连续性
    * 我继续向上滑动，依次体验了"想象"(Vivid Imagery)、"词源"(Etymological Essence)等所有维度的内容卡片。每一次滑动都像在操作一个制作精良的、具有物理质感的数字卷轴

4.  **精致的视觉层次设计**：
    * 屏幕中央的当前卡片：100% 大小，完全清晰，充分的对比度和饱和度
    * 紧邻的上下卡片：约 85% 大小，轻微模糊（blur radius: 1-2），透明度约 80%
    * 更远的卡片：约 70% 大小，中等模糊（blur radius: 3-4），透明度约 50%
    * 最远的卡片：约 60% 大小，较强模糊（blur radius: 5），透明度约 30%
    * 这种渐进式的视觉层次让我始终能感知到内容的整体结构，同时将注意力完美地聚焦在当前正在阅读的内容上

5.  **技术实现的无感体验**：
    * 整个滚动过程使用了类似 Apple Music 的 GeometryReader 技术，实时计算每个卡片与屏幕中心的距离
    * 根据距离动态调整缩放比例、模糊程度和透明度，创造出平滑的视觉过渡效果
    * 磁性吸附算法确保每次滚动结束时，都有一张卡片完美地居中显示，避免了内容被"切断"的尴尬情况
    * 这种技术实现完全隐藏在用户体验背后，我只感受到流畅自然的交互，而不会察觉到复杂的技术细节


## Story05｜例句的“水平舞台"深度交互
当我滚动到“例句 (Usage Examples)"这张特殊的卡片时，交互的维度变得更加丰富：
1.  完整例句展示：卡片首先展示了例句的分类、原文和翻译，并自动播放了音频。
2.  水平探索的暗示：我注意到，在句子的右侧，有一个不断呼吸的动画效果，暗示我可以进行水平探索。
3.  进入“短语分解"模式：我向左滑动，视图无缝地切换到了“短语分解 (Phrase Breakdown)"模式。
    * 第一个短语被高亮，其他部分变灰，其对应的释义和音频被单独呈现。
    * 我可以继续向左滑动，依次学习每一个短语的细节，每一次切换都伴随着精准的触觉反馈和独立的音频播放。
4.  返回与巩固：当我完成所有短语的学习并再次向左滑动后，视图会恢复到完整的例句，并重播一遍完整的音频，以此来巩固我的整体理解。此时，我才可以继续向上滚动，探索下一个内容板块。

### 具体例子：以`serendipity`的例句为例
当我学习`serendipity`时，看到了这个例句：
- **完整例句**："Finding that rare book in a dusty antique shop was a moment of pure serendipity."
- **翻译**："在尘土飞扬的古董店里找到那本稀有的书，真是一次纯粹的serendipity。"

**短语分解过程**：
1. 第一次左滑："Finding that rare book" → "找到那本稀有的书"
2. 第二次左滑："in a dusty antique shop" → "在一家尘土飞扬的古董店里"
3. 第三次左滑："was a moment of pure serendipity" → "是一次纯粹的serendipity"

通过这种分解，我不仅理解了整句话，还学会了"pure serendipity"这个地道搭配和"dusty antique shop"这种描述性表达。

## Story06｜探索的终点与新旅程的起点：从`serendipity`到`discovery`的知识连接

当我依次浏览完“场景"、“用法注意"、“同义词"等所有深度解析卡片后，我来到了当前单词探索之旅的终点。

1.  下一个单词的暗示：在最后一张卡片的底部，我看到了下一个相关单词（例如`reform`）的卡片，一部分出现在了屏幕的下15%，能够看到单词，但是看不到完整的解释，“冒了个头"。
2.  无缝进入新世界：我继续向上滑动，整个屏幕发生了一次流畅的转场。顶部的“单词锚点"区域，内容从`progressive`更新为了`reform`，而下方的“内容舞台"也重置为`reform`的第一张“意图"卡片。
3. 我明白了，这才是“无限内容流"的真正含义。它首先是一场对单个单词的、垂直的深度探索，而在这场探索的终点，又无缝地衔接上了下一场相关探索的起点。顶部的“单词锚点"则像船上的罗盘，永远清晰地告诉我，我当前探索的是哪一片知识大陆。

### 具体例子：从`serendipity`到相关概念的探索链
当我完成对`serendipity`的深度学习后，我看到了相关概念的连接：
- **下一个词**：`discovery` - 我发现这两个词在科学发现的语境中经常一起使用
- **词汇家族**：`chance`, `fortune`, `coincidence`, `accident` - 构成了一个关于"偶然性"的完整知识网络
- **知识连接**：通过这种方式，我不仅学会了一个词，还理解了一个概念群，建立了英语思维中关于"发现"和"偶然"的完整认知框架

## Story07｜从使用者到贡献者：以`serendipity`为例的完整搜索体验
1. **即时搜索需求**：在阅读一篇关于科学发现的文章时，我遇到了"The discovery was a result of pure serendipity"这句话。虽然大概知道是"偶然"的意思，但想了解更精确的含义。

2. **优雅的搜索入口**：我立刻打开SenseWord，向下滑动进入搜索模式，界面带有高斯模糊效果，非常优雅。

3. **智能的模糊匹配**：我输入`seren...`，本地预存的词汇索引立即显示了匹配结果：
   - `serendipity` - 意外发现珍宝的运气
   - `serene` - 宁静的，平静的
   - `serenade` - 小夜曲

4. **AI实时生成体验**：我点击`serendipity`，由于这个词在数据库中不存在，App显示"正在为您生成深度解析..."，几秒钟后呈现了完整的内容：
   - 核心定义：意外发现珍宝的运气
   - 发音：`/ˌser.ənˈdɪp.ə.ti/`
   - 母语者意图：强调"意外性"和"愉快性"
   - 情感共鸣：积极、惊喜和一点点魔法的感觉
   - 词源故事：1754年英国作家创造，源于波斯童话

5. **成为贡献者的满足感**：我意识到，我的这次查询为整个用户社区贡献了一个新的、高质量的知识资产。之后所有查询这个词的用户都能受益于我的这次搜索。我从使用者变成了贡献者。

## Story08｜个人成长与价值实现
1. 无焦虑的成长：我发现我不再关注“今天学了多少个"或“总进度是多少"这类数字。因为单词流是无限的，我反而没有了压力。我的目标很简单：享受当下的探索，沉浸于与每一个单词的“心语"对话中。我只关心对某一个单词本身的学习次数，这让我有“我很熟悉这个词"的真实成就感。
2. 订阅转化 (Subscription)：作为免费用户，在我在今天搜索了5个单词后，还有当我在无限内容流中连续探索了到第6个单词时，我看到了这个单词显示的是我特别想继续了解的一个词，elegant，但是我只能看到这个单词，无法继续向上滑动，elegant 这个单词卡片被固定在了底部，一旦被拉上来就会强制tan。
3. 一个设计精美的半透明面板从底部弹出，提示我“升级到 Premium，解锁无限探索"。考虑到每月仅$1.99的低价和已经获得的绝佳体验，我毫不犹豫地完成了订阅。
4. 从产品到习惯：SenseWord已经不再是一个我只在遇到生词时才想起的“词典工具"，它成为了我每天都会打开，用来进行碎片化时间探索和发现的“知识流媒体"。

## Story09｜搜索 - 从“即时需求"到“无尽探索"

故事主角：李明，一位正在阅读英文技术博客的开发者。
1.  遇到生词，唤醒工具：李明在阅读一篇关于系统架构的文章时，遇到了一个陌生的词：`idempotent`。他有些不确定其在技术语境下的精确含义。他立刻想起了SenseWord，这个他信赖的“深度单词解析器"。
2.  优雅地进入搜索：他打开SenseWord，主界面依然是那个他熟悉的“聚光灯无限内容流"。他自然地用手指向下滑动屏幕，顶部的搜索按钮随着他的手势逐渐变大，当达到一定阈值后，整个界面平滑地、带有高斯模糊效果地进入了搜索模式。这个交互让他感觉非常自然，像是拉开了一张半透明的幕布。
3.  智能的即时反馈：搜索框的占位词提示着他：“使用搜索用深思语境练单词"。他开始输入`idem...`，输入框下方立刻以列表形式，实时地从本地预存的词汇索引中展示出模糊匹配的结果，例如`idempotent`、`identity`等，每个词后面都附有其核心释义。
4.  LPLC原则的体现：他点击了`idempotent`。App首先检查本地缓存，发现没有。此时，并且只有在此时（Lazy Produce），App才向后端`api-worker`发起了一个请求。`api-worker`发现这个词在D1数据库中也不存在，于是立刻调用AI引擎，实时生成了一份关于`idempotent`的、包含德语“心语"解析（因为李明的母语是德语）的全新JSON，并将其存入数据库。
5.  从“工具"到“媒体"的无缝转换：搜索面板平滑收起，主界面的“单词锚点"立刻更新为`idempotent`及其核心信息。下方的“内容舞台"则展示出对其“心语"的深度解析。李明在满足了自己“查词"这个即时需求后，自然地向上滑动，立刻被`idempotent`的关联概念（例如 `stateless`, `retry`, `distributed system`）所吸引，无缝地进入了一场全新的“兴趣驱动无限探索之旅"。
哲学思考：搜索功能完美体现了LPLC原则和产品从“工具"到“媒体"的升维。它以最低的成本（只在需要时生产）满足了用户最高频的需求（查词），然后通过与“无限内容流"的无缝衔接，将一次性的工具使用，转化为一次长期的、充满价值的学习和探索体验。

## Story10｜我与付费墙的"心痒"时刻

场景: 我已经连续探索了5个单词，正沉浸在知识的海洋中，突然遇到了付费墙。

我的体验:
1. 从"痛点"到"痒点"的转变：
   - 当我遇到生词`idempotent`时，我的"痛点"是"我需要立刻知道这个词的意思"。SenseWord完全免费地满足了我的这个核心需求，我看到了完整的深度解析，感觉很满意。
   - 但当我沉浸在无限内容流中，连续探索了`progressive` → `reform` → `innovative` → `dynamic` → `adaptive`这5个相关词后，我看到了下一个诱人的词`elegant`，却无法继续时，我感受到的不是"被拒绝"，而是一种"心痒"的感觉。

2. 我面临的选择：
   - 路径A（免费但费力）：我可以退出内容流，回到搜索页面，手动输入`elegant`，然后免费查看它的完整解析。SenseWord没有阻止我学习任何我想学的东西。
   - 路径B（付费但省力）：我也可以选择支付$1.99，然后什么都不用做，只需要继续向上滑动，就能立刻、无缝地继续我的探索之旅。

3. 我的内心独白：
   - "唉，每次都要退出去再搜索好麻烦...这个无限流的感觉实在太棒了，我愿意花一杯咖啡的钱，来购买这种'省心省力'的、不被打断的'心流'体验。"
   - 我意识到，我付费的理由不是"为了解锁我无法访问的内容"，而是"为了购买一种更优越的体验"。

4. 我感受到的心理机制：
   - 好奇心缺口：`elegant`这个词"冒了个头"，我强烈想知道它的深层含义
   - 未完成感：我的探索之旅被打断了，这种"就差一点就看完了"的感觉让我念念不忘
   - 不愿失去：我不想失去刚才那种流畅、沉浸的探索心流
   - 惊喜期待：我永远不知道下一个词会带来怎样的"卧槽时刻"
   - 省力需求：我想要更省心、更省力的学习方式

我的感受:
- 我不会感受到"我是被强迫的"，而是"出于自己的选择，我想更省心，更省力"
- 这个付费墙让我感觉被尊重，而不是被勒索
- 我知道SenseWord在售卖的是"便利性"和"心流体验"，而不是内容本身

## Story11｜我发现SenseWord不只是工具，更是我的知识伙伴

场景: 我从一个偶尔查词的用户，逐渐变成了SenseWord的深度用户，体验到了从"工具"到"媒体"的价值升维。

我的使用演变:

1. 初期：我把它当作查词工具：
   - 我在阅读时遇到生词，打开SenseWord搜索，看完解释就关闭
   - 我发现它的解析比其他词典更深刻，有"心语"级的理解
   - 我开始信任这个产品，把它设为默认词典

2. 转变：我发现了无限探索的魅力：
   - 有一次查完`progressive`后，我好奇地向上滑动，发现了`reform`
   - 我被这种"一个词引出另一个词"的连锁探索深深吸引
   - 我意识到，我不再是在"查词"，而是在"探索知识"

3. 深度使用：我体验到了独特的价值：
   - 我的搜索在帮助产品进化：每次我搜索一个新词，我知道我在为整个用户社区贡献新的内容资产
   - 我享受零成本的深度体验：我可以无限次搜索任何词，获得完整的深度解析，这比传统词典的体验好太多
   - 我感受到了被尊重：产品没有用广告打扰我，没有强迫我付费才能查词

4. 习惯养成：从工具变成媒体：
   - 我不再只在遇到生词时才想起SenseWord
   - 我开始每天打开它，从"每日一词"开始我的探索之旅
   - 它成为了我碎片化时间学习的最佳伴侣，就像我刷社交媒体一样自然

我感受到的价值递进:
1. 工具价值（免费）：解决我"需要知道这个词的意思"的痛点
2. 媒体价值（付费）：满足我"想持续探索相关知识"的渴望
3. 习惯价值：从偶尔使用的工具变成每日必备的知识流媒体

我与其他产品的对比感受:
- 传统词典：给我静态释义，用完就走，没有惊喜
- SenseWord：给我动态探索，越用越想用，总有新发现
- 我意识到竞争已经从"哪个词典更准确"升级到了"哪个产品的探索体验更好"

我对这种商业模式的感受:
- 我感觉被尊重：产品不是在"勒索"我，而是在"吸引"我
- 我愿意付费：不是因为被强迫，而是因为我真的想要更好的体验
- 我成为了忠实用户：这种基于正面情绪的关系让我更愿意长期使用

## Story12｜我理解了付费规则，感觉公平且清晰

场景: 作为SenseWord的用户，我需要了解付费规则，并感受到这些规则是公平、清晰且合理的。

我看到的规则:

1. 我的使用限制: 作为免费用户，我每天有5次探索新单词的机会
2. 计算方式: 当我在内容流中探索一个今天还没看过的新单词时，我的额度就会减少1次
3. 重置时间: 每天零点，我的额度会自动恢复到5次
4. 重复查看: 如果我想再看一遍今天已经探索过的单词，不会消耗我的额度

我看到的界面提示:
- 我能清楚地看到"今日剩余探索次数：3/5"，让我知道还能探索几个词
- 当我用完额度时，看到的是"5次免费探索 vs 无限♾️探索"的对比
- 付费墙的文案很友好："您今天的免费探索已用完。升级到PRO，享受无限探索。"

我对这些设计的理解和感受:

1. 为什么是每日总额而不是每次使用：
   - 我理解这样设计是为了防止我通过重启App来"作弊"
   - 这让我养成了每天回来的习惯，就像每日签到一样
   - 当我真的很想继续探索时，付费的冲动最强烈
   - 从技术角度看，这种计算方式很简单明了

2. 为什么是5次而不是更多：
   - 5次让我充分体验到了无限内容流的魅力
   - 但总是在我想要完整探索一个主题时"差一点"，让我有点"意犹未尽"
   - 我明白如果给我10次，我可能就满足了，不会想付费
   - "5 vs ♾️"这个对比很有冲击力，让我一眼就明白付费的价值

3. 为什么不是每个单词单独限制：
   - 我很庆幸不是这样，因为那样会打断我的探索心流
   - 我喜欢这种连续探索的感觉，不想在每个词前都要做决策
   - 这样的设计保持了产品体验的完整性

我感受到的心理机制:
- 我不想浪费：这5次机会感觉是"给我的礼物"，我不想随便浪费
- 我害怕失去：当我建立起探索的心流后，我不想被打断
- 我有未完成感："就差一点就看完了"的感觉让我念念不忘
- 我想要轻松：我愿意为"省心省力"的便利体验付费

我的整体感受:
- 我觉得这些规则很公平，没有被"坑"的感觉
- 我理解产品需要盈利，这种方式让我感觉被尊重
- 我相信即使我不付费，也能长期使用这个产品
- 我愿意推荐给朋友，因为我觉得这是一个值得信赖的产品

## Story13｜张静的“沉浸式复习"

1. 张静在设置页面点击了“我的收藏"。
2. 界面平滑地过渡到她熟悉的“聚光灯无限内容流"视图。顶部的“单词锚点"和下方的“内容舞台"与主界面别无二致。
3. 但她立刻发现，这个内容流的“播放列表"，现在完全是由她自己收藏的那些单词组成的。她看到了上周收藏的`ubiquitous`，愉快地再次回顾了它的“心语"解析。
4. 在回顾完之后，她好奇心起，向上滑动，视图无缝地切换到了`ubiquitous`的一个关联词`pervasive`上。她心想：“原来这两个词可以这样对比着记，太棒了。"
5. 这次复习，不仅让她巩固了旧知识，还意外地获得了新知。

## Story14｜从“免费探索"到“价值认同"

* 场景: 作为免费用户，我的探索受到轻量的限制，这引导我认识到产品的核心价值并转化为付费会员。
* 故事:
    1.  触发付费墙: 当我在今天的无限内容流中，连续探索到第6个新单词时，流动的体验停止了。最后一张卡片`elegant`被固定在了底部，我能看到它，但无法上滑来查看它的完整解析。
    2.  清晰的价值主张: 同时，一个设计精美的半透明面板从底部弹出，提示我：“升级到 Premium，解锁无限探索。" 这个设计没有打断我，而是给了我一个明确的选择。
    3.  轻松的决策: 面对已经获得的绝佳体验，以及`$1.99/月`这个极具吸引力的价格，我几乎没有犹豫，点击按钮，通过App Store内购，轻松地完成了订阅。我知道我付费购买的，是通往这个无限知识世界的“永久门票"。
    4.  成为习惯: 从此，SenseWord不再仅仅是一个工具，它成为了我每日必不可少的“知识流媒体"，是我进行碎片化时间学习和探索的最佳伴侣。


## Story15｜我需要一个简洁而贴心的设置页面

**场景**: 作为SenseWord的用户，我希望有一个设置页面来管理我的账户、调整体验偏好，但我不希望它过于复杂。

**我对设置页面的期望**:

## 第一部分：我的账户管理
1. **查看我的账户信息**：
   - 我想看到我通过Apple/Google登录的昵称和邮箱
   - 我不需要编辑这些信息，因为SenseWord不是社交产品，保持简洁就好
   - 这让我确认自己登录的是正确的账户

2. **管理我的订阅**：
   - 我想清楚地看到我当前是免费用户还是PRO会员，以及到期时间
   - 我希望有一个按钮能直接跳转到App Store的订阅管理页面
   - 我不希望在App内处理复杂的支付流程，交给Apple处理更安全

## 第二部分：个性化我的体验
1. **音频播放控制**：
   - 我希望能选择是否自动播放音频
   - 有时我在安静的办公室，不希望有声音；有时我在家，希望听到发音
   - 这个选择权让我在不同场景下都能舒适地使用

2. **触感反馈设置**：
   - 我想控制"磁性吸附"和滑动时的震动反馈
   - 有些人喜欢这种触觉确认，有些人觉得干扰
   - 我希望能根据自己的偏好来调整

3. **每日提醒通知**：
   - 我想选择是否接收"每日一词"的推送通知
   - 这是一个很好的学习提醒，但我希望能自己决定是否需要
   - 我不希望被强迫接收通知，这让我感觉被尊重

## 第三部分：我的学习资产
1. **我的收藏管理**：
   - 我想有一个入口能快速访问我收藏的所有单词
   - 我希望这个体验和主界面一样流畅，用同样的"聚光灯无限内容流"
   - 我的收藏是我最宝贵的学习资产，应该被重视

2. **缓存清理工具**：
   - 当App占用空间太大时，我希望能一键清除缓存
   - 当遇到数据同步问题时，我希望能重置而不用卸载重装
   - 这个功能我可能不常用，但关键时刻很有用

## 第四部分：产品信息和支持
1. **了解产品**：
   - 我想了解SenseWord的产品理念和当前版本
   - 我想知道这个产品背后的故事

2. **给出反馈**：
   - 我想能方便地在App Store给产品评分
   - 我想有渠道反馈问题或建议
   - 我想能查看隐私政策和用户协议

## 第五部分：账户操作
1. **退出登录**：
   - 有时我需要切换账户或在共用设备上使用
   - 我希望这个操作简单明确

2. **注销账户**：
   - 虽然我希望永远不需要用到，但我知道这是Apple要求的
   - 我希望这个操作有足够的确认步骤，避免误操作

**我的整体感受**:
- 我希望设置页面体现SenseWord的简洁哲学，不要有太多复杂选项
- 我希望每个设置都有明确的价值，不是为了功能而功能
- 我希望感受到产品对我个人偏好的尊重
- 我希望重要的功能（如订阅管理）能无缝对接到系统级服务

**我不希望看到的**:
- 复杂的外观定制选项
- 过多的社交功能设置
- 混乱的功能分类
- 强制性的设置要求

---
# 功能清单
## 第一部分：账户与订阅管理 (Account & Subscription)
这是最基础也是最必要的部分，它关乎用户的核心权益。

1.  账户信息 (Account Info)
    * 功能：简单地展示用户通过Apple/Google登录的昵称和邮箱。
    * 哲学：只读，不可编辑。这再次强调了SenseWord非社交的产品属性，避免了引入自定义头像、昵称等增加系统复杂度的功能。

2.  订阅管理 (Manage Subscription)
    * 功能：清晰地显示用户当前的订阅状态（免费版 / PRO会员）和到期日。提供一个按钮，点击后直接跳转到iOS系统的App Store订阅管理页面，让用户可以方便地管理自己的订阅。
    * 哲学：将专业的事务（支付与订阅管理）完全委托给平台（Apple），保持App本身的轻量。

## 第二部分：核心体验微调 (Experience Fine-tuning)
这部分提供的不是“外观"定制，而是对核心“感官"体验的微调。

1.  音频自动播放 (Audio Auto-play)
    * 功能：提供一个开关，允许用户开启或关闭进入卡片时自动播放音频的功能。
    * 哲学：尊重不同用户在不同场景下的偏好。有些用户可能在安静的办公室使用，不希望有声音；有些用户则希望获得沉浸式的听说体验。这是一个有意义的选择。

2.  每日一词通知 (Word of the Day Notification)
    * 功能：提供一个开关，允许用户选择是否每天接收一个关于“每日一词"的推送通知。
    * 哲学：这是一个极其重要的、低成本的用户召回（Re-engagement）手段。我们不强迫用户，而是给予他们选择权，让他们可以决定是否希望被我们“温柔地提醒"。

3. 使用语言
   * 功能：默认为用户设备当前的语言，可以手动调整指定范围的语言，使用语言更改设备界面语言和发起单词查询的参数，从而更改讲解单词内容的语言。

## 第三部分：学习与数据 (Learning & Data)
这部分关乎用户个人学习资产的管理。

2.  清除本地缓存 (Clear Local Cache)
    * 功能：提供一个按钮，用于清除App在本地缓存的所有单词深度解析数据和搜索索引。
    * 哲学：这是一个面向少数“高级用户"的实用工具。当用户感觉App占用空间过大，或者遇到一些罕见的数据同步问题时，这个功能可以让他们“一键重置"，而无需卸载重装App。它不常用，但在关键时刻非常有用。

## 第四部分：关于与支持 (About & Support)
这是所有成熟App的标准配置，体现了产品的专业度和对用户的责任感。

1.  关于SenseWord：简单介绍产品哲学、版本号。
2.  评价我们：引导用户跳转到App Store进行评分。
3.  反馈与帮助：提供您的联系邮箱或反馈渠道。
4.  隐私政策 & 用户协议：链接到对应的法律文本页面。

## 第五部分：账户操作 (Account Actions)
这是敏感但必要的操作，通常放在设置页面的最底部。

1.  退出登录 (Sign Out)
2.  注销账户 (Delete Account) (苹果审核的强制要求)

---

## Story16｜我的"serendipity时刻"：从查词到发现知识宝藏

**场景**: 我在阅读一篇关于创新的英文文章时，遇到了`serendipity`这个词，这次查词体验让我真正理解了SenseWord的独特价值。

**我的完整体验**:

### 第一阶段：即时需求的满足
1. **遇到生词的困惑**：我在文章中看到"The discovery was a result of pure serendipity"，虽然大概知道是"偶然"的意思，但想了解更精确的含义。

2. **优雅的搜索体验**：我打开SenseWord，向下滑动进入搜索模式。输入`seren...`时，搜索建议立即显示了`serendipity`，旁边还有简短的核心释义。

3. **AI实时生成的惊喜**：我点击后，App显示"正在为您生成深度解析..."，几秒钟后，一份完整的、包含中文"心语"解析的内容就呈现在我面前。我意识到，我的这次查询为整个用户社区贡献了一个新的知识资产。

### 第二阶段：深度理解的获得
4. **单词锚点的清晰展示**：
   - 单词：`serendipity`
   - 发音：`/ˌser.ənˈdɪp.ə.ti/` (英式) 和 `/ˌser.ənˈdɪp.ə.ti/` (美式)
   - 核心定义：意外发现珍宝的运气
   - 音频状态：绿色圆点显示音频已准备就绪

5. **"意图"卡片的深度解析**：我了解到母语者使用这个词时，想表达的不仅仅是运气好，更强调"意外性"和"愉快性"，而且通常是在没有刻意寻找的情况下发生的。

6. **"情绪"卡片的共鸣**：我读到这个词带有"积极、惊喜和一点点魔法的感觉"，让人联想到"柳暗花明又一村"的惊喜感。我突然明白了为什么这个词在英文中如此受欢迎。

### 第三阶段：生动场景的想象
7. **"想象"卡片的画面感**：我看到了两个生动的例子：
   - 在雨中寻找伞，却在旧书店发现绝版书的惊喜
   - 旅行中迷路，却意外走进风景如画小镇的美好

   这些画面让我完全理解了`serendipity`的精髓。

8. **"词源"卡片的文化洞察**：我惊讶地发现这个词是1754年由英国作家创造的，灵感来源于《serendip的三个王子》的波斯童话。这种历史背景让这个词变得更加有趣。

### 第四阶段：实用例句的掌握
9. **例句的分类学习**：我看到了两个类别的例句：
   - 描述幸运的意外发现："Finding that rare book in a dusty antique shop was a moment of pure serendipity."
   - 科学学术发现："Penicillin was discovered by serendipity when Fleming noticed mold inhibiting bacterial growth."

10. **短语分解的细致理解**：当我向左滑动第一个例句时，每个短语都被单独解释：
    - "Finding that rare book" → "找到那本稀有的书"
    - "in a dusty antique shop" → "在一家尘土飞扬的古董店里"
    - "was a moment of pure serendipity" → "是一次纯粹的serendipity"

    每个短语都有独立的音频播放，让我掌握了地道的发音和用法。

### 第五阶段：使用场景的拓展
11. **四个使用场景的启发**：
    - 旅行与探索：偏离计划发现美食、景点
    - 学术与科研：实验中的意外发现
    - 日常生活：购物、社交中的不期而遇
    - 人际关系：社交活动中认识重要的人

12. **与相似词的区别**：我终于明白了`serendipity`与`luck`、`chance`的细微差别：
    - `luck`是更广泛的好运概念
    - `chance`指可能性或随机性，可以是中性的
    - `serendipity`特指在寻找某物时意外发现另一件有价值的事物

### 第六阶段：无限探索的开始
13. **相关概念的吸引**：在内容的最后，我看到了相关概念：`chance`、`discovery`、`fortune`、`coincidence`、`accident`。

14. **无缝的知识连接**：我好奇地向上滑动，进入了`discovery`的探索。我发现这两个词在科学发现的语境中经常一起使用，但`discovery`更强调主动的探索过程。

15. **知识网络的构建**：接下来我又探索了`fortune`和`coincidence`，逐渐构建起了一个关于"偶然性"和"发现"的完整知识网络。

### 我的感受和收获：
- **即时满足**：我的查词需求被完美满足，而且获得了比预期更丰富的理解
- **文化洞察**：我不仅学会了一个词，还了解了它背后的文化故事
- **知识连接**：我发现了一个词汇家族，理解了它们之间的微妙差别
- **学习乐趣**：整个过程充满发现的乐趣，就像体验了一次小小的`serendipity`

**这次体验让我明白**：SenseWord不只是一个词典，它是一个知识探索的平台。我来查一个词，却发现了一个知识宝藏。这种体验本身就是一种`serendipity`。

## Story17｜当网络不给力时，我依然能享受学习

**场景**: 我在地铁里使用SenseWord，网络信号时断时续，但我的学习体验并没有被打断。

**我的体验**:

1. **离线缓存的无感体验**：我打开App看到今日推荐词`serendipity`，虽然地铁里网络很差，但内容加载得很流畅。我意识到这个词昨天就被预加载到了本地。

2. **智能的网络策略**：当我向上滑动探索相关词汇时，App优先显示已缓存的内容，同时在后台尝试加载新内容。我看到一个小的加载指示器，但不影响我继续阅读。

3. **优雅的降级体验**：当我搜索一个全新的词`ephemeral`时，由于网络问题无法立即生成，App显示："网络连接不稳定，已为您保存搜索请求，将在网络恢复时自动获取内容。" 同时推荐了几个相关的已缓存词汇。

4. **后台同步的贴心**：当我出地铁网络恢复时，收到了一个温和的通知："您搜索的'ephemeral'已准备就绪"，点击后直接进入了完整的解析页面。

5. **数据同步的智能处理**：我在地铁里收藏的几个单词，在网络恢复后自动同步到了云端，我在其他设备上也能看到这些收藏。

**我的感受**：即使在网络不佳的环境下，SenseWord依然让我能够持续学习，这种体验让我更加信任这个产品。我知道无论在什么环境下，我的学习都不会被技术问题打断。

## Story18｜我感受到了真正的英语进步

**场景**: 使用SenseWord三个月后，我在阅读英文文章时突然意识到自己的英语水平有了质的提升。

**我的进步体验**:

1. **词汇理解的深度变化**：我在读到"The serendipitous discovery led to a paradigm shift"时，不仅理解了`serendipitous`的含义，还能感受到作者选择这个词而不是`accidental`的微妙用意——强调发现的积极性和意外的愉悦感。

2. **文化理解的提升**：当我看到"It was a Eureka moment"时，我想起了在SenseWord中学到的词源故事，理解了这种表达背后的文化内涵和历史典故。

3. **语言直觉的培养**：我发现自己开始能够"感受"到某些词汇的情感色彩：
   - `serendipity`的积极魔法感
   - `melancholy`的诗意忧伤感
   - `ephemeral`的转瞬即逝的美感

4. **搭配使用的自然性**：我在写作时自然地使用了"pure serendipity"、"happy coincidence"这样的地道搭配，而不是生硬的直译。

5. **主动探索的习惯**：我不再满足于"大概理解"，而是主动探索词汇的深层含义和使用场景。遇到有趣的词汇时，我会主动在SenseWord中探索它的词汇家族。

6. **跨文化交流的自信**：在与外国同事交流时，我能够使用更精准、更有文化内涵的表达，他们也注意到了我英语表达的提升。

**我的成就感**：这种进步不是通过背单词列表获得的，而是通过深度理解和知识连接自然形成的。我感觉自己不只是在学英语，而是在理解一种文化和思维方式。SenseWord让我从"知道单词的意思"提升到了"理解单词的灵魂"。

---