# 用户故事：个人收藏的沉浸式复习体验

## 1. 用户画像声明 (User Persona Statement)

作为一名 已经使用SenseWord学习了一段时间，积累了一定数量的收藏单词，希望能够系统性地复习和巩固这些词汇，同时在复习过程中发现新的知识连接的学习者张静，

我想要 一个能够将我的收藏单词以与主学习界面相同的沉浸式体验进行展示，并能够从收藏词汇无缝地探索到相关概念，实现复习与新学习相结合的功能，

以便于 我能够有效地巩固已学词汇，通过复习发现新的知识连接，建立更完整的词汇网络，并保持学习的连续性和深度。

## 2. 用户角色详述 (User Persona Detail)

张静，27岁，英语培训机构老师，对学习方法和记忆技巧有深入的理解。她知道复习是学习过程中不可缺少的环节，但传统的复习方式往往枯燥乏味。她在使用SenseWord的过程中收藏了很多有价值的单词，希望能够有一个优雅的方式来复习这些词汇。她不希望复习只是简单的重复，而是希望在复习过程中能够发现新的知识连接，实现螺旋式的学习提升。

## 3. 用户目标 (User Goal)

张静希望能够通过沉浸式的复习体验，有效地巩固已收藏的词汇，同时在复习过程中发现新的知识连接和相关概念，实现复习与新学习的有机结合，建立更完整的词汇知识网络。

## 4. 场景与过程 (Scenario & Process)

### 初始场景
张静在周末的下午，想要复习这周收藏的单词。她已经收藏了20多个词汇，包括`ubiquitous`、`serendipity`、`ephemeral`等，希望通过系统的复习来巩固这些词汇。

### 详细交互流程

1. **进入收藏复习模式**：
   - 张静打开SenseWord，在设置页面点击了"我的收藏"
   - 界面平滑地过渡到她熟悉的"聚光灯无限内容流"视图
   - 顶部的"单词锚点"和下方的"内容舞台"与主界面完全一致
   - 她立刻感受到了界面的一致性，没有学习新交互的负担

2. **收藏词汇的优雅展示**：
   - 她发现这个内容流的"播放列表"完全由她收藏的单词组成
   - 第一个出现的是上周收藏的`ubiquitous`，以熟悉的锚点形式展示：
     - 单词：`ubiquitous`
     - 发音：`/juːˈbɪkwɪtəs/`
     - 核心释义：无处不在的，普遍存在的
   - 音频状态显示为绿色，表示所有相关音频已准备就绪

3. **深度复习的重新体验**：
   - 张静开始向上滑动，重新体验`ubiquitous`的各个维度解析
   - "意图"卡片：母语者使用这个词时想强调某事物的普遍性和无所不在
   - "情绪"卡片：带有一种"无法逃避"或"全面覆盖"的感觉
   - "想象"卡片：像空气一样无处不在，像网络信号一样覆盖每个角落
   - 她发现重新阅读这些内容时，理解更加深刻了

4. **收藏标记的特殊显示**：
   - 在每张卡片的右上角，她看到了一个金色的星标，提醒她这是收藏的内容
   - 这个视觉标记让她有一种"这是我的知识资产"的归属感
   - 她可以随时取消收藏，星标会相应地消失

5. **从复习到新发现的转换**：
   - 当她完成对`ubiquitous`的复习后，向上滑动进入下一个词汇
   - 令她惊喜的是，系统显示的不是她收藏列表中的下一个词，而是`pervasive`
   - 她意识到这是一个与`ubiquitous`相关的概念，虽然她没有收藏过

6. **知识连接的新发现**：
   - 张静开始学习`pervasive`，发现它与`ubiquitous`有微妙的差别：
     - `ubiquitous`更强调"无处不在"的客观存在
     - `pervasive`更强调"渗透性"的影响过程
   - 她心想："原来这两个词可以这样对比着记，太棒了！"
   - 她立即收藏了`pervasive`，将其加入自己的知识资产

7. **复习与探索的循环**：
   - 接下来她又遇到了收藏的`serendipity`，重新体验了这个词的魅力
   - 然后系统又引导她发现了相关的`coincidence`和`fortune`
   - 她意识到自己不仅在复习旧知识，还在建立新的知识连接

8. **学习成果的积累**：
   - 经过一个小时的复习，张静不仅巩固了原有的收藏词汇
   - 还新收藏了5个相关概念，建立了更完整的词汇网络
   - 她感受到了复习的真正价值：不是简单的重复，而是知识的深化和扩展

## 5. 用户价值体现 (User Value Realization)

### 复习体验的革新
张静感叹道："这是我体验过的最好的复习方式！不是枯燥的重复背诵，而是重新发现和深化理解。每次复习都能有新的收获，这种感觉太棒了。"

### 知识网络的建立
"通过复习，我不仅巩固了已学的词汇，还发现了很多相关概念。比如从`ubiquitous`发现`pervasive`，从`serendipity`发现`coincidence`。这种知识连接让我的词汇学习更加系统化。"

### 学习效率的提升
"这种复习方式比传统的单词卡片效率高太多。我不需要机械地重复，而是在理解的基础上进行巩固。而且还能在复习过程中学到新词汇，一举两得。"

### 学习动力的维持
"复习不再是一件枯燥的事情，而是充满发现的探索过程。我永远不知道下一个会遇到什么相关概念，这种不确定性让复习变得有趣。"

### 知识资产的增值
"我的收藏不再是静态的词汇列表，而是动态的知识网络。每次复习都能让这个网络更加完善，我感觉自己的知识资产在不断增值。"

## 6. 验收标准 (Acceptance Criteria)

### AC1: 收藏入口的便捷访问
- **鉴于** 用户想要复习收藏的单词
- **当** 用户在设置页面点击"我的收藏"时
- **那么** 应平滑过渡到收藏复习界面
- **并且** 界面布局应与主学习界面保持一致
- **并且** 应显示收藏词汇的总数量

### AC2: 收藏内容的优先展示
- **鉴于** 用户进入收藏复习模式
- **当** 界面加载完成时
- **那么** 应优先显示用户收藏的词汇
- **并且** 收藏词汇应按收藏时间倒序排列
- **并且** 每个收藏词汇应有明显的收藏标记

### AC3: 收藏状态的视觉标识
- **鉴于** 用户正在复习收藏的词汇
- **当** 显示词汇内容时
- **那么** 应在卡片上显示金色星标等收藏标识
- **并且** 用户应能直接取消收藏
- **并且** 取消收藏后应立即更新视觉状态

### AC4: 相关概念的智能推荐
- **鉴于** 用户完成一个收藏词汇的复习
- **当** 用户向上滑动时
- **那么** 系统应智能推荐相关概念词汇
- **并且** 推荐词汇应与当前词汇有语义关联
- **并且** 推荐词汇可以是未收藏的新词汇

### AC5: 新发现词汇的收藏功能
- **鉴于** 用户在复习过程中遇到新的相关词汇
- **当** 用户想要收藏新词汇时
- **那么** 应提供便捷的收藏功能
- **并且** 新收藏的词汇应立即加入收藏列表
- **并且** 应有收藏成功的反馈提示

### AC6: 复习进度的记录
- **鉴于** 用户进行收藏复习
- **当** 用户完成词汇学习时
- **那么** 系统应记录复习进度和时间
- **并且** 应统计复习次数和新发现词汇数量
- **并且** 应在适当时机显示复习成果总结
