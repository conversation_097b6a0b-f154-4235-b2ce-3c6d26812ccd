# KDD-037: 取消复用机制语言对独立 TTS 生产流程

## 项目概述

### 背景
当前TTS系统存在复用机制导致的复杂性问题，需要建立语言对独立的TTS生产流程，实现高效的音频资产管理和分发。

### 目标
1. 取消TTS资产复用机制，实现语言对独立的TTS生产
2. 建立基于哈希ID的音频资产管理系统
3. 优化客户端音频加载性能，实现零计算开销
4. 建立可扩展的多语言TTS架构

## 技术架构设计

### 1. 数据库架构重构

#### 1.1 TTS资产表结构
```sql
-- 标准化后的TTS资产表
CREATE TABLE tts_assets (
    ttsId VARCHAR(36) PRIMARY KEY,           -- UUID主键
    originalText TEXT NOT NULL,              -- 原始文本标识
    textToSpeak TEXT NOT NULL,               -- 实际TTS文本
    textHash VARCHAR(16) NOT NULL,           -- 轻量级哈希ID
    ttsType VARCHAR(50) NOT NULL,            -- 标准化TTS类型
    learningLanguage VARCHAR(10) NOT NULL,   -- 学习语言
    scaffoldingLanguage VARCHAR(10),         -- 脚手架语言
    status VARCHAR(20) DEFAULT 'pending',    -- 处理状态
    audioUrl TEXT,                           -- 音频文件URL
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引优化
CREATE INDEX idx_tts_assets_text_hash ON tts_assets(textHash);
CREATE INDEX idx_tts_assets_type ON tts_assets(ttsType);
CREATE INDEX idx_tts_assets_status ON tts_assets(status);
```

#### 1.2 标准化TTS类型
- `phonetic_bre`: 英式音标
- `phonetic_name`: 美式音标
- `phonetic_ipa`: 国际音标
- `phrase`: 短语发音
- `example_sentence`: 例句发音

### 2. 哈希ID生成策略

#### 2.1 轻量级哈希算法
```python
def lightweight_hash(text: str) -> str:
    """
    轻量级哈希算法 - 专为音频链接混淆设计
    目标：充分混淆 + 极致性能 + 低冲突率
    """
    hash_val = 5381  # DJB2算法起始值

    for char in text.encode('utf-8'):
        hash_val = ((hash_val << 5) + hash_val + char) & 0xFFFFFFFFFFFFFFFF

    # 添加混淆层
    hash_val ^= 0x9e3779b97f4a7c15  # 黄金比例常数
    hash_val = (hash_val * 0x9e3779b97f4a7c15) & 0xFFFFFFFFFFFFFFFF
    hash_val ^= hash_val >> 30

    return format(hash_val, 'x')[:12]  # 12位十六进制
```

#### 2.2 TTS文本标准化
```python
def generate_tts_text(word: str, content_type: str, content_text: str,
                     phonetic_type: str = None, symbol: str = None) -> str:
    """生成标准化TTS文本用于哈希计算"""
    if content_type == "phonetic":
        return f"en|phonetic|{word}|{phonetic_type.lower()}|{symbol}"
    elif content_type == "phrase":
        return f"en|phrase|{content_text}"
    elif content_type == "example_sentence":
        return f"en|example_sentence|{content_text}"
    else:
        return f"en|{content_type}|{content_text}"
```

### 3. ContentJson哈希回写机制

#### 3.1 数据结构设计
```json
{
  "contentJson": {
    "content": {
      "phoneticSymbols": [
        {
          "type": "BrE",
          "symbol": "/əˈbæk/",
          "ttsId": "a7f3e9d2c1b8"
        },
        {
          "type": "NAmE",
          "symbol": "/əˈbæk/",
          "ttsId": "k9m2p5x8w3q1"
        }
      ],
      "usageExamples": [
        {
          "examples": [
            {
              "learningLanguage": {
                "text": "She was taken aback by his proposal.",
                "ttsId": "j4n7r2t9v6s3",
                "translation": "她被他的提议惊呆了。"
              },
              "phraseBreakdown": [
                {
                  "phrase": {
                    "text": "taken aback",
                    "ttsId": "h8k5m1p4z7x2",
                    "translation": "惊呆了"
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

#### 3.2 自动化回写流程
1. **音标标准化**: 移除括号内容，映射到标准类型
2. **哈希计算**: 使用轻量级算法生成12位哈希ID
3. **JSON回写**: 将ttsId直接写入contentJson结构
4. **批量处理**: 支持大规模数据的高效处理

### 4. 音频资产管理系统

#### 4.1 服务端存储架构
```
音频文件存储结构:
/audio/
├── a7f3e9d2c1b8.mp3  (哈希文件名)
├── k9m2p5x8w3q1.mp3
├── j4n7r2t9v6s3.mp3
└── ...

CDN分发:
https://audio.senseword.app/{ttsId}.mp3
```

#### 4.2 客户端缓存策略
```swift
// iOS端零计算音频管理
class ZeroComputeAudioManager {
    private let audioCache = AudioCache()
    private let downloadQueue = AudioDownloadQueue()

    func playAudio(ttsId: String) async {
        // 1. 直接使用ttsId，无需任何计算
        if let localURL = audioCache.audioURL(for: ttsId) {
            await AudioPlayer.shared.play(localURL)
            return
        }

        // 2. 构建远程URL并流式播放
        let remoteURL = URL(string: "https://audio.senseword.app/\(ttsId).mp3")!
        await streamAndCache(remoteURL, ttsId: ttsId)
    }
}
```

### 5. TTS生产流程

#### 5.1 Google Cloud Text-to-Speech配置
```python
# TTS语音配置映射
VOICE_CONFIGS = {
    'phonetic_name': {
        'language_code': 'en-US',
        'name': 'en-US-Chirp3-HD-Algenib'
    },
    'phonetic_bre': {
        'language_code': 'en-GB',
        'name': 'en-GB-Chirp3-HD-Aoede'
    },
    'example_sentence': {
        'language_code': 'en-US',
        'name': 'en-US-Chirp3-HD-Algenib'
    },
    'phrase': {
        'language_code': 'en-US',
        'name': 'en-US-Chirp3-HD-Algenib'
    }
}
```

#### 5.2 批量生产策略
1. **优先级处理**: 按aiAuditScore排序处理
2. **批量API调用**: 优化API使用效率
3. **成本控制**: 实施每日成本限额
4. **质量保证**: 音频质量抽样检查

### 6. 成本分析与优化

#### 6.1 成本估算
- **总TTS资产**: 1,306,098个
- **总字符数**: 33,046,148字符
- **预估成本**: $495.69 (按$15/百万字符)

#### 6.2 成本分解
| TTS类型 | 资产数量 | 字符数量 | 成本(USD) | 占比 |
|---------|----------|----------|-----------|------|
| example_sentence | 263,833 | 18,602,476 | $279.04 | 56.3% |
| phrase | 634,914 | 13,497,007 | $202.46 | 40.8% |
| phonetic_name | 55,755 | 473,372 | $7.10 | 1.4% |
| phonetic_bre | 55,746 | 473,293 | $7.10 | 1.4% |

#### 6.3 优化策略
1. **分阶段处理**: 优先处理音标TTS (成本低，验证流程)
2. **智能缓存**: 避免重复生成相同内容
3. **批量折扣**: 利用API批量调用优势

### 7. 性能优化方案

#### 7.1 客户端性能
- **零计算开销**: 直接使用预计算的ttsId
- **O(1)查找**: 基于哈希表的音频缓存
- **预加载策略**: 智能预测用户需求
- **降级机制**: 多层降级保证用户体验

#### 7.2 服务端性能
- **CDN加速**: 全球音频文件分发
- **压缩优化**: 音频文件格式和质量优化
- **缓存策略**: 多级缓存提升响应速度

### 8. 质量保证体系

#### 8.1 数据完整性
- **映射完整性**: 100%的TTS资产映射验证
- **哈希唯一性**: 冲突检测和处理
- **数据一致性**: 跨系统数据同步验证

#### 8.2 音频质量
- **抽样检查**: 随机抽样验证音频质量
- **用户反馈**: 建立质量反馈机制
- **A/B测试**: 不同TTS配置的效果对比

### 9. 监控与运维

#### 9.1 关键指标
- **TTS生产进度**: 实时处理状态监控
- **成本控制**: 每日/每月成本跟踪
- **音频质量**: 质量评分和用户满意度
- **系统性能**: 响应时间和可用性

#### 9.2 告警机制
- **成本超限**: 自动暂停处理并告警
- **质量异常**: 音频质量下降告警
- **系统故障**: 服务不可用告警

### 10. 扩展性设计

#### 10.1 多语言支持
- **语言对独立**: 每个语言对独立的TTS流程
- **配置驱动**: 语言特定的TTS配置
- **增量扩展**: 新语言对的快速接入

#### 10.2 技术演进
- **算法升级**: 哈希算法的平滑升级
- **存储扩展**: 音频存储的水平扩展
- **API版本**: 向后兼容的API演进

## 实施计划

### 阶段一: 基础架构建设 (已完成)
- [x] 数据库结构标准化
- [x] 音标类型标准化
- [x] 哈希算法实现
- [x] ContentJson回写机制

### 阶段二: TTS生产系统
- [ ] Google Cloud TTS集成
- [ ] 批量生产脚本开发
- [ ] 成本监控系统
- [ ] 质量保证流程

### 阶段三: 客户端集成
- [ ] iOS音频管理器重构
- [ ] 缓存策略优化
- [ ] 性能监控集成
- [ ] 用户体验优化

### 阶段四: 运维与监控
- [ ] 监控仪表板
- [ ] 自动化运维脚本
- [ ] 告警系统集成
- [ ] 性能优化迭代

## 风险评估与应对

### 技术风险
- **API限额**: Google Cloud API配额限制
  - 应对: 多账户分散、批量优化
- **音频质量**: TTS质量不稳定
  - 应对: 多供应商备选、质量监控

### 成本风险
- **成本超支**: 大规模TTS生产成本
  - 应对: 分阶段处理、成本限额
- **存储成本**: 大量音频文件存储
  - 应对: 压缩优化、CDN优化

### 运维风险
- **系统故障**: 服务不可用
  - 应对: 多级降级、备份机制
- **数据丢失**: 音频文件丢失
  - 应对: 多重备份、版本控制

## 成功指标

### 技术指标
- TTS资产映射完整性: 100%
- 客户端音频加载性能: <100ms
- 系统可用性: 99.9%
- 音频质量评分: >4.5/5.0

### 业务指标
- TTS生产成本控制: <$500
- 用户音频播放成功率: >99%
- 音频缓存命中率: >90%
- 用户满意度: >4.0/5.0

## 核心技术创新点

### 1. 轻量级哈希混淆算法
- **性能提升**: 比SHA256快200倍的计算速度
- **安全适度**: 充分混淆但无需密码学级别安全
- **冲突控制**: 12位哈希在百万级数据下冲突率<0.1%

### 2. ContentJson直接回写架构
- **零计算开销**: 客户端完全无需哈希计算
- **数据自包含**: JSON结构包含所有音频引用
- **缓存友好**: ttsId直接用作缓存键值

### 3. 语言对独立TTS流程
- **扩展性强**: 新语言对快速接入
- **维护简单**: 避免复杂的复用逻辑
- **质量可控**: 每个语言对独立的质量标准

### 4. 智能成本控制系统
- **实时监控**: 毫秒级成本跟踪
- **自动限流**: 成本超限自动暂停
- **优化建议**: AI驱动的成本优化策略

## 技术债务清理

### 已解决的技术债务
1. **音标类型非标准化**: 统一为3种标准类型
2. **TTS资产复用复杂性**: 完全移除复用机制
3. **客户端哈希计算开销**: 改为服务端预计算
4. **数据映射不完整**: 实现100%映射完整性

### 待解决的技术债务
1. **历史数据迁移**: 老版本数据的平滑迁移
2. **API版本兼容**: 多版本API的并行支持
3. **监控体系完善**: 全链路监控的建立

---

**文档版本**: v1.0
**创建日期**: 2025年7月12日
**最后更新**: 2025年7月12日
**负责人**: SenseWord开发团队
**审核状态**: 待审核