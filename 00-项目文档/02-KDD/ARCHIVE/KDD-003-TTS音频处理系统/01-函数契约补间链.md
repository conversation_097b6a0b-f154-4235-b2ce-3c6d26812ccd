# SenseWord AI内容标准化+TTS音频处理系统 - 线性处理版

## 1. 系统概述

本系统负责为SenseWord应用提供完整的AI内容标准化和TTS音频处理能力。系统采用**数据驱动的完全解耦架构**：

- **立即响应**: AI内容标准化后立即返回文本内容
- **异步音频**: 后台统一生成所有音频（单词发音+例句+短语）
- **状态轮询**: 客户端通过状态指示器显示音频生成进度（3秒轮询）

通过这种设计，用户立即获得完整文本内容开始学习，音频在后台无感生成（预计3-10秒内完成），通过UI状态反馈（灰色/绿色圆点）提供清晰的用户体验。数据库作为唯一的状态信息源，确保系统完全解耦和高度可靠。

## 2. 分支策略与工作区建议

### Git Worktree 工作区配置

- **建议的特性分支名称**: `feature/tts/linear-audio-processing-system`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-tts-linear-processing`
- **基础分支**: `dev`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-tts-linear-processing -b feature/tts/linear-audio-processing-system dev
    ```

### 工作区特点
- **独立开发环境**: 专门用于线性TTS音频处理系统开发
- **并行开发**: 不影响主工作区的其他开发任务
- **清晰隔离**: TTS相关代码变更完全隔离
- **便于测试**: 独立环境便于TTS功能测试和验证

## 3. 线性处理架构设计

### 核心流程
```
用户查询 → AI生成+标准化 → 立即返回文本 →
设置ttsStatus='pending' → 异步触发音频生成 →
后台统一生成所有音频 → 客户端3秒轮询状态 → 状态变为'completed'
```

### 状态管理（极简版）
- **processing**: 音频生成中（灰色圆点）
- **completed**: 所有音频已就绪（绿色圆点）
- **failed**: 生成失败，定时重试

### 文件结构（极简版）
```
cloudflare/workers/
├── api/                          # [修改] 现有API Worker
│   ├── src/
│   │   ├── index.ts              # [修改] 添加状态查询端点
│   │   ├── services/
│   │   │   ├── ai.service.ts     # [修改] 添加AI内容标准化
│   │   │   └── word.service.ts   # [修改] 添加状态管理
│   │   └── types/
│   │       └── word-types.ts     # [修改] 添加音频状态类型
└── tts/                          # [新增] TTS Worker（极简版）
    ├── src/
    │   ├── index.ts              # [新增] 定时任务处理
    │   └── services/
    │       └── audio.service.ts  # [新增] 统一音频生成
    └── wrangler.toml             # [新增] 定时任务配置
```

## 4. Commit 规划概要（线性处理版）

### 第一期实施（已完成）
- [x] feat(ai): 添加AI内容标准化过滤器
- [x] feat(api): 实现线性处理流程（立即返回文本+异步音频）
- [x] feat(api): 添加音频状态查询端点（支持3秒轮询）
- [x] feat(tts-worker): 创建TTS Worker（统一音频生成）
- [x] feat(tts-worker): 实现定时任务处理所有'processing'状态记录
- [x] feat(tts-worker): 配置宽松重试机制（音频是内容资产）
- [x] chore(config): 配置Azure TTS和R2存储环境变量
- [x] test(tts): 添加线性处理系统端到端测试用例

### 第二期优化（用户体验提升）
- [ ] feat(api): 异步触发音频生成（从1分钟优化到3-10秒）
- [ ] feat(tts-worker): 添加HTTP处理端点（数据驱动架构）
- [ ] feat(tts-worker): 实现统一音频生成（所有音频一次性处理）
- [ ] feat(api): 优化异步触发逻辑（容错处理）

## 5. 函数契约补间链（线性处理版）

### [FC-A1]: API端点主处理器（线性版）

- **职责**: 实现线性处理流程：AI生成+标准化→立即返回文本→异步触发音频生成
- **函数签名**: `handleWordLookup(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

**线性处理流程**:
1. AI生成内容并标准化（FC-A2）
2. 立即返回文本响应（status='processing'）
3. 异步触发音频生成（不等待）

>>>>> **输入 (Input)**: 单词查询请求

```typescript
interface WordLookupRequest {
  word: string;     // "progressive"
  lang: string;     // "zh"
}
```

<<<<< **输出 (Output)**: 立即文本响应

```typescript
// 线性处理响应（立即返回文本）
interface WordResponse {
  word: string;
  content: {
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
      audioUrl: string;            // 初始为空""
    }>;
    coreDefinition: string;
    contextualExplanation: {
      nativeSpeakerIntent: string;
      emotionalResonance: string;
      vividImagery: string;
      etymologicalEssence: string;
    };
    usageExamples: Array<{
      category: string;
      examples: Array<{
        english: string;
        translation: string;
        audioUrl: string;          // 初始为空""
        phraseBreakdown: Array<{
          phrase: string;
          translation: string;
          audioUrl: string;        // 初始为空""
        }>;
      }>;
    }>;
  };
  audioStatus: 'processing';       // 音频生成中
}
```

**线性处理优势**：
- **立即可用**: 用户1-2秒内获得完整文本内容
- **状态清晰**: audioStatus明确告知音频生成进度
- **学习不阻塞**: 用户可以立即开始学习文本内容

---

### [FC-A2]: AI内容标准化过滤器（线性版）

- **职责**: 对AI生成的内容进行标准化过滤，确保contextualExplanation字段符合预期结构
- **函数签名**: `standardizeAIContent(aiContent: RawAIContent): StandardizedAIContent`
- **所在文件**: `cloudflare/workers/api/src/services/ai.service.ts`

>>>>> **输入 (Input)**: AI原始内容

```typescript
interface RawAIContent {
  word: string;
  content: {
    contextualExplanation: {
      nativeSpeakerIntent: string;
      emotionalResonance: string;
      vividImagery: string;
      etymologicalEssence: string;
      // 可能的随机字段
      criticalLanguageRule?: string;
      culturalContext?: string;
    };
    // ... 其他内容字段
  };
}
```

<<<<< **输出 (Output)**: 标准化内容

```typescript
interface StandardizedAIContent {
  word: string;
  content: {
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
    }>;
    coreDefinition: string;
    contextualExplanation: {
      nativeSpeakerIntent: string;
      emotionalResonance: string;
      vividImagery: string;
      etymologicalEssence: string;
      // 随机字段已移除
    };
    usageExamples: Array<{
      category: string;
      examples: Array<{
        english: string;
        translation: string;
        phraseBreakdown: Array<{
          phrase: string;
          translation: string;
        }>;
      }>;
    }>;
  };
}
```

---

### [FC-A3]: 音频状态查询端点

- **职责**: 提供轻量级接口供客户端查询音频生成状态，支持3秒轮询
- **函数签名**: `getAudioStatus(word: string, env: Env): Promise<AudioStatus>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

>>>>> **输入 (Input)**: 状态查询

```typescript
interface StatusQuery {
  word: string;        // "progressive"
  lang: string;        // "zh"
}
```

<<<<< **输出 (Output)**: 音频状态

```typescript
interface AudioStatusResponse {
  word: string;
  audioStatus: 'processing' | 'completed' | 'failed';
  lastUpdated: string;
}
```

---

### [FC-T1]: TTS Worker定时任务处理器

- **职责**: 定时处理所有'processing'状态的记录，统一生成所有音频
- **函数签名**: `processAudioJobs(event: ScheduledEvent, env: Env): Promise<void>`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`

>>>>> **输入 (Input)**: 定时任务事件

```typescript
interface ScheduledEvent {
  scheduledTime: number;
  cron: string;        // "*/1 * * * *" (每分钟)
}
```

<<<<< **输出 (Output)**: 无返回值

```typescript
// void - 处理完成，更新数据库状态
```

---

### [FC-T2]: 统一音频生成服务

- **职责**: 为单词生成所有缺失的音频（单词发音+例句+短语）
- **函数签名**: `generateAllAudio(record: DatabaseRecord, env: Env): Promise<boolean>`
- **所在文件**: `cloudflare/workers/tts/src/services/audio.service.ts`

>>>>> **输入 (Input)**: 数据库记录

```typescript
interface DatabaseRecord {
  word: string;
  contentJson: string;     // 包含所有需要音频的文本
  audioStatus: 'processing';
}
```

<<<<< **输出 (Output)**: 处理结果

```typescript
interface AudioResult {
  success: boolean;        // 全成功或全失败
}
```

**统一音频生成逻辑**:
1. 解析contentJson提取所有需要音频的文本
2. 调用Azure TTS生成所有音频（单词发音+例句+短语）
3. 上传到R2存储并获取URL
4. 更新contentJson中的audioUrl字段
5. 全成功返回true，任何失败返回false

---

### [FC-T3]: TTS Worker数据驱动处理端点（最优架构）

- **职责**: 接收单词处理请求，从数据库查询待处理记录并生成音频
- **函数签名**: `handleWordAudioGeneration(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/tts/src/index.ts`

>>>>> **输入 (Input)**: URL路径参数

```typescript
interface AudioRequest {
  word: string;        // URL路径中的单词 "/process/apple/zh"
  language: string;    // URL路径中的语言
}
```

<<<<< **输出 (Output)**: 简单确认

```typescript
interface AudioResponse {
  success: boolean;
  word: string;
}
```

**数据驱动策略**:
1. 从数据库查询指定单词记录
2. 检查ttsStatus是否为'pending'
3. 生成所有音频（单词发音+例句+短语）
4. 更新数据库ttsStatus为'completed'

---

### [FC-A4]: API Worker异步触发逻辑（数据驱动优化）

- **职责**: 在保存单词后立即异步通知TTS Worker处理该单词
- **函数签名**: `triggerAudioGeneration(word: string, language: string, env: Env): Promise<void>`
- **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

>>>>> **输入 (Input)**: 单词信息

```typescript
interface TriggerParams {
  word: string;
  language: string;
  ttsWorkerUrl: string;
}
```

<<<<< **输出 (Output)**: 无返回值（异步处理）

```typescript
// void - 异步触发，不阻塞主流程
```

**数据驱动触发策略**:
1. 使用env.waitUntil()确保不阻塞响应
2. 简单HTTP GET调用：`/process/{word}/{language}`
3. 触发失败不影响主流程，数据库状态保持'pending'
4. TTS Worker从数据库查询状态，确保数据一致性

---

## 6. 线性处理流程伪代码

```typescript
// API Worker：立即响应 + 数据驱动触发
async function handleWordLookup(request: Request, env: Env): Promise<Response> {
    const { word, lang } = extractParams(request)

    // 检查是否已存在
    const existing = await findWord(env.DB, word, lang)
    if (existing) return formatResponse(existing)

    // AI生成+标准化
    const aiContent = await generateAIContent(word, lang, env)
    const standardized = await standardizeContent(aiContent)

    // 保存记录（ttsStatus='pending'）
    await saveRecord(env.DB, word, standardized, 'pending')

    // 【数据驱动触发】异步通知TTS Worker处理该单词
    env.waitUntil(triggerAudioGeneration(word, lang, env))

    // 立即返回文本响应
    return formatResponse({ word, content: standardized, audioStatus: 'processing' })
}

// API Worker：数据驱动触发逻辑
async function triggerAudioGeneration(word: string, lang: string, env: Env): Promise<void> {
    try {
        // 简单的GET调用，让TTS Worker处理指定单词
        await fetch(`${env.TTS_WORKER_URL}/process/${word}/${lang}`, {
            signal: AbortSignal.timeout(3000) // 3秒超时，快速失败
        })
        console.log(`[API] 触发TTS处理成功: ${word}`)
    } catch (error) {
        console.log(`[API] 触发失败，数据库状态保持pending: ${error}`)
        // 失败不影响主流程，定时任务会兜底处理
    }
}

// TTS Worker：数据驱动处理端点
async function handleWordAudioGeneration(request: Request, env: Env): Promise<Response> {
    const { word, language } = extractParams(request.url) // 从URL路径提取
    
    console.log(`[TTS] 开始处理单词: ${word}`)
    
    // 从数据库查询记录
    const record = await findWordDefinition(env.DB, word, language)
    if (!record || record.ttsStatus !== 'pending') {
        console.log(`[TTS] 单词不存在或状态不是pending: ${word}`)
        return Response.json({ success: false, word })
    }

    // 生成所有音频（单词发音+例句+短语）
    const success = await generateAllAudio(record, env)
    
    if (success) {
        // 更新状态为completed
        await updateTtsStatus(env.DB, word, language, 'completed')
        console.log(`[TTS] 音频生成成功: ${word}`)
        return Response.json({ success: true, word })
    } else {
        console.log(`[TTS] 音频生成失败: ${word}`)
        return Response.json({ success: false, word })
    }
}

// TTS Worker：定时任务（兜底处理）
async function processAudioJobs(event: ScheduledEvent, env: Env): Promise<void> {
    // 处理所有超时的pending记录（超过5分钟未处理的）
    const stuckJobs = await getStuckJobs(env.DB, 5) // 5分钟超时
    
    for (const record of stuckJobs) {
        console.log(`[TTS] 定时任务处理超时记录: ${record.word}`)
        const success = await generateAllAudio(record, env)
        const newStatus = success ? 'completed' : 'pending'
        await updateTtsStatus(env.DB, record.word, record.language, newStatus)
    }
}

// 客户端状态查询（数据驱动）
async function getAudioStatus(word: string, env: Env): Promise<AudioStatusResponse> {
    const record = await findWord(env.DB, word)
    return {
        word: record.word,
        audioStatus: record.ttsStatus === 'completed' ? 'completed' : 'processing',
        lastUpdated: record.updatedAt
    }
}
```
---

## 7. 容错机制与风险控制

### 7.1 并发控制
- **数据库锁机制**: 使用`SELECT ... FOR UPDATE`防止同一单词并发处理
- **幂等性保证**: 重复调用同一单词的音频生成不会产生重复音频
- **竞态条件避免**: 异步触发和定时任务通过状态字段协调

### 7.2 资源限制应对
- **Azure TTS限流**: 添加指数退避重试机制，最大重试3次
- **R2存储监控**: 预设存储空间告警，接近限制时暂停音频生成
- **数据库连接池**: 控制并发连接数，避免数据库过载

### 7.3 降级策略
- **异步触发失败**: 自动回退到定时任务处理，用户体验降级但功能不丢失
- **音频生成失败**: 保持pending状态，定时任务持续重试
- **CDN异常**: 提供备用音频访问路径

### 7.4 监控与告警
- **关键指标监控**: 异步触发成功率、音频生成耗时、API调用频率
- **异常告警**: Azure TTS失败率超过20%时触发告警
- **性能基线**: 统一音频生成时间超过30秒视为异常

## 8. 线性处理技术决策

### 7.1 线性处理优势
- **立即响应**: 用户1-2秒内获得完整文本内容
- **状态清晰**: 通过UI状态指示器（灰色/绿色圆点）提供清晰反馈
- **学习不阻塞**: 用户可以立即开始学习，音频在后台生成

### 7.2 状态管理（极简版）
- **processing**: 音频生成中（灰色圆点）
- **completed**: 音频已就绪（绿色圆点）
- **failed**: 生成失败，定时重试

### 7.3 客户端轮询策略
- **轮询频率**: 3秒查询一次音频状态
- **状态显示**: 通过UI圆点颜色变化提供视觉反馈
- **音频预计完成时间**: 3-10秒内（所有音频统一生成）

### 7.4 异步处理策略
- **定时任务**: 每分钟处理一次'pending'状态的记录
- **统一生成**: 所有音频（单词发音+例句+短语）一次性生成
- **容错重试**: 音频是内容资产，失败后保持pending状态等待重试

---

这个线性处理版的KDD-003文档体现了奥卡姆剃刀原则的极致应用，通过简化架构、统一处理流程和清晰的状态管理，为SenseWord应用提供了一个简单、可靠、易于维护的AI内容标准化+TTS音频处理系统。