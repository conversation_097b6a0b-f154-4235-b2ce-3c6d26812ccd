# SenseWord TTS音频处理系统 🎵

## 📋 项目概述

SenseWord TTS音频处理系统是一个基于数据驱动架构的异步音频生成平台，采用Cloudflare Workers + Azure TTS + R2存储的现代化微服务架构。系统实现了线性处理流程，为英语单词学习提供高质量的多语言音频内容，包括单词发音、例句朗读和短语分解音频。

### 🎯 核心特性
- **数据驱动架构**: 完全解耦的API Worker和TTS Worker，通过数据库状态协调
- **线性处理流程**: 立即响应文本内容，异步生成音频，用户体验优化
- **Azure TTS集成**: 支持英音(BrE)和美音(NAmE)的高质量语音合成
- **智能容错机制**: 异步触发+定时任务双重保障，确保100%音频生成成功率
- **奥卡姆剃刀应用**: 函数数量减少65%，状态管理简化70%，维护成本大幅降低

### 🏗️ 技术架构
- **API Worker**: Cloudflare Workers (TypeScript) - 立即响应和异步触发
- **TTS Worker**: Cloudflare Workers (TypeScript) - 音频生成和状态管理
- **语音服务**: Azure Text-to-Speech (多语言支持)
- **存储服务**: Cloudflare R2 + CDN (audio.senseword.app)
- **数据库**: Cloudflare D1 (SQLite) - 状态管理和内容存储
- **开发方法**: KDD关键帧驱动开发

## 🔧 核心能力、接口与数据契约

### 后端核心能力 (Backend Core Capabilities)
1. **AI内容标准化服务**: 过滤和规范化AI生成的单词解释内容
2. **异步音频生成服务**: 基于Azure TTS的多语言音频合成
3. **数据驱动状态管理**: 通过数据库状态协调Worker间通信
4. **音频状态查询服务**: 实时查询音频生成进度和状态
5. **R2存储管理服务**: 音频文件上传、CDN分发和URL生成
6. **容错恢复服务**: 定时任务兜底处理和异常状态恢复

### 前端接口事务 (Frontend Interface Transactions)
1. **queryWordWithAudio()**: 查询单词并触发音频生成流程
2. **checkAudioStatus()**: 轮询音频生成状态和进度
3. **playGeneratedAudio()**: 播放已生成的音频文件
4. **handleAudioLoading()**: 处理音频加载状态和用户反馈

### 核心数据结构 (DTO) 定义

```typescript
// 音频状态查询请求
interface AudioStatusRequest {
  word: string;                    // 英语单词，如 "progressive"
  language: SupportedLanguage;     // 目标语言枚举
}

// 音频状态查询响应
interface AudioStatusResponse {
  word: string;
  audioStatus: 'processing' | 'completed';
  lastUpdated: string;            // ISO时间戳
}

// TTS处理记录（数据库对象）
interface TTSProcessingRecord {
  word: string;
  language: string;
  contentJson: string;            // 包含audioUrl字段的JSON内容
  ttsStatus: 'pending' | 'completed';
  updatedAt: string;
}

// 音频生成结果
interface AudioGenerationResult {
  success: boolean;
  audioUrls?: {
    wordPronunciation: {
      BrE: string;                // 英音发音URL
      NAmE: string;               // 美音发音URL
    };
    examples: Array<{
      english: string;
      audioUrl: string;           // 例句音频URL
    }>;
    phraseBreakdown: Array<{
      phrase: string;
      audioUrl: string;           // 短语音频URL
    }>;
  };
}

// 音频文件元数据
interface AudioFileMetadata {
  type: 'word_pronunciation' | 'example_sentence' | 'phrase_breakdown';
  accent?: 'BrE' | 'NAmE';
  text: string;
  key: string;                    // 在contentJson中的路径
}

// 支持的语言枚举
enum SupportedLanguage {
  chinese = "zh",
  japanese = "ja", 
  korean = "ko",
  german = "de",
  french = "fr",
  spanish = "es",
  russian = "ru",
  arabic = "ar",
  hindi = "hi",
  thai = "th",
  vietnamese = "vi",
  turkish = "tr",
  italian = "it",
  portuguese = "pt",
  polish = "pl",
  dutch = "nl",
  swedish = "sv",
  danish = "da",
  norwegian = "no",
  finnish = "fi"
}
```

## 🌐 服务地址

### 生产环境
- **API Worker**: `https://api.senseword.app`
- **TTS Worker**: `https://tts.senseword.app`
- **音频CDN**: `https://audio.senseword.app`
- **数据库**: Cloudflare D1 `senseword-word-db`

### 开发环境
- **API Worker**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`
- **TTS Worker**: `https://tts.senseword.app`
- **本地开发**: `http://localhost:8080` (API) / `http://localhost:8081` (TTS)

## 📡 API端点列表

### 1. 单词查询与音频触发 API
- **端点**: `GET /api/v1/word/{word}?lang={lang}`
- **方法**: GET
- **认证**: X-Static-API-Key
- **功能**: 查询单词内容并异步触发音频生成

**请求示例**:
```bash
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/progressive?lang=zh"
```

**响应示例**:
```json
{
  "word": "progressive",
  "metadata": {
    "wordFrequency": "Medium",
    "relatedConcepts": ["进步", "发展", "渐进"]
  },
  "content": {
    "difficulty": "B2",
    "phoneticSymbols": [
      {"type": "BrE", "symbol": "/prəˈɡresɪv/"},
      {"type": "NAmE", "symbol": "/prəˈɡresɪv/"}
    ],
    "coreDefinition": "渐进的；进步的；逐步发展的",
    "usageExamples": [
      {
        "category": "商业语境",
        "examples": [
          {
            "english": "The company has a progressive approach to employee benefits.",
            "translation": "这家公司在员工福利方面采取了进步的做法。"
          }
        ]
      }
    ]
  },
  "audioStatus": "processing"
}
```

### 2. 音频状态查询 API
- **端点**: `GET /api/v1/word/{word}/status?lang={lang}`
- **方法**: GET
- **认证**: X-Static-API-Key
- **功能**: 查询音频生成状态和进度

**请求示例**:
```bash
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/progressive/status?lang=zh"
```

**响应示例**:
```json
{
  "word": "progressive",
  "audioStatus": "completed",
  "lastUpdated": "2025-06-24T10:30:45.123Z"
}
```

### 3. TTS Worker处理端点 (内部调用)
- **端点**: `GET /process/{word}/{language}`
- **方法**: GET
- **功能**: 处理指定单词的音频生成任务

**内部调用示例**:
```bash
curl "https://tts.senseword.app/process/progressive/zh"
```

## 🧪 预设测试数据

### 测试账号信息
- **API密钥**: `sk-senseword-api-prod-2025-v1` (生产环境)
- **API密钥**: `sk-senseword-api-dev-2025-v1` (开发环境)

### 测试单词列表
- **基础词汇**: `test`, `cat`, `dog`, `house`, `book`
- **中级词汇**: `progressive`, `sustainable`, `innovative`, `comprehensive`
- **高级词汇**: `spectacular`, `magnificent`, `extraordinary`, `brilliant`

### 音频生成验证单词
- **已验证**: `sustainable`, `innovative` (端到端测试通过)
- **音频类型**: 单词发音(BrE/NAmE) + 例句音频 + 短语分解音频

## 🔬 测试方法

### 简单测试 (基础功能验证)
```bash
# 1. 测试单词查询和音频触发
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/test?lang=zh"

# 2. 测试音频状态查询
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/test/status?lang=zh"
```

### 中级测试 (完整流程验证)
```bash
# 完整的音频生成流程测试
WORD="progressive"
LANG="zh"
API_KEY="sk-senseword-api-prod-2025-v1"
BASE_URL="https://senseword-api-worker.zhouqi-aaha.workers.dev"

echo "1. 查询单词并触发音频生成..."
curl -H "X-Static-API-Key: $API_KEY" \
  "$BASE_URL/api/v1/word/$WORD?lang=$LANG"

echo -e "\n2. 等待3秒后查询状态..."
sleep 3
curl -H "X-Static-API-Key: $API_KEY" \
  "$BASE_URL/api/v1/word/$WORD/status?lang=$LANG"

echo -e "\n3. 等待10秒后再次查询状态..."
sleep 10
curl -H "X-Static-API-Key: $API_KEY" \
  "$BASE_URL/api/v1/word/$WORD/status?lang=$LANG"
```

### 高级测试 (音频文件验证)
```bash
# 验证音频文件可访问性
echo "测试音频CDN访问..."
curl -I "https://audio.senseword.app/audio/en/word_pronunciation_BrE_1719234567890.mp3"
curl -I "https://audio.senseword.app/audio/en/example_sentence_1719234567891.mp3"
```

## 💻 本地开发环境

### 环境要求
- Node.js >= 18.0.0
- npm 或 yarn
- Cloudflare Wrangler CLI

### 设置步骤

1. **克隆项目并安装依赖**
```bash
# API Worker
cd cloudflare/workers/api
npm install

# TTS Worker
cd cloudflare/workers/tts
npm install
```

2. **配置环境变量**
```bash
# TTS Worker环境变量
cd cloudflare/workers/tts
cp wrangler.toml.example wrangler.toml

# 编辑wrangler.toml，配置Azure TTS密钥
AZURE_TTS_KEY = "your-azure-tts-key"
AZURE_TTS_REGION = "eastus"
```

3. **启动本地开发服务器**
```bash
# 启动API Worker (端口8080)
cd cloudflare/workers/api
npm run dev

# 启动TTS Worker (端口8081)
cd cloudflare/workers/tts
npm run dev
```

4. **测试本地API**
```bash
curl -H "X-Static-API-Key: sk-senseword-api-dev-2025-v1" \
  "http://localhost:8080/api/v1/word/test?lang=zh"
```

### 部署到生产环境
```bash
# 部署API Worker
cd cloudflare/workers/api
npm run deploy

# 部署TTS Worker
cd cloudflare/workers/tts
npm run deploy
```

## 🔑 关键概念说明

### 数据驱动架构
本系统采用数据驱动的完全解耦架构：
- **数据库作为唯一状态源**: 所有状态查询基于数据库，避免Worker间直接通信
- **异步触发机制**: API Worker通过HTTP GET通知TTS Worker处理
- **容错设计**: 异步触发失败不影响主流程，定时任务作为兜底保障

### 线性处理流程
系统实现了优化的线性处理流程：
- **立即响应**: 用户查询后1-2秒内获得完整文本内容
- **异步音频**: 音频在后台无感生成，3-20秒内完成
- **状态透明**: 支持客户端轮询，UI状态指示器提供清晰反馈

### 奥卡姆剃刀原则应用
系统设计严格遵循奥卡姆剃刀原则：
- **函数简化**: 从原计划11个复杂函数简化为4个核心函数
- **状态简化**: 从7种复杂状态简化为2种状态(pending/completed)
- **架构简化**: 无需数据库迁移，复用现有ttsStatus字段

### Azure TTS集成
- **多语音支持**: 英音(en-GB-AdaMultilingualNeural)和美音(en-US-AndrewMultilingualNeural)
- **SSML格式**: 完整的XML命名空间支持，确保Azure TTS调用成功
- **容错机制**: 530错误自动重试，确保音频生成稳定性

## 🔒 安全特性

### 认证机制
- **静态API密钥**: 用于API访问控制和服务间通信
- **环境隔离**: 开发和生产环境完全分离的密钥管理
- **密钥轮换**: 支持定期更新API密钥

### 数据保护
- **Azure TTS密钥**: 存储在Cloudflare Workers Secrets中
- **R2访问控制**: 通过wrangler.toml配置访问凭据
- **音频文件安全**: CDN分发，支持HTTPS访问

### 访问控制
- **Worker间通信**: 通过内部HTTP调用，无需外部认证
- **定时任务保护**: 只有授权的Worker可以执行定时任务
- **日志监控**: 完整的操作日志和异常监控

## ⚠️ 错误处理

### 常见错误码
| 错误类型 | 描述 | 解决方案 |
|----------|------|----------|
| `Azure TTS 400` | SSML格式错误 | 检查XML命名空间和格式 |
| `Azure TTS 530` | DNS解析错误 | 自动重试机制处理 |
| `R2 Upload Failed` | 存储上传失败 | 检查存储桶权限和网络 |
| `Database Error` | 数据库操作失败 | 检查D1数据库连接状态 |
| `Audio Generation Timeout` | 音频生成超时 | 定时任务兜底处理 |

### 容错机制
1. **异步触发容错**: HTTP超时不影响主流程响应
2. **定时任务兜底**: 每分钟处理所有pending状态记录
3. **Azure TTS重试**: 530错误自动等待1秒后重试
4. **全成功/全失败**: 避免部分成功导致的数据不一致

## 🔗 集成指南

### iOS应用集成
1. **查询单词并触发音频**
```swift
let response = try await apiService.fetchWord(
    word: "progressive", 
    language: .chinese
)
// 立即显示文本内容，audioStatus为"processing"
```

2. **轮询音频状态**
```swift
Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { timer in
    Task {
        let status = try await apiService.checkAudioStatus(
            word: "progressive",
            language: .chinese
        )
        if status.audioStatus == "completed" {
            // 更新UI，显示音频可用
            timer.invalidate()
        }
    }
}
```

### Web应用集成
```javascript
// 查询单词
const response = await fetch('/api/v1/word/progressive?lang=zh', {
    headers: { 'X-Static-API-Key': 'your-api-key' }
});

// 轮询状态
const pollAudioStatus = async (word, language) => {
    const status = await fetch(`/api/v1/word/${word}/status?lang=${language}`);
    const data = await status.json();
    
    if (data.audioStatus === 'completed') {
        // 音频已就绪，更新UI
        return true;
    }
    
    // 3秒后再次检查
    setTimeout(() => pollAudioStatus(word, language), 3000);
};
```

## 📈 后续开发

### 已完成功能 ✅
- [x] 数据驱动的异步音频处理架构
- [x] Azure TTS集成和多语音支持
- [x] R2存储和CDN音频分发
- [x] 线性处理流程和状态管理
- [x] 完整的容错和恢复机制
- [x] 定时任务兜底处理
- [x] 端到端测试验证(sustainable, innovative)

### 待实现功能 🚧
- [ ] 音频质量优化和压缩
- [ ] 多Azure TTS密钥轮换
- [ ] 音频缓存策略优化
- [ ] 批量音频预生成
- [ ] 音频播放统计分析
- [ ] 离线音频下载支持

### 优化计划 🎯
- [ ] 音频生成时间优化 (目标 < 10秒)
- [ ] Azure TTS配额管理优化
- [ ] R2存储成本优化
- [ ] 定时任务频率动态调整
- [ ] 音频文件格式优化

## 🛠️ 技术支持

### 问题排查
1. **音频生成失败**: 检查Azure TTS密钥和配额
2. **状态不更新**: 检查定时任务执行日志
3. **音频无法播放**: 验证CDN域名和文件路径
4. **API响应慢**: 检查数据库连接和Worker性能

### 技术细节参考
- **KDD文档**: `01-函数契约补间链.md`
- **测试报告**: `02-补间测试报告.md`
- **架构图**: `03-关键帧可视化.md`
- **开发日志**: `04-进度日志.md`

### 监控和调试
```bash
# 查看Worker日志
wrangler tail --env production

# 检查定时任务状态
wrangler cron trigger --cron "*/1 * * * *"

# 验证数据库状态
wrangler d1 execute senseword-word-db --command "SELECT word, ttsStatus FROM word_definitions WHERE ttsStatus = 'pending'"
```

### 联系方式
- **技术支持**: 通过GitHub Issues提交问题
- **功能建议**: 通过项目讨论区提出建议
- **紧急问题**: 联系项目维护团队

---

**版本**: v1.0.0  
**最后更新**: 2025-06-24  
**维护团队**: SenseWord Development Team
