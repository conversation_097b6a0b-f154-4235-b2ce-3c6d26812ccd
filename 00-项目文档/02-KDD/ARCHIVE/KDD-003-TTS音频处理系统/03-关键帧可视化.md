# TTS线性处理系统 - 关键帧可视化（数据驱动架构）

## 数据驱动架构概览

```mermaid
graph TD
    A[用户查询单词] --> B[API Worker]
    B --> C{检查单词是否存在}
    C -->|存在| D[立即返回完整内容]
    C -->|不存在| E[AI生成+标准化]
    
    E --> F[保存到数据库<br/>ttsStatus=pending]
    F --> G[立即返回文本内容<br/>audioStatus=processing]
    F -.->|异步触发| H[HTTP GET /process/word/lang]
    
    H --> I[TTS Worker]
    I --> J[从数据库查询<br/>pending记录]
    J --> K[统一生成所有音频<br/>单词发音+例句+短语]
    K --> L[更新数据库<br/>ttsStatus=completed]
    
    %% 兜底机制
    M[定时任务<br/>每分钟检查] -.->|处理超时记录| I
    
    %% 用户轮询
    N[客户端状态轮询<br/>3秒间隔] --> O[查询ttsStatus]
    O --> P[更新UI状态<br/>processing→completed]
    
    style H fill:#e1f5fe,color:#000
    style J fill:#f3e5f5,color:#000
    style L fill:#e8f5e8,color:#000
    style M fill:#fff3e0,color:#000
```

## 完全解耦的数据流

```mermaid
sequenceDiagram
    participant C as 客户端
    participant API as API Worker
    participant DB as Cloudflare D1
    participant TTS as TTS Worker
    participant Azure as Azure TTS
    participant R2 as R2存储

    Note over C,R2: 数据驱动的完全解耦架构
    
    C->>API: GET /api/v1/word/apple/zh
    API->>DB: 查询记录
    DB-->>API: 记录不存在
    
    API->>API: AI生成+标准化(FC-A2)
    API->>DB: 保存(ttsStatus='pending')
    
    par 立即响应
        API-->>C: 文本内容 + audioStatus='processing'
    and 异步解耦
        API->>TTS: GET /process/apple/zh
        Note right of API: 3秒超时，快速失败
    end
    
    Note over TTS,R2: TTS Worker独立处理流程
    TTS->>DB: 查询word='apple' & language='zh'
    DB-->>TTS: 返回pending记录详情
    
    TTS->>TTS: 提取所有音频文本（单词+例句+短语）
    TTS->>Azure: 统一批量生成音频
    Azure-->>TTS: 返回所有音频数据
    
    TTS->>R2: 上传到CDN
    R2-->>TTS: 返回CDN URLs
    
    TTS->>DB: 更新ttsStatus='completed'
    
    Note over C,DB: 数据驱动状态轮询
    loop 每3秒
        C->>API: GET /api/v1/word/apple/status
        API->>DB: 查询ttsStatus
        DB-->>API: 'completed'
        API-->>C: audioStatus='completed' + URLs
    end
```

## 关键帧数据结构演变

### 关键帧1：用户查询输入
```typescript
interface UserQuery {
  word: string;        // "apple"
  language: string;    // "zh"
  timestamp: string;   // "2025-06-23T08:40:00Z"
}
```

### 关键帧2：AI生成原始内容
```typescript
interface AIGeneratedContent {
  word: string;
  language: string;
  definitions: Array<{
    partOfSpeech: string;
    meanings: Array<{
      definition: string;
      examples: string[];
    }>;
  }>;
  phonetic: {
    symbol: string;
    dialectVariations: {
      BrE: string;
      NAmE: string;
    };
  };
  contextualExplanation: {
    // AI可能生成的冗余字段
    nativeSpeakerIntent: string;
    emotionalResonance: string;
    vividImagery: string;
    etymologicalEssence: string;
    // 其他可能的字段...
  };
}
```

### 关键帧3：FC-A2标准化后内容
```typescript
interface StandardizedContent {
  word: string;
  language: string;
  definitions: Array<{
    partOfSpeech: string;
    meanings: Array<{
      definition: string;
      examples: string[];
    }>;
  }>;
  phonetic: {
    symbol: string;
    dialectVariations: {
      BrE: string;
      NAmE: string;
    };
  };
  contextualExplanation: {
    // 严格只保留4个核心字段
    nativeSpeakerIntent: string;
    emotionalResonance: string;
    vividImagery: string;
    etymologicalEssence: string;
  };
}
```

### 关键帧4：数据库Pending记录
```typescript
interface DatabasePendingRecord {
  id: string;
  word: string;
  language: string;
  content: string;          // JSON序列化的StandardizedContent
  ttsStatus: 'pending';     // 等待音频处理
  createdAt: string;
  updatedAt: string;
}
```

### 关键帧5：立即响应给客户端
```typescript
interface ImmediateResponse {
  word: string;
  content: StandardizedContent;
  audioStatus: 'processing';   // 映射自ttsStatus
  timestamp: string;
}
```

### 关键帧6：异步触发参数
```typescript
interface AsyncTrigger {
  method: 'GET';
  url: '/process/apple/zh';    // 数据驱动URL路径
  timeout: 3000;               // 3秒快速失败
  purpose: 'notify-only';      // 仅通知，不传递数据
}
```

### 关键帧7：TTS Worker处理输入
```typescript
interface TTSWorkerInput {
  word: string;           // 从URL路径解析
  language: string;       // 从URL路径解析
  dbQuery: {             // 从数据库查询的记录
    id: string;
    content: StandardizedContent;
    ttsStatus: 'pending';
  };
  audioTexts: Array<{    // 提取的待生成音频
    type: 'word' | 'example' | 'phrase';
    text: string;
    voice: 'BrE' | 'NAmE';
    filename: string;
  }>;
}
```

### 关键帧8：音频生成完成状态
```typescript
interface CompletedDatabaseRecord {
  id: string;
  word: string;
  language: string;
  content: string;              // 保持不变
  ttsStatus: 'completed';       // 状态已更新
  audioUrls: string;            // JSON序列化的音频URL映射
  createdAt: string;
  updatedAt: string;           // 自动更新时间戳
}
```

### 关键帧9：客户端最终获得的完整响应
```typescript
interface ClientFinalResponse {
  word: string;
  content: StandardizedContent;
  audioStatus: 'completed';     // 从ttsStatus='completed'映射
  audioUrls: {
    word: {
      BrE: string;              // CDN URL
      NAmE: string;             // CDN URL
    };
    examples: Array<{
      text: string;
      audioUrl: string;         // CDN URL
    }>;
    phrases: Array<{
      text: string;
      audioUrl: string;         // CDN URL
    }>;
  };
  lastUpdated: string;
}
```

## 数据驱动状态机

```mermaid
stateDiagram-v2
    [*] --> QueryReceived: 用户查询
    QueryReceived --> DatabaseCheck: 检查是否存在
    
    DatabaseCheck --> ExistingRecord: 记录存在
    DatabaseCheck --> AIGeneration: 记录不存在
    
    AIGeneration --> ContentStandardization: FC-A2过滤
    ContentStandardization --> PendingInDatabase: 保存ttsStatus='pending'
    
    PendingInDatabase --> ImmediateResponse: 立即返回文本
    PendingInDatabase --> AsyncNotification: 异步通知TTS
    
    AsyncNotification --> TTSTriggered: 触发成功
    AsyncNotification --> TimerBackup: 触发失败
    
    TTSTriggered --> AudioProcessing: 数据库查询
    TimerBackup --> AudioProcessing: 定时任务兜底
    
    AudioProcessing --> CompletedInDatabase: 音频生成成功
    AudioProcessing --> RetryProcessing: 生成失败
    
    RetryProcessing --> PendingInDatabase: 重置状态
    
    CompletedInDatabase --> ClientPolling: 状态轮询
    ExistingRecord --> ClientPolling: 直接查询
    
    ClientPolling --> [*]: 返回完整结果
    
    note right of PendingInDatabase
        数据库是唯一的
        状态信息源
    end note
    
    note right of AsyncNotification
        完全解耦设计
        失败不影响主流程
    end note
    
    note right of CompletedInDatabase
        包含完整的
        CDN音频URLs
    end note
```

## 容错与监控机制

```mermaid
graph LR
    A[API异步触发] --> B{TTS Worker响应}
    B -->|3秒内成功| C[正常音频处理]
    B -->|超时/失败| D[数据库状态保持pending]
    
    D --> E[定时任务扫描<br/>每分钟执行]
    E --> F{超时检查}
    F -->|>5分钟未处理| G[兜底处理流程]
    F -->|<5分钟| H[继续等待]
    
    C --> I[更新ttsStatus='completed']
    G --> J{音频生成结果}
    J -->|成功| I
    J -->|失败| K[保持pending状态<br/>等待下次处理]
    
    I --> L[客户端轮询获得结果]
    
    style D fill:#fff2cc,color:#000
    style G fill:#ffe6cc,color:#000
    style I fill:#d5e8d4,color:#000
    style L fill:#e1f5fe,color:#000
```

## 奥卡姆剃刀原则应用效果

### 架构简化对比

| 方面 | 原复杂方案 | 数据驱动简化方案 |
|------|------------|------------------|
| **函数数量** | 11个复杂函数 | 4个核心函数 (-65%) |
| **状态管理** | 7种复杂状态 | 3种状态 (-57%) |
| **数据库改动** | 需要新增字段 | 复用现有字段 |
| **错误处理** | 多层级状态同步 | 单一数据源 |
| **监控复杂度** | 分布式状态追踪 | 集中化状态查询 |

### 用户体验优化

| 时间点 | 用户获得的价值 | 技术实现方式 |
|--------|----------------|--------------|
| **1秒** | 完整文本内容 | 立即返回标准化内容 |
| **3-10秒** | 所有音频就绪 | 数据驱动异步处理 |
| **任何时候** | 可靠的状态查询 | 数据库单一信息源 |

### 开发效率提升

- **代码量减少65%**：从复杂状态机简化为线性流程
- **测试用例减少85%**：状态组合从49种减少到7种
- **部署复杂度最小化**：无需数据库迁移
- **维护成本降低**：单一状态数据源，易于调试

## 用户体验时间线

| 时间 | 事件 | 用户感知 | 技术状态 |
|------|------|----------|----------|
| 0s | 查询单词 | 输入"apple"并提交 | - |
| 1s | 获得文本内容 | 立即看到完整定义和例句 | ttsStatus='pending' |
| 3-10s | 所有音频就绪 | 可以听到所有音频内容 | ttsStatus='completed' |
| 任何时候 | 状态查询 | UI圆点从灰色变为绿色 | 3秒轮询反馈 |

### 极简化设计优势

- **无优先级复杂度**：不区分单词发音和例句音频的生成顺序
- **统一处理时间**：3-10秒内获得所有音频，预期明确
- **状态简单明了**：只有processing/completed/failed三种状态
- **完全数据驱动**：数据库是唯一的状态真相源
