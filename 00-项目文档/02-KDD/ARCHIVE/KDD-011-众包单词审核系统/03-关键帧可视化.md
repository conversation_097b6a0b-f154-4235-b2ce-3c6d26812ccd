


```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e1f5fe', 'primaryTextColor': '#01579b', 'primaryBorderColor': '#0277bd', 'lineColor': '#0288d1', 'secondaryColor': '#f3e5f5', 'tertiaryColor': '#fff3e0', 'background': '#fafafa', 'mainBkg': '#ffffff', 'secondBkg': '#f5f5f5'}}}%%

graph TD
    subgraph "📅 每日凌晨2点 - 自动清理阶段"
        A[🕐 Cron触发清理任务] --> B[🔍 扫描数据库]
        B --> C{📊 检查feedbackScore}
        C -->|score > -3| D[✅ 保留记录<br/>progressive: score=2]
        C -->|score ≤ -3| E[🗑️ 删除记录<br/>terrible: score=-5]
        E --> F[📝 记录清理日志<br/>删除了3个低分单词]
    end
    
    subgraph "👤 用户查询阶段 - 上午10点"
        G[📱 用户查询单词<br/>GET /api/v1/word?word=terrible] --> H{🔍 数据库查找}
        H -->|找到记录| I[📖 返回现有内容]
        H -->|记录不存在| J[🤖 触发AI生成<br/>调用现有generateWordContent]
        J --> K[💾 保存新内容<br/>feedbackScore=0]
        K --> L[📱 返回新生成内容给用户]
    end
    
    subgraph "🔄 用户反馈循环"
        M[👍 用户点赞 +1] --> N[📈 分数变为1]
        O[👎 用户点踩 -1] --> P[📉 分数变为-1]
        P --> Q[👎 更多用户点踩] --> R[📉 分数降至-4]
        R --> S[⏰ 等待下次清理]
    end
    
    F --> G
    L --> M
    L --> O
    S --> A
    
    style A fill:#e8f5e8,stroke:#4caf50,color:#2e7d32
    style E fill:#ffebee,stroke:#f44336,color:#c62828
    style J fill:#fff3e0,stroke:#ff9800,color:#ef6c00
    style K fill:#e3f2fd,stroke:#2196f3,color:#1565c0
```



```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e1f5fe', 'primaryTextColor': '#01579b', 'primaryBorderColor': '#0277bd', 'lineColor': '#0288d1', 'secondaryColor': '#f3e5f5', 'tertiaryColor': '#fff3e0', 'background': '#fafafa', 'mainBkg': '#ffffff', 'secondBkg': '#f5f5f5'}}}%%

sequenceDiagram
    participant U as 👤 用户
    participant API as 🌐 API服务
    participant DB as 🗄️ 数据库
    participant Cleaner as 🧹 清理Worker
    participant AI as 🤖 AI服务
    
    Note over DB: 📊 初始状态<br/>terrible: {score: -5, content: "旧内容"}
    
    rect rgb(255, 245, 238)
        Note over Cleaner: 🕐 2025-06-24 02:00 - 定时清理
        Cleaner->>DB: SELECT * WHERE feedbackScore <= -3
        DB-->>Cleaner: 返回3条记录: [terrible:-5, awful:-4, bad:-3]
        Cleaner->>DB: DELETE FROM word_definitions WHERE word='terrible'
        Cleaner->>DB: DELETE FROM word_definitions WHERE word='awful'  
        Cleaner->>DB: DELETE FROM word_definitions WHERE word='bad'
        DB-->>Cleaner: ✅ 删除成功
        Note over Cleaner: 📝 日志: 清理了3个低分单词
    end
    
    Note over DB: 📊 清理后状态<br/>terrible: 记录不存在 ❌
    
    rect rgb(232, 245, 233)
        Note over U: 🕙 2025-06-24 10:30 - 用户查询
        U->>API: GET /api/v1/word?word=terrible&language=zh
        API->>DB: SELECT * WHERE word='terrible' AND language='zh'
        DB-->>API: ❌ 记录不存在 (null)
        
        Note over API: 🤖 触发现有AI生成流程
        API->>AI: generateWordContent('terrible', 'zh')
        AI-->>API: 🎯 新内容: {difficulty: "B1", coreDefinition: "极其糟糕的"}
        
        API->>DB: INSERT INTO word_definitions (word, contentJson, feedbackScore, ...)
        Note over DB: 💾 保存: terrible: {score: 0, content: "新内容"}
        DB-->>API: ✅ 保存成功
        
        API-->>U: 📱 返回新生成的优质内容
    end
    
    rect rgb(227, 242, 253)
        Note over U: 👍 用户满意，点赞
        U->>API: POST /api/v1/feedback {word: "terrible", action: "like"}
        API->>DB: UPDATE feedbackScore = feedbackScore + 1
        Note over DB: 📈 更新: terrible: {score: 1, content: "新内容"}
        DB-->>API: ✅ 更新成功
        API-->>U: 🎉 反馈成功，新分数: 1
    end
```


```mermaid
%%{init: {'theme':'base', 'themeVariables': { 'primaryColor': '#e1f5fe', 'primaryTextColor': '#01579b', 'primaryBorderColor': '#0277bd', 'lineColor': '#0288d1', 'secondaryColor': '#f3e5f5', 'tertiaryColor': '#fff3e0', 'background': '#fafafa', 'mainBkg': '#ffffff', 'secondBkg': '#f5f5f5'}}}%%

graph TB
    subgraph "🌍 Cloudflare 边缘网络"
        subgraph "📱 现有系统 (完全不变)"
            API[🌐 API Worker<br/>senseword-api-worker<br/>处理用户查询]
            AI[🤖 AI服务<br/>generateWordContent<br/>Gemini 2.5 Flash]
            API --> AI
        end
        
        subgraph "🧹 新增清理系统 (独立部署)"
            Cleaner[🧹 清理Worker<br/>word-quality-cleaner<br/>定时清理低分单词]
            Cron[⏰ Cron触发器<br/>每日凌晨2点<br/>0 2 * * *]
            Cron --> Cleaner
        end
        
        subgraph "🗄️ 共享数据层"
            DB[(🗄️ D1 数据库<br/>word_definitions表<br/>存储单词和评分)]
            KV[📦 KV存储<br/>配置和缓存]
        end
    end
    
    subgraph "👥 用户层"
        User[👤 用户<br/>查询单词<br/>提供反馈]
        Mobile[📱 iOS客户端<br/>SenseWord App]
        User --> Mobile
    end
    
    %% 数据流连接
    Mobile -.->|HTTPS请求| API
    API <-->|读写| DB
    API <-->|缓存| KV
    Cleaner -->|删除低分记录| DB
    
    %% 工作流程标注
    API -.->|记录不存在时<br/>自动触发AI生成| AI
    AI -.->|生成新内容| API
    API -.->|保存新记录<br/>feedbackScore=0| DB
    
    %% 样式设置
    style API fill:#e3f2fd,stroke:#1976d2,color:#0d47a1
    style Cleaner fill:#fff3e0,stroke:#f57c00,color:#e65100
    style DB fill:#f3e5f5,stroke:#7b1fa2,color:#4a148c
    style AI fill:#e8f5e8,stroke:#388e3c,color:#1b5e20
    style User fill:#ffebee,stroke:#d32f2f,color:#b71c1c
```