# 需求："用户账户管理补全" 的函数契约补间链 (V1.0 - 极简安全版)

## 0. 依赖关系与影响分析

- [新增] `cloudflare/workers/auth/src/controllers/logout.controller.ts`: 新增登出控制器
- [新增] `cloudflare/workers/auth/src/controllers/account.controller.ts`: 新增账户管理控制器
- [修改] `cloudflare/workers/auth/src/index.ts`: 添加新的API路由
- [修改] `iOS/Packages/AuthDomain/Sources/AuthDomain/AuthService.swift`: 完善前端登出实现
- [重用] `cloudflare/workers/auth/src/auth/session.service.ts`: 使用现有的Session撤销功能
- [重用] `cloudflare/workers/auth/src/middleware/auth.middleware.ts`: 使用现有的认证中间件

## 1. 项目文件结构概览 (Project File Structure Overview)

```
cloudflare/workers/auth/
├── src/
│   ├── controllers/
│   │   ├── auth.controller.ts                      # [保留] 现有认证控制器
│   │   ├── logout.controller.ts                    # [新增] 登出控制器
│   │   └── account.controller.ts                   # [新增] 账户管理控制器
│   ├── auth/
│   │   ├── session.service.ts                      # [重用] 现有Session服务
│   │   └── auth.service.ts                         # [修改] 添加账户删除功能
│   ├── types/
│   │   └── auth-types.ts                           # [修改] 添加新的类型定义
│   └── index.ts                                    # [修改] 添加新路由
iOS/Packages/AuthDomain/
└── Sources/AuthDomain/
    └── AuthService.swift                           # [修改] 完善前端登出实现
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/auth/account-management-completion`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/05-account-management
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/05-account-management -b feature/auth/account-management-completion dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(auth): 添加用户登出API端点
- [ ] feat(auth): 添加注销所有设备API端点
- [ ] feat(auth): 添加账户删除API端点
- [ ] feat(auth): 完善前端AuthService登出实现
- [ ] test(auth): 添加账户管理功能测试用例

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 单设备登出处理器

- 职责: 处理用户从当前设备登出，撤销当前Session
- 函数签名: `handleLogout(request: Request, env: AuthWorkerEnv) -> Response`
- 所在文件: `cloudflare/workers/auth/src/controllers/logout.controller.ts`

>>>>> 输入 (Input): LogoutRequest

```typescript
interface LogoutRequest {
  method: 'POST';
  url: '/api/v1/auth/logout';
  headers: {
    'Authorization': string;          // 格式: "Bearer <sessionId>"
    'Content-Type': 'application/json';
  };
  body?: {
    reason?: 'user_initiated' | 'security' | 'admin';  // 可选的登出原因
  };
}
```

<<<<< 输出 (Output): LogoutResponse

```typescript
// 成功响应
interface LogoutSuccessResponse {
  success: true;
  message: string;                    // "已成功登出"
  sessionRevoked: boolean;            // true
  timestamp: string;                  // ISO时间戳
}

// 失败响应
interface LogoutErrorResponse {
  success: false;
  error: 'UNAUTHORIZED' | 'SESSION_NOT_FOUND' | 'ALREADY_LOGGED_OUT';
  message: string;
  timestamp: string;
}
```

---

### [FC-02]: 全设备登出处理器

- 职责: 撤销用户在所有设备上的Session，实现"退出所有设备"功能
- 函数签名: `handleLogoutAll(request: Request, env: AuthWorkerEnv) -> Response`
- 所在文件: `cloudflare/workers/auth/src/controllers/logout.controller.ts`

>>>>> 输入 (Input): LogoutAllRequest

```typescript
interface LogoutAllRequest {
  method: 'POST';
  url: '/api/v1/auth/logout-all';
  headers: {
    'Authorization': string;          // 格式: "Bearer <sessionId>"
    'Content-Type': 'application/json';
  };
  body?: {
    reason?: 'security_concern' | 'device_lost' | 'user_request';
  };
}
```

<<<<< 输出 (Output): LogoutAllResponse

```typescript
// 成功响应
interface LogoutAllSuccessResponse {
  success: true;
  message: string;                    // "已从所有设备登出"
  sessionsRevoked: number;            // 撤销的Session数量
  timestamp: string;
}

// 失败响应
interface LogoutAllErrorResponse {
  success: false;
  error: 'UNAUTHORIZED' | 'USER_NOT_FOUND' | 'SYSTEM_ERROR';
  message: string;
  timestamp: string;
}
```

---

### [FC-03]: 账户删除处理器

- 职责: 处理Apple要求的账户删除功能，清理Session、用户数据和生词本，保留购买记录
- 函数签名: `handleAccountDeletion(request: Request, env: AuthWorkerEnv) -> Response`
- 所在文件: `cloudflare/workers/auth/src/controllers/account.controller.ts`

>>>>> 输入 (Input): AccountDeletionRequest

```typescript
interface AccountDeletionRequest {
  method: 'DELETE';
  url: '/api/v1/users/me';
  headers: {
    'Authorization': string;          // 格式: "Bearer <sessionId>"
    'Content-Type': 'application/json';
  };
  body: {
    confirmation: 'DELETE_MY_ACCOUNT'; // 必需的确认字符串
  };
}
```

**中间状态 (Intermediate State): DeletionProgress**

```typescript
interface DeletionProgress {
  userId: string;
  startTime: string;
  steps: {
    sessionRevocation: 'PENDING' | 'COMPLETED' | 'FAILED';
    bookmarkCleanup: 'PENDING' | 'COMPLETED' | 'FAILED';
    userDeletion: 'PENDING' | 'COMPLETED' | 'FAILED';
  };
  currentStep: 'SESSIONS' | 'BOOKMARKS' | 'USER_RECORD' | 'COMPLETED';
}
```

<<<<< 输出 (Output): AccountDeletionResponse

```typescript
// 成功响应
interface AccountDeletionSuccessResponse {
  success: true;
  message: string;                    // "账户已成功删除"
  dataCleared: {
    sessions: number;                 // 清理的Session数量
    bookmarks: number;                // 清理的生词本数量
    userRecord: boolean;              // 用户记录是否已删除
  };
  timestamp: string;
}

// 失败响应
interface AccountDeletionErrorResponse {
  success: false;
  error: 'UNAUTHORIZED' | 'INVALID_CONFIRMATION' | 'DELETION_FAILED';
  message: string;
  timestamp: string;
}
```

---

### [FC-04]: 前端登出服务增强器

- 职责: 完善前端AuthService的登出功能，确保调用后端API撤销Session
- 函数签名: `signOut(logoutAll: Bool = false) async throws -> Void`
- 所在文件: `iOS/Packages/AuthDomain/Sources/AuthDomain/AuthService.swift`

>>>>> 输入 (Input): SignOutRequest

```swift
struct SignOutRequest {
    let logoutAll: Bool                 // false: 仅当前设备, true: 所有设备
    let currentSessionId: String?       // 当前Session ID
    let reason: LogoutReason?           // 可选的登出原因
}

enum LogoutReason {
    case userInitiated
    case security
    case deviceLost
}
```

<<<<< 输出 (Output): SignOutResult

```swift
// 成功情况
struct SignOutSuccess {
    let localCleared: Bool              // 本地数据是否已清理
    let serverNotified: Bool            // 服务器是否已通知
    let sessionsRevoked: Int            // 撤销的Session数量
}

// 失败情况
enum SignOutError: Error {
    case networkUnavailable
    case serverError(String)
    case localClearFailed
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/workers/auth/src/index.ts
cloudflare/workers/auth/src/controllers/auth.controller.ts
cloudflare/workers/auth/src/auth/session.service.ts
cloudflare/workers/auth/src/auth/auth.service.ts
cloudflare/workers/auth/src/middleware/auth.middleware.ts
cloudflare/workers/auth/src/types/auth-types.ts
iOS/Packages/AuthDomain/Sources/AuthDomain/AuthService.swift
iOS/Packages/SharedModels/Sources/SharedModels/SessionManager.swift
</context_files>

## 6. 数据库操作详细规范

### 6.1 Session数据库操作 (AUTH_DB)

```sql
-- [FC-01] 单设备登出 - Session撤销
UPDATE sessions
SET
  is_active = false,
  revoked_at = CURRENT_TIMESTAMP,
  revoked_reason = 'USER_LOGOUT'
WHERE session_id = ? AND user_id = ? AND is_active = true;

-- [FC-02] 全设备登出 - 批量Session撤销
UPDATE sessions
SET
  is_active = false,
  revoked_at = CURRENT_TIMESTAMP,
  revoked_reason = 'USER_LOGOUT_ALL'
WHERE user_id = ? AND is_active = true;

-- 查询撤销的Session数量
SELECT COUNT(*) as revoked_count
FROM sessions
WHERE user_id = ? AND revoked_at IS NOT NULL;
```

### 6.2 账户删除数据库操作序列 (多数据库事务)

```sql
-- [FC-03] 步骤1: 撤销所有Session (AUTH_DB)
UPDATE sessions
SET
  is_active = false,
  revoked_at = CURRENT_TIMESTAMP,
  revoked_reason = 'ACCOUNT_DELETION'
WHERE user_id = ? AND is_active = true;

-- [FC-03] 步骤2: 清理生词本数据 (BOOKMARKS_DB)
DELETE FROM bookmarks WHERE user_id = ?;

-- [FC-03] 步骤3: 删除用户记录 (AUTH_DB)
DELETE FROM users WHERE user_id = ?;

-- 注意：购买记录 (purchases表) 保持不变，符合Apple要求
```

### 6.3 数据库事务原子性保证

```typescript
// 账户删除的原子性操作
async function executeAccountDeletion(userId: string, env: AuthWorkerEnv): Promise<DeletionResult> {
    const progress: DeletionProgress = {
        userId,
        startTime: new Date().toISOString(),
        steps: {
            sessionRevocation: 'PENDING',
            bookmarkCleanup: 'PENDING',
            userDeletion: 'PENDING'
        },
        currentStep: 'SESSIONS'
    };

    try {
        // 步骤1: 撤销所有Session
        progress.currentStep = 'SESSIONS';
        const sessionCount = await revokeAllUserSessions(userId, env.AUTH_DB);
        progress.steps.sessionRevocation = 'COMPLETED';

        // 步骤2: 清理生词本数据
        progress.currentStep = 'BOOKMARKS';
        const bookmarkCount = await deleteUserBookmarks(userId, env.BOOKMARKS_DB);
        progress.steps.bookmarkCleanup = 'COMPLETED';

        // 步骤3: 删除用户记录
        progress.currentStep = 'USER_RECORD';
        await deleteUserRecord(userId, env.AUTH_DB);
        progress.steps.userDeletion = 'COMPLETED';

        progress.currentStep = 'COMPLETED';

        return {
            success: true,
            dataCleared: {
                sessions: sessionCount,
                bookmarks: bookmarkCount,
                userRecord: true
            }
        };

    } catch (error) {
        // 记录失败状态，但不回滚（避免复杂性）
        throw new Error(`账户删除失败在步骤 ${progress.currentStep}: ${error.message}`);
    }
}
```

## 7. 核心业务流程伪代码

```typescript
// [FC-01] 单设备登出流程
async function handleLogout(request: Request, env: AuthWorkerEnv): Promise<Response> {
    const user = await authenticate(request, env);
    const sessionId = extractSessionId(request);

    // 数据库操作：撤销单个Session
    const result = await env.AUTH_DB.prepare(`
        UPDATE sessions
        SET is_active = false, revoked_at = CURRENT_TIMESTAMP
        WHERE session_id = ? AND user_id = ?
    `).bind(sessionId, user.id).run();

    return Response.json({ success: result.success });
}

// [FC-02] 全设备登出流程
async function handleLogoutAll(request: Request, env: AuthWorkerEnv): Promise<Response> {
    const user = await authenticate(request, env);

    // 数据库操作：撤销用户所有Session
    const result = await env.AUTH_DB.prepare(`
        UPDATE sessions
        SET is_active = false, revoked_at = CURRENT_TIMESTAMP
        WHERE user_id = ?
    `).bind(user.id).run();

    return Response.json({
        success: true,
        count: result.changes
    });
}

// [FC-03] 账户删除流程
async function handleAccountDeletion(request: Request, env: AuthWorkerEnv): Promise<Response> {
    const user = await authenticate(request, env);
    const { confirmation } = await request.json();

    if (confirmation !== 'DELETE_MY_ACCOUNT') {
        throw new Error('INVALID_CONFIRMATION');
    }

    // 执行原子性删除操作
    const result = await executeAccountDeletion(user.id, env);

    return Response.json({
        success: true,
        message: "账户已成功删除",
        dataCleared: result.dataCleared,
        timestamp: new Date().toISOString()
    });
}
```
