# KDD-015: 用户账户管理补全关键帧可视化

## 用户账户管理数据流程图（优化版）

```mermaid
graph TD
    A[用户登出请求] -->|FC-01| B{登出类型?}
    B -->|单设备| C[当前Session撤销]
    B -->|所有设备| D[用户所有Session撤销]
    C --> E[登出成功响应]
    D --> F[全设备登出响应]

    G[账户删除请求] -->|FC-03| H{确认字符串正确?}
    H -->|是| I[开始数据清理流程]
    H -->|否| J[确认错误响应]
    I --> K[步骤1: Session全部撤销]
    K --> L[步骤2: 生词本数据清理]
    L --> M[步骤3: 用户记录删除]
    M --> N[购买记录保护<br/>保持不变]
    N --> O[账户删除成功]

    P[前端登出调用] -->|FC-04| Q[后端API调用]
    Q --> R{网络可用?}
    R -->|是| S[Session撤销成功]
    R -->|否| T[网络错误]
    S --> U[本地数据清理]
    T --> U
    U --> V[登出完成]

    %% 数据结构关键帧
    A1[LogoutRequest<br/>sessionId] -.->|输入| A
    E1[LogoutResponse<br/>success] -.->|输出| E
    G1[AccountDeletionRequest<br/>confirmation] -.->|输入| G
    O1[DeletionResponse<br/>dataCleared<br/>sessions/bookmarks/userRecord] -.->|输出| O
    P1[SignOutRequest<br/>logoutAll<br/>sessionId] -.->|输入| P
    V1[SignOutResult<br/>localCleared<br/>serverNotified] -.->|输出| V

    %% 样式
    classDef input fill:#ffebee,stroke:#d32f2f,color:#000
    classDef output fill:#e8f5e8,stroke:#4caf50,color:#000
    classDef process fill:#e3f2fd,stroke:#2196f3,color:#000
    classDef decision fill:#fff3e0,stroke:#ff9800,color:#000
    classDef error fill:#ffcdd2,stroke:#f44336,color:#000
    classDef success fill:#c8e6c9,stroke:#4caf50,color:#000
    classDef preserve fill:#fff3e0,stroke:#ff9800,color:#000

    class A,A1,G,G1,P,P1 input
    class E,E1,F,O,O1,V,V1 output
    class C,D,I,K,L,M,Q,S,U process
    class B,H,R decision
    class J,T error
    class O,V success
    class N preserve
```

## Session 生命周期管理图

```mermaid
stateDiagram-v2
    [*] --> Active: 用户登录
    Active --> Revoking: 单设备登出
    Active --> RevokingAll: 全设备登出
    Active --> Deleting: 账户删除
    
    Revoking --> Revoked: Session撤销成功
    RevokingAll --> AllRevoked: 所有Session撤销
    Deleting --> UserDeleted: 用户数据删除
    
    Revoked --> [*]: 登出完成
    AllRevoked --> [*]: 全设备登出完成
    UserDeleted --> [*]: 账户删除完成
    
    Active --> Expired: Session过期
    Expired --> [*]: 自动清理
```

## 数据库操作详细流程图

```mermaid
graph TD
    A[账户删除请求] --> B{确认字符串验证}
    B -->|正确| C[开始数据清理]
    B -->|错误| D[返回确认错误]

    C --> E[步骤1: AUTH_DB<br/>撤销所有Session]
    E --> F[UPDATE sessions<br/>SET is_active = false]
    F --> G[步骤2: BOOKMARKS_DB<br/>清理生词本数据]
    G --> H[DELETE FROM bookmarks<br/>WHERE user_id = ?]
    H --> I[步骤3: AUTH_DB<br/>删除用户记录]
    I --> J[DELETE FROM users<br/>WHERE user_id = ?]
    J --> K[返回清理统计]

    L[购买记录表] --> M[保持不变<br/>符合Apple要求]

    %% 数据库标识
    N[(AUTH_DB<br/>sessions表<br/>users表)] -.-> E
    N -.-> I
    O[(BOOKMARKS_DB<br/>bookmarks表)] -.-> G
    P[(不涉及<br/>purchases表)] -.-> M

    %% 样式
    classDef database fill:#e1f5fe,stroke:#0277bd,color:#000
    classDef operation fill:#f3e5f5,stroke:#7b1fa2,color:#000
    classDef success fill:#e8f5e8,stroke:#4caf50,color:#000
    classDef error fill:#ffcdd2,stroke:#f44336,color:#000
    classDef preserve fill:#fff3e0,stroke:#ff9800,color:#000

    class N,O,P database
    class E,F,G,H,I,J operation
    class K success
    class D error
    class L,M preserve
```

## 数据库事务原子性保证图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 账户删除API
    participant AuthDB as AUTH_DB
    participant BookmarkDB as BOOKMARKS_DB

    Client->>API: DELETE /api/v1/users/me
    API->>API: 验证确认字符串

    Note over API: 开始原子性删除流程

    API->>AuthDB: 1. 撤销所有Session
    AuthDB-->>API: 返回撤销数量

    API->>BookmarkDB: 2. 删除生词本数据
    BookmarkDB-->>API: 返回删除数量

    API->>AuthDB: 3. 删除用户记录
    AuthDB-->>API: 确认删除成功

    Note over API: 购买记录保持不变

    API-->>Client: 返回删除统计
```

## 关键帧数据结构详解

### 关键帧 A: 登出请求（简化版）
- **类型**: 输入关键帧
- **数据结构**: LogoutRequest
- **核心字段**:
  - `sessionId`: 当前用户的Session ID
  - `logoutAll`: 是否登出所有设备（布尔值）

### 关键帧 E: 登出响应（简化版）
- **类型**: 输出关键帧
- **数据结构**: LogoutResponse
- **核心字段**:
  - `success`: 操作是否成功
  - `count`: 撤销的Session数量（全设备登出时）

### 关键帧 G: 账户删除请求（Apple合规版）
- **类型**: 输入关键帧
- **数据结构**: AccountDeletionRequest
- **核心字段**:
  - `confirmation`: 必需的确认字符串 "DELETE_MY_ACCOUNT"
  - `sessionId`: 用于认证的Session ID（从Header提取）

### 关键帧 O: 账户删除响应（购买保护版）
- **类型**: 输出关键帧
- **数据结构**: AccountDeletionResponse
- **核心字段**:
  - `success`: 删除是否成功
  - `dataCleared`: 清理的数据统计
    - `sessions`: 清理的Session数量
    - `bookmarks`: 清理的生词本条目数量
    - `userRecord`: 用户记录是否已删除（布尔值）
  - **注意**: 购买记录完全保留，不在响应中体现

### 关键帧 P: 前端登出请求
- **类型**: 输入关键帧
- **数据结构**: SignOutRequest
- **核心字段**:
  - `logoutAll`: 是否登出所有设备
  - `currentSessionId`: 当前Session ID
  - `reason`: 登出原因枚举

### 关键帧 V: 前端登出结果
- **类型**: 输出关键帧
- **数据结构**: SignOutResult
- **核心字段**:
  - `localCleared`: 本地数据是否已清理
  - `serverNotified`: 服务器是否已通知
  - `sessionsRevoked`: 撤销的Session数量

## 安全性考虑

### 权限验证流程
1. **Session验证**: 确保请求来自有效的认证用户
2. **用户匹配**: 确保用户只能操作自己的账户
3. **确认机制**: 账户删除需要明确的确认字符串

### 数据保护措施
1. **软删除**: Session撤销使用软删除，保留审计记录
2. **级联清理**: 账户删除时确保相关数据完全清理
3. **原子操作**: 确保数据清理操作的原子性

## 错误处理分支（简化版）

### 登出错误处理
- `UNAUTHORIZED`: Session无效或已过期
- `ALREADY_LOGGED_OUT`: Session已被撤销（幂等处理）

### 账户删除错误处理
- `INVALID_CONFIRMATION`: 确认字符串不匹配
- `DELETION_FAILED`: 数据删除过程中发生错误，包含具体失败步骤
  - 失败在 `SESSIONS` 步骤
  - 失败在 `BOOKMARKS` 步骤
  - 失败在 `USER_RECORD` 步骤

### 前端错误处理
- `networkUnavailable`: 网络连接失败（仍执行本地清理）
- `serverError`: 服务器返回错误（仍执行本地清理）

## 数据保护确认

### Apple合规保证
- ✅ **账户删除功能**: 提供完整的账户删除API
- ✅ **购买记录保护**: purchases表数据完全保留
- ✅ **用户数据清理**: Session、生词本、用户记录完全清理
- ✅ **确认机制**: 需要明确的确认字符串验证

### 数据库操作确认
- **AUTH_DB**: Session软删除 + 用户记录硬删除
- **BOOKMARKS_DB**: 生词本数据硬删除
- **purchases表**: 完全不涉及，保持原状
