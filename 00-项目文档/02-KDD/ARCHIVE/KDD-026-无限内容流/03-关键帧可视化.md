# 重新设计的无限内容流系统关键帧可视化

## V5.0 架构数据流可视化

### 整体系统架构图

```mermaid
graph TB
    subgraph "用户交互层"
        A[WordPageContainer] --> B[PullUpGestureDetector]
        A --> C[页面1: 深思语境]
        A --> D[页面2: 例句]
        A --> E[页面3: 场景]
        A --> F[页面4: 用法]
        A --> G[页面5: 同义词]
    end

    subgraph "服务层"
        H[RecommendationArrayManager]
        I[JITPreloader]
    end

    subgraph "数据层"
        J[推荐数组]
        K[缓存系统]
        L[API服务]
    end

    B --> H
    H --> J
    H --> I
    I --> K
    I --> L
    A --> H
    A --> I
```

### 用户交互流程关键帧

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as PullUpGestureDetector
    participant R as RecommendationArrayManager
    participant J as JITPreloader
    participant W as WordPageContainer

    Note over U,W: 用户在任意页面浏览单词

    U->>P: 在页面底部向上拉动
    P->>P: 检测拖拽距离

    alt 30px阈值
        P->>W: 显示"↑ 探索更多"提示
    end

    alt 80px阈值
        P->>R: 触发获取下一个单词
        R->>R: 从推荐数组获取下一个
        R->>J: 检查是否已预加载

        alt 已预加载
            J->>W: 返回缓存的单词数据
            W->>W: 使用现有动画切换单词
        else 未预加载
            J->>J: 异步加载单词数据
            J->>W: 返回加载完成的数据
            W->>W: 使用现有动画切换单词
        end

        R->>J: 预加载下一个单词
    end
```

### 推荐数组构建流程

```mermaid
flowchart TD
    A[用户触发推荐] --> B{推荐类型}

    B -->|每日一词| C[获取每日一词]
    B -->|搜索词| D[获取搜索词]
    B -->|生词本| E[获取收藏生词]

    C --> F[提取relatedConcepts]
    D --> F
    E --> G[为每个生词提取concepts]

    F --> H[构建推荐数组]
    G --> I[构建混合推荐数组]

    H --> J[数组: 起始词 + concepts]
    I --> K[数组: 生词A + conceptsA + 生词B + conceptsB]

    J --> L[RecommendationArrayManager]
    K --> L

    L --> M[触发JIT预加载第一个单词]
```

### JIT预加载状态变化

```mermaid
stateDiagram-v2
    [*] --> Idle: 初始状态

    Idle --> Loading: 收到预加载请求
    Loading --> Caching: 数据加载完成
    Caching --> Ready: 缓存到内存
    Ready --> Serving: 用户请求数据
    Serving --> Idle: 数据已提供

    Loading --> Error: 加载失败
    Error --> Idle: 重试或跳过

    Ready --> Expired: 缓存过期
    Expired --> Idle: 清理缓存
```

### 三种推荐流模式对比

```mermaid
graph LR
    subgraph "每日一词流"
        A1[每日一词] --> A2[concept1]
        A2 --> A3[concept2]
        A3 --> A4[concept3]
    end

    subgraph "搜索词流"
        B1[搜索词] --> B2[related1]
        B2 --> B3[related2]
        B3 --> B4[related3]
    end

    subgraph "生词本流"
        C1[生词A] --> C2[conceptA1]
        C2 --> C3[生词B]
        C3 --> C4[conceptB1]
        C4 --> C5[生词C]
    end
```

### 组件解耦关系图

```mermaid
graph TD
    subgraph "业务逻辑层"
        A[每日一词业务]
        B[搜索业务]
        C[生词本业务]
    end

    subgraph "服务层"
        D[RecommendationArrayManager]
        E[JITPreloader]
    end

    subgraph "UI层"
        F[WordPageContainer]
        G[PullUpGestureDetector]
    end

    subgraph "现有基础设施"
        H[单词展示逻辑]
        I[动画系统]
        J[音频播放]
    end

    A --> D
    B --> D
    C --> D

    D --> E
    D --> F

    F --> G
    F --> H
    F --> I
    F --> J

    E -.-> H

    style D fill:#e1f5fe
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style H fill:#f3e5f5
    style I fill:#f3e5f5
    style J fill:#f3e5f5
```

## 数据结构关键帧变化

### RecommendationArrayManager状态变化

```mermaid
stateDiagram-v2
    [*] --> Empty: 初始化

    Empty --> Building: 开始构建推荐数组
    Building --> Ready: 数组构建完成
    Ready --> Navigating: 用户开始导航
    Navigating --> Extending: 需要扩展数组
    Extending --> Ready: 扩展完成

    Navigating --> Exhausted: 数组用完
    Exhausted --> Building: 重新构建

    Building --> Error: 构建失败
    Error --> Empty: 重置状态
```

### 用户体验状态流转

```mermaid
journey
    title 用户无限内容流体验旅程
    section 开始探索
      打开单词页面: 5: 用户
      浏览当前单词: 4: 用户

    section 触发流程
      页面底部上拉: 5: 用户
      看到探索提示: 4: 用户
      继续上拉触发: 5: 用户

    section 内容切换
      新单词出现: 5: 用户
      使用熟悉动画: 5: 用户
      无等待加载: 5: 用户

    section 持续探索
      继续浏览新词: 4: 用户
      再次上拉切换: 5: 用户
      发现相关概念: 5: 用户
```