# 002 - Azure多Key轮换配置指南

## 📋 概述

本文档详细说明如何配置和使用多个Azure TTS API密钥实现负载均衡和额度轮换，解决单个账号额度不足的问题。

---

## 🎯 解决的问题

### 单Key限制
- **额度限制**: 单个Azure账号的TTS额度可能不够大批量处理
- **速率限制**: 单个Key可能有API调用频率限制
- **单点故障**: 一个Key失效会影响整个服务

### 多Key优势
- **额度叠加**: 多个账号的额度可以累加使用
- **负载分散**: 请求分散到多个Key，降低单个Key压力
- **容错能力**: 某个Key失效不会影响整体服务
- **简单实现**: 无需复杂的账号关联，只需要Key即可

---

## 🔧 配置方法

### 1. wrangler.toml配置

```toml
# 方式1: 多Key配置（推荐）
[vars]
AZURE_TTS_KEYS = "key1,key2,key3"  # 逗号分隔的多个Key
AZURE_TTS_REGION = "eastus"

# 方式2: 单Key配置（向后兼容）
[vars]
AZURE_TTS_KEY = "single_key_here"
AZURE_TTS_REGION = "eastus"
```

### 2. 环境特定配置

```toml
# 开发环境 - 使用单个Key
[env.development.vars]
AZURE_TTS_KEYS = "dev_key_only"

# 生产环境 - 使用多个Key
[env.production.vars]
AZURE_TTS_KEYS = "prod_key1,prod_key2,prod_key3"
```

---

## 🔄 轮换机制

### 轮换算法

```mermaid
flowchart TD
    A[API请求] --> B[获取Key列表]
    B --> C{Key数量}
    C -->|单个| D[直接使用]
    C -->|多个| E[轮换选择]
    E --> F[currentIndex % keyCount]
    F --> G[更新索引]
    G --> H[返回选中的Key]
    D --> I[执行API调用]
    H --> I
    
    classDef processStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef decisionStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef resultStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B,E,F,G processStyle
    class C decisionStyle
    class D,H,I resultStyle
```

### 实现代码

```typescript
// 全局轮换状态
let currentKeyIndex = 0;

function getNextAzureKey(env: Env): string {
  const keys = getAzureKeys(env);
  
  if (keys.length === 1) {
    return keys[0];
  }
  
  // 轮换逻辑
  const key = keys[currentKeyIndex % keys.length];
  currentKeyIndex = (currentKeyIndex + 1) % keys.length;
  
  console.log(`[TTS Worker] 使用Azure Key ${currentKeyIndex}/${keys.length} (轮换)`);
  return key;
}
```

---

## 📊 负载分布示例

### 3个Key的请求分布

```mermaid
gantt
    title Azure Key使用时间线
    dateFormat X
    axisFormat %s
    
    section Key 1
    请求1    :0, 100
    请求4    :300, 400
    请求7    :600, 700
    
    section Key 2
    请求2    :100, 200
    请求5    :400, 500
    请求8    :700, 800
    
    section Key 3
    请求3    :200, 300
    请求6    :500, 600
    请求9    :800, 900
```

### 负载均衡效果

| Key编号 | 请求数量 | 使用率 | 额度消耗 |
|---------|----------|--------|----------|
| Key 1   | 33%      | 均衡   | 1/3      |
| Key 2   | 33%      | 均衡   | 1/3      |
| Key 3   | 34%      | 均衡   | 1/3      |

---

## 🛡️ 容错机制

### 错误处理流程

```mermaid
flowchart TD
    A[使用当前Key] --> B{API调用成功?}
    B -->|成功| C[返回结果]
    B -->|失败| D{是否认证错误?}
    D -->|是| E[标记Key失效]
    D -->|否| F[记录临时错误]
    E --> G[切换到下一个Key]
    F --> H[重试当前Key]
    G --> I[重新执行请求]
    H --> I
    
    classDef successStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    
    class C successStyle
    class D,E,F errorStyle
    class A,G,H,I processStyle
```

### 未来增强功能

```typescript
// 可以添加的增强功能
interface KeyStatus {
  key: string;
  isActive: boolean;
  errorCount: number;
  lastError?: Date;
}

// Key健康检查
function checkKeyHealth(key: string): Promise<boolean> {
  // 实现Key可用性检查
}

// 智能Key选择
function selectBestKey(keys: KeyStatus[]): string {
  // 选择错误最少的可用Key
}
```

---

## 📝 配置最佳实践

### 1. Key获取建议

```bash
# 创建多个Azure账号
# 每个账号创建TTS服务
# 获取各自的订阅密钥

# 示例Key格式
Key1: "1234567890abcdef1234567890abcdef"
Key2: "abcdef1234567890abcdef1234567890"
Key3: "567890abcdef1234567890abcdef1234"
```

### 2. 配置格式

```toml
# 正确格式 - 逗号分隔，无空格
AZURE_TTS_KEYS = "key1,key2,key3"

# 错误格式 - 包含空格
AZURE_TTS_KEYS = "key1, key2, key3"  # 会被trim处理，但不推荐

# 错误格式 - 使用分号
AZURE_TTS_KEYS = "key1;key2;key3"    # 不支持
```

### 3. 环境分离

```toml
# 开发环境 - 使用免费额度Key
[env.development.vars]
AZURE_TTS_KEYS = "free_tier_key"

# 测试环境 - 使用少量付费Key
[env.staging.vars]
AZURE_TTS_KEYS = "test_key1,test_key2"

# 生产环境 - 使用多个生产Key
[env.production.vars]
AZURE_TTS_KEYS = "prod_key1,prod_key2,prod_key3,prod_key4"
```

---

## 🔍 监控和调试

### 日志输出

```
[TTS Worker] 使用Azure Key 1/3 (轮换)
[TTS Worker] 使用Azure Key 2/3 (轮换)
[TTS Worker] 使用Azure Key 3/3 (轮换)
[TTS Worker] 使用Azure Key 1/3 (轮换)  # 循环开始
```

### 监控指标

- **Key使用分布**: 确保负载均衡
- **错误率**: 监控各Key的成功率
- **额度消耗**: 跟踪各账号的额度使用情况
- **响应时间**: 不同Key的性能差异

---

## ✅ 实施步骤

### 1. 准备阶段
- [ ] 创建多个Azure账号
- [ ] 为每个账号开通TTS服务
- [ ] 获取各自的订阅密钥
- [ ] 验证Key的有效性

### 2. 配置阶段
- [ ] 更新wrangler.toml配置
- [ ] 设置AZURE_TTS_KEYS环境变量
- [ ] 部署到开发环境测试
- [ ] 验证轮换机制工作正常

### 3. 部署阶段
- [ ] 部署到生产环境
- [ ] 监控Key使用情况
- [ ] 验证负载均衡效果
- [ ] 设置监控和告警

---

## 🎯 总结

通过多Key轮换机制，TTS Worker可以：

- **突破单账号限制**: 多个账号额度叠加
- **提高服务可靠性**: 分散风险，避免单点故障
- **简化实施复杂度**: 无需复杂的账号关联
- **保持向后兼容**: 支持单Key和多Key两种模式

这种方案特别适合需要大量TTS处理的场景，通过简单的配置就能实现企业级的负载均衡和容错能力。
