# 003 - Azure TTS批处理开发指南

## 📋 概述

本文档记录Azure TTS批处理API的完整开发经验，包括技术方案、核心难点、常见陷阱和最佳实践。基于实际开发过程中遇到的问题和解决方案。

---

## 🏗️ 技术架构方案

### 整体架构设计

```mermaid
graph TB
    subgraph "📝 任务提交层"
        A[Python脚本] --> B[HTTP批量提交]
        B --> C[Cloudflare Worker]
    end
    
    subgraph "🔄 批处理管理层"
        C --> D[任务存储D1]
        D --> E[定时批处理器]
        E --> F[Azure TTS API]
    end
    
    subgraph "📦 结果处理层"
        F --> G[ZIP下载]
        G --> H[JSZip解压]
        H --> I[文件映射]
        I --> J[R2上传]
    end
    
    subgraph "📊 状态管理层"
        J --> K[状态更新]
        K --> L[完成通知]
    end
    
    classDef submitStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef resultStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef statusStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B,C submitStyle
    class D,E,F processStyle
    class G,H,I,J resultStyle
    class K,L statusStyle
```

### 核心技术选型

| 组件 | 技术选择 | 原因 |
|------|----------|------|
| **Worker平台** | Cloudflare Workers | 边缘计算、低延迟、内置定时任务 |
| **数据库** | D1 SQLite | 轻量级、事务支持、与Worker集成 |
| **存储** | R2 Object Storage | 成本低、CDN集成、高可用 |
| **ZIP处理** | JSZip | 纯JS实现、Worker兼容、功能完整 |
| **API版本** | 2024-04-01 | 最新稳定版、功能最全 |

---

## 🚨 核心技术难点

### 1. Azure API版本兼容性问题

#### 问题描述
Azure TTS批处理API在不同版本间有重大差异，容易踩坑。

#### 版本对比

```mermaid
graph LR
    subgraph "❌ 旧版本 (2023-12-01)"
        A1[POST方法] --> A2[不同的请求格式]
        A2 --> A3[有限的功能]
    end
    
    subgraph "✅ 新版本 (2024-04-01)"
        B1[PUT方法] --> B2[标准化请求格式]
        B2 --> B3[完整功能支持]
    end
    
    classDef oldStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef newStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A1,A2,A3 oldStyle
    class B1,B2,B3 newStyle
```

#### 解决方案
```typescript
// 正确的API调用格式 (2024-04-01)
const url = `https://${region}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`;

const response = await fetch(url, {
  method: 'PUT',  // 关键：必须使用PUT，不是POST
  headers: {
    'Ocp-Apim-Subscription-Key': azureKey,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    description: 'SenseWord TTS batch processing',
    inputKind: 'PlainText',  // 固定值
    inputs: tasks.map(task => ({ content: task.text })),
    synthesisConfig: {
      voice: voiceName
    }
  })
});
```

### 2. ZIP文件解压和映射逻辑

#### 问题描述
Azure返回的ZIP文件结构复杂，需要精确的映射逻辑。

#### ZIP文件结构
```
batch_result.zip
├── summary.json          # 核心映射文件
├── 0001.wav             # 音频文件1
├── 0002.wav             # 音频文件2
└── 0003.wav             # 音频文件3
```

#### summary.json格式
```json
{
  "results": [
    {
      "contents": ["apple pronunciation"],
      "audioFileName": "0001.wav",
      "status": "Succeeded"
    },
    {
      "contents": ["banana pronunciation"], 
      "audioFileName": "0002.wav",
      "status": "Succeeded"
    }
  ]
}
```

#### 映射逻辑实现
```typescript
// 关键：双重映射逻辑
const contentToFileMap = new Map<string, string>();
const textToTtsIdMap = new Map<string, string>();

// 第一步：建立content到文件名的映射
summaryData.results.forEach(result => {
  if (result.contents && result.contents.length > 0) {
    contentToFileMap.set(result.contents[0], result.audioFileName);
  }
});

// 第二步：建立text到ttsId的映射
tasks.forEach(task => {
  textToTtsIdMap.set(task.text, task.ttsId);
});

// 第三步：通过content找到真实的ttsId
let realTtsId = mapping.ttsId;
if (textToTtsIdMap.has(mapping.ttsId)) {
  realTtsId = textToTtsIdMap.get(mapping.ttsId)!;
}
```

### 3. 同类型任务的Content冲突

#### 问题描述
同一个单词的不同音标类型会产生相同的文本内容，导致映射冲突。

#### 冲突示例
```json
{
  "tasks": [
    {
      "ttsId": "id1",
      "text": "aback",           // 相同文本
      "type": "phonetic_bre"
    },
    {
      "ttsId": "id2", 
      "text": "aback",           // 相同文本
      "type": "phonetic_name"
    }
  ]
}
```

#### 解决方案：按类型分组
```typescript
// 按类型分组，避免冲突
function groupTasksByType(tasks: any[]): Record<string, any[]> {
  const groups: Record<string, any[]> = {};
  
  for (const task of tasks) {
    const type = task.type || 'default';
    if (!groups[type]) groups[type] = [];
    groups[type].push(task);
  }
  
  return groups;
}

// 为每个类型创建独立的批处理
for (const [type, tasks] of Object.entries(groupedTasks)) {
  const azureRequest = buildAzureBatchRequest(tasks, type);
  // 每个类型使用不同的语音模型
}
```

---

## 🕳️ 常见陷阱和解决方案

### 1. 文件大小为0B的问题

#### 陷阱描述
上传到R2的文件大小为0B，但下载的ZIP文件正常。

#### 根本原因
```typescript
// ❌ 错误做法：直接上传ZIP文件
await env.AUDIO_BUCKET.put(key, zipBuffer);

// ✅ 正确做法：解压后上传WAV文件
const zip = await JSZip.loadAsync(zipBuffer);
const wavFile = await zip.file("0001.wav")?.async("arraybuffer");
await env.AUDIO_BUCKET.put(key, wavFile);
```

#### 解决步骤
1. **下载ZIP文件** → 验证大小正常
2. **JSZip解压缩** → 提取individual WAV文件
3. **验证WAV大小** → 确保不为0
4. **上传到R2** → 使用WAV的ArrayBuffer

### 2. 批处理状态轮询的时机问题

#### 陷阱描述
过于频繁的状态查询会被Azure限流，过于稀疏会影响用户体验。

#### 最佳实践
```typescript
// 分阶段轮询策略
const getPollingInterval = (elapsedTime: number): number => {
  if (elapsedTime < 60000) return 10000;      // 前1分钟：10秒
  if (elapsedTime < 300000) return 30000;     // 前5分钟：30秒  
  return 60000;                               // 5分钟后：60秒
};
```

### 3. 数据库状态管理混乱

#### 陷阱描述
多个状态之间的转换逻辑不清晰，容易出现状态不一致。

#### 状态流转图
```mermaid
stateDiagram-v2
    [*] --> pending : 任务提交
    pending --> processing : Worker接收
    processing --> batched : 创建Azure批处理
    batched --> succeeded : Azure处理完成
    succeeded --> completed : 下载并上传完成
    
    processing --> failed : 提交失败
    batched --> failed : Azure处理失败
    succeeded --> failed : 下载失败
    
    failed --> processing : 重试
    completed --> [*]
    
    note right of succeeded : 临时状态，表示Azure完成但未下载
    note right of completed : 最终状态，音频已上传到R2
```

#### 状态管理最佳实践
```typescript
// 使用事务确保状态一致性
await db.batch([
  db.prepare('UPDATE azure_batch_jobs SET status = ? WHERE batchId = ?')
    .bind('succeeded', batchId),
  db.prepare('UPDATE tts_tasks SET status = ? WHERE batchId = ?')
    .bind('processing', batchId)
]);
```

### 4. JSZip在Worker环境的兼容性

#### 陷阱描述
某些ZIP库在Cloudflare Workers环境中不兼容。

#### 解决方案
```typescript
// ✅ 推荐：JSZip (兼容性最好)
import JSZip from 'jszip';

// ❌ 避免：Node.js特定的库
// import AdmZip from 'adm-zip';  // 不兼容Worker
// import yauzl from 'yauzl';     // 不兼容Worker
```

---

## 🛡️ 错误处理策略

### 分层错误处理

```mermaid
graph TD
    A[API调用] --> B{HTTP状态码}
    B -->|200| C[解析响应]
    B -->|401| D[认证错误 - 检查Key]
    B -->|429| E[限流错误 - 延迟重试]
    B -->|500| F[服务错误 - 重试]
    
    C --> G{业务状态}
    G -->|Succeeded| H[处理成功]
    G -->|Failed| I[业务失败]
    G -->|Running| J[继续等待]
    
    classDef successStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef errorStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    classDef waitStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    
    class H successStyle
    class D,E,F,I errorStyle
    class J waitStyle
```

### 错误处理代码模板

```typescript
async function handleAzureResponse(response: Response, operation: string) {
  if (!response.ok) {
    const errorText = await response.text();
    
    switch (response.status) {
      case 401:
        throw new Error(`Azure认证失败: ${errorText}`);
      case 429:
        throw new Error(`Azure限流: ${errorText}`);
      case 404:
        throw new Error(`批处理不存在: ${errorText}`);
      default:
        throw new Error(`Azure API错误 (${response.status}): ${errorText}`);
    }
  }
  
  return await response.json();
}
```

---

## 📊 性能优化策略

### 1. 批处理大小优化

```typescript
// 根据任务类型调整批处理大小
const getBatchSize = (type: string): number => {
  switch (type) {
    case 'phonetic_name':
    case 'phonetic_bre':
      return 50;        // 短文本，可以更多
    case 'example_sentence':
      return 20;        // 长文本，减少数量
    case 'phrase_breakdown':
      return 30;        // 中等长度
    default:
      return 25;
  }
};
```

### 2. 并行处理优化

```typescript
// 并行上传音频文件
const uploadPromises = audioMappings.map(mapping => 
  uploadToR2(mapping.ttsId, mapping.audioBuffer, env)
);

const results = await Promise.allSettled(uploadPromises);
```

### 3. 数据库批量操作

```typescript
// 批量更新而不是逐个更新
const statements = completions.map(({ttsId, audioUrl}) => 
  db.prepare(`
    UPDATE tts_tasks 
    SET status = 'completed', audioUrl = ?, completedAt = datetime('now')
    WHERE ttsId = ?
  `).bind(audioUrl, ttsId)
);

await db.batch(statements);
```

---

## 🔍 调试和监控

### 关键日志点

```typescript
// 1. 批处理创建
console.log(`[TTS Worker] 创建批处理: ${batchId}, 任务数: ${tasks.length}, 类型: ${type}`);

// 2. 状态轮询
console.log(`[TTS Worker] 批处理状态: ${batchId} -> ${status.status}`);

// 3. ZIP下载
console.log(`[TTS Worker] ZIP下载: ${downloadUrl}, 大小: ${zipBuffer.byteLength} bytes`);

// 4. 文件映射
console.log(`[TTS Worker] 映射成功: "${content}" -> ${realTtsId}`);

// 5. 上传完成
console.log(`[TTS Worker] 上传完成: ${ttsId} -> ${audioUrl}`);
```

### 监控指标

- **批处理成功率**: 成功/总数
- **平均处理时间**: 从提交到完成的时间
- **文件大小分布**: 确保音频文件正常
- **错误类型统计**: 识别常见问题

---

## 📝 开发检查清单

### 部署前检查
- [ ] API版本正确 (2024-04-01)
- [ ] HTTP方法正确 (PUT)
- [ ] 请求格式符合规范
- [ ] 错误处理覆盖全面
- [ ] 日志记录充分
- [ ] 状态管理清晰

### 测试验证
- [ ] 单个任务处理正常
- [ ] 批量任务处理正常
- [ ] 同类型任务无冲突
- [ ] ZIP解压缩正确
- [ ] 文件映射准确
- [ ] 音频文件大小正常

### 生产监控
- [ ] 批处理成功率 > 95%
- [ ] 平均处理时间 < 5分钟
- [ ] 错误率 < 5%
- [ ] 音频文件质量正常

---

## 🎯 总结

Azure TTS批处理开发的关键成功因素：

1. **正确的API版本和格式** - 使用2024-04-01版本和PUT方法
2. **精确的文件映射逻辑** - 通过summary.json建立双重映射
3. **按类型分组处理** - 避免同文本内容冲突
4. **完善的错误处理** - 分层处理不同类型的错误
5. **充分的日志记录** - 便于调试和监控

通过遵循这些最佳实践，可以构建一个稳定可靠的TTS批处理系统。

---

## 🔧 实战代码片段

### Azure批处理请求构建

```typescript
function buildAzureBatchRequest(tasks: any[], ttsType?: string): AzureBatchRequest {
  // 根据类型选择语音模型
  let voice = 'en-US-AndrewNeural'; // 默认美式
  if (ttsType === 'phonetic_bre') {
    voice = 'en-GB-MiaNeural'; // 英式
  }

  return {
    description: `SenseWord TTS batch - ${ttsType} (${tasks.length} tasks)`,
    inputKind: 'PlainText',
    inputs: tasks.map(task => ({
      content: task.text
    })),
    synthesisConfig: {
      voice: voice,
      style: 'general',
      rate: '0%',
      pitch: '0%'
    }
  };
}
```

### 批处理状态检查

```typescript
async function checkBatchStatus(batchId: string, env: Env): Promise<BatchStatus> {
  const url = `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Ocp-Apim-Subscription-Key': getNextAzureKey(env)
    }
  });

  if (!response.ok) {
    if (response.status === 404) {
      throw new Error(`批处理不存在: ${batchId}`);
    }
    throw new Error(`状态查询失败: ${response.status}`);
  }

  const status = await response.json();

  // 记录详细状态信息
  console.log(`[TTS Worker] 批处理状态详情: ${batchId}`, {
    status: status.status,
    createdDateTime: status.createdDateTime,
    lastActionDateTime: status.lastActionDateTime,
    totalTasks: status.totalTasks,
    succeededTasks: status.succeededTasks,
    failedTasks: status.failedTasks
  });

  return status;
}
```

### ZIP文件处理完整流程

```typescript
async function processZipFile(downloadUrl: string, tasks: any[], env: Env): Promise<void> {
  // 1. 下载ZIP文件
  console.log(`[TTS Worker] 开始下载ZIP: ${downloadUrl}`);
  const zipResponse = await fetch(downloadUrl);

  if (!zipResponse.ok) {
    throw new Error(`ZIP下载失败: ${zipResponse.status}`);
  }

  const zipBuffer = await zipResponse.arrayBuffer();
  console.log(`[TTS Worker] ZIP下载完成: ${zipBuffer.byteLength} bytes`);

  // 2. 解压ZIP文件
  const zip = await JSZip.loadAsync(zipBuffer);

  // 3. 读取summary.json
  const summaryFile = zip.file('summary.json');
  if (!summaryFile) {
    throw new Error('ZIP文件中未找到summary.json');
  }

  const summaryText = await summaryFile.async('text');
  const summaryData = JSON.parse(summaryText);

  // 4. 建立映射关系
  const audioMappings = [];
  const textToTtsIdMap = new Map<string, string>();

  // 建立text到ttsId的映射
  tasks.forEach(task => {
    textToTtsIdMap.set(task.text, task.ttsId);
  });

  // 处理每个音频文件
  for (const result of summaryData.results) {
    if (result.status !== 'Succeeded' || !result.contents || result.contents.length === 0) {
      console.warn(`[TTS Worker] 跳过失败的结果:`, result);
      continue;
    }

    const content = result.contents[0];
    const audioFileName = result.audioFileName;

    // 获取音频文件
    const audioFile = zip.file(audioFileName);
    if (!audioFile) {
      console.error(`[TTS Worker] 音频文件不存在: ${audioFileName}`);
      continue;
    }

    const audioBuffer = await audioFile.async('arraybuffer');

    // 通过content找到对应的ttsId
    let ttsId = content; // 默认使用content作为ttsId
    if (textToTtsIdMap.has(content)) {
      ttsId = textToTtsIdMap.get(content)!;
      console.log(`[TTS Worker] 映射成功: "${content}" -> ${ttsId}`);
    } else {
      console.warn(`[TTS Worker] 未找到映射: "${content}"`);
    }

    audioMappings.push({
      ttsId,
      audioBuffer,
      originalContent: content,
      fileName: audioFileName
    });
  }

  // 5. 并行上传到R2
  await processAudioMappings(audioMappings, env);
}
```

---

## 🚨 生产环境注意事项

### 1. 配额管理

```typescript
// 监控Azure配额使用情况
interface QuotaInfo {
  used: number;
  limit: number;
  resetDate: string;
}

async function checkQuotaUsage(env: Env): Promise<QuotaInfo> {
  // 实现配额检查逻辑
  // Azure TTS有每月字符数限制
}
```

### 2. 成本控制

```typescript
// 估算处理成本
function estimateProcessingCost(tasks: any[]): number {
  const totalCharacters = tasks.reduce((sum, task) => sum + task.text.length, 0);
  const costPerCharacter = 0.000015; // Azure TTS定价
  return totalCharacters * costPerCharacter;
}
```

### 3. 监控告警

```typescript
// 关键指标监控
const metrics = {
  batchSuccessRate: successfulBatches / totalBatches,
  averageProcessingTime: totalProcessingTime / completedBatches,
  errorRate: failedTasks / totalTasks,
  quotaUsagePercent: usedQuota / totalQuota
};

// 告警阈值
if (metrics.batchSuccessRate < 0.95) {
  await sendAlert('批处理成功率过低');
}

if (metrics.quotaUsagePercent > 0.8) {
  await sendAlert('Azure配额使用率过高');
}
```

---

## 📚 参考资源

### 官方文档
- [Azure TTS批处理API文档](https://docs.microsoft.com/en-us/azure/cognitive-services/speech-service/batch-synthesis)
- [Azure TTS定价](https://azure.microsoft.com/en-us/pricing/details/cognitive-services/speech-services/)
- [Cloudflare Workers文档](https://developers.cloudflare.com/workers/)

### 相关工具
- [JSZip文档](https://stuk.github.io/jszip/)
- [D1数据库文档](https://developers.cloudflare.com/d1/)
- [R2存储文档](https://developers.cloudflare.com/r2/)

### 调试工具
- [Azure Portal](https://portal.azure.com/) - 监控TTS使用情况
- [Cloudflare Dashboard](https://dash.cloudflare.com/) - Worker日志和监控
- [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/) - 本地开发和部署

---

## 🎯 最终建议

1. **从小规模开始**: 先用少量任务验证整个流程
2. **充分测试**: 覆盖各种边界情况和错误场景
3. **监控为先**: 建立完善的监控和告警机制
4. **文档记录**: 记录所有配置和操作步骤
5. **定期维护**: 定期检查和更新API版本

Azure TTS批处理是一个复杂的系统，但通过系统性的设计和充分的测试，可以构建出稳定可靠的生产级服务。
