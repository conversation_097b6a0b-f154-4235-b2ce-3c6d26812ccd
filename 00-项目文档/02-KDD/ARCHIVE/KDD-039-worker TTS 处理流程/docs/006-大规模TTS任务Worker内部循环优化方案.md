# 006 - 大规模TTS任务Worker内部循环优化方案

## 📋 概述

本文档针对SenseWord项目的130万个TTS任务，设计基于Cloudflare Worker内部循环的大规模批处理优化方案，目标在4小时内完成所有任务处理，实现6000任务/分钟的高吞吐量。

---

## 📊 任务规模分析

### 🎯 **项目规模**
- **单词总数**: 57,000个
- **TTS任务总数**: 1,300,000个
- **平均任务/单词**: 22.8个
- **任务类型分布**:
  - phonetic_bre: ~57,000个 (英式发音)
  - phonetic_name: ~57,000个 (美式发音)
  - phonetic_ipa: ~57,000个 (IPA发音)
  - example_sentence: ~342,000个 (例句，6个/单词)
  - phrase_breakdown: ~1,140,000个 (短语分解，20个/单词)

### ⏱️ **完成时间对比分析**

```mermaid
xychart-beta
    title "不同批处理配置下的完成时间预估"
    x-axis ["当前50/min", "保守3000/min", "激进6000/min", "极限12000/min"]
    y-axis "完成时间 (小时)" 0 --> 450
    bar [433, 7.2, 3.6, 1.8]
```

| 配置方案 | 吞吐量/分钟 | 完成时间 | Worker负载 | 推荐度 |
|----------|-------------|----------|------------|--------|
| **当前配置** | 50 | 18天 | 极低 | ❌ |
| **保守方案** | 3,000 | 7.2小时 | 中等 | ⭐⭐⭐ |
| **激进方案** | 6,000 | 3.6小时 | 高 | ⭐⭐⭐⭐⭐ |
| **极限方案** | 12,000 | 1.8小时 | 极高 | ⭐⭐ |

---

## 🚀 Worker内部循环优化方案

### 💡 **核心设计理念**

```mermaid
flowchart TD
    subgraph "🔄 Worker内部高频循环"
        A[Cloudflare Cron<br/>每分钟触发] --> B[Worker启动]
        B --> C[内部循环6次]
        C --> D[每次处理1000个任务]
        D --> E[9秒间隔等待]
        E --> F{循环完成?}
        F -->|否| D
        F -->|是| G[返回处理结果]
    end
    
    subgraph "📈 性能目标"
        H[6次 × 1000任务 = 6000任务/分钟]
        I[130万任务 ÷ 6000/分钟 = 3.6小时]
        J[Azure利用率: 1%]
    end
    
    C --> H
    H --> I
    I --> J
    
    classDef workerStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef targetStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B,C,D,E,F,G workerStyle
    class H,I,J targetStyle
```

### 🎯 **最优批处理参数**

基于Worker计算能力和Azure限制的平衡点：

```typescript
// 推荐的大规模批处理配置
const LARGE_SCALE_CONFIG = {
  // 核心参数
  cycles: 6,                    // 每分钟6次循环
  batchSize: 1000,              // 每次1000个任务
  interval: 9000,               // 9秒间隔 (留1秒缓冲)
  
  // Worker限制
  maxProcessingTime: 55000,     // 最大55秒处理时间
  maxMemoryUsage: 100,          // 最大100MB内存
  
  // 性能优化
  adaptiveScaling: true,        // 动态调整批处理大小
  typeGrouping: true,           // 按类型分组避免冲突
  batchDatabaseOps: true,       // 批量数据库操作
  
  // 容错机制
  errorThreshold: 0.1,          // 10%错误率阈值
  retryAttempts: 3,             // 重试次数
  fallbackBatchSize: 500,       // 降级批处理大小
  
  // 预期性能
  expectedThroughput: 6000,     // 6000任务/分钟
  estimatedCompletion: 3.6      // 3.6小时完成
};
```

---

## 🛠️ 技术实现方案

### 🔧 **优化的Worker核心逻辑**

```typescript
export default {
  async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log('🚀 大规模TTS批处理开始 - 目标: 130万任务');
    
    const startTime = Date.now();
    const config = await getDynamicConfig(env);
    
    let totalProcessed = 0;
    let successCount = 0;
    let errorCount = 0;
    let memoryPeak = 0;
    
    try {
      // 高频内部循环处理
      for (let cycle = 1; cycle <= config.cycles; cycle++) {
        const cycleStartTime = Date.now();
        
        // 时间限制检查
        if (Date.now() - startTime > config.maxProcessingTime) {
          console.warn(`⏰ 接近60秒限制，停止处理。已完成: ${totalProcessed}`);
          break;
        }
        
        try {
          console.log(`📊 周期 ${cycle}/${config.cycles} 开始 (已用时: ${Date.now() - startTime}ms)`);
          
          // 1. 智能任务获取
          const tasks = await getOptimizedTaskBatch(config.batchSize, env);
          if (tasks.length === 0) {
            console.log('📭 队列为空，处理完成');
            break;
          }
          
          // 2. 内存监控
          const memoryUsage = estimateMemoryUsage(tasks);
          memoryPeak = Math.max(memoryPeak, memoryUsage);
          
          if (memoryUsage > config.maxMemoryUsage) {
            console.warn(`🧠 内存压力: ${memoryUsage}MB，动态降级`);
            config.batchSize = Math.floor(config.batchSize * 0.8);
            continue;
          }
          
          // 3. 按类型分组处理 (避免Azure冲突)
          const taskGroups = groupTasksByType(tasks);
          console.log(`🔄 处理 ${Object.keys(taskGroups).length} 个类型组，共 ${tasks.length} 个任务`);
          
          // 4. 并行提交Azure批处理
          const batchResults = await submitParallelAzureBatches(taskGroups, env);
          
          // 5. 批量数据库更新 (性能优化)
          await batchUpdateTaskStatus(tasks, 'processing', batchResults, env);
          
          totalProcessed += tasks.length;
          successCount++;
          
          // 6. 性能统计
          const cycleTime = Date.now() - cycleStartTime;
          const currentThroughput = Math.round(totalProcessed * 60000 / (Date.now() - startTime));
          
          console.log(`✅ 周期 ${cycle} 完成: ${tasks.length} 任务, 耗时: ${cycleTime}ms, 吞吐量: ${currentThroughput}/分钟`);
          
          // 7. 动态参数调整
          if (config.adaptiveScaling) {
            config.batchSize = adjustBatchSizeByPerformance(cycleTime, config);
          }
          
        } catch (error) {
          errorCount++;
          console.error(`❌ 周期 ${cycle} 失败:`, error);
          
          // 错误率过高时降级处理
          if (errorCount / cycle > config.errorThreshold) {
            console.warn('🚨 错误率过高，启动降级模式');
            config.batchSize = config.fallbackBatchSize;
            config.adaptiveScaling = false;
          }
        }
        
        // 8. 智能等待间隔
        if (cycle < config.cycles) {
          const actualCycleTime = Date.now() - cycleStartTime;
          const waitTime = Math.max(1000, config.interval - actualCycleTime);
          console.log(`⏳ 智能等待: ${waitTime}ms`);
          await sleep(waitTime);
        }
      }
      
      // 处理完成统计和预测
      await generateProcessingReport(startTime, totalProcessed, successCount, errorCount, memoryPeak, env);
      
    } catch (error) {
      console.error('❌ 大规模批处理系统失败:', error);
      await recordCriticalError(error, env);
      throw error;
    }
  }
};

// 动态配置获取
async function getDynamicConfig(env: Env): Promise<BatchConfig> {
  const queueSize = await getTotalQueueSize(env);
  const systemLoad = await getSystemLoad(env);
  
  if (queueSize > 1000000 && systemLoad < 0.7) {
    // 超大规模 + 系统负载低 = 激进配置
    return {
      cycles: 6,
      batchSize: 1000,
      interval: 9000,
      maxProcessingTime: 55000,
      adaptiveScaling: true,
      errorThreshold: 0.1
    };
  } else if (queueSize > 500000) {
    // 大规模处理
    return {
      cycles: 6,
      batchSize: 500,
      interval: 9500,
      maxProcessingTime: 55000,
      adaptiveScaling: true,
      errorThreshold: 0.15
    };
  } else {
    // 常规处理
    return {
      cycles: 6,
      batchSize: 100,
      interval: 9500,
      maxProcessingTime: 55000,
      adaptiveScaling: false,
      errorThreshold: 0.2
    };
  }
}

// 智能任务获取 (优化数据库查询)
async function getOptimizedTaskBatch(batchSize: number, env: Env): Promise<TTSTask[]> {
  try {
    // 使用索引优化的查询，按类型平衡获取
    const query = `
      WITH balanced_tasks AS (
        SELECT *, ROW_NUMBER() OVER (PARTITION BY type ORDER BY created_at) as rn
        FROM tts_tasks 
        WHERE status = 'pending'
      )
      SELECT ttsId, content, type, voice, language, created_at
      FROM balanced_tasks 
      WHERE rn <= ?
      ORDER BY type, created_at ASC 
      LIMIT ?
    `;
    
    const balancePerType = Math.ceil(batchSize / 5); // 5种主要类型
    const result = await env.DB.prepare(query).bind(balancePerType, batchSize).all();
    
    console.log(`📊 获取任务批次: ${result.results.length} 个任务`);
    return result.results as TTSTask[];
    
  } catch (error) {
    console.error('❌ 获取任务批次失败:', error);
    return [];
  }
}

// 按类型分组任务 (避免Azure批处理冲突)
function groupTasksByType(tasks: TTSTask[]): Record<string, TTSTask[]> {
  const groups: Record<string, TTSTask[]> = {};
  
  for (const task of tasks) {
    const typeKey = task.type.toLowerCase(); // 标准化类型名
    if (!groups[typeKey]) {
      groups[typeKey] = [];
    }
    groups[typeKey].push(task);
  }
  
  console.log(`🔄 任务分组结果:`, Object.entries(groups).map(([type, tasks]) => 
    `${type}: ${tasks.length}个`
  ).join(', '));
  
  return groups;
}

// 并行提交Azure批处理 (按类型分组)
async function submitParallelAzureBatches(
  taskGroups: Record<string, TTSTask[]>, 
  env: Env
): Promise<BatchResult[]> {
  const azureKey = await getAvailableAzureKey(env);
  const batchPromises: Promise<BatchResult>[] = [];
  
  for (const [type, tasks] of Object.entries(taskGroups)) {
    const batchId = `batch_${Date.now()}_${type}_${Math.random().toString(36).substr(2, 6)}`;
    
    const batchRequest = {
      inputKind: "SSML",
      inputs: tasks.map(task => ({
        content: task.content
      })),
      properties: {
        outputFormat: "riff-24khz-16bit-mono-pcm",
        concatenateResult: false,
        decompressOutputFiles: false
      }
    };
    
    const promise = submitSingleAzureBatch(batchId, batchRequest, azureKey, tasks.length);
    batchPromises.push(promise);
  }
  
  // 并行等待所有批处理提交
  const results = await Promise.allSettled(batchPromises);
  const successful = results.filter(r => r.status === 'fulfilled').length;
  
  console.log(`🚀 并行提交完成: ${successful}/${results.length} 个批次成功`);
  
  return results
    .filter(r => r.status === 'fulfilled')
    .map(r => (r as PromiseFulfilledResult<BatchResult>).value);
}

// 批量数据库状态更新 (性能优化)
async function batchUpdateTaskStatus(
  tasks: TTSTask[], 
  status: string, 
  batchResults: BatchResult[], 
  env: Env
): Promise<void> {
  try {
    const statements = tasks.map(task => {
      const batchId = batchResults.find(br => br.taskCount > 0)?.batchId || 'unknown';
      return env.DB.prepare(
        "UPDATE tts_tasks SET status = ?, batch_id = ?, updated_at = CURRENT_TIMESTAMP WHERE ttsId = ?"
      ).bind(status, batchId, task.ttsId);
    });
    
    // 使用事务批量执行
    await env.DB.batch(statements);
    console.log(`📝 批量更新 ${tasks.length} 个任务状态为: ${status}`);
    
  } catch (error) {
    console.error('❌ 批量状态更新失败:', error);
    // 降级为单个更新
    for (const task of tasks) {
      try {
        await env.DB.prepare(
          "UPDATE tts_tasks SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE ttsId = ?"
        ).bind(status, task.ttsId).run();
      } catch (singleError) {
        console.error(`❌ 单个任务 ${task.ttsId} 更新失败:`, singleError);
      }
    }
  }
}

// 动态批处理大小调整
function adjustBatchSizeByPerformance(cycleTime: number, config: BatchConfig): number {
  const targetCycleTime = config.interval * 0.8; // 目标80%间隔时间
  const currentBatchSize = config.batchSize;
  
  if (cycleTime < targetCycleTime * 0.6) {
    // 处理很快，可以增加批处理大小
    const newSize = Math.min(currentBatchSize * 1.2, 2000);
    console.log(`⬆️ 性能良好，批处理大小: ${currentBatchSize} → ${newSize}`);
    return newSize;
  } else if (cycleTime > targetCycleTime) {
    // 处理较慢，需要减少批处理大小
    const newSize = Math.max(currentBatchSize * 0.8, 100);
    console.log(`⬇️ 性能压力，批处理大小: ${currentBatchSize} → ${newSize}`);
    return newSize;
  }
  
  return currentBatchSize;
}

// 生成处理报告
async function generateProcessingReport(
  startTime: number, 
  totalProcessed: number, 
  successCount: number, 
  errorCount: number, 
  memoryPeak: number, 
  env: Env
): Promise<void> {
  const totalTime = Date.now() - startTime;
  const remainingTasks = await getTotalQueueSize(env);
  const actualThroughput = Math.round(totalProcessed * 60000 / totalTime);
  const estimatedCompletion = remainingTasks / actualThroughput; // 分钟
  const progressPercentage = ((1300000 - remainingTasks) / 1300000 * 100).toFixed(2);
  
  const report = {
    timestamp: new Date().toISOString(),
    processing_stats: {
      total_processed: totalProcessed,
      remaining_tasks: remainingTasks,
      progress_percentage: `${progressPercentage}%`,
      actual_throughput: `${actualThroughput}/分钟`,
      estimated_completion: `${Math.round(estimatedCompletion)}分钟`,
      success_rate: `${Math.round(successCount/(successCount+errorCount)*100)}%`
    },
    performance_stats: {
      total_time_ms: totalTime,
      memory_peak_mb: memoryPeak,
      cycles_completed: successCount + errorCount,
      error_rate: `${Math.round(errorCount/(successCount+errorCount)*100)}%`
    },
    projection: {
      estimated_total_time: `${Math.round(1300000 / actualThroughput / 60)}小时`,
      azure_utilization: `${(actualThroughput / 600000 * 100).toFixed(3)}%`,
      cost_efficiency: 'optimal'
    }
  };
  
  console.log('📈 大规模处理报告:');
  console.log(`  - 已处理: ${totalProcessed.toLocaleString()} (${progressPercentage}%)`);
  console.log(`  - 剩余任务: ${remainingTasks.toLocaleString()}`);
  console.log(`  - 实际吞吐量: ${actualThroughput.toLocaleString()}/分钟`);
  console.log(`  - 预计完成: ${Math.round(estimatedCompletion)}分钟`);
  console.log(`  - 成功率: ${Math.round(successCount/(successCount+errorCount)*100)}%`);
  console.log(`  - Azure利用率: ${(actualThroughput / 600000 * 100).toFixed(3)}%`);
  
  // 保存详细报告到KV
  await env.TTS_KV.put('tts:large_scale:latest_report', JSON.stringify(report));
  await env.TTS_KV.put(`tts:large_scale:report:${Date.now()}`, JSON.stringify(report), { 
    expirationTtl: 86400 * 7 // 保存7天
  });
}
```

---

## 📊 性能预期与监控

### 🎯 **分阶段实施策略**

```mermaid
gantt
    title 130万TTS任务处理时间线
    dateFormat HH:mm
    axisFormat %H:%M
    
    section 第一阶段
    保守测试 (500/批次)    :active, phase1, 00:00, 2h
    
    section 第二阶段  
    激进优化 (1000/批次)   :phase2, after phase1, 3h
    
    section 第三阶段
    极限测试 (2000/批次)   :phase3, after phase2, 1h
    
    section 完成
    任务处理完成           :milestone, done, after phase3, 0h
```

### 📈 **关键性能指标 (KPI)**

| 指标 | 目标值 | 监控方式 | 告警阈值 |
|------|--------|----------|----------|
| **吞吐量** | 6000任务/分钟 | 实时计算 | <3000/分钟 |
| **成功率** | >95% | 周期统计 | <90% |
| **内存使用** | <100MB | 估算监控 | >120MB |
| **处理时间** | <55秒/周期 | 实时监控 | >50秒 |
| **错误率** | <10% | 累计统计 | >15% |
| **Azure利用率** | ~1% | 计算得出 | >2% |

### 🔍 **实时监控仪表板**

```typescript
// 监控数据结构
interface ProcessingMetrics {
  current_throughput: number;      // 当前吞吐量
  total_processed: number;         // 总处理数量
  remaining_tasks: number;         // 剩余任务
  progress_percentage: number;     // 完成百分比
  estimated_completion: number;    // 预计完成时间(分钟)
  success_rate: number;           // 成功率
  error_rate: number;             // 错误率
  memory_usage: number;           // 内存使用
  azure_utilization: number;      // Azure利用率
}
```

---

## 🎯 实施建议

### 立即执行方案
1. **部署优化Worker**: 使用激进配置 (1000任务/批次)
2. **启动监控**: 实时跟踪处理进度和性能指标
3. **预期完成**: 3.6小时内完成130万任务

### 风险控制
1. **降级机制**: 错误率>10%时自动降级到500任务/批次
2. **时间限制**: 严格控制在55秒内完成每个周期
3. **内存监控**: 超过100MB时动态调整批处理大小

### 成功标准
- **完成时间**: <4小时
- **成功率**: >95%
- **系统稳定性**: 无Worker超时或崩溃
- **数据一致性**: 所有任务状态正确更新

通过这个优化方案，可以将130万TTS任务的处理时间从18天缩短到3.6小时，实现**120倍**的性能提升！
