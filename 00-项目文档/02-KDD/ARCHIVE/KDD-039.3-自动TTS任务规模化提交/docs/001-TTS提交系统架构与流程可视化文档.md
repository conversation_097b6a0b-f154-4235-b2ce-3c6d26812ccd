# 001 - TTS提交系统架构与流程可视化文档

## 📋 文档概述

本文档分析了TTS任务提交系统的两个核心Python脚本，展示了基于积压任务绝对数量的智能流量控制架构。这两个文件构成了一个完全自动化的TTS任务管理系统，实现了单词级精确流量控制，每个单词处理后立即进行系统健康评估和流量调节。

**包含文件**：
- `tts_02_submit_tasks_optimized.py` - 核心TTS任务提交器（单个单词处理 + 实时状态返回 + 成本监控）
- `tts_04_large_scale_coordinator.py` - 智能流量控制器（单词级循环 + 实时流量控制 + 零配置运行）

**文档价值**：帮助理解单词级精确流量控制机制、实时系统健康评估和基于积压任务绝对数量的动态调节策略。

## 🏗️ 系统架构图

### 文件关系与数据流程

```mermaid
graph TB
    %% 定义样式
    classDef entryPoint fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef types fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef utils fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef database fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef api fill:#F0E68C,stroke:#000000,stroke-width:2px,color:#000000

    %% 主要文件节点
    OPTIMIZED["🚀 tts_02_submit_tasks_optimized.py<br/>核心TTS任务提交器<br/>• 逐个单词处理<br/>• 优先级排序<br/>• 实时状态返回"]

    LARGE_SCALE["📊 tts_04_large_scale_coordinator.py<br/>智能流量控制器<br/>• 单词级循环处理<br/>• 实时流量控制<br/>• 每个单词后立即评估"]
    


    %% 共享配置和数据
    CONFIG["📋 config.json<br/>共享配置文件<br/>• Worker端点配置<br/>• 数据库路径<br/>• 批量处理参数"]
    
    TTS_ASSETS[("💾 tts_assets表<br/>SQLite数据库<br/>• 任务状态管理<br/>• 优先级排序<br/>• 音频URL存储")]

    %% 外部系统
    TTS_WORKER[("🌐 TTS Worker<br/>Cloudflare Worker<br/>• /submit端点<br/>• /dashboard端点<br/>• /billing端点")]
    
    AZURE_TTS[("☁️ Azure TTS API<br/>语音合成服务<br/>• 成本计费<br/>• API Key管理")]
    


    %% 数据流程 - 常规提交（同步部分）
    OPTIMIZED -->|"读取配置"| CONFIG
    OPTIMIZED -->|"查询待处理任务"| TTS_ASSETS
    OPTIMIZED -->|"POST /submit<br/>立即返回写入结果"| TTS_WORKER
    OPTIMIZED -->|"GET /dashboard"| TTS_WORKER
    OPTIMIZED -->|"GET /billing/status"| TTS_WORKER

    %% 数据流程 - 智能流量控制
    LARGE_SCALE -->|"读取配置"| CONFIG
    LARGE_SCALE -->|"查询优先单词"| TTS_ASSETS
    LARGE_SCALE -->|"调用02脚本<br/>subprocess.run()"| OPTIMIZED
    LARGE_SCALE -->|"解析积压任务数<br/>流量控制评估"| LARGE_SCALE



    %% 异步处理流程（Worker后台）
    TTS_WORKER -.->|"定时任务轮询<br/>异步处理"| TTS_WORKER
    TTS_WORKER -.->|"API调用计费<br/>异步音频生成"| AZURE_TTS
    TTS_WORKER -.->|"更新completed状态<br/>异步写入audioUrl"| TTS_ASSETS

    %% 协作关系
    LARGE_SCALE -.->|"积压不足时<br/>涡轮模式(0.5s)"| LARGE_SCALE
    LARGE_SCALE -.->|"积压适中时<br/>正常模式(2s)"| LARGE_SCALE
    LARGE_SCALE -.->|"积压过多时<br/>减速模式(5s)"| LARGE_SCALE
    LARGE_SCALE -.->|"积压临界时<br/>暂停模式(60s)"| LARGE_SCALE
    LARGE_SCALE -.->|"成本达到上限时<br/>停止等待手动切换"| LARGE_SCALE

    %% 应用样式
    class OPTIMIZED entryPoint
    class LARGE_SCALE service
    class CONFIG types
    class TTS_ASSETS database
    class TTS_WORKER,AZURE_TTS external
```

### 架构层次说明

- **🎯 核心层**: `02_submit_tts_tasks_optimized.py` 负责同步提交，立即获得写入结果和积压任务数量
- **🔧 流量控制层**: `04_large_scale_tts_submitter.py` 基于积压任务绝对数量实现四级流量控制
- **🌐 异步处理层**: TTS Worker通过定时任务异步处理音频生成，与提交完全解耦
- **📋 配置层**: `config.json` 提供统一的配置管理
- **💾 数据层**: `tts_assets` SQLite表提供持久化存储，支持状态流转
- **👤 手动管理**: API Key切换和Worker部署通过手动操作，避免自动化风险

## 🔄 详细处理流程时序图

### 完整TTS智能流量控制提交处理流程（基于积压任务绝对数量）

```mermaid
sequenceDiagram
    participant U as 👤 用户
    participant C04 as 📊 04流量控制器
    participant C02 as 🚀 02核心提交器
    participant DB as 💾 tts_assets数据库
    participant W as 🌐 TTS Worker
    participant A as ☁️ Azure TTS

    Note over U,WR: 📥 阶段1: 智能流量控制启动
    U->>C04: python tts_04_large_scale_coordinator.py
    C04->>DB: 查询所有待处理的优先单词
    DB-->>C04: 返回优先单词列表(按frequency排序)
    C04->>C04: 单词级循环处理(逐个单词处理)

    Note over U,A: 🔄 阶段2: 单词级智能流量控制循环
    loop 单词级流量控制循环
        C04->>C04: 获取下一个单词

        Note over C04,C02: 📞 调用02脚本处理单个单词
        C04->>C02: subprocess.run("python3 tts_02_submit_tasks_optimized.py --words word1")

        Note over C02,W: 🚀 02脚本执行单词提交流程
        C02->>DB: 查询单词任务
        DB-->>C02: 返回任务列表

        C02->>W: POST /submit {word, tasks[]}
        W->>W: 写入D1数据库 (status='submitted')
        W-->>C02: 立即返回 {success, billing, system_stats, polling}
        Note over C02: 02脚本立即获得写入结果，包含积压任务数量

        Note over W,A: ⏰ Worker异步处理（与02脚本解耦）
        par Worker后台异步处理
            loop Worker定时任务轮询
                W->>W: 查询pending任务
                W->>A: Azure TTS API调用
                A-->>W: 音频生成 + 计费累积
                W->>W: 更新任务状态为completed + audioUrl
            end
        end

C02-->>C04: 返回输出: 提交结果 + 计费信息 + 积压任务数量

Note over C04: 📊 实时流量控制评估
C04->>C04: 解析02脚本输出
C04->>C04: evaluate_system_health()
C04->>C04: 基于积压任务绝对数量确定流量模式

alt 系统健康 (成本<95% && 积压<200)
C04->>C04: ✅ 继续下一个单词
C04-->>U: 显示单词处理进度
else 成本达到95%上限
C04-->>U: 🚨 停止程序，等待手动切换API Key
Note over C04,U: 程序完全停止，需要手动重新启动
else 任务积压过多 (≥1000)
C04->>C04: ⏰ 等待1分钟后重试
C04-->>U: 显示积压状态，1分钟后重试当前单词
C04->>C04: sleep(60) # 固定1分钟，无指数回退
C04->>C04: 重试当前单词 (不跳过)
end
end

    Note over U,A: ⚡ 阶段3: 手动成本管理
    alt 成本接近上限时
        C04-->>U: ⚠️ 成本使用率达到95%，建议手动切换API Key
        U->>U: 手动更新Worker环境变量
        U->>U: 手动重新部署Worker
        U->>W: POST /billing/reset (手动重置计费)
        U->>C04: 继续运行协调器
    else 成本正常
        C04->>C04: 继续智能协调
    end

    Note over U,A: 🎯 阶段4: 协调完成
    C04-->>U: 📊 最终统计报告
    Note over U: 处理单词数: X<br/>提交成功: Y<br/>成功率: Z%
```

## 🔗 模块依赖关系图

### 导入导出关系与类型共享

```mermaid
graph TB
    %% 定义样式
    classDef currentFile fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef serviceFile fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef typeFile fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef configFile fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef externalLib fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 主要文件
    subgraph MAIN_FILES["🎯 TTS提交系统文件"]
        OPTIMIZED_FILE["02_submit_tts_tasks_optimized.py<br/>• OptimizedTTSSubmitter类<br/>• get_pending_words_by_priority()<br/>• submit_word_tasks()<br/>• get_dashboard_status()"]
        
        LARGE_SCALE_FILE["04_large_scale_tts_submitter.py<br/>• LargeScaleTTSSubmitter类<br/>• SubmissionConfig dataclass<br/>• SystemStatus dataclass<br/>• get_priority_words()"]
        
        COST_MGR_FILE["05_apikey_cost_manager.py<br/>• ApiKeyCostManager类<br/>• ApiKeyConfig dataclass<br/>• switch_to_next_key()<br/>• monitor_and_manage()"]
    end

    %% 共享配置
    subgraph SHARED_CONFIG["📋 共享配置"]
        CONFIG_FILE["config.json<br/>• worker.baseUrl<br/>• worker.submitEndpoint<br/>• database.sqlitePath<br/>• batch.timeout"]
    end

    %% 数据类型定义
    subgraph DATA_TYPES["🏗️ 数据结构"]
        SUBMISSION_CONFIG["SubmissionConfig<br/>• cost_limit_usd: float<br/>• target_backlog_tasks: int<br/>• max_backlog_tasks: int<br/>• critical_backlog_tasks: int"]
        
        API_KEY_CONFIG["ApiKeyConfig<br/>• key_id: str<br/>• azure_key: str<br/>• region: str<br/>• cost_limit: float"]
        
        SYSTEM_STATUS["SystemStatus<br/>• current_cost: float<br/>• pending_tasks: int<br/>• completion_rate: float<br/>• can_submit: bool"]
    end

    %% 外部依赖
    subgraph EXTERNAL_DEPS["📦 外部依赖"]
        SQLITE3["sqlite3<br/>数据库连接"]
        REQUESTS["requests<br/>HTTP客户端"]
        SUBPROCESS["subprocess<br/>Wrangler调用"]
        ARGPARSE["argparse<br/>命令行参数"]
        DATACLASSES["dataclasses<br/>数据类定义"]
    end

    %% 依赖关系
    OPTIMIZED_FILE --> CONFIG_FILE
    OPTIMIZED_FILE --> SQLITE3
    OPTIMIZED_FILE --> REQUESTS
    OPTIMIZED_FILE --> ARGPARSE

    LARGE_SCALE_FILE --> CONFIG_FILE
    LARGE_SCALE_FILE --> SUBMISSION_CONFIG
    LARGE_SCALE_FILE --> SYSTEM_STATUS
    LARGE_SCALE_FILE --> SQLITE3
    LARGE_SCALE_FILE --> REQUESTS
    LARGE_SCALE_FILE --> DATACLASSES

    COST_MGR_FILE --> CONFIG_FILE
    COST_MGR_FILE --> API_KEY_CONFIG
    COST_MGR_FILE --> SUBPROCESS
    COST_MGR_FILE --> REQUESTS
    COST_MGR_FILE --> DATACLASSES

    %% 数据类型关系
    LARGE_SCALE_FILE --> SUBMISSION_CONFIG
    LARGE_SCALE_FILE --> SYSTEM_STATUS
    COST_MGR_FILE --> API_KEY_CONFIG

    %% 协作关系（虚线表示间接依赖）
    LARGE_SCALE_FILE -.->|"成本监控需求"| COST_MGR_FILE
    COST_MGR_FILE -.->|"API Key切换后"| LARGE_SCALE_FILE

    %% 应用样式
    class OPTIMIZED_FILE currentFile
    class LARGE_SCALE_FILE,COST_MGR_FILE serviceFile
    class CONFIG_FILE configFile
    class SUBMISSION_CONFIG,API_KEY_CONFIG,SYSTEM_STATUS typeFile
    class SQLITE3,REQUESTS,SUBPROCESS,ARGPARSE,DATACLASSES externalLib
```

## 🚀 四级流量控制机制图

### 基于积压任务绝对数量的智能流量控制

```mermaid
graph TB
    %% 定义样式
    classDef turboMode fill:#E6FFE6,stroke:#000000,stroke-width:3px,color:#000000
    classDef normalMode fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef slowMode fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef pauseMode fill:#FFE4E1,stroke:#000000,stroke-width:2px,color:#000000
    classDef stopMode fill:#FFB6C1,stroke:#000000,stroke-width:3px,color:#000000

    %% 积压任务数量节点
    PENDING["📊 积压任务数量<br/>从02脚本返回的<br/>system_stats.pending"]

    %% 流量控制决策
    subgraph FLOW_CONTROL["🎯 四级流量控制决策"]
        TURBO["🚀 涡轮模式<br/>积压 < 200<br/>间隔: 0.5秒<br/>快速补充任务队列"]

        NORMAL["✅ 正常模式<br/>积压 200-499<br/>间隔: 2.0秒<br/>维持稳定流量"]

        SLOW["🐌 减速模式<br/>积压 500-999<br/>间隔: 5.0秒<br/>给系统喘息时间"]

        PAUSE["⏸️ 暂停模式<br/>积压 ≥ 1000<br/>等待: 60秒<br/>暂停提交等待消化"]
    end

    %% 成本检查
    COST_CHECK["💰 成本检查<br/>API Key使用率"]
    STOP["🚨 停止模式<br/>成本 ≥ 95%<br/>程序停止<br/>等待手动切换"]

    %% 系统状态
    subgraph SYSTEM_STATE["📈 系统运行状态"]
        TARGET["🎯 目标状态<br/>积压任务: 200<br/>系统满负荷运行"]

        WORKER["⚙️ Worker处理能力<br/>~600任务/分钟<br/>异步音频生成"]

        QUEUE["📋 任务队列状态<br/>submitted → processing → completed"]
    end

    %% 流程连接
    PENDING --> TURBO
    PENDING --> NORMAL
    PENDING --> SLOW
    PENDING --> PAUSE

    TURBO --> COST_CHECK
    NORMAL --> COST_CHECK
    SLOW --> COST_CHECK
    PAUSE --> COST_CHECK

    COST_CHECK --> STOP

    %% 反馈循环
    TURBO -.->|"快速提交补充积压"| TARGET
    NORMAL -.->|"稳定维持目标积压"| TARGET
    SLOW -.->|"减速避免积压增长"| TARGET
    PAUSE -.->|"暂停等待积压消化"| TARGET

    TARGET -.->|"Worker异步处理"| WORKER
    WORKER -.->|"处理完成减少积压"| QUEUE
    QUEUE -.->|"更新积压数量"| PENDING

    %% 应用样式
    class TURBO turboMode
    class NORMAL normalMode
    class SLOW slowMode
    class PAUSE pauseMode
    class STOP stopMode
```

### 流量控制价值说明

- **🎯 目标导向**: 始终保持200个积压任务，让Worker满负荷运行
- **📊 数据驱动**: 基于实际积压任务数量而非估算比例
- **⚡ 动态调节**: 根据积压情况自动在四种模式间切换
- **🔄 闭环控制**: 提交速度与Worker处理能力精确匹配
- **💰 成本保护**: 在流量控制基础上叠加成本上限保护

## 📊 数据流转生命周期

### 数据在模块间的完整流转

```mermaid
graph LR
    %% 定义样式
    classDef inputData fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef processData fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputData fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageData fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef configData fill:#FFE4E1,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入阶段
    subgraph INPUT["📥 数据输入阶段"]
        CMD_ARGS["命令行参数<br/>--target-backlog 200<br/>--max-backlog 500<br/>--critical-backlog 1000<br/>零配置运行"]
        
        CONFIG_JSON["配置文件数据<br/>config.json<br/>{<br/>  worker: {baseUrl, endpoints},<br/>  database: {sqlitePath},<br/>  batch: {timeout}<br/>}"]
        
        DB_QUERY["数据库查询结果<br/>tts_assets表<br/>{<br/>  word, frequency, ttsId,<br/>  textToSpeak, status<br/>}"]
    end

    %% 处理阶段
    subgraph PROCESS["🔄 数据处理阶段"]
        WORD_BATCH["单词数据<br/>String<br/>• 按频率排序<br/>• 单个单词传递给02脚本<br/>• 02脚本处理单个单词"]
        
        TASK_BATCH["任务批次数据<br/>List[Dict]<br/>{<br/>  ttsId, word, text,<br/>  type, frequency<br/>}"]
        
        HTTP_REQUEST["HTTP请求数据<br/>POST /submit<br/>{<br/>  word: string,<br/>  tasks: TTSTaskInput[]<br/>}"]
        
        COST_CHECK["成本检查数据<br/>GET /billing/status<br/>{<br/>  current_cost, usage_rate,<br/>  can_continue<br/>}"]
        
        API_SWITCH["API Key切换数据<br/>wrangler deploy<br/>--var AZURE_TTS_KEY:new_key<br/>--var AZURE_TTS_REGION:region"]
    end

    %% 输出阶段
    subgraph OUTPUT["📤 数据输出阶段"]
        SUBMIT_RESULT["提交结果数据<br/>SubmitResponse<br/>{<br/>  success, inserted,<br/>  billing, system_stats<br/>}"]
        
        COST_STATUS["成本状态数据<br/>CostStatus<br/>{<br/>  current_key, cost,<br/>  usage_percentage<br/>}"]
        
        BATCH_STATS["批量统计数据<br/>BatchStats<br/>{<br/>  processed, submitted,<br/>  failed, success_rate<br/>}"]
    end

    %% 存储阶段
    subgraph STORAGE["💾 数据存储阶段"]
        DB_UPDATE["数据库更新<br/>UPDATE tts_assets<br/>SET status='submitted'<br/>WHERE word IN (...)"]
        
        BILLING_RESET["计费重置<br/>POST /billing/reset<br/>清空累计统计"]
        
        WORKER_DEPLOY["Worker部署<br/>新API Key配置<br/>环境变量更新"]
    end

    %% 数据流转路径
    CMD_ARGS --> WORD_BATCH
    CONFIG_JSON --> HTTP_REQUEST
    DB_QUERY --> TASK_BATCH
    
    WORD_BATCH --> TASK_BATCH
    TASK_BATCH --> HTTP_REQUEST
    HTTP_REQUEST --> SUBMIT_RESULT
    
    SUBMIT_RESULT --> COST_CHECK
    COST_CHECK --> API_SWITCH
    API_SWITCH --> WORKER_DEPLOY
    
    SUBMIT_RESULT --> DB_UPDATE
    COST_CHECK --> COST_STATUS
    TASK_BATCH --> BATCH_STATS
    
    API_SWITCH --> BILLING_RESET
    WORKER_DEPLOY --> HTTP_REQUEST

    %% 应用样式
    class CMD_ARGS,CONFIG_JSON,DB_QUERY inputData
    class WORD_BATCH,TASK_BATCH,HTTP_REQUEST,COST_CHECK,API_SWITCH processData
    class SUBMIT_RESULT,COST_STATUS,BATCH_STATS outputData
    class DB_UPDATE,BILLING_RESET,WORKER_DEPLOY storageData
```

## 🔄 单词级处理流程详解

### 04脚本单词级循环 vs 02脚本单词处理

```mermaid
graph TB
    %% 定义样式
    classDef coordinator fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef submitter fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef worker fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 04脚本层面
    subgraph COORD["🎯 04协调器层面 (tts_04_large_scale_coordinator.py)"]
        ALL_WORDS["📋 所有待处理单词<br/>(按frequency排序)<br/>word1, word2, ..., wordN"]

        WORD1["单词1<br/>word1"]
        WORD2["单词2<br/>word2"]
        WORD3["单词3<br/>word3"]
        WORDN["单词N<br/>wordN"]

        ALL_WORDS --> WORD1
        ALL_WORDS --> WORD2
        ALL_WORDS --> WORD3
        ALL_WORDS --> WORDN
    end

    %% 02脚本层面
    subgraph SUB["🚀 02提交器层面 (tts_02_submit_tasks_optimized.py)"]
        CALL1["📞 调用1<br/>python tts_02_submit_tasks_optimized.py<br/>--words word1"]
        CALL2["📞 调用2<br/>python tts_02_submit_tasks_optimized.py<br/>--words word2"]

        PROCESS1["🔄 单词处理<br/>word1 → 获取tasks → 提交Worker"]

        PROCESS2["🔄 单词处理<br/>word2 → 获取tasks → 提交Worker"]
    end

    %% Worker层面
    subgraph WORK["🌐 Worker层面 (Cloudflare Worker)"]
        REQ1["📨 请求1<br/>POST /submit<br/>{word: word1, tasks: [...]}"]
        REQ2["📨 请求2<br/>POST /submit<br/>{word: word2, tasks: [...]}"]
        REQ3["📨 请求3<br/>POST /submit<br/>{word: word3, tasks: [...]}"]
        REQN["📨 请求N<br/>POST /submit<br/>{word: wordN, tasks: [...]}"]

        QUEUE["📋 任务队列<br/>每个单词的所有任务<br/>逐个写入数据库<br/>异步调用Azure TTS"]
    end

    %% 连接关系
    WORD1 --> CALL1
    WORD2 --> CALL2

    CALL1 --> PROCESS1
    CALL2 --> PROCESS2

    PROCESS1 --> REQ1
    PROCESS2 --> REQ2

    REQ1 --> QUEUE
    REQ2 --> QUEUE
    REQ3 --> QUEUE
    REQN --> QUEUE

    %% 应用样式
    class ALL_WORDS,WORD1,WORD2,WORD3,WORDN coordinator
    class CALL1,CALL2,PROCESS1,PROCESS2 submitter
    class REQ1,REQ2,REQ3,REQN,QUEUE worker
```

### 关键理解点

1. **04脚本的单词级循环**: 逐个单词处理，每次只传递一个单词给02脚本
2. **02脚本的处理**: 收到单个单词后，获取其所有任务并提交给Worker
3. **Worker的接收**: 每次接收一个单词的所有任务，写入数据库后异步处理
4. **实时流量控制**: 每个单词处理后立即进行系统健康评估和流量调节
5. **精确控制粒度**: 流量控制间隔从79秒降到1.58秒，提升50倍响应速度

## 关键设计特点

### 架构设计原则

1. **零配置自动化运行**
   - **无需参数**: 直接运行`python tts_04_large_scale_coordinator.py`即可
   - **处理所有单词**: 自动处理数据库中所有待处理的单词
   - **智能调节**: 根据系统健康度自动调整处理策略
   - **完整处理**: 确保所有例句资产都有对应的音频文件

2. **简化职责分离**
   - **核心提交层(02)**: 逐个单词处理，包含实时状态返回和积压任务数量
   - **智能流量控制层(04)**: 基于积压任务绝对数量的四级流量控制器

3. **积压任务绝对数量驱动**
   - **目标积压**: 200个任务 - 让系统始终满负荷运行
   - **绝对数量**: 用具体任务数而不是比例判断系统状态
   - **四级控制**: 涡轮(0.5s)/正常(2s)/减速(5s)/暂停(60s)

4. **异步解耦设计**
   - **同步提交**: 02脚本立即获得写入结果和积压任务数量
   - **异步处理**: Worker通过定时任务自行管理Azure TTS调用
   - **流量匹配**: 基于积压数量精确匹配Worker处理能力
   - **完全解耦**: 提交速度不受Azure API响应时间影响

### 优化亮点

1. **零配置运行**: 无需设置参数，直接运行即可处理所有待处理单词
2. **完整资产处理**: 移除35000限制，确保所有例句资产都有音频文件
3. **单词级精确流量控制**: 每个单词处理后立即进行流量控制评估
4. **实时响应**: 流量控制间隔从79秒降到1.58秒，提升50倍响应速度
5. **积压任务数量驱动**: 基于绝对任务数量而非比例的精确流量控制
6. **四级流量控制**: 涡轮/正常/减速/暂停四种模式自动切换
7. **与Worker能力匹配**: 始终保持200个目标积压，让系统满负荷运行
8. **手动成本管理**: 避免自动化风险，通过手动切换API Key确保可靠性

## 🚀 系统优势

### 性能优势
- **实时流量控制**: 每个单词处理后立即评估，响应速度提升50倍
- **精确流量控制**: 基于积压任务绝对数量的四级流量控制
- **最大化吞吐**: 始终保持200个目标积压，让系统满负荷运行
- **智能加速**: 积压不足时涡轮模式(0.5s)快速补充任务队列

### 稳定性保障
- **积压任务缓冲**: 200个目标积压确保Worker始终有任务处理
- **过载保护**: 积压超过1000时暂停提交，避免系统崩溃
- **成本保护**: 成本达到95%时立即停止，等待手动切换

### 可维护性
- **简化架构**: 两个脚本，职责清晰，易于维护
- **参数可调**: 目标积压、间隔时间等关键参数可配置
- **状态透明**: 详细的流量控制状态和积压任务数量显示
- **手动管理**: API Key切换通过手动操作，避免自动化风险
