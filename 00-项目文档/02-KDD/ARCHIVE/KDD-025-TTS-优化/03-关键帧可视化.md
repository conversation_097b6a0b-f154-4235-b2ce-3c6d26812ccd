# TTS优化关键帧可视化

## 数据结构生命周期变化图

```mermaid
graph TD
    A[AI生成完成] --> B[P0阶段处理]
    B --> C[P1阶段处理]
    C --> D[全量音频就绪]
    
    A1[关键帧A: AI生成状态<br/>phoneticSymbols.audioUrl = ''<br/>examples.audioUrl = ''<br/>phraseBreakdown.audioUrl = ''<br/>ttsStatus = 'ai_generated'] --> A
    
    B1[关键帧B: P0完成状态<br/>phoneticSymbols.audioUrl = 'https://...'<br/>examples.audioUrl = ''<br/>phraseBreakdown.audioUrl = ''<br/>ttsStatus = 'word_audio_ready'] --> B
    
    C1[关键帧C: P1完成状态<br/>phoneticSymbols.audioUrl = 'https://...'<br/>examples.audioUrl = 'https://...'<br/>phraseBreakdown.audioUrl = 'https://...'<br/>ttsStatus = 'all_audio_ready'] --> C
    
    style A1 fill:#FFE4E1,stroke:#FF6B6B,stroke-width:3px
    style B1 fill:#E8F5E8,stroke:#4CAF50,stroke-width:3px
    style C1 fill:#E3F2FD,stroke:#2196F3,stroke-width:3px
```

## 系统架构流程图

```mermaid
sequenceDiagram
    participant User as 用户
    participant iOS as iOS App
    participant API as API Worker
    participant TTS as TTS Worker
    participant DB as 数据库
    participant Azure as Azure TTS
    participant R2 as R2存储

    User->>iOS: 搜索单词
    iOS->>API: 请求单词定义
    
    Note over API: FC-TTS-01: 标准化AI内容
    API->>API: 预设音频字段为空字符串
    
    Note over API: FC-TTS-02: P0阶段同步音频生成
    API->>Azure: 生成单词发音音频(BrE/NAmE)
    Azure-->>API: 返回音频数据
    API->>R2: 上传单词音频
    R2-->>API: 返回音频URL
    
    Note over API: FC-TTS-03: 保存带P0音频的数据
    API->>DB: 保存单词数据(word_audio_ready状态)
    API-->>iOS: 返回带单词音频的数据
    
    Note over iOS: FC-TTS-06: 优化轮询逻辑
    iOS->>iOS: 单词音频立即可用，开始P1轮询
    
    Note over API: FC-TTS-04: 延迟触发P1处理
    API->>TTS: 50秒后触发P1处理
    
    Note over TTS: FC-TTS-05: P1阶段音频生成
    TTS->>DB: 查询word_audio_ready状态记录
    TTS->>Azure: 生成例句和短语音频
    Azure-->>TTS: 返回音频数据
    TTS->>R2: 上传例句短语音频
    TTS->>DB: 更新状态为all_audio_ready
    
    iOS->>API: P1轮询检查状态
    API-->>iOS: 返回all_audio_ready状态
    iOS->>iOS: 停止轮询，所有音频就绪
```

## 分支处理逻辑图

```mermaid
flowchart TD
    Start([用户请求单词]) --> Check{单词是否存在?}
    
    Check -->|否| Generate[AI生成单词内容]
    Check -->|是| Return[直接返回现有数据]
    
    Generate --> Standardize[FC-TTS-01: 标准化音频字段]
    Standardize --> P0Audio[FC-TTS-02: 生成P0音频]
    
    P0Audio --> P0Success{P0音频生成成功?}
    P0Success -->|是| SaveP0[FC-TTS-03: 保存word_audio_ready状态]
    P0Success -->|否| SavePending[降级保存pending状态]
    
    SaveP0 --> TriggerP1[FC-TTS-04: 延迟触发P1处理]
    SavePending --> TriggerLegacy[触发传统全量处理]
    
    TriggerP1 --> P1Process[FC-TTS-05: P1阶段处理]
    TriggerLegacy --> LegacyProcess[传统全量处理]
    
    P1Process --> P1Success{P1处理成功?}
    P1Success -->|是| UpdateComplete[更新all_audio_ready状态]
    P1Success -->|否| KeepP0[保持word_audio_ready状态]
    
    LegacyProcess --> LegacySuccess{传统处理成功?}
    LegacySuccess -->|是| UpdateComplete
    LegacySuccess -->|否| KeepPending[保持pending状态]
    
    Return --> CheckStatus{检查音频状态}
    CheckStatus -->|all_audio_ready| Complete[所有音频就绪]
    CheckStatus -->|word_audio_ready| StartP1Poll[开始P1轮询]
    CheckStatus -->|pending| StartLegacyPoll[开始传统轮询]
    
    UpdateComplete --> Complete
    KeepP0 --> StartP1Poll
    KeepPending --> StartLegacyPoll
    
    style Generate fill:#FFE4E1,stroke:#FF6B6B,stroke-width:2px
    style P0Audio fill:#FFF3E0,stroke:#FF9800,stroke-width:2px
    style P1Process fill:#E8F5E8,stroke:#4CAF50,stroke-width:2px
    style Complete fill:#E3F2FD,stroke:#2196F3,stroke-width:2px
```

## 性能优化对比图

```mermaid
gantt
    title TTS处理时间对比
    dateFormat X
    axisFormat %s
    
    section 传统方案
    等待所有音频生成    :done, legacy, 0, 60s
    用户可以听单词      :milestone, 60s
    
    section 优化方案
    P0单词音频生成      :done, p0, 0, 5s
    用户可以听单词      :milestone, 5s
    P1例句短语音频      :done, p1, 50s, 60s
    所有音频完成        :milestone, 60s
```

## 关键指标

### 用户体验提升
- **单词发音可用时间**: 从60秒降低到5秒 (91.7%提升)
- **首次音频响应**: 立即可用 vs 需要等待
- **轮询频率优化**: P1阶段50秒起步，减少无效请求

### 系统性能优化
- **关键路径分离**: 核心功能(单词发音) vs 增强功能(例句短语)
- **资源利用**: 同步处理小文件，异步处理大文件
- **错误恢复**: P0失败时降级到传统流程，保证可用性
