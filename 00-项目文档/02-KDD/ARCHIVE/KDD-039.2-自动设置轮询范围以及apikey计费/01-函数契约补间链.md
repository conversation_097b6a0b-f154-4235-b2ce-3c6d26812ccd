# 需求："TTS Worker自动设置轮询范围以及API Key计费统计" 的函数契约补间链 (V1.0)

## 0. 依赖关系与影响分析

- [重用] `cloudflare/workers/tts/src/services/task-manager.service.ts`: 继续使用现有的 `batchInsertTasks` 和 `getTaskStatistics` 函数
- [重用] `cloudflare/workers/tts/src/services/realtime-tts.service.ts`: 继续使用 `processBatchRealtimeTTS` 函数进行TTS任务处理
- [新增] `cloudflare/d1/tts-db/migrations/0004_workflow_config_and_billing.sql`: 创建工作流全局配置表，支持轮询窗口和计费统计
- [新增] `cloudflare/workers/tts/src/services/billing-tracker.service.ts`: 实现Azure API Key计费统计服务
- [新增] `cloudflare/workers/tts/src/services/optimized-polling.service.ts`: 实现智能轮询窗口管理
- [修改] `cloudflare/workers/tts/src/index.ts`: 集成计费统计和轮询窗口管理到主Worker入口
- [修改] `cloudflare/workers/tts/src/types/realtime-tts-types.ts`: 添加计费信息相关的类型定义
- [修改] `senseword-content-factory/workflow/08-音频生成/scripts/02_submit_tts_tasks_optimized.py`: 集成计费信息显示和轮询窗口管理

## 1. 项目文件结构概览 (Project File Structure Overview)

```
senseword-ios/
├── cloudflare/
│   ├── d1/
│   │   └── tts-db/
│   │       └── migrations/
│   │           └── 0004_workflow_config_and_billing.sql    # [新增] 工作流配置表迁移
│   └── workers/
│       └── tts/
│           └── src/
│               ├── services/
│               │   ├── billing-tracker.service.ts          # [新增] 计费统计服务
│               │   ├── optimized-polling.service.ts        # [新增] 轮询窗口管理服务
│               │   ├── task-manager.service.ts             # [重用] 任务管理服务
│               │   └── realtime-tts.service.ts             # [重用] TTS处理服务
│               ├── types/
│               │   └── realtime-tts-types.ts               # [修改] 添加计费类型定义
│               └── index.ts                                # [修改] 主Worker入口
└── senseword-content-factory/
    └── workflow/
        └── 08-音频生成/
            └── scripts/
                └── 02_submit_tts_tasks_optimized.py        # [修改] 本地提交脚本
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/tts/polling-optimization-billing`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-tts-polling-billing
- 基础分支: `main`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout main
    # git pull origin main
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-tts-polling-billing -b feature/tts/polling-optimization-billing main
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(db): create workflow config table for polling window and billing tracking
- [ ] feat(billing): implement Azure API Key billing tracker service
- [ ] feat(polling): implement optimized polling window management service
- [ ] feat(types): add billing info types to realtime-tts-types
- [ ] feat(worker): integrate billing tracking into task submission flow
- [ ] feat(worker): add billing management API endpoints
- [ ] feat(worker): integrate polling window management into scheduled tasks
- [ ] feat(script): add billing status display to Python submission script
- [ ] feat(script): add billing status query command line option
- [ ] test(billing): verify billing calculation accuracy and API endpoints
- [ ] docs(billing): update API documentation with billing endpoints

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 本地任务提交处理器

- 职责: 处理用户通过Python脚本提交的TTS任务，执行批量插入并触发计费统计和轮询窗口设置
- 函数签名: `handleWordTTSSubmit(request: Request, env: Env): Promise<Response>`
- 所在文件: `cloudflare/workers/tts/src/index.ts`

>>>>> 输入 (Input): SubmitWordTTSRequest

由本地Python脚本发送的单词级TTS任务提交请求。

```typescript
interface SubmitWordTTSRequest {
  word: string;                    // 目标单词
  tasks: TTSTaskInput[];          // TTS任务列表
}

interface TTSTaskInput {
  ttsId: string;                  // 任务唯一标识
  text: string;                   // 待转换文本
  type: 'bre' | 'name' | 'ipa';   // TTS类型
}
```

<<<<< 输出 (Output): SubmitWordTTSResponse

包含任务插入结果和实时计费信息的响应。

```typescript
interface SubmitWordTTSResponse {
  success: boolean;               // 是否成功入库
  word: string;                   // 处理的单词
  received: number;               // 接收的任务数
  inserted: number;               // 成功入库的任务数
  failed_tasks: string[];         // 失败的ttsId列表
  timestamp: string;              // 处理时间戳
  billing?: BillingInfo | null;   // 计费信息（新增）
}

interface BillingInfo {
  totalCharacters: number;        // 总字符数
  totalCostUSD: number;          // 总成本（美元）
  newCharacters: number;         // 本次新增字符数
  newCostUSD: number;            // 本次新增成本（美元）
  lastUpdated: string;           // 最后更新时间
}
```

---

### [FC-02]: 任务批量插入服务

- 职责: 将TTS任务批量插入到D1数据库，确保数据完整性和一致性
- 函数签名: `batchInsertTasks(tasks: TTSTaskInput[], env: Env): Promise<BatchInsertResult>`
- 所在文件: `cloudflare/workers/tts/src/services/task-manager.service.ts`

>>>>> 输入 (Input): TTSTaskInput[]

与FC-01中的tasks字段相同的TTS任务数组。

```typescript
interface TTSTaskInput {
  ttsId: string;                  // 任务唯一标识
  text: string;                   // 待转换文本
  type: 'bre' | 'name' | 'ipa';   // TTS类型
}
```

<<<<< 输出 (Output): BatchInsertResult

批量插入操作的详细结果。

```typescript
interface BatchInsertResult {
  success: boolean;               // 整体是否成功
  inserted: number;               // 成功插入的任务数
  failed: string[];               // 失败的ttsId列表
  duplicates: string[];           // 重复的ttsId列表
  errors: Array<{                 // 详细错误信息
    ttsId: string;
    error: string;
  }>;
}
```

---

### [FC-03]: 计费统计处理器

- 职责: 处理任务提交后的计费更新，统计字符消耗并计算Azure API成本
- 函数签名: `handleTaskSubmissionBilling(env: Env, submittedTasks: TTSTaskInput[]): Promise<BillingUpdateResult>`
- 所在文件: `cloudflare/workers/tts/src/services/billing-tracker.service.ts`

>>>>> 输入 (Input): TTSTaskInput[]

成功插入数据库的TTS任务列表，用于计费统计。

```typescript
interface TTSTaskInput {
  ttsId: string;                  // 任务唯一标识
  text: string;                   // 待转换文本（计费基础）
  type: 'bre' | 'name' | 'ipa';   // TTS类型
}
```

**中间数据结构变化过程**:

1. **字符数统计阶段**:
```typescript
interface CharacterCountResult {
  newCharacters: number;          // 新提交任务的字符数
  totalCharacters: number;        // 累计总字符数
}
```

2. **计费计算阶段**:
```typescript
interface CostCalculationResult {
  newCharacters: number;          // 新增字符数
  newCostUSD: number;            // 新增成本（$15/百万字符）
  totalCharacters: number;        // 累计字符数
  totalCostUSD: number;          // 累计成本
}
```

3. **数据库更新阶段**:
```typescript
interface ApiKeyBillingStats {
  totalCharacters: number;        // 总字符数
  totalCostUSD: number;          // 总成本（美元）
  lastUpdated: string;           // 最后更新时间
  charactersSinceLastBilling: number; // 自上次计费以来的字符数
}
```

<<<<< 输出 (Output): BillingUpdateResult

计费更新的完整结果，包含成功状态和详细计费信息。

```typescript
interface BillingUpdateResult {
  success: boolean;               // 计费更新是否成功
  billing: {
    totalCharacters: number;      // 累计总字符数
    totalCostUSD: number;        // 累计总成本（美元）
    newCharacters: number;       // 本次新增字符数
    newCostUSD: number;          // 本次新增成本（美元）
    lastUpdated: string;         // 最后更新时间
  };
}
```

---

### [FC-04]: 轮询窗口管理器

- 职责: 根据提交的任务数量自动设置或延长轮询窗口，实现按需轮询
- 函数签名: `extendPollingWindow(env: Env, newTaskCount: number): Promise<PollingWindowResult>`
- 所在文件: `cloudflare/workers/tts/src/services/optimized-polling.service.ts`

>>>>> 输入 (Input): PollingWindowInput

任务提交后触发轮询窗口设置的输入参数。

```typescript
interface PollingWindowInput {
  newTaskCount: number;           // 新提交的任务数量
  currentTime: Date;              // 当前时间
}
```

**中间数据结构变化过程**:

1. **时间估算阶段**:
```typescript
interface TimeEstimation {
  additionalMinutes: number;      // 基于任务数量估算的处理时间
  estimatedEndTime: Date;         // 预估结束时间
}
```

2. **现有窗口检查阶段**:
```typescript
interface CurrentWindowStatus {
  exists: boolean;                // 是否存在现有窗口
  isActive: boolean;              // 现有窗口是否仍然有效
  currentEndTime?: Date;          // 现有窗口的结束时间
  estimatedTasks?: number;        // 现有窗口的预估任务数
}
```

3. **窗口配置更新阶段**:
```typescript
interface PollingWindowConfig {
  enabled: boolean;               // 是否启用轮询
  endTime: string;               // 轮询结束时间（ISO字符串）
  estimatedTasks: number;        // 预估任务总数
  lastExtended: string;          // 最后延长时间
}
```

<<<<< 输出 (Output): PollingWindowResult

轮询窗口设置的结果，包含窗口状态和时间信息。

```typescript
interface PollingWindowResult {
  success: boolean;               // 窗口设置是否成功
  action: 'created' | 'extended' | 'maintained'; // 执行的操作类型
  windowConfig: {
    enabled: boolean;             // 轮询是否启用
    endTime: string;             // 轮询结束时间
    estimatedTasks: number;      // 预估任务总数
    remainingMinutes: number;    // 剩余轮询时间（分钟）
  };
  message: string;               // 操作描述信息
}
```

---

### [FC-05]: 定时任务轮询检查器

- 职责: 在Cloudflare Worker定时任务中检查是否应该执行轮询，避免无效的数据库查询
- 函数签名: `shouldPoll(env: Env): Promise<PollingCheckResult>`
- 所在文件: `cloudflare/workers/tts/src/services/optimized-polling.service.ts`

>>>>> 输入 (Input): PollingCheckInput

定时任务触发时的检查输入。

```typescript
interface PollingCheckInput {
  currentTime: Date;              // 当前时间
  cronExpression: string;         // 触发的cron表达式
}
```

**中间数据结构变化过程**:

1. **配置读取阶段**:
```typescript
interface StoredPollingConfig {
  config_key: string;             // 配置键："polling_window"
  config_value: string;           // JSON格式的配置值
  updated_at: string;             // 最后更新时间
}
```

2. **配置解析阶段**:
```typescript
interface ParsedPollingConfig {
  enabled: boolean;               // 是否启用轮询
  endTime: Date;                 // 轮询结束时间（已解析）
  estimatedTasks: number;        // 预估任务数
  lastExtended: Date;            // 最后延长时间（已解析）
}
```

3. **时间窗口验证阶段**:
```typescript
interface WindowValidation {
  isWithinWindow: boolean;        // 是否在轮询窗口内
  isExpired: boolean;            // 窗口是否已过期
  remainingMinutes: number;      // 剩余时间（分钟）
  shouldDisable: boolean;        // 是否应该禁用过期窗口
}
```

<<<<< 输出 (Output): PollingCheckResult

轮询检查的结果，决定是否执行后续的任务处理。

```typescript
interface PollingCheckResult {
  shouldPoll: boolean;            // 是否应该执行轮询
  reason: string;                 // 决策原因
  windowStatus: {
    enabled: boolean;             // 轮询窗口是否启用
    isActive: boolean;           // 窗口是否活跃
    remainingMinutes: number;    // 剩余时间
    estimatedTasks: number;      // 预估任务数
  };
  action?: 'disable_expired';     // 需要执行的清理动作
}
```

---

### [FC-06]: 计费状态查询API

- 职责: 提供HTTP API端点供外部查询当前API Key的计费状态
- 函数签名: `handleGetBillingStatus(env: Env): Promise<Response>`
- 所在文件: `cloudflare/workers/tts/src/index.ts`

>>>>> 输入 (Input): BillingStatusRequest

HTTP GET请求，无请求体。

```typescript
interface BillingStatusRequest {
  method: 'GET';                  // HTTP方法
  path: '/billing/status';        // 请求路径
  headers: Record<string, string>; // 请求头
}
```

**中间数据结构变化过程**:

1. **数据库查询阶段**:
```typescript
interface StoredBillingConfig {
  config_key: string;             // 配置键："azure_key_billing"
  config_value: string;           // JSON格式的计费数据
  updated_at: string;             // 最后更新时间
}
```

2. **计费数据解析阶段**:
```typescript
interface ParsedBillingStats {
  totalCharacters: number;        // 总字符数
  totalCostUSD: number;          // 总成本（美元）
  charactersSinceLastBilling: number; // 自上次计费以来的字符数
  lastUpdated: string;           // 最后更新时间
}
```

<<<<< 输出 (Output): BillingStatusResponse

包含完整计费状态信息的HTTP响应。

```typescript
interface BillingStatusResponse {
  success: boolean;               // 查询是否成功
  billing: {
    totalCharacters: number;      // 累计总字符数
    totalCostUSD: number;        // 累计总成本（美元）
    charactersSinceLastBilling: number; // 自上次计费以来的字符数
    lastUpdated: string;         // 最后更新时间
    pricePerMillionChars: number; // 每百万字符价格（$15）
  };
  timestamp: string;             // 响应时间戳
}
```

---

### [FC-07]: 计费重置API

- 职责: 提供HTTP API端点供手动重置API Key计费统计（用于更换API Key时）
- 函数签名: `handleResetBilling(env: Env): Promise<Response>`
- 所在文件: `cloudflare/workers/tts/src/index.ts`

>>>>> 输入 (Input): BillingResetRequest

HTTP POST请求，用于重置计费统计。

```typescript
interface BillingResetRequest {
  method: 'POST';                 // HTTP方法
  path: '/billing/reset';         // 请求路径
  headers: Record<string, string>; // 请求头
  body?: {};                      // 空请求体
}
```

**中间数据结构变化过程**:

1. **重置数据准备阶段**:
```typescript
interface ResetBillingStats {
  totalCharacters: 0;             // 重置为0
  totalCostUSD: 0;               // 重置为0
  lastUpdated: string;           // 当前时间
  charactersSinceLastBilling: 0; // 重置为0
}
```

2. **数据库更新阶段**:
```typescript
interface BillingConfigUpdate {
  config_key: 'azure_key_billing'; // 固定配置键
  config_value: string;           // JSON序列化的重置数据
  updated_at: string;             // 数据库更新时间
}
```

<<<<< 输出 (Output): BillingResetResponse

计费重置操作的结果响应。

```typescript
interface BillingResetResponse {
  success: boolean;               // 重置是否成功
  message: string;               // 操作结果消息
  timestamp: string;             // 操作时间戳
}
```

---

### [FC-08]: Python脚本计费信息显示器

- 职责: 在Python脚本中处理Worker返回的计费信息并向用户展示
- 函数签名: `submit_word_tasks(self, word: str, tasks: List[Dict]) -> Dict`
- 所在文件: `senseword-content-factory/workflow/08-音频生成/scripts/02_submit_tts_tasks_optimized.py`

>>>>> 输入 (Input): WordTaskSubmissionInput

Python脚本向Worker提交任务的输入数据。

```python
class WordTaskSubmissionInput:
    word: str                     # 目标单词
    tasks: List[Dict]            # TTS任务列表

# 其中tasks的结构为:
task_dict = {
    "ttsId": str,                # 任务唯一标识
    "text": str,                 # 待转换文本
    "type": str                  # TTS类型 ('bre', 'name', 'ipa')
}
```

**中间数据结构变化过程**:

1. **HTTP请求构建阶段**:
```python
class WorkerRequestData:
    url: str                     # Worker端点URL
    headers: Dict[str, str]      # 请求头
    json_data: Dict              # JSON请求体
    timeout: int                 # 超时设置
```

2. **Worker响应解析阶段**:
```python
class WorkerResponseData:
    status_code: int             # HTTP状态码
    json_response: Dict          # 解析后的JSON响应
    success: bool                # 请求是否成功
```

3. **计费信息提取阶段**:
```python
class ExtractedBillingInfo:
    has_billing: bool            # 是否包含计费信息
    new_characters: int          # 新增字符数
    new_cost_usd: float         # 新增成本
    total_characters: int        # 累计字符数
    total_cost_usd: float       # 累计成本
    last_updated: str           # 最后更新时间
```

<<<<< 输出 (Output): TaskSubmissionResult

包含任务提交结果和计费信息的Python字典。

```python
class TaskSubmissionResult:
    success: bool                # 提交是否成功
    submitted: int               # 提交的任务数
    failed: int                  # 失败的任务数
    inserted: int                # 成功插入的任务数
    failed_tasks: List[str]      # 失败的任务ID列表
    billing: Optional[Dict]      # 计费信息（如果有）

# billing字典结构:
billing_info = {
    "newCharacters": int,        # 新增字符数
    "newCostUSD": float,        # 新增成本
    "totalCharacters": int,      # 累计字符数
    "totalCostUSD": float,      # 累计成本
    "lastUpdated": str          # 最后更新时间
}
```

---

### [FC-09]: Python脚本计费状态查询器

- 职责: 提供独立的计费状态查询功能，供用户通过命令行查看API Key使用情况
- 函数签名: `get_billing_status(self) -> Optional[Dict]`
- 所在文件: `senseword-content-factory/workflow/08-音频生成/scripts/02_submit_tts_tasks_optimized.py`

>>>>> 输入 (Input): BillingStatusQueryInput

Python脚本查询计费状态的输入参数。

```python
class BillingStatusQueryInput:
    worker_base_url: str         # Worker基础URL
    billing_endpoint: str        # 计费状态端点 "/billing/status"
    timeout: int                 # 请求超时时间
    headers: Dict[str, str]      # HTTP请求头
```

**中间数据结构变化过程**:

1. **HTTP请求执行阶段**:
```python
class BillingStatusRequest:
    url: str                     # 完整的请求URL
    method: str                  # HTTP方法 "GET"
    headers: Dict[str, str]      # 请求头
    timeout: int                 # 超时设置
```

2. **响应数据解析阶段**:
```python
class BillingStatusRawResponse:
    status_code: int             # HTTP状态码
    json_data: Dict              # 原始JSON响应
    success_flag: bool           # 响应中的success字段
```

3. **计费数据格式化阶段**:
```python
class FormattedBillingDisplay:
    total_characters: str        # 格式化的总字符数（带千分位）
    total_cost: str             # 格式化的总成本（4位小数）
    price_per_million: str      # 每百万字符价格
    last_updated: str           # 最后更新时间
```

<<<<< 输出 (Output): BillingStatusQueryResult

计费状态查询的结果，包含格式化的显示信息。

```python
class BillingStatusQueryResult:
    success: bool                # 查询是否成功
    billing_data: Optional[Dict] # 计费数据（如果成功）
    error_message: Optional[str] # 错误信息（如果失败）

# billing_data字典结构:
billing_data = {
    "totalCharacters": int,      # 总字符数
    "totalCostUSD": float,      # 总成本（美元）
    "charactersSinceLastBilling": int, # 自上次计费以来的字符数
    "lastUpdated": str,         # 最后更新时间
    "pricePerMillionChars": float # 每百万字符价格
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/d1/tts-db/migrations/0004_workflow_config_and_billing.sql
cloudflare/workers/tts/src/services/billing-tracker.service.ts
cloudflare/workers/tts/src/services/optimized-polling.service.ts
cloudflare/workers/tts/src/services/task-manager.service.ts
cloudflare/workers/tts/src/services/realtime-tts.service.ts
cloudflare/workers/tts/src/types/realtime-tts-types.ts
cloudflare/workers/tts/src/index.ts
senseword-content-factory/workflow/08-音频生成/scripts/02_submit_tts_tasks_optimized.py
senseword-content-factory/workflow/08-音频生成/scripts/config.json
wrangler.toml
</context_files>

## 6. 核心业务流程伪代码

```typescript
// 主要业务流程：任务提交 + 计费统计 + 轮询窗口管理
async function handleWordTTSSubmitWithBilling(request: Request, env: Env): Promise<Response> {
    // [FC-01] 解析任务提交请求
    const { word, tasks } = await request.json() as SubmitWordTTSRequest

    // [FC-02] 批量插入TTS任务到数据库
    const insertResult = await batchInsertTasks(tasks, env)

    if (insertResult.inserted > 0) {
        // [FC-03] 处理计费统计（仅对成功插入的任务）
        const successfulTasks = tasks.slice(0, insertResult.inserted)
        const billingResult = await handleTaskSubmissionBilling(env, successfulTasks)

        // [FC-04] 自动设置/延长轮询窗口
        const pollingResult = await extendPollingWindow(env, insertResult.inserted)

        // 构建包含计费信息的响应
        const response: SubmitWordTTSResponse = {
            success: insertResult.failed.length === 0,
            word: word,
            received: tasks.length,
            inserted: insertResult.inserted,
            failed_tasks: insertResult.failed,
            timestamp: new Date().toISOString(),
            billing: billingResult.success ? billingResult.billing : null
        }

        return new Response(JSON.stringify(response), { status: 200 })
    }

    // 处理插入失败的情况
    return new Response(JSON.stringify({
        success: false,
        error: 'Failed to insert tasks'
    }), { status: 500 })
}

// 定时任务流程：智能轮询检查
async function scheduledTaskWithPollingCheck(event: ScheduledEvent, env: Env): Promise<void> {
    // [FC-05] 检查是否应该执行轮询
    const pollingCheck = await shouldPoll(env)

    if (!pollingCheck.shouldPoll) {
        console.log(`[TTS Worker] ${pollingCheck.reason}`)
        return // 静默退出，避免无效查询
    }

    // 执行正常的TTS任务处理流程
    const pendingTasks = await optimizedGetPendingTasks(env, 50)
    if (pendingTasks.length > 0) {
        await processBatchRealtimeTTS(pendingTasks, env)
    }
}

// Python脚本流程：任务提交 + 计费显示
def submit_tasks_with_billing_display(self, word: str, tasks: List[Dict]) -> Dict:
    # [FC-08] 提交任务到Worker
    result = self.submit_word_tasks(word, tasks)

    if result["success"] and result.get("billing"):
        # 显示计费信息
        billing = result["billing"]
        print(f"💰 计费更新: 新增{billing['newCharacters']}字符 (${billing['newCostUSD']:.4f})")
        print(f"📊 累计统计: {billing['totalCharacters']}字符 (${billing['totalCostUSD']:.4f})")

    return result

# Python脚本流程：计费状态查询
def query_billing_status(self) -> Optional[Dict]:
    # [FC-09] 查询计费状态
    billing_data = self.get_billing_status()

    if billing_data:
        print("💰 API Key计费状态:")
        print(f"   总字符数: {billing_data['totalCharacters']:,}")
        print(f"   总成本: ${billing_data['totalCostUSD']:.4f}")
        print(f"   价格: ${billing_data['pricePerMillionChars']}/百万字符")

    return billing_data
```
