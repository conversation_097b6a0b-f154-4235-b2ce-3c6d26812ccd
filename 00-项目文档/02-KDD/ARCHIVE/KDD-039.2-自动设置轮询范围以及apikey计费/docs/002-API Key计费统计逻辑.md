# 002-API Key计费统计逻辑

## 📋 概述

本文档详细记录了Azure TTS API Key计费统计系统的设计逻辑、实现方案和优化策略，实现了透明化的成本管理和智能的密钥轮换支持。

## 💰 计费系统架构

### 核心设计原则
1. **本地计算 + 云端统计**: 分离关注点，提升用户体验
2. **实时透明**: 任务提交后立即显示计费信息
3. **累计追踪**: 支持API Key使用量的长期跟踪
4. **轮换支持**: 为密钥轮换决策提供数据支持

### 数据流架构
```
Python脚本 → 本地计算显示 → 用户立即看到本次费用
     ↓
Worker → 读取workflow_config → 累加统计 → 更新workflow_config → 返回累计数据
```

## 🔧 技术实现

### 1. 数据存储结构

#### workflow_config表设计
```sql
CREATE TABLE workflow_config (
    config_key TEXT PRIMARY KEY,
    config_value TEXT NOT NULL,
    created_at TEXT DEFAULT (datetime('now')),
    updated_at TEXT DEFAULT (datetime('now'))
);
```

#### 计费数据结构
```typescript
interface ApiKeyBillingStats {
    totalCharacters: number;           // API Key累计总字符数
    totalCostUSD: number;             // API Key累计总成本
    lastUpdated: string;              // 最后更新时间
    charactersSinceLastBilling: number; // 自上次计费以来的字符数
}
```

### 2. 计费逻辑实现

#### 核心计费处理器
```typescript
async function handleTaskSubmissionBilling(
    env: Env, 
    submittedTasksCount: number,
    submittedCharacters: number
): Promise<BillingUpdateResult> {
    
    // 1. 获取当前计费统计
    const currentStats = await getCurrentBillingStats(env);
    
    // 2. 判断首次计费 vs 增量计费
    const isFirstBilling = currentStats.totalCharacters === 0;
    
    if (isFirstBilling) {
        // 首次计费: 汇总全表历史数据 + 本次提交
        const existingCharacters = await getTotalCharactersFromTasks(env);
        const totalCharacters = existingCharacters + submittedCharacters;
        
        updatedStats = {
            totalCharacters: totalCharacters,
            totalCostUSD: calculateCost(totalCharacters),
            lastUpdated: new Date().toISOString(),
            charactersSinceLastBilling: submittedCharacters
        };
    } else {
        // 增量计费: 直接累加到现有统计
        updatedStats = {
            totalCharacters: currentStats.totalCharacters + submittedCharacters,
            totalCostUSD: calculateCost(currentStats.totalCharacters + submittedCharacters),
            lastUpdated: new Date().toISOString(),
            charactersSinceLastBilling: currentStats.charactersSinceLastBilling + submittedCharacters
        };
    }
    
    // 3. 更新到workflow_config表
    await updateBillingStats(env, updatedStats);
    
    return { success: true, billing: updatedStats };
}
```

### 3. 成本计算模型

#### Azure TTS定价
```typescript
const AZURE_TTS_PRICE_PER_MILLION_CHARS = 15; // $15/百万字符

function calculateCost(characters: number): number {
    return (characters / 1_000_000) * AZURE_TTS_PRICE_PER_MILLION_CHARS;
}
```

#### 实际成本示例
```
单个任务: 平均23字符 → $0.000345
单个单词: 平均22个任务 → $0.0076
1000个单词: 约22,000个任务 → $7.59
API Key轮换建议: 累计成本达到$50-100时考虑轮换
```

## 📊 双重显示策略

### 1. 本地计算显示（Python脚本）
```python
# 立即计算并显示本次提交费用
local_characters = sum(len(task["textToSpeak"]) for task in tasks)
local_cost = (local_characters / 1_000_000) * 15
print(f"💰 本次提交: {local_characters}字符 (${local_cost:.4f})")
```

**优势**:
- 无需等待网络请求
- 用户立即获得反馈
- 不依赖云端计算结果

### 2. 云端累计统计（Worker）
```typescript
// 返回API Key的累计使用统计
return {
    totalCharacters: updatedStats.totalCharacters,
    totalCostUSD: updatedStats.totalCostUSD,
    lastUpdated: updatedStats.lastUpdated,
    charactersSinceLastBilling: updatedStats.charactersSinceLastBilling
};
```

**优势**:
- 提供密钥轮换决策依据
- 长期成本跟踪
- 多客户端数据一致性

## 🔄 首次计费 vs 增量计费

### 首次计费逻辑
```typescript
if (currentStats.totalCharacters === 0) {
    // 汇总全表历史数据作为基准
    const existingCharacters = await getTotalCharactersFromTasks(env);
    const totalCharacters = existingCharacters + submittedCharacters;
    
    console.log(`首次计费: 历史${existingCharacters}字符 + 本次${submittedCharacters}字符`);
}
```

**场景**: 
- 系统首次启用计费功能
- 计费数据被重置后的首次提交
- 需要建立历史数据基准

### 增量计费逻辑
```typescript
else {
    // 直接累加到现有统计
    const newTotalCharacters = currentStats.totalCharacters + submittedCharacters;
    
    console.log(`增量计费: ${currentStats.totalCharacters} + ${submittedCharacters}`);
}
```

**场景**:
- 日常任务提交
- 高频率的增量更新
- 性能优化的关键路径

## 🎯 业务价值分析

### 1. 成本透明化
```
实时成本显示示例:
✅ example: 22 个任务提交成功
   💰 本次提交: 469字符 ($0.0070)
   📊 API Key累计: 6,880字符 ($0.1032)
```

**价值**:
- 用户清楚了解每次操作的成本
- 支持成本预算和控制决策
- 提供API使用量的历史趋势

### 2. 密钥轮换支持
```typescript
// 使用率计算
const usage_percentage = (totalCostUSD / 100) * 100; // 相对于$100预算
const estimated_usage = `$${totalCostUSD.toFixed(4)} / $100 (${usage_percentage.toFixed(1)}%)`;
```

**轮换策略**:
- 成本阈值: $50-100时考虑轮换
- 使用率监控: 实时跟踪API Key消耗
- 预警机制: 接近限额时提前通知

### 3. 性能优化效果

#### 数据库操作优化
```
传统方案: 每次查询全表统计 → 高成本
优化方案: 只操作workflow_config表 → 极低成本

操作对比:
- 读取: 1条配置记录 vs 130万任务记录
- 写入: 1条配置更新 vs 复杂的聚合计算
- 成本: $0.001 vs $1.30 (1300倍差异)
```

## 🔧 API管理接口

### 1. 计费状态查询
```bash
curl https://senseword-tts-worker.zhouqi-aaha.workers.dev/billing/status
```

**响应示例**:
```json
{
    "success": true,
    "billing": {
        "totalCharacters": 8147,
        "totalCostUSD": 0.1222,
        "charactersSinceLastBilling": 971,
        "pricePerMillionChars": 15,
        "lastUpdated": "2025-07-16T10:58:02.704Z"
    }
}
```

### 2. 计费重置功能
```bash
curl -X POST https://senseword-tts-worker.zhouqi-aaha.workers.dev/billing/reset
```

**使用场景**:
- API Key轮换后重置统计
- 新计费周期开始
- 测试环境数据清理

## 📈 监控和分析

### 1. 成本趋势分析
```
日常使用模式:
- 单词处理: 20-25字符/任务
- 批量处理: 1000单词 ≈ $7
- 月度预算: $100 ≈ 14,000单词

成本优化建议:
- 批量提交: 减少网络开销
- 文本优化: 去除不必要的字符
- 缓存策略: 避免重复处理
```

### 2. 使用量预警
```typescript
// 预警阈值设置
const WARNING_THRESHOLDS = {
    low: 0.5,      // 50% - 提醒注意
    medium: 0.8,   // 80% - 建议准备轮换
    high: 0.95     // 95% - 紧急轮换
};
```

## 🚀 未来优化方向

### 1. 多API Key管理
- 支持多个API Key的并行使用
- 自动负载均衡和故障转移
- 成本分摊和统计分析

### 2. 智能成本优化
- 基于历史数据的成本预测
- 自动化的成本优化建议
- 动态定价策略适配

### 3. 高级分析功能
- 成本趋势图表
- 使用模式分析
- 异常检测和告警

---

**实施完成时间**: 2025-07-16  
**核心特性**: 双重显示、实时统计、轮换支持  
**性能提升**: 1300倍数据库操作成本降低  
**业务价值**: 透明化成本管理，支持智能决策
