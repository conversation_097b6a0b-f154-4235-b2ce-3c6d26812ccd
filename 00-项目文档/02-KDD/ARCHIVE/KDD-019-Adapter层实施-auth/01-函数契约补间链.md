# KDD-019 认证模块Adapter层实施 - 函数契约补间链

> **SenseWord iOS认证模块Adapter转译层完整开发指南**
> 基于TADA架构设计，严格按照技术方案实现
> 生成时间: 2025-06-26
> 版本: v1.0

---

## 0. 依赖关系与影响分析

### 重用组件
- **APIClient.swift**: 已实现的HTTP客户端基础设施，提供统一的网络请求接口 ✅
- **APIError.swift**: 已实现的统一错误处理枚举，包含7个标准错误类型 ✅
- **APIConfig.swift**: 已实现的认证配置管理，提供静态密钥和双重认证头部生成 ✅
- **AdapterContainer.swift**: 已实现的依赖注入容器，管理API客户端实例 ✅

### 新增组件
- **AuthAPIAdapter.swift**: 认证API转译层，实现4个核心认证方法
- **AuthAPIModels.swift**: 认证API数据模型，包含请求和响应结构

### 技术架构确认
- **认证方式**: Session-based认证（非JWT），使用永久Session ID
- **双重认证**: 静态API密钥 + Session ID的双重验证机制
- **服务端点**: auth.senseword.app（认证服务）

---

## 1. 项目文件结构概览

```
SensewordApp/
├── Services/
│   └── Adapters/
│       └── AuthAPIAdapter.swift           # [新增] 认证API转译层
├── Models/
│   └── API/
│       └── AuthAPIModels.swift            # [新增] 认证API数据模型
├── Network/                               # [已实现] 网络基础设施
│   ├── APIClient.swift                    # HTTP客户端基础类
│   ├── APIConfig.swift                    # 认证配置管理
│   └── APIError.swift                     # 统一错误处理
├── DI/                                    # [已实现] 依赖注入层
│   └── AdapterContainer.swift             # 依赖注入容器
└── SensewordAppTests/                     # [新增] 测试文件
    └── AdapterTests/
        └── AuthAPIAdapterTests.swift      # 认证适配器测试
```

## 2. 分支策略建议

- **建议的特性分支名称**: `feature/auth-adapter-implementation`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-auth-adapter-worktree`
- **基础分支**: `dev`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-auth-adapter-worktree -b feature/auth-adapter-implementation dev
    ```

## 3. Commit 规划概要

- [ ] feat(auth-models): 实现认证API数据模型结构（4个核心方法）
- [ ] feat(auth-adapter): 实现AuthAPIAdapter核心认证方法（login, logout, logoutAll, healthCheck）
- [ ] feat(auth-adapter): 集成AdapterContainer依赖注入配置
- [ ] test(auth-adapter): 实现AuthAPIAdapter单元测试
- [ ] docs(auth-adapter): 完善认证模块文档和使用说明

## 4. 函数契约补间链

### [FC-01]: 用户登录认证转译器

- **职责**: 处理Apple ID登录请求，将前端认证凭证转译为后端API调用
- **函数签名**: `func login(idToken: String, provider: AuthProvider) async throws -> SessionLoginSuccessResponse`
- **所在文件**: `SensewordApp/Services/Adapters/AuthAPIAdapter.swift`

>>>>> 输入 (Input): Apple ID认证凭证

```swift
// 输入参数结构
idToken: String                    // Apple ID Token (JWT格式)
provider: AuthProvider            // 认证提供方，必需参数

// AuthProvider枚举定义
enum AuthProvider: String, Codable {
    case apple = "apple"
}
```

<<<<< 输出 (Output): Session登录成功响应

```swift
// 成功响应结构
struct SessionLoginSuccessResponse: Codable {
    let success: Bool                      // 固定为true
    let session: SessionInfo
}

struct SessionInfo: Codable {
    let sessionId: String                  // 永久Session ID (格式: "sess_" + 20位随机字符)
    let user: UserInfo
}

struct UserInfo: Codable {
    let id: String                         // 用户唯一标识
    let email: String                      // 用户邮箱
    let displayName: String                // 显示名称
    let isPro: Bool                        // Pro状态（计算属性）
}
```

---

### [FC-02]: 用户登出转译器

- **职责**: 处理用户登出请求，撤销当前Session实现安全登出
- **函数签名**: `func logout(sessionId: String, reason: LogoutReason?) async throws -> LogoutSuccessResponse`
- **所在文件**: `SensewordApp/Services/Adapters/AuthAPIAdapter.swift`

>>>>> 输入 (Input): Session登出请求

```swift
// 输入参数结构
sessionId: String                  // 当前用户的Session ID
reason: LogoutReason?              // 可选的登出原因

// LogoutReason枚举定义
enum LogoutReason: String, Codable {
    case userInitiated = "user_initiated"
    case security = "security"
    case admin = "admin"
}
```

<<<<< 输出 (Output): 登出成功响应

```swift
// 成功响应结构
struct LogoutSuccessResponse: Codable {
    let success: Bool                      // 固定为true
    let message: String                    // 用户友好的提示信息
    let sessionRevoked: Bool               // Session是否成功撤销
    let timestamp: String                  // 操作时间戳 (ISO 8601)
}
```

---

### [FC-03]: 全设备登出转译器

- **职责**: 处理全设备登出请求，撤销用户的所有Session
- **函数签名**: `func logoutAll(sessionId: String, reason: LogoutAllReason?) async throws -> LogoutAllSuccessResponse`
- **所在文件**: `SensewordApp/Services/Adapters/AuthAPIAdapter.swift`

>>>>> 输入 (Input): 全设备登出请求

```swift
// 输入参数结构
sessionId: String                  // 当前用户的Session ID
reason: LogoutAllReason?           // 可选的登出原因

// LogoutAllReason枚举定义
enum LogoutAllReason: String, Codable {
    case securityConcern = "security_concern"
    case deviceLost = "device_lost"
    case userRequest = "user_request"
}
```

<<<<< 输出 (Output): 全设备登出成功响应

```swift
// 成功响应结构
struct LogoutAllSuccessResponse: Codable {
    let success: Bool                      // 固定为true
    let message: String                    // 用户友好的提示信息
    let sessionsRevoked: Int               // 撤销的Session数量
    let timestamp: String                  // 操作时间戳 (ISO 8601)
}
```

---

### [FC-04]: 健康检查转译器

- **职责**: 检查认证服务状态，提供服务版本和环境信息
- **函数签名**: `func healthCheck() async throws -> HealthCheckResponse`
- **所在文件**: `SensewordApp/Services/Adapters/AuthAPIAdapter.swift`

>>>>> 输入 (Input): 无输入参数

```swift
// 无输入参数，仅需要静态API密钥认证
```

<<<<< 输出 (Output): 健康检查响应

```swift
// 成功响应结构
struct HealthCheckResponse: Codable {
    let status: String                     // 服务状态，固定为"healthy"
    let service: String                    // 服务名称，如"auth-worker"
    let timestamp: String                  // 检查时间戳 (ISO 8601)
    let environment: String                // 环境信息，如"production"
    let version: String                    // 服务版本号
}
```



## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/SensewordApp/Network/APIClient.swift
iOS/SensewordApp/Network/APIConfig.swift
iOS/SensewordApp/Network/APIError.swift
iOS/SensewordApp/DI/AdapterContainer.swift
0-KDD - 关键帧驱动开发/03-Docs/04-技术方案/KDD-018-iOS-Adapter转译层技术方案.md
0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md
iOS/SensewordAppTests/AdapterContainerTests.swift
iOS/SensewordAppTests/APIClientTests.swift
</context_files>

## 6. 核心业务流程伪代码

```swift
// 认证模块Adapter核心流程
class AuthAPIAdapter: AuthAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    // [FC-01] Apple登录流程
    func login(idToken: String, provider: AuthProvider) async throws -> SessionLoginSuccessResponse {
        // 1. 构建请求体
        let requestBody = LoginRequestBody(idToken: idToken, provider: provider)
        let bodyData = try JSONEncoder().encode(requestBody)

        // 2. 发起HTTP请求（仅静态密钥认证）
        return try await apiClient.request(
            endpoint: "/api/v1/auth/login",
            method: .POST,
            headers: APIConfig.staticHeaders.merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    // [FC-02] 用户登出流程
    func logout(sessionId: String, reason: LogoutReason?) async throws -> LogoutSuccessResponse {
        // 1. 构建请求体
        let requestBody = LogoutRequest(reason: reason)
        let bodyData = try JSONEncoder().encode(requestBody)

        // 2. 发起HTTP请求（双重认证）
        return try await apiClient.request(
            endpoint: "/api/v1/auth/logout",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    // [FC-03] 全设备登出流程
    func logoutAll(sessionId: String, reason: LogoutAllReason?) async throws -> LogoutAllSuccessResponse {
        // 1. 构建请求体
        let requestBody = LogoutAllRequest(reason: reason)
        let bodyData = try JSONEncoder().encode(requestBody)

        // 2. 发起HTTP请求（双重认证）
        return try await apiClient.request(
            endpoint: "/api/v1/auth/logout-all",
            method: .POST,
            headers: APIConfig.authHeaders(sessionId: sessionId).merging(["Content-Type": "application/json"]) { _, new in new },
            body: bodyData
        )
    }

    // [FC-04] 健康检查流程
    func healthCheck() async throws -> HealthCheckResponse {
        // 简单的GET请求，仅需静态密钥
        return try await apiClient.request(
            endpoint: "/api/v1/auth/health",
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    }
}
```