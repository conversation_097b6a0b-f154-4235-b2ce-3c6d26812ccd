## Story09｜搜索 - 从“即时需求"到“无尽探索"

故事主角：李明，一位正在阅读英文技术博客的开发者。
1.  遇到生词，唤醒工具：李明在阅读一篇关于系统架构的文章时，遇到了一个陌生的词：`idempotent`。他有些不确定其在技术语境下的精确含义。他立刻想起了SenseWord，这个他信赖的“深度单词解析器"。
2.  优雅地进入搜索：他打开SenseWord，主界面依然是那个他熟悉的“聚光灯无限内容流"。他自然地用手指向下滑动屏幕，顶部的搜索按钮随着他的手势逐渐变大，当达到一定阈值后，整个界面平滑地、带有高斯模糊效果地进入了搜索模式。这个交互让他感觉非常自然，像是拉开了一张半透明的幕布。
3.  智能的即时反馈：搜索框的占位词提示着他：“使用搜索用深思语境练单词"。他开始输入`idem...`，输入框下方立刻以列表形式，实时地从本地预存的词汇索引中展示出模糊匹配的结果，例如`idempotent`、`identity`等，每个词后面都附有其核心释义。
4.  LPLC原则的体现：他点击了`idempotent`。App首先检查本地缓存，发现没有。此时，并且只有在此时（Lazy Produce），App才向后端`api-worker`发起了一个请求。`api-worker`发现这个词在D1数据库中也不存在，于是立刻调用AI引擎，实时生成了一份关于`idempotent`的、包含德语“心语"解析（因为李明的母语是德语）的全新JSON，并将其存入数据库。
5.  从“工具"到“媒体"的无缝转换：搜索面板平滑收起，主界面的“单词锚点"立刻更新为`idempotent`及其核心信息。下方的“内容舞台"则展示出对其“心语"的深度解析。李明在满足了自己“查词"这个即时需求后，自然地向上滑动，立刻被`idempotent`的关联概念（例如 `stateless`, `retry`, `distributed system`）所吸引，无缝地进入了一场全新的“兴趣驱动无限探索之旅"。
哲学思考：搜索功能完美体现了LPLC原则和产品从“工具"到“媒体"的升维。它以最低的成本（只在需要时生产）满足了用户最高频的需求（查词），然后通过与“无限内容流"的无缝衔接，将一次性的工具使用，转化为一次长期的、充满价值的学习和探索体验。

---

# KDD-021 内容搜索功能配置文档

## 🎨 设计规范
> **重要**: 本项目严格遵循iOS设计规范，所有图标必须使用SF Symbols，禁止使用自定义图标或第三方图标库。

## 1. 视图层配置 (View Layer)

### 人类可读配置
```markdown
# SearchView 视图配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **视图ID**: SearchView
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **设计目标**: 实现优雅的搜索交互，从工具使用无缝转换到媒体探索

## 布局设计
### 整体布局
- **布局类型**: overlay
- **设计理念**: 搜索界面作为覆盖层，与主界面形成层次关系

### 搜索触发区域 (searchTrigger)
> **设计意图**: 通过下拉手势自然唤起搜索，模拟拉开幕布的感觉
- **区域类型**: gesture_zone
- **尺寸**: 顶部80pt高度
- **背景**: 透明

#### 组件列表
1. **搜索按钮**
   - **组件类型**: button
   - **图标**: SF Symbol `magnifyingglass.circle.fill`
   - **样式**: 圆形，渐变背景，动态缩放
   - **数据绑定**: `searchModel.triggerScale`
   - **交互行为**: 下拉手势触发

### 搜索面板 (searchPanel)
> **设计意图**: 半透明覆盖层，提供沉浸式搜索体验
- **区域类型**: floating_panel
- **尺寸**: 全屏覆盖
- **背景**: 高斯模糊背景

#### 组件列表
1. **搜索输入框**
   - **组件类型**: text_field
   - **样式**: 圆角，毛玻璃效果，大字体
   - **数据绑定**: `searchModel.searchText`
   - **交互行为**: 实时输入监听

2. **搜索建议列表**
   - **组件类型**: suggestion_list
   - **样式**: 卡片式列表
   - **数据绑定**: `searchModel.suggestions`
   - **交互行为**: 点击选择

## 动画效果
### 搜索唤起动画 (searchReveal)
> **设计理念**: 模拟拉开半透明幕布的物理感受
- **动画类型**: spring
- **参数**: 响应时间0.8秒，阻尼系数0.7
- **触发条件**: 下拉手势达到60pt阈值
- **视觉效果**: 高斯模糊背景渐现，搜索面板从顶部滑入

### 按钮缩放动画 (buttonScale)
> **设计理念**: 提供即时的视觉反馈，引导用户操作
- **动画类型**: easeOut
- **参数**: 0.2秒过渡时间
- **触发条件**: 下拉手势进行中
- **视觉效果**: 搜索按钮根据手势距离动态缩放



### 搜索收起动画 (searchDismiss)
> **设计理念**: 平滑过渡到内容探索状态
- **动画类型**: easeInOut
- **参数**: 0.6秒过渡时间
- **触发条件**: 选择搜索结果后
- **视觉效果**: 搜索面板向上滑出，背景模糊消失

## 状态管理
### 搜索激活状态 (searchActive)
- **触发条件**: `searchModel.isSearchActive`
- **视觉效果**: 显示搜索面板，背景模糊

### 输入状态 (inputting)
- **触发条件**: `!searchModel.searchText.isEmpty`
- **视觉效果**: 显示建议列表，隐藏占位提示

## 用户体验目标
1. 自然性：手势操作符合用户直觉，无学习成本
2. 即时性：输入反馈实时，搜索结果快速展现
3. 沉浸性：模糊背景营造专注的搜索环境
4. 连续性：从搜索到探索的无缝过渡

## 🔧 技术实现注意事项
- **图标规范**: 严格使用SF Symbols，禁止自定义图标或第三方图标库
- 手势识别优化，避免与系统手势冲突
- 搜索建议本地缓存，提升响应速度
- 键盘弹出时的布局自适应
- VoiceOver无障碍支持
```

### 机器可执行配置
```json
{
  "viewId": "SearchView",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchView.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "layout": {
    "type": "overlay",
    "sections": [
      {
        "id": "searchTrigger",
        "type": "gesture_zone",
        "height": 80,
        "gesture": {
          "type": "pull_down",
          "threshold": 60,
          "action": "activateSearch"
        },
        "components": [
          {
            "type": "button",
            "id": "searchButton",
            "binding": "searchModel.triggerScale",
            "icon": "magnifyingglass.circle.fill",
            "style": {
              "shape": "circle",
              "background": "gradient",
              "transform": "scale"
            }
          }
        ]
      },
      {
        "id": "searchPanel",
        "type": "floating_panel",
        "visibility": "conditional",
        "condition": "searchModel.isSearchActive",
        "background": {
          "type": "blur",
          "style": "systemUltraThinMaterial",
          "intensity": 0.8
        },
        "components": [
          {
            "type": "text_field",
            "id": "searchInput",
            "binding": "searchModel.searchText",
            "placeholder": "使用搜索用深思语境练单词",
            "style": {
              "cornerRadius": 12,
              "background": "glassmorphism",
              "fontSize": 18
            }
          },
          {
            "type": "suggestion_list",
            "id": "suggestions",
            "binding": "searchModel.suggestions",
            "visibility": "conditional",
            "condition": "!searchModel.searchText.isEmpty"
          }
        ]
      }
    ]
  },
  "animations": [
    {
      "name": "searchReveal",
      "type": "spring",
      "response": 0.8,
      "dampingFraction": 0.7,
      "trigger": "gesture_threshold"
    },
    {
      "name": "buttonScale",
      "type": "easeOut",
      "duration": 0.2,
      "trigger": "gesture_progress"
    },
    {
      "name": "searchDismiss",
      "type": "easeInOut",
      "duration": 0.6,
      "trigger": "selection_made"
    }
  ],
  "states": [
    {
      "name": "searchActive",
      "condition": "searchModel.isSearchActive",
      "effects": [
        {
          "target": "searchPanel",
          "property": "visibility",
          "value": "visible"
        },
        {
          "target": "background",
          "property": "blur",
          "value": 20.0
        }
      ]
    }
  ]
}
```

## 2. 视图模型配置 (ViewModel Layer)

### 人类可读配置
```markdown
# SearchViewModel 视图模型配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **视图模型ID**: SearchViewModel
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **职责**: 管理搜索状态、处理用户输入、协调搜索业务逻辑

## 状态管理
### 发布属性 (Published Properties)
> **设计原则**: 只暴露UI需要的状态，保持最小化原则

1. **isSearchActive**
   - **类型**: Bool
   - **初始值**: false
   - **用途**: 控制搜索面板的显示/隐藏状态
   - **更新时机**: 手势触发或搜索完成时

2. **searchText**
   - **类型**: String
   - **初始值**: ""
   - **用途**: 绑定搜索输入框的文本内容
   - **更新时机**: 用户输入时实时更新

3. **suggestions**
   - **类型**: [SearchSuggestion]
   - **初始值**: []
   - **用途**: 展示搜索建议列表
   - **更新时机**: searchText变化时触发更新

4. **triggerScale**
   - **类型**: CGFloat
   - **初始值**: 1.0
   - **用途**: 控制搜索按钮的缩放动画
   - **更新时机**: 下拉手势进行中

5. **isLoading**
   - **类型**: Bool
   - **初始值**: false
   - **用途**: 显示搜索加载状态
   - **更新时机**: 网络请求开始/结束时

### 计算属性 (Computed Properties)
1. **shouldShowSuggestions**
   - **类型**: Bool
   - **计算逻辑**: `!searchText.isEmpty && isSearchActive`
   - **依赖**: searchText, isSearchActive

2. **searchPlaceholder**
   - **类型**: String
   - **计算逻辑**: 根据用户语言返回本地化占位文本
   - **依赖**: userLanguage

## 用户操作处理
### 激活搜索 (activateSearch)
> **交互设计**: 通过下拉手势自然唤起搜索模式
- **触发方式**: 下拉手势达到阈值
- **参数**: gestureProgress: CGFloat
- **处理逻辑**:
  1. 更新triggerScale基于手势进度
  2. 达到阈值时设置isSearchActive = true
  3. 触发搜索面板显示动画
- **副作用**: 暂停主界面内容滚动

### 处理文本输入 (handleTextInput)
> **交互设计**: 实时响应用户输入，提供即时反馈
- **触发方式**: 文本框内容变化
- **参数**: newText: String
- **处理逻辑**:
  1. 更新searchText属性
  2. 防抖处理（300ms延迟）
  3. 调用本地索引服务获取建议
  4. 更新suggestions数组
- **副作用**: 触发本地搜索索引查询

### 选择搜索结果 (selectSearchResult)
> **交互设计**: 从工具使用转换到媒体探索
- **触发方式**: 点击搜索建议项
- **参数**: selectedWord: String
- **处理逻辑**:
  1. 设置isSearchActive = false
  2. 调用内容服务加载单词详情
  3. 更新主界面单词锚点
  4. 触发搜索面板收起动画
- **副作用**: 切换到内容探索模式

### 取消搜索 (cancelSearch)
> **交互设计**: 提供退出搜索的便捷方式
- **触发方式**: 点击取消按钮
- **参数**: 无
- **处理逻辑**:
  1. 清空searchText
  2. 设置isSearchActive = false
  3. 重置triggerScale = 1.0
- **副作用**: 恢复主界面正常状态

## 服务依赖
### SearchService (搜索服务)
- **依赖方法**: getSuggestions, searchWord
- **调用时机**: 文本输入时、选择结果时
- **错误处理**: 网络错误时显示本地缓存结果

### ContentService (内容服务)
- **依赖方法**: loadWordContent
- **调用时机**: 选择搜索结果后
- **错误处理**: 加载失败时显示错误提示

## 技术实现注意事项
- 使用Combine框架处理异步搜索请求
- 实现防抖机制避免频繁API调用
- 搜索建议本地缓存策略
- 内存管理：及时释放大型搜索结果
```

### 机器可执行配置
```json
{
  "viewModelId": "SearchViewModel",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchViewModel.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "stateManagement": {
    "publishedProperties": [
      {
        "name": "isSearchActive",
        "type": "Bool",
        "initialValue": "false",
        "description": "控制搜索面板显示状态"
      },
      {
        "name": "searchText",
        "type": "String",
        "initialValue": "\"\"",
        "description": "搜索输入框文本内容"
      },
      {
        "name": "suggestions",
        "type": "[SearchSuggestion]",
        "initialValue": "[]",
        "description": "搜索建议列表"
      },
      {
        "name": "triggerScale",
        "type": "CGFloat",
        "initialValue": "1.0",
        "description": "搜索按钮缩放比例"
      },
      {
        "name": "isLoading",
        "type": "Bool",
        "initialValue": "false",
        "description": "搜索加载状态"
      }
    ],
    "computedProperties": [
      {
        "name": "shouldShowSuggestions",
        "type": "Bool",
        "logic": "!searchText.isEmpty && isSearchActive",
        "dependencies": ["searchText", "isSearchActive"]
      },
      {
        "name": "searchPlaceholder",
        "type": "String",
        "logic": "LocalizationService.getSearchPlaceholder()",
        "dependencies": ["userLanguage"]
      }
    ]
  },
  "actions": [
    {
      "name": "activateSearch",
      "parameters": [
        {
          "name": "gestureProgress",
          "type": "CGFloat"
        }
      ],
      "logic": {
        "type": "sequential",
        "steps": [
          "updateTriggerScale(gestureProgress)",
          "checkThreshold(gestureProgress)",
          "setSearchActive(true)",
          "triggerAnimation(searchReveal)"
        ],
        "effects": ["pauseMainContent"]
      }
    },
    {
      "name": "handleTextInput",
      "parameters": [
        {
          "name": "newText",
          "type": "String"
        }
      ],
      "logic": {
        "type": "debounced",
        "delay": 300,
        "steps": [
          "updateSearchText(newText)",
          "searchService.getSuggestions(newText)",
          "updateSuggestions(results)"
        ],
        "effects": ["triggerLocalSearch"]
      }
    },
    {
      "name": "selectSearchResult",
      "parameters": [
        {
          "name": "selectedWord",
          "type": "String"
        }
      ],
      "logic": {
        "type": "sequential",
        "steps": [
          "setSearchActive(false)",
          "contentService.loadWordContent(selectedWord)",
          "updateMainView(wordContent)",
          "triggerAnimation(searchDismiss)"
        ],
        "effects": ["switchToExploreMode"]
      }
    }
  ],
  "dependencies": [
    {
      "service": "SearchService",
      "methods": ["getSuggestions", "searchWord"],
      "errorHandling": "showCachedResults"
    },
    {
      "service": "ContentService",
      "methods": ["loadWordContent"],
      "errorHandling": "showErrorMessage"
    }
  ]
}
```

## 3. 业务层配置 (Business Layer)

### 人类可读配置
```markdown
# SearchService 业务服务配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **服务ID**: SearchService
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **业务职责**: 处理搜索逻辑、管理本地索引、协调LPLC原则的实现

## 业务方法
### ~~获取搜索建议 (getSuggestions)~~ [架构调整]
> **架构说明**: 搜索建议功能已移至LocalIndexService，SearchService不再直接提供此方法
- **新实现**: 通过LocalIndexService.searchSuggestions方法实现
- **调用方式**: SearchService内部协调LocalIndexService和缓存策略
- **性能优势**: 本地查询，毫秒级响应，无网络依赖

### 搜索单词内容 (searchWord)
> **业务价值**: 实现LPLC原则，按需生成单词内容
- **输入参数**: word: String, userLanguage: LanguageCode
- **返回类型**: WordContent
- **业务逻辑**:
  1. 检查本地缓存是否存在
  2. 如不存在，调用API生成内容
  3. 缓存生成的内容
  4. 返回完整的单词解析
- **调用时机**: 用户选择搜索结果时

#### 缓存策略
- **缓存类型**: permanent
- **缓存键**: "word_\(word)_\(userLanguage.rawValue)"
- **过期时间**: 永不过期
- **缓存更新**: 仅在内容版本升级时更新

#### 错误处理
- **重试策略**: 指数退避，最多3次
- **降级方案**: 三级渐进式降级策略
  - **优先级1**: 返回本地缓存的完整AI生成内容
  - **优先级2**: 返回本地索引基础信息（音标+核心释义）
  - **优先级3**: 提示"需要网络连接获取单词信息"
- **错误分类**: 网络错误、API限流、内容生成失败

### 更新本地索引 (updateLocalIndex)
> **业务价值**: 维护高效的本地搜索能力
- **输入参数**: newWords: [String]
- **返回类型**: Void
- **业务逻辑**:
  1. 解析新单词列表
  2. 构建搜索索引项
  3. 更新本地SQLite数据库
  4. 清除相关搜索缓存
- **调用时机**: 应用启动时、内容更新时

#### 缓存策略
- **缓存类型**: none
- **缓存键**: 不适用
- **过期时间**: 不适用
- **缓存更新**: 直接更新数据库

#### 错误处理
- **重试策略**: 失败时标记待重试
- **降级方案**: 使用旧版本索引
- **错误分类**: 磁盘空间不足、数据库锁定

## 数据流转
### ~~搜索建议流~~ [架构调整]
> **架构说明**: 搜索建议流已移至LocalIndexService管理
- **新流程**: SearchService → LocalIndexService → SQLite查询 → UI模型
- **职责分离**: SearchService专注业务协调，LocalIndexService专注索引查询

### 内容获取流
- **起点**: 选择的单词
- **终点**: 内容展示
- **转换逻辑**: 单词 → 缓存检查 → API调用 → 内容解析
- **验证规则**: 单词格式验证、语言代码验证

## 技术实现注意事项
- 专注业务逻辑协调，搜索建议实现委托给LocalIndexService
- 缓存策略管理，避免重复的业务逻辑处理
- 多服务协调，确保SearchAPIAdapter和WordAPIAdapter的正确调用
- 错误处理和降级策略，保证用户体验的连续性
```

## LocalIndexService 本地索引服务配置

### 人类可读配置
```markdown
# LocalIndexService 本地索引服务配置
> **核心职责**: 管理本地搜索索引，提供离线搜索建议，支持三级降级策略

## 基本信息
- **服务ID**: LocalIndexService
- **业务职责**: 本地索引管理、搜索建议、离线数据查询
- **数据存储**: SQLite本地数据库
- **性能目标**: 毫秒级响应，支持离线使用

## 业务方法
### 搜索建议 (searchSuggestions)
> **业务价值**: 提供实时搜索建议，支持离线使用
- **输入参数**: query: String, limit: Int = 10
- **返回类型**: [SearchSuggestion]
- **业务逻辑**:
  1. 在本地SQLite中执行全文搜索
  2. 按相关度排序结果
  3. 限制返回数量
  4. 返回搜索建议列表

### 检查缓存 (checkCache)
> **业务价值**: 支持三级降级策略的缓存检查
- **输入参数**: word: String, language: LanguageCode
- **返回类型**: CacheStatus (full/index/none)
- **业务逻辑**:
  1. 检查完整内容缓存
  2. 检查索引基础信息
  3. 返回缓存状态

### 获取索引数据 (getIndexData)
> **业务价值**: 降级策略中提供基础信息
- **输入参数**: word: String, language: LanguageCode
- **返回类型**: WordIndexItem?
- **业务逻辑**:
  1. 查询本地索引数据库
  2. 返回音标和核心释义
  3. 支持离线使用

### 更新本地索引 (updateLocalIndex)
> **业务价值**: 维护高效的本地搜索能力
- **输入参数**: indexItems: [WordIndexItem]
- **返回类型**: Void
- **业务逻辑**:
  1. 批量插入或更新索引数据
  2. 更新本地SQLite数据库
  3. 清除相关搜索缓存
  4. 优化数据库索引

## 数据模型

### SearchSuggestion
```swift
struct SearchSuggestion {
    let word: String
    let definition: String        // 核心释义 - 用户最需要的信息
    let relevanceScore: Double    // 相关性分数 - 用于搜索排序
    let hasFullContent: Bool      // 是否有完整内容缓存
}
```

### CacheStatus
```swift
enum CacheStatus {
    case full(WordDefinitionResponse)
    case index(WordIndexItem)
    case none
}
```

## 技术实现注意事项
- 使用SQLite FTS (Full-Text Search) 提高搜索性能
- 实现增量索引更新，避免全量重建
- 支持多语言搜索和排序
- 提供数据库迁移和版本管理
```

### 机器可执行配置
```json
{
  "serviceId": "LocalIndexService",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "LocalIndexService.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "searchSuggestions",
      "parameters": [
        {
          "name": "query",
          "type": "String",
          "required": true
        },
        {
          "name": "limit",
          "type": "Int",
          "required": false,
          "defaultValue": 10
        }
      ],
      "returnType": "[SearchSuggestion]",
      "capability": "提供实时搜索建议，支持离线使用"
    },
    {
      "name": "checkCache",
      "parameters": [
        {
          "name": "word",
          "type": "String",
          "required": true
        },
        {
          "name": "language",
          "type": "LanguageCode",
          "required": true
        }
      ],
      "returnType": "CacheStatus",
      "capability": "支持三级降级策略的缓存检查"
    },
    {
      "name": "getIndexData",
      "parameters": [
        {
          "name": "word",
          "type": "String",
          "required": true
        },
        {
          "name": "language",
          "type": "LanguageCode",
          "required": true
        }
      ],
      "returnType": "WordIndexItem?",
      "capability": "降级策略中提供基础信息"
    },
    {
      "name": "updateLocalIndex",
      "parameters": [
        {
          "name": "indexItems",
          "type": "[WordIndexItem]",
          "required": true
        }
      ],
      "returnType": "Void",
      "capability": "维护高效的本地搜索能力"
    }
  ],
  "dataModels": [
    {
      "name": "SearchSuggestion",
      "fields": [
        {"name": "word", "type": "String"},
        {"name": "definition", "type": "String"},
        {"name": "relevanceScore", "type": "Double"},
        {"name": "hasFullContent", "type": "Bool"}
      ]
    },
    {
      "name": "CacheStatus",
      "type": "enum",
      "cases": [
        {"name": "full", "associatedValue": "WordDefinitionResponse"},
        {"name": "index", "associatedValue": "WordIndexItem"},
        {"name": "none"}
      ]
    }
  ],
  "dependencies": ["SQLiteManager", "FileManager"],
  "lifecycle": "singleton",
  "initializationStrategy": "lazy"
}
```

### SearchService 机器可执行配置
```json
{
  "serviceId": "SearchService",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchService.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "getSuggestions",
      "parameters": [
        {
          "name": "query",
          "type": "String",
          "required": true
        },
        {
          "name": "limit",
          "type": "Int",
          "required": false,
          "defaultValue": 10
        }
      ],
      "returnType": "[SearchSuggestion]",
      "caching": {
        "strategy": "lru",
        "key": "suggestions_${query.lowercased()}",
        "ttl": 3600,
        "conditions": ["query.count >= 2"]
      },
      "errorHandling": {
        "retry": {
          "maxAttempts": 0,
          "backoff": "none"
        },
        "fallback": "getHistorySearches",
        "errorMapping": {
          "indexCorrupted": "rebuildIndex"
        }
      },
      "analytics": [
        "search_suggestion_requested",
        "search_suggestion_returned"
      ]
    },
    {
      "name": "searchWord",
      "parameters": [
        {
          "name": "word",
          "type": "String",
          "required": true
        },
        {
          "name": "userLanguage",
          "type": "LanguageCode",
          "required": true
        }
      ],
      "returnType": "WordContent",
      "caching": {
        "strategy": "permanent",
        "key": "word_${word}_${userLanguage.rawValue}",
        "ttl": -1,
        "conditions": []
      },
      "errorHandling": {
        "retry": {
          "maxAttempts": 3,
          "backoff": "exponential"
        },
        "fallback": "getBasicDefinition",
        "errorMapping": {
          "networkError": "retry",
          "apiLimitExceeded": "fallback",
          "contentGenerationFailed": "fallback"
        }
      },
      "analytics": [
        "word_search_initiated",
        "word_content_loaded",
        "lplc_principle_applied"
      ]
    },
    {
      "name": "updateLocalIndex",
      "parameters": [
        {
          "name": "newWords",
          "type": "[String]",
          "required": true
        }
      ],
      "returnType": "Void",
      "caching": {
        "strategy": "none",
        "key": "",
        "ttl": 0,
        "conditions": []
      },
      "errorHandling": {
        "retry": {
          "maxAttempts": 1,
          "backoff": "linear"
        },
        "fallback": "markForRetry",
        "errorMapping": {
          "diskSpaceInsufficient": "cleanup",
          "databaseLocked": "retry"
        }
      },
      "analytics": [
        "index_update_started",
        "index_update_completed"
      ]
    }
  ],
  "dataFlow": [
    {
      "name": "suggestionFlow",
      "from": "UserInput",
      "to": "UIModel",
      "converter": "SuggestionConverter",
      "validation": [
        "inputLengthCheck",
        "specialCharacterFilter"
      ]
    },
    {
      "name": "contentFlow",
      "from": "SelectedWord",
      "to": "ContentDisplay",
      "converter": "ContentConverter",
      "validation": [
        "wordFormatValidation",
        "languageCodeValidation"
      ]
    }
  ]
}
```

## 4. 业务模型配置 (Business Model Layer)

### 人类可读配置
```markdown
# SearchModels 业务模型配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **模型ID**: SearchModels
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **业务含义**: 定义搜索功能相关的数据结构和转换逻辑

## 数据结构
### SearchSuggestion (搜索建议)
#### 核心字段
1. **word**
   - **类型**: String
   - **业务含义**: 建议的单词文本
   - **验证规则**: 非空，长度1-50字符
   - **默认值**: 无

2. **definition**
   - **类型**: String
   - **业务含义**: 单词的核心释义
   - **验证规则**: 非空，长度1-200字符
   - **默认值**: 无

3. **relevanceScore**
   - **类型**: Double
   - **业务含义**: 与搜索查询的相关度分数
   - **验证规则**: 0.0-1.0之间
   - **默认值**: 0.0

4. **hasFullContent**
   - **类型**: Bool
   - **业务含义**: 是否有完整内容缓存
   - **验证规则**: 布尔值
   - **默认值**: false

#### 计算字段
1. **displayText**
   - **类型**: String
   - **计算逻辑**: "\(word) - \(definition)"
   - **依赖字段**: word, definition
   - **业务规则**: 用于UI展示的格式化文本

### WordContent (单词内容)
#### 核心字段
1. **word**
   - **类型**: String
   - **业务含义**: 目标单词
   - **验证规则**: 非空，字母组成
   - **默认值**: 无

2. **definitions**
   - **类型**: [Definition]
   - **业务含义**: 多维度释义列表
   - **验证规则**: 至少包含一个释义
   - **默认值**: []

3. **userLanguage**
   - **类型**: LanguageCode
   - **业务含义**: 用户的母语代码
   - **验证规则**: 支持的语言代码
   - **默认值**: .english

### SearchIndex (搜索索引)
#### 核心字段
1. **word**
   - **类型**: String
   - **业务含义**: 索引的单词
   - **验证规则**: 唯一性约束
   - **默认值**: 无

2. **lastUpdated**
   - **类型**: Date
   - **业务含义**: 索引最后更新时间
   - **验证规则**: 有效日期格式
   - **默认值**: 当前时间

## 数据转换

### 到View DTO转换
> **转换目的**: 将业务数据转换为UI展示格式
- **目标类型**: SearchSuggestionViewModel
- **格式化规则**:
  1. 截断过长的释义文本
  2. 本地化处理
- **本地化处理**: 根据用户语言调整显示格式
- **性能优化**: 懒加载详细信息，预计算显示文本

## 技术实现注意事项
- 使用Codable协议支持JSON序列化
- 实现Equatable和Hashable用于集合操作
- 大型内容对象使用引用类型避免复制开销
- 搜索索引使用值类型确保线程安全
```

### 机器可执行配置
```json
{
  "modelId": "SearchModels",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchModels.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "structure": {
    "models": [
      {
        "name": "SearchSuggestion",
        "coreFields": [
          {
            "name": "word",
            "type": "String",
            "required": true,
            "validation": {
              "rules": ["nonEmpty", "maxLength:50"],
              "errorMessage": "单词不能为空且长度不超过50字符"
            }
          },
          {
            "name": "definition",
            "type": "String",
            "required": true,
            "validation": {
              "rules": ["nonEmpty", "maxLength:200"],
              "errorMessage": "释义不能为空且长度不超过200字符"
            }
          },
          {
            "name": "relevanceScore",
            "type": "Double",
            "required": true,
            "validation": {
              "rules": ["range:0.0-1.0"],
              "errorMessage": "相关度分数必须在0.0-1.0之间"
            },
            "defaultValue": "0.0"
          },
          {
            "name": "hasFullContent",
            "type": "Bool",
            "required": true,
            "validation": {
              "rules": [],
              "errorMessage": ""
            },
            "defaultValue": "false"
          }
        ],
        "computedFields": [
          {
            "name": "displayText",
            "type": "String",
            "logic": "\"\\(word) - \\(definition)\"",
            "dependencies": ["word", "definition"]
          }
        ]
      },
      {
        "name": "WordContent",
        "coreFields": [
          {
            "name": "word",
            "type": "String",
            "required": true,
            "validation": {
              "rules": ["nonEmpty", "alphabetic"],
              "errorMessage": "单词必须为非空字母组合"
            }
          },

          {
            "name": "definitions",
            "type": "[Definition]",
            "required": true,
            "validation": {
              "rules": ["minCount:1"],
              "errorMessage": "至少需要一个释义"
            },
            "defaultValue": "[]"
          },

          {
            "name": "userLanguage",
            "type": "LanguageCode",
            "required": true,
            "validation": {
              "rules": ["supportedLanguage"],
              "errorMessage": "必须是支持的语言代码"
            },
            "defaultValue": ".english"
          }
        ],
        "computedFields": []
      }
    ]
  },
  "conversions": [
    {
      "from": "SearchSuggestion",
      "to": "SearchSuggestionViewModel",
      "converter": "SearchViewConverter",
      "rules": [
        {
          "sourceField": "word",
          "targetField": "displayWord",
          "transformation": "directMapping"
        },
        {
          "sourceField": "definition",
          "targetField": "displayDefinition",
          "transformation": "truncateAndLocalize"
        }
      ]
    }
  ]
}
```

---

## 5. Adapter层配置 (Adapter Layer)

### 人类可读配置
```markdown
# SearchAPIAdapter 适配器配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **适配器ID**: SearchAPIAdapter
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **职责**: 纯转译层，负责搜索相关API调用的HTTP请求构建和JSON解析

## TADA架构集成
### Adapter层职责定位
> **设计原则**: 调用Adapter为业务层提供能力，而不要直接构建网络请求
- **能力调用**: 调用已实现的Adapter能力，无需关心内部HTTP实现
- **强制复用**: Adapter能力已实现完成，默认只能复用和组合现有能力
- **无业务逻辑**: 不包含缓存管理、错误重试、数据验证等业务逻辑
- **1:1映射**: Adapter的能力和数据结构必须1:1和adapter model层保持一致

### 与现有Adapter的集成
> **强制复用策略**: 最大化复用已实现的Adapter能力
- **WordAPIAdapter.getWord**: 强制复用单词内容获取能力，支持AI生成
- **SearchAPIAdapter.getWordIndexUpdates**: 调用搜索索引同步能力
- **能力组合**: 通过组合现有Adapter能力满足业务需求
- **统一错误处理**: 遵循现有Adapter层的错误传播机制
- **统一依赖注入**: 通过AdapterContainer统一管理

## Adapter能力调用详解
### ~~获取搜索建议 (getSuggestions)~~ [架构调整]
> **架构说明**: 搜索建议功能基于本地索引实时检索，不需要Adapter能力调用
- **实现方式**: 通过LocalIndexService在本地SQLite数据库中进行全文搜索
- **性能优势**: 毫秒级响应，无网络延迟，支持离线使用
- **数据来源**: 通过getWordIndexUpdates能力同步的本地索引数据

### 复用单词内容获取 (调用WordAPIAdapter.getWord能力)
> **能力调用**: 复用现有的单词内容获取能力，支持AI生成和缓存
- **方法签名**: `func getWord(_ word: String, language: LanguageCode?) async throws -> WordDefinitionResponse`
- **输入参数**:
  - `word`: 要查询的单词
  - `language`: 用户语言代码（可选）
- **返回值**: `WordDefinitionResponse` - 完整的单词定义响应
- **使用示例**:
```swift
let wordAdapter = AdapterContainer.shared.wordAPIAdapter
let wordContent = try await wordAdapter.getWord("serendipity", language: .chinese)
```
- **复用说明**: 直接使用现有WordAPIAdapter能力，无需重复实现

### 获取单词索引更新 (调用SearchAPIAdapter.getWordIndexUpdates能力)
> **能力调用**: 获取本地搜索索引的增量更新数据
- **方法签名**: `func getWordIndexUpdates(language: LanguageCode, since: Int?) async throws -> WordIndexResponse`
- **输入参数**:
  - `language`: 目标语言
  - `since`: 上次同步ID（传nil获取全部数据）
- **返回值**: `WordIndexResponse` - 索引更新数据
- **使用示例**:
```swift
let indexUpdates = try await adapter.getWordIndexUpdates(language: .chinese, since: lastSyncId)
```

## 基础设施层服务配置

### SQLiteManager 数据库管理器配置

#### 人类可读配置
```markdown
# SQLiteManager 数据库管理器配置
> **核心职责**: 管理本地SQLite数据库，提供数据持久化能力

## 基本信息
- **服务ID**: SQLiteManager
- **业务职责**: 数据库连接管理、SQL执行、事务处理
- **数据存储**: 本地SQLite文件
- **性能目标**: 高效的数据库操作，支持并发访问

## 业务方法
### 执行查询 (executeQuery)
- **输入参数**: sql: String, parameters: [Any]
- **返回类型**: [[String: Any]]
- **业务逻辑**: 执行SELECT查询并返回结果

### 执行更新 (executeUpdate)
- **输入参数**: sql: String, parameters: [Any]
- **返回类型**: Bool
- **业务逻辑**: 执行INSERT/UPDATE/DELETE操作

### 批量操作 (executeBatch)
- **输入参数**: operations: [DatabaseOperation]
- **返回类型**: Bool
- **业务逻辑**: 在事务中执行批量操作
```

#### 机器可执行配置
```json
{
  "serviceId": "SQLiteManager",
  "metadata": {
    "humanConfigSource": "SQLiteManager.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "executeQuery",
      "parameters": [
        {"name": "sql", "type": "String", "required": true},
        {"name": "parameters", "type": "[Any]", "required": false}
      ],
      "returnType": "[[String: Any]]",
      "capability": "执行SELECT查询"
    },
    {
      "name": "executeUpdate",
      "parameters": [
        {"name": "sql", "type": "String", "required": true},
        {"name": "parameters", "type": "[Any]", "required": false}
      ],
      "returnType": "Bool",
      "capability": "执行数据修改操作"
    }
  ],
  "lifecycle": "singleton",
  "initializationStrategy": "eager"
}
```

### CacheService 缓存服务配置

#### 人类可读配置
```markdown
# CacheService 缓存服务配置
> **核心职责**: 管理应用缓存，支持内存和磁盘缓存

## 基本信息
- **服务ID**: CacheService
- **业务职责**: 缓存管理、过期策略、缓存清理
- **存储类型**: 内存缓存 + 磁盘缓存
- **性能目标**: 快速缓存访问，智能过期管理

## 业务方法
### 获取缓存 (get)
- **输入参数**: key: String
- **返回类型**: T?
- **业务逻辑**: 从缓存中获取数据

### 设置缓存 (set)
- **输入参数**: key: String, value: T, expiry: TimeInterval?
- **返回类型**: Void
- **业务逻辑**: 将数据存储到缓存

### 清除缓存 (clear)
- **输入参数**: key: String?
- **返回类型**: Void
- **业务逻辑**: 清除指定或全部缓存
```

#### 机器可执行配置
```json
{
  "serviceId": "CacheService",
  "protocol": "CacheServiceProtocol",
  "metadata": {
    "humanConfigSource": "CacheService.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "get",
      "parameters": [
        {"name": "key", "type": "String", "required": true}
      ],
      "returnType": "T?",
      "capability": "从缓存获取数据"
    },
    {
      "name": "set",
      "parameters": [
        {"name": "key", "type": "String", "required": true},
        {"name": "value", "type": "T", "required": true},
        {"name": "expiry", "type": "TimeInterval?", "required": false}
      ],
      "returnType": "Void",
      "capability": "设置缓存数据"
    }
  ],
  "lifecycle": "singleton",
  "initializationStrategy": "lazy"
}
```


### ContentService 内容服务配置

#### 人类可读配置
```markdown
# ContentService 内容服务配置
> **核心职责**: 内容管理，支持搜索结果的内容处理

## 基本信息
- **服务ID**: ContentService
- **业务职责**: 内容格式化、内容缓存、内容展示逻辑
- **集成**: 与WordAPIAdapter协同工作

## 业务方法
```

#### 机器可执行配置
```json
{
  "serviceId": "ContentService",
  "protocol": "ContentServiceProtocol",
  "metadata": {
    "humanConfigSource": "ContentService.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "methods": [
    {
      "name": "formatContent",
      "parameters": [
        {"name": "content", "type": "WordDefinitionResponse", "required": true}
      ],
      "returnType": "FormattedContent",
      "capability": "格式化内容为UI展示格式"
    }
  ],
  "lifecycle": "singleton",
  "initializationStrategy": "lazy"
}
```

## 📊 API数据模型 (DTO定义)
### ~~SearchSuggestionsResponse~~ [已移除]
> **架构调整**: 搜索建议基于本地索引，不需要API数据模型
- **本地实现**: 通过LocalIndexService直接查询SQLite数据库
- **数据结构**: 使用业务层的SearchSuggestion模型，而非API DTO

### 复用WordDefinitionResponse (来自WordAPIAdapter)
```swift
// 直接使用现有的WordDefinitionResponse
// 包含完整的单词内容：metadata, content, phoneticSymbols等
// 支持AI生成的深度解析内容
```

### WordIndexResponse
```swift
struct WordIndexResponse: Codable {
    let success: Bool
    let data: [WordIndexItem]
    let lastSyncId: Int
    let metadata: WordIndexMetadata
}

struct WordIndexItem: Codable {
    let syncId: Int
    let word: String
    let language: LanguageCode
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
}

struct PhoneticSymbol: Codable {
    let type: String
    let symbol: String
}

struct WordIndexMetadata: Codable {
    let totalWords: Int
    let lastUpdated: String
}
```

## 🔧 技术实现注意事项
- **UI图标规范**: 严格使用SF Symbols，禁止自定义图标或第三方图标库
- 强制复用现有的WordAPIAdapter.getWord能力获取单词内容，避免重复实现
- 搜索建议完全基于本地索引实现，无需调用Adapter能力，确保毫秒级响应
- 通过调用getWordIndexUpdates能力定期同步服务器索引到本地SQLite数据库
- 错误处理遵循Adapter层统一规范，直接传播到Business Service层
- 通过AdapterContainer统一管理依赖注入，保持架构一致性
- 严格遵循TADA架构，只调用Adapter能力，无直接HTTP请求构建
```

### 机器可执行配置
```json
{
  "adapterId": "SearchAPIAdapter",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchAPIAdapter.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "protocol": "SearchAPIAdapterProtocol",
  "implementation": "SearchAPIAdapter",
  "dependencies": ["APIClient"],
  "tadaArchitecture": {
    "layer": "Adapter",
    "responsibility": "Adapter capability invocation",
    "integrationWith": ["WordAPIAdapter"],
    "businessLogic": false,
    "forceReuse": true,
    "newCapabilities": false
  },
  "methods": [
    {
      "name": "getWordIndexUpdates",
      "parameters": [
        {
          "name": "language",
          "type": "LanguageCode",
          "required": true
        },
        {
          "name": "since",
          "type": "Int?",
          "required": false
        }
      ],
      "returnType": "WordIndexResponse",
      "capability": "获取本地搜索索引的增量更新数据"
    }
  ],
  "dataModels": [
    {
      "name": "WordIndexResponse",
      "fields": [
        {"name": "success", "type": "Bool"},
        {"name": "data", "type": "[WordIndexItem]"},
        {"name": "lastSyncId", "type": "Int"},
        {"name": "metadata", "type": "WordIndexMetadata"}
      ]
    },
    {
      "name": "WordIndexItem",
      "fields": [
        {"name": "syncId", "type": "Int"},
        {"name": "word", "type": "String"},
        {"name": "language", "type": "LanguageCode"},
        {"name": "phoneticSymbols", "type": "[PhoneticSymbol]"},
        {"name": "coreDefinition", "type": "String"}
      ]
    },
    {
      "name": "WordIndexMetadata",
      "fields": [
        {"name": "totalWords", "type": "Int"},
        {"name": "lastUpdated", "type": "String"}
      ]
    },
    {
      "name": "PhoneticSymbol",
      "fields": [
        {"name": "type", "type": "String"},
        {"name": "symbol", "type": "String"}
      ]
    }
  ]
}
```

---

## 6. 依赖注入配置 (Dependency Injection Configuration)

### 人类可读配置
```markdown
# SearchDIContainer 依赖注入配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **容器ID**: SearchDIContainer
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **职责**: 管理搜索功能相关组件的依赖关系和生命周期

## TADA架构集成
### 现有服务复用
> **设计原则**: 最大化复用现有AdapterContainer中的服务
- **WordAPIAdapter**: 复用现有单词内容获取能力
- **AdapterContainer**: 统一管理所有Adapter层服务
- **依赖传递**: SearchService通过构造函数接收多个Adapter实例

### 新增服务注册
> **扩展策略**: 在现有架构基础上添加搜索专用服务
- **SearchAPIAdapter**: 新增索引同步能力（不包含搜索建议API）
- **LocalIndexService**: 新增本地搜索索引管理和搜索建议实现
- **SearchService**: 新增搜索业务逻辑协调

## 服务注册
### Adapter层服务 (AI自动生成)
> **设计原则**: 纯转译层，无状态设计，单例模式

1. **SearchAPIAdapter**
   - **生命周期**: 单例
   - **依赖**: APIClient
   - **注册方式**: 懒加载初始化
   - **职责**: HTTP请求转译和JSON解析

### Business层服务 (人工设计)
> **设计原则**: 有状态业务逻辑，单例模式，复杂依赖管理

1. **SearchService**
   - **生命周期**: 单例
   - **依赖**: SearchAPIAdapter, WordAPIAdapter, CacheService, AnalyticsService
   - **注册方式**: 构造函数注入
   - **职责**: 搜索业务逻辑、缓存策略、错误处理
   - **TADA集成**: 通过WordAPIAdapter复用单词内容获取能力

2. **LocalIndexService**
   - **生命周期**: 单例
   - **依赖**: SQLiteManager, FileManager
   - **注册方式**: 延迟初始化
   - **职责**: 本地搜索索引管理

### ViewModel层服务 (按需创建)
> **设计原则**: 视图相关状态管理，工厂模式创建

1. **SearchViewModel**
   - **生命周期**: 视图生命周期
   - **依赖**: SearchService, ContentService
   - **注册方式**: 工厂方法创建
   - **职责**: 搜索UI状态管理

## 依赖关系图
### 服务依赖链
- SearchViewModel → SearchService → SearchAPIAdapter → APIClient
- SearchService → WordAPIAdapter → APIClient (复用现有能力)
- SearchService → LocalIndexService → SQLiteManager
- SearchService → CacheService → MemoryCache/DiskCache
- SearchService → AnalyticsService → EventTracker

### 循环依赖检测
- **检测机制**: 启动时依赖图验证
- **解决策略**: 使用协议注入，避免具体类型依赖
- **监控工具**: 依赖关系可视化工具

## 配置管理
### 环境配置
- **开发环境**: 使用Mock服务，本地数据库
- **测试环境**: 使用测试API端点，内存数据库
- **生产环境**: 使用生产API，持久化数据库

### 功能开关
- **本地搜索**: 可配置开启/关闭（基于LocalIndexService）
- **本地索引**: 可配置同步频率
- **缓存策略**: 可配置TTL和容量限制

## 技术实现注意事项
- 使用协议导向设计，便于单元测试
- 实现服务定位器模式，支持运行时服务替换
- 提供依赖注入容器的线程安全保证
- 支持服务的懒加载和预加载策略
```

### 机器可执行配置
```json
{
  "containerId": "SearchDIContainer",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchDIContainer.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "services": [
    {
      "protocol": "SearchAPIAdapterProtocol",
      "implementation": "SearchAPIAdapter",
      "lifecycle": "singleton",
      "dependencies": ["APIClient"],
      "initializationStrategy": "lazy"
    },
    {
      "protocol": "SearchServiceProtocol",
      "implementation": "SearchService",
      "lifecycle": "singleton",
      "dependencies": [
        "SearchAPIAdapterProtocol",
        "WordAPIAdapterProtocol",
        "CacheServiceProtocol",
        "AnalyticsServiceProtocol"
      ],
      "initializationStrategy": "eager",
      "tadaIntegration": {
        "reuseExistingAdapters": ["WordAPIAdapterProtocol"],
        "newAdapters": ["SearchAPIAdapterProtocol"]
      }
    },
    {
      "protocol": "LocalIndexServiceProtocol",
      "implementation": "LocalIndexService",
      "lifecycle": "singleton",
      "dependencies": ["SQLiteManager", "FileManager"],
      "initializationStrategy": "lazy"
    },
    {
      "protocol": "SQLiteManagerProtocol",
      "implementation": "SQLiteManager",
      "lifecycle": "singleton",
      "dependencies": ["FileManager"],
      "initializationStrategy": "eager"
    },
    {
      "protocol": "CacheServiceProtocol",
      "implementation": "CacheService",
      "lifecycle": "singleton",
      "dependencies": ["FileManager"],
      "initializationStrategy": "lazy"
    },
    {
      "protocol": "AnalyticsServiceProtocol",
      "implementation": "AnalyticsService",
      "lifecycle": "singleton",
      "dependencies": [],
      "initializationStrategy": "lazy"
    },
    {
      "protocol": "ContentServiceProtocol",
      "implementation": "ContentService",
      "lifecycle": "singleton",
      "dependencies": ["CacheServiceProtocol"],
      "initializationStrategy": "lazy"
    }
  ],
  "factories": [
    {
      "name": "makeSearchViewModel",
      "returnType": "SearchViewModel",
      "dependencies": [
        "SearchServiceProtocol",
        "ContentServiceProtocol"
      ],
      "lifecycle": "transient"
    }
  ],
  "configuration": {
    "environment": {
      "development": {
        "apiBaseURL": "https://dev-api.senseword.app",
        "databasePath": "dev_search.db",
        "enableMockServices": true
      },
      "production": {
        "apiBaseURL": "https://api.senseword.app",
        "databasePath": "search.db",
        "enableMockServices": false
      }
    },
    "featureFlags": {
      "enableSearchSuggestions": true,
      "localIndexSyncInterval": 3600,
      "cacheMaxSize": 100,
      "cacheTTL": 1800
    }
  }
}
```

---

## 7. 测试配置 (Testing Configuration)

### 人类可读配置
```markdown
# SearchTesting 测试配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **测试套件ID**: SearchTesting
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **测试目标**: 确保搜索功能的正确性、性能和用户体验

## TADA架构测试策略
### Adapter层测试 (纯转译测试)
> **测试原则**: 专注于HTTP请求构建和JSON解析，无业务逻辑测试
- **SearchAPIAdapter**: 测试索引同步能力（不包含搜索建议API）
- **WordAPIAdapter集成**: 验证现有单词获取能力的复用

### Business层测试 (业务逻辑测试)
> **测试原则**: 专注于缓存策略、错误处理、数据转换等业务逻辑
- **SearchService**: 测试多Adapter协调、缓存管理、LPLC原则实现

## 单元测试 (Unit Tests)
### SearchAPIAdapter测试
> **测试重点**: HTTP请求构建和JSON解析的正确性

1. **getWordIndexUpdates方法测试**
   - **测试场景**: 增量同步、全量同步、同步失败
   - **Mock策略**: Mock不同同步状态的API响应
   - **断言验证**: 请求参数构建、响应解析、错误处理
   - **测试数据**: 模拟的索引更新数据

3. **WordAPIAdapter集成测试**
   - **测试场景**: 验证现有getWord方法的复用
   - **Mock策略**: 使用现有WordAPIAdapter的Mock
   - **断言验证**: 方法调用正确性、数据格式一致性
   - **测试数据**: 复用现有单词内容测试数据

### SearchService测试
> **测试重点**: 业务逻辑、缓存策略、多Adapter协调

1. **业务逻辑协调测试**
   - **测试场景**: SearchService协调LocalIndexService和WordAPIAdapter
   - **Mock策略**: Mock LocalIndexService和WordAPIAdapter
   - **断言验证**: 服务调用顺序、缓存策略、错误处理
   - **测试数据**: 模拟的搜索请求和响应数据

2. **多服务协调测试**
   - **测试场景**: LocalIndexService查询 + WordAPIAdapter内容获取
   - **Mock策略**: Mock本地索引查询和WordAPIAdapter响应
   - **断言验证**: 服务调用顺序、数据传递、错误隔离
   - **测试数据**: 本地搜索结果和单词内容的组合数据

3. **LPLC原则验证测试**
   - **测试场景**: 首次搜索、重复搜索、离线模式
   - **Mock策略**: Mock WordAPIAdapter的AI生成和缓存响应
   - **断言验证**: 懒加载触发时机、内容生成流程、缓存命中率
   - **测试数据**: 不同网络条件下的响应，包含AI生成延迟模拟

### SearchViewModel测试
> **测试重点**: UI状态管理、用户交互响应

1. **搜索状态管理测试**
   - **测试场景**: 激活搜索、输入处理、结果选择
   - **Mock策略**: Mock SearchService
   - **断言验证**: Published属性变化、状态转换
   - **测试数据**: 模拟用户输入序列

2. **防抖机制测试**
   - **测试场景**: 快速输入、延迟触发、取消请求
   - **Mock策略**: 时间控制和异步操作Mock
   - **断言验证**: 防抖延迟、请求合并、内存泄漏
   - **测试数据**: 高频输入模拟

## 集成测试 (Integration Tests)
### 搜索流程端到端测试
> **测试重点**: 完整搜索流程的协调性

1. **本地搜索到内容加载流程**
   - **测试场景**: 用户输入 → 本地索引查询 → 选择结果 → 内容展示
   - **环境配置**: 本地测试数据库、Mock WordAPIAdapter
   - **断言验证**: 数据流转正确性、状态同步
   - **性能要求**: 本地查询 < 100ms，内容加载 < 500ms

2. **离线搜索能力测试**
   - **测试场景**: 网络断开、本地索引查询、缓存降级
   - **环境配置**: 模拟网络故障、预填充本地数据
   - **断言验证**: 离线功能可用性、数据一致性
   - **性能要求**: 本地查询 < 100ms

## UI测试 (UI Tests)
### 搜索交互测试
> **测试重点**: 用户界面交互的正确性和流畅性

1. **搜索手势测试**
   - **测试场景**: 下拉激活、输入交互、结果选择
   - **自动化脚本**: XCUITest自动化
   - **断言验证**: 动画效果、手势响应、状态反馈
   - **设备覆盖**: iPhone各尺寸、iPad

2. **搜索性能测试**
   - **测试场景**: 大量数据滚动、内存使用、电池消耗
   - **监控指标**: CPU使用率、内存峰值、网络请求数
   - **性能基准**: 内存 < 50MB、CPU < 30%
   - **测试工具**: Instruments性能分析

## Mock数据和测试工具
### Mock服务配置
```swift
// SearchAPIAdapterMock示例
class SearchAPIAdapterMock: SearchAPIAdapterProtocol {
    var mockSuggestions: [SearchSuggestionAPI] = []
    var shouldFailRequest: Bool = false

    func getSuggestions(query: String, limit: Int, language: LanguageCode?) async throws -> SearchSuggestionsResponse {
        if shouldFailRequest {
            throw APIError.networkError
        }
        return SearchSuggestionsResponse(
            suggestions: mockSuggestions,
            totalCount: mockSuggestions.count,
            queryTime: 0.1
        )
    }
}
```

### 测试数据管理
- **测试数据集**: 多语言搜索词汇、API响应样本
- **数据生成**: 自动化测试数据生成脚本
- **数据清理**: 测试后自动清理临时数据
- **数据版本**: 测试数据版本控制和更新机制

## 技术实现注意事项
- 使用依赖注入便于Mock替换
- 实现测试专用的配置环境
- 提供测试覆盖率报告和质量门禁
- 集成CI/CD自动化测试流程
```

### 机器可执行配置
```json
{
  "testSuiteId": "SearchTesting",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchTesting.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "unitTests": [
    {
      "target": "SearchAPIAdapter",
      "testClass": "SearchAPIAdapterTests",
      "methods": [
        {
          "name": "testGetWordIndexUpdates_Success",
          "scenario": "正常索引同步请求",
          "mockStrategy": "mockAPIClient",
          "assertions": ["requestParameters", "responseMapping"],
          "testData": "index_updates_success.json"
        },
        {
          "name": "testGetSuggestions_NetworkError",
          "scenario": "网络错误处理",
          "mockStrategy": "mockNetworkFailure",
          "assertions": ["errorPropagation"],
          "testData": "network_error.json"
        }
      ]
    },
    {
      "target": "SearchService",
      "testClass": "SearchServiceTests",
      "methods": [
        {
          "name": "testSearchWithCache_Hit",
          "scenario": "缓存命中场景",
          "mockStrategy": "mockCacheService",
          "assertions": ["cacheStrategy", "performanceMetrics"],
          "testData": "cache_hit_data.json"
        },
        {
          "name": "testLPLCPrinciple",
          "scenario": "LPLC原则验证",
          "mockStrategy": "mockAPIAdapter",
          "assertions": ["lazyLoading", "contentGeneration"],
          "testData": "lplc_test_data.json"
        }
      ]
    }
  ],
  "integrationTests": [
    {
      "name": "SearchFlowEndToEnd",
      "scenario": "完整搜索流程",
      "environment": "testAPI",
      "steps": [
        "activateSearch",
        "inputQuery",
        "selectSuggestion",
        "verifyContentLoad"
      ],
      "assertions": ["dataFlow", "stateSync"],
      "performanceRequirements": {
        "responseTime": 500,
        "memoryUsage": 50
      }
    }
  ],
  "uiTests": [
    {
      "name": "SearchGestureTests",
      "testClass": "SearchUITests",
      "scenarios": [
        "pullDownActivation",
        "textInputInteraction",
        "resultSelection"
      ],
      "devices": ["iPhone14", "iPhone14Pro", "iPadAir"],
      "assertions": ["animationSmooth", "gestureResponse"]
    }
  ],
  "mockConfiguration": {
    "services": [
      {
        "protocol": "SearchAPIAdapterProtocol",
        "mockImplementation": "SearchAPIAdapterMock",
        "testDataPath": "TestData/SearchAPI/"
      }
    ],
    "testData": {
      "basePath": "TestResources/SearchData/",
      "files": [
        "suggestions_success.json",
        "word_generation_response.json",
        "network_error.json"
      ]
    }
  }
}
```

---

## 8. 性能优化配置 (Performance Optimization Configuration)

### 人类可读配置
```markdown
# SearchPerformance 性能优化配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **优化配置ID**: SearchPerformance
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **优化目标**: 确保搜索功能的响应速度、内存效率和电池续航

## 响应速度优化
### 本地搜索优化
> **目标**: 用户输入后100ms内显示本地搜索结果

1. **本地索引预加载**
   - **策略**: 应用启动时异步加载常用词汇索引
   - **数据量**: 限制在10MB以内，包含5000个高频词汇
   - **更新机制**: 后台增量更新，避免阻塞主线程
   - **缓存策略**: LRU缓存，最多保存1000个搜索结果

2. **防抖优化**
   - **延迟时间**: 300ms防抖，平衡响应性和API调用频率
   - **取消机制**: 新输入时取消之前的请求
   - **批处理**: 合并相似查询，减少重复请求
   - **预测输入**: 基于用户历史，预加载可能的搜索结果

### 内容加载优化
> **目标**: 选择搜索结果后500ms内显示内容

1. **LPLC原则实现**
   - **懒加载**: 只在用户选择时才生成内容
   - **智能预测**: 基于当前单词的相关词汇（同义词、词根相关词）进行下一个单词的智能预测和预加载
   - **压缩传输**: 使用gzip压缩API响应

2. **缓存策略**
   - **永久缓存**: 生成的内容永久保存在本地
   - **音频文件缓存**: TTS音频文件独立缓存管理
     - 音频文件压缩存储（AAC格式）
     - 音频缓存容量限制（最大500MB）
     - 音频文件LRU清理策略
   - **智能预取**: 基于相关词汇预取可能需要的内容
   - **缓存清理**: 定期清理低频访问的缓存内容
   - **版本管理**: 支持内容版本更新和增量同步

## 内存效率优化
### 数据结构优化
> **目标**: 搜索功能内存占用不超过50MB

1. **模型设计**
   - **值类型**: 优先使用struct减少引用开销
   - **懒加载属性**: 大型属性使用lazy var延迟初始化
   - **弱引用**: 避免循环引用导致的内存泄漏
   - **内存池**: 复用搜索结果对象，减少频繁分配

2. **缓存管理**
   - **容量限制**: 内存缓存最大50MB，磁盘缓存最大200MB
   - **LRU策略**: 自动清理最少使用的缓存项
   - **内存警告**: 响应系统内存警告，主动释放缓存
   - **分级缓存**: 热数据内存缓存，冷数据磁盘缓存



## 电池续航优化
### 网络请求优化
> **目标**: 减少不必要的网络活动

1. **请求优化**
   - **请求去重**: 避免重复请求相同内容
   - **压缩传输**: 启用请求和响应压缩

2. **智能同步**
   - **WiFi优先**: 大量数据同步优先使用WiFi
   - **后台限制**: 限制后台网络活动
   - **增量同步**: 只同步变更的数据
   - **时机选择**: 在设备充电时进行大量同步

### CPU使用优化
> **目标**: 减少CPU密集型操作

1. **异步处理**
   - **后台队列**: 搜索索引构建在后台队列执行
   - **主线程保护**: UI更新严格在主线程执行
   - **任务分片**: 大型任务分片处理，避免阻塞
   - **优先级管理**: 用户交互任务优先级最高

2. **算法优化**
   - **搜索算法**: 使用高效的字符串匹配算法
   - **排序优化**: 预排序减少实时排序开销
   - **缓存计算**: 缓存复杂计算结果
   - **数据结构**: 选择适合的数据结构提升查询效率

## 监控和分析
### 性能指标监控
> **目标**: 实时监控性能表现

1. **关键指标**
   - **响应时间**: 本地搜索查询、内容加载的响应时间
   - **内存使用**: 峰值内存、平均内存使用量
   - **网络流量**: 请求次数、数据传输量
   - **电池消耗**: CPU使用率、网络活动频率

2. **性能分析**
   - **热点分析**: 识别性能瓶颈和热点代码
   - **趋势分析**: 跟踪性能指标的变化趋势
   - **用户体验**: 收集用户对响应速度的反馈
   - **A/B测试**: 测试不同优化策略的效果

## 技术实现注意事项
- 使用Instruments进行性能分析和优化
- 实现性能监控和告警机制
- 定期进行性能回归测试
- 建立性能优化的持续改进流程
```

### 机器可执行配置
```json
{
  "performanceConfigId": "SearchPerformance",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchPerformance.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "responseTimeOptimization": {
    "searchSuggestions": {
      "targetResponseTime": 300,
      "debounceDelay": 300,
      "localIndexSize": 10485760,
      "cacheSize": 1000,
      "preloadStrategy": "highFrequencyWords"
    },
    "contentLoading": {
      "targetResponseTime": 500,
      "lazyLoadingEnabled": true,
      "compressionEnabled": true,
      "layeredLoading": true
    }
  },
  "memoryOptimization": {
    "maxMemoryUsage": 52428800,
    "cacheConfiguration": {
      "memoryCache": {
        "maxSize": 52428800,
        "strategy": "lru"
      },
      "diskCache": {
        "maxSize": 209715200,
        "strategy": "lru"
      }
    },
    "dataStructures": {
      "preferValueTypes": true,
      "useLazyProperties": true,
      "enableObjectPooling": true
    }
  },
  "batteryOptimization": {
    "networkRequests": {
      "batchingEnabled": true,
      "deduplicationEnabled": true,
      "compressionEnabled": true,
      "wifiPreferred": true
    },
    "cpuUsage": {
      "backgroundProcessing": true,
      "taskSlicing": true,
      "priorityManagement": true
    }
  },
  "monitoring": {
    "metrics": [
      {
        "name": "searchResponseTime",
        "type": "histogram",
        "unit": "milliseconds",
        "threshold": 300
      },
      {
        "name": "memoryUsage",
        "type": "gauge",
        "unit": "bytes",
        "threshold": 52428800
      },
      {
        "name": "networkRequests",
        "type": "counter",
        "unit": "count",
        "threshold": 100
      }
    ],
    "alerting": {
      "enabled": true,
      "thresholds": {
        "responseTime": 500,
        "memoryUsage": 67108864,
        "errorRate": 0.05
      }
    }
  }
}
```

---

## 9. 错误处理配置 (Error Handling Configuration)

### 人类可读配置
```markdown
# SearchErrorHandling 错误处理配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **错误处理配置ID**: SearchErrorHandling
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **处理目标**: 提供优雅的错误处理，确保用户体验不被技术问题打断

## 错误分类和处理策略
### 网络相关错误
> **设计原则**: 网络问题不应阻断用户的学习体验

1. **连接超时错误**
   - **错误类型**: NetworkError.timeout
   - **处理策略**: 自动重试3次，指数退避
   - **用户体验**: 显示"网络连接较慢，正在重试..."
   - **降级方案**: 使用本地缓存数据

2. **网络不可用错误**
   - **错误类型**: NetworkError.noConnection
   - **处理策略**: 立即切换到离线模式
   - **用户体验**: 显示"当前离线，显示本地内容"
   - **降级方案**: 完全使用本地搜索索引

3. **服务器错误**
   - **错误类型**: NetworkError.serverError(5xx)
   - **处理策略**: 重试1次，失败后降级
   - **用户体验**: 显示"服务暂时不可用，已为您保存搜索请求"
   - **降级方案**: 保存搜索请求，网络恢复后自动执行

### API相关错误
> **设计原则**: API错误应该透明处理，不暴露技术细节

1. **内容生成失败**
   - **错误类型**: APIError.contentGenerationFailed
   - **处理策略**: 重试1次，失败后提供基础释义
   - **用户体验**: 显示"正在生成深度解析，请稍候..."
   - **降级方案**: 显示基础词典释义

2. **API限流错误**
   - **错误类型**: APIError.rateLimitExceeded
   - **处理策略**: 延迟重试，使用缓存数据
   - **用户体验**: 无感知，优先使用缓存
   - **降级方案**: 完全依赖本地缓存

3. **认证错误**
   - **错误类型**: APIError.unauthorized
   - **处理策略**: 自动刷新token，重试请求
   - **用户体验**: 透明处理，无用户感知
   - **降级方案**: 引导用户重新登录

### 数据相关错误
> **设计原则**: 数据问题应该自动修复，保证功能可用

1. **本地索引损坏**
   - **错误类型**: DataError.indexCorrupted
   - **处理策略**: 自动重建索引
   - **用户体验**: 显示"正在优化搜索功能..."
   - **降级方案**: 使用网络搜索替代

2. **缓存数据过期**
   - **错误类型**: DataError.cacheExpired
   - **处理策略**: 后台刷新，继续使用过期数据
   - **用户体验**: 无感知，数据无缝更新
   - **降级方案**: 显示过期数据并标注

3. **存储空间不足**
   - **错误类型**: DataError.insufficientStorage
   - **处理策略**: 自动清理旧缓存，释放空间
   - **用户体验**: 显示"正在清理缓存..."
   - **降级方案**: 禁用缓存，直接使用网络

### 用户输入错误
> **设计原则**: 引导用户正确使用，而不是简单拒绝

1. **无效搜索查询**
   - **错误类型**: InputError.invalidQuery
   - **处理策略**: 智能纠错和建议
   - **用户体验**: 显示"您是否要搜索：[建议词汇]"
   - **降级方案**: 显示搜索历史

2. **查询过于频繁**
   - **错误类型**: InputError.tooFrequent
   - **处理策略**: 防抖处理，合并请求
   - **用户体验**: 无感知，自动优化
   - **降级方案**: 延迟处理请求

## 错误恢复机制
### 自动恢复策略
> **目标**: 最大化系统的自愈能力

1. **网络恢复检测**
   - **检测机制**: 网络状态监听器
   - **恢复动作**: 自动重试失败的请求
   - **用户通知**: 显示"网络已恢复，正在同步数据"
   - **数据同步**: 增量同步本地和远程数据

2. **服务降级恢复**
   - **检测机制**: 健康检查API
   - **恢复动作**: 逐步恢复完整功能
   - **用户通知**: 显示"服务已恢复，功能已完全可用"
   - **功能验证**: 验证所有功能正常工作

### 用户引导和反馈
> **目标**: 让用户理解当前状态并知道如何操作

1. **错误状态展示**
   - **视觉设计**: 友好的错误图标和说明
   - **文案策略**: 积极正面的错误提示
   - **操作指引**: 明确的下一步操作建议
   - **进度反馈**: 显示恢复进度和预计时间

2. **用户操作选项**
   - **重试操作**: 一键重试失败的操作
   - **离线模式**: 手动切换到离线模式
   - **反馈渠道**: 提供问题反馈入口
   - **帮助文档**: 链接到相关帮助文档

## 错误监控和分析
### 错误收集和上报
> **目标**: 全面了解错误发生情况，持续改进

1. **错误数据收集**
   - **收集范围**: 所有层级的错误信息
   - **数据脱敏**: 移除用户敏感信息
   - **上报时机**: 实时上报关键错误，批量上报一般错误
   - **本地存储**: 网络不可用时本地存储，恢复后上报

2. **错误分析和告警**
   - **错误分类**: 按类型、频率、影响范围分类
   - **趋势分析**: 跟踪错误率变化趋势
   - **告警机制**: 错误率超过阈值时自动告警
   - **影响评估**: 评估错误对用户体验的影响

## 技术实现注意事项
- 使用Result类型进行错误传播
- 实现错误恢复的状态机
- 提供错误处理的单元测试覆盖
- 建立错误处理的最佳实践文档
```

### 机器可执行配置
```json
{
  "errorHandlingConfigId": "SearchErrorHandling",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchErrorHandling.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "errorCategories": [
    {
      "category": "NetworkError",
      "errors": [
        {
          "type": "timeout",
          "retryStrategy": {
            "maxAttempts": 3,
            "backoff": "exponential",
            "baseDelay": 1000
          },
          "userMessage": "网络连接较慢，正在重试...",
          "fallbackAction": "useCachedData"
        },
        {
          "type": "noConnection",
          "retryStrategy": {
            "maxAttempts": 0,
            "backoff": "none"
          },
          "userMessage": "当前离线，显示本地内容",
          "fallbackAction": "switchToOfflineMode"
        }
      ]
    },
    {
      "category": "APIError",
      "errors": [
        {
          "type": "contentGenerationFailed",
          "retryStrategy": {
            "maxAttempts": 1,
            "backoff": "linear",
            "baseDelay": 2000
          },
          "userMessage": "正在生成深度解析，请稍候...",
          "fallbackAction": "showBasicDefinition"
        },
        {
          "type": "rateLimitExceeded",
          "retryStrategy": {
            "maxAttempts": 1,
            "backoff": "exponential",
            "baseDelay": 5000
          },
          "userMessage": null,
          "fallbackAction": "useCachedData"
        }
      ]
    }
  ],
  "recoveryMechanisms": [
    {
      "name": "networkRecovery",
      "trigger": "networkStatusChange",
      "actions": [
        "retryFailedRequests",
        "syncPendingData",
        "notifyUser"
      ],
      "userNotification": "网络已恢复，正在同步数据"
    },
    {
      "name": "serviceRecovery",
      "trigger": "healthCheckSuccess",
      "actions": [
        "restoreFullFunctionality",
        "validateFeatures",
        "notifyUser"
      ],
      "userNotification": "服务已恢复，功能已完全可用"
    }
  ],
  "monitoring": {
    "errorCollection": {
      "enabled": true,
      "realTimeErrors": ["NetworkError.noConnection", "APIError.unauthorized"],
      "batchErrors": ["DataError.cacheExpired", "InputError.invalidQuery"],
      "dataPrivacy": {
        "removeUserData": true,
        "hashSensitiveFields": true
      }
    },
    "alerting": {
      "errorRateThreshold": 0.05,
      "criticalErrors": ["DataError.indexCorrupted", "APIError.unauthorized"],
      "notificationChannels": ["slack", "email"]
    }
  }
}
```

---

## 10. 安全配置 (Security Configuration)

### 人类可读配置
```markdown
# SearchSecurity 安全配置 (Story09: 搜索 - 从即时需求到无尽探索)

## 基本信息
- **安全配置ID**: SearchSecurity
- **故事引用**: Story09 - 搜索 - 从即时需求到无尽探索
- **安全目标**: 保护用户搜索数据、防止恶意攻击、确保数据传输安全

## 数据保护
### 用户搜索隐私
> **原则**: 用户搜索行为是私密的，需要严格保护

1. **搜索历史加密**
   - **加密方式**: AES-256本地加密存储
   - **密钥管理**: 使用Keychain存储加密密钥
   - **数据范围**: 搜索查询、选择结果、时间戳
   - **清理策略**: 用户可选择清除搜索历史

2. **网络传输安全**
   - **传输协议**: 强制使用HTTPS/TLS 1.3
   - **证书验证**: 启用证书固定(Certificate Pinning)
   - **数据脱敏**: 传输前移除设备标识信息
   - **请求签名**: 使用HMAC签名验证请求完整性

### 本地数据安全
> **原则**: 本地存储的数据应该受到保护，防止未授权访问

1. **数据库加密**
   - **加密方式**: SQLCipher加密SQLite数据库
   - **密钥轮换**: 定期更换数据库加密密钥
   - **访问控制**: 应用沙盒内访问，防止其他应用读取
   - **备份保护**: 排除敏感数据的iCloud备份

2. **缓存数据保护**
   - **敏感数据**: 用户个人搜索偏好不缓存到磁盘
   - **数据分级**: 区分公开内容和个人数据的存储策略
   - **自动清理**: 应用卸载时自动清理所有本地数据
   - **完整性校验**: 使用校验和验证缓存数据完整性

## 输入验证和防护
### 搜索输入安全
> **原则**: 防止恶意输入影响系统稳定性

1. **输入验证**
   - **长度限制**: 搜索查询最大100字符
   - **字符过滤**: 过滤特殊字符和控制字符
   - **编码验证**: 确保输入为有效UTF-8编码
   - **注入防护**: 防止SQL注入和脚本注入

2. **频率限制**
   - **请求限流**: 每分钟最多60次搜索请求
   - **异常检测**: 检测异常高频请求模式
   - **临时封禁**: 异常行为触发临时限制
   - **白名单机制**: 开发和测试环境白名单

### API安全防护
> **原则**: 保护后端API免受滥用和攻击

1. **身份认证**
   - **Token验证**: 使用JWT Token进行身份验证
   - **Token刷新**: 自动刷新过期Token
   - **设备绑定**: Token与设备ID绑定
   - **异地登录**: 检测异常登录位置

2. **请求安全**
   - **请求签名**: 使用API密钥签名请求
   - **时间戳验证**: 防止重放攻击
   - **请求去重**: 防止重复请求攻击
   - **IP白名单**: 生产环境IP访问控制

## 隐私合规
### 数据收集合规
> **原则**: 严格遵守GDPR、CCPA等隐私法规

1. **数据最小化**
   - **收集原则**: 只收集功能必需的数据
   - **匿名化**: 分析数据去除个人标识
   - **聚合统计**: 使用聚合数据而非个人数据
   - **保留期限**: 设定数据保留期限并自动删除

2. **用户控制**
   - **透明度**: 清晰说明数据收集和使用目的
   - **选择权**: 用户可选择是否参与数据收集
   - **访问权**: 用户可查看收集的个人数据
   - **删除权**: 用户可要求删除个人数据

### 第三方集成安全
> **原则**: 第三方服务集成不应泄露用户数据

1. **服务商评估**
   - **安全认证**: 选择有安全认证的服务商
   - **数据协议**: 签署数据处理协议
   - **定期审计**: 定期审计第三方服务安全性
   - **应急预案**: 制定第三方服务泄露应急预案

2. **数据传输控制**
   - **数据脱敏**: 传输给第三方前脱敏处理
   - **最小权限**: 只提供必要的数据访问权限
   - **传输加密**: 所有第三方数据传输加密
   - **访问日志**: 记录所有第三方数据访问

## 安全监控和响应
### 安全事件监控
> **原则**: 及时发现和响应安全威胁

1. **异常检测**
   - **行为分析**: 检测异常用户行为模式
   - **流量监控**: 监控异常网络流量
   - **错误分析**: 分析异常错误模式
   - **性能监控**: 监控异常性能指标

2. **威胁响应**
   - **自动响应**: 自动阻断明显的恶意行为
   - **告警机制**: 及时通知安全团队
   - **事件记录**: 详细记录安全事件
   - **影响评估**: 评估安全事件影响范围

### 安全更新机制
> **原则**: 快速响应安全漏洞和威胁

1. **漏洞管理**
   - **定期扫描**: 定期进行安全漏洞扫描
   - **依赖检查**: 检查第三方依赖的安全漏洞
   - **补丁管理**: 及时应用安全补丁
   - **版本控制**: 维护安全版本发布记录

2. **应急响应**
   - **响应团队**: 建立安全应急响应团队
   - **响应流程**: 制定标准化响应流程
   - **通信机制**: 建立内外部通信机制
   - **恢复计划**: 制定系统恢复计划

## 技术实现注意事项
- 使用iOS安全框架进行加密和认证
- 实现安全编码最佳实践
- 定期进行安全代码审查
- 建立安全测试和验证流程
```

### 机器可执行配置
```json
{
  "securityConfigId": "SearchSecurity",
  "storyReference": "Story09",
  "metadata": {
    "humanConfigSource": "SearchSecurity.human.md",
    "compiledAt": "2024-01-15T10:30:00Z",
    "version": "1.0.0"
  },
  "dataProtection": {
    "searchHistory": {
      "encryption": {
        "algorithm": "AES-256",
        "keyStorage": "keychain",
        "keyRotation": true,
        "rotationInterval": 2592000
      },
      "storage": {
        "location": "applicationSupport",
        "excludeFromBackup": true,
        "autoCleanup": true
      }
    },
    "networkSecurity": {
      "transport": {
        "protocol": "HTTPS",
        "tlsVersion": "1.3",
        "certificatePinning": true
      },
      "requestSigning": {
        "algorithm": "HMAC-SHA256",
        "timestampValidation": true,
        "nonceValidation": true
      }
    }
  },
  "inputValidation": {
    "searchQuery": {
      "maxLength": 100,
      "allowedCharacters": "alphanumeric_unicode",
      "encoding": "UTF-8",
      "sanitization": true
    },
    "rateLimiting": {
      "requestsPerMinute": 60,
      "burstLimit": 10,
      "anomalyDetection": true,
      "temporaryBan": {
        "enabled": true,
        "duration": 300
      }
    }
  },
  "apiSecurity": {
    "authentication": {
      "tokenType": "JWT",
      "tokenExpiry": 3600,
      "refreshEnabled": true,
      "deviceBinding": true
    },
    "requestSecurity": {
      "signatureRequired": true,
      "timestampValidation": true,
      "deduplication": true,
      "ipWhitelist": {
        "enabled": false,
        "production": true
      }
    }
  },
  "privacyCompliance": {
    "dataMinimization": {
      "collectOnlyNecessary": true,
      "anonymizeAnalytics": true,
      "aggregateStatistics": true,
      "retentionPeriod": 7776000
    },
    "userRights": {
      "transparency": true,
      "optOut": true,
      "dataAccess": true,
      "dataDeletion": true
    }
  },
  "monitoring": {
    "anomalyDetection": {
      "behaviorAnalysis": true,
      "trafficMonitoring": true,
      "errorAnalysis": true,
      "performanceMonitoring": true
    },
    "threatResponse": {
      "automaticBlocking": true,
      "alerting": true,
      "eventLogging": true,
      "impactAssessment": true
    }
  }
}
```

---

## 总结

本文档完整定义了KDD-021内容搜索功能的前端TADA架构配置，包含以下十个核心配置层级：

### 配置层级概览
1. **视图层配置** - 定义搜索界面的布局、动画和交互
2. **视图模型配置** - 管理搜索相关的UI状态和用户操作
3. **业务层配置** - 实现搜索业务逻辑、缓存策略和LPLC原则
4. **业务模型配置** - 定义搜索相关的数据结构和转换逻辑
5. **Adapter层配置** - 提供纯转译的API调用能力
6. **依赖注入配置** - 管理组件依赖关系和生命周期
7. **测试配置** - 确保搜索功能的质量和可靠性
8. **性能优化配置** - 保证搜索功能的响应速度和资源效率
9. **错误处理配置** - 提供优雅的错误处理和恢复机制
10. **安全配置** - 保护用户数据和防范安全威胁

### 核心设计原则
- **TADA架构分离**: 严格区分AI自动生成的转译层和人工设计的业务层
- **现有能力复用**: 通过WordAPIAdapter复用单词内容获取能力，避免重复实现
- **1:1映射原则**: Adapter的能力和数据结构必须与adapter model层保持一致
- **LPLC原则实现**: 按需生成内容，最大化资源利用效率
- **用户体验优先**: 从工具使用无缝转换到媒体探索的体验设计
- **配置驱动开发**: 双重配置结构支持人类理解和机器执行
- **安全隐私保护**: 全方位保护用户搜索数据和行为隐私

### 技术实现特点
- **响应式设计**: 100ms本地搜索响应，500ms内容加载
- **智能缓存**: LRU策略、分层缓存、自动清理
- **离线优先**: 本地索引查询，无网络依赖的搜索体验
- **性能监控**: 实时监控关键指标，自动优化
- **安全防护**: 端到端加密、输入验证、异常检测

这套完整的配置文档为SenseWord搜索功能的开发提供了详尽的技术规范和实现指导，确保功能的高质量交付和优秀的用户体验。
