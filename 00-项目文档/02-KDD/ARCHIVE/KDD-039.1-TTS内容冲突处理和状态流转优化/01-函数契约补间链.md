# 需求："在线实时TTS处理系统" 的函数契约补间链 (V1.0)

## 0. 依赖关系与影响分析

- [修改] `cloudflare/d1/tts-db/migrations/`: 需要新增迁移脚本删除azure_batch_jobs表和相关批处理字段，简化为纯实时处理架构
- [修改] `cloudflare/workers/tts/src/index.ts`: 需要重构为实时处理模式，移除所有批处理相关逻辑，实现直接TTS调用
- [简化] `senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py`: 简化为纯任务提交器，移除本地状态管理逻辑
- [重用] `cloudflare/workers/tts/src/services/audio.service.ts`: Azure TTS调用逻辑可以复用，但需要简化为单个请求处理
- [替代] 批处理相关的定时任务逻辑: 将被Worker内的实时处理和状态管理逻辑完全取代
- [新增] Worker状态管理机制: Worker负责tts_tasks表的筛选、并发控制、状态更新
- [新增] 并发控制机制: 需要实现TPS限制和请求队列管理

## 1. 项目文件结构概览 (Project File Structure Overview)

```
senseword-ios/
├── cloudflare/
│   ├── workers/
│   │   └── tts/
│   │       ├── src/
│   │       │   ├── index.ts                    # [修改] 重构为实时TTS处理入口
│   │       │   ├── types/
│   │       │   │   └── realtime-tts-types.ts   # [新增] 实时TTS类型定义
│   │       │   ├── services/
│   │       │   │   ├── realtime-tts.service.ts # [新增] 实时TTS核心服务
│   │       │   │   ├── task-manager.service.ts # [新增] Worker端任务状态管理
│   │       │   │   └── rate-limiter.service.ts # [新增] TPS限制和并发控制
│   │       │   └── utils/
│   │       │       └── azure-tts.util.ts       # [修改] 简化Azure TTS调用工具
│   │       └── wrangler.toml                   # [修改] 移除批处理相关配置
│   └── d1/
│       └── tts-db/
│           └── migrations/
│               ├── 0001_pure_database_schema.sql # [已实现] 原始数据库schema
│               └── 0002_remove_batch_processing.sql # [新增] 删除批处理相关表和字段
├── senseword-content-factory/
│   └── workflow/
│       └── 08-音频生成/
│           └── scripts/
│               ├── 01_submit_tts_tasks.py      # [简化] 纯任务提交器，移除状态管理
│               └── config.json                 # [修改] 更新Worker端点配置
└── 0-KDD - 关键帧驱动开发/
    └── 02-KDD/
        └── KDD-039-worker TTS 处理流程/
            └── KDD/
                └── KDD-039.7-TTS内容冲突处理和状态流转优化/
                    └── docs/
                        └── 001-在线实时TTS架构迁移设计方案.md # [已实现] 架构设计文档
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/tts/realtime-processing`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-senseword-realtime-tts
- 基础分支: `main`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout main
    # git pull origin main
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-senseword-realtime-tts -b feature/tts/realtime-processing main
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [x] chore(db): 添加数据库迁移脚本删除azure_batch_jobs表和批处理字段
- [x] refactor(tts-types): 定义实时TTS处理的核心类型和接口
- [x] feat(task-manager): 实现Worker端任务状态管理服务
- [x] feat(rate-limiter): 实现TPS限制和并发控制服务
- [x] refactor(azure-tts): 简化Azure TTS调用工具为单请求处理
- [x] feat(realtime-tts): 实现核心实时TTS处理服务
- [x] refactor(tts-worker): 重构Worker主入口为实时处理模式
- [x] test(tts-worker): 添加实时TTS处理的单元测试
- [x] refactor(python-script): 简化Python脚本为纯任务提交器
- [ ] docs(tts): 更新TTS处理流程文档和配置说明
- [ ] perf(tts): 优化并发处理性能和错误处理机制

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 数据库迁移脚本

- 职责: 删除批处理相关的表和字段，简化数据库结构为纯实时处理架构
- 函数签名: `SQL Migration Script`
- 所在文件: `cloudflare/d1/tts-db/migrations/0002_remove_batch_processing.sql`

>>>>> 输入 (Input): 现有数据库结构

```sql
-- 当前数据库包含批处理相关结构
-- 表: azure_batch_jobs (需要删除)
-- 字段: tts_tasks.batchId (需要删除)
-- 索引: 相关批处理索引 (需要删除)
```

<<<<< 输出 (Output): 简化的数据库结构

```sql
-- 迁移脚本内容
-- 删除批处理相关表
DROP TABLE IF EXISTS azure_batch_jobs;

-- 删除tts_tasks表中的批处理字段
ALTER TABLE tts_tasks DROP COLUMN batchId;

-- 删除批处理相关索引
DROP INDEX IF EXISTS idx_tts_tasks_batch_id;
DROP INDEX IF EXISTS idx_azure_batch_jobs_status;
DROP INDEX IF EXISTS idx_azure_batch_jobs_type;

-- 简化后的tts_tasks表结构
-- tts_tasks表只保留实时处理必需字段:
-- - ttsId (主键)
-- - text (文本内容)
-- - type (TTS类型)
-- - status (pending/processing/completed/failed)
-- - audioUrl (音频链接)
-- - errorMessage (错误信息)
-- - createdAt, updatedAt, completedAt (时间戳)
```

---

### [FC-02]: Python脚本简化任务提交器 (完整数据转变链)

- 职责: 按单词为单位提交TTS任务，处理Worker响应数组，汇总最终结果
- 函数签名: `submit_words_to_worker(word_limit: int) -> SubmissionResult`
- 所在文件: `senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py`

>>>>> 输入 (Input): 简化的提交参数

```python
# 脚本调用参数
word_limit: int = 100                # 处理单词数量限制
words_per_batch: int = 10            # 每批次单词数量
worker_url: str = "https://tts.senseword.app"  # Worker端点

# 从本地数据库查询的具体表和字段
# 查询表1: words_for_publish 表
# SELECT word, ttsStatus, ttsHashList FROM words_for_publish
# WHERE ttsStatus = 'pending' OR word IN (
#   SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed'
# )
# ORDER BY
#   CASE WHEN word IN (SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed')
#   THEN 0 ELSE 1 END,  -- 优先处理包含failed任务的单词
#   word
# LIMIT ?

# 查询表2: tts_assets 表 (对每个单词)
# SELECT ttsId, textToSpeak, ttsType, status
# FROM tts_assets
# WHERE originalText = ?
# AND status IN ('pending', 'failed')

class WordWithTasks:
    word: str                        # 来自 words_for_publish.word
    status: str                      # 来自 words_for_publish.ttsStatus (pending/submitted)
    tts_hash_list: str               # 来自 words_for_publish.ttsHashList (JSON字符串)
    tts_tasks: List[TTSTask]         # 来自 tts_assets 表的查询结果

class TTSTask:
    ttsId: str                       # 来自 tts_assets.ttsId (24位哈希ID)
    text: str                        # 来自 tts_assets.textToSpeak (实际TTS文本)
    type: str                        # 来自 tts_assets.ttsType (phonetic_bre/phonetic_name/example_sentence/phrase_breakdown)
    status: str                      # 来自 tts_assets.status (pending/failed)

# 具体查询示例和数据
# 步骤1: 查询待处理单词 (优先处理包含failed任务的单词，确保最终一致性)
words_query = """
SELECT word, ttsStatus, ttsHashList
FROM words_for_publish
WHERE ttsStatus = 'pending'
   OR word IN (SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed')
ORDER BY
  CASE WHEN word IN (SELECT DISTINCT originalText FROM tts_assets WHERE status = 'failed')
  THEN 0 ELSE 1 END,  -- 优先处理包含failed TTS任务的单词
  word
LIMIT ?
"""

# 步骤2: 对每个单词查询其TTS任务 (如果tts_assets表中已存在)
tasks_query = """
SELECT ttsId, textToSpeak, ttsType, status
FROM tts_assets
WHERE originalText = ?
  AND status IN ('pending', 'failed')
"""

# 注意:
# 1. 如果tts_assets表中没有该单词的记录，需要从ttsHashList解析生成任务
# 2. 优先处理包含failed任务的单词，确保最终一致性
# 3. 单词一旦提交成功就标记为submitted，与个别TTS任务失败无关

# 查询结果示例
List[WordWithTasks] = [
    WordWithTasks(
        word="hello",                # words_for_publish.word
        status="pending",            # words_for_publish.ttsStatus
        tts_hash_list='{"phonetic_name": "a1b2c3d4e5f6789012345678", "phonetic_bre": "b2c3d4e5f6789012345678a1", ...}',  # words_for_publish.ttsHashList
        tts_tasks=[
            TTSTask(
                ttsId="a1b2c3d4e5f6789012345678",  # tts_assets.ttsId 或从ttsHashList解析
                text="hello",                      # tts_assets.textToSpeak 或从contentJson解析
                type="phonetic_name",              # tts_assets.ttsType 或从ttsHashList键名
                status="pending"                   # tts_assets.status 或默认pending
            ),
            TTSTask(
                ttsId="b2c3d4e5f6789012345678a1",
                text="ˈheləʊ",
                type="phonetic_bre",
                status="failed"                    # 上次失败的任务，优先处理
            ),
            TTSTask(
                ttsId="c3d4e5f6789012345678a1b2",
                text="Hello, how are you?",
                type="example_sentence",
                status="pending"
            ),
            # ... 约25个任务 (从ttsHashList解析得到)
        ]
    ),
    WordWithTasks(
        word="world",
        status="pending",
        tts_hash_list='{"phonetic_name": "d4e5f6g7h8i9012345678901", ...}',
        tts_tasks=[
            # ... 该单词的TTS任务 (从ttsHashList + contentJson解析)
        ]
    ),
    # ... 更多单词 (总计word_limit个)
]
```

**中间数据转变过程 (Intermediate Data Transformations):**

```python
# 步骤1: 单个单词的Worker请求格式
class SubmitWordTTSRequest:
    word: str                        # 单词名称
    tasks: List[Dict]                # 简化的任务列表

# 转换示例
SubmitWordTTSRequest(
    word="hello",
    tasks=[
        {"ttsId": "a1b2c3d4...", "text": "hello", "type": "phonetic_name"},
        {"ttsId": "b2c3d4e5...", "text": "ˈheləʊ", "type": "phonetic_bre"},
        # ... 25个任务
    ]
)

# 步骤2: Worker单个响应格式 (来自FC-03)
class SubmitWordTTSResponse:
    success: bool                    # 是否成功入库
    word: str                        # 处理的单词
    received: int                    # 接收的任务数
    inserted: int                    # 成功入库的任务数
    failed_tasks: List[str]          # 失败的ttsId列表
    timestamp: str                   # 处理时间戳

# 单个响应示例
SubmitWordTTSResponse(
    success=True,
    word="hello",
    received=25,
    inserted=23,
    failed_tasks=["a1b2c3d4...", "b2c3d4e5..."],  # 2个失败
    timestamp="2025-07-15T03:30:00.000Z"
)

# 步骤3: 多个单词的响应数组
List[SubmitWordTTSResponse] = [
    SubmitWordTTSResponse(success=True, word="hello", received=25, inserted=25, failed_tasks=[], ...),
    SubmitWordTTSResponse(success=True, word="world", received=24, inserted=22, failed_tasks=["c3d4e5f6...", "d4e5f6g7..."], ...),
    SubmitWordTTSResponse(success=False, word="error", received=26, inserted=0, failed_tasks=["all"], ...),
    # ... 100个响应
]
```

<<<<< 输出 (Output): SubmissionResult

汇总所有Worker响应后的最终结果。

```python
# 最终汇总结果
class SubmissionResult:
    words_processed: int             # 处理的单词数
    words_submitted: int             # 成功提交的单词数 (failed_tasks为空)
    words_failed: int                # 提交失败的单词数 (failed_tasks非空)
    total_tasks: int                 # 总TTS任务数
    total_inserted: int              # 总入库任务数
    failed_tasks: List[str]          # 汇总所有失败的ttsId
    submission_rate: float           # 单词提交成功率
    task_success_rate: float         # 任务入库成功率
    total_time: float                # 总耗时(秒)

# 汇总逻辑示例
SubmissionResult(
    words_processed=100,             # 处理了100个单词
    words_submitted=97,              # 97个单词完全成功 (failed_tasks=[])
    words_failed=3,                  # 3个单词有失败任务
    total_tasks=2500,                # 100个单词 × 平均25任务
    total_inserted=2485,             # 实际入库2485个任务
    failed_tasks=[                   # 汇总所有失败的ttsId
        "a1b2c3d4...", "b2c3d4e5...", # 来自单词1
        "c3d4e5f6...", "d4e5f6g7...", # 来自单词2
        "all"                         # 来自完全失败的单词3
    ],
    submission_rate=97.0,            # 97/100 = 97%
    task_success_rate=99.4,          # 2485/2500 = 99.4%
    total_time=45.2
)
```

---

### [FC-03]: Worker简化HTTP端点处理器

- 职责: 接收单词级别的TTS任务提交，将任务入库并返回简单的成功/失败结果
- 函数签名: `handleWordTTSSubmit(request: Request, env: Env) -> Promise<Response>`
- 所在文件: `cloudflare/workers/tts/src/index.ts`

>>>>> 输入 (Input): HTTP Request

```typescript
// HTTP POST请求到 /submit 端点 (单词级别)
interface SubmitWordTTSRequest {
  word: string;                      // 单词名称
  tasks: Array<{
    ttsId: string;                   // 24位哈希ID
    text: string;                    // 文本内容
    type: string;                    // TTS类型
  }>;
}

// 请求示例 (一个单词的所有TTS任务)
POST /submit
Content-Type: application/json

{
  "word": "hello",
  "tasks": [
    { "ttsId": "a1b2c3d4e5f6789012345678", "text": "hello", "type": "phonetic_name" },
    { "ttsId": "b2c3d4e5f6789012345678a1", "text": "ˈheləʊ", "type": "phonetic_bre" },
    { "ttsId": "c3d4e5f6789012345678a1b2", "text": "Hello world", "type": "example_sentence" },
    // ... 约25个任务/单词
  ]
}
```

<<<<< 输出 (Output): HTTP Response

极简的响应格式，专注于入库结果。

```typescript
// 成功响应 (HTTP 200) - 任务已入库
interface SubmitWordTTSResponse {
  success: boolean;                  // 是否成功入库
  word: string;                      // 处理的单词
  received: number;                  // 接收的任务数
  inserted: number;                  // 成功入库的任务数
  failed_tasks: string[];            // 失败的ttsId列表(罕见)
  timestamp: string;                 // 处理时间戳
}

// 正常成功响应
HTTP 200 OK
{
  "success": true,
  "word": "hello",
  "received": 25,
  "inserted": 25,
  "failed_tasks": [],
  "timestamp": "2025-07-15T03:30:00.000Z"
}

// 部分失败响应 (极罕见)
HTTP 200 OK
{
  "success": true,
  "word": "hello",
  "received": 25,
  "inserted": 23,
  "failed_tasks": ["a1b2c3d4e5f6789012345678", "b2c3d4e5f6789012345678a1"],
  "timestamp": "2025-07-15T03:30:00.000Z"
}

// 完全失败响应 (极罕见)
HTTP 500 Internal Server Error
{
  "success": false,
  "word": "hello",
  "received": 25,
  "inserted": 0,
  "failed_tasks": ["all"],
  "timestamp": "2025-07-15T03:30:00.000Z"
}
```

---

### [FC-04]: Worker任务状态管理服务

- 职责: Worker端核心状态管理，筛选pending任务，更新状态，协调并发处理
- 函数签名: `manageTaskProcessing(tasks: TTSTaskInput[], env: Env) -> Promise<TaskProcessingResult>`
- 所在文件: `cloudflare/workers/tts/src/services/task-manager.service.ts`

>>>>> 输入 (Input): TTSTaskInput[]

```typescript
// 来自HTTP端点的任务列表
interface TTSTaskInput {
  ttsId: string;                     // 24位哈希ID
  text: string;                      // 文本内容
  type: TTSType;                     // TTS类型
}

// 输入示例
TTSTaskInput[] = [
  { ttsId: "a1b2c3d4e5f6789012345678", text: "hello", type: "phonetic_name" },
  { ttsId: "b2c3d4e5f6789012345678a1", text: "ˈheləʊ", type: "phonetic_bre" },
  { ttsId: "c3d4e5f6789012345678a1b2", text: "world", type: "phonetic_name" }
]
```

<<<<< 输出 (Output): TaskProcessingResult

任务处理结果，包含状态筛选和处理统计。

```typescript
// 任务处理结果
interface TaskProcessingResult {
  received: number;                  // 接收任务数
  pending_tasks: TTSTaskInput[];     // 需要处理的pending任务
  skipped_completed: number;         // 跳过的已完成任务数
  skipped_processing: number;        // 跳过的处理中任务数
  processed_successfully: number;    // 成功处理数
  processing_failed: number;         // 处理失败数
  processing_results: TaskResult[];  // 详细处理结果
}

interface TaskResult {
  ttsId: string;
  status: 'completed' | 'failed' | 'skipped';
  audioUrl?: string;
  errorMessage?: string;
  processingTime: number;
}

// 返回结果示例
TaskProcessingResult {
  received: 50,
  pending_tasks: [/* 45个pending任务 */],
  skipped_completed: 3,              // 3个任务已完成
  skipped_processing: 2,             // 2个任务正在处理中
  processed_successfully: 43,
  processing_failed: 2,
  processing_results: [
    { ttsId: "a1b2...", status: "completed", audioUrl: "https://...", processingTime: 1200 },
    { ttsId: "b2c3...", status: "failed", errorMessage: "Azure timeout", processingTime: 30000 },
    // ... 更多结果
  ]
}
```

---

### [FC-05]: 实时TTS处理服务

- 职责: 核心TTS处理逻辑，调用Azure TTS API，上传R2存储，更新数据库状态
- 函数签名: `processRealtimeTTS(task: TTSTaskInput, env: Env) -> Promise<TTSProcessingResult>`
- 所在文件: `cloudflare/workers/tts/src/services/realtime-tts.service.ts`

>>>>> 输入 (Input): TTSTaskInput

```typescript
// 单个待处理的TTS任务
interface TTSTaskInput {
  ttsId: string;                     // 24位哈希ID
  text: string;                      // 文本内容
  type: TTSType;                     // TTS类型
}

// 输入示例
TTSTaskInput {
  ttsId: "a1b2c3d4e5f6789012345678",
  text: "hello",
  type: "phonetic_name"
}
```

<<<<< 输出 (Output): TTSProcessingResult

单个任务的完整处理结果。

```typescript
// TTS处理结果
interface TTSProcessingResult {
  ttsId: string;                     // 任务ID
  success: boolean;                  // 处理是否成功
  status: 'completed' | 'failed';    // 最终状态
  audioUrl?: string;                 // 音频URL(成功时)
  errorMessage?: string;             // 错误信息(失败时)
  processingTime: number;            // 处理耗时(毫秒)
  azureCallTime: number;             // Azure调用耗时
  r2UploadTime: number;              // R2上传耗时
  dbUpdateTime: number;              // 数据库更新耗时
}

// 成功结果示例
TTSProcessingResult {
  ttsId: "a1b2c3d4e5f6789012345678",
  success: true,
  status: "completed",
  audioUrl: "https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav",
  processingTime: 1250,
  azureCallTime: 800,
  r2UploadTime: 300,
  dbUpdateTime: 150
}

// 失败结果示例
TTSProcessingResult {
  ttsId: "b2c3d4e5f6789012345678a1",
  success: false,
  status: "failed",
  errorMessage: "Azure TTS API timeout after 30 seconds",
  processingTime: 30000,
  azureCallTime: 30000,
  r2UploadTime: 0,
  dbUpdateTime: 50
}
```

---

### [FC-06]: Azure TTS调用工具

- 职责: 简化的Azure TTS单请求调用，根据类型选择语音，返回音频数据
- 函数签名: `callAzureRealtimeTTS(text: string, type: TTSType, env: Env) -> Promise<ArrayBuffer>`
- 所在文件: `cloudflare/workers/tts/src/utils/azure-tts.util.ts`

>>>>> 输入 (Input): TTS请求参数

```typescript
// Azure TTS调用参数
interface AzureTTSParams {
  text: string;                      // 文本内容
  type: TTSType;                     // TTS类型(决定语音选择)
  azureKey: string;                  // Azure API密钥
  azureRegion: string;               // Azure区域
}

// 输入示例
AzureTTSParams {
  text: "hello",
  type: "phonetic_name",
  azureKey: "BAdzCkBt8KiZZezZCCNZSL8z...",
  azureRegion: "eastus"
}
```

<<<<< 输出 (Output): ArrayBuffer

Azure TTS返回的音频数据。

```typescript
// 音频数据
ArrayBuffer                          // WAV格式音频数据
// 大小: 通常20-100KB
// 格式: 24khz-16bit-mono-pcm
// 时长: 根据文本长度，通常0.5-5秒

// 语音选择逻辑:
// - phonetic_bre: "en-GB-MiaNeural" (英式发音)
// - phonetic_name, phonetic_ipa, example_sentence, phrase_breakdown: "en-US-AndrewNeural" (美式发音)

// SSML格式示例:
// <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
//   <voice name="en-US-AndrewNeural">
//     <prosody rate="0.9" pitch="0%">hello</prosody>
//   </voice>
// </speak>
```

---

### [FC-07]: R2存储上传服务

- 职责: 将音频数据上传到R2存储，生成CDN访问URL
- 函数签名: `uploadAudioToR2(ttsId: string, audioBuffer: ArrayBuffer, env: Env) -> Promise<string>`
- 所在文件: `cloudflare/workers/tts/src/services/realtime-tts.service.ts`

>>>>> 输入 (Input): 音频上传参数

```typescript
// R2上传参数
interface R2UploadParams {
  ttsId: string;                     // 24位哈希ID作为文件名
  audioBuffer: ArrayBuffer;          // 音频数据
  r2Bucket: R2Bucket;                // R2存储桶
}

// 输入示例
R2UploadParams {
  ttsId: "a1b2c3d4e5f6789012345678",
  audioBuffer: ArrayBuffer(45678),   // 音频数据
  r2Bucket: env.AUDIO_BUCKET
}
```

<<<<< 输出 (Output): string

R2存储的CDN访问URL。

```typescript
// 音频文件URL
string = "https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav"

// R2存储路径: a1b2c3d4e5f6789012345678.wav
// Content-Type: audio/wav
// Cache-Control: public, max-age=31536000 (1年缓存)
// 文件大小: 通常20-100KB
```

---

### [FC-08]: 数据库状态更新服务

- 职责: 更新tts_tasks表中的任务状态，记录处理结果和时间戳
- 函数签名: `updateTaskStatus(ttsId: string, status: TaskStatus, result: TaskUpdateData, env: Env) -> Promise<boolean>`
- 所在文件: `cloudflare/workers/tts/src/services/task-manager.service.ts`

>>>>> 输入 (Input): 状态更新参数

```typescript
// 数据库更新参数
interface TaskUpdateParams {
  ttsId: string;                     // 任务ID
  status: TaskStatus;                // 新状态
  result: TaskUpdateData;            // 更新数据
  db: D1Database;                    // D1数据库连接
}

type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed';

interface TaskUpdateData {
  audioUrl?: string;                 // 音频URL(成功时)
  errorMessage?: string;             // 错误信息(失败时)
  completedAt?: string;              // 完成时间
  processingTime?: number;           // 处理耗时
}

// 输入示例
TaskUpdateParams {
  ttsId: "a1b2c3d4e5f6789012345678",
  status: "completed",
  result: {
    audioUrl: "https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav",
    completedAt: "2025-07-15T03:30:00.000Z",
    processingTime: 1250
  },
  db: env.TTS_DB
}
```

<<<<< 输出 (Output): boolean

数据库更新是否成功。

```typescript
// 更新成功
boolean = true

// 执行的SQL语句示例:
// UPDATE tts_tasks
// SET status = 'completed',
//     audioUrl = 'https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav',
//     completedAt = '2025-07-15T03:30:00.000Z',
//     updatedAt = datetime('now')
// WHERE ttsId = 'a1b2c3d4e5f6789012345678'

// 失败时返回 false，并记录错误日志
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/d1/tts-db/migrations/0001_pure_database_schema.sql
cloudflare/d1/tts-db/migrations/0002_remove_batch_processing.sql
cloudflare/workers/tts/src/index.ts
cloudflare/workers/tts/src/types/realtime-tts-types.ts
cloudflare/workers/tts/src/services/realtime-tts.service.ts
cloudflare/workers/tts/src/services/task-manager.service.ts
cloudflare/workers/tts/src/services/rate-limiter.service.ts
cloudflare/workers/tts/src/utils/azure-tts.util.ts
cloudflare/workers/tts/wrangler.toml
senseword-content-factory/workflow/08-音频生成/scripts/01_submit_tts_tasks.py
senseword-content-factory/workflow/08-音频生成/scripts/config.json
senseword-content-factory/workflow/02-数据库初始化/scripts/database_init.py
</context_files>

## 6. 核心业务流程伪代码

```typescript
// 简化的Worker处理流程
async function handleWordTTSSubmit(request: Request, env: Env): Promise<Response> {
    // [FC-03] 解析HTTP请求 (单词级别)
    const { word, tasks } = await request.json() as SubmitWordTTSRequest

    const failedTasks: string[] = []
    let insertedCount = 0

    try {
        // [FC-04] 批量将TTS任务入库 (状态为pending)
        for (const task of tasks) {
            try {
                await env.TTS_DB.prepare(`
                    INSERT OR IGNORE INTO tts_tasks
                    (ttsId, text, type, status, createdAt)
                    VALUES (?, ?, ?, 'pending', datetime('now'))
                `).bind(task.ttsId, task.text, task.type).run()

                insertedCount++
            } catch (error) {
                // 个别任务入库失败 (极罕见)
                failedTasks.push(task.ttsId)
                console.error(`Failed to insert task ${task.ttsId}:`, error)
            }
        }

        // 返回简单的入库结果
        return new Response(JSON.stringify({
            success: true,
            word,
            received: tasks.length,
            inserted: insertedCount,
            failed_tasks: failedTasks,
            timestamp: new Date().toISOString()
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        })

    } catch (error) {
        // 整体入库失败 (极罕见)
        return new Response(JSON.stringify({
            success: false,
            word,
            received: tasks.length,
            inserted: 0,
            failed_tasks: ["all"],
            timestamp: new Date().toISOString()
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        })
    }
}

// Python脚本完整数据转变流程
async function submitWordsToWorker(wordLimit: int): Promise<SubmissionResult> {
    // [FC-02] 步骤1: 查询待处理单词 (优先处理包含failed任务的单词)
    const wordsToProcess: WordWithTasks[] = await getWordsToProcess(wordLimit)

    // 初始化汇总统计
    let wordsSubmitted = 0
    let wordsFailed = 0
    let totalTasks = 0
    let totalInserted = 0
    const allFailedTasks: string[] = []
    const workerResponses: SubmitWordTTSResponse[] = []

    // 步骤2: 逐个处理单词，收集Worker响应
    for (const wordData of wordsToProcess) {
        try {
            // 转换为Worker请求格式
            const workerRequest: SubmitWordTTSRequest = {
                word: wordData.word,
                tasks: wordData.tts_tasks.map(task => ({
                    ttsId: task.ttsId,
                    text: task.text,
                    type: task.type
                }))
            }

            // 调用Worker
            const response = await fetch(`${WORKER_URL}/submit`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(workerRequest)
            })

            // 解析Worker响应
            const workerResult: SubmitWordTTSResponse = await response.json()
            workerResponses.push(workerResult)

            // 步骤3: 处理单个Worker响应
            totalTasks += workerResult.received
            totalInserted += workerResult.inserted

            if (workerResult.success) {
                // Worker有响应 - 单词立即标记为submitted (基于提交失败极罕见的前提)
                await updateWordStatus(wordData.word, 'submitted')
                wordsSubmitted++

                // 处理个别任务失败 (与单词状态无关)
                if (workerResult.failed_tasks.length > 0) {
                    if (workerResult.failed_tasks.includes("all")) {
                        // 完全失败 - 添加该单词的所有ttsId
                        allFailedTasks.push(...wordData.tts_tasks.map(t => t.ttsId))
                    } else {
                        // 部分失败 - 添加具体失败的ttsId
                        allFailedTasks.push(...workerResult.failed_tasks)
                    }

                    // 只标记具体的TTS任务为failed，单词状态仍为submitted
                    await markTasksAsFailed(workerResult.failed_tasks, wordData.word)
                }
            } else {
                // Worker完全失败 (网络错误等) - 极罕见情况
                wordsFailed++
                allFailedTasks.push(...wordData.tts_tasks.map(t => t.ttsId))
                // 单词状态保持pending，等待下次重试
            }

        } catch (error) {
            // 网络或其他错误 - 整个单词失败
            wordsFailed++
            allFailedTasks.push(...wordData.tts_tasks.map(t => t.ttsId))
            console.error(`Failed to submit word ${wordData.word}:`, error)
        }
    }

    // 步骤4: 汇总最终结果
    const finalResult: SubmissionResult = {
        words_processed: wordsToProcess.length,
        words_submitted: wordsSubmitted,
        words_failed: wordsFailed,
        total_tasks: totalTasks,
        total_inserted: totalInserted,
        failed_tasks: allFailedTasks,
        submission_rate: (wordsSubmitted / wordsToProcess.length) * 100,
        task_success_rate: totalTasks > 0 ? (totalInserted / totalTasks) * 100 : 0,
        total_time: (Date.now() - startTime) / 1000
    }

    console.log(`📊 提交完成: ${wordsSubmitted}/${wordsToProcess.length} 单词成功, ${totalInserted}/${totalTasks} 任务入库`)

    return finalResult
}
```