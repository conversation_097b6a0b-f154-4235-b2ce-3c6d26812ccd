# 在线实时TTS处理系统工作原理可视化

本文档通过一系列可视化图表，详细展示SenseWord在线实时TTS处理系统的工作原理、数据流转和关键处理逻辑。

## 目录

1. [系统架构总览](#系统架构总览)
2. [数据流转与处理流程](#数据流转与处理流程)
3. [速率限制与自动降级机制](#速率限制与自动降级机制)
4. [TTS任务处理详细流程](#tts任务处理详细流程)
5. [失败处理与重试机制](#失败处理与重试机制)
6. [数据结构转换示例](#数据结构转换示例)

---

## 系统架构总览

下图展示了SenseWord在线实时TTS处理系统的整体架构，包括所有关键组件及其交互关系。

```mermaid
flowchart TB
    classDef localSystem fill:#E0F7FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef cloudflareWorker fill:#FFECB3,stroke:#000000,stroke-width:3px,color:#000000
    classDef azureService fill:#F3E5F5,stroke:#000000,stroke-width:2px,color:#000000
    classDef storage fill:#E8F5E9,stroke:#000000,stroke-width:2px,color:#000000
    classDef database fill:#FFF3E0,stroke:#000000,stroke-width:2px,color:#000000
    
    LocalSystem["💻 本地系统<br>(Python脚本)"]:::localSystem
    CloudflareWorker["☁️ Cloudflare Worker<br>(实时TTS处理)"]:::cloudflareWorker
    AzureTTS["🔊 Azure TTS API<br>(语音合成)"]:::azureService
    R2Storage["📦 R2存储<br>(音频文件)"]:::storage
    D1Database["🗄️ D1数据库<br>(任务状态)"]:::database
    
    subgraph 本地环境["本地环境"]
        LocalSystem
    end
    
    subgraph Cloudflare["Cloudflare平台"]
        CloudflareWorker
        D1Database
        R2Storage
    end
    
    subgraph Azure["Azure平台"]
        AzureTTS
    end
    
    LocalSystem -->|1. 提交单词级别TTS任务| CloudflareWorker
    CloudflareWorker -->|2. 存储任务| D1Database
    CloudflareWorker -->|3. 请求语音合成| AzureTTS
    AzureTTS -->|4. 返回音频数据| CloudflareWorker
    CloudflareWorker -->|5. 上传音频文件| R2Storage
    CloudflareWorker -->|6. 更新任务状态| D1Database
    R2Storage -->|7. 生成CDN URL| CloudflareWorker
    CloudflareWorker -->|8. 返回处理结果| LocalSystem
    
    %% 定时任务流程
    D1Database -->|9. 查询待处理任务| CloudflareWorker
    
    %% 添加说明标签
    style 本地环境 fill:#E0F7FA,stroke:#000000,stroke-width:1px
    style Cloudflare fill:#FFFDE7,stroke:#000000,stroke-width:1px
    style Azure fill:#F3E5F5,stroke:#000000,stroke-width:1px
```

### 核心组件说明

1. **本地系统 (Python脚本)**
   - 负责从本地数据库读取待处理单词
   - 按单词级别提交TTS任务到Worker
   - 更新本地单词状态

2. **Cloudflare Worker (实时TTS处理)**
   - 接收单词级别的TTS任务
   - 管理任务状态和处理流程
   - 调用Azure TTS API生成音频
   - 上传音频到R2存储
   - 提供定时任务处理待处理队列

3. **D1数据库 (任务状态)**
   - 存储所有TTS任务的状态和元数据
   - 支持任务状态查询和更新
   - 不再包含批处理相关表和字段

4. **Azure TTS API (语音合成)**
   - 提供高质量的文本到语音转换服务
   - 支持多种语音和语言
   - 按字符计费

5. **R2存储 (音频文件)**
   - 存储生成的音频文件
   - 提供CDN访问URL

---

## 数据流转与处理流程

下图展示了数据在系统中的完整流转过程，从Python脚本提交任务开始，到最终音频生成完成。

```mermaid
sequenceDiagram
    participant Python as 💻 Python脚本
    participant Worker as ☁️ Cloudflare Worker
    participant D1 as 🗄️ D1数据库
    participant Azure as 🔊 Azure TTS API
    participant R2 as 📦 R2存储
    
    autonumber
    
    Note over Python: 读取待处理单词
    
    Python->>Worker: 提交单词级别TTS任务<br>POST /submit
    Note right of Python: {<br>  word: "hello",<br>  tasks: [{ttsId, text, type}, ...]<br>}
    
    Worker->>D1: 批量插入任务<br>(ttsId, text, type, status='pending')
    D1-->>Worker: 插入结果
    
    Worker->>Python: 返回提交结果
    Note left of Worker: {<br>  success: true,<br>  word: "hello",<br>  inserted: 25,<br>  failed_tasks: []<br>}
    
    Python->>Python: 更新单词状态为'submitted'
    
    Note over Worker: 定时任务触发<br>(每分钟)
    
    Worker->>D1: 查询待处理任务<br>(status='pending')
    D1-->>Worker: 返回待处理任务列表
    
    loop 每个待处理任务
        Worker->>D1: 更新状态为'processing'
        Worker->>Azure: 请求语音合成
        Note right of Worker: 使用多Key轮换策略
        
        Azure-->>Worker: 返回音频数据
        
        Worker->>R2: 上传音频文件
        R2-->>Worker: 返回存储URL
        
        Worker->>D1: 更新状态为'completed'<br>添加audioUrl和completedAt
    end
    
    Note over Python: 下次运行时<br>优先处理失败任务
```

### 处理流程说明

1. **任务提交阶段**
   - Python脚本按单词级别提交TTS任务
   - Worker将任务批量插入数据库
   - 返回提交结果，Python脚本更新单词状态

2. **任务处理阶段**
   - Worker定时任务每分钟触发
   - 查询并处理一批待处理任务
   - 每个任务独立处理，失败不影响其他任务

3. **状态流转**
   - 任务状态: pending → processing → completed/failed
   - 单词状态: pending → submitted (基于Worker响应)

---

## 速率限制与自动降级机制

下图展示了系统的速率限制和自动降级机制，确保系统在高负载下仍能稳定运行。

```mermaid
stateDiagram-v2
    [*] --> 正常运行
    
    state 正常运行 {
        [*] --> TPS_50
        
        state "TPS = 50" as TPS_50
        state "TPS = 60" as TPS_60
        state "TPS = 70" as TPS_70
        state "TPS = 80" as TPS_80
        state "TPS = 90" as TPS_90
        state "TPS = 100" as TPS_100
        
        TPS_50 --> TPS_60 : 错误率 < 2%<br>持续30秒
        TPS_60 --> TPS_70 : 错误率 < 2%<br>持续30秒
        TPS_70 --> TPS_80 : 错误率 < 2%<br>持续30秒
        TPS_80 --> TPS_90 : 错误率 < 2%<br>持续30秒
        TPS_90 --> TPS_100 : 错误率 < 2%<br>持续30秒
        
        TPS_100 --> TPS_80 : 错误率 > 10%
        TPS_90 --> TPS_72 : 错误率 > 10%
        TPS_80 --> TPS_64 : 错误率 > 10%
        TPS_70 --> TPS_56 : 错误率 > 10%
        TPS_60 --> TPS_48 : 错误率 > 10%
        TPS_50 --> TPS_40 : 错误率 > 10%
    }
    
    正常运行 --> 降级模式 : 错误率 > 20%<br>持续60秒
    
    state 降级模式 {
        [*] --> TPS_20
        
        state "TPS = 20" as TPS_20
        state "TPS = 30" as TPS_30
        state "TPS = 40" as TPS_40
        
        TPS_20 --> TPS_30 : 错误率 < 5%<br>持续60秒
        TPS_30 --> TPS_40 : 错误率 < 5%<br>持续60秒
        
        TPS_40 --> TPS_30 : 错误率 > 15%
        TPS_30 --> TPS_20 : 错误率 > 15%
    }
    
    降级模式 --> 正常运行 : 错误率 < 2%<br>持续120秒
    
    state "系统暂停" as 系统暂停
    
    降级模式 --> 系统暂停 : 错误率 > 50%<br>持续120秒
    系统暂停 --> 降级模式 : 手动恢复<br>并修复问题
```

### 速率限制机制说明

1. **自适应TPS调整**
   - 系统默认以50 TPS运行
   - 根据错误率自动调整TPS
   - 低错误率时逐步提高TPS
   - 高错误率时快速降低TPS

2. **多级降级策略**
   - 正常运行模式: 50-100 TPS
   - 降级模式: 20-40 TPS
   - 系统暂停: 需手动恢复

3. **监控指标**
   - 错误率: 失败请求数/总请求数
   - 响应时间: 请求处理耗时
   - 资源使用率: CPU、内存使用情况

---

## TTS任务处理详细流程

下图展示了单个TTS任务从提交到完成的详细处理流程。

```mermaid
flowchart TD
    classDef process fill:#FFECB3,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#E0F7FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef database fill:#FFF3E0,stroke:#000000,stroke-width:2px,color:#000000
    classDef service fill:#F3E5F5,stroke:#000000,stroke-width:2px,color:#000000
    classDef storage fill:#E8F5E9,stroke:#000000,stroke-width:2px,color:#000000
    
    Start([开始处理任务])
    ValidateTask{验证任务数据}:::decision
    CheckExisting{检查任务是否存在}:::decision
    CheckStatus{检查任务状态}:::decision
    UpdateProcessing[更新状态为processing]:::process
    SelectAzureKey[选择Azure Key<br>轮换策略]:::process
    BuildSSML[构建SSML请求]:::process
    CallAzureTTS[调用Azure TTS API]:::service
    CheckAzureResponse{检查Azure响应}:::decision
    RetryAzure{重试次数<3?}:::decision
    UploadToR2[上传音频到R2]:::process
    CheckR2Response{上传成功?}:::decision
    RetryR2{重试次数<3?}:::decision
    UpdateCompleted[更新状态为completed<br>添加audioUrl]:::process
    UpdateFailed[更新状态为failed<br>记录错误信息]:::process
    End([结束处理])
    
    DB[(D1数据库)]:::database
    AzureTTS[Azure TTS API]:::service
    R2Storage[R2存储]:::storage
    
    Start --> ValidateTask
    ValidateTask -->|无效| UpdateFailed
    ValidateTask -->|有效| CheckExisting
    
    CheckExisting -->|不存在| UpdateFailed
    CheckExisting -->|存在| CheckStatus
    
    CheckStatus -->|completed| End
    CheckStatus -->|processing| End
    CheckStatus -->|pending/failed| UpdateProcessing
    
    UpdateProcessing --> DB
    UpdateProcessing --> SelectAzureKey
    
    SelectAzureKey --> BuildSSML
    BuildSSML --> CallAzureTTS
    CallAzureTTS --> AzureTTS
    AzureTTS --> CheckAzureResponse
    
    CheckAzureResponse -->|失败| RetryAzure
    RetryAzure -->|是| SelectAzureKey
    RetryAzure -->|否| UpdateFailed
    
    CheckAzureResponse -->|成功| UploadToR2
    UploadToR2 --> R2Storage
    R2Storage --> CheckR2Response
    
    CheckR2Response -->|失败| RetryR2
    RetryR2 -->|是| UploadToR2
    RetryR2 -->|否| UpdateFailed
    
    CheckR2Response -->|成功| UpdateCompleted
    
    UpdateCompleted --> DB
    UpdateCompleted --> End
    
    UpdateFailed --> DB
    UpdateFailed --> End
```

### 处理流程说明

1. **任务验证与状态检查**
   - 验证任务数据完整性
   - 检查任务是否已存在
   - 检查任务当前状态，避免重复处理

2. **Azure TTS调用**
   - 选择Azure Key (轮换策略)
   - 构建SSML请求
   - 调用Azure TTS API
   - 失败时自动重试 (最多3次)

3. **R2存储上传**
   - 上传音频文件到R2存储
   - 生成CDN访问URL
   - 失败时自动重试 (最多3次)

4. **状态更新**
   - 成功: 更新状态为completed，添加audioUrl
   - 失败: 更新状态为failed，记录错误信息

---

## 失败处理与重试机制

下图展示了系统如何处理失败任务并确保最终一致性。

```mermaid
sequenceDiagram
    participant Python as 💻 Python脚本
    participant DB as 🗄️ 本地数据库
    participant Worker as ☁️ Cloudflare Worker
    participant D1 as 🗄️ D1数据库
    
    autonumber
    
    Note over Python: 第一次运行
    
    Python->>DB: 查询待处理单词<br>(ttsStatus='pending')
    DB-->>Python: 返回单词列表
    
    Python->>Worker: 提交单词A的任务
    Worker->>D1: 存储任务
    Worker-->>Python: 返回成功结果
    
    Python->>DB: 更新单词A状态为'submitted'
    
    Note over Worker: 处理任务
    Worker->>D1: 查询待处理任务
    D1-->>Worker: 返回任务列表
    
    Worker->>Worker: 处理任务1-4成功<br>任务5失败
    Worker->>D1: 更新任务1-4状态为'completed'
    Worker->>D1: 更新任务5状态为'failed'
    
    Note over Python: 第二次运行
    
    Python->>DB: 查询单词<br>(优先查询包含failed任务的单词)
    DB-->>Python: 返回单词A (包含failed任务)
    
    Python->>Worker: 重新提交单词A的所有任务
    Worker->>D1: 检查任务状态
    
    Note over Worker: 智能跳过已完成任务
    Worker-->>Python: 返回结果 (只处理failed任务)
    
    Python->>DB: 保持单词A状态为'submitted'
    
    Note over Worker: 处理剩余失败任务
    Worker->>Worker: 处理任务5成功
    Worker->>D1: 更新任务5状态为'completed'
    
    Note over Python,D1: 最终一致性达成<br>所有任务都完成
```

### 失败处理机制说明

1. **优先级重试策略**
   - Python脚本优先处理包含failed任务的单词
   - 重新提交整个单词的所有任务
   - Worker智能跳过已完成的任务

2. **单词状态与任务状态解耦**
   - 单词状态只有pending → submitted的单向流转
   - 任务状态独立管理: pending → processing → completed/failed
   - 单词可以包含多个不同状态的任务

3. **最终一致性保证**
   - 通过优先级重试机制
   - 失败任务会在后续运行中被优先处理
   - 系统最终会处理完所有任务

---

## 数据结构转换示例

下图展示了数据在系统中的转换过程，使用真实数据示例。

```mermaid
graph TD
    classDef localData fill:#E0F7FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef workerData fill:#FFECB3,stroke:#000000,stroke-width:2px,color:#000000
    classDef azureData fill:#F3E5F5,stroke:#000000,stroke-width:2px,color:#000000
    classDef r2Data fill:#E8F5E9,stroke:#000000,stroke-width:2px,color:#000000
    classDef d1Data fill:#FFF3E0,stroke:#000000,stroke-width:2px,color:#000000
    
    LocalWord["📝 本地单词数据<br>words_for_publish表"]:::localData
    LocalTTS["🔊 本地TTS资产<br>tts_assets表"]:::localData
    
    SubmitRequest["📤 提交请求<br>SubmitWordTTSRequest"]:::workerData
    SubmitResponse["📥 提交响应<br>SubmitWordTTSResponse"]:::workerData
    
    D1Task["💾 D1任务记录<br>tts_tasks表"]:::d1Data
    
    AzureRequest["🔊 Azure请求<br>SSML"]:::azureData
    AzureResponse["🎵 Azure响应<br>音频数据"]:::azureData
    
    R2Upload["📦 R2上传<br>音频文件"]:::r2Data
    R2URL["🔗 R2 URL<br>CDN访问链接"]:::r2Data
    
    LocalWord -->|"转换"| SubmitRequest
    LocalTTS -->|"提供数据"| SubmitRequest
    
    SubmitRequest -->|"Worker处理"| D1Task
    D1Task -->|"状态更新"| SubmitResponse
    
    D1Task -->|"构建SSML"| AzureRequest
    AzureRequest -->|"Azure TTS API"| AzureResponse
    
    AzureResponse -->|"上传"| R2Upload
    R2Upload -->|"生成URL"| R2URL
    
    R2URL -->|"更新任务"| D1Task
    
    subgraph "示例数据"
        LocalWordExample["words_for_publish:<br>{<br>  word: 'hello',<br>  ttsStatus: 'pending',<br>  ttsHashList: '[\"a1b2c3d4e5f6789012345678\", ...]'<br>}"]:::localData
        
        LocalTTSExample["tts_assets:<br>{<br>  ttsId: 'a1b2c3d4e5f6789012345678',<br>  originalText: 'hello',<br>  ttsType: 'phonetic_name',<br>  textToSpeak: 'həˈloʊ',<br>  status: 'pending'<br>}"]:::localData
        
        SubmitRequestExample["SubmitWordTTSRequest:<br>{<br>  word: 'hello',<br>  tasks: [<br>    {<br>      ttsId: 'a1b2c3d4e5f6789012345678',<br>      text: 'həˈloʊ',<br>      type: 'phonetic_name'<br>    },<br>    ...<br>  ]<br>}"]:::workerData
        
        D1TaskExample["tts_tasks:<br>{<br>  ttsId: 'a1b2c3d4e5f6789012345678',<br>  text: 'həˈloʊ',<br>  type: 'phonetic_name',<br>  status: 'pending',<br>  createdAt: '2025-07-15T10:00:00.000Z'<br>}"]:::d1Data
        
        AzureRequestExample["SSML:<br><speak version=\"1.0\" xmlns=\"http://www.w3.org/2001/10/synthesis\" xml:lang=\"en-US\"><br>  <voice name=\"en-US-AndrewNeural\"><br>    <prosody rate=\"0.9\" pitch=\"0%\">həˈloʊ</prosody><br>  </voice><br></speak>"]:::azureData
        
        R2URLExample["CDN URL:<br>https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav"]:::r2Data
        
        D1TaskCompletedExample["tts_tasks (已完成):<br>{<br>  ttsId: 'a1b2c3d4e5f6789012345678',<br>  text: 'həˈloʊ',<br>  type: 'phonetic_name',<br>  status: 'completed',<br>  audioUrl: 'https://audio.senseword.app/a1b2c3d4e5f6789012345678.wav',<br>  completedAt: '2025-07-15T10:01:30.000Z'<br>}"]:::d1Data
    end
```

### 数据转换说明

1. **本地数据 → 提交请求**
   - 从words_for_publish表读取单词信息
   - 从tts_assets表读取TTS任务详情
   - 构建SubmitWordTTSRequest对象

2. **Worker处理 → D1数据库**
   - 将任务存储到tts_tasks表
   - 初始状态为pending

3. **D1数据 → Azure请求**
   - 构建SSML格式的请求
   - 根据任务类型选择适当的语音

4. **Azure响应 → R2存储**
   - 将音频数据上传到R2存储
   - 生成CDN访问URL

5. **最终状态更新**
   - 更新任务状态为completed
   - 添加audioUrl和completedAt信息

---

## 并发控制与队列管理

下图展示了系统如何管理并发请求和任务队列，确保系统稳定运行。

```mermaid
flowchart TB
    classDef queue fill:#E0F7FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef limiter fill:#FFECB3,stroke:#000000,stroke-width:3px,color:#000000
    classDef worker fill:#F3E5F5,stroke:#000000,stroke-width:2px,color:#000000
    classDef monitor fill:#E8F5E9,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "请求队列管理"
        IncomingRequests["📥 传入请求<br>(Python脚本)"]:::queue
        RequestQueue["⏳ 请求队列<br>等待处理"]:::queue
        ProcessingSlots["🔄 处理槽位<br>(最大50个)"]:::limiter
    end

    subgraph "速率限制器"
        RateLimiter["🎛️ 速率限制器<br>50-100 TPS"]:::limiter
        ConcurrencyControl["⚡ 并发控制<br>最大50并发"]:::limiter
        TPSMonitor["📊 TPS监控<br>实时统计"]:::monitor
    end

    subgraph "任务处理器"
        TaskProcessor1["🔧 处理器1<br>Azure TTS调用"]:::worker
        TaskProcessor2["🔧 处理器2<br>Azure TTS调用"]:::worker
        TaskProcessor3["🔧 处理器N<br>Azure TTS调用"]:::worker
    end

    subgraph "监控与调整"
        ErrorMonitor["❌ 错误监控<br>错误率统计"]:::monitor
        AutoAdjuster["🔧 自动调整器<br>TPS动态调整"]:::monitor
        AlertSystem["🚨 告警系统<br>异常通知"]:::monitor
    end

    IncomingRequests --> RequestQueue
    RequestQueue --> RateLimiter
    RateLimiter --> ConcurrencyControl
    ConcurrencyControl --> ProcessingSlots

    ProcessingSlots --> TaskProcessor1
    ProcessingSlots --> TaskProcessor2
    ProcessingSlots --> TaskProcessor3

    TaskProcessor1 --> TPSMonitor
    TaskProcessor2 --> TPSMonitor
    TaskProcessor3 --> TPSMonitor

    TPSMonitor --> ErrorMonitor
    ErrorMonitor --> AutoAdjuster
    AutoAdjuster --> RateLimiter

    ErrorMonitor --> AlertSystem

    %% 添加流量控制说明
    RequestQueue -.->|"队列满时"| IncomingRequests
    ProcessingSlots -.->|"槽位满时"| RequestQueue

    %% 添加监控反馈
    TaskProcessor1 -.->|"成功/失败统计"| ErrorMonitor
    TaskProcessor2 -.->|"成功/失败统计"| ErrorMonitor
    TaskProcessor3 -.->|"成功/失败统计"| ErrorMonitor
```

### 并发控制机制说明

1. **请求队列管理**
   - 传入请求首先进入队列等待
   - 队列满时新请求被拒绝或延迟
   - 按FIFO顺序处理请求

2. **速率限制**
   - 默认50 TPS，可动态调整到100 TPS
   - 基于滑动窗口算法
   - 超过限制的请求进入等待队列

3. **并发控制**
   - 最大50个并发处理槽位
   - 每个槽位处理一个TTS任务
   - 槽位释放后立即处理下一个任务

4. **监控与调整**
   - 实时监控TPS和错误率
   - 根据系统性能自动调整TPS
   - 异常情况触发告警

---

## Azure TTS多密钥轮换机制

下图展示了系统如何管理多个Azure TTS密钥，实现负载均衡和故障转移。

```mermaid
stateDiagram-v2
    [*] --> 密钥管理器

    state 密钥管理器 {
        [*] --> Key1

        state "Azure Key 1" as Key1
        state "Azure Key 2" as Key2
        state "Azure Key 3" as Key3
        state "Azure Key N" as KeyN

        Key1 --> Key2 : 轮换策略<br>请求完成
        Key2 --> Key3 : 轮换策略<br>请求完成
        Key3 --> KeyN : 轮换策略<br>请求完成
        KeyN --> Key1 : 轮换策略<br>请求完成

        Key1 --> Key2 : 密钥失败<br>自动切换
        Key2 --> Key3 : 密钥失败<br>自动切换
        Key3 --> Key1 : 密钥失败<br>自动切换
    }

    state 请求处理 {
        [*] --> 选择密钥
        选择密钥 --> 发送请求
        发送请求 --> 检查响应

        检查响应 --> 请求成功 : 200 OK
        检查响应 --> 请求失败 : 4xx/5xx

        请求成功 --> [*]
        请求失败 --> 重试判断

        重试判断 --> 切换密钥 : 重试次数 < 3
        重试判断 --> 最终失败 : 重试次数 >= 3

        切换密钥 --> 选择密钥
        最终失败 --> [*]
    }

    密钥管理器 --> 请求处理 : 提供密钥
    请求处理 --> 密钥管理器 : 报告状态

    state 监控统计 {
        [*] --> 密钥使用统计
        密钥使用统计 --> 成功率统计
        成功率统计 --> 配额监控
        配额监控 --> 性能分析
        性能分析 --> [*]
    }

    请求处理 --> 监控统计 : 统计数据
```

### 多密钥轮换机制说明

1. **轮换策略**
   - 按顺序轮换使用Azure密钥
   - 每个请求使用不同的密钥
   - 实现负载均衡

2. **故障转移**
   - 密钥失败时自动切换到下一个
   - 最多重试3次
   - 记录失败密钥状态

3. **监控统计**
   - 统计每个密钥的使用次数
   - 监控成功率和响应时间
   - 跟踪配额使用情况

---

## 错误处理与恢复流程

下图展示了系统如何处理各种错误情况并进行恢复。

```mermaid
flowchart TD
    classDef error fill:#FFCDD2,stroke:#000000,stroke-width:2px,color:#000000
    classDef recovery fill:#C8E6C9,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#E0F7FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef action fill:#FFECB3,stroke:#000000,stroke-width:2px,color:#000000

    Start([任务处理开始])

    subgraph "错误检测"
        NetworkError["🌐 网络错误<br>连接超时/断开"]:::error
        AzureError["🔊 Azure API错误<br>4xx/5xx响应"]:::error
        R2Error["📦 R2存储错误<br>上传失败"]:::error
        DatabaseError["🗄️ 数据库错误<br>查询/更新失败"]:::error
        ValidationError["✅ 数据验证错误<br>格式不正确"]:::error
    end

    subgraph "错误分类"
        RetryableError{可重试错误?}:::decision
        CriticalError{严重错误?}:::decision
        TemporaryError{临时错误?}:::decision
    end

    subgraph "恢复策略"
        RetryWithBackoff["🔄 指数退避重试<br>1s → 2s → 4s"]:::recovery
        SwitchAzureKey["🔑 切换Azure密钥<br>使用下一个可用密钥"]:::recovery
        ReduceTPS["📉 降低TPS<br>减少系统负载"]:::recovery
        MarkTaskFailed["❌ 标记任务失败<br>记录错误信息"]:::action
        TriggerAlert["🚨 触发告警<br>通知管理员"]:::action
        SystemPause["⏸️ 系统暂停<br>等待人工干预"]:::action
    end

    Start --> NetworkError
    Start --> AzureError
    Start --> R2Error
    Start --> DatabaseError
    Start --> ValidationError

    NetworkError --> RetryableError
    AzureError --> RetryableError
    R2Error --> RetryableError
    DatabaseError --> CriticalError
    ValidationError --> TemporaryError

    RetryableError -->|是| RetryWithBackoff
    RetryableError -->|否| MarkTaskFailed

    CriticalError -->|是| SystemPause
    CriticalError -->|否| TriggerAlert

    TemporaryError -->|是| SwitchAzureKey
    TemporaryError -->|否| ReduceTPS

    RetryWithBackoff -->|重试成功| Start
    RetryWithBackoff -->|重试失败| MarkTaskFailed

    SwitchAzureKey --> RetryWithBackoff
    ReduceTPS --> RetryWithBackoff

    MarkTaskFailed --> TriggerAlert
    TriggerAlert --> End([处理结束])
    SystemPause --> End

    %% 添加错误统计
    MarkTaskFailed -.->|"更新错误统计"| ErrorStats["📊 错误统计<br>用于TPS调整"]:::recovery
    ErrorStats -.->|"触发自动调整"| ReduceTPS
```

### 错误处理机制说明

1. **错误分类**
   - 可重试错误: 网络超时、临时服务不可用
   - 严重错误: 数据库连接失败、配置错误
   - 临时错误: API限流、密钥配额不足

2. **恢复策略**
   - 指数退避重试: 1秒 → 2秒 → 4秒
   - 密钥切换: 自动使用下一个可用密钥
   - TPS降级: 根据错误率自动降低处理速度

3. **监控告警**
   - 实时监控错误率和类型
   - 超过阈值时触发告警
   - 严重错误时暂停系统

---

## 系统性能监控仪表板

下图展示了系统的关键性能指标和监控维度。

```mermaid
graph TB
    classDef metric fill:#E3F2FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef alert fill:#FFCDD2,stroke:#000000,stroke-width:2px,color:#000000
    classDef healthy fill:#C8E6C9,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "实时性能指标"
        TPS["📊 当前TPS<br>实时处理速度"]:::metric
        Concurrency["⚡ 并发数<br>活跃处理任务"]:::metric
        QueueLength["📋 队列长度<br>等待处理任务"]:::metric
        ResponseTime["⏱️ 响应时间<br>平均处理耗时"]:::metric
    end

    subgraph "成功率统计"
        OverallSuccess["✅ 总体成功率<br>95.2%"]:::healthy
        AzureSuccess["🔊 Azure成功率<br>98.1%"]:::healthy
        R2Success["📦 R2成功率<br>99.5%"]:::healthy
        DatabaseSuccess["🗄️ 数据库成功率<br>99.9%"]:::healthy
    end

    subgraph "错误监控"
        ErrorRate["❌ 错误率<br>4.8%"]:::alert
        ErrorTypes["🔍 错误类型分布<br>网络:60% API:30% 其他:10%"]:::metric
        FailedTasks["📉 失败任务数<br>1,234个"]:::alert
        RetryRate["🔄 重试成功率<br>78.5%"]:::metric
    end

    subgraph "资源使用"
        CPUUsage["💻 CPU使用率<br>45%"]:::healthy
        MemoryUsage["🧠 内存使用率<br>62%"]:::healthy
        DatabaseConnections["🔗 数据库连接<br>15/50"]:::healthy
        AzureQuota["📊 Azure配额<br>78%已使用"]:::metric
    end

    subgraph "业务指标"
        ProcessedWords["📝 已处理单词<br>12,456个"]:::metric
        CompletedTasks["✅ 已完成任务<br>298,734个"]:::metric
        PendingTasks["⏳ 待处理任务<br>5,678个"]:::metric
        AudioGenerated["🎵 生成音频<br>1.2GB"]:::metric
    end

    subgraph "告警规则"
        TPSAlert["🚨 TPS < 30<br>性能告警"]:::alert
        ErrorAlert["🚨 错误率 > 10%<br>质量告警"]:::alert
        QueueAlert["🚨 队列长度 > 1000<br>积压告警"]:::alert
        QuotaAlert["🚨 配额 > 90%<br>资源告警"]:::alert
    end

    %% 连接关系
    TPS -.->|"低于阈值"| TPSAlert
    ErrorRate -.->|"超过阈值"| ErrorAlert
    QueueLength -.->|"超过阈值"| QueueAlert
    AzureQuota -.->|"超过阈值"| QuotaAlert

    %% 健康状态指示
    OverallSuccess -.->|"影响"| TPS
    AzureSuccess -.->|"影响"| ErrorRate
    R2Success -.->|"影响"| ErrorRate
    DatabaseSuccess -.->|"影响"| ErrorRate
```

### 监控指标说明

1. **实时性能指标**
   - 当前TPS: 实时处理速度
   - 并发数: 同时处理的任务数
   - 队列长度: 等待处理的任务数
   - 响应时间: 平均处理耗时

2. **成功率统计**
   - 总体成功率: 端到端处理成功率
   - 各组件成功率: Azure、R2、数据库
   - 用于评估系统健康状态

3. **错误监控**
   - 错误率: 失败任务占比
   - 错误类型分布: 便于定位问题
   - 重试成功率: 恢复机制效果

4. **告警规则**
   - 性能告警: TPS过低
   - 质量告警: 错误率过高
   - 积压告警: 队列过长
   - 资源告警: 配额不足

---

## 总结

通过以上详细的可视化图表，我们可以清楚地看到SenseWord在线实时TTS处理系统的工作原理：

### 🎯 核心优势

1. **简化架构**: 从复杂的批处理模式简化为直观的实时处理模式
2. **状态解耦**: 单词状态与任务状态独立管理，避免复杂的状态流转
3. **自动恢复**: 通过优先级重试机制保证最终一致性
4. **智能限流**: 自适应TPS调整，确保系统稳定运行
5. **多重保障**: 多密钥轮换、错误重试、监控告警等机制

### 🚀 系统特点

- **50-100 TPS**: 保守稳定的处理能力
- **幂等性**: ttsId + text + type + status的完美幂等结构
- **最终一致性**: 失败任务通过优先级队列最终被处理
- **实时监控**: 全方位的性能和错误监控

这个系统完美体现了"简单即美"的工程哲学，在保证功能完整性的同时，大幅降低了系统复杂度和维护成本。
