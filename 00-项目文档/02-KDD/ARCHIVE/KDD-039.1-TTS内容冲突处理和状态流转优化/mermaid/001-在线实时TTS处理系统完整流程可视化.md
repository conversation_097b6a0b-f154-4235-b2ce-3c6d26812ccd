# 在线实时TTS处理系统完整流程可视化

## 📋 概述

本文档通过详细的Mermaid图表和真实数据示例，完整展示SenseWord在线实时TTS处理系统的工作原理，包括数据流转、状态管理、系统架构等核心概念。

---

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "💻 本地环境"
        A["📊 本地SQLite数据库<br/>words_for_publish + tts_assets"]
        B["🐍 Python提交脚本<br/>01_submit_tts_tasks.py"]
    end
    
    subgraph "☁️ Cloudflare Worker"
        C["🌐 HTTP端点<br/>/submit"]
        D["⚙️ 任务管理服务<br/>TaskManager"]
        E["🎵 实时TTS服务<br/>RealtimeTTS"]
        F["🔄 并发控制器<br/>RateLimiter (50-100 TPS)"]
    end
    
    subgraph "🔵 Azure服务"
        G["🎤 Azure TTS API<br/>实时语音合成"]
    end
    
    subgraph "📦 存储服务"
        H["💾 D1数据库<br/>tts_tasks表"]
        I["🗄️ R2存储<br/>音频文件CDN"]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    E --> H
    G --> I
    
    classDef localStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef workerStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef azureStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B localStyle
    class C,D,E,F workerStyle
    class G azureStyle
    class H,I storageStyle
```

---

## 📊 核心数据结构转化流程

```mermaid
graph TD
    subgraph "🔍 步骤1: 本地数据查询"
        A1["📋 words_for_publish表<br/>word: 'hello'<br/>ttsStatus: 'pending'<br/>ttsHashList: '{...}'"]
        A2["📝 tts_assets表<br/>ttsId: 'a1b2c3d4...'<br/>text: 'hello'<br/>type: 'phonetic_name'<br/>status: 'failed' ⚠️"]
    end
    
    subgraph "🔄 步骤2: 数据转换"
        B1["🐍 WordWithTasks<br/>word: 'hello'<br/>status: 'pending'<br/>tts_tasks: [25个任务]"]
        B2["📤 SubmitWordTTSRequest<br/>word: 'hello'<br/>tasks: [25个简化任务]"]
    end
    
    subgraph "⚙️ 步骤3: Worker处理"
        C1["📥 Worker接收请求<br/>received: 25"]
        C2["💾 批量入库D1<br/>inserted: 23<br/>failed: 2"]
        C3["📤 SubmitWordTTSResponse<br/>success: true<br/>failed_tasks: ['a1b2...', 'b2c3...']"]
    end
    
    subgraph "📈 步骤4: 结果汇总"
        D1["📊 SubmissionResult<br/>words_submitted: 98/100<br/>failed_tasks: [4个ttsId]<br/>submission_rate: 98%"]
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> B2
    B2 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> D1
    
    classDef queryStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef transformStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef resultStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    
    class A1,A2 queryStyle
    class B1,B2 transformStyle
    class C1,C2,C3 processStyle
    class D1 resultStyle
```

---

## 🔄 状态流转图 (核心重点)

```mermaid
stateDiagram-v2
    [*] --> pending_word : 📝 单词创建
    
    state "📋 单词状态" as word_states {
        pending_word : 🟡 pending<br/>等待提交
        submitted_word : 🟢 submitted<br/>已提交完成
    }
    
    state "🎵 TTS任务状态" as task_states {
        pending_task : 🟡 pending<br/>等待处理
        processing_task : 🔵 processing<br/>Worker处理中
        completed_task : 🟢 completed<br/>音频已生成
        failed_task : 🔴 failed<br/>处理失败
    }
    
    pending_word --> submitted_word : ✅ Worker响应成功<br/>(无论是否有个别任务失败)
    pending_word --> pending_word : ❌ Worker完全失败<br/>(网络错误等)
    
    pending_task --> processing_task : 🔄 Worker开始处理
    processing_task --> completed_task : ✅ Azure TTS成功
    processing_task --> failed_task : ❌ Azure TTS失败
    failed_task --> pending_task : 🔄 下次优先重试
    
    note right of submitted_word : 关键设计：单词状态与<br/>个别任务失败解耦
    note right of failed_task : 最终一致性：失败任务<br/>会被优先重试处理
```

---

## ⏱️ 完整时序图 (真实数据示例)

```mermaid
sequenceDiagram
    participant P as 🐍 Python脚本
    participant D as 📊 本地数据库
    participant W as ⚙️ TTS Worker
    participant A as 🎤 Azure TTS
    participant R as 🗄️ R2存储
    participant D1 as 💾 D1数据库
    
    Note over P,D1: 📝 处理单词 "hello" (25个TTS任务)
    
    P->>D: 查询待处理单词
    D->>P: word: "hello", status: "pending"<br/>+ 2个failed任务优先处理
    
    P->>P: 构造请求数据<br/>25个TTS任务
    P->>W: POST /submit<br/>{"word": "hello", "tasks": [25个]}
    
    Note over W: 🔄 Worker处理阶段
    
    W->>D1: 批量入库TTS任务
    D1->>W: 23个成功, 2个失败
    
    W->>P: 响应: {"success": true,<br/>"inserted": 23, "failed_tasks": [2个]}
    
    Note over P: 📊 Python状态更新
    
    P->>D: 更新单词状态为 "submitted"
    P->>D: 标记2个ttsId为 "failed"
    
    Note over W,R: 🎵 后台TTS处理 (异步)
    
    loop 23个成功入库的任务
        W->>D1: 查询pending任务
        W->>A: 调用Azure TTS API
        A->>W: 返回音频数据
        W->>R: 上传音频到R2
        W->>D1: 更新状态为completed
    end
    
    Note over P,D1: ✅ 结果：单词已submitted，23个任务completed，2个任务failed
```

---

## 🔄 失败任务重试流程

```mermaid
graph TD
    subgraph "🔍 第一次提交 (hello单词)"
        A["📝 25个TTS任务提交"]
        B["✅ 23个成功入库"]
        C["❌ 2个失败 (网络超时)"]
        D["🟢 单词状态: submitted"]
        E["🔴 失败任务标记: failed"]
    end
    
    subgraph "🔍 第二次提交 (优先处理)"
        F["🔍 查询发现hello有failed任务"]
        G["📤 只提交2个failed任务"]
        H["✅ 2个任务成功入库"]
        I["🎯 最终一致性达成"]
    end
    
    A --> B
    A --> C
    B --> D
    C --> E
    E --> F
    F --> G
    G --> H
    H --> I
    
    classDef firstStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef retryStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef successStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef failedStyle fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B firstStyle
    class F,G,H retryStyle
    class D,I successStyle
    class C,E failedStyle
```

---

## 🎯 并发处理与TPS控制

```mermaid
graph TD
    subgraph "📥 任务接收层"
        A["🐍 Python脚本<br/>每批10个单词<br/>~250个TTS任务"]
        B["⚙️ Worker接收<br/>批量入库D1"]
    end

    subgraph "🔄 并发控制层 (50-100 TPS)"
        C["📋 任务队列<br/>pending状态"]
        D["🎛️ 速率限制器<br/>50 TPS配置"]
        E["⚡ 并发处理器<br/>50个并发请求"]
    end

    subgraph "🎤 Azure处理层"
        F["🔵 Azure TTS API<br/>实时语音合成"]
        G["📊 处理统计<br/>成功率: 99.5%"]
    end

    subgraph "💾 存储层"
        H["🗄️ R2音频存储<br/>CDN分发"]
        I["💾 D1状态更新<br/>completed状态"]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I

    classDef inputStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef controlStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class A,B inputStyle
    class C,D,E controlStyle
    class F,G processStyle
    class H,I storageStyle
```

---

## 📊 数据库查询优先级策略

```mermaid
graph TD
    subgraph "🔍 查询策略 (确保最终一致性)"
        A["📋 查询条件<br/>ttsStatus = 'pending'<br/>OR 包含failed任务的单词"]
        B["📈 优先级排序<br/>1. 包含failed任务 (优先级0)<br/>2. 普通pending单词 (优先级1)"]
        C["🎯 查询结果<br/>优先处理failed任务"]
    end

    subgraph "📝 实际查询示例"
        D["🔴 hello (包含2个failed任务)<br/>优先级: 0"]
        E["🟡 world (全新pending单词)<br/>优先级: 1"]
        F["🟡 apple (全新pending单词)<br/>优先级: 1"]
    end

    subgraph "📤 提交顺序"
        G["1️⃣ 首先处理: hello<br/>只提交2个failed任务"]
        H["2️⃣ 然后处理: world<br/>提交25个新任务"]
        I["3️⃣ 最后处理: apple<br/>提交25个新任务"]
    end

    A --> B
    B --> C
    C --> D
    D --> G
    E --> H
    F --> I

    classDef strategyStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef exampleStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef orderStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef priorityStyle fill:#FFCCCC,stroke:#000000,stroke-width:3px,color:#000000

    class A,B,C strategyStyle
    class E,F exampleStyle
    class G,H,I orderStyle
    class D priorityStyle
```

---

## 🎵 Azure TTS处理详细流程

```mermaid
sequenceDiagram
    participant W as ⚙️ Worker
    participant A as 🎤 Azure TTS
    participant R as 🗄️ R2存储
    participant D as 💾 D1数据库

    Note over W,D: 🎵 单个TTS任务处理流程

    W->>D: 查询pending任务<br/>ttsId: "a1b2c3d4..."
    D->>W: text: "hello", type: "phonetic_name"

    W->>W: 构造SSML<br/>&lt;speak&gt;&lt;voice name="en-US-AndrewNeural"&gt;<br/>&lt;prosody rate="0.9"&gt;hello&lt;/prosody&gt;&lt;/voice&gt;&lt;/speak&gt;

    W->>A: POST Azure TTS API<br/>Content-Type: application/ssml+xml

    Note over A: 🎤 Azure处理 (100-500ms)

    A->>W: 返回音频数据<br/>ArrayBuffer (WAV格式, ~45KB)

    W->>R: 上传音频文件<br/>Key: "a1b2c3d4....wav"
    R->>W: 返回CDN URL<br/>"https://audio.senseword.app/a1b2c3d4....wav"

    W->>D: 更新任务状态<br/>status: "completed"<br/>audioUrl: CDN链接

    Note over W,D: ✅ 任务完成，用户可访问音频
```

---

## 📈 系统性能与容量规划

```mermaid
xychart-beta
    title "TTS处理性能对比 (130万任务)"
    x-axis ["50 TPS配置", "100 TPS配置", "批处理模式"]
    y-axis "处理时间 (小时)" 0 --> 8
    bar [7.2, 3.6, 2.2]
```

```mermaid
pie title 系统资源使用分布
    "Azure TTS调用" : 60
    "R2存储上传" : 25
    "数据库操作" : 10
    "网络传输" : 5
```

---

## 🎯 核心设计优势总结

```mermaid
mindmap
  root((🎯 在线实时TTS<br/>核心优势))
    🏗️ 架构简洁
      单词状态解耦
      幂等性保证
      最终一致性
    ⚡ 性能稳定
      50-100 TPS
      并发控制
      资源优化
    🔧 维护简单
      状态清晰
      错误隔离
      调试容易
    💰 成本可控
      按需处理
      避免重复
      资源节约
```
