# KDD-029: 语言切换功能实现

## 项目概述

### 功能描述
在设置页面中添加语言切换功能，使用现有的 LanguageCode 模型，用户选择的语言将影响单词查询API的参数，而不改变界面的其他部分。

### 核心目标
- 在用户设置中添加语言偏好存储
- 创建语言选择界面组件
- 修改设置页面的语言设置区域
- 确保单词查询使用用户选择的语言

### 技术栈
- SwiftUI (界面实现)
- UserDefaults (设置持久化)
- 现有的 LanguageCode 枚举
- 现有的 SettingsService 架构

## 数据结构设计

### 关键帧数据结构

#### 关键帧 1: 用户设置扩展 (UserSettings+Language)
```swift
struct UserSettings: Codable {
    var autoPlayAudio: Bool
    var hapticFeedback: Bool
    var dailyNotification: Bool
    var phoneticPreference: PhoneticPreference
    var subscriptionStatus: SubscriptionStatus
    var userInfo: SenseWordUserInfo?
    
    // 新增：用户偏好语言
    var preferredLanguage: LanguageCode
    
    static let `default` = UserSettings(
        autoPlayAudio: true,
        hapticFeedback: true,
        dailyNotification: false,
        phoneticPreference: .american,
        subscriptionStatus: .free,
        userInfo: nil,
        preferredLanguage: .chinese  // 默认中文
    )
}
```

#### 关键帧 2: 语言选择请求 (LanguageSelectionRequest)
```swift
struct LanguageSelectionRequest {
    let currentLanguage: LanguageCode
    let availableLanguages: [LanguageCode]
}
```

#### 关键帧 3: 语言选择响应 (LanguageSelectionResponse)
```swift
struct LanguageSelectionResponse {
    let selectedLanguage: LanguageCode
    let success: Bool
    let error: String?
}
```

#### 关键帧 4: 语言设置更新请求 (LanguageUpdateRequest)
```swift
struct LanguageUpdateRequest {
    let newLanguage: LanguageCode
    let userId: String?
}
```

#### 关键帧 5: 语言设置更新响应 (LanguageUpdateResponse)
```swift
struct LanguageUpdateResponse {
    let success: Bool
    let updatedSettings: UserSettings
    let error: String?
}
```

## 函数契约补间链

### [FC-01] 用户设置模型扩展
**输入 >>>>>**
```swift
// 现有的 UserSettings 结构
struct UserSettings: Codable {
    var autoPlayAudio: Bool
    var hapticFeedback: Bool
    var dailyNotification: Bool
    var phoneticPreference: PhoneticPreference
    var subscriptionStatus: SubscriptionStatus
    var userInfo: SenseWordUserInfo?
}
```

**输出 <<<<<**
```swift
// 扩展后的 UserSettings 结构
struct UserSettings: Codable {
    var autoPlayAudio: Bool
    var hapticFeedback: Bool
    var dailyNotification: Bool
    var phoneticPreference: PhoneticPreference
    var subscriptionStatus: SubscriptionStatus
    var userInfo: SenseWordUserInfo?
    var preferredLanguage: LanguageCode  // 新增字段
}
```

**实现逻辑**：
1. 在 UserSettings 结构中添加 preferredLanguage 字段
2. 更新默认设置，设置默认语言为中文
3. 确保 Codable 兼容性，支持旧版本设置的迁移

### [FC-02] 设置服务语言管理扩展
**输入 >>>>>**
```swift
LanguageUpdateRequest {
    newLanguage: LanguageCode.spanish
    userId: nil
}
```

**输出 <<<<<**
```swift
LanguageUpdateResponse {
    success: true
    updatedSettings: UserSettings(preferredLanguage: .spanish, ...)
    error: nil
}
```

**实现逻辑**：
1. 在 SettingsService 中添加 updatePreferredLanguage 方法
2. 验证语言代码的有效性
3. 更新用户设置并持久化
4. 发布设置变更通知

### [FC-03] 语言选择界面组件
**输入 >>>>>**
```swift
LanguageSelectionRequest {
    currentLanguage: .chinese
    availableLanguages: LanguageCode.allCases
}
```

**输出 <<<<<**
```swift
LanguageSelectionResponse {
    selectedLanguage: .spanish
    success: true
    error: nil
}
```

**实现逻辑**：
1. 创建 LanguageSelectionView 组件
2. 显示所有可用语言的列表
3. 高亮当前选择的语言
4. 支持搜索和筛选功能
5. 处理用户选择并返回结果

### [FC-04] 设置页面语言区域更新
**输入 >>>>>**
```swift
// 当前设置页面的语言区域（占位符状态）
SettingsNavigationRow(
    title: "界面语言",
    subtitle: "简体中文",
    action: { print("打开语言选择") }
)
```

**输出 <<<<<**
```swift
// 更新后的语言设置区域
SettingsNavigationRow(
    title: "单词查询语言",
    subtitle: viewModel.userSettings.preferredLanguage.displayName,
    action: { viewModel.showLanguageSelection = true }
)
```

**实现逻辑**：
1. 修改设置页面的语言设置标题和描述
2. 动态显示当前选择的语言
3. 添加语言选择界面的展示逻辑
4. 处理语言选择结果的回调

### [FC-05] 单词查询语言参数集成
**输入 >>>>>**
```swift
// 当前的单词查询调用（使用默认语言）
wordAPIAdapter.getWord(word, language: .chinese)
```

**输出 <<<<<**
```swift
// 使用用户设置的语言进行查询
let userLanguage = SettingsService.shared.currentSettings.preferredLanguage
wordAPIAdapter.getWord(word, language: userLanguage)
```

**实现逻辑**：
1. 修改 SearchService 中的单词查询逻辑
2. 从 SettingsService 获取用户的语言偏好
3. 将用户语言传递给 API 调用
4. 确保所有单词查询都使用统一的语言设置

## 文件结构

### 新增文件
```
iOS/SensewordApp/Views/Settings/
├── LanguageSelectionView.swift          # 语言选择界面
└── Components/
    └── LanguageSelectionRow.swift       # 语言选择行组件

iOS/SensewordApp/ViewModels/
└── LanguageSelectionViewModel.swift     # 语言选择视图模型
```

### 修改文件
```
iOS/SensewordApp/Models/Settings/
└── SettingsModels.swift                 # 添加语言相关模型

iOS/SensewordApp/Services/Settings/
└── SettingsService.swift                # 添加语言设置方法

iOS/SensewordApp/Views/Settings/
└── SettingsView.swift                   # 更新语言设置区域

iOS/SensewordApp/ViewModels/
└── SettingsViewModel.swift              # 添加语言选择逻辑

iOS/SensewordApp/Services/Business/
└── SearchService.swift                  # 使用用户语言设置
```

## 依赖分析

### 内部依赖
- `LanguageCode` 枚举 (SharedModels.swift)
- `UserSettings` 结构 (SettingsModels.swift)
- `SettingsService` 类 (SettingsService.swift)
- `WordAPIAdapter` 协议 (WordAPIAdapter.swift)

### 外部依赖
- SwiftUI 框架
- Foundation 框架
- UserDefaults 存储

## Commit 规划

### Commit 1: feat(settings): 添加用户语言偏好设置模型
- 扩展 UserSettings 结构添加 preferredLanguage 字段
- 更新默认设置和 Codable 实现
- 添加语言相关的请求/响应模型

### Commit 2: feat(settings): 扩展设置服务支持语言管理
- 在 SettingsService 中添加语言更新方法
- 实现语言设置的验证和持久化
- 添加语言设置的获取方法

### Commit 3: feat(ui): 实现语言选择界面组件
- 创建 LanguageSelectionView 和相关组件
- 实现语言列表显示和选择逻辑
- 添加搜索和筛选功能

### Commit 4: feat(settings): 更新设置页面语言设置区域
- 修改设置页面的语言设置显示
- 集成语言选择界面
- 处理语言选择的回调逻辑

### Commit 5: feat(search): 集成用户语言设置到单词查询
- 修改 SearchService 使用用户语言偏好
- 确保所有单词查询使用统一语言
- 添加语言设置变更的响应逻辑

## 测试策略

### 单元测试
- UserSettings 语言字段的编码/解码测试
- SettingsService 语言更新方法测试
- 语言选择逻辑的验证测试

### 集成测试
- 语言设置变更后的API调用测试
- 设置持久化和恢复测试
- 界面状态同步测试

### 用户体验测试
- 语言选择界面的交互测试
- 设置页面的显示更新测试
- 单词查询语言切换的效果测试

## 风险评估

### 技术风险
- **设置迁移风险**: 现有用户的设置可能不包含语言字段
- **API兼容性**: 确保所有语言代码都被后端支持
- **性能影响**: 语言选择界面的渲染性能

### 缓解措施
- 实现设置迁移逻辑，为旧设置提供默认语言
- 验证语言代码与后端API的兼容性
- 优化语言列表的渲染性能

## 上下文文件

### 核心文件
```
iOS/SensewordApp/Models/Shared/SharedModels.swift
iOS/SensewordApp/Models/Settings/SettingsModels.swift
iOS/SensewordApp/Services/Settings/SettingsService.swift
iOS/SensewordApp/Views/Settings/SettingsView.swift
iOS/SensewordApp/ViewModels/SettingsViewModel.swift
iOS/SensewordApp/Services/Business/SearchService.swift
iOS/SensewordApp/Services/Adapters/WordAPIAdapter.swift
```

### 参考文件
```
iOS/SensewordApp/Views/Settings/Components/SettingsCard.swift
iOS/SensewordApp/Views/Settings/Components/SettingsNavigationRow.swift
```
