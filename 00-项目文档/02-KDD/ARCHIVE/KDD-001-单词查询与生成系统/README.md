# SenseWord 单词查询与生成系统 📚

## 📋 项目概述

SenseWord单词查询与生成系统是一个基于AI驱动的英语单词学习平台，采用Cloudflare Workers + D1数据库 + Gemini AI的现代化架构。系统支持20种目标语言，提供高质量的单词定义、用法示例和文化背景解释，帮助非英语母语者深度理解英语单词。

### 🎯 核心特性
- **AI驱动内容生成**: 集成Gemini 2.5 Flash模型，生成深度、情感化的单词解释
- **多语言支持**: 支持中文、日语、韩语、法语、德语等20种语言
- **缓存优先策略**: 数据库查询优先，按需AI生成，确保高性能
- **完整错误处理**: 统一错误码体系，覆盖网络、AI生成、数据库等各种场景
- **用户反馈系统**: 支持用户对单词解释进行评价，持续优化内容质量

### 🏗️ 技术架构
- **后端**: Cloudflare Workers (TypeScript)
- **数据库**: Cloudflare D1 (SQLite)
- **AI服务**: Google Gemini 2.5 Flash
- **前端**: iOS Swift (SharedModels Package)
- **开发方法**: KDD关键帧驱动开发

## 🔧 核心能力、接口与数据契约

### 后端核心能力 (Backend Core Capabilities)
1. **单词查询与生成服务**: 基于缓存优先策略的智能单词查询
2. **AI内容生成服务**: 使用Gemini AI生成高质量教学内容，支持候选词评估
3. **数据库管理服务**: 高性能的单词定义存储和检索，支持增量同步
4. **用户反馈处理服务**: 收集和处理用户对内容质量的反馈
5. **提示词预处理服务**: 多语言提示词模板处理和占位符替换
6. **响应格式化服务**: 统一的API响应格式标准化
7. **搜索优化服务**: 基于coreDefinition字段的快速搜索建议
8. **每日一词集成**: 支持候选词标记和选举功能

### 前端接口事务 (Frontend Interface Transactions)
1. **fetchWord()**: 查询指定单词的完整定义和解释
2. **submitFeedback()**: 提交用户对单词解释的反馈评价
3. **handleRetry()**: 网络失败时的指数退避重试机制
4. **cacheManagement()**: 本地缓存同步和离线数据管理

### 核心数据结构 (DTO) 定义

```typescript
// 单词查询请求
interface WordQueryRequest {
  word: string;                    // 英语单词，如 "progressive"
  language: SupportedLanguage;     // 目标语言枚举
}

// 单词定义响应
interface WordDefinitionResponse {
  word: string;
  metadata: {
    wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare';
    relatedConcepts: string[];
    isWordOfTheDayCandidate?: boolean; // [新增] 每日一词候选标记
  };
  content: {
    difficulty: string;            // CEFR等级 "A1"-"C2"
    phoneticSymbols: Array<{
      type: 'BrE' | 'NAmE';
      symbol: string;
    }>;
    coreDefinition: string;
    contextualExplanation: {
      nativeSpeakerIntent: string;
      emotionalResonance: string;
      vividImagery: string;
      etymologicalEssence: string;
    };
    usageExamples: Array<{
      category: string;
      examples: Array<{
        english: string;
        translation: string;
        phraseBreakdown: Array<{
          phrase: string;
          translation: string;
        }>;
      }>;
    }>;
    usageScenarios: Array<{
      category: string;
      relevance: string;
      context: string;
    }>;
    collocations: Array<{
      type: string;
      pattern: string;
      examples: Array<{
        collocation: string;
        translation: string;
      }>;
    }>;
    usageNotes: Array<{
      aspect: string;
      explanation: string;
      examples: Array<{
        sentence: string;
        translation: string;
      }>;
    }>;
    synonyms: Array<{
      word: string;
      explanation: string;
      examples: Array<{
        sentence: string;
        translation: string;
      }>;
    }>;
  };
}

// [新增] 数据库记录接口 (反映最新表结构)
interface WordDefinitionRecord {
  sync_id: number;                 // [新增] 增量同步ID
  word: string;
  language: string;
  contentJson: string;             // JSON字符串形式的完整内容
  coreDefinition: string;          // [新增] 核心定义提取字段
  feedbackScore: number;           // 用户反馈评分
  isHumanReviewed: number;         // 0或1，是否人工审核
  ttsStatus: string;               // TTS状态
  difficulty: string;              // CEFR等级
  frequency: string;               // 词频等级
  relatedConcepts: string;         // JSON字符串形式的相关概念数组
  isWordOfTheDayCandidate: number; // [新增] 每日一词候选标记 (0或1)
  promptVersion: string;           // 提示词版本
  createdAt: string;              // ISO时间戳
  updatedAt: string;              // ISO时间戳
}

// 用户反馈请求
interface FeedbackRequest {
  word: string;                    // 要反馈的单词
  language: string;               // 语言代码
  action: 'like' | 'dislike';    // 反馈动作
}

// 用户反馈响应
interface FeedbackSuccessResponse {
  success: true;
  newScore: number;              // 更新后的反馈分数
  message: string;              // 成功消息
}

// 错误响应
interface ErrorResponse {
  error: {
    code: 'INVALID_WORD' | 'AI_GENERATION_FAILED' | 'DATABASE_ERROR' | 'AUTHENTICATION_REQUIRED';
    message: string;
    details?: any;
  };
}

// 支持的语言枚举
enum SupportedLanguage {
  english = "en",
  chinese = "zh",
  japanese = "ja",
  korean = "ko",
  german = "de",
  french = "fr",
  spanish = "es",
  russian = "ru",
  arabic = "ar",
  hindi = "hi",
  thai = "th",
  vietnamese = "vi",
  turkish = "tr",
  indonesian = "id",
  italian = "it",
  portuguese = "pt",
  polish = "pl",
  dutch = "nl",
  swedish = "sv",
  danish = "da",
  norwegian = "no",
  finnish = "fi"
}
```

## 🗄️ 数据库结构 (最新版本)

### word_definitions表结构
系统使用Cloudflare D1数据库存储单词定义，表结构经过多次优化：

```sql
-- 最新的word_definitions表结构 (v2.1)
CREATE TABLE word_definitions (
    -- === 增量同步支持 (KDD-009) ===
    sync_id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 增量同步ID，用于本地索引更新

    -- === 核心标识符 ===
    word TEXT NOT NULL,                         -- 英语单词，如 "progressive"
    language TEXT NOT NULL,                     -- 语言代码，如 "zh", "ja"

    -- === AI生成的核心内容 ===
    contentJson TEXT NOT NULL,                  -- JSON字符串形式的完整内容

    -- === 搜索优化字段 (KDD-004) ===
    coreDefinition TEXT,                        -- 核心定义提取字段，避免JSON解析开销

    -- === 系统管理的状态与运营字段 ===
    feedbackScore INTEGER NOT NULL DEFAULT 0,   -- 用户反馈评分 (like +1, dislike -1)
    isHumanReviewed INTEGER NOT NULL DEFAULT 0, -- 是否人工精修 (0=否, 1=是)
    ttsStatus TEXT NOT NULL DEFAULT 'pending',  -- TTS音频生成状态

    -- === AI分析的或系统注入的元数据 ===
    difficulty TEXT,                            -- CEFR难度等级 (A1-C2, 来自AI)
    frequency TEXT,                             -- 词频等级 (High/Medium/Low/Rare, 来自AI)
    relatedConcepts TEXT,                       -- 相关概念 (来自AI, JSON字符串格式)

    -- === 每日一词功能支持 (KDD-010) ===
    isWordOfTheDayCandidate INTEGER DEFAULT 0,  -- 候选词标记字段 (0=否, 1=是)

    -- === 系统元数据 ===
    promptVersion TEXT NOT NULL,                -- 生成时使用的提示词版本
    createdAt TEXT NOT NULL DEFAULT (datetime('now')),  -- 创建时间 (ISO格式)
    updatedAt TEXT NOT NULL DEFAULT (datetime('now')),  -- 更新时间 (ISO格式)

    -- === 唯一约束 ===
    UNIQUE(word, language)
);
```

### 索引优化
```sql
-- 性能优化索引
CREATE INDEX idx_feedback_score ON word_definitions (feedbackScore DESC);
CREATE INDEX idx_is_human_reviewed ON word_definitions (isHumanReviewed);
CREATE INDEX idx_tts_status ON word_definitions (ttsStatus);

-- 搜索功能优化索引 (KDD-004)
CREATE INDEX idx_word_search ON word_definitions (word);
CREATE INDEX idx_core_definition_search ON word_definitions (coreDefinition);

-- 增量同步优化索引 (KDD-009)
CREATE INDEX idx_sync_language ON word_definitions (sync_id, language);
CREATE INDEX idx_language_sync ON word_definitions (language, sync_id);

-- 每日一词功能索引 (KDD-010)
CREATE INDEX idx_isWordOfTheDayCandidate ON word_definitions (isWordOfTheDayCandidate);

-- 复合查询优化索引
CREATE INDEX idx_word_language ON word_definitions (word, language);
```

### 数据库演进历史
- **v1.0**: 基础表结构，支持单词查询和AI生成
- **v1.1**: 添加coreDefinition字段，优化搜索性能 (KDD-004)
- **v2.0**: 添加sync_id字段，支持增量同步 (KDD-009)
- **v2.1**: 添加isWordOfTheDayCandidate字段，支持每日一词功能 (KDD-010)

## 🌐 服务地址

### 生产环境
- **API基础URL**: `https://senseword-api-worker.zhouqi-aaha.workers.dev`
- **数据库**: Cloudflare D1 `senseword-word-db`
- **认证密钥**: `sk-senseword-api-prod-2025-v1`

### 开发环境  
- **API基础URL**: `https://senseword-api-worker-dev.zhouqi-aaha.workers.dev`
- **本地开发**: `http://localhost:8080` (使用 `wrangler dev`)
- **认证密钥**: `sk-senseword-api-dev-2025-v1`

## 📡 API端点列表

### 1. 单词查询/生成 API
- **端点**: `GET /api/v1/word/{word}?lang={lang}`
- **方法**: GET
- **认证**: X-Static-API-Key
- **功能**: 查询或生成指定单词的完整定义

**请求示例**:
```bash
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/progressive?lang=zh"
```

**响应示例**:
```json
{
  "word": "progressive",
  "metadata": {
    "wordFrequency": "Medium",
    "relatedConcepts": ["进步", "发展", "渐进"],
    "isWordOfTheDayCandidate": true
  },
  "content": {
    "difficulty": "B2",
    "phoneticSymbols": [
      {"type": "BrE", "symbol": "/prəˈɡresɪv/"},
      {"type": "NAmE", "symbol": "/prəˈɡresɪv/"}
    ],
    "coreDefinition": "渐进的；进步的；逐步发展的",
    "contextualExplanation": {
      "nativeSpeakerIntent": "表达持续改进和向前发展的概念",
      "emotionalResonance": "积极向上，充满希望的改变",
      "vividImagery": "如同阶梯般一步步向上攀登",
      "etymologicalEssence": "来自拉丁语'progressus'，意为向前行走"
    },
    "usageExamples": [
      {
        "category": "教育领域",
        "examples": [
          {
            "english": "Progressive education emphasizes student-centered learning.",
            "translation": "进步教育强调以学生为中心的学习。",
            "phraseBreakdown": [
              {"phrase": "Progressive education", "translation": "进步教育"},
              {"phrase": "emphasizes", "translation": "强调"},
              {"phrase": "student-centered learning", "translation": "以学生为中心的学习"}
            ]
          }
        ]
      }
    ]
  }
}
```

### 2. 用户反馈 API
- **端点**: `POST /api/v1/feedback`
- **方法**: POST
- **认证**: X-Static-API-Key + X-Dynamic-Key
- **功能**: 提交用户对单词解释的反馈

**请求示例**:
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -H "X-Dynamic-Key: user-device-key" \
  -d '{"word":"progressive","language":"zh","action":"like"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/feedback"
```

**响应示例**:
```json
{
  "success": true,
  "newScore": 6,
  "message": "反馈记录成功。新分数: 6"
}
```

### 3. 提示词测试 API (开发专用)
- **端点**: `GET /api/v1/test-prompt/{word}?lang={lang}`
- **方法**: GET
- **认证**: X-Static-API-Key
- **功能**: 测试提示词占位符替换功能

## 🧪 预设测试数据

### 测试账号信息
- **静态API密钥**: `sk-senseword-api-prod-2025-v1` (生产环境)
- **静态API密钥**: `sk-senseword-api-dev-2025-v1` (开发环境)

### 测试单词列表
- **基础词汇**: `cat`, `dog`, `house`, `book`, `water`
- **中级词汇**: `progressive`, `innovative`, `sustainable`, `comprehensive`
- **高级词汇**: `serendipity`, `ubiquitous`, `ephemeral`, `paradigm`

### 测试语言代码
- **亚洲语言**: `zh` (中文), `ja` (日语), `ko` (韩语)
- **欧洲语言**: `de` (德语), `fr` (法语), `es` (西语)
- **其他语言**: `ru` (俄语), `ar` (阿拉伯语), `hi` (印地语)

## 🔬 测试方法

### 简单测试 (基础功能验证)
```bash
# 1. 测试单词查询
curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/cat?lang=zh"

# 2. 测试用户反馈
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
  -d '{"word":"cat","language":"zh","action":"like"}' \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/feedback"
```

### 中级测试 (多语言验证)
```bash
# 测试不同语言的单词查询
for lang in zh ja ko de fr es; do
  echo "Testing language: $lang"
  curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
    "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/progressive?lang=$lang"
  echo -e "\n---\n"
done
```

### 高级测试 (性能和错误处理)
```bash
# 1. 并发测试
for i in {1..10}; do
  curl -H "X-Static-API-Key: sk-senseword-api-prod-2025-v1" \
    "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/test$i?lang=zh" &
done
wait

# 2. 错误处理测试
curl -H "X-Static-API-Key: invalid-key" \
  "https://senseword-api-worker.zhouqi-aaha.workers.dev/api/v1/word/test?lang=zh"
```

## 💻 本地开发环境

### 环境要求
- Node.js >= 18.0.0
- npm 或 yarn
- Cloudflare Wrangler CLI

### 设置步骤

1. **克隆项目并安装依赖**
```bash
cd cloudflare/workers/api
npm install
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入实际的API密钥
GEMINI_API_KEY=your-gemini-api-key
STATIC_API_KEY=sk-senseword-api-dev-2025-v1
```

3. **启动本地开发服务器**
```bash
# 启动开发服务器
npm run dev

# 或使用特定环境
npx wrangler dev --env development
```

4. **测试本地API**
```bash
curl -H "X-Static-API-Key: sk-senseword-api-dev-2025-v1" \
  "http://localhost:8080/api/v1/word/test?lang=zh"
```

### 部署到生产环境
```bash
# 部署到生产环境
npm run deploy

# 或使用特定环境
npx wrangler deploy --env production
```

## 🔑 关键概念说明

### KDD关键帧驱动开发
本项目采用KDD (Keyframe-Driven Development) 开发方法论：
- **关键帧**: 数据在业务流程中的完整状态快照
- **补间链**: 连接关键帧的函数契约序列
- **契约驱动**: 先定义接口契约，再实现具体逻辑
- **测试验证**: 通过补间测试验证每个函数契约

### Heart-Language原则
AI生成内容遵循"心语"原则：
- **情感共鸣**: 内容要触动学习者的情感
- **文化桥梁**: 连接英语文化和学习者母语文化
- **认知脚手架**: 提供渐进式的理解支持

### 缓存优先策略
- **数据库优先**: 首先查询已存在的单词定义
- **按需生成**: 仅在缓存未命中时调用AI生成
- **异步优化**: 后台异步处理音频生成等耗时任务

## 🔒 安全特性

### 认证机制
- **静态API密钥**: 用于基础API访问控制
- **动态密钥**: 用于用户特定操作的额外验证
- **CORS配置**: 严格的跨域访问控制

### 数据保护
- **输入验证**: 严格的参数类型和格式验证
- **SQL注入防护**: 使用参数化查询防止注入攻击
- **错误信息过滤**: 避免敏感信息泄露

### 访问控制
- **频率限制**: 防止API滥用和DDoS攻击
- **密钥轮换**: 定期更新API密钥
- **日志监控**: 完整的访问日志和异常监控

## ⚠️ 错误处理

### 常见错误码
| 错误码 | HTTP状态 | 描述 | 解决方案 |
|--------|----------|------|----------|
| `INVALID_WORD` | 400 | 单词参数无效 | 检查单词拼写和格式 |
| `INVALID_LANGUAGE` | 400 | 不支持的语言代码 | 使用支持的语言代码 |
| `AUTHENTICATION_REQUIRED` | 401 | API密钥无效 | 检查X-Static-API-Key头部 |
| `AI_GENERATION_FAILED` | 503 | AI生成服务失败 | 稍后重试或联系技术支持 |
| `DATABASE_ERROR` | 500 | 数据库操作失败 | 检查网络连接，稍后重试 |

### 错误处理最佳实践
1. **指数退避重试**: 网络错误时使用指数退避策略
2. **优雅降级**: AI服务失败时提供基础定义
3. **用户友好提示**: 将技术错误转换为用户可理解的消息
4. **完整日志记录**: 记录错误上下文便于调试

## 🔗 集成指南

### iOS应用集成
1. **添加SharedModels依赖**
```swift
.package(path: "../SharedModels")
```

2. **配置API服务**
```swift
let apiService = APIService(
    baseURL: "https://senseword-api-worker.zhouqi-aaha.workers.dev",
    apiKey: "sk-senseword-api-prod-2025-v1"
)
```

3. **调用API**
```swift
do {
    let response = try await apiService.fetchWord(
        word: "progressive", 
        language: .chinese
    )
    // 处理响应
} catch {
    // 错误处理
}
```

### 其他平台集成
- **Android**: 使用HTTP客户端调用REST API
- **Web**: 使用fetch或axios调用API端点
- **后端服务**: 服务间调用使用相同的REST接口

## 📈 后续开发

### 已完成功能 ✅
- [x] 单词查询与AI生成系统
- [x] 用户反馈评价系统
- [x] 多语言支持 (20种语言)
- [x] 提示词预处理服务
- [x] 数据库缓存优化
- [x] 完整错误处理机制
- [x] 生产环境部署验证
- [x] **搜索优化功能** (KDD-004): coreDefinition字段支持快速搜索
- [x] **增量同步支持** (KDD-009): sync_id字段支持本地索引更新
- [x] **每日一词集成** (KDD-010): isWordOfTheDayCandidate字段支持候选词标记

### 待实现功能 🚧
- [ ] 用户认证与授权系统 (KDD-005)
- [ ] TTS音频生成服务
- [ ] 生词本管理功能 (KDD-006)
- [ ] 学习进度跟踪
- [ ] 个性化推荐算法
- [ ] 离线模式支持
- [ ] 性能监控和分析

### 优化计划 🎯
- [x] ~~API响应时间优化 (目标 < 500ms)~~ **已完成**: 通过coreDefinition字段优化
- [x] ~~数据库查询性能提升~~ **已完成**: 新增多个索引优化
- [ ] AI生成内容质量改进
- [ ] 用户体验优化
- [ ] 移动端适配优化

### 数据库演进计划 📊
- [x] **v1.1**: 添加搜索优化字段 (coreDefinition)
- [x] **v2.0**: 添加增量同步支持 (sync_id)
- [x] **v2.1**: 添加每日一词功能 (isWordOfTheDayCandidate)
- [ ] **v3.0**: 计划添加用户个性化字段
- [ ] **v3.1**: 计划添加学习进度跟踪字段

## 🛠️ 技术支持

### 问题排查
1. **API无响应**: 检查网络连接和API密钥
2. **返回错误**: 查看错误码和详细消息
3. **性能问题**: 检查请求频率和数据大小
4. **集成问题**: 参考集成指南和示例代码

### 技术细节参考
- **KDD文档**: `01-函数契约补间链.md`
- **测试报告**: `02-补间测试报告.md`
- **架构图**: `03-关键帧可视化.md`
- **开发日志**: `04-进度日志.md`

### 联系方式
- **技术支持**: 通过GitHub Issues提交问题
- **功能建议**: 通过项目讨论区提出建议
- **紧急问题**: 联系项目维护团队

---

**版本**: v2.1.0
**最后更新**: 2025-06-24
**数据库版本**: v2.1 (支持搜索优化、增量同步、每日一词功能)
**维护团队**: SenseWord Development Team

### 📝 更新日志
- **v2.1.0** (2025-06-24): 更新数据库结构文档，添加KDD-004/009/010功能支持
- **v2.0.0** (2025-06-24): 添加增量同步支持和搜索优化功能
- **v1.0.0** (2025-06-24): 初始版本，基础单词查询与生成功能
