# SenseWord 单词查询与生成系统 - 关键帧可视化 (V1.0)

## 1. 数据结构生命周期变化图

```mermaid
graph TD
    A[iOS请求: WordQueryRequest<br/>含SupportedLanguage枚举] -->|FC-01| B[HTTP请求: RequestContext<br/>语言代码转换]
    B -->|FC-02| C{数据库查询}
    C -->|FC-03 存在| D[数据库记录: WordDefinitionRecord]
    C -->|FC-03 不存在| E[提示词预处理: PromptProcessingRequest]
    E -->|FC-04.1| F[语言映射和占位符替换]
    F -->|FC-04| G[Gemini AI生成: AIGeneratedContent]
    G -->|FC-06| H[数据库保存: boolean]
    D -->|FC-07| I[标准响应: WordDefinitionResponse]
    G -->|FC-07| I
    I -->|FC-02| J[HTTP响应: Response]
    J -->|FC-01| K[iOS结果: WordDefinitionResponse]

    style A fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    style K fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#74c0fc,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#ffd43b,stroke:#333,stroke-width:2px,color:#000
    style D fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style E fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    style F fill:#ffa94d,stroke:#333,stroke-width:2px,color:#fff
    style G fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style H fill:#74c0fc,stroke:#333,stroke-width:2px,color:#fff
    style I fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style J fill:#74c0fc,stroke:#333,stroke-width:2px,color:#fff
```

## 2. 分支决策流程图

```mermaid
flowchart TD
    Start([开始: 单词查询请求]) --> Validate{验证请求参数}
    Validate -->|无效| Error1[返回参数错误]
    Validate -->|有效| DBQuery[查询数据库]

    DBQuery --> Exists{记录是否存在?}
    Exists -->|是| Format1[格式化现有记录]
    Exists -->|否| LangMap[语言代码映射]

    LangMap --> PromptProcess[提示词预处理]
    PromptProcess --> AIGen[调用Gemini AI生成]

    AIGen --> AISuccess{AI生成成功?}
    AISuccess -->|否| Error2[返回AI生成失败]
    AISuccess -->|是| SaveDB[保存到数据库]

    SaveDB --> SaveSuccess{保存成功?}
    SaveSuccess -->|否| Error3[返回数据库错误]
    SaveSuccess -->|是| Format2[格式化新生成内容]

    Format1 --> Success[返回成功响应]
    Format2 --> Success
    Error1 --> End([结束])
    Error2 --> End
    Error3 --> End
    Success --> End

    style Start fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style End fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style Validate fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style Exists fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style AISuccess fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style SaveSuccess fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style Error1 fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    style Error2 fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    style Error3 fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    style Success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
```

## 3. 字段级别数据追踪图

```mermaid
graph LR
    subgraph "iOS输入阶段"
        A1[word: String]
        A2[language: SupportedLanguage]
    end

    subgraph "语言映射阶段"
        B1[languageCode: string]
        B2[languageName: string]
        B3[映射表查询]
    end

    subgraph "数据库查询阶段"
        C1[word: string]
        C2[language: string]
        C3[contentJson: string]
        C4[metadata fields...]
    end

    subgraph "AI生成阶段"
        D1[word: string]
        D2[targetAudienceLanguage: string]
        D3[processedPrompt: string]
        D4[完整AI内容结构]
    end

    subgraph "响应格式化阶段"
        E1[word: string]
        E2[metadata: object]
        E3[content: object]
    end

    subgraph "最终输出阶段"
        F1[word: String]
        F2[metadata: WordMetadata]
        F3[content: WordContent]
    end

    A1 --> C1
    A2 --> B1
    B1 --> B3
    B3 --> B2
    B1 --> C2
    A1 --> D1
    B2 --> D2
    D2 --> D3

    C1 --> E1
    C3 --> E3
    D1 --> E1
    D4 --> E2
    D4 --> E3

    E1 --> F1
    E2 --> F2
    E3 --> F3

    style A1 fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    style A2 fill:#ff6b6b,stroke:#333,stroke-width:2px,color:#fff
    style B2 fill:#ffa94d,stroke:#333,stroke-width:2px,color:#fff
    style D3 fill:#ffa94d,stroke:#333,stroke-width:2px,color:#fff
    style F1 fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style F2 fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
    style F3 fill:#51cf66,stroke:#333,stroke-width:2px,color:#fff
```

## 4. 状态机转换图

```mermaid
stateDiagram-v2
    [*] --> RequestReceived: iOS发起请求
    RequestReceived --> Validating: 验证参数
    Validating --> DatabaseQuerying: 参数有效
    Validating --> ErrorState: 参数无效

    DatabaseQuerying --> RecordFound: 找到记录
    DatabaseQuerying --> RecordNotFound: 未找到记录
    DatabaseQuerying --> ErrorState: 数据库错误

    RecordFound --> FormattingExisting: 格式化现有记录
    RecordNotFound --> LanguageMapping: 语言代码映射

    LanguageMapping --> PromptProcessing: 提示词预处理
    PromptProcessing --> AIGenerating: 调用Gemini AI生成

    AIGenerating --> AICompleted: AI生成成功
    AIGenerating --> ErrorState: AI生成失败

    AICompleted --> SavingToDatabase: 保存到数据库
    SavingToDatabase --> SaveCompleted: 保存成功
    SavingToDatabase --> ErrorState: 保存失败

    SaveCompleted --> FormattingNew: 格式化新内容
    FormattingExisting --> ResponseReady: 响应准备就绪
    FormattingNew --> ResponseReady: 响应准备就绪

    ResponseReady --> [*]: 返回成功响应
    ErrorState --> [*]: 返回错误响应
```

## 5. 批处理流程优化图

```mermaid
graph TB
    subgraph "单个请求处理"
        A[单词请求] --> B[数据库查询]
        B --> C{存在?}
        C -->|是| D[直接返回]
        C -->|否| E[AI生成]
        E --> F[保存]
        F --> G[返回]
    end

    subgraph "批量优化策略"
        H[批量请求] --> I[批量数据库查询]
        I --> J[分离存在/不存在]
        J --> K[批量AI生成]
        K --> L[批量保存]
        L --> M[批量响应]
    end

    subgraph "缓存策略"
        N[请求] --> O{内存缓存?}
        O -->|命中| P[直接返回]
        O -->|未命中| Q[正常流程]
        Q --> R[更新缓存]
    end

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style H fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style N fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style D fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style G fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style M fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style P fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
```