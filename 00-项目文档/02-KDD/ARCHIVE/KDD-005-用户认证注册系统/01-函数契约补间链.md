# KDD-005: 用户认证注册系统 - 函数契约补间链

## 1. 项目文件结构概览 (Project File Structure Overview) - 微服务架构版

基于`029｜分布式微服务架构.md`的建议，采用独立的`auth-worker`实现"专业工具箱"模式，确保认证系统的最高安全级别和独立部署能力。

- `# [新增]` 表示新创建的文件或目录  
- `# [修改]` 表示需要修改的现有文件
- `# [迁移]` 表示从其他位置迁移的文件

```
SenseWord项目根目录/
├── cloudflare/
│   ├── d1/
│   │   └── migrations/
│   │       └── 0005_create_users_table.sql     # [新增] 用户表迁移脚本
│   └── workers/
│       ├── api/                                # [保持] 核心API网关 (api.senseword.com)
│       │   ├── src/
│       │   │   ├── index.ts                    # [修改] 移除认证路由，专注核心API
│       │   │   ├── services/                   # [保持] 单词查询、AI生成等服务
│       │   │   └── types/                      # [保持] 核心API类型定义
│       │   └── wrangler.toml                   # [修改] 移除认证相关环境变量
│       └── auth/                               # [新增] 独立认证服务 (auth.senseword.com)
│           ├── src/
│           │   ├── auth/
│           │   │   ├── auth.service.ts          # [新增] 用户认证核心业务逻辑
│           │   │   ├── apple-auth.service.ts    # [新增] Apple ID Token验证服务
│           │   │   ├── jwt.service.ts           # [新增] JWT令牌生成和验证
│           │   │   └── middleware/
│           │   │       └── auth.middleware.ts   # [新增] 认证中间件
│           │   ├── controllers/
│           │   │   └── auth.controller.ts       # [新增] 认证API控制器
│           │   ├── types/
│           │   │   └── auth-types.ts            # [新增] 认证系统类型定义
│           │   └── index.ts                     # [新增] auth-worker主入口
│           ├── package.json                     # [新增] 独立的依赖管理
│           ├── tsconfig.json                    # [新增] 独立的TypeScript配置
│           └── wrangler.toml                    # [新增] auth-worker专用配置
├── iOS/
│   ├── Sources/
│   │   └── AuthDomain/                          # [新增] 认证领域包
│   │       ├── Package.swift                    # [新增] Swift包配置
│   │       └── Sources/
│   │           └── AuthDomain/
│   │               ├── Models/
│   │               │   ├── AuthSession.swift    # [新增] 认证会话模型
│   │               │   └── UserProfile.swift    # [新增] 用户资料模型
│   │               ├── Services/
│   │               │   └── AuthService.swift    # [新增] 前端认证服务
│   │               └── Views/
│   │                   └── SignInView.swift     # [新增] 登录界面
│   └── SenseWordApp.swift                       # [修改] 集成认证功能
└── 0-KDD - 关键帧驱动开发/
    └── 02-KDD/
        └── KDD-005-用户认证注册系统/
            ├── 01-函数契约补间链.md             # [修改] 本文档
            ├── 02-补间测试报告.md               # [修改] 测试报告
            ├── 03-关键帧可视化.md               # [修改] 可视化图表
            └── 04-进度日志.md                   # [修改] 进度跟踪
```

## 2. 微服务架构与路由策略

### 2.1 服务域名分配
基于`029｜分布式微服务架构.md`的建议，采用子域名路由：

- **`api.senseword.com`** → `api-worker` (核心API网关)
  - 单词查询: `GET /api/v1/word/{wordName}`
  - 搜索建议: `GET /api/v1/suggestions`
  - 生词本管理: `GET|POST|DELETE /api/v1/bookmarks`

- **`auth.senseword.com`** → `auth-worker` (认证与支付网关)
  - Apple登录: `POST /api/v1/auth/login`
  - 用户信息: `GET /api/v1/users/me`
  - 购买验证: `POST /api/v1/purchases/verify`

- **`tts.senseword.com`** → `tts-worker` (已存在)
  - 音频生成: `POST /api/v1/tts/generate`

### 2.2 分支策略建议
- **建议的特性分支名称**: `feature/auth/microservice-architecture`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-senseword-auth-system`
- **基础分支**: `dev`

### 2.3 安全策略差异化
- **auth-worker**: 绝不缓存、严格速率限制、最高安全等级
- **api-worker**: 宽松缓存策略、高频读取优化

## 3. Commit 规划概要 (微服务架构版 - MVP) ✅ 已完成

### 3.1 基础设施建设 ✅ 全部完成
- [x] chore(db): 创建users表结构和基础索引
- [x] feat(auth-worker): 创建独立的auth-worker项目结构
- [x] chore(auth-worker): 配置TypeScript、wrangler.toml、package.json

### 3.2 认证服务核心实现 ✅ 全部完成
- [x] feat(apple-auth): 实现Apple ID Token验证服务 (支持开发模式)
- [x] feat(jwt-service): 实现JWT令牌生成和验证工具
- [x] feat(user-service): 实现用户查找或创建核心逻辑
- [x] feat(auth-middleware): 创建认证中间件

### 3.3 API端点实现 ✅ 全部完成
- [x] feat(auth-api): 实现POST /api/v1/auth/login端点
- [x] feat(users-api): 实现GET /api/v1/users/me端点
- [x] feat(mock-auth): 创建开发环境模拟认证端点

### 3.4 API网关重构 ✅ 全部完成
- [x] refactor(api-worker): 从api-worker移除认证相关代码
- [x] feat(deployment): 成功部署auth-worker到生产环境并完成测试

### 3.5 前端集成 🔄 待后续实现
- [ ] feat(auth-domain): 创建iOS认证领域Swift包
- [ ] feat(auth-ui): 实现Sign in with Apple前端界面
- [ ] feat(service-client): 更新前端服务客户端支持多域名

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 前端Apple登录触发器

- **职责**: 响应用户点击"Sign in with Apple"按钮，调用Apple原生认证服务，获取ID Token
- **函数签名**: `handleAppleSignIn() -> Void`
- **所在文件**: `iOS/Sources/AuthDomain/Sources/AuthDomain/Views/SignInView.swift`

>>>>> 输入 (Input): 用户点击事件

用户在UI界面点击"Sign in with Apple"按钮触发的交互事件。

```swift
// 用户交互触发，无具体数据结构
// 触发Apple原生认证流程
```

<<<<< 输出 (Output): Apple认证结果回调

Apple认证服务返回的结果，包含成功或失败的情况。

```swift
// 成功时的ASAuthorization对象
struct AppleAuthResult {
    let identityToken: Data?        // Apple ID Token
    let authorizationCode: Data?    // 授权码
    let user: String               // 用户唯一标识符
    let fullName: PersonNameComponents? // 用户姓名（可选）
    let email: String?             // 用户邮箱（可选）
}

// 失败时的错误对象
enum AppleAuthError: Error {
    case cancelled              // 用户取消
    case failed(Error)         // 认证失败
    case unknown               // 未知错误
}
```

---

### [FC-02]: 前端认证服务处理器

- **职责**: 接收Apple认证结果，提取ID Token，发送到后端API进行验证和用户创建
- **函数签名**: `signInWithApple(credential: ASAuthorizationAppleIDCredential) async throws -> AuthSession`
- **所在文件**: `iOS/Sources/AuthDomain/Sources/AuthDomain/Services/AuthService.swift`

>>>>> 输入 (Input): ASAuthorizationAppleIDCredential

从Apple认证服务获取的原生凭证对象。

```swift
// Apple原生认证凭证
protocol ASAuthorizationAppleIDCredential {
    var identityToken: Data? { get }      // Apple ID Token (JWT格式)
    var authorizationCode: Data? { get }  // 授权码
    var user: String { get }              // 用户唯一标识符
    var fullName: PersonNameComponents? { get } // 用户姓名
    var email: String? { get }            // 用户邮箱
}
```

<<<<< 输出 (Output): AuthSession (成功时) 或 throws Error (失败时)

成功时返回完整的认证会话对象，失败时抛出错误。

```swift
// 成功时的认证会话
struct AuthSession: Codable {
    let user: UserProfile
    let tokens: AuthTokens
}

struct UserProfile: Codable {
    let id: String              // 用户唯一标识符
    let email: String           // 用户邮箱
    let displayName: String     // 显示名称
    let isPro: Bool            // Pro状态
    let createdAt: String      // 创建时间
}

struct AuthTokens: Codable {
    let accessToken: String     // 访问令牌
    let refreshToken: String    // 刷新令牌
}

// 失败时的错误类型
enum AuthError: Error {
    case invalidToken          // 无效Token
    case networkError         // 网络错误
    case serverError          // 服务器错误
}
```

---

### [FC-03]: 后端认证API端点处理器

- **职责**: 作为`POST /api/v1/auth/login`的入口，接收前端发送的Apple ID Token，调用认证服务，返回标准化HTTP响应
- **函数签名**: `handleAppleLogin(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/index.ts`

>>>>> 输入 (Input): HTTP Request with LoginRequestBody

前端发送的包含Apple ID Token的HTTP请求。

```typescript
// HTTP请求体结构
interface LoginRequestBody {
  idToken: string;                    // Apple ID Token (JWT格式)
  provider: 'apple';                  // 认证提供方 (仅支持Apple)
}

// 完整的HTTP请求对象
interface HTTPRequest {
  method: 'POST';
  url: '/api/v1/auth/login';
  headers: {
    'Content-Type': 'application/json';
    'User-Agent': string;
  };
  body: LoginRequestBody;
}
```

<<<<< 输出 (Output): HTTP Response

标准化的HTTP响应，包含成功或失败的结果。

```typescript
// 成功响应 (HTTP 200) - MVP版本：只返回令牌，遵循单一职责
interface LoginSuccessResponse {
  success: true;
  tokens: {
    accessToken: string;              // JWT访问令牌
    refreshToken: string;             // 刷新令牌
  };
}

// 失败响应 (HTTP 4xx/5xx)
interface LoginErrorResponse {
  success: false;
  error: 'INVALID_TOKEN' | 'USER_CREATION_FAILED' | 'SYSTEM_ERROR';
  message: string;
  timestamp: string;
}
```

---

### [FC-04]: Apple ID Token验证服务

- **职责**: 验证Apple ID Token的真实性和有效性，解析用户信息，返回验证后的用户数据
- **函数签名**: `verifyAppleIdToken(idToken: string, env: Env): Promise<ValidatedUserInfo>`
- **所在文件**: `cloudflare/workers/api/src/auth/apple-auth.service.ts`

>>>>> 输入 (Input): Apple ID Token字符串

从前端接收的Apple ID Token (JWT格式)。

```typescript
// Apple ID Token (JWT字符串)
type AppleIdToken = string; // 格式: "header.payload.signature"
```

**中间数据结构A**: Token解码后的原始载荷

```typescript
// Token解码后的载荷结构 (Apple官方标准)
interface AppleIdTokenPayload {
  iss: string;                        // 发行者 (https://appleid.apple.com)
  aud: string;                        // 受众 (App Bundle ID)
  exp: number;                        // 过期时间
  iat: number;                        // 签发时间
  sub: string;                        // 用户唯一标识符
  email?: string;                     // 用户邮箱 (可能不提供)
  email_verified?: boolean;           // 邮箱验证状态
  at_hash?: string;                   // 访问令牌哈希
  auth_time?: number;                 // 认证时间
}
```

**中间数据结构B**: 验证后的处理结果

```typescript
// Token验证中间结果
interface TokenValidationResult {
  isValid: boolean;                   // 验证是否成功
  payload: AppleIdTokenPayload | null; // 解析的载荷
  error?: string;                     // 验证失败原因
  publicKey?: any;                    // 使用的公钥信息
}
```

<<<<< 输出 (Output): ValidatedUserInfo

验证成功后的统一用户信息结构。

```typescript
// 验证后的用户信息 (简化版，遵循奥卡姆剃刀原则)
interface ValidatedUserInfo {
  id: string;                         // 用户唯一标识符 (来自sub)
  email: string;                      // 用户邮箱 (生成或提取)
  displayName: string;                // 用户显示名称 (生成)
}
```

---

### [FC-05]: 用户查找或创建服务

- **职责**: 根据验证后的用户信息，在数据库中查找现有用户或创建新用户记录
- **函数签名**: `findOrCreateUser(userInfo: ValidatedUserInfo, env: Env): Promise<UserWithComputedProps>`
- **所在文件**: `cloudflare/workers/api/src/auth/auth.service.ts`

>>>>> 输入 (Input): ValidatedUserInfo

从Apple ID Token验证服务返回的已验证用户信息。

```typescript
// 已验证的用户信息 (简化版，与FC-04输出相同)
interface ValidatedUserInfo {
  id: string;                         // 用户唯一标识符
  email: string;                      // 用户邮箱
  displayName: string;                // 用户显示名称
}
```

**中间数据结构A**: 数据库查询原始结果

```typescript
// 数据库查询的原始结果
interface DatabaseQueryResult {
  success: boolean;                   // 查询是否成功
  user: any | null;                   // 查询到的用户记录 (可能为null)
  error?: string;                     // 查询失败原因
}
```

**中间数据结构B**: 用户创建/更新操作结果

```typescript
// 用户操作结果
interface UserOperationResult {
  operation: 'found' | 'created' | 'updated'; // 执行的操作类型
  user: UserRecord;                   // 操作后的用户记录
  isNewUser: boolean;                 // 是否为新用户
}
```

<<<<< 输出 (Output): UserWithComputedProps

包含计算属性的完整用户对象。

```typescript
// 数据库用户记录结构 (简化版)
interface UserRecord {
  id: string;                         // 用户唯一标识符
  email: string;                      // 用户邮箱
  displayName: string;                // 用户显示名称
  subscription_expires_at: string | null; // 订阅过期时间
  createdAt: string;                  // 创建时间
  updatedAt: string;                  // 更新时间
}

// 包含计算属性的用户对象
interface UserWithComputedProps extends UserRecord {
  isPro: boolean;                     // 计算得出的Pro状态
}
```

---

### [FC-06]: JWT令牌生成服务

- **职责**: 为已认证用户生成访问令牌和刷新令牌，用于后续API调用的身份验证
- **函数签名**: `generateTokens(user: UserWithComputedProps, env: Env): Promise<AuthTokens>`
- **所在文件**: `cloudflare/workers/api/src/auth/jwt.service.ts`

>>>>> 输入 (Input): UserWithComputedProps

包含完整用户信息和计算属性的用户对象。

```typescript
// 完整的用户对象 (与FC-05输出相同)
interface UserWithComputedProps extends UserRecord {
  id: string;                         // 用户唯一标识符
  email: string;                      // 用户邮箱
  provider: 'apple';                  // 认证提供方
  displayName: string;                // 用户显示名称
  subscription_expires_at: string | null; // 订阅过期时间
  createdAt: string;                  // 创建时间
  updatedAt: string;                  // 更新时间
  isPro: boolean;                     // 计算得出的Pro状态
}
```

<<<<< 输出 (Output): AuthTokens

生成的JWT令牌对。

```typescript
// JWT令牌对
interface AuthTokens {
  accessToken: string;                // 访问令牌 (1小时有效期)
  refreshToken: string;               // 刷新令牌 (30天有效期)
}

// 访问令牌载荷结构
interface AccessTokenPayload {
  sub: string;                        // 用户ID
  email: string;                      // 用户邮箱
  provider: 'apple';                  // 认证提供方
  isPro: boolean;                     // Pro状态
  iat: number;                        // 签发时间
  exp: number;                        // 过期时间 (1小时)
}

// 刷新令牌载荷结构
interface RefreshTokenPayload {
  sub: string;                        // 用户ID
  type: 'refresh';                    // 令牌类型
  iat: number;                        // 签发时间
  exp: number;                        // 过期时间 (30天)
}
```

---

### [FC-07]: 认证中间件

- **职责**: 验证API请求中的访问令牌，提取用户信息，用于保护需要认证的API端点
- **函数签名**: `authenticate(request: Request, env: Env): Promise<AccessTokenPayload>`
- **所在文件**: `cloudflare/workers/api/src/auth/middleware/auth.middleware.ts`

>>>>> 输入 (Input): HTTP Request with Authorization Header

包含Authorization头的HTTP请求。

```typescript
// HTTP请求对象
interface AuthenticatedRequest {
  method: string;
  url: string;
  headers: {
    'Authorization': string;          // 格式: "Bearer <accessToken>"
    [key: string]: string;
  };
  body?: any;
}
```

<<<<< 输出 (Output): AccessTokenPayload (成功时) 或 throws Error (失败时)

成功时返回解析后的令牌载荷，失败时抛出认证错误。

```typescript
// 成功时的令牌载荷 (与FC-06中定义相同)
interface AccessTokenPayload {
  sub: string;                        // 用户ID
  email: string;                      // 用户邮箱
  isPro: boolean;                     // Pro状态
  iat: number;                        // 签发时间
  exp: number;                        // 过期时间
}

// 失败时的错误类型
enum AuthMiddlewareError {
  MISSING_TOKEN = 'Missing authorization header',
  INVALID_TOKEN = 'Invalid token format',
  EXPIRED_TOKEN = 'Token expired',
  VERIFICATION_FAILED = 'Token verification failed'
}
```

---

### [FC-08]: 用户信息获取端点 (新增)

- **职责**: 基于有效的访问令牌，返回当前用户的完整信息，实现认证与用户信息获取的职责分离
- **函数签名**: `getCurrentUser(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/users/users.controller.ts`

>>>>> 输入 (Input): HTTP Request with Authorization Header

需要认证的HTTP请求，通过FC-07中间件验证后的请求。

```typescript
// 经过认证中间件处理的请求
interface AuthenticatedUserRequest {
  method: 'GET';
  url: '/api/v1/users/me';
  headers: {
    'Authorization': string;          // 格式: "Bearer <accessToken>"
  };
  // 中间件已验证并注入的用户信息
  user: AccessTokenPayload;
}
```

<<<<< 输出 (Output): HTTP Response with UserProfile

返回当前用户的完整信息。

```typescript
// 成功响应 (HTTP 200)
interface UserProfileResponse {
  success: true;
  user: {
    id: string;                       // 用户唯一标识符
    email: string;                    // 用户邮箱
    displayName: string;              // 显示名称
    isPro: boolean;                   // Pro状态
    createdAt: string;                // 创建时间
  };
}

// 失败响应 (HTTP 401)
interface UserProfileErrorResponse {
  success: false;
  error: 'UNAUTHORIZED';
  message: string;
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/workers/api/src/index.ts
cloudflare/workers/api/wrangler.toml
cloudflare/workers/api/src/types/word-types.ts
iOS/Sources/SensewordAppDependencies/DependencyManager.swift
iOS/SenseWordApp.swift
iOS/Packages/UIComponents/Package.swift
0-KDD - 关键帧驱动开发/01-Public/02-KDD-Protocol.md
0-KDD - 关键帧驱动开发/03-Docs/04-技术方案/KDD-005-用户认证注册系统.md
0-KDD - 关键帧驱动开发/03-Docs/05-会议讨论/026｜用户注册-优化版.md
</context_files>

## 6. 核心业务流程伪代码 (MVP版本 - 快速失败策略)

```typescript
async function handleAppleLogin(request: Request, env: Env): Promise<Response> {
    const startTime = Date.now();

    try {
        // [FC-03] 解析请求体 - 基础验证
        const requestBody = await request.json() as LoginRequestBody;

        if (!requestBody.idToken || requestBody.provider !== 'apple') {
            return createErrorResponse('INVALID_REQUEST', 'Invalid request format', 400);
        }

        // [FC-04] 验证Apple ID Token - 快速失败
        const validatedUserInfo = await verifyAppleIdToken(requestBody.idToken, env);

        // [FC-05] 查找或创建用户 - 快速失败
        const user = await findOrCreateUser(validatedUserInfo, env);

        // [FC-06] 生成JWT令牌 - 快速失败
        const tokens = await generateTokens(user, env);

        // [FC-03] 返回成功响应 - MVP版本只返回令牌
        const responseTime = Date.now() - startTime;
        console.log(`[认证API] 登录成功: ${user.email}, 耗时: ${responseTime}ms`);

        const response: LoginSuccessResponse = {
            success: true,
            tokens: {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken
            }
        };

        return new Response(JSON.stringify(response), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'X-Response-Time': `${responseTime}ms`
            }
        });

    } catch (error) {
        const responseTime = Date.now() - startTime;

        // 详细错误日志记录 (用于数据驱动优化)
        console.error(`[认证API] 登录失败详情:`, {
            error: error.message,
            stack: error.stack,
            responseTime: `${responseTime}ms`,
            timestamp: new Date().toISOString(),
            // 记录更多上下文用于后续分析
            userAgent: request.headers.get('User-Agent'),
            ip: request.headers.get('CF-Connecting-IP')
        });

        // MVP版本：统一返回通用错误，避免信息泄露
        return createErrorResponse('SYSTEM_ERROR', 'Authentication failed', 500);
    }
}

// 用户信息获取端点 (新增)
async function getCurrentUser(request: Request, env: Env): Promise<Response> {
    try {
        // [FC-07] 认证中间件已验证用户身份
        const userPayload = request.user as AccessTokenPayload; // 中间件注入

        // [FC-08] 根据JWT载荷构建用户信息响应
        const response: UserProfileResponse = {
            success: true,
            user: {
                id: userPayload.sub,
                email: userPayload.email,
                displayName: `SenseWord用户`, // MVP版本使用简单显示名
                isPro: userPayload.isPro,
                createdAt: new Date(userPayload.iat * 1000).toISOString()
            }
        };

        return new Response(JSON.stringify(response), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        console.error('[用户API] 获取用户信息失败:', error);
        return createErrorResponse('UNAUTHORIZED', 'Invalid or expired token', 401);
    }
}

// 辅助函数：创建标准化错误响应
function createErrorResponse(errorCode: string, message: string, statusCode: number): Response {
    const errorResponse = {
        success: false,
        error: errorCode,
        message: message,
        timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(errorResponse), {
        status: statusCode,
        headers: { 'Content-Type': 'application/json' }
    });
}

// 认证中间件使用示例
async function protectedEndpoint(request: Request, env: Env): Promise<Response> {
    try {
        // [FC-07] 验证用户身份
        const userPayload = await authenticate(request, env)

        // 执行需要认证的业务逻辑
        const result = await someProtectedOperation(userPayload.sub, env)

        return new Response(JSON.stringify(result), { status: 200 })

    } catch (error) {
        return new Response(JSON.stringify({
            success: false,
            error: 'UNAUTHORIZED',
            message: 'Authentication required'
        }), { status: 401 })
    }
}
```

## 7. MVP版本的简化处理策略

### 7.1 错误处理原则

MVP版本采用"快速失败 + 详细日志"策略：

1. **统一错误响应**: 所有错误统一返回`SYSTEM_ERROR`，避免信息泄露
2. **详细错误日志**: 在后端记录完整的错误上下文，用于后续分析
3. **数据驱动优化**: 基于真实使用数据，识别需要特殊处理的高频错误

### 7.2 日志记录策略

```typescript
// 标准化日志记录
function logAuthenticationEvent(event: {
  type: 'success' | 'failure';
  userId?: string;
  error?: string;
  responseTime: number;
  userAgent?: string;
  ip?: string;
}) {
  console.log(`[认证事件] ${JSON.stringify({
    ...event,
    timestamp: new Date().toISOString()
  })}`);
}
```

### 7.3 未来优化方向

基于MVP运行数据，后续版本可能需要添加的功能：

1. **高频错误的重试机制**: 如果某类错误频繁出现且重试有效
2. **设备管理**: 如果用户反馈多设备登录问题
3. **性能优化**: 如果响应时间成为瓶颈

这种方法避免了过早优化，确保我们只为真正需要的功能投入开发资源。

## 8. MVP版本说明

### 8.1 职责分离设计

本MVP版本采用严格的职责分离原则：

1. **认证端点** (`POST /api/v1/auth/login`): 只负责验证Apple ID Token并返回JWT令牌
2. **用户信息端点** (`GET /api/v1/users/me`): 只负责返回当前用户信息
3. **认证中间件** (`FC-07`): 只负责验证JWT令牌的有效性

### 8.2 与其他KDD模块的集成

其他KDD模块需要用户认证时，统一通过以下方式：

1. **使用认证中间件**: 在需要认证的端点前应用`FC-07`认证中间件
2. **获取用户信息**: 从中间件注入的`AccessTokenPayload`中获取用户ID和Pro状态
3. **避免直接数据库查询**: 不直接查询users表，通过JWT载荷获取必要信息

这种设计确保了模块间的松耦合，避免了过早的接口抽象。

## 9. MVP版本成功指标

### 9.1 核心指标
- **认证成功率**: 目标 >95% (降低期望，专注核心功能)
- **平均响应时间**: 目标 <3秒 (为简化逻辑留出余量)
- **系统可用性**: 目标 >99% (基于Cloudflare基础设施)

### 9.2 监控重点
- **错误日志完整性**: 确保所有错误都被详细记录
- **用户反馈**: 收集真实用户的使用体验
- **性能基线**: 建立后续优化的基准数据

### 9.3 迭代优化策略
基于MVP运行数据，按优先级进行后续优化：
1. **P0**: 修复导致认证失败的关键问题
2. **P1**: 优化高频出现的性能瓶颈
3. **P2**: 添加用户体验改进功能