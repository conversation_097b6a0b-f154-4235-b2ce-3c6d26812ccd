# AI智能审核系统 - 函数契约补间链

## 项目概述

**目标**: 构建AI智能审核系统，使用同心圆分层模型对SenseWord数据库中的单词进行质量筛选，生成Vertex AI批处理请求队列

**核心价值**:
- 自动化质量控制：使用AI替代人工审核，提高效率
- 优先级排序：基于同心圆模型为单词分配学习优先级
- 批量处理：生成Vertex AI批处理队列，支持大规模数据处理

## 关键帧补间链

### [F1] 数据库连接与单词提取
**输入关键帧**: `DatabaseConfig`
```typescript
interface DatabaseConfig {
  dbPath: string                    // SQLite数据库路径
  tableName: string                 // 目标表名 "definitions"  
  outputDir: string                 // 输出目录路径
}
```

**输出关键帧**: `WordExtractionResult`
```typescript
interface WordExtractionResult {
  totalWords: number                // 提取的单词总数
  words: WordPair[]                 // 单词对列表
  extractionTimestamp: string       // 提取时间戳
}

interface WordPair {
  id: number                        // 数据库中的主键ID
  word: string                      // 单词文本
}
```

**函数契约**: `FC-01: extractWordsFromSQLite`
- 连接SQLite数据库 `senseword_content.db`
- 执行SQL查询: `SELECT id, word FROM definitions ORDER BY id`
- 提取所有id-word对，不进行任何筛选
- 返回结构化的单词数据

---

### [F2] 批处理分组与JSONL生成
**输入关键帧**: `WordExtractionResult` + `BatchConfig`
```typescript
interface BatchConfig {
  batchSize: number                 // 每批处理的单词数量 (100)
  promptTemplate: string            // 同心圆分层模型提示词
  outputFormat: string              // 输出格式标识符
}
```

**输出关键帧**: `BatchGenerationResult`
```typescript
interface BatchGenerationResult {
  totalBatches: number              // 生成的批次总数
  batchFiles: BatchFileInfo[]       // 批处理文件信息
  generationSummary: BatchSummary   // 生成摘要
}

interface BatchFileInfo {
  fileName: string                  // JSONL文件名
  filePath: string                  // 完整文件路径
  wordCount: number                 // 包含的单词数量
  batchIndex: number                // 批次索引号
}

interface BatchSummary {
  totalWords: number                // 总单词数
  totalBatches: number              // 总批次数
  avgWordsPerBatch: number          // 平均每批单词数
  generatedAt: string               // 生成时间
}
```

**函数契约**: `FC-02: generateVertexAIBatchFiles`
- 将单词列表按100个分组
- 为每个批次生成符合Vertex AI格式的JSONL文件
- 文件命名: `ai_audit_batch_{index:03d}.jsonl`
- 每行包含完整的Vertex AI批处理请求格式

---

### [F3] Vertex AI请求格式构建
**输入关键帧**: `WordBatch` + `PromptConfig`
```typescript
interface WordBatch {
  batchIndex: number                // 批次索引
  words: WordPair[]                 // 当前批次的单词对
}

interface PromptConfig {
  systemPrompt: string              // 同心圆分层模型提示词
  temperature: number               // AI温度设置 (0.7)
  maxTokens: number                 // 最大输出token数
  responseFormat: string            // 响应格式 ("application/json")
}
```

**输出关键帧**: `VertexAIRequest`
```typescript
interface VertexAIRequest {
  request: {
    contents: RequestContent[]      // 请求内容数组
    generationConfig: GenerationConfig  // 生成配置
  }
}

interface RequestContent {
  role: string                      // 用户角色 "user"
  parts: ContentPart[]              // 内容部分
}

interface ContentPart {
  text: string                      // 完整的提示词 + 单词数据
}

interface GenerationConfig {
  temperature: number               // 温度设置
  maxOutputTokens: number           // 最大输出token
  topK: number                      // Top-K采样
  topP: number                      // Top-P采样  
  responseMimeType: string          // 响应MIME类型
}
```

**函数契约**: `FC-03: buildVertexAIRequest`
- 构建符合Vertex AI批处理API规范的请求格式
- 将同心圆提示词与单词数据结合
- 设置适当的生成参数（温度0.7，最大60000 tokens）
- 确保JSON格式输出

---

### [F4] 文件系统管理与输出
**输入关键帧**: `BatchGenerationResult` + `OutputConfig`
```typescript
interface OutputConfig {
  baseOutputDir: string             // 基础输出目录
  createTimestamp: boolean          // 是否创建时间戳目录
  generateManifest: boolean         // 是否生成清单文件
}
```

**输出关键帧**: `FileSystemResult`
```typescript
interface FileSystemResult {
  outputDirectory: string           // 最终输出目录
  manifestFile: string              // 清单文件路径
  batchFiles: string[]              // 所有批处理文件路径
  totalSize: number                 // 总文件大小(字节)
  isReady: boolean                  // 是否准备就绪
}
```

**函数契约**: `FC-04: organizeOutputFiles`
- 创建带时间戳的输出目录结构
- 生成批处理清单文件 `batch_manifest.json`
- 验证所有JSONL文件的完整性
- 输出处理统计信息

---

## 提示词模板

### 同心圆分层模型提示词
```
你是英语词汇筛选专家，负责为英语学习应用筛选词汇。请使用同心圆分层模型进行精细化筛选。

**目标用户：**
- 想达到英语母语者水平的学习者
- 目标被动词汇量：40,000-50,000词

**同心圆分层模型：**

**第1层 - 核心主动词汇 (优先级8-10)：**
- 母语者日常必用的5,000个核心词汇
- 例如：basic, important, family, work, happy, difficult

**第2层 - 扩展被动词汇 (优先级6-7)：**
- 新闻、书籍、正式交流中的常见词汇  
- 例如：significant, sophisticated, elaborate, contemporary

**第3层 - 专业常见词汇 (优先级5-6)：**
- 媒体政治类：democracy, legislation, controversy
- 商务科技类：algorithm, optimization, interface
- 文学艺术类：metaphor, aesthetic, protagonist  
- 学术通用类：hypothesis, methodology, correlation

**第4层 - 过度专业 (KEEP=0)：**
- 医学专用：hemagglutination, dextroamphetamine
- 法律条文：过度专业的法律术语
- 科研专用：实验室专业术语
- 极冷门词汇：普通人永远不会遇到的词汇

**筛选标准：**

KEEP=1 (保留条件)：
1. 第1-3层词汇：核心、扩展、专业常见
2. 英语母语者在日常生活、媒体、文学中会遇到
3. 有助于理解主流英语内容（新闻、电影、书籍）
4. 词汇长度通常≤15个字符
5. 标准拼写，非复合拼写错误

KEEP=0 (移除条件)：
1. 第4层过度专业词汇
2. 极冷门的科学术语、医学专用词
3. 拼写错误或非标准拼写
4. 过长的技术术语（>15字符）
5. 普通母语者也不认识的词汇

**输出格式：**
对每个词汇输出一行JSON：
{"id": 词汇ID, "word": 单词, "keep": 0或1, "priority": 1-10优先级}

- 使用提供的id字段
- keep字段必须是数字 0 或 1  
- priority基于同心圆层级：第1层=8-10, 第2层=6-7, 第3层=5-6
- 不要添加解释文字
```

## 实现计划

### Commit 规划

#### Commit 1: 数据库连接与单词提取模块
- [ ] **FC-01**: 实现SQLite数据库连接和单词提取
- [ ] 创建 `extract_words_from_sqlite.py` 脚本
- [ ] 测试数据库连接和数据提取功能
- [ ] 验证提取的单词数据格式

**预期输出**: 
- Python脚本能成功连接SQLite数据库
- 提取所有definitions表中的id-word对
- 生成结构化的中间数据文件

#### Commit 2: 批处理分组与文件生成器
- [ ] **FC-02**: 实现批处理分组逻辑
- [ ] 创建 `generate_batch_files.py` 脚本
- [ ] 实现100个单词/批次的分组机制
- [ ] 生成符合Vertex AI格式的JSONL文件

**预期输出**:
- 按100个单词分组的批处理文件
- 符合Vertex AI批处理API格式的JSONL文件
- 批处理清单和统计信息

#### Commit 3: Vertex AI请求格式标准化
- [ ] **FC-03**: 完善Vertex AI请求构建
- [ ] 集成同心圆分层模型提示词
- [ ] 设置标准化的生成参数
- [ ] 验证请求格式兼容性

**预期输出**:
- 标准化的Vertex AI批处理请求格式
- 集成完整的同心圆提示词模板
- 可直接提交给Vertex AI的JSONL文件

#### Commit 4: 文件管理与输出系统
- [ ] **FC-04**: 实现输出目录管理
- [ ] 创建时间戳目录结构
- [ ] 生成处理清单和统计报告
- [ ] 完成端到端测试验证

**预期输出**:
- 完整的目录结构管理
- 批处理清单文件 `batch_manifest.json`
- 处理统计报告
- 可立即使用的批处理队列文件

## 技术规范

### 文件路径配置
```python
# 基础路径配置
BASE_PATH = "/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS"
DB_PATH = f"{BASE_PATH}/senseword-content-factory/01-EN/SQLite/senseword_content.db"
SCRIPTS_DIR = f"{BASE_PATH}/senseword-content-factory/01-EN/SQLite/workflows/01-单词过滤筛选优先级排序/scripts"
OUTPUT_DIR = f"{BASE_PATH}/senseword-content-factory/01-EN/SQLite/workflows/01-单词过滤筛选优先级排序/output"
```

### 批处理规范
- **批处理大小**: 100个单词/批次
- **文件命名**: `ai_audit_batch_{index:03d}.jsonl`
- **请求格式**: 符合Vertex AI批处理API v1规范
- **温度设置**: 0.7 (平衡创造性和一致性)
- **最大Token**: 60000 (支持大批量处理)

### 依赖项
```python
# 必需的Python包
- sqlite3 (内置)
- json (内置)  
- os (内置)
- datetime (内置)
- typing (内置)
```

## 验证标准

### 数据提取验证
- [ ] SQLite连接成功，无错误日志
- [ ] 提取的单词数量与数据库记录数一致
- [ ] 所有id-word对格式正确，无缺失数据

### 批处理生成验证  
- [ ] 批次分组正确，每批不超过100个单词
- [ ] JSONL文件格式符合Vertex AI规范
- [ ] 提示词正确嵌入每个请求中

### 输出质量验证
- [ ] 文件目录结构清晰，易于管理
- [ ] 清单文件包含完整的批处理信息
- [ ] 生成的文件可直接提交给Vertex AI处理

---

**下一步**: 开始实现FC-01数据库连接与单词提取模块
