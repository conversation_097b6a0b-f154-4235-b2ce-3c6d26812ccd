# KDD-014: 搜索API清理关键帧可视化

## 数据结构生命周期流程图

```mermaid
graph TD
    A[项目代码库状态] -->|FC-01| B[代码分析结果]
    B --> C{是否有外部引用?}
    C -->|是| D[❌ 停止清理<br/>需要手动处理引用]
    C -->|否| E[待删除文件列表]
    E -->|FC-02| F[文件删除执行]
    F --> G[清理完成状态]
    G --> H[✅ 代码库清洁完成]

    %% 数据结构关键帧
    A1[ProjectCodebase<br/>searchServiceFile<br/>relatedModelFiles<br/>documentationFiles] -.->|输入| A
    B1[CodeAnalysisResult<br/>filesToDelete<br/>filesToCheck<br/>dependencies] -.->|输出| B
    E1[FilesToDelete<br/>primaryFiles<br/>relatedFiles] -.->|输入| E
    G1[DeletionResult<br/>deletedFiles<br/>cleanupComplete<br/>codebaseStatus] -.->|输出| G

    %% 样式
    classDef input fill:#ffebee,stroke:#d32f2f,color:#000
    classDef output fill:#e8f5e8,stroke:#4caf50,color:#000
    classDef process fill:#e3f2fd,stroke:#2196f3,color:#000
    classDef decision fill:#fff3e0,stroke:#ff9800,color:#000
    classDef error fill:#ffcdd2,stroke:#f44336,color:#000
    classDef success fill:#c8e6c9,stroke:#4caf50,color:#000

    class A,A1,E,E1 input
    class B,B1,G,G1 output
    class F process
    class C decision
    class D error
    class H success
```

## 关键帧数据结构详解

### 关键帧 A: 项目代码库状态
- **类型**: 输入关键帧
- **数据结构**: ProjectCodebase
- **核心字段**:
  - `searchServiceFile`: 废弃的搜索服务文件路径
  - `relatedModelFiles`: 相关的数据模型文件
  - `documentationFiles`: 相关文档文件

### 关键帧 B: 代码分析结果
- **类型**: 输出关键帧
- **数据结构**: CodeAnalysisResult
- **核心字段**:
  - `filesToDelete`: 确认需要删除的文件列表
  - `filesToCheck`: 需要检查的文件列表
  - `dependencies`: 依赖关系分析结果

### 关键帧 E: 待删除文件列表
- **类型**: 输入关键帧
- **数据结构**: FilesToDelete
- **核心字段**:
  - `primaryFiles`: 主要删除文件（SearchService.swift）
  - `relatedFiles`: 相关的废弃文件

### 关键帧 G: 清理完成状态
- **类型**: 输出关键帧
- **数据结构**: DeletionResult
- **核心字段**:
  - `deletedFiles`: 已删除的文件列表
  - `cleanupComplete`: 清理是否完成
  - `codebaseStatus`: 代码库最终状态

## 分支处理说明

### 成功路径
1. 代码分析 → 无外部引用 → 执行删除 → 清理完成

### 失败路径
1. 代码分析 → 发现外部引用 → 停止清理 → 需要手动处理

## 清理范围确认

### 确认删除
- ✅ `iOS/Sources/Shared/Services/SearchService.swift` (约300行代码)
- ✅ 相关的废弃搜索数据模型（如果存在）

### 确认保留
- ✅ `LocalSearchService.swift` - 本地搜索实现
- ✅ `LocalSearchManager.swift` - 本地搜索管理器
- ✅ `LocalSearchGuide.md` - 本地搜索文档

### 后端状态
- ✅ `GET /api/v1/suggestions` 端点已确认未实现，无需清理