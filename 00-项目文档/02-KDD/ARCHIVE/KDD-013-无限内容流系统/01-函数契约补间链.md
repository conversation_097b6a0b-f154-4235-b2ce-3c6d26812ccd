# 需求："同义词页面无限内容流触发系统" 的技术方案蓝图 (V3.0 - 混合策略版)

## 0. 依赖关系与影响分析

- [重用] `WordService.swift`: 现有单词详情API服务，完全复用getWord(id:)方法
- [重用] `AudioService.swift`: 现有音频服务，复用preloadAudio(url:)方法
- [重用] `SynonymsCarouselComponent.swift`: 现有同义词组件，需要增强手势检测
- [重用] `WordCardView.swift`: 现有单词卡片UI组件，无需修改
- [修改] `WordPageContainer.swift`: 同义词页面需要集成新的无限内容流触发功能
- [新增] `InfiniteRecommendationBuilder.swift`: 智能推荐数组构建器（混合策略）
- [新增] `JITSinglePreloader.swift`: JIT单点预加载器
- [新增] `SeamlessContentStreamComponent.swift`: 无缝滑动切换组件

## 1. 项目文件结构概览 (Project File Structure Overview)

下方结构树清晰地展示了为实现本垂直切片，需要创建或修改的文件及其在项目中的位置。这为后续的开发任务提供了明确的物理路径指引。

```
iOS/
├── SensewordApp/
│   └── Views/
│       └── HorizontalStage/
│           ├── SynonymsCarouselComponent.swift    # [修改] 增强手势检测逻辑
│           └── WordPageContainer.swift            # [修改] 集成无限内容流触发
├── Packages/
│   └── UIComponents/
│       └── Sources/
│           └── UIComponents/
│               └── InfiniteContentStream/         # [新增] 无限内容流组件包
│                   ├── InfiniteRecommendationBuilder.swift  # [新增] 智能推荐构建器
│                   ├── JITSinglePreloader.swift             # [新增] JIT单点预加载器
│                   ├── SeamlessContentStreamComponent.swift # [新增] 无缝滑动组件
│                   ├── ExtendedSwipeDetector.swift          # [新增] 扩展滑动检测器
│                   └── StreamModels.swift                   # [新增] 数据模型定义
└── Tests/
    └── UIComponentsTests/
        └── InfiniteContentStreamTests/            # [新增] 无限内容流测试
            ├── InfiniteRecommendationBuilderTests.swift    # [新增] 推荐构建器测试
            ├── JITSinglePreloaderTests.swift               # [新增] 预加载器测试
            └── SeamlessContentStreamComponentTests.swift   # [新增] 组件测试
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/infinite-content-stream-trigger`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-infinite-stream-workspace
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-infinite-stream-workspace -b feature/infinite-content-stream-trigger dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(models): 创建无限内容流数据模型和枚举定义
- [ ] feat(detector): 实现ExtendedSwipeDetector扩展滑动检测器
- [ ] feat(builder): 实现InfiniteRecommendationBuilder智能推荐构建器
- [ ] feat(preloader): 实现JITSinglePreloader单点预加载器
- [ ] feat(component): 实现SeamlessContentStreamComponent无缝滑动组件
- [ ] feat(synonyms): 增强SynonymsCarouselComponent手势检测逻辑
- [ ] feat(integration): 集成到WordPageContainer同义词页面
- [ ] test(infinite-stream): 添加无限内容流系统单元测试

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 混合内容数组构建器

- 职责: 根据不同的流模式（每日一词/搜索/生词本），构建包含主词和一层级联词汇的混合内容数组
- 函数签名: `buildContentArray(mode: StreamMode, input: StreamInput) async throws -> [String]`
- 所在文件: `iOS/Packages/UIComponents/Sources/UIComponents/ContentStream/MixedContentArray.swift`

>>>>> 输入 (Input): StreamInput

根据不同模式的输入数据结构：

```swift
// 流模式枚举
enum StreamMode {
    case daily      // 每日一词模式
    case search     // 搜索模式
    case bookmark   // 生词本模式
}

// 统一输入接口
protocol StreamInput {}

// 每日一词输入
struct DailyWordInput: StreamInput {
    let dailyWord: String  // 来自 DailyWordService.getTodayWord()
}

// 搜索模式输入
struct SearchInput: StreamInput {
    let searchWord: String     // 用户搜索的单词
    let bookmarks: [String]    // 来自 BookmarkService.getUserBookmarks()
}

// 生词本模式输入
struct BookmarkInput: StreamInput {
    let bookmarks: [String]    // 来自 BookmarkService.getUserBookmarks()
}
```

<<<<< 输出 (Output): [String]

构建完成的混合内容数组，包含主词和一层级联词汇：

```swift
// 每日一词模式输出示例
// 输入: dailyWord = "serendipity"
// 输出: ["serendipity", "chance", "discovery", "luck", "fortune"]

// 搜索模式输出示例
// 输入: searchWord = "progressive", bookmarks = ["innovative", "sophisticated"]
// 输出: ["progressive", "advanced", "modern", "innovative", "creative", "novel", "sophisticated", "complex", "refined", "progressive", "forward", "evolving"]

// 生词本模式输出示例
// 输入: bookmarks = ["innovative", "sophisticated", "dynamic"]
// 输出: ["innovative", "creative", "novel", "sophisticated", "complex", "refined", "dynamic", "active", "energetic", "innovative", "creative", "novel"]
```

---

### [FC-02]: 简化JIT预加载器

- 职责: 实现极简的Just-In-Time预加载策略，利用用户1分钟阅读时间异步预加载下一个单词，提供本地缓存管理
- 函数签名: `preloadNext(word: String) async -> Void` 和 `getCachedWord(word: String) -> WordDTO?`
- 所在文件: `iOS/Packages/UIComponents/Sources/UIComponents/ContentStream/SimpleJITLoader.swift`

>>>>> 输入 (Input): String

需要预加载的单词字符串：

```swift
// 预加载输入 - 基于1分钟阅读时间的优化策略
let wordToPreload: String = "advanced"  // 数组中的下一个单词
// 预加载时机：用户开始阅读当前单词时立即触发
// 预加载窗口：1分钟足够完成网络请求和CDN缓存
```

<<<<< 输出 (Output): Void (异步) / WordDTO? (缓存获取)

预加载操作无直接返回值，但会更新本地缓存。缓存获取返回完整的单词数据：

```swift
// 预加载操作：无返回值，异步执行，优先使用CDN缓存
// preloadNext("advanced") -> Void
// 策略：CDN缓存命中 > 本地缓存 > 数据库查询

// 缓存获取返回：简化版
struct CachedWordData {
    let word: WordDTO?          // 完整单词数据，如果缓存命中
    let isReady: Bool          // 是否已预加载完成
}

// 本地缓存策略：
// - 内存缓存：最近10个单词
// - 磁盘缓存：最近100个单词
// - 网络异常时：优雅降级到本地缓存
```

---

### [FC-03]: 内容流协调器

- 职责: 作为整个内容流系统的主协调器，管理流模式切换、数组构建、预加载协调、用户导航等核心逻辑
- 函数签名: `initializeStream(mode: StreamMode, input: StreamInput) async throws -> Void` 和 `streamToNext() async throws -> Void`
- 所在文件: `iOS/Packages/UIComponents/Sources/UIComponents/ContentStream/ContentStreamCoordinator.swift`

>>>>> 输入 (Input): StreamMode + StreamInput

初始化时的模式和输入数据：

```swift
// 初始化输入
struct StreamInitializationInput {
    let mode: StreamMode
    let input: StreamInput
}

// 示例：每日一词模式初始化
let dailyInit = StreamInitializationInput(
    mode: .daily,
    input: DailyWordInput(dailyWord: "serendipity")
)

// 示例：搜索模式初始化
let searchInit = StreamInitializationInput(
    mode: .search,
    input: SearchInput(
        searchWord: "progressive",
        bookmarks: ["innovative", "sophisticated", "dynamic"]
    )
)
```

<<<<< 输出 (Output): ContentStreamState

协调器的完整状态，包含当前模式、数组、索引等信息：

```swift
// 内容流状态 - 精简版
struct ContentStreamState {
    let mode: StreamMode                    // 当前流模式
    let contentArray: [String]              // 混合内容数组
    let currentIndex: Int                   // 当前单词索引
    let isLoading: Bool                     // 是否正在加载
    let error: String?                      // 错误信息
    let nextWordReady: Bool                 // 下一个单词是否已预加载
}

// 导航操作输出 - 精简版
struct NavigationResult {
    let success: Bool                       // 导航是否成功
    let currentWord: String                // 当前单词
    let nextWordReady: Bool                // 下一个单词是否已预加载
}
```

---

### [FC-04]: 级联词汇提取器

- 职责: 从单词的完整数据中提取一层级联词汇（relatedConcepts + synonyms），严格遵循一层级联原则
- 函数签名: `extractCascadeWords(from wordData: WordDTO) -> [String]`
- 所在文件: `iOS/Packages/UIComponents/Sources/UIComponents/ContentStream/MixedContentArray.swift`

>>>>> 输入 (Input): WordDTO

完整的单词数据对象（来自现有WordService）：

```swift
// 输入的WordDTO结构（复用现有模型）
struct WordDTO {
    let word: String                        // 主单词
    let content: WordContent               // 单词内容
    let metadata: WordMetadata             // 元数据
    let pronunciations: [Pronunciation]    // 发音信息
}

// 关键的级联数据来源
struct WordMetadata {
    let relatedConcepts: [String]          // 相关概念词汇
    // 其他元数据...
}

struct WordContent {
    let synonyms: [Synonym]                // 同义词列表
    // 其他内容...
}

struct Synonym {
    let word: String                       // 同义词
    let similarity: Double                 // 相似度
}
```

<<<<< 输出 (Output): [String]

提取的一层级联词汇数组：

```swift
// 级联词汇提取结果 - 精简版（直接返回字符串数组）
// 示例输出：
// 输入单词: "serendipity"
// 输出: ["chance", "discovery", "luck", "fortune", "coincidence"]
// 来源: 合并 relatedConcepts + synonyms，去重后返回
```

---

### [FC-05]: 现有API服务集成器

- 职责: 封装对现有WordService、BookmarkService、DailyWordService的调用，提供统一的数据获取接口
- 函数签名: `getDailyWord() async throws -> String`, `getBookmarks() async throws -> [String]`, `getWordDetail(word: String) async throws -> WordDTO`
- 所在文件: `iOS/Packages/UIComponents/Sources/UIComponents/ContentStream/ContentStreamCoordinator.swift`

>>>>> 输入 (Input): Void / String

各种API调用的输入参数：

```swift
// 每日一词获取：无输入参数，调用公开端点
// GET /api/v1/daily-word -> 无需认证

// 生词本获取：无输入参数（使用当前用户会话）
// getBookmarks() -> 需要用户认证

// 单词详情获取：单词字符串
let wordId: String = "progressive"  // 目标单词ID
```

<<<<< 输出 (Output): String / [String] / WordDTO

各种API调用的输出数据 - 精简版：

```swift
// 每日一词输出 - 解析API响应获取单词
// API响应: {"word": "progressive", "date": "2025-06-25"}
// 函数返回: "progressive"

// 生词本输出 - 直接返回单词数组
// getBookmarks() -> [String] (如 ["innovative", "sophisticated"])

// 单词详情输出 - 直接返回WordDTO
// getWordDetail(word: String) -> WordDTO

// 错误处理 - 精简版（优先本地缓存降级）
enum ContentStreamError: Error {
    case dailyWordUnavailable              // 每日一词API不可用
    case bookmarksEmpty                    // 生词本为空
    case wordNotFound(String)              // 单词未找到且本地缓存无数据
    case networkError                      // 网络错误（自动降级到本地缓存）
}
```

---

### [FC-06]: 内容流UI视图控制器

- 职责: 提供SwiftUI界面，集成ContentStreamCoordinator，处理用户手势，显示单词卡片和流状态
- 函数签名: `body: some View` (SwiftUI视图) 和 `handleSwipeGesture(direction: SwipeDirection) async -> Void`
- 所在文件: `iOS/SensewordApp/ContentStreamView.swift`

>>>>> 输入 (Input): SwipeDirection / StreamMode

用户交互输入和初始化参数：

```swift
// 手势输入
enum SwipeDirection {
    case up         // 向上滑动 = 下一个单词
    case down       // 向下滑动 = 上一个单词（如果支持）
    case left       // 向左滑动 = 收藏操作
    case right      // 向右滑动 = 跳过操作
}

// 初始化输入
struct ContentStreamViewInput {
    let initialMode: StreamMode?           // 初始流模式（可选）
    let searchWord: String?                // 搜索词（搜索模式时）
    let showDebugInfo: Bool                // 是否显示调试信息
}
```

<<<<< 输出 (Output): SwiftUI View

SwiftUI视图状态和用户反馈 - 精简版：

```swift
// 视图状态 - 精简版
struct ContentStreamViewState {
    let isLoading: Bool                    // 是否正在加载
    let currentWord: String?               // 当前显示的单词
    let currentWordData: WordDTO?          // 当前单词的完整数据
    let streamMode: StreamMode             // 当前流模式
    let error: String?                     // 错误信息
}

// 手势处理结果 - 精简版
struct SwipeHandlingResult {
    let success: Bool                      // 手势处理是否成功
    let action: SwipeAction                // 执行的动作
}

enum SwipeAction {
    case nextWord                          // 切换到下一个单词
    case bookmarkWord                      // 收藏当前单词
    case skipWord                          // 跳过当前单词
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/WordService.swift
iOS/Packages/SharedModels/Sources/SharedModels/APIService.swift
iOS/Packages/SharedModels/Sources/SharedModels/WordModels.swift
iOS/Packages/SharedModels/Sources/SharedModels/SessionManager.swift
iOS/SensewordApp/WordLearningCoordinator.swift
iOS/Sources/Learning/WordLearningCoordinator.swift
iOS/SensewordApp/LearningView.swift
iOS/Packages/UIComponents/Sources/UIComponents/WordCard/WordCardView.swift
cloudflare/workers/api/src/services/word.service.ts
cloudflare/workers/api/src/services/bookmark.service.ts
cloudflare/workers/api/src/services/daily-word.service.ts
cloudflare/workers/api/src/index.ts
</context_files>

## 6. 核心业务流程伪代码

```swift
// 内容流系统主流程
async function initializeContentStream(mode: StreamMode, input: StreamInput) {
    // [FC-01] 构建混合内容数组
    let contentArray = await buildContentArray(mode: mode, input: input)

    // [FC-02] 初始化JIT预加载器
    let jitLoader = SimpleJITLoader()

    // [FC-03] 创建协调器状态
    let coordinator = ContentStreamCoordinator(
        mode: mode,
        contentArray: contentArray,
        jitLoader: jitLoader
    )

    // 预加载前几个单词
    await preloadInitialWords(coordinator)

    return coordinator
}

async function streamToNextWord(coordinator: ContentStreamCoordinator) {
    // 检查是否到达数组末尾
    if coordinator.isAtEnd() {
        await switchToNextMode(coordinator)
        return
    }

    // [FC-03] 导航到下一个单词
    coordinator.moveToNext()

    // [FC-02] 利用1分钟阅读时间预加载下一个单词
    // 用户开始阅读当前单词时，异步预加载下一个单词
    // 1分钟的阅读时间足够完成网络请求和CDN缓存
    let nextWord = coordinator.getNextWord()
    if let nextWord = nextWord {
        Task {
            await jitLoader.preloadNext(word: nextWord)
        }
    }

    // 更新UI状态
    coordinator.notifyStateChange()
}

async function buildContentArray(mode: StreamMode, input: StreamInput) -> [String] {
    switch mode {
    case .daily:
        // [FC-05] 调用每日一词API: GET /api/v1/daily-word
        let dailyWordResponse = await fetch("https://api.senseword.app/api/v1/daily-word")
        let dailyWord = dailyWordResponse.word  // 解析 {"word": "progressive", "date": "2025-06-25"}

        // [FC-05] 获取单词详情
        let wordData = await getWordDetail(word: dailyWord)

        // [FC-04] 提取级联词汇
        let cascadeWords = extractCascadeWords(from: wordData)

        return [dailyWord] + cascadeWords

    case .search:
        let searchInput = input as! SearchInput
        var array: [String] = []

        // 搜索词 + 级联
        let searchWordData = await getWordDetail(word: searchInput.searchWord)
        let searchCascade = extractCascadeWords(from: searchWordData)
        array += [searchInput.searchWord] + searchCascade

        // 每个生词 + 级联
        for bookmark in searchInput.bookmarks {
            let bookmarkData = await getWordDetail(word: bookmark)
            let bookmarkCascade = extractCascadeWords(from: bookmarkData)
            array += [bookmark] + bookmarkCascade
        }

        return array

    case .bookmark:
        let bookmarkInput = input as! BookmarkInput
        var array: [String] = []

        // 为每个生词构建级联块
        for bookmark in bookmarkInput.bookmarks {
            let bookmarkData = await getWordDetail(word: bookmark)
            let bookmarkCascade = extractCascadeWords(from: bookmarkData)
            array += [bookmark] + bookmarkCascade
        }

        return array
    }
}

async function handleUserSwipe(direction: SwipeDirection, coordinator: ContentStreamCoordinator) {
    switch direction {
    case .up:
        // 向上滑动 = 下一个单词
        await streamToNextWord(coordinator)

    case .left:
        // 向左滑动 = 收藏当前单词
        let currentWord = coordinator.getCurrentWord()
        await bookmarkWord(currentWord)

    case .right:
        // 向右滑动 = 跳过当前单词
        await streamToNextWord(coordinator)

    default:
        // 其他手势暂不处理
        break
    }
}
```