# SenseWord数据库v2.0至v3.0迁移系统实现

## 摘要

本文档记录了SenseWord数据库从单表架构(v2.0)迁移到双表架构(v3.0)的完整实现过程。该迁移的核心目标是将音频资产管理从单一的contentJson字段中分离出来，建立独立的TTS资产表，并为每个需要语音合成的文本段分配唯一的UUID标识符。

## 用户故事 (User Stories)

### 主要用户价值
1. **音频资产管理优化**: 开发者能够独立管理TTS音频资产，不再依赖复杂的JSON解析
2. **性能提升**: 通过表结构优化，提升音频资产的查询和管理效率
3. **扩展性增强**: 为未来的音频功能扩展（如多语言TTS、音频质量管理）奠定基础
4. **数据完整性**: 通过UUID确保音频资产与文本内容的精确对应关系

### 开发者价值
1. **简化开发**: TTS相关功能开发不再需要复杂的JSON操作
2. **维护便利**: 音频资产状态管理变得直观和高效
3. **调试友好**: 独立的TTS表使问题定位和数据分析更加容易

## 功能描述 (Feature Description)

### 核心功能边界
本垂直切片实现了以下主要功能：

1. **数据库架构重构**
   - 将`words_for_publish`表分解为`words`和`tts_assets`两个表
   - 保持所有现有字段的完整性，包括AI审核相关字段

2. **UUID注入系统**
   - 为`phoneticSymbols`中的音标符号添加UUID
   - 为`usageExamples`中的`learningLanguage`字段添加UUID
   - 为`phraseBreakdown`中的`phrase`字段添加UUID

3. **JSON结构重构**
   - 将文本字符串转换为`{text: "原文本", ttsId: "uuid"}`对象
   - 保持JSON结构的向后兼容性

4. **数据完整性验证**
   - 提供完整的迁移验证工具
   - 确保数据迁移的准确性和完整性

## 背景

### 技术背景
原有的v2.0架构将所有内容存储在单一的`words_for_publish`表中，音频相关信息嵌入在`contentJson`字段内。这种设计在项目初期简化了开发，但随着功能扩展，暴露出以下问题：

1. **音频资产管理复杂**: 需要解析JSON才能获取音频相关信息
2. **查询性能限制**: 无法对音频资产建立有效索引
3. **扩展性受限**: 新增音频功能需要修改复杂的JSON结构
4. **维护困难**: 音频状态管理分散在JSON中，难以统一处理

### 业务需求
随着SenseWord应用的发展，TTS功能变得越来越重要，需要：
- 支持批量音频生成和管理
- 提供音频资产状态跟踪
- 优化音频相关查询性能
- 为多语言TTS支持做准备

## 架构设计

### 数据库架构变更

```mermaid
graph TB
    subgraph "v2.0 单表架构"
        A[words_for_publish]
        A --> B[contentJson包含所有音频信息]
    end
    
    subgraph "v3.0 双表架构"
        C[words表]
        D[tts_assets表]
        C --> E[重构后的contentJson]
        E --> F[包含ttsId引用]
        D --> G[独立的音频资产管理]
        F -.-> G
    end
    
    A ==> C
    B ==> D
```

### 核心概念图式

#### 中心概念
- **UUID驱动的资产管理**: 每个需要TTS的文本段都有唯一标识符
- **表结构分离**: 内容与音频资产的物理分离
- **引用完整性**: 通过UUID维护内容与音频的关联关系

#### 核心要素
1. **UUID生成策略**: 使用UUID v4确保全局唯一性
2. **JSON重构规则**: 文本字符串 → {text, ttsId}对象
3. **数据迁移原则**: 零数据丢失，完整性验证
4. **向后兼容性**: 保持现有API的兼容性

## 实现原理

### 思维方式
采用**渐进式迁移模式**来实现目标：

1. **分析阶段**: 深入理解现有数据结构和业务逻辑
2. **设计阶段**: 设计新的表结构和数据转换规则
3. **实现阶段**: 编写迁移脚本和验证工具
4. **验证阶段**: 确保数据完整性和功能正确性

### 核心算法

#### UUID注入算法
```python
def inject_uuid_to_text_field(field_value, tts_records, language):
    """
    为文本字段注入UUID的核心算法
    
    输入: 文本字符串
    输出: {text: 原文本, ttsId: UUID}对象
    副作用: 向tts_records添加新记录
    """
    if isinstance(field_value, str):
        tts_id = generate_uuid()
        tts_records.append((tts_id, field_value, language, 'pending', None))
        return {"text": field_value, "ttsId": tts_id}
    return field_value
```

#### JSON遍历策略
使用**深度优先遍历**策略处理嵌套的JSON结构：
1. 识别目标字段（phoneticSymbols, learningLanguage, phrase）
2. 应用UUID注入算法
3. 收集TTS记录用于批量插入

## 详细流程分解

### 步骤1: 环境准备和验证
```bash
# 检查源数据库存在性
# 创建目标数据库
# 验证Python依赖包
```

### 步骤2: 表结构创建
```sql
-- 创建words表（保持所有原有字段，包括AI审核字段）
CREATE TABLE IF NOT EXISTS words (
    id INTEGER PRIMARY KEY,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL,
    scaffoldingLanguage TEXT NOT NULL,
    contentJson TEXT NOT NULL,                    -- 重构后的JSON
    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
    contentVersion TEXT DEFAULT 'v3.0',          -- 更新为v3.0
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    partsOfSpeech TEXT,
    ttsStatus TEXT DEFAULT 'pending',
    aiAuditScore REAL,                           -- AI审核评分
    aiAuditShouldRegenerate INTEGER,             -- 是否需要重新生成
    aiAuditComment TEXT,                         -- AI审核评论
    frequency TEXT,
    priorityScore INTEGER DEFAULT 0,             -- 优先级分数
    auditStatus TEXT DEFAULT 'pending_review',
    aiAuditShouldRemove INTEGER DEFAULT 0,       -- 是否应该移除
    culturalRiskRegions TEXT DEFAULT '[]',       -- 文化风险区域
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
);

-- 创建tts_assets表（音频资产管理）
CREATE TABLE IF NOT EXISTS tts_assets (
    ttsId TEXT PRIMARY KEY,                      -- UUID主键
    text_to_speak TEXT NOT NULL,                 -- 需要语音合成的文本
    learningLanguage TEXT NOT NULL,              -- 学习语言
    status TEXT NOT NULL DEFAULT 'pending',     -- 音频状态
    audio_url TEXT,                              -- 音频文件URL
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP -- 创建时间
);
```

### 步骤3: 数据转换和迁移
```python
# 伪代码流程
for each record in words_for_publish:
    content_json = parse_json(record.contentJson)
    tts_records = []
    
    # 处理音标
    process_phonetic_symbols(content_json, tts_records)
    
    # 处理使用示例
    process_usage_examples(content_json, tts_records)
    
    # 插入到新表
    insert_into_words_table(record, updated_content_json)
    batch_insert_tts_assets(tts_records)
```

### 步骤4: 数据验证
```python
# 验证项目
- 记录数量一致性检查
- UUID结构正确性验证
- TTS资产完整性验证
- JSON格式正确性检查
```

## 具体实施步骤

### 1. 脚本开发
创建了两个核心脚本，位于 `senseword-content-factory/01-EN/SQLite/workflows/04-创建 AI 自动审核批处理任务/scripts/` 目录：

#### 012_migrate_v2_to_v3.py - 主迁移脚本
**核心功能**：
- **数据库连接管理**: 同时连接源数据库和目标数据库
- **表结构创建**: 自动创建words和tts_assets表，包含完整的字段定义
- **JSON解析和重构**: 深度遍历contentJson，识别需要TTS的文本段
- **UUID生成和注入**: 为每个文本段生成UUID v4，并重构JSON结构
- **批量数据插入**: 使用事务确保数据一致性，支持批量插入优化

**处理的字段**：
```python
# 音标处理
phoneticSymbols[].symbol → phoneticSymbols[].{symbol, ttsId}

# 例句处理
usageExamples[].examples[].learningLanguage → {text, ttsId}

# 短语分解处理
usageExamples[].examples[].phraseBreakdown[].phrase → {text, ttsId}
```

#### 013_verify_v3_migration.py - 验证脚本
**验证项目**：
- **文件存在性**: 检查源数据库和目标数据库文件
- **表结构完整性**: 验证所有必需字段是否存在
- **数据数量一致性**: 确保迁移前后记录数量匹配
- **UUID结构正确性**: 抽样检查JSON结构转换是否正确
- **TTS资产完整性**: 验证contentJson中的ttsId与tts_assets表的一致性

### 2. 功能特性

#### 数据安全保障
- **事务安全**: 使用SQLite事务，失败时自动回滚
- **覆盖确认**: 目标数据库存在时需要用户确认
- **数据备份建议**: 脚本提醒用户备份原数据库

#### 用户体验优化
- **进度显示**: 使用tqdm库提供实时进度条和ETA
- **结构化日志**: 采用 `[文件名][函数名_序号]` 格式的日志标记
- **详细报告**: 生成包含统计信息和建议的Markdown报告

#### 错误处理机制
```python
# 示例错误处理结构
try:
    # 迁移逻辑
    conn_dest.execute('BEGIN TRANSACTION;')
    # ... 数据处理
    conn_dest.execute('COMMIT;')
except Exception as e:
    print(f"ERROR - [migrate_v2_to_v3][function_name] 错误描述: {e}")
    conn_dest.execute('ROLLBACK;')
    return False
finally:
    # 清理资源
    if conn_source: conn_source.close()
    if conn_dest: conn_dest.close()
```

### 3. 验证机制

#### 多层验证策略
1. **结构验证**: 检查表结构和字段完整性
2. **数量验证**: 确保数据迁移的完整性
3. **内容验证**: 抽样检查JSON结构转换
4. **关联验证**: 验证UUID引用的一致性

#### 统计报告生成
验证脚本自动生成详细报告，包含：
- 数据迁移统计（记录数、TTS资产数）
- 按语言分类的TTS资产统计
- 验证结果清单
- 性能优化建议

#### 问题诊断能力
- **自动识别**: 检测常见的迁移问题
- **详细报告**: 提供具体的错误位置和原因
- **修复建议**: 针对发现的问题提供解决方案

## 使用示例

### 基本迁移流程
```bash
# 1. 执行迁移
cd senseword-content-factory/01-EN/SQLite/workflows/04-创建\ AI\ 自动审核批处理任务/scripts
python3 012_migrate_v2_to_v3.py

# 2. 验证结果
python3 013_verify_v3_migration.py

# 3. 查看验证报告
cat migration_verification_report_*.md
```

### 迁移前后对比

#### 真实数据示例 - 单词 "aback"

**v2.0 结构（部分）**：
```json
{
  "content": {
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/əˈbæk/"
      },
      {
        "type": "NAmE",
        "symbol": "/əˈbæk/"
      }
    ],
    "usageExamples": [
      {
        "category": "表示突然的惊讶或震惊",
        "examples": [
          {
            "learningLanguage": "She was completely taken aback by his sudden proposal.",
            "translation": "她被他突然的求婚完全惊呆了。",
            "phraseBreakdown": [
              {
                "phrase": "She was completely taken aback",
                "translation": "她完全惊呆了"
              },
              {
                "phrase": "by his sudden proposal.",
                "translation": "被他突然的求婚。"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

**v3.0 结构（转换后）**：
```json
{
  "content": {
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/əˈbæk/",
        "ttsId": "550e8400-e29b-41d4-a716-446655440001"
      },
      {
        "type": "NAmE",
        "symbol": "/əˈbæk/",
        "ttsId": "550e8400-e29b-41d4-a716-446655440002"
      }
    ],
    "usageExamples": [
      {
        "category": "表示突然的惊讶或震惊",
        "examples": [
          {
            "learningLanguage": {
              "text": "She was completely taken aback by his sudden proposal.",
              "ttsId": "550e8400-e29b-41d4-a716-446655440003"
            },
            "translation": "她被他突然的求婚完全惊呆了。",
            "phraseBreakdown": [
              {
                "phrase": {
                  "text": "She was completely taken aback",
                  "ttsId": "550e8400-e29b-41d4-a716-446655440004"
                },
                "translation": "她完全惊呆了"
              },
              {
                "phrase": {
                  "text": "by his sudden proposal.",
                  "ttsId": "550e8400-e29b-41d4-a716-446655440005"
                },
                "translation": "被他突然的求婚。"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

**对应的tts_assets表记录**：
```sql
INSERT INTO tts_assets VALUES
('550e8400-e29b-41d4-a716-446655440001', '/əˈbæk/', 'en', 'pending', NULL, '2025-07-11 10:30:00'),
('550e8400-e29b-41d4-a716-446655440002', '/əˈbæk/', 'en', 'pending', NULL, '2025-07-11 10:30:00'),
('550e8400-e29b-41d4-a716-446655440003', 'She was completely taken aback by his sudden proposal.', 'en', 'pending', NULL, '2025-07-11 10:30:00'),
('550e8400-e29b-41d4-a716-446655440004', 'She was completely taken aback', 'en', 'pending', NULL, '2025-07-11 10:30:00'),
('550e8400-e29b-41d4-a716-446655440005', 'by his sudden proposal.', 'en', 'pending', NULL, '2025-07-11 10:30:00');
```

## 下一步完善计划

### 短期优化 (1-2周)
1. **性能优化**
   - 为tts_assets表添加索引
   - 优化批量插入性能
   - 添加查询性能监控

2. **功能增强**
   - 支持增量迁移模式
   - 添加数据回滚功能
   - 实现迁移进度恢复

### 中期扩展 (1个月)
1. **TTS集成**
   - 实现TTS服务调用接口
   - 添加音频文件管理功能
   - 支持音频质量评估

2. **监控和维护**
   - 添加数据库健康检查
   - 实现自动化测试套件
   - 建立性能基准测试

### 长期规划 (3个月)
1. **多语言支持**
   - 扩展TTS资产表支持多语言
   - 实现语言特定的音频处理
   - 添加语音质量评估机制

2. **云端集成**
   - 支持云端音频存储
   - 实现CDN分发优化
   - 添加音频缓存策略

---

*文档版本: v1.0*  
*最后更新: 2025-07-11*  
*作者: SenseWord开发团队*
