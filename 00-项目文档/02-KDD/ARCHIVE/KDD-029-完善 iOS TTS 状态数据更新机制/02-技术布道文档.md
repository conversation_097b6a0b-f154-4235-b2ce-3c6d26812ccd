# 技术布道文档：回调机制 vs 直接状态查询的架构设计

## 问题背景

在实现 iOS TTS 状态数据更新机制时，我们面临一个关键的架构设计选择：

**方案A（回调机制）**: AudioStatusManager 在 P1 完成时通过回调通知 WordResultView
**方案B（直接查询）**: WordResultView 直接监听 AudioStatusManager 状态，自己获取数据

## 回调机制深度解析

### 1. 回调的定义位置和执行者

#### 定义位置
```swift
// 在 AudioStatusManager.swift 中定义
class AudioStatusManager: ObservableObject {
    /// P1阶段完成回调
    var onP1Completed: ((String, String) -> Void)?
}
```

#### 设置位置
```swift
// 在 WordResultView.swift 中设置
private func startAudioStatusMonitoring() {
    audioStatusManager.onP1Completed = { [weak self] word, language in
        Task { @MainActor in
            await self?.preloadUpdatedAudioData()
        }
    }
}
```

#### 执行者
```swift
// 在 AudioStatusManager.performP1Poll() 中执行
if audioStatus.audioStatus == .completed {
    // AudioStatusManager 执行回调
    onP1Completed?(word, language)
}
```

### 2. 为什么不让 WordResultView 直接查询状态？

#### 架构原则：单一职责原则 (SRP)

**AudioStatusManager 的职责**:
- ✅ 管理轮询逻辑
- ✅ 维护音频状态
- ✅ 通知状态变化
- ❌ 不应该知道谁在使用这些状态

**WordResultView 的职责**:
- ✅ 管理UI展示
- ✅ 处理用户交互
- ✅ 响应数据变化
- ❌ 不应该主动轮询外部状态

#### 控制反转 (IoC) 原则

```swift
// ❌ 错误方式：WordResultView 主动查询
.onReceive(Timer.publish(every: 5, on: .main, in: .common).autoconnect()) { _ in
    if audioStatusManager.status == .p1Completed {
        // WordResultView 需要知道轮询逻辑
        Task { await preloadUpdatedAudioData() }
    }
}

// ✅ 正确方式：AudioStatusManager 主动通知
audioStatusManager.onP1Completed = { [weak self] word, language in
    // WordResultView 只需要响应通知
    Task { @MainActor in await self?.preloadUpdatedAudioData() }
}
```

### 3. 回调机制的架构优势

#### 3.1 解耦合 (Decoupling)
```swift
// AudioStatusManager 不需要知道有多少个视图在监听
class AudioStatusManager {
    var onP1Completed: ((String, String) -> Void)?
    
    // 可以轻松扩展为多个回调
    var onP1CompletedCallbacks: [((String, String) -> Void)] = []
}
```

#### 3.2 可测试性 (Testability)
```swift
// 测试 AudioStatusManager 时，可以轻松模拟回调
func testP1Completion() {
    let manager = AudioStatusManager()
    var callbackTriggered = false
    
    manager.onP1Completed = { word, language in
        callbackTriggered = true
    }
    
    // 模拟 P1 完成
    // ...
    
    XCTAssertTrue(callbackTriggered)
}
```

#### 3.3 性能优化 (Performance)
```swift
// 回调机制：事件驱动，零轮询开销
onP1Completed?(word, language) // 只在状态变化时执行一次

// 直接查询：需要持续轮询，消耗资源
.onReceive(Timer.publish(every: 1, on: .main, in: .common)) { _ in
    checkStatus() // 每秒执行一次，即使状态没变化
}
```

### 4. 实际场景对比

#### 场景：多个视图需要响应 P1 完成事件

**回调方式**:
```swift
// AudioStatusManager 只需要维护一个回调列表
var p1CompletionCallbacks: [P1CompletionCallback] = []

// 各个视图独立注册
wordResultView.register()
controlledWordView.register()
audioPlayerView.register()
```

**直接查询方式**:
```swift
// 每个视图都需要自己的轮询逻辑
// WordResultView
.onReceive(timer1) { checkP1Status() }

// ControlledWordView  
.onReceive(timer2) { checkP1Status() }

// AudioPlayerView
.onReceive(timer3) { checkP1Status() }
```

### 5. 回调机制的最佳实践

#### 5.1 内存管理
```swift
// ✅ 使用弱引用避免循环引用
audioStatusManager.onP1Completed = { [weak self] word, language in
    await self?.preloadUpdatedAudioData()
}

// ✅ 在视图消失时清理回调
.onDisappear {
    audioStatusManager.onP1Completed = nil
}
```

#### 5.2 线程安全
```swift
// ✅ 确保回调在主线程执行
onP1Completed?(word, language)

// ✅ 在回调中使用 @MainActor
audioStatusManager.onP1Completed = { [weak self] word, language in
    Task { @MainActor in
        await self?.preloadUpdatedAudioData()
    }
}
```

#### 5.3 错误处理
```swift
// ✅ 回调中的错误不应该影响调用者
private func triggerP1Completion(word: String, language: String) {
    do {
        onP1Completed?(word, language)
    } catch {
        NSLog("❌ P1完成回调执行失败: \(error)")
        // 不重新抛出错误，避免影响 AudioStatusManager
    }
}
```

## 总结：为什么选择回调机制

1. **职责清晰**: AudioStatusManager 专注状态管理，WordResultView 专注UI响应
2. **性能优越**: 事件驱动比轮询查询更高效
3. **扩展性强**: 可以轻松支持多个监听者
4. **测试友好**: 便于单元测试和集成测试
5. **内存安全**: 通过弱引用避免循环引用

回调机制体现了"好莱坞原则"：**Don't call us, we'll call you**
- AudioStatusManager: "不要来问我状态，我会在合适的时候通知你"
- WordResultView: "我不主动查询，我只响应通知"

这种设计让系统更加健壮、高效和可维护。
