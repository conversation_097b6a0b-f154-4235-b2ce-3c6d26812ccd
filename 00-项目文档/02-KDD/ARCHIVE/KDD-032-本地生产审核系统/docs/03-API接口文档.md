# SenseWord ContentHub - API接口文档

## 📋 API概览

### 基础信息
- **Base URL**: `http://localhost:3000/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 无 (本地部署)

### 通用响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;        // 请求是否成功
  data?: T;               // 响应数据
  error?: string;         // 错误信息
  message?: string;       // 提示信息
}
```

### 分页响应格式
```typescript
interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;         // 当前页码
    limit: number;        // 每页数量
    total: number;        // 总记录数
    totalPages: number;   // 总页数
  };
}
```

## 🔍 单词定义管理 API

### 1. 获取单词列表
```http
GET /api/v1/definitions
```

#### 查询参数
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 20 | 每页数量 |
| search | string | 否 | - | 搜索关键词 |
| frequency | string | 否 | - | 频率筛选 (High/Medium/Low/Rare) |
| auditStatus | string | 否 | - | 审核状态筛选 |
| sortBy | string | 否 | word | 排序字段 |
| sortOrder | string | 否 | asc | 排序方向 (asc/desc) |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": 1,
        "word": "Amy",
        "learningLanguage": "en",
        "scaffoldingLanguage": "zh",
        "definitionJson": "{\"definitions\":[...]}",
        "frequency": "High",
        "priorityScore": 8.5,
        "auditStatus": "pending_review",
        "updatedAt": "2025-01-07T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 62815,
      "totalPages": 3141
    }
  }
}
```

### 2. 获取单个单词详情
```http
GET /api/v1/definitions/:id
```

#### 路径参数
| 参数 | 类型 | 说明 |
|------|------|------|
| id | number | 单词ID |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "word": "Amy",
    "learningLanguage": "en",
    "scaffoldingLanguage": "zh",
    "definitionJson": "{\"definitions\":[{\"partOfSpeech\":\"noun\",\"definition\":\"A female given name\"}]}",
    "frequency": "High",
    "priorityScore": 8.5,
    "auditStatus": "pending_review",
    "updatedAt": "2025-01-07T10:00:00.000Z"
  }
}
```

### 3. 更新审核状态
```http
PATCH /api/v1/definitions/:id/audit-status
```

#### 请求体
```json
{
  "auditStatus": "approved"  // pending_review | approved | rejected | in_translation
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "审核状态更新成功"
}
```

### 4. 搜索单词
```http
GET /api/v1/definitions/search
```

#### 查询参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| limit | number | 否 | 搜索结果数量限制 |

#### 响应示例
```json
{
  "success": true,
  "data": [
    {
      "word": "Amy",
      "frequency": "High",
      "priorityScore": 8.5,
      "auditStatus": "pending_review"
    }
  ]
}
```

## 📝 例句管理 API

### 1. 获取单词例句
```http
GET /api/v1/definitions/:word/examples
```

#### 路径参数
| 参数 | 类型 | 说明 |
|------|------|------|
| word | string | 单词文本 |

#### 响应示例
```json
{
  "success": true,
  "data": {
    "id": 1,
    "word": "Amy",
    "learningLanguage": "en",
    "scaffoldingLanguage": "zh",
    "examplesJson": "{\"examples\":[{\"sentence\":\"Amy is a beautiful name.\",\"translation\":\"Amy是一个美丽的名字。\"}]}",
    "auditStatus": "pending_review",
    "ttsStatus": "pending",
    "updatedAt": "2025-01-07T10:00:00.000Z"
  }
}
```

### 2. 更新例句审核状态
```http
PATCH /api/v1/definitions/:word/examples/audit-status
```

#### 请求体
```json
{
  "auditStatus": "approved"  // pending_review | approved | rejected | in_translation
}
```

#### 响应示例
```json
{
  "success": true,
  "message": "例句审核状态更新成功"
}
```

## 📊 统计信息 API

### 1. 获取系统统计
```http
GET /api/v1/definitions/stats
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "stats": {
      "total": 62815,
      "pending_review": 58000,
      "approved": 4500,
      "rejected": 300,
      "in_translation": 15
    },
    "frequency": {
      "High": 14324,
      "Medium": 29000,
      "Low": 14117,
      "Rare": 5374
    },
    "priority": {
      "high": 15000,
      "medium": 35000,
      "low": 12815
    }
  }
}
```

### 2. 获取TTS统计
```http
GET /api/v1/definitions/tts-stats
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "pending": 62815,
    "processing": 0,
    "completed": 0,
    "failed": 0,
    "skipped": 0
  }
}
```

### 3. 获取发布统计
```http
GET /api/v1/definitions/publish-stats
```

#### 响应示例
```json
{
  "success": true,
  "data": {
    "pending_upload": 0,
    "uploading": 0,
    "published": 0,
    "failed": 0,
    "outdated": 0
  }
}
```

## 🔧 数据类型定义

### Definition 类型
```typescript
interface Definition {
  id: number;
  word: string;
  learningLanguage: string;
  scaffoldingLanguage: string;
  definitionJson: string;
  frequency: 'High' | 'Medium' | 'Low' | 'Rare';
  priorityScore: number;
  auditStatus: 'pending_review' | 'approved' | 'rejected' | 'in_translation';
  updatedAt: string;
}
```

### ExampleSentence 类型
```typescript
interface ExampleSentence {
  id: number;
  word: string;
  learningLanguage: string;
  scaffoldingLanguage: string;
  examplesJson: string;
  auditStatus: 'pending_review' | 'approved' | 'rejected' | 'in_translation';
  ttsStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
  updatedAt: string;
}
```

### FilterParams 类型
```typescript
interface FilterParams {
  frequency?: string;
  auditStatus?: string;
  ttsStatus?: string;
  learningLanguage?: string;
  scaffoldingLanguage?: string;
}
```

## ❌ 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": "错误描述信息"
}
```

### 常见错误码
| HTTP状态码 | 错误类型 | 说明 |
|------------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误示例
```json
{
  "success": false,
  "error": "单词不存在"
}
```

## 🧪 API测试示例

### 使用curl测试
```bash
# 获取单词列表
curl "http://localhost:3000/api/v1/definitions?page=1&limit=5"

# 获取单个单词
curl "http://localhost:3000/api/v1/definitions/1"

# 更新审核状态
curl -X PATCH "http://localhost:3000/api/v1/definitions/1/audit-status" \
  -H "Content-Type: application/json" \
  -d '{"auditStatus": "approved"}'

# 获取例句
curl "http://localhost:3000/api/v1/definitions/Amy/examples"

# 获取统计信息
curl "http://localhost:3000/api/v1/definitions/stats"
```

### 使用JavaScript测试
```javascript
// 获取单词列表
const response = await fetch('/api/v1/definitions?page=1&limit=10');
const data = await response.json();

// 更新审核状态
const updateResponse = await fetch('/api/v1/definitions/1/audit-status', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    auditStatus: 'approved'
  })
});
```

## 📝 开发注意事项

### 1. 数据验证
- 所有输入参数都会进行类型和格式验证
- auditStatus只接受预定义的枚举值
- 分页参数会自动校正到合理范围

### 2. 性能考虑
- 大数据量查询会自动分页
- 建议使用合适的limit值避免超时
- 复杂查询可能需要较长响应时间

### 3. 并发处理
- 支持多用户同时操作
- 数据更新采用乐观锁机制
- 避免长时间占用数据库连接

---

**文档版本**: v1.0  
**最后更新**: 2025-01-07  
**API版本**: v1  
**维护状态**: 活跃开发中
