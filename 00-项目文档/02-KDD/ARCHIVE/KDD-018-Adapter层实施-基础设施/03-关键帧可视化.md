# KDD-018 iOS Adapter层基础设施 - 关键帧可视化

## 1. 网络基础设施架构概览

```mermaid
graph TB
    subgraph "iOS App层"
        UI[SwiftUI Views]
        VM[ViewModels]
    end
    
    subgraph "网络基础设施层"
        AC[AdapterContainer<br/>依赖注入容器]
        API1[AuthAPIClient<br/>认证服务客户端]
        API2[MainAPIClient<br/>主API客户端]
        CONFIG[APIConfig<br/>认证配置管理]
        ERROR[APIError<br/>统一错误处理]
    end
    
    subgraph "测试基础设施层"
        MOCK[MockAPIClient<br/>测试客户端]
        TEST[XCTest<br/>测试用例]
    end
    
    subgraph "外部服务"
        AUTH[auth.senseword.app]
        MAIN[api.senseword.app]
    end
    
    UI --> VM
    VM --> AC
    AC --> API1
    AC --> API2
    API1 --> CONFIG
    API2 --> CONFIG
    API1 --> ERROR
    API2 --> ERROR
    API1 --> AUTH
    API2 --> MAIN
    
    TEST --> MOCK
    MOCK --> ERROR
    
    style AC fill:#e1f5fe
    style CONFIG fill:#f3e5f5
    style ERROR fill:#ffebee
    style MOCK fill:#e8f5e8
```

## 2. 函数契约关键帧生命周期

### 2.1 [FC-01] APIClient请求处理流程

```mermaid
sequenceDiagram
    participant App as iOS App
    participant Client as APIClient
    participant Config as APIConfig
    participant Server as API Server
    participant Error as APIError
    
    Note over App, Error: HTTP请求完整生命周期
    
    App->>Client: request<T>(endpoint, method, headers, body)
    Note right of App: 输入关键帧：<br/>泛型请求参数
    
    Client->>Client: 构建URL(baseURL + endpoint)
    alt URL无效
        Client->>Error: throw APIError.invalidURL
        Error->>App: 错误关键帧
    end
    
    Client->>Client: 创建URLRequest
    Client->>Client: 设置HTTP头部
    Client->>Server: URLSession.data(for: request)
    
    alt 网络错误
        Server->>Error: 网络连接失败
        Error->>Client: networkError(Error)
        Client->>App: 错误关键帧
    end
    
    Server->>Client: (Data, HTTPURLResponse)
    Note right of Server: 响应关键帧：<br/>原始数据+状态码
    
    alt HTTP状态码检查
        Client->>Client: 验证HTTPURLResponse
        alt 401
            Client->>Error: throw APIError.unauthorized
            Error->>App: 认证错误关键帧
        else 403
            Client->>Error: throw APIError.invalidAPIKey
            Error->>App: API密钥错误关键帧
        else 500+
            Client->>Error: throw APIError.serverError(code, message)
            Error->>App: 服务器错误关键帧
        end
    end
    
    Client->>Client: JSONDecoder().decode(T.self, from: data)
    alt JSON解析失败
        Client->>Error: throw APIError.decodingError(Error)
        Error->>App: 解析错误关键帧
    end
    
    Client->>App: return T
    Note left of App: 成功关键帧：<br/>强类型对象
```

### 2.2 [FC-02/03] APIConfig认证头部生成流程

```mermaid
flowchart TD
    START[开始] --> STATIC_CHECK{需要静态认证？}
    
    STATIC_CHECK -->|是| STATIC[生成静态头部]
    STATIC --> STATIC_KEY["X-Static-API-Key: sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"]
    STATIC_KEY --> AUTH_CHECK{需要Session认证？}
    
    STATIC_CHECK -->|否| AUTH_CHECK
    
    AUTH_CHECK -->|是| SESSION[生成认证头部]
    SESSION --> SESSION_INPUT[输入：sessionId]
    SESSION_INPUT --> BEARER["Authorization: Bearer {sessionId}"]
    BEARER --> MERGE[合并头部]
    
    AUTH_CHECK -->|否| STATIC_ONLY[仅静态头部]
    
    MERGE --> DUAL_AUTH[双重认证头部]
    STATIC_ONLY --> SINGLE_AUTH[单一认证头部]
    DUAL_AUTH --> END[返回头部字典]
    SINGLE_AUTH --> END
    
    style STATIC_KEY fill:#e3f2fd
    style BEARER fill:#f3e5f5
    style DUAL_AUTH fill:#e8f5e8
    style SINGLE_AUTH fill:#fff3e0
```

### 2.3 [FC-04] AdapterContainer依赖注入生命周期

```mermaid
stateDiagram-v2
    [*] --> Uninitialized: 应用启动
    
    state "AdapterContainer" as AC {
        Uninitialized --> SingletonCreated: AdapterContainer.shared
        SingletonCreated --> LazyLoading: 首次访问客户端
        
        state "LazyLoading" as LL {
            [*] --> AuthClientCheck: authAPIClient访问
            [*] --> MainClientCheck: mainAPIClient访问
            
            AuthClientCheck --> AuthClientCreate: 创建AuthAPIClient
            MainClientCheck --> MainClientCreate: 创建MainAPIClient
            
            AuthClientCreate --> AuthClientCached: 缓存实例
            MainClientCreate --> MainClientCached: 缓存实例
        }
        
        LazyLoading --> Ready: 客户端实例化完成
    }
    
    Ready --> [*]: 应用生命周期结束
    
    note right of SingletonCreated
        关键帧：单例容器
        线程安全保证
    end note
    
    note right of AuthClientCached
        关键帧：认证客户端
        baseURL: auth.senseword.app
    end note
    
    note right of MainClientCached
        关键帧：主API客户端  
        baseURL: api.senseword.app
    end note
```

### 2.4 [FC-05/06] MockAPIClient测试数据流

```mermaid
graph LR
    subgraph "测试准备阶段"
        TC[测试用例] --> MOCK[MockAPIClient]
        MOCK --> SET_RESP[setMockResponse]
        SET_RESP --> ENCODE[JSON编码]
        ENCODE --> STORE[存储到mockResponses]
    end
    
    subgraph "测试执行阶段"
        REQ[request调用] --> ERROR_CHECK{shouldThrowError?}
        ERROR_CHECK -->|是| THROW[抛出预设错误]
        ERROR_CHECK -->|否| LOOKUP[查找Mock响应]
        LOOKUP --> FOUND{找到响应?}
        FOUND -->|否| NOT_FOUND[抛出invalidResponse]
        FOUND -->|是| DECODE[JSON解码]
        DECODE --> RETURN[返回Mock对象]
    end
    
    subgraph "关键帧数据"
        STORE --> MR[mockResponses:<br/>[String: Any]]
        THROW --> ERR[APIError关键帧]
        RETURN --> RESULT[T类型关键帧]
        NOT_FOUND --> ERR
    end
    
    style MR fill:#e1f5fe
    style ERR fill:#ffebee
    style RESULT fill:#e8f5e8
```

## 3. 错误处理分支可视化

```mermaid
graph TD
    REQUEST[HTTP请求] --> URL_CHECK{URL有效?}
    
    URL_CHECK -->|否| ERROR1[APIError.invalidURL]
    URL_CHECK -->|是| NETWORK[网络请求]
    
    NETWORK --> NET_CHECK{网络成功?}
    NET_CHECK -->|否| ERROR2[APIError.networkError]
    NET_CHECK -->|是| RESPONSE[HTTP响应]
    
    RESPONSE --> STATUS_CHECK{状态码检查}
    STATUS_CHECK --> S200[200-299<br/>成功]
    STATUS_CHECK --> S401[401<br/>未授权]
    STATUS_CHECK --> S403[403<br/>API密钥无效]
    STATUS_CHECK --> S500[500+<br/>服务器错误]
    
    S401 --> ERROR3[APIError.unauthorized]
    S403 --> ERROR4[APIError.invalidAPIKey]
    S500 --> ERROR5[APIError.serverError]
    
    S200 --> JSON_PARSE{JSON解析}
    JSON_PARSE -->|成功| SUCCESS[返回强类型对象]
    JSON_PARSE -->|失败| ERROR6[APIError.decodingError]
    
    RESPONSE --> RESP_CHECK{响应有效?}
    RESP_CHECK -->|否| ERROR7[APIError.invalidResponse]
    RESP_CHECK -->|是| STATUS_CHECK
    
    style SUCCESS fill:#4caf50
    style ERROR1 fill:#f44336
    style ERROR2 fill:#f44336
    style ERROR3 fill:#ff9800
    style ERROR4 fill:#ff9800
    style ERROR5 fill:#f44336
    style ERROR6 fill:#ff5722
    style ERROR7 fill:#f44336
```

## 4. 测试覆盖率关键帧

```mermaid
pie title 测试类型分布
    "补间测试(正常流程)" : 16
    "变体测试(异常流程)" : 16
    "集成测试" : 1
```

```mermaid
graph LR
    subgraph "测试覆盖范围"
        A[HTTP方法] --> A1[GET ✅]
        A --> A2[POST ✅]
        A --> A3[DELETE ✅]
        
        B[错误类型] --> B1[invalidURL ✅]
        B --> B2[unauthorized ✅]
        B --> B3[invalidAPIKey ✅]
        B --> B4[serverError ✅]
        B --> B5[networkError ✅]
        B --> B6[decodingError ✅]
        B --> B7[invalidResponse ✅]
        
        C[认证方式] --> C1[静态API密钥 ✅]
        C --> C2[Session认证 ✅]
        C --> C3[双重认证 ✅]
        
        D[架构特性] --> D1[单例模式 ✅]
        D --> D2[懒加载 ✅]
        D --> D3[线程安全 ✅]
        D --> D4[依赖注入 ✅]
    end
    
    style A1 fill:#4caf50
    style A2 fill:#4caf50
    style A3 fill:#4caf50
    style B1 fill:#4caf50
    style B2 fill:#4caf50
    style B3 fill:#4caf50
    style B4 fill:#4caf50
    style B5 fill:#4caf50
    style B6 fill:#4caf50
    style B7 fill:#4caf50
    style C1 fill:#4caf50
    style C2 fill:#4caf50
    style C3 fill:#4caf50
    style D1 fill:#4caf50
    style D2 fill:#4caf50
    style D3 fill:#4caf50
    style D4 fill:#4caf50
```

## 5. 总结：关键帧演进路径

```mermaid
timeline
    title 网络基础设施关键帧演进
    
    section 准备阶段
        契约理解 : 解析6个函数契约
                : 确定架构方案
    
    section 实现阶段
        核心基础设施 : APIClient实现
                    : APIConfig实现
                    : AdapterContainer实现
                    : APIError定义
        
        测试基础设施 : MockAPIClient实现
                    : 协议抽象设计
                    : 测试架构构建
    
    section 验证阶段
        补间测试 : 16个正常流程测试
                : 功能完整性验证
        
        变体测试 : 16个异常流程测试
                : 边界条件验证
        
        集成测试 : 主应用集成
                : 端到端验证
    
    section 交付阶段
        文档完善 : 补间测试报告
                : 进度日志更新
                : 可视化图表
        
        准备就绪 : 网络基础设施完成
                : 支持后续开发
```

所有关键帧数据结构的生命周期变化已完整映射，网络基础设施的每个组件都有清晰的输入输出关键帧定义，为后续的API适配器开发提供了可靠的基础。
