# KDD-018 iOS Adapter转译层基础设施 - 函数契约补间链

## 0. 依赖关系与影响分析

- **[新增]** `SensewordApp/Network/APIClient.swift`: 全新的HTTP客户端基础设施，提供统一的网络请求接口
- **[新增]** `SensewordApp/Network/APIError.swift`: 统一错误处理枚举，覆盖所有网络和API错误场景
- **[新增]** `SensewordApp/Network/APIConfig.swift`: 认证配置管理，包含静态API密钥和认证头部生成
- **[新增]** `SensewordApp/DI/AdapterContainer.swift`: 依赖注入容器，管理所有Adapter实例的生命周期
- **[重用]** `iOS/Packages/UIComponents/`: 现有UI组件包，将继续使用其测试基础设施模式
- **[参考]** `0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md`: 后端API接口规范，确保Adapter实现与后端完全匹配

## 1. 项目文件结构概览 (Project File Structure Overview)

```
iOS/SensewordApp/
├── Network/                            # 🌐 网络基础设施层
│   ├── APIClient.swift                 # [新增] HTTP客户端基础类
│   ├── APIError.swift                  # [新增] 统一错误处理
│   └── APIConfig.swift                 # [新增] 认证配置管理
│
├── Services/                           # 💼 服务层架构
│   └── Adapters/                       # 🔄 API转译适配层 (后续阶段)
│       ├── AuthAPIAdapter.swift        # [计划] 认证API转译
│       ├── WordAPIAdapter.swift        # [计划] 单词服务API转译
│       └── ...                         # [计划] 其他API适配器
│
├── Models/                             # 📊 数据模型层
│   └── API/                            # API数据模型 (后续阶段)
│       ├── AuthAPIModels.swift         # [计划] 认证API数据模型
│       ├── WordAPIModels.swift         # [计划] 单词服务API数据模型
│       └── ...                         # [计划] 其他API数据模型
│
├── DI/                                 # 🔧 依赖注入层
│   ├── AdapterContainer.swift          # [新增] Adapter层依赖注入容器
│   └── ServiceFactory.swift           # [计划] 服务工厂 (后续阶段)
│
└── Tests/                              # 🧪 测试基础设施
    ├── NetworkTests/                   # [新增] 网络层测试
    │   ├── APIClientTests.swift        # [新增] HTTP客户端测试
    │   ├── APIErrorTests.swift         # [新增] 错误处理测试
    │   └── MockAPIClient.swift         # [新增] Mock测试客户端
    └── AdapterTests/                   # [计划] Adapter层测试 (后续阶段)
```

## 2. 分支策略建议

- **建议的特性分支名称**: `feature/adapter-infrastructure`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-adapter-infrastructure`
- **基础分支**: `dev`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-adapter-infrastructure -b feature/adapter-infrastructure dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [x] feat(network): implement APIClient HTTP基础设施
- [x] feat(network): implement APIError统一错误处理
- [x] feat(network): implement APIConfig认证配置管理
- [x] feat(di): implement AdapterContainer依赖注入容器
- [x] test(network): implement APIClient单元测试
- [x] test(network): implement MockAPIClient测试基础设施
- [x] docs(network): add network layer documentation

## 4. 函数契约补间链 (Function Contract Tweening Chain)

> **工程实现指导原则**  
> 本节描述期望的API体验和功能需求。实现时可以采用符合目标语言最佳实践的技术方案，只要保证功能完全符合契约意图且API调用体验一致即可。

### [FC-01]: APIClient统一请求接口

- **职责**: 提供统一的泛型HTTP请求接口，整合URL构建、网络请求、JSON解析的完整流程
- **API体验期望**: 支持默认参数的简化调用，如 `try await client.request<T>(endpoint: "/api/data")`
- **实现指导**: 可使用协议+扩展模式适配Swift语言特性
- **函数签名**: `request<T: Codable>(endpoint: String, method: HTTPMethod = .GET, headers: [String: String]? = nil, body: Data? = nil) async throws -> T`
- **所在文件**: `SensewordApp/Network/APIClient.swift`

>>>>> **输入 (Input)**: API请求参数

```swift
// 直接的函数参数，无需额外结构体
let endpoint: String           // API端点路径，如 "/api/v1/word/apple"
let method: HTTPMethod         // HTTP方法枚举，默认为.GET
let headers: [String: String]? // 可选的HTTP头部字典，默认为nil
let body: Data?               // 可选的请求体数据，默认为nil

// HTTPMethod枚举定义（严格按照技术方案）
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case DELETE = "DELETE"
}
```

<<<<< **输出 (Output)**: 解析后的强类型对象或抛出错误

```swift
// 成功时返回泛型对象，类型由调用方指定
T where T: Codable

// 失败时抛出APIError（严格按照技术方案的7个case）
enum APIError: Error {
    case invalidURL
    case invalidResponse
    case invalidAPIKey
    case unauthorized
    case networkError(Error)
    case decodingError(Error)
    case serverError(Int, String)
}
```

---

### [FC-02]: APIConfig静态头部生成器

- **职责**: 生成仅包含静态API密钥的认证头部，用于公开API访问
- **函数签名**: `static var staticHeaders: [String: String] { get }`
- **所在文件**: `SensewordApp/Network/APIConfig.swift`

>>>>> **输入 (Input)**: 无输入参数（计算属性）

```swift
// 无需输入参数，使用预定义的静态API密钥
Void
```

<<<<< **输出 (Output)**: 静态认证头部字典

```swift
// 返回包含静态API密钥的头部字典
[
    "X-Static-API-Key": "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
]
```

---

### [FC-03]: APIConfig双重认证头部生成器

- **职责**: 生成包含静态API密钥和Session的双重认证头部，用于需要用户认证的API
- **函数签名**: `static func authHeaders(sessionId: String) -> [String: String]`
- **所在文件**: `SensewordApp/Network/APIConfig.swift`

>>>>> **输入 (Input)**: 必需的Session ID

```swift
// 用户会话ID，必需参数（非可选）
let sessionId: String
```

<<<<< **输出 (Output)**: 双重认证头部字典

```swift
// 返回包含静态密钥和Session的完整认证头部
[
    "X-Static-API-Key": "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025",
    "Authorization": "Bearer \(sessionId)"
]
```

---

### [FC-04]: AdapterContainer网络客户端工厂

- **职责**: 创建和管理APIClient实例，为不同的服务端点提供预配置的客户端
- **函数签名**: `lazy var authAPIClient: APIClientProtocol` 和 `lazy var mainAPIClient: APIClientProtocol`
- **所在文件**: `SensewordApp/DI/AdapterContainer.swift`

>>>>> **输入 (Input)**: 无输入参数（使用预定义配置）

```swift
// 使用APIConfig中的预定义URL配置
// authBaseURL: "https://auth.senseword.app"
// apiBaseURL: "https://api.senseword.app"
Void
```

<<<<< **输出 (Output)**: 预配置的API客户端实例

```swift
// 认证服务客户端（指向auth.senseword.app）
let authAPIClient: APIClientProtocol = APIClient(baseURL: APIConfig.authBaseURL)

// 主API服务客户端（指向api.senseword.app）
let mainAPIClient: APIClientProtocol = APIClient(baseURL: APIConfig.apiBaseURL)
```

---

### [FC-05]: MockAPIClient测试请求接口

- **职责**: 提供与APIClient相同接口的Mock实现，用于单元测试和离线开发
- **函数签名**: `request<T: Codable>(endpoint: String, method: HTTPMethod, headers: [String: String]?, body: Data?) async throws -> T`
- **所在文件**: `SensewordApp/Tests/NetworkTests/MockAPIClient.swift`

>>>>> **输入 (Input)**: 与APIClient相似但无默认参数的请求参数

```swift
// 注意：MockAPIClient的参数没有默认值（与APIClient不同）
let endpoint: String           // API端点路径，必需参数
let method: HTTPMethod         // HTTP方法枚举，必需参数（无默认值）
let headers: [String: String]? // HTTP头部字典，必需参数（无默认值）
let body: Data?               // 请求体数据，必需参数（无默认值）
```

<<<<< **输出 (Output)**: Mock响应对象或模拟错误

```swift
// 成功时返回预设的Mock响应对象
T where T: Codable

// 失败时抛出预设的Mock错误
APIError // 通过shouldThrowError属性控制
```

---

### [FC-06]: MockAPIClient响应配置器

- **职责**: 为特定端点设置Mock响应数据，支持测试用例的数据准备
- **函数签名**: `setMockResponse<T: Codable>(for endpoint: String, response: T)`
- **所在文件**: `SensewordApp/Tests/NetworkTests/MockAPIClient.swift`

>>>>> **输入 (Input)**: 端点路径和Mock响应对象

```swift
// 目标端点路径
let endpoint: String

// 要返回的Mock响应对象
let response: T where T: Codable
```

<<<<< **输出 (Output)**: 无返回值（配置内部状态）

```swift
// 无返回值，将响应数据存储到内部mockResponses字典
// mockResponses: [String: Any] = [:]
Void
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
iOS/Packages/UIComponents/Package.swift
iOS/Packages/UIComponents/Tests/UIComponentsTests/AudioPlayerServiceTests.swift
iOS/SensewordApp.xcodeproj/project.pbxproj
iOS/SensewordApp/Info.plist
0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md
0-KDD - 关键帧驱动开发/03-Docs/04-技术方案/KDD-018-iOS-Adapter转译层技术方案.md
cloudflare/workers/api/src/index.ts
cloudflare/workers/api/src/services/ai.service.ts
</context_files>

## 6. 核心业务流程伪代码

```swift
// 网络基础设施初始化和使用流程
func initializeNetworkInfrastructure() async throws {
    // [FC-04] 获取依赖注入容器中的预配置客户端
    let container = AdapterContainer.shared
    let authClient = container.authAPIClient    // 认证服务客户端
    let mainClient = container.mainAPIClient    // 主API服务客户端

    // [FC-02] 生成静态认证头部
    let staticHeaders = APIConfig.staticHeaders

    // [FC-01] 执行API请求 - 单词查询示例
    let wordResponse: WordDefinitionResponse = try await mainClient.request(
        endpoint: "/api/v1/word/apple",
        method: .GET,
        headers: staticHeaders,
        body: nil
    )

    // [FC-03] 双重认证API请求示例
    let sessionId = "user_session_123"
    let authHeaders = APIConfig.authHeaders(sessionId: sessionId)

    let bookmarks: GetBookmarksResponse = try await mainClient.request(
        endpoint: "/api/v1/bookmarks",
        method: .GET,
        headers: authHeaders,
        body: nil
    )
}

// 测试环境Mock配置流程
func setupMockTesting() async throws {
    // [FC-05] 创建Mock客户端
    let mockClient = MockAPIClient()

    // [FC-06] 配置Mock响应
    let mockWordResponse = WordDefinitionResponse(
        word: "apple",
        metadata: WordMetadata(wordFrequency: "high", relatedConcepts: ["fruit"]),
        content: WordContent(difficulty: "easy", phoneticSymbols: [], coreDefinition: "A fruit")
    )

    mockClient.setMockResponse(for: "/api/v1/word/apple", response: mockWordResponse)

    // [FC-05] 执行Mock请求
    let result: WordDefinitionResponse = try await mockClient.request(
        endpoint: "/api/v1/word/apple",
        method: .GET,
        headers: APIConfig.staticHeaders,
        body: nil
    )
}

// 完整错误处理流程
func handleNetworkErrors() async {
    do {
        let result: WordDefinitionResponse = try await apiClient.request(
            endpoint: "/api/v1/word/test",
            method: .GET,
            headers: APIConfig.staticHeaders,
            body: nil
        )
    } catch APIError.invalidURL {
        // 处理URL构建错误
    } catch APIError.invalidResponse {
        // 处理无效响应错误
    } catch APIError.invalidAPIKey {
        // 处理API密钥错误
    } catch APIError.unauthorized {
        // 处理未授权错误
    } catch APIError.networkError(let underlyingError) {
        // 处理网络连接错误
    } catch APIError.decodingError(let decodingError) {
        // 处理JSON解析错误
    } catch APIError.serverError(let statusCode, let message) {
        // 处理服务器错误
    }
}
```