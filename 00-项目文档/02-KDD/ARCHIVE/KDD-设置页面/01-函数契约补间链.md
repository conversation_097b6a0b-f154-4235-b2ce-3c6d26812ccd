# KDD-设置页面 函数契约补间链

## 项目概述

基于 Story15 用户需求，实现一个简洁而贴心的设置页面，遵循 SenseWord 的设计哲学：高斯模糊 + 低饱和度 + 节制颜色 + 低打扰用户，符合 Apple 设计规范。

## 核心设计原则

1. **简洁哲学**：每个设置都有明确价值，不为功能而功能
2. **用户尊重**：尊重用户个人偏好，提供选择权而非强制
3. **系统集成**：重要功能（订阅管理）无缝对接系统级服务
4. **视觉一致**：延续 SenseWord 的健康风格背景和悬浮设计

## 页面结构设计

### 第一部分：我的账户管理
- 账户信息展示（Apple/Google 登录信息）
- 订阅状态管理（免费/PRO，到期时间）
- App Store 订阅管理跳转

### 第二部分：个性化体验
- 音频播放控制（自动播放开关）
- 触感反馈设置（震动反馈开关）
- 每日提醒通知（推送通知开关）

### 第三部分：学习资产
- 我的收藏管理（快速访问入口）
- 缓存清理工具（一键清除缓存）

### 第四部分：产品信息和支持
- 产品信息（版本、理念）
- 用户反馈（App Store 评分、问题反馈）
- 法律信息（隐私政策、用户协议）

### 第五部分：账户操作
- 退出登录
- 注销账户（带确认步骤）

## 技术架构

### 数据模型
```swift
// 用户设置数据模型
struct UserSettings {
    var autoPlayAudio: Bool
    var hapticFeedback: Bool
    var dailyNotification: Bool
    var subscriptionStatus: SubscriptionStatus
    var userInfo: UserInfo
}

// 订阅状态
enum SubscriptionStatus {
    case free
    case pro(expiryDate: Date)
}

// 用户信息
struct UserInfo {
    let name: String
    let email: String
    let loginProvider: LoginProvider
}

enum LoginProvider {
    case apple
    case google
}
```

### 视图组件
```swift
// 主设置页面
struct SettingsView: View

// 设置分组组件
struct SettingsSection: View

// 设置项组件
struct SettingsRow: View

// 开关设置项
struct SettingsToggleRow: View

// 导航设置项
struct SettingsNavigationRow: View

// 账户信息卡片
struct AccountInfoCard: View

// 订阅状态卡片
struct SubscriptionStatusCard: View
```

## 函数契约清单

### [FC-01] 设置页面初始化
**输入 (>>>>>)**:
```swift
// 无输入参数，从本地存储和用户状态获取数据
```

**输出 (<<<<<)**:
```swift
struct SettingsViewState {
    let userSettings: UserSettings
    let isLoading: Bool
    let error: String?
}
```

### [FC-02] 设置项更新
**输入 (>>>>>)**:
```swift
struct SettingUpdateRequest {
    let settingKey: SettingKey
    let newValue: Any
}

enum SettingKey {
    case autoPlayAudio
    case hapticFeedback
    case dailyNotification
}
```

**输出 (<<<<<)**:
```swift
struct SettingUpdateResponse {
    let success: Bool
    let updatedSettings: UserSettings
    let error: String?
}
```

### [FC-03] 订阅管理跳转
**输入 (>>>>>)**:
```swift
// 无输入参数
```

**输出 (<<<<<)**:
```swift
struct SubscriptionManagementResponse {
    let success: Bool
    let error: String?
}
```

### [FC-04] 缓存清理
**输入 (>>>>>)**:
```swift
// 无输入参数
```

**输出 (<<<<<)**:
```swift
struct CacheClearResponse {
    let success: Bool
    let clearedSize: String
    let error: String?
}
```

### [FC-05] 账户操作
**输入 (>>>>>)**:
```swift
enum AccountAction {
    case logout
    case deleteAccount
}
```

**输出 (<<<<<)**:
```swift
struct AccountActionResponse {
    let success: Bool
    let requiresConfirmation: Bool
    let error: String?
}
```

## 文件结构

```
iOS/SensewordApp/Views/Settings/
├── SettingsView.swift                 # 主设置页面
├── Components/
│   ├── SettingsSection.swift         # 设置分组组件
│   ├── SettingsRow.swift             # 基础设置项
│   ├── SettingsToggleRow.swift       # 开关设置项
│   ├── SettingsNavigationRow.swift   # 导航设置项
│   ├── AccountInfoCard.swift         # 账户信息卡片
│   └── SubscriptionStatusCard.swift  # 订阅状态卡片
├── ViewModels/
│   └── SettingsViewModel.swift       # 设置页面视图模型
└── Models/
    └── SettingsModels.swift          # 设置相关数据模型

iOS/SensewordApp/Services/Settings/
├── SettingsService.swift             # 设置服务
├── SubscriptionService.swift         # 订阅管理服务
└── CacheService.swift               # 缓存管理服务
```

## 设计规范

### 视觉风格
- **背景**：健康风格渐变背景，与主界面保持一致
- **卡片设计**：高斯模糊半透明卡片，圆角 16px
- **颜色方案**：低饱和度，主要使用白色和灰色
- **字体**：系统字体，层级清晰

### 交互规范
- **触感反馈**：重要操作提供触觉反馈
- **动画**：简洁的过渡动画，避免过度设计
- **确认机制**：危险操作（注销账户）提供二次确认

### 无障碍支持
- **VoiceOver**：所有控件提供适当的标签
- **动态字体**：支持系统字体大小调整
- **对比度**：确保文字和背景有足够对比度

## Commit 规划

1. **feat(settings): 创建设置页面基础结构和数据模型**
2. **feat(settings): 实现账户信息和订阅状态展示**
3. **feat(settings): 添加个性化设置开关功能**
4. **feat(settings): 实现学习资产管理功能**
5. **feat(settings): 添加产品信息和支持功能**
6. **feat(settings): 实现账户操作功能**
7. **feat(settings): 集成设置页面到主导航**

## Context Files

- `iOS/SensewordApp/Views/Search/SearchView.swift` - 参考悬浮按钮设计
- `iOS/SensewordApp/Views/Components/FloatingActionBar.swift` - 参考按钮样式
- `iOS/SensewordApp/Views/Background/KeyframeAnimationWallpaperView.swift` - 参考背景设计
- `iOS/SensewordApp/Models/Shared/SharedModels.swift` - 参考数据模型设计
