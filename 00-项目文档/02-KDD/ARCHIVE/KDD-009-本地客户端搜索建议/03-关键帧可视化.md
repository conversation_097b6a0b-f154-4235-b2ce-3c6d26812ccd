# KDD-009: 本地客户端搜索建议 - 关键帧可视化

## 1. 数据结构生命周期变化图

```mermaid
graph TD
    %% 定义样式
    classDef input fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#b71c1c,font-weight:bold
    classDef processing fill:#e3f2fd,stroke:#1565c0,stroke-width:3px,color:#0d47a1,font-weight:bold
    classDef output fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#1b5e20,font-weight:bold
    classDef transformation fill:#fff3e0,stroke:#ef6c00,stroke-width:3px,color:#e65100,font-weight:bold
    classDef storage fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c,font-weight:bold

    %% 关键帧1: 现有数据库记录
    A["关键帧1: 现有word_definitions记录<br/>{<br/>  word: 'progressive',<br/>  language: 'zh',<br/>  contentJson: '{...完整JSON...}',<br/>  createdAt: '2025-06-24...'<br/>}"]

    %% 数据转换1: 添加sync_id
    B["FC-01: 数据库结构增强<br/>ALTER TABLE word_definitions<br/>ADD COLUMN sync_id INTEGER<br/>PRIMARY KEY AUTOINCREMENT"]

    %% 关键帧2: 增强后的数据库记录
    C["关键帧2: 增强后的数据记录<br/>{<br/>  sync_id: 15001,<br/>  word: 'progressive',<br/>  language: 'zh',<br/>  contentJson: '{...}',<br/>  createdAt: '2025-06-24...'<br/>}"]

    %% 数据转换2: API数据提取
    D["FC-02: 增量同步API处理<br/>JSON_EXTRACT(<br/>  contentJson,<br/>  '$.content.definitions[0]'<br/>) → 核心释义提取"]

    %% 关键帧3: API响应数据
    E["关键帧3: API响应数据<br/>{<br/>  success: true,<br/>  data: [{<br/>    syncId: 15001,<br/>    word: 'progressive',<br/>    language: 'zh',<br/>    coreDefinition: '渐进的，进步的'<br/>  }],<br/>  lastSyncId: 15001<br/>}"]

    %% 数据转换3: 本地数据库重构
    F["FC-03: 本地索引数据库重构<br/>CREATE TABLE local_word_index<br/>(sync_id, word, language, core_definition)<br/>INSERT OR REPLACE INTO..."]

    %% 关键帧4: 本地索引记录
    G["关键帧4: 本地索引记录<br/>{<br/>  sync_id: 15001,<br/>  word: 'progressive',<br/>  language: 'zh',<br/>  core_definition: '渐进的，进步的'<br/>}"]

    %% 数据转换4: 搜索查询处理
    H["FC-04: 瞬时搜索建议生成<br/>SELECT word FROM local_word_index<br/>WHERE language = 'zh'<br/>AND word LIKE 'prog%'<br/>ORDER BY LENGTH(word), word"]

    %% 关键帧5: 搜索建议结果
    I["关键帧5: 搜索建议结果<br/>{<br/>  suggestions: [<br/>    'progress',<br/>    'progressive',<br/>    'program'<br/>  ],<br/>  responseTime: 0.025,<br/>  isOffline: true<br/>}"]

    %% 定义流程
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I

    %% 应用样式
    class A input
    class B,D,F,H transformation
    class C,E,G processing
    class I output

    %% 添加说明
    subgraph Legend ["奥卡姆剃刀简化说明"]
        L1["❌ 移除字段: difficulty, frequency, updatedAt"]
        L2["✅ 保留核心: syncId, word, language, coreDefinition"]
        L3["⚡ 性能提升: 从200-500ms → <50ms"]
        L4["📱 离线支持: 100%搜索功能离线可用"]
        L5["💰 成本优化: 搜索相关后端负载降为零"]
    end

    style Legend fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#e65100
```

## 2. 系统架构变化对比图

```mermaid
graph TB
    subgraph Before ["现有架构：实时搜索建议"]
        U1[用户输入 'prog'] --> D1[防抖延迟 300ms]
        D1 --> API1[GET /api/v1/suggestions]
        API1 --> DB1[D1数据库查询]
        DB1 --> R1[网络响应 200-500ms]
        R1 --> UI1[显示建议列表]

        style U1 fill:#ffebee,stroke:#d32f2f
        style D1 fill:#fff3e0,stroke:#ef6c00
        style API1 fill:#e3f2fd,stroke:#1565c0
        style DB1 fill:#f3e5f5,stroke:#7b1fa2
        style R1 fill:#fff3e0,stroke:#ef6c00
        style UI1 fill:#e8f5e8,stroke:#2e7d32
    end

    subgraph After ["重构架构：本地索引搜索"]
        U2[用户输入 'prog'] --> L1[本地SQLite查询]
        L1 --> R2[瞬时响应 <50ms]
        R2 --> UI2[显示建议列表]

        BG[后台增量同步] -.-> L1

        style U2 fill:#ffebee,stroke:#d32f2f
        style L1 fill:#e8f5e8,stroke:#2e7d32
        style R2 fill:#e8f5e8,stroke:#2e7d32
        style UI2 fill:#e8f5e8,stroke:#2e7d32
        style BG fill:#f3e5f5,stroke:#7b1fa2
    end

    %% 对比说明
    subgraph Comparison ["性能对比"]
        C1["现有方案：<br/>• 响应时间: 200-500ms<br/>• 网络依赖: 100%<br/>• 离线可用: 0%<br/>• 后端负载: 高"]
        C2["重构方案：<br/>• 响应时间: <50ms<br/>• 网络依赖: 0%<br/>• 离线可用: 100%<br/>• 后端负载: 0"]

        style C1 fill:#ffebee,stroke:#d32f2f
        style C2 fill:#e8f5e8,stroke:#2e7d32
    end
```

## 3. 函数契约依赖关系图

```mermaid
graph TD
    %% 函数契约节点
    FC01["FC-01<br/>数据库结构增强器<br/>添加sync_id字段"]
    FC02["FC-02<br/>增量同步API处理器<br/>替代实时搜索API"]
    FC03["FC-03<br/>本地索引数据库重构器<br/>重构本地表结构"]
    FC04["FC-04<br/>瞬时搜索建议生成器<br/>本地搜索查询"]
    FC05["FC-05<br/>增量同步数据处理器<br/>同步数据到本地"]

    %% 依赖关系
    FC01 --> FC02
    FC02 --> FC05
    FC03 --> FC04
    FC05 --> FC04

    %% 样式定义
    classDef fc fill:#e3f2fd,stroke:#1565c0,stroke-width:3px,color:#0d47a1,font-weight:bold

    %% 应用样式
    class FC01,FC02,FC03,FC04,FC05 fc

    %% 阶段分组
    subgraph Phase1 ["阶段1: 服务端改造 (3天)"]
        FC01
        FC02
    end

    subgraph Phase2 ["阶段2: 客户端重构 (4天)"]
        FC03
        FC05
        FC04
    end

    style Phase1 fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#b71c1c
    style Phase2 fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#1b5e20
```

## 4. 数据流转时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as iOS界面
    participant LSM as LocalSearchManager
    participant LSS as LocalSearchService
    participant SS as SyncService
    participant API as 增量同步API
    participant DB as D1数据库

    Note over U,DB: 初始化阶段
    U->>UI: 启动应用
    UI->>LSM: initialize()
    LSM->>SS: shouldSync()
    SS->>API: GET /api/v1/word-index/updates?lang=zh&since=0
    API->>DB: 查询增量数据
    DB-->>API: 返回词汇数据
    API-->>SS: 返回增量响应
    SS->>LSS: 批量插入本地索引
    LSS-->>SS: 插入完成
    SS-->>LSM: 同步完成
    LSM-->>UI: 初始化完成

    Note over U,DB: 搜索阶段
    U->>UI: 输入 "prog"
    UI->>LSM: getSearchSuggestions("prog")
    LSM->>LSS: 本地查询
    LSS->>LSS: SELECT word FROM local_word_index WHERE...
    LSS-->>LSM: ["progress", "progressive", "program"]
    LSM-->>UI: 返回建议列表 (<50ms)
    UI-->>U: 显示搜索建议

    Note over U,DB: 后台同步阶段
    SS->>API: 定期增量同步
    API->>DB: 查询新增数据
    DB-->>API: 返回新词汇
    API-->>SS: 增量数据
    SS->>LSS: 更新本地索引
```

## 5. 奥卡姆剃刀简化对比

### 数据字段简化对比

| 原始字段 | 简化后 | 简化原因 |
|---------|--------|----------|
| id, word, difficulty, definition | syncId, word, language, coreDefinition | 移除非搜索必需字段 |
| 8个API响应字段 | 4个API响应字段 | 50%字段减少 |
| 复杂分页元数据 | 简单lastSyncId | 移除过度设计 |
| 多个索引 | 单个复合索引 | 优化查询性能 |

### 性能提升对比

| 指标 | 现有方案 | 重构方案 | 提升幅度 |
|------|---------|---------|----------|
| 搜索响应时间 | 200-500ms | <50ms | 4-10倍 |
| 离线可用性 | 0% | 100% | 无限提升 |
| 后端负载 | 高频查询 | 零负载 | 100%减少 |
| 数据传输量 | 每次搜索 | 增量同步 | 90%减少 |

这个可视化清晰地展示了从复杂的实时搜索系统到简洁的本地索引系统的完整转换过程，完美体现了奥卡姆剃刀原则的应用效果。