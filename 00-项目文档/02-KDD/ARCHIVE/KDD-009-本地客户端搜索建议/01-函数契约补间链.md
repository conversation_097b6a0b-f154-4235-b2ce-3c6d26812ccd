# KDD-009: 本地客户端搜索建议的函数契约补间链 (奥卡姆剃刀极简版)

## 1. 项目文件结构概览 (Project File Structure Overview)

下方结构树清晰地展示了为实现本垂直切片，需要创建或修改的文件及其在项目中的位置。

```
SensewordApp/
├── cloudflare/
│   ├── d1/
│   │   └── migrations/
│   │       └── 0004_add_sync_id_for_local_search.sql  # [新增] 为word_definitions表添加sync_id
│   └── workers/
│       └── api/
│           └── src/
│               ├── handlers/
│               │   └── wordIndexHandler.ts             # [新增] 替代searchSuggestionsHandler.ts
│               └── index.ts                           # [修改] 更新路由配置
├── iOS/
│   ├── Packages/
│   │   └── CoreDataDomain/
│   │       └── Sources/
│   │           └── CoreDataDomain/
│   │               ├── LocalSearchService.swift       # [修改] 重构为本地索引查询
│   │               └── SyncService.swift              # [修改] 重构为增量同步
│   └── Sources/
│       └── Shared/
│           └── Services/
│               └── LocalSearchManager.swift           # [修改] 移除远程API依赖
└── wrangler.toml                                      # [修改] 更新路由配置
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/local-search-index-refactor`
- 建议的 git worktree 文件路径：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-local-search-refactor
- 基础分支: `dev`
- 分支创建模拟命令行:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/03-local-search-refactor -b feature/local-search-index-refactor dev
    ```

## 3. Commit 规划概要 (Commit Plan Summary & Status)

- [ ] feat(db): 为word_definitions表添加sync_id字段支持增量同步
- [ ] feat(api): 实现增量同步API替代实时搜索建议API
- [ ] refactor(ios): 重构LocalSearchService为本地索引查询
- [ ] refactor(ios): 重构SyncService为增量同步机制
- [ ] refactor(ios): 移除LocalSearchManager的远程API依赖
- [ ] test(ios): 验证本地搜索性能和离线功能

## 4. 函数契约补间链 (Function Contract Tweening Chain)

### [FC-01]: 数据库结构增强器

- 职责: 为现有word_definitions表添加sync_id字段，支持增量同步机制
- 函数签名: `ALTER TABLE word_definitions ADD COLUMN sync_id INTEGER PRIMARY KEY AUTOINCREMENT`
- 所在文件: `cloudflare/d1/migrations/0004_add_sync_id_for_local_search.sql`

>>>>> 输入 (Input): 现有word_definitions表结构

```sql
-- 现有表结构
CREATE TABLE word_definitions (
    word TEXT NOT NULL,
    language TEXT NOT NULL,
    contentJson TEXT NOT NULL,
    feedbackScore INTEGER DEFAULT 0,
    isHumanReviewed INTEGER DEFAULT 0,
    ttsStatus TEXT DEFAULT 'pending',
    difficulty TEXT,
    frequency TEXT,
    relatedConcepts TEXT,
    promptVersion TEXT NOT NULL,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    PRIMARY KEY (word, language)
);
```

<<<<< 输出 (Output): 增强后的表结构

```sql
-- 增强后的表结构（支持增量同步）
CREATE TABLE word_definitions (
    sync_id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 新增：增量同步ID
    word TEXT NOT NULL,
    language TEXT NOT NULL,
    contentJson TEXT NOT NULL,
    feedbackScore INTEGER DEFAULT 0,
    isHumanReviewed INTEGER DEFAULT 0,
    ttsStatus TEXT DEFAULT 'pending',
    difficulty TEXT,
    frequency TEXT,
    relatedConcepts TEXT,
    promptVersion TEXT NOT NULL,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    UNIQUE(word, language)  -- 改为唯一约束
);

-- 新增索引支持增量查询
CREATE INDEX idx_sync_language ON word_definitions (sync_id, language);
```

---

### [FC-02]: 增量同步API处理器

- 职责: 实现增量同步API，替代现有的实时搜索建议API
- 函数签名: `handleWordIndexUpdates(request: Request, env: Env): Promise<Response>`
- 所在文件: `cloudflare/workers/api/src/handlers/wordIndexHandler.ts`

>>>>> 输入 (Input): HTTP请求参数

```typescript
// GET /api/v1/word-index/updates?lang=zh&since=15000
interface WordIndexRequest {
  lang: string;        // 目标语言代码
  since?: number;      // 客户端已有的最后sync_id
}
```

<<<<< 输出 (Output): 增量数据响应

```typescript
// 极简响应结构（奥卡姆剃刀处理）
interface WordIndexResponse {
  success: boolean;
  data: WordIndexItem[];
  lastSyncId: number;
}

interface WordIndexItem {
  syncId: number;
  word: string;
  language: string;
  coreDefinition: string;  // 从contentJson提取的核心释义
}
```

---

### [FC-03]: 本地索引数据库重构器

- 职责: 重构iOS本地数据库结构，支持增量同步的本地索引
- 函数签名: `setupLocalIndexDatabase(): Bool`
- 所在文件: `iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/LocalSearchService.swift`

>>>>> 输入 (Input): 现有本地数据库结构

```swift
// 现有本地表结构
CREATE TABLE word_summaries (
    id TEXT PRIMARY KEY,
    word TEXT NOT NULL,
    difficulty TEXT NOT NULL,
    definition TEXT NOT NULL
)
```

<<<<< 输出 (Output): 重构后的本地索引表

```swift
// 重构后的本地索引表（奥卡姆剃刀极简版）
CREATE TABLE local_word_index (
    sync_id INTEGER PRIMARY KEY,
    word TEXT NOT NULL,
    language TEXT NOT NULL,
    core_definition TEXT NOT NULL,
    UNIQUE(word, language)
);

// 搜索优化索引
CREATE INDEX idx_word_language_prefix ON local_word_index (language, word);
```

---

### [FC-04]: 瞬时搜索建议生成器

- 职责: 实现瞬时本地搜索建议，完全替代远程API调用
- 函数签名: `getSearchSuggestions(_ query: String) -> [String]`
- 所在文件: `iOS/Sources/Shared/Services/LocalSearchManager.swift`

>>>>> 输入 (Input): 用户搜索查询

```swift
// 用户输入的搜索查询
struct SearchQuery {
    let text: String        // 例如: "prog"
    let language: String    // 例如: "zh"
}
```

<<<<< 输出 (Output): 瞬时搜索建议列表

```swift
// 瞬时搜索建议结果（<50ms响应）
struct SearchSuggestions {
    let suggestions: [String]    // 例如: ["progress", "progressive", "program"]
    let responseTime: Double     // 例如: 0.025 (25ms)
    let isOffline: Bool         // 例如: true
}
```

---

### [FC-05]: 增量同步数据处理器

- 职责: 处理从服务端获取的增量数据，更新本地索引
- 函数签名: `syncIncrementalUpdates() async throws -> SyncResult`
- 所在文件: `iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/SyncService.swift`

>>>>> 输入 (Input): 服务端增量数据响应

```swift
// 从服务端获取的增量数据
struct WordIndexResponse {
    let success: Bool
    let data: [WordIndexItem]
    let lastSyncId: Int
}

struct WordIndexItem {
    let syncId: Int
    let word: String
    let language: String
    let coreDefinition: String
}
```

<<<<< 输出 (Output): 本地同步结果

```swift
// 本地同步完成结果
struct SyncResult {
    let success: Bool
    let newWordsCount: Int
    let totalWordsCount: Int
    let syncTime: Double
    let lastSyncId: Int
    let error: SyncError?
}
```

## 5. AI Agent 需要了解的文件上下文

<context_files>
cloudflare/d1/migrations/0001_create_word_definitions.sql
cloudflare/workers/api/src/handlers/searchSuggestionsHandler.ts
cloudflare/workers/api/src/services/searchQueryService.ts
cloudflare/workers/api/src/index.ts
iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/LocalSearchService.swift
iOS/Packages/CoreDataDomain/Sources/CoreDataDomain/SyncService.swift
iOS/Sources/Shared/Services/LocalSearchManager.swift
iOS/Packages/SharedModels/Sources/SharedModels/WordSummaryDTO.swift
iOS/Packages/SharedModels/Sources/SharedModels/SyncModels.swift
wrangler.toml
</context_files>

## 6. 核心业务流程伪代码

```typescript
// 服务端：增量同步API
async function handleWordIndexUpdates(request: Request, env: Env): Promise<Response> {
    // [FC-02] 解析请求参数
    const { lang, since } = parseQueryParams(request.url)

    // 查询增量数据（奥卡姆剃刀：仅必需字段）
    const query = `
        SELECT
            sync_id,
            word,
            language,
            JSON_EXTRACT(contentJson, '$.content.definitions[0]') as core_definition
        FROM word_definitions
        WHERE language = ? AND sync_id > ?
        ORDER BY sync_id ASC
        LIMIT 1000
    `

    const results = await env.DB.prepare(query).bind(lang, since || 0).all()

    return Response.json({
        success: true,
        data: results.map(row => ({
            syncId: row.sync_id,
            word: row.word,
            language: row.language,
            coreDefinition: row.core_definition
        })),
        lastSyncId: results.length > 0 ? results[results.length - 1].sync_id : since
    })
}

// iOS客户端：瞬时搜索建议
func getSearchSuggestions(_ query: String) -> [String] {
    // [FC-04] 本地瞬时查询（无网络依赖）
    let normalizedQuery = query.lowercased().trimmingCharacters(in: .whitespaces)
    guard normalizedQuery.count >= 2 else { return [] }

    let sql = """
        SELECT word FROM local_word_index
        WHERE language = ? AND word LIKE ?
        ORDER BY LENGTH(word), word
        LIMIT 10
    """

    return database.query(sql, parameters: [currentLanguage, "\(normalizedQuery)%"])
}

// iOS客户端：增量同步
func syncIncrementalUpdates() async throws {
    // [FC-05] 获取上次同步ID
    let lastSyncId = UserDefaults.standard.integer(forKey: "lastSyncId")

    // 请求增量数据
    let response = try await apiService.getWordIndexUpdates(
        language: currentLanguage,
        since: lastSyncId
    )

    // 批量更新本地索引
    try database.transaction {
        for item in response.data {
            try insertOrUpdateWordIndex(item)
        }
        UserDefaults.standard.set(response.lastSyncId, forKey: "lastSyncId")
    }
}
```