# KDD-006: 用户生词本管理 - 极简内容流推送源版

## 🎯 核心理念转变

根据会议纪要《生词也是无限内容流的一部分》的核心洞察：
- ❌ **移除**: 独立的生词本页面、复杂的前端UI、批量获取逻辑、分页系统
- ✅ **保留**: 极简的CRUD API，作为内容流推荐算法的数据源
- ✅ **核心**: 整个App只有一个内容流页面 + 搜索入口

## 1. 极简文件结构

```
SenseWord项目根目录/ (极简内容流版)
├── cloudflare/
│   ├── d1/migrations/
│   │   └── 0006_create_bookmarks_table.sql    # [新增] 极简生词本表
│   └── workers/api/src/
│       ├── bookmarks/
│       │   ├── bookmarks.service.ts           # [新增] 极简CRUD服务
│       │   ├── bookmarks.controller.ts        # [新增] 极简API控制器
│       │   └── types/bookmark-types.ts        # [新增] 极简数据类型
│       ├── middleware/session-auth.middleware.ts # [新增] Session验证
│       └── index.ts                           # [修改] 集成路由
├── iOS/Sources/
│   ├── ContentStreamDomain/                   # [已实现] 内容流领域 (主要功能)
│   │   └── Sources/ContentStreamDomain/
│   │       ├── Models/WordItem.swift          # [修改] 添加isBookmarked字段
│   │       ├── Services/
│   │       │   ├── ContentStreamService.swift # [修改] 集成推荐算法
│   │       │   └── BookmarkDataSource.swift  # [新增] 生词本数据源
│   │       └── Views/
│   │           ├── ContentStreamView.swift    # [修改] 唯一主界面
│   │           └── BookmarkButton.swift       # [新增] 收藏按钮
│   └── SearchDomain/                          # [已实现] 搜索领域
└── SenseWordApp.swift                         # [修改] 极简App结构
```

## 2. 分支策略建议

### Git Worktree 工作区设置

- **建议的特性分支名称**: `feature/bookmark/user-vocabulary-management`
- **建议的 git worktree 文件路径**: `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-senseword-bookmark-system`（请创建和根目录同层工作区）
- **基础分支**: `dev`
- **分支创建模拟命令行**:
    ```bash
    # 概念性命令，用于记录和指导
    # git checkout dev
    # git pull origin dev
    # git worktree add /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/04-senseword-bookmark-system -b feature/bookmark/user-vocabulary-management dev
    ```

### Worktree 优势分析

| 优势项 | 说明 | 价值 |
|-------|------|------|
| **独立开发环境** | 与dev分支完全隔离，避免冲突 | 提升开发效率 |
| **专注功能实现** | 专门用于生词本极简功能开发 | 减少干扰因素 |
| **便于测试验证** | 独立环境便于功能测试和验证 | 提升代码质量 |
| **一致性命名** | 与现有auth系统worktree保持规范 | 维护项目整洁 |

## 3. 极简Commit规划

- [ ] chore(db): 创建极简bookmarks表 (复合主键，仅存储收藏关系)
- [ ] feat(bookmark-api): 实现极简CRUD API (添加、删除、获取收藏列表)
- [ ] feat(content-stream): 集成生词本数据源到推荐算法
- [ ] feat(bookmark-button): 在内容流中添加收藏按钮
- [ ] test(bookmarks): 核心CRUD功能测试

## 4. 极简函数契约

### [FC-01]: 内容流收藏按钮

- **职责**: 在内容流中为每个单词提供收藏/取消收藏功能
- **函数签名**: `toggleBookmark(word: String, language: String) async -> Void`
- **所在文件**: `iOS/Sources/ContentStreamDomain/Views/BookmarkButton.swift`

>>>>> 输入 (Input): 单词标识符

```swift
struct BookmarkToggleInput {
    let word: String                    // 单词文本 (已标准化)
    let language: String                // 语言代码
}
```

<<<<< 输出 (Output): 状态更新

```swift
enum BookmarkToggleResult {
    case success(isBookmarked: Bool)    // 操作成功
    case failure(BookmarkError)         // 操作失败
}
```

---

### [FC-02]: 生词本数据源服务

- **职责**: 为内容流推荐算法提供用户收藏的单词列表
- **函数签名**: `getUserBookmarkedWords(userId: String) async throws -> [String]`
- **所在文件**: `iOS/Sources/ContentStreamDomain/Services/BookmarkDataSource.swift`

>>>>> 输入 (Input): 用户ID

```swift
struct BookmarkDataSourceInput {
    let userId: String                  // 已认证的用户ID
}
```

<<<<< 输出 (Output): 收藏单词列表

```swift
// 极简输出：只返回单词列表，供推荐算法使用
typealias BookmarkedWordsList = [String]

// 示例: ["progressive", "innovative", "sophisticated"]
```

---

### [FC-03]: 后端极简CRUD API

- **职责**: 提供生词本的基础增删查操作，无复杂业务逻辑
- **函数签名**: `handleBookmarkCRUD(request: Request, env: Env): Promise<Response>`
- **所在文件**: `cloudflare/workers/api/src/bookmarks/bookmarks.controller.ts`

>>>>> 输入 (Input): HTTP CRUD请求

```typescript
// 添加生词: POST /api/v1/bookmarks
interface AddBookmarkRequest {
  method: 'POST';
  headers: { 'Authorization': 'Bearer <sessionId>' };
  body: { word: string; language: string; };
}

// 删除生词: DELETE /api/v1/bookmarks
interface RemoveBookmarkRequest {
  method: 'DELETE';
  headers: { 'Authorization': 'Bearer <sessionId>' };
  body: { word: string; language: string; };
}

// 获取收藏列表: GET /api/v1/bookmarks
interface GetBookmarksRequest {
  method: 'GET';
  headers: { 'Authorization': 'Bearer <sessionId>' };
}
```

<<<<< 输出 (Output): HTTP响应

```typescript
// 添加/删除响应
interface CRUDResponse {
  success: boolean;
  message: string;
}

// 获取列表响应 (已升级为扩展版)
interface GetBookmarksResponse {
  success: boolean;
  bookmarks: BookmarkItem[];            // 扩展：返回完整生词信息数组
}

interface BookmarkItem {
  word: string;                         // 单词文本
  language: string;                     // 语言代码
  createdAt: string;                    // 创建时间 (ISO 8601)
}
```

## 5. 极简数据库设计

```sql
-- 极简生词本表 (只存储收藏关系)
CREATE TABLE IF NOT EXISTS bookmarks (
    user_id TEXT NOT NULL,              -- 用户ID
    word TEXT NOT NULL,                 -- 单词 (已标准化)
    language TEXT NOT NULL,             -- 语言代码
    created_at TEXT NOT NULL,           -- 创建时间
    
    PRIMARY KEY (user_id, word, language),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 单一索引 (支持按用户查询)
CREATE INDEX IF NOT EXISTS idx_bookmarks_user ON bookmarks (user_id);
```

## 6. 极简API协议

### 添加生词
```
POST /api/v1/bookmarks
Authorization: Bearer <sessionId>
Content-Type: application/json

{ "word": "progressive", "language": "zh" }

→ { "success": true, "message": "已收藏" }
```

### 删除生词
```
DELETE /api/v1/bookmarks
Authorization: Bearer <sessionId>
Content-Type: application/json

{ "word": "progressive", "language": "zh" }

→ { "success": true, "message": "已取消收藏" }
```

### 获取收藏列表
```
GET /api/v1/bookmarks
Authorization: Bearer <sessionId>

→ { "success": true, "words": ["progressive", "innovative", "sophisticated"] }
```

## 7. 核心业务流程

```typescript
// 极简业务流程
async function handleBookmarkCRUD(request: Request, env: Env): Promise<Response> {
    const user = await authenticateSessionRequest(request, env.AUTH_WORKER_URL)
    const { method } = request
    
    if (method === 'POST') {
        const { word, language } = await request.json()
        const normalizedWord = word.toLowerCase().trim()
        
        await env.DB.prepare(`
            INSERT OR IGNORE INTO bookmarks (user_id, word, language, created_at) 
            VALUES (?, ?, ?, ?)
        `).bind(user.sub, normalizedWord, language, new Date().toISOString()).run()
        
        return Response.json({ success: true, message: "已收藏" })
    }
    
    if (method === 'DELETE') {
        const { word, language } = await request.json()
        const normalizedWord = word.toLowerCase().trim()
        
        await env.DB.prepare(`
            DELETE FROM bookmarks 
            WHERE user_id = ? AND word = ? AND language = ?
        `).bind(user.sub, normalizedWord, language).run()
        
        return Response.json({ success: true, message: "已取消收藏" })
    }
    
    if (method === 'GET') {
        const { results } = await env.DB.prepare(`
            SELECT word FROM bookmarks 
            WHERE user_id = ? 
            ORDER BY created_at DESC
        `).bind(user.sub).all()
        
        const words = results.map(row => row.word)
        return Response.json({ success: true, words })
    }
}
```

## 8. 最终特点

**🎯 奥卡姆剃刀极致应用**:
1. **无独立页面**: 生词本功能完全融入内容流
2. **无复杂UI**: 只有一个收藏按钮
3. **无分页逻辑**: 直接返回全部收藏单词
4. **无JOIN查询**: 纯粹的单表CRUD操作
5. **无批量接口**: 推荐算法直接使用简单列表

**🚀 内容流推送源**:
- 生词本作为推荐算法的重要数据源
- 用户收藏的单词会在内容流中获得更高权重
- 整个App保持单一内容流的简洁体验

**📊 开发效率提升**:
- 开发时间: 减少80% (无复杂UI和逻辑)
- 测试复杂度: 减少90% (只测试CRUD)
- 维护成本: 减少95% (极简架构)

这是奥卡姆剃刀原则的完美体现：**移除一切非必要复杂度，专注核心价值**。
