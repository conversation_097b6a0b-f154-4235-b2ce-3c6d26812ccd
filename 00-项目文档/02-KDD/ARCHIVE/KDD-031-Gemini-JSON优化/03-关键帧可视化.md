# KDD-031-Gemini-JSON优化 - 关键帧可视化

## 数据结构生命周期图

### 整体数据流转图

```mermaid
graph TD
    A[原始词汇列表<br/>OriginalWordList] --> B[编号化词汇批次<br/>NumberedWordBatch]
    B --> C[Gemini处理结果<br/>GeminiProcessedResult]
    C --> D[最终处理报告<br/>ProcessingReport]
    
    A1[words: string[]] --> A
    A2[total_count: number] --> A
    A3[source_file: string] --> A
    
    B1[batch_number: number] --> B
    B2[batch_size: number] --> B
    B3[words: Array<{id, word}>] --> B
    B4[output_file: string] --> B
    
    C1[batch_number: number] --> C
    C2[processed_words: Array<{id, word, keep, topics, priority}>] --> C
    C3[processing_status: success/error] --> C
    C4[error_message?: string] --> C
    
    D1[success_count: number] --> D
    D2[error_count: number] --> D
    D3[processed_batches: Array] --> D
```

### 关键帧A：原始词汇列表结构

```mermaid
classDiagram
    class OriginalWordList {
        +words: string[]
        +total_count: number
        +source_file: string
        +load_from_file() string[]
        +validate_words() boolean
        +get_statistics() WordStats
    }
    
    class WordStats {
        +total_words: number
        +unique_words: number
        +avg_word_length: number
        +word_length_distribution: Map
    }
    
    OriginalWordList --> WordStats
```

### 关键帧B：编号化词汇批次结构

```mermaid
classDiagram
    class NumberedWordBatch {
        +batch_number: number
        +batch_size: number
        +words: WordWithId[]
        +output_file: string
        +create_json_file() string
        +validate_numbering() boolean
        +get_word_by_id(id: number) string
    }
    
    class WordWithId {
        +id: number
        +word: string
        +validate() boolean
    }
    
    NumberedWordBatch --> WordWithId
```

### 关键帧C：Gemini处理结果结构

```mermaid
classDiagram
    class GeminiProcessedResult {
        +batch_number: number
        +processed_words: ProcessedWord[]
        +processing_status: ProcessingStatus
        +error_message?: string
        +validate_result() boolean
        +get_success_rate() number
        +get_processing_summary() Summary
    }
    
    class ProcessedWord {
        +id: number
        +word: string
        +keep: boolean
        +topics: string[]
        +priority: number
        +validate_fields() boolean
    }
    
    class ProcessingStatus {
        <<enumeration>>
        SUCCESS
        ERROR
        PARTIAL_SUCCESS
    }
    
    GeminiProcessedResult --> ProcessedWord
    GeminiProcessedResult --> ProcessingStatus
```

## 数据转换流程图

### FC-01: 批次生成流程

```mermaid
flowchart TD
    Start([开始]) --> LoadWords[加载原始词汇列表]
    LoadWords --> ValidateWords{验证词汇格式}
    ValidateWords -->|无效| ErrorFormat[报告格式错误]
    ValidateWords -->|有效| SplitBatch[分割成500个单词的批次]
    SplitBatch --> AddNumbering[为每个单词添加唯一编号]
    AddNumbering --> CreateJSON[生成JSON格式文件]
    CreateJSON --> ValidateJSON{验证JSON格式}
    ValidateJSON -->|无效| ErrorJSON[报告JSON错误]
    ValidateJSON -->|有效| SaveFile[保存批次文件]
    SaveFile --> End([结束])
    ErrorFormat --> End
    ErrorJSON --> End
```

### FC-02: VocabularyBatchSplitter增强流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Splitter as VocabularyBatchSplitter
    participant FileSystem as 文件系统
    
    Client->>Splitter: 初始化(batch_size=500)
    Client->>Splitter: load_vocabulary(input_file)
    Splitter->>FileSystem: 读取原始词汇文件
    FileSystem-->>Splitter: 返回词汇列表
    Splitter-->>Client: 返回词汇数组
    
    Client->>Splitter: create_batch_json(words, batch_num, output_dir)
    Splitter->>Splitter: 为单词添加编号
    Splitter->>Splitter: 生成JSON结构
    Splitter->>FileSystem: 保存JSON文件
    FileSystem-->>Splitter: 确认保存成功
    Splitter-->>Client: 返回文件路径
```

### FC-03: 主脚本更新流程

```mermaid
graph LR
    A[create_first_batch.py] --> B[导入VocabularyBatchSplitter]
    B --> C[设置批次大小为500]
    C --> D[加载词汇列表]
    D --> E[创建JSON格式批次]
    E --> F[输出处理日志]
    F --> G[完成]
```

### FC-04: JSON结果处理流程

```mermaid
flowchart TD
    Input[Gemini JSON结果] --> Validate{验证JSON格式}
    Validate -->|无效| ParseError[解析错误处理]
    Validate -->|有效| CheckFields{检查必需字段}
    CheckFields -->|缺失| FieldError[字段错误处理]
    CheckFields -->|完整| ValidateData{验证数据类型}
    ValidateData -->|错误| TypeError[类型错误处理]
    ValidateData -->|正确| ProcessResults[处理结果数据]
    ProcessResults --> GenerateReport[生成处理报告]
    GenerateReport --> Output[输出最终结果]
    
    ParseError --> ErrorLog[记录错误日志]
    FieldError --> ErrorLog
    TypeError --> ErrorLog
    ErrorLog --> Output
```

## 状态转换图

### 批次处理状态转换

```mermaid
stateDiagram-v2
    [*] --> Initial
    Initial --> Loading: 加载词汇
    Loading --> Loaded: 加载完成
    Loading --> LoadError: 加载失败
    Loaded --> Processing: 开始处理
    Processing --> Numbering: 添加编号
    Numbering --> JsonGeneration: 生成JSON
    JsonGeneration --> Validation: 验证格式
    Validation --> Completed: 验证通过
    Validation --> ValidationError: 验证失败
    Completed --> [*]
    LoadError --> [*]
    ValidationError --> [*]
```

### Gemini处理状态转换

```mermaid
stateDiagram-v2
    [*] --> Waiting
    Waiting --> Processing: 开始处理
    Processing --> Analyzing: 分析单词
    Analyzing --> Categorizing: 分类处理
    Categorizing --> Prioritizing: 优先级排序
    Prioritizing --> Success: 处理成功
    Prioritizing --> PartialSuccess: 部分成功
    Processing --> Error: 处理失败
    Success --> [*]
    PartialSuccess --> [*]
    Error --> [*]
```

## 数据结构对比图

### CSV vs JSON 格式对比

```mermaid
graph TB
    subgraph CSV格式
        CSV1[word,keep,topics,priority]
        CSV2[apple,,,]
        CSV3[banana,,,]
        CSV4[cherry,,,]
    end
    
    subgraph JSON格式
        JSON1[{"id": 1, "word": "apple"}]
        JSON2[{"id": 2, "word": "banana"}]
        JSON3[{"id": 3, "word": "cherry"}]
    end
    
    CSV1 --> JSON1: 转换
    CSV2 --> JSON2: 转换
    CSV3 --> JSON3: 转换
```

### 处理结果对比

```mermaid
graph TB
    subgraph 原始CSV输出
        CSVO1[apple,true,fruit,3]
        CSVO2[banana,true,fruit,2]
        CSVO3[cherry,false,fruit,1]
    end
    
    subgraph 新JSON输出
        JSONO1[{"id": 1, "word": "apple", "keep": true, "topics": ["fruit"], "priority": 3}]
        JSONO2[{"id": 2, "word": "banana", "keep": true, "topics": ["fruit"], "priority": 2}]
        JSONO3[{"id": 3, "word": "cherry", "keep": false, "topics": ["fruit"], "priority": 1}]
    end
    
    CSVO1 --> JSONO1: 格式优化
    CSVO2 --> JSONO2: 格式优化
    CSVO3 --> JSONO3: 格式优化
```

## 架构层次图

```mermaid
graph TD
    subgraph 输入层
        RawData[原始词汇文件]
    end
    
    subgraph 处理层
        Splitter[VocabularyBatchSplitter]
        Numberer[编号生成器]
        JsonGenerator[JSON生成器]
    end
    
    subgraph 输出层
        JsonFile[JSON批次文件]
    end
    
    subgraph 外部处理
        GeminiAPI[Gemini API]
    end
    
    subgraph 结果层
        ProcessedResults[处理结果]
        FinalReport[最终报告]
    end
    
    RawData --> Splitter
    Splitter --> Numberer
    Numberer --> JsonGenerator
    JsonGenerator --> JsonFile
    JsonFile --> GeminiAPI
    GeminiAPI --> ProcessedResults
    ProcessedResults --> FinalReport
```