# 需求："精细化的单词处理状态管理" 的技术方案蓝图

## 0. 依赖关系与影响分析

- [重用] `senseword_content_v4.db`: 现有的SQLite数据库将继续作为主要数据存储，新增的处理队列表将与现有表结构协同工作
- [重用] `words_for_publish` 表: 现有的单词发布表结构保持不变，新的处理队列将通过外键关联到此表
- [重用] `tts_assets` 表: 现有的TTS资产管理表将继续使用，处理队列的TTS标记阶段将与此表交互
- [新增] `word_processing_queue` 表: 全新的看板式处理状态管理表，实现精细化的单词处理流程控制
- [修改] 现有批量处理脚本: 需要适配新的状态管理机制，从全量处理模式转换为基于队列的精细化处理模式

## 1. 项目文件结构概览

```
senseword-content-factory/01-EN/SQLite/
├── workflows/
│   └── 06-精细化单词处理状态管理/           # [新增] 新的工作流目录
│       ├── scripts/
│       │   ├── 01_create_processing_queue_table.py    # [新增] 创建处理队列表
│       │   ├── 02_kanban_word_processor.py             # [新增] 看板式单词处理器
│       │   ├── 03_word_list_exporter.py                # [新增] 单词列表导出器(核心信息管道)
│       │   └── 05_progress_monitor.py                  # [新增] 进度监控工具
│       ├── reports/
│       │   └── processing_progress_report.md           # [新增] 处理进度报告模板
│       └── README_KANBAN_PROCESSING.md                 # [新增] 看板式处理说明文档
├── senseword_content_v4.db                            # [修改] 新增处理队列表
```

## 2. 分支策略建议

- 建议的特性分支名称: `feature/kanban-word-processing`
- 建议的工作目录: `workflows/06-精细化单词处理状态管理/`
- 基础分支: `main`
- 分支创建概念命令:
    ```bash
    # git checkout main
    # git pull origin main
    # git checkout -b feature/kanban-word-processing
    ```

## 3. Commit 规划概要

- [ ] feat(db): 创建word_processing_queue看板式状态管理表
- [ ] feat(processor): 实现KanbanWordProcessor核心处理逻辑
- [ ] feat(exporter): 实现WordListExporter单词列表导出功能(核心信息管道)
- [ ] feat(monitor): 实现进度监控和状态查询功能
- [ ] feat(integration): 与Vertex AI批处理脚本工作流集成
- [ ] test(exporter): 添加单词导出器单元测试
- [ ] docs(workflow): 更新文档说明工作流程信息管道
- [ ] chore(cleanup): 清理临时文件和优化代码结构

## 4. 技术方案蓝图

### `workflows/06-精细化单词处理状态管理/scripts/01_create_processing_queue_table.py`

1. 核心职责 (Responsibilities):
   - 创建word_processing_queue表结构，建立看板式的单词处理状态管理系统的数据基础

2. 技术需求定义 (Technical Requirements):
   - [数据完整性] 必须建立与words_for_publish表的外键约束，确保数据一致性
   - [性能优化] 必须为learningLanguage、scaffoldingLanguage和处理状态字段创建复合索引
   - [向后兼容] 表结构设计必须与现有v4.0数据库架构兼容，不影响现有功能

3. 函数/方法签名 (Function/Method Signatures):
   - `def create_processing_queue_table(db_path: str) -> bool`
   - `def create_indexes(cursor: sqlite3.Cursor) -> None`
   - `def validate_table_creation(cursor: sqlite3.Cursor) -> bool`

4. 数据结构定义 (Data Structures / DTOs):
   ```python
   # 看板式处理状态表结构
   CREATE TABLE word_processing_queue (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       word TEXT NOT NULL,
       learningLanguage TEXT NOT NULL DEFAULT 'en',
       scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',

       # 核心处理流程状态 (看板列)
       contentGenerated BOOLEAN NOT NULL DEFAULT FALSE,
       contentAiReviewed BOOLEAN NOT NULL DEFAULT FALSE,
       ttsIdGenerated BOOLEAN NOT NULL DEFAULT FALSE,
       audioGenerated BOOLEAN NOT NULL DEFAULT FALSE,
       readyForPublish BOOLEAN NOT NULL DEFAULT FALSE,

       # 基本信息
       createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

       UNIQUE(word, learningLanguage, scaffoldingLanguage)
   );
   ```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
   1. [连接数据库] 建立到senseword_content_v4.db的连接
   2. [检查表存在] 验证word_processing_queue表是否已存在，如存在则跳过创建
   3. [创建表结构] 执行CREATE TABLE语句，建立看板式状态管理表
   4. [创建索引] 为语言对和处理状态创建性能优化索引
   5. [验证创建] 查询表结构确认创建成功，验证所有字段和约束
   6. [记录日志] 记录表创建成功的详细信息和统计数据

### `workflows/06-精细化单词处理状态管理/scripts/02_kanban_word_processor.py`

1. 核心职责 (Responsibilities):
   - 实现看板式单词处理的核心业务逻辑，提供单词状态筛选、TXT文件输出和状态更新功能，作为工作流程的信息管道

2. 技术需求定义 (Technical Requirements):
   - [文件输出] 必须支持将筛选结果输出为TXT文件，一行一个单词，支持指定输出目录
   - [状态筛选] 支持基于状态字段组合的复杂查询，精确筛选待处理单词
   - [事务安全] 所有状态更新操作必须在数据库事务中执行，确保数据一致性
   - [工作流集成] 设计为工作流程信息管道，与外部Vertex AI批处理脚本无缝集成

3. 函数/方法签名 (Function/Method Signatures):
   - `def add_words_to_queue(words: List[str], learning_lang: str, scaffolding_lang: str) -> int`
   - `def export_words_by_stage(stage: str, learning_lang: str, scaffolding_lang: str, output_dir: str, filename: str) -> str`
   - `def update_word_stage(word: str, stage: str, learning_lang: str, scaffolding_lang: str) -> bool`
   - `def batch_update_stage(words: List[str], stage: str, learning_lang: str, scaffolding_lang: str) -> int`

4. 数据结构定义 (Data Structures / DTOs):
   ```python
   # 单词处理状态数据结构
   class WordProcessingState:
       word: str
       learningLanguage: str
       scaffoldingLanguage: str
       contentGenerated: bool
       contentAiReviewed: bool
       ttsIdGenerated: bool
       audioGenerated: bool
       readyForPublish: bool
       createdAt: str
       updatedAt: str

   # 处理统计结构
   class ProcessingStats:
       learningLanguage: str
       scaffoldingLanguage: str
       totalWords: int
       contentGeneratedCount: int
       contentAiReviewedCount: int
       ttsIdGeneratedCount: int
       audioGeneratedCount: int
       readyForPublishCount: int
       completionPercentage: float
   ```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
   1. [单词添加] 批量插入单词到处理队列，设置初始状态为全部FALSE
   2. [状态筛选] 根据状态字段组合查询符合条件的单词列表
   3. [文件输出] 将筛选结果写入TXT文件，一行一个单词，保存到指定目录
   4. [状态更新] 支持单个和批量状态更新，作为外部脚本处理完成后的回调
   5. [工作流集成] 提供标准化的文件接口，与Vertex AI批处理脚本无缝对接

### `workflows/06-精细化单词处理状态管理/scripts/03_word_list_exporter.py`

1. 核心职责 (Responsibilities):
   - 专门负责根据状态筛选单词并输出为TXT文件，作为工作流程中的核心信息管道

2. 技术需求定义 (Technical Requirements):
   - [文件格式] 输出标准TXT文件，UTF-8编码，一行一个单词，无额外格式
   - [目录管理] 支持指定输出目录，自动创建不存在的目录结构
   - [文件命名] 支持自定义文件名，建议使用描述性命名如"words_for_ai_review.txt"
   - [状态组合] 支持复杂的状态字段组合查询，精确筛选目标单词

3. 函数/方法签名 (Function/Method Signatures):
   - `def export_pending_content_generation(learning_lang: str, scaffolding_lang: str, output_dir: str) -> str`
   - `def export_pending_ai_review(learning_lang: str, scaffolding_lang: str, output_dir: str) -> str`
   - `def export_pending_tts_id_generation(learning_lang: str, scaffolding_lang: str, output_dir: str) -> str`
   - `def export_pending_audio_generation(learning_lang: str, scaffolding_lang: str, output_dir: str) -> str`
   - `def export_custom_filter(filter_conditions: Dict[str, bool], learning_lang: str, scaffolding_lang: str, output_dir: str, filename: str) -> str`

4. 数据结构定义 (Data Structures / DTOs):
   ```python
   # 导出配置结构
   class ExportConfig:
       learningLanguage: str
       scaffoldingLanguage: str
       outputDirectory: str
       filename: str
       filterConditions: Dict[str, bool]

   # 导出结果结构
   class ExportResult:
       success: bool
       filePath: str
       wordCount: int
       exportTime: str
       errorMessage: str

   # 预定义筛选条件
   class FilterPresets:
       PENDING_CONTENT_GENERATION = {"contentGenerated": False}
       PENDING_AI_REVIEW = {"contentGenerated": True, "contentAiReviewed": False}
       PENDING_TTS_ID = {"contentAiReviewed": True, "ttsIdGenerated": False}
       PENDING_AUDIO = {"ttsIdGenerated": True, "audioGenerated": False}
       READY_FOR_PUBLISH = {"audioGenerated": True, "readyForPublish": False}
   ```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
   1. [条件构建] 根据预设或自定义条件构建SQL WHERE子句
   2. [数据查询] 执行查询获取符合条件的单词列表
   3. [目录检查] 检查输出目录是否存在，不存在则创建
   4. [文件写入] 将单词列表写入TXT文件，一行一个单词
   5. [结果返回] 返回文件路径和导出统计信息

### `workflows/06-精细化单词处理状态管理/scripts/05_progress_monitor.py`

1. 核心职责 (Responsibilities):
   - 提供实时的处理进度监控、统计报告生成和可视化展示功能

2. 技术需求定义 (Technical Requirements):
   - [实时性] 进度数据更新延迟不超过5秒，支持实时监控
   - [可视化] 生成清晰的进度图表和统计报告，支持多种输出格式
   - [历史追踪] 记录处理历史数据，支持趋势分析和性能优化
   - [告警机制] 检测异常情况并发送告警，如处理卡住、错误率过高

3. 函数/方法签名 (Function/Method Signatures):
   - `def generate_progress_report(learning_lang: str, scaffolding_lang: str, output_format: str) -> str`
   - `def get_real_time_stats(learning_lang: str, scaffolding_lang: str) -> Dict[str, Any]`
   - `def detect_stuck_words(threshold_hours: int, learning_lang: str, scaffolding_lang: str) -> List[Dict[str, Any]]`
   - `def export_processing_history(start_date: str, end_date: str, learning_lang: str, scaffolding_lang: str) -> str`

4. 数据结构定义 (Data Structures / DTOs):
   ```python
   # 实时统计数据结构
   class RealTimeStats:
       learningLanguage: str
       scaffoldingLanguage: str
       totalWordsInQueue: int
       wordsByStage: Dict[str, int]
       averageProcessingTime: float
       errorRate: float
       lastUpdated: str

   # 卡住的单词信息
   class StuckWord:
       word: str
       learningLanguage: str
       scaffoldingLanguage: str
       currentStage: str
       stuckDurationHours: float
       lastUpdated: str

   # 进度报告结构
   class ProgressReport:
       learningLanguage: str
       scaffoldingLanguage: str
       reportTime: str
       stageProgress: Dict[str, float]
       estimatedCompletionTime: str
       bottleneckStage: str
       recommendations: List[str]
   ```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
   1. [数据收集] 查询数据库获取指定语言对的最新处理状态统计信息
   2. [进度计算] 计算各阶段完成百分比和整体进度
   3. [异常检测] 识别处理时间异常的单词，基于updatedAt字段判断卡住状态
   4. [报告生成] 生成格式化的进度报告，支持Markdown、JSON等格式
   5. [趋势分析] 分析历史数据，预测完成时间和识别性能瓶颈

## 5. AI Agent 需要了解的文件上下文

<context_files>
senseword-content-factory/01-EN/SQLite/senseword_content_v4.db
senseword-content-factory/01-EN/SQLite/schemas/words_for_publish_schema.sql
senseword-content-factory/01-EN/SQLite/schemas/tts_assets_schema.sql
senseword-content-factory/01-EN/scripts/content_generation/
senseword-content-factory/01-EN/scripts/tts_processing/
senseword-content-factory/01-EN/scripts/utils/database_utils.py
senseword-content-factory/01-EN/scripts/utils/logging_utils.py
senseword-content-factory/01-EN/config/database_config.py
senseword-content-factory/01-EN/requirements.txt
</context_files>

## 6. 冲突检查报告

### 6.1 数据库架构验证 ✅

#### 现有数据库结构确认
- **✅ senseword_content_v4.db**: 已实现，包含`words_for_publish`、`tts_assets`等核心表
- **✅ words_for_publish表**: 已实现，包含`word`、`learningLanguage`、`scaffoldingLanguage`字段
- **✅ tts_assets表**: 已实现，支持TTS资产管理和hash ID存储

#### 新增数据模型需求
- **⚠️ word_processing_queue表**: 需要新增，技术方案中已定义完整结构
- **⚠️ 处理状态索引**: 需要新增，技术方案中已定义优化索引

### 6.2 现有脚本兼容性验证 ✅

#### 内容生成脚本兼容性
- **✅ 内容生成流程**: 现有脚本可无缝集成到新的状态管理系统
- **✅ 数据输入输出**: 现有脚本的输入输出格式与新系统兼容
- **✅ 错误处理机制**: 现有的错误处理可适配新的状态更新机制

#### TTS处理脚本兼容性
- **✅ TTS生成流程**: 现有TTS脚本可集成到audio_generated状态更新
- **✅ Hash ID管理**: 现有的hash ID系统与tts_marked阶段完全兼容
- **✅ 音频文件组织**: 现有的音频文件结构不受新系统影响

### 6.3 技术栈兼容性验证 ✅

#### Python环境兼容性
- **✅ Python版本**: 项目使用Python 3.8+，支持所有新功能特性
- **✅ SQLite支持**: 支持布尔字段、复合索引和外键约束
- **✅ 依赖库**: 现有的sqlite3、pandas等库完全满足新系统需求

#### 工作流集成兼容性
- **✅ 目录结构**: 新的workflows目录与现有结构完全兼容
- **✅ 脚本调用**: 新脚本可通过现有的调用机制集成
- **✅ 日志系统**: 可复用现有的logging_utils.py日志工具

### 6.4 潜在风险识别 ⚠️

#### 中等风险项
1. **数据迁移风险**: 现有单词数据需要迁移到新的处理队列表，建议分批迁移并验证数据完整性
2. **性能影响**: 新增的状态查询可能影响数据库性能，建议监控查询执行时间并优化索引

#### 低风险项
1. **脚本集成复杂度**: 现有脚本集成到新系统需要适配工作，但改动量较小
2. **学习成本**: 团队需要熟悉新的看板式处理概念，但逻辑相对简单

### 6.5 修正建议 📝

#### 技术方案微调
1. **渐进式迁移**: 建议先在测试环境验证新系统，然后逐步迁移生产数据
2. **向后兼容**: 保留现有批量处理脚本作为备用方案，确保系统稳定性

#### 实现优先级调整
1. **优先级1**: 创建数据库表结构和基础索引
2. **优先级2**: 实现核心的KanbanWordProcessor处理逻辑
3. **优先级3**: 集成现有内容生成和TTS脚本
4. **优先级4**: 实现进度监控和可视化功能

### 6.6 结论 ✅

**技术方案与现有项目实现高度兼容**，主要发现：

1. **✅ 数据库兼容性**: 新表结构与现有v4.0数据库完全兼容，无冲突风险
2. **✅ 脚本集成性**: 现有处理脚本可平滑集成到新的状态管理系统
3. **✅ 技术栈一致性**: 完全基于现有Python+SQLite技术栈，无新依赖
4. **⚠️ 迁移复杂度**: 需要谨慎处理数据迁移和脚本适配工作
5. **📈 系统改进**: 新系统将显著提升处理灵活性和状态可见性

**建议继续技术方案实施**，采用渐进式部署策略，先在测试环境验证完整性，再逐步推广到生产环境。

## 7. audioGenerated状态联动更新机制

### 7.1 核心联动逻辑

当特定单词的所有TTS资产全部完成后，需要自动更新该单词在`word_processing_queue`表中的`audioGenerated`字段为`TRUE`。

#### 联动触发条件
```sql
-- 检查单词的所有TTS资产是否全部完成
SELECT COUNT(*) as total_assets,
       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_assets
FROM tts_assets
WHERE sourceWord = ?
  AND learningLanguage = ?
  AND scaffoldingLanguage = ?;

-- 当 total_assets = completed_assets 时，触发audioGenerated更新
```

### 7.2 联动更新实现

#### 方案A：TTS同步脚本中的联动检查

```python
class TTSStatusSynchronizer:
    """TTS状态同步器 - 包含audioGenerated联动更新"""

    async def sync_completed_tasks_with_linkage(self, hours_back: int = 24) -> SyncResult:
        """同步完成任务并检查audioGenerated联动"""

        # 1. 执行基础的TTS状态同步
        sync_result = await self.sync_completed_tasks(hours_back)

        # 2. 获取本次同步涉及的单词列表
        affected_words = self.get_affected_words_from_sync(sync_result)

        # 3. 检查每个单词的音频完成状态
        audio_completed_words = []
        for word_info in affected_words:
            if await self.check_word_audio_completion(word_info):
                audio_completed_words.append(word_info)

        # 4. 批量更新audioGenerated状态
        if audio_completed_words:
            await self.batch_update_audio_generated(audio_completed_words)

        return SyncResult(
            synced_count=sync_result.synced_count,
            audio_completed_words=len(audio_completed_words)
        )

    async def check_word_audio_completion(self, word_info: WordInfo) -> bool:
        """检查单词的所有音频是否完成"""

        cursor = self.local_db.cursor()
        cursor.execute("""
            SELECT COUNT(*) as total_assets,
                   COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_assets
            FROM tts_assets
            WHERE sourceWord = ?
              AND learningLanguage = ?
              AND scaffoldingLanguage = ?
        """, (word_info.word, word_info.learning_language, word_info.scaffolding_language))

        result = cursor.fetchone()
        total_assets, completed_assets = result

        # 所有资产都完成时返回True
        return total_assets > 0 and total_assets == completed_assets

    async def batch_update_audio_generated(self, words: List[WordInfo]) -> None:
        """批量更新audioGenerated状态"""

        cursor = self.local_db.cursor()

        for word_info in words:
            cursor.execute("""
                UPDATE word_processing_queue
                SET audioGenerated = TRUE,
                    updatedAt = CURRENT_TIMESTAMP
                WHERE word = ?
                  AND learningLanguage = ?
                  AND scaffoldingLanguage = ?
                  AND audioGenerated = FALSE
            """, (word_info.word, word_info.learning_language, word_info.scaffolding_language))

        self.local_db.commit()
        print(f"已更新 {len(words)} 个单词的audioGenerated状态")
```

#### 方案B：数据库触发器实现联动

```sql
-- 创建触发器：当tts_assets状态更新时检查联动
CREATE TRIGGER update_audio_generated_on_tts_completion
AFTER UPDATE OF status ON tts_assets
WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    -- 检查该单词的所有TTS资产是否全部完成
    UPDATE word_processing_queue
    SET audioGenerated = TRUE,
        updatedAt = CURRENT_TIMESTAMP
    WHERE word = NEW.sourceWord
      AND learningLanguage = NEW.learningLanguage
      AND scaffoldingLanguage = NEW.scaffoldingLanguage
      AND audioGenerated = FALSE
      AND NOT EXISTS (
          SELECT 1 FROM tts_assets
          WHERE sourceWord = NEW.sourceWord
            AND learningLanguage = NEW.learningLanguage
            AND scaffoldingLanguage = NEW.scaffoldingLanguage
            AND status != 'completed'
      );
END;
```

### 7.3 联动状态验证

#### 状态一致性检查脚本

```python
class AudioGeneratedValidator:
    """audioGenerated状态一致性验证器"""

    def validate_audio_generated_consistency(self) -> ValidationReport:
        """验证audioGenerated状态与实际TTS完成情况的一致性"""

        cursor = self.db.cursor()

        # 1. 查找audioGenerated=TRUE但TTS未全部完成的单词
        cursor.execute("""
            SELECT wpq.word, wpq.learningLanguage, wpq.scaffoldingLanguage,
                   COUNT(ta.ttsId) as total_assets,
                   COUNT(CASE WHEN ta.status = 'completed' THEN 1 END) as completed_assets
            FROM word_processing_queue wpq
            LEFT JOIN tts_assets ta ON wpq.word = ta.sourceWord
                AND wpq.learningLanguage = ta.learningLanguage
                AND wpq.scaffoldingLanguage = ta.scaffoldingLanguage
            WHERE wpq.audioGenerated = TRUE
            GROUP BY wpq.word, wpq.learningLanguage, wpq.scaffoldingLanguage
            HAVING total_assets != completed_assets OR total_assets = 0
        """)

        false_positives = cursor.fetchall()

        # 2. 查找audioGenerated=FALSE但TTS已全部完成的单词
        cursor.execute("""
            SELECT wpq.word, wpq.learningLanguage, wpq.scaffoldingLanguage,
                   COUNT(ta.ttsId) as total_assets,
                   COUNT(CASE WHEN ta.status = 'completed' THEN 1 END) as completed_assets
            FROM word_processing_queue wpq
            LEFT JOIN tts_assets ta ON wpq.word = ta.sourceWord
                AND wpq.learningLanguage = ta.learningLanguage
                AND wpq.scaffoldingLanguage = ta.scaffoldingLanguage
            WHERE wpq.audioGenerated = FALSE
            GROUP BY wpq.word, wpq.learningLanguage, wpq.scaffoldingLanguage
            HAVING total_assets > 0 AND total_assets = completed_assets
        """)

        false_negatives = cursor.fetchall()

        return ValidationReport(
            false_positives=false_positives,
            false_negatives=false_negatives,
            total_inconsistencies=len(false_positives) + len(false_negatives)
        )

    def fix_audio_generated_inconsistencies(self, report: ValidationReport) -> None:
        """修复audioGenerated状态不一致问题"""

        cursor = self.db.cursor()

        # 修复假阳性：将不应该为TRUE的设为FALSE
        for word_info in report.false_positives:
            cursor.execute("""
                UPDATE word_processing_queue
                SET audioGenerated = FALSE, updatedAt = CURRENT_TIMESTAMP
                WHERE word = ? AND learningLanguage = ? AND scaffoldingLanguage = ?
            """, word_info[:3])

        # 修复假阴性：将应该为TRUE的设为TRUE
        for word_info in report.false_negatives:
            cursor.execute("""
                UPDATE word_processing_queue
                SET audioGenerated = TRUE, updatedAt = CURRENT_TIMESTAMP
                WHERE word = ? AND learningLanguage = ? AND scaffoldingLanguage = ?
            """, word_info[:3])

        self.db.commit()
        print(f"已修复 {report.total_inconsistencies} 个audioGenerated状态不一致问题")
```

### 7.4 联动机制的集成策略

#### 推荐实施方案

1. **主要机制**: 使用方案A（TTS同步脚本中的联动检查）
   - 优势：逻辑清晰，易于调试和维护
   - 适用：与现有的TTS同步流程无缝集成

2. **备用机制**: 使用方案B（数据库触发器）作为兜底
   - 优势：实时性强，无需额外脚本调用
   - 适用：确保数据一致性的最后防线

3. **验证机制**: 定期运行状态一致性检查
   - 频率：每日或每周运行一次
   - 目的：发现和修复潜在的状态不一致问题

#### 集成到现有工作流

```python
# 在TTS同步脚本中集成联动更新
async def main():
    synchronizer = TTSStatusSynchronizer(config)

    # 执行TTS状态同步，包含audioGenerated联动更新
    result = await synchronizer.sync_completed_tasks_with_linkage(hours_back=24)

    print(f"TTS同步完成: {result.synced_count} 个任务")
    print(f"audioGenerated更新: {result.audio_completed_words} 个单词")

    # 可选：运行状态一致性验证
    validator = AudioGeneratedValidator()
    validation_report = validator.validate_audio_generated_consistency()

    if validation_report.total_inconsistencies > 0:
        print(f"发现 {validation_report.total_inconsistencies} 个状态不一致，正在修复...")
        validator.fix_audio_generated_inconsistencies(validation_report)
```

这个联动机制确保了`audioGenerated`状态与实际的TTS完成情况保持一致，为后续的`readyForPublish`状态判断提供了可靠的基础。