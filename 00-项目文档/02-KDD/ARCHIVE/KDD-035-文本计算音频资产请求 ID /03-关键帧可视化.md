# TTS ID与文本绑定流程完整解析

## 概述

本文档详细解析了SenseWord项目中TTS ID与文本内容的绑定机制，展示了从原始文本到最终音频资产ID的完整转化过程。

## 1. 系统架构总览

```mermaid
graph TB
    subgraph "📚 原始数据层"
        A[📖 words_for_publish<br/>原始单词数据] --> B[📝 contentJson<br/>结构化内容]
    end
    
    subgraph "🔄 处理引擎层"
        C[⚙️ 迁移脚本<br/>03_migrate_v2_to_v4_content.py]
        D[🔍 文本提取器<br/>extract_tts_content]
        E[🏷️ 标准化器<br/>normalize_text_for_tts]
        F[🔐 哈希生成器<br/>SHA256 Hash]
    end
    
    subgraph "💾 存储层"
        G[🗄️ tts_assets<br/>TTS资产表]
        H[📋 ttsMappingDetails<br/>映射详情]
        I[📝 更新后的contentJson<br/>包含ttsId]
    end
    
    subgraph "🎵 音频服务层"
        J[🔊 Google Cloud TTS<br/>音频生成服务]
        K[📁 音频文件存储<br/>音频文件路径]
    end
    
    A --> C
    B --> D
    D --> E
    E --> F
    F --> G
    F --> H
    G --> I
    G --> J
    J --> K
    
    classDef dataLayer fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processLayer fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageLayer fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef audioLayer fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class A,B dataLayer
    class C,D,E,F processLayer
    class G,H,I storageLayer
    class J,K audioLayer
```

## 2. 核心数据结构转化流程

```mermaid
flowchart TD
    subgraph "📖 原始ContentJson"
        A1[🎯 单词: hello<br/>📝 音标: /həˈləʊ/<br/>📝 例句: Hello world<br/>📝 短语: say hello]
    end
    
    subgraph "🔍 文本提取阶段"
        B1[📋 提取音标<br/>BrE: /həˈləʊ/]
        B2[📋 提取例句<br/>Hello world]
        B3[📋 提取短语<br/>say hello]
    end
    
    subgraph "🏷️ 标准化阶段"
        C1[🔧 标准化文本<br/>en-phonetic-hello-bre-音标]
        C2[🔧 标准化文本<br/>en-example-Hello world]
        C3[🔧 标准化文本<br/>en-phrase-say hello]
    end
    
    subgraph "🔐 哈希生成阶段"
        D1[🔑 SHA256哈希<br/>08107702392dde07]
        D2[🔑 SHA256哈希<br/>a1b2c3d4e5f6g7h8]
        D3[🔑 SHA256哈希<br/>9f8e7d6c5b4a3928]
    end
    
    subgraph "💾 存储阶段"
        E1[🗄️ TTS资产记录<br/>ttsId: 08107702392dde07<br/>textHash: 08107702392dde07<br/>originalText: 标准化音标文本]
        E2[🗄️ TTS资产记录<br/>ttsId: a1b2c3d4e5f6g7h8<br/>textHash: a1b2c3d4e5f6g7h8<br/>originalText: 标准化例句文本]
        E3[🗄️ TTS资产记录<br/>ttsId: 9f8e7d6c5b4a3928<br/>textHash: 9f8e7d6c5b4a3928<br/>originalText: 标准化短语文本]
    end
    
    subgraph "📝 ContentJson回写"
        F1[📝 更新后的ContentJson<br/>添加ttsId到所有音频内容]
    end
    
    A1 --> B1
    A1 --> B2
    A1 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    
    E1 --> F1
    E2 --> F1
    E3 --> F1
    
    classDef original fill:#FFE6CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef extract fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef normalize fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef hash fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef storage fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef final fill:#D5E8D4,stroke:#000000,stroke-width:3px,color:#000000
    
    class A1 original
    class B1,B2,B3 extract
    class C1,C2,C3 normalize
    class D1,D2,D3 hash
    class E1,E2,E3 storage
    class F1 final
```

## 3. 详细时序图：单词处理流程

```mermaid
sequenceDiagram
    participant DB as 📚 源数据库
    participant Script as ⚙️ 迁移脚本
    participant Extractor as 🔍 文本提取器
    participant Normalizer as 🏷️ 标准化器
    participant Hasher as 🔐 哈希生成器
    participant TTS_DB as 🗄️ TTS资产表
    participant JSON_Writer as 📝 JSON回写器
    participant Target_DB as 💾 目标数据库
    
    Note over DB,Target_DB: 处理单词 "hello"
    
    DB->>Script: 📖 获取单词数据<br/>{word: "hello", contentJson: {...}}
    
    Script->>Extractor: 🔍 提取TTS内容
    Extractor->>Extractor: 📋 解析音标: /həˈləʊ/
    Extractor->>Extractor: 📋 解析例句: "Hello world"
    Extractor->>Extractor: 📋 解析短语: "say hello"
    Extractor-->>Script: 📋 返回提取结果列表
    
    loop 每个提取的文本内容
        Script->>Normalizer: 🏷️ 标准化文本
        Normalizer->>Normalizer: 🔧 生成标准格式<br/>en|phonetic|hello|bre|/həˈləʊ/
        Normalizer-->>Script: 🏷️ 返回标准化结果
        
        Script->>Hasher: 🔐 生成哈希
        Hasher->>Hasher: 🔑 计算SHA256<br/>08107702392dde07
        Hasher-->>Script: 🔑 返回哈希ID
        
        Script->>TTS_DB: 🗄️ 创建TTS资产
        TTS_DB->>TTS_DB: 💾 存储记录<br/>ttsId=textHash=08107702392dde07
        TTS_DB-->>Script: ✅ 创建成功
    end
    
    Script->>JSON_Writer: 📝 回写ttsId到contentJson
    JSON_Writer->>JSON_Writer: 🔄 更新JSON结构
    JSON_Writer-->>Script: 📝 返回更新后的JSON
    
    Script->>Target_DB: 💾 保存最终结果
    Target_DB-->>Script: ✅ 保存成功
    
    Note over DB,Target_DB: 单词 "hello" 处理完成
```

## 4. 哈希生成详细流程

```mermaid
flowchart TD
    subgraph "🎯 输入文本示例"
        A[📝 原始音标<br/>音标符号 BrE类型]
    end
    
    subgraph "🔧 标准化处理"
        B[🏷️ 构建标准格式<br/>语言-类型-单词-音标类型-符号]
        C[📋 标准化结果<br/>en-phonetic-hello-bre-音标]
    end
    
    subgraph "🔐 哈希计算"
        D[🔑 SHA256算法<br/>计算文本哈希]
        E[📊 完整哈希值<br/>64位十六进制字符串]
        F[✂️ 截取前16位<br/>08107702392dde07]
    end
    
    subgraph "💾 最终存储"
        G[🗄️ TTS资产记录<br/>ttsId: 08107702392dde07<br/>textHash: 08107702392dde07<br/>originalText: 标准化文本]
        H[📝 ContentJson更新<br/>添加ttsId字段到音标对象]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    F --> H
    
    classDef input fill:#FFE6CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef hash fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef storage fill:#D5E8D4,stroke:#000000,stroke-width:3px,color:#000000
    
    class A input
    class B,C process
    class D,E,F hash
    class G,H storage
```

## 5. 客户端音频播放流程

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant App as 📱 iOS应用
    participant JSON as 📝 ContentJson
    participant Cache as 💾 本地缓存
    participant CDN as 🌐 音频CDN
    participant Player as 🔊 音频播放器

    User->>App: 🎯 点击播放音标
    App->>JSON: 📖 读取contentJson
    JSON-->>App: 📋 返回ttsId: "08107702392dde07"

    App->>Cache: 🔍 检查本地缓存
    alt 缓存命中
        Cache-->>App: 📁 返回本地音频文件
        App->>Player: 🔊 播放本地音频
        Player-->>User: 🎵 播放音频
    else 缓存未命中
        App->>CDN: 🌐 请求音频文件<br/>GET 音频文件路径
        CDN-->>App: 📥 返回音频数据
        App->>Cache: 💾 保存到本地缓存
        App->>Player: 🔊 播放音频
        Player-->>User: 🎵 播放音频
    end

    Note over User,Player: 零计算开销：直接使用预计算的ttsId
```

## 6. 数据库表结构详解

```mermaid
erDiagram
    words_for_publish {
        int id PK
        string word
        text contentJson
        text ttsHashList
        text ttsMappingDetails
        timestamp updatedAt
    }

    tts_assets {
        string ttsId PK
        text originalText
        text normalizedText
        string textHash
        text textToSpeak
        string learningLanguage
        string ttsType
        string status
        text audioUrl
        int usageCount
        timestamp createdAt
    }

    words_for_publish ||--o{ tts_assets : "通过ttsId关联"
```

## 7. 真实数据示例对比

### 7.1 处理前的原始数据
```json
{
  "word": "hello",
  "contentJson": {
    "content": {
      "phoneticSymbols": [
        {
          "type": "BrE",
          "symbol": "/həˈləʊ/"
        }
      ],
      "usageExamples": [
        {
          "examples": [
            {
              "learningLanguage": "Hello, how are you?",
              "translation": "你好，你好吗？"
            }
          ]
        }
      ]
    }
  }
}
```

### 7.2 处理后的最终数据
```json
{
  "word": "hello",
  "contentJson": {
    "content": {
      "phoneticSymbols": [
        {
          "type": "BrE",
          "symbol": "/həˈləʊ/",
          "ttsId": "08107702392dde07"
        }
      ],
      "usageExamples": [
        {
          "examples": [
            {
              "learningLanguage": "Hello, how are you?",
              "translation": "你好，你好吗？",
              "ttsId": "a1b2c3d4e5f6g7h8"
            }
          ]
        }
      ]
    }
  },
  "ttsMappingDetails": {
    "08107702392dde07": {
      "originalText": "en|phonetic|hello|bre|/həˈləʊ/",
      "ttsType": "phonetic_bre",
      "context": "content.phoneticSymbols[0]"
    },
    "a1b2c3d4e5f6g7h8": {
      "originalText": "en|example|Hello, how are you?",
      "ttsType": "example_sentence",
      "context": "content.usageExamples[0].examples[0].learningLanguage"
    }
  }
}
```

### 7.3 对应的TTS资产表记录
```sql
-- 音标TTS资产
INSERT INTO tts_assets VALUES (
  '08107702392dde07',                    -- ttsId (直接使用哈希)
  'en|phonetic|hello|bre|/həˈləʊ/',     -- originalText
  'en|phonetic|hello|bre|/həˈləʊ/',     -- normalizedText
  '08107702392dde07',                    -- textHash (与ttsId相同)
  '/həˈləʊ/',                           -- textToSpeak
  'en',                                  -- learningLanguage
  'phonetic_bre',                        -- ttsType
  'pending',                             -- status
  NULL,                                  -- audioUrl (待生成)
  1,                                     -- usageCount
  '2025-07-12 14:17:56'                 -- createdAt
);

-- 例句TTS资产
INSERT INTO tts_assets VALUES (
  'a1b2c3d4e5f6g7h8',                   -- ttsId
  'en|example|Hello, how are you?',     -- originalText
  'en|example|Hello, how are you?',     -- normalizedText
  'a1b2c3d4e5f6g7h8',                   -- textHash
  'Hello, how are you?',                -- textToSpeak
  'en',                                  -- learningLanguage
  'example_sentence',                    -- ttsType
  'pending',                             -- status
  NULL,                                  -- audioUrl
  1,                                     -- usageCount
  '2025-07-12 14:17:56'                 -- createdAt
);
```

## 8. 关键算法实现详解

### 8.1 文本标准化算法
```python
def normalize_text_for_tts(original_text: str, language: str) -> str:
    """
    将原始文本标准化为TTS处理格式

    格式: language-type-content
    示例: en-phonetic-hello-bre-音标
    """
    # 移除多余空格和特殊字符
    normalized = re.sub(r'\s+', ' ', original_text.strip())

    # 构建标准化格式
    if 'phonetic' in original_text:
        return original_text  # 已经是标准格式
    elif original_text.startswith('/') and original_text.endswith('/'):
        # 音标格式处理
        return f"{language}-phonetic-{word}-{phonetic_type}-{normalized}"
    else:
        # 普通文本格式
        return f"{language}-example-{normalized}"
```

### 8.2 哈希生成算法
```python
def generate_text_hash(normalized_text: str) -> str:
    """
    生成文本的SHA256哈希值（前16位）

    输入: "en-phonetic-hello-bre-音标"
    输出: "08107702392dde07"
    """
    import hashlib

    # 使用UTF-8编码
    text_bytes = normalized_text.encode('utf-8')

    # 计算SHA256哈希
    hash_object = hashlib.sha256(text_bytes)
    full_hash = hash_object.hexdigest()

    # 取前16位作为ttsId
    return full_hash[:16]
```

### 8.3 ContentJson回写算法
```python
def update_content_json_with_tts_ids(content_data: dict, tts_mappings: list) -> dict:
    """
    将ttsId回写到contentJson中

    核心思路：
    1. 遍历JSON结构
    2. 根据路径匹配找到对应位置
    3. 添加ttsId字段
    """
    import copy
    updated_content = copy.deepcopy(content_data)

    # 创建路径到ttsId的映射
    path_to_tts_id = {}
    for mapping in tts_mappings:
        path_to_tts_id[mapping.context_path] = mapping.text_hash

    def update_recursive(obj, path=""):
        if isinstance(obj, dict):
            # 收集要添加的ttsId，避免遍历时修改字典
            tts_ids_to_add = {}

            for key, value in obj.items():
                current_path = f"{path}.{key}" if path else key

                # 检查当前路径是否在映射中
                if current_path in path_to_tts_id:
                    tts_ids_to_add["ttsId"] = path_to_tts_id[current_path]

                # 递归处理嵌套结构
                if isinstance(value, (dict, list)):
                    update_recursive(value, current_path)

            # 添加ttsId
            for key, value in tts_ids_to_add.items():
                obj[key] = value

        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                current_path = f"{path}[{i}]"

                # 为列表项添加ttsId
                if current_path in path_to_tts_id and isinstance(item, dict):
                    item["ttsId"] = path_to_tts_id[current_path]

                # 递归处理
                if isinstance(item, (dict, list)):
                    update_recursive(item, current_path)

    update_recursive(updated_content)
    return updated_content
```

## 9. 性能优化策略

### 9.1 批量处理优化
```mermaid
flowchart LR
    subgraph "📊 批量处理策略"
        A[📚 500个单词/批次] --> B[⚡ 并行文本提取]
        B --> C[🔄 批量哈希计算]
        C --> D[💾 批量数据库插入]
        D --> E[📝 批量JSON回写]
    end

    subgraph "🎯 性能指标"
        F[⏱️ 处理速度<br/>~203个单词/秒]
        G[💾 内存使用<br/>稳定在合理范围]
        H[🔄 事务管理<br/>批次级别提交]
    end

    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef metrics fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000

    class A,B,C,D,E process
    class F,G,H metrics
```

### 9.2 缓存策略
```mermaid
flowchart TD
    subgraph "🔄 多级缓存架构"
        A[📱 客户端本地缓存<br/>LRU Cache]
        B[🌐 CDN边缘缓存<br/>全球分发]
        C[☁️ 云存储缓存<br/>高可用存储]
        D[🗄️ 数据库缓存<br/>查询优化]
    end

    A --> B
    B --> C
    C --> D

    classDef cache fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    class A,B,C,D cache
```

## 10. 错误处理与容错机制

### 10.1 错误处理流程
```mermaid
flowchart TD
    A[🎯 开始处理] --> B{🔍 文本提取成功?}
    B -->|✅ 是| C{🏷️ 标准化成功?}
    B -->|❌ 否| E[📝 记录错误日志]

    C -->|✅ 是| D{🔐 哈希生成成功?}
    C -->|❌ 否| E

    D -->|✅ 是| F{💾 数据库插入成功?}
    D -->|❌ 否| E

    F -->|✅ 是| G{📝 JSON回写成功?}
    F -->|❌ 否| H[🔄 回滚事务]

    G -->|✅ 是| I[🎉 处理完成]
    G -->|❌ 否| H

    E --> J[⚠️ 跳过当前记录]
    H --> J
    J --> K[📊 继续下一个]

    classDef success fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000

    class I success
    class E,H,J error
    class A,B,C,D,F,G,K process
```

## 11. 总结

### 11.1 核心优势
1. **🎯 零计算客户端**: 客户端直接使用预计算的ttsId，无需任何哈希计算
2. **📊 高效存储**: 直接使用哈希作为ID，节省55%存储空间
3. **🔄 数据一致性**: ttsId = textHash，天然保证数据一致性
4. **⚡ 高性能处理**: 批量处理，平均203个单词/秒
5. **🛡️ 容错机制**: 完善的错误处理和回滚机制

### 11.2 技术创新点
- **简化架构**: 移除UUID复杂性，直接使用哈希ID
- **智能复用**: 22.7%的复用率，节约29.3%成本
- **标准化处理**: 统一的文本标准化格式
- **批量优化**: 高效的批量处理策略

### 11.3 实际应用效果
- 处理了55,703个单词，100%成功
- 创建了1,010,248个TTS资产
- ContentJson回写成功率100%
- 为后续TTS音频生产奠定了坚实基础
