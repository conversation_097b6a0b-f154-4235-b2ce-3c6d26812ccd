# KDD-035 函数契约补间链：文本计算音频资产请求ID系统 v4.0

## 0. 依赖关系与影响分析
- [重用] `words_for_publish` 表：现有表结构将被扩展，新增TTS相关字段
- [新增] `tts_assets` 表：全新的TTS资产管理表，实现智能复用机制
- [新增] `usage_stats` 表：TTS资产使用统计表，支持复用分析
- [修改] 现有contentJson结构：从v2.0的纯文本模式升级到v4.0的文本哈希映射模式

## 1. 项目文件结构概览 (Project File Structure Overview)
```
senseword-content-factory/01-EN/SQLite/workflows/05-例句资产 TTS /scripts/
├── 02_create_v4_database_structure.py    # [新增] 创建v4.0数据库表结构
├── 03_migrate_v2_to_v4_content.py        # [新增] 迁移v2.0内容到v4.0架构
└── utils/
    ├── text_normalizer.py                # [新增] 多语言文本标准化工具
    ├── tts_mapper.py                     # [新增] TTS映射生成器
    └── hash_generator.py                 # [新增] 文本哈希生成工具
```

## 2. 分支策略建议
- 建议的特性分支名称: `feature/tts-v4-text-hash-mapping`
- 建议的工作目录：当前目录 `/workflows/05-例句资产 TTS /scripts`
- 基础分支: `main`
- 数据库备份策略：在执行迁移前自动创建备份

## 3. Commit 规划概要 (Commit Plan Summary & Status)
- [ ] feat(db): 创建v4.0数据库表结构和索引
- [ ] feat(utils): 实现多语言文本标准化算法
- [ ] feat(migration): 实现v2到v4内容结构迁移脚本
- [ ] feat(tts): 实现智能TTS资产映射和复用机制
- [ ] test(migration): 验证迁移结果和数据完整性
- [ ] docs(db): 更新数据库架构文档

## 4. 技术方案蓝图

### `02_create_v4_database_structure.py`
1. 核心职责 (Responsibilities):
    1. 创建v4.0版本的数据库表结构，包括words表的扩展和新的tts_assets表
    2. 建立必要的索引以支持高效的文本哈希查询和复用检测

2. 技术需求定义 (Technical Requirements):
    1. [数据完整性] 必须在事务中执行所有DDL操作，确保原子性
    2. [性能优化] 为textHash字段创建专用索引，支持毫秒级查询
    3. [向后兼容] 保持现有words_for_publish表的所有字段不变
    4. [日志记录] 详细记录每个表创建步骤和索引建立过程

3. 函数/方法签名 (Function/Method Signatures):
    1. `def create_v4_database_structure(db_path: str) -> bool`
    2. `def add_tts_fields_to_words_table(cursor: sqlite3.Cursor) -> None`
    3. `def create_tts_assets_table(cursor: sqlite3.Cursor) -> None`
    4. `def create_usage_stats_table(cursor: sqlite3.Cursor) -> None`
    5. `def create_indexes(cursor: sqlite3.Cursor) -> None`

4. 数据结构定义 (Data Structures / DTOs):
```python
from dataclasses import dataclass
from typing import Optional, List
from datetime import datetime

@dataclass
class TTSAsset:
    """TTS资产数据结构"""
    ttsId: str                    # UUID主键
    originalText: str             # 原始文本
    normalizedText: str           # 标准化文本
    textHash: str                 # 文本哈希（16字符）
    textToSpeak: str             # TTS处理文本
    learningLanguage: str        # 学习语言
    ttsType: str                 # TTS类型
    status: str = 'pending'      # 状态
    audioUrl: Optional[str] = None # 音频URL
    audioDuration: Optional[float] = None # 音频时长
    usageCount: int = 1          # 使用次数
    firstUsedWordId: Optional[int] = None # 首次使用的单词ID
    createdAt: datetime = None   # 创建时间
    lastUsedAt: datetime = None  # 最后使用时间

@dataclass
class WordTTSMapping:
    """单词TTS映射数据结构"""
    wordId: int                  # 单词ID
    ttsHashList: List[str]       # TTS哈希列表
    ttsMappingDetails: dict      # 详细映射信息
```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
    1. [数据库连接] 连接到SQLite数据库，开启事务
    2. [备份检查] 检查是否需要创建数据库备份
    3. [表结构扩展] 为words_for_publish表添加ttsHashList和ttsMappingDetails字段
    4. [新表创建] 创建tts_assets表，包含所有必要字段和约束
    5. [统计表创建] 创建usage_stats表，支持复用统计分析
    6. [索引建立] 创建textHash、语言等关键字段的索引
    7. [版本更新] 更新contentVersion字段默认值为'v4.0'
    8. [事务提交] 提交所有更改，记录成功日志

### `03_migrate_v2_to_v4_content.py`
1. 核心职责 (Responsibilities):
    1. 将现有v2.0格式的contentJson迁移到v4.0的文本哈希映射格式
    2. 提取所有文本内容，生成对应的TTS资产映射关系，保持内容纯净性

2. 技术需求定义 (Technical Requirements):
    1. [内容保持] 确保迁移后的contentJson保持v2.0的纯净性，不添加技术标识符
    2. [智能复用] 检测相同文本内容，实现TTS资产的智能复用
    3. [数据完整性] 验证迁移前后的数据一致性，确保无内容丢失
    4. [批量处理] 支持大批量数据的高效处理，避免内存溢出

3. 函数/方法签名 (Function/Method Signatures):
    1. `def migrate_v2_to_v4_content(db_path: str, batch_size: int = 1000) -> bool`
    2. `def extract_texts_from_content_json(content_json: str) -> List[TextExtraction]`
    3. `def normalize_and_hash_text(text: str, language: str = 'en') -> TextNormalization`
    4. `def find_or_create_tts_asset(text_hash: str, original_text: str, language: str, tts_type: str) -> str`
    5. `def update_word_with_tts_mappings(word_id: int, tts_mappings: List[TTSMapping]) -> None`

4. 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class TextExtraction:
    """文本提取结果"""
    text: str                    # 原始文本
    context_path: str            # JSON路径上下文
    tts_type: str               # TTS类型标识
    translation: Optional[str] = None # 翻译内容

@dataclass
class TextNormalization:
    """文本标准化结果"""
    original: str               # 原始文本
    normalized: str             # 标准化文本
    text_hash: str             # 文本哈希
    language: str              # 语言代码

@dataclass
class TTSMapping:
    """TTS映射关系"""
    text_hash: str             # 文本哈希
    original_text: str         # 原始文本
    tts_type: str             # TTS类型
    context_path: str         # 上下文路径
```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
    1. [批量读取] 分批读取words_for_publish表中的记录
    2. [内容解析] 解析每条记录的contentJson，提取所有文本内容
    3. [文本标准化] 对提取的文本进行多语言标准化处理
    4. [哈希生成] 为每个标准化文本生成16字符的哈希值
    5. [复用检测] 查询tts_assets表，检查是否存在相同哈希的资产
    6. [资产创建] 如果不存在，创建新的TTS资产记录
    7. [映射生成] 生成ttsHashList和ttsMappingDetails
    8. [内容保持] 保持contentJson的v2.0纯净格式不变
    9. [记录更新] 更新words表的TTS相关字段
    10. [进度报告] 记录处理进度和统计信息

### `utils/text_normalizer.py`
1. 核心职责 (Responsibilities):
    1. 实现多语言文本的标准化处理算法
    2. 支持15+种语言的特定处理规则

2. 技术需求定义 (Technical Requirements):
    1. [多语言支持] 支持英语、中文、日语、韩语、西班牙语、法语等主要语言
    2. [一致性保证] 相同语义的文本必须产生相同的标准化结果
    3. [性能优化] 标准化处理速度需达到1000次/秒以上
    4. [可扩展性] 支持新语言处理规则的动态添加

3. 函数/方法签名 (Function/Method Signatures):
    1. `def normalize_text(text: str, language: str = 'en') -> TextNormalization`
    2. `def detect_language(text: str) -> str`
    3. `def clean_text_basic(text: str) -> str`
    4. `def process_by_language(text: str, language: str) -> str`
    5. `def generate_text_hash(normalized_text: str, language: str) -> str`

4. 数据结构定义 (Data Structures / DTOs):
```python
@dataclass
class LanguageProcessor:
    """语言处理器配置"""
    language_code: str          # 语言代码
    char_pattern: str          # 字符正则模式
    space_replacement: str     # 空格替换字符
    special_rules: List[str]   # 特殊处理规则

# 语言处理器映射
LANGUAGE_PROCESSORS = {
    'en': LanguageProcessor('en', r'[^\w\s]', '-', []),
    'zh': LanguageProcessor('zh', r'[^\u4e00-\u9fff\w]', '', []),
    'ja': LanguageProcessor('ja', r'[^\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff\w]', '', []),
    'ko': LanguageProcessor('ko', r'[^\uac00-\ud7af\w]', '', []),
    'es': LanguageProcessor('es', r'[^\w\sñáéíóúü]', '-', []),
    'fr': LanguageProcessor('fr', r'[^\w\sàâäéèêëïîôöùûüÿç]', '-', [])
}
```

5. 伪代码实现逻辑 (Pseudocode Implementation Logic):
    1. [基础清理] 去除首尾空格，统一空格，转换为小写
    2. [语言检测] 如果未指定语言，自动检测文本语言
    3. [语言处理] 根据语言类型应用特定的处理规则
    4. [字符过滤] 移除不相关的标点符号和特殊字符
    5. [空格处理] 根据语言特性处理空格（英语转破折号，中文移除）
    6. [哈希生成] 使用SHA-256算法生成16字符哈希
    7. [结果封装] 返回包含原始文本、标准化文本和哈希的结果对象

## 5. AI Agent 需要了解的文件上下文
<context_files>
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content.db
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/02-KDD/KDD-035-文本计算音频资产请求 ID /03-关键帧可视化.md
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/02-KDD/KDD-035-文本计算音频资产请求 ID /Context/01-目标与技术需求说明.md
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/SenseWord-ContentHub/docs/03-数据库设计.md
</context_files>

## X. 冲突检查报告

### X.1 数据库表结构验证 ✅

#### words_for_publish表验证结果
- **✅ 表存在**: `words_for_publish`表已存在于数据库中
- **✅ 字段完整**: 包含所有必要的基础字段（id, word, contentJson等）
- **✅ AI审核字段**: 已包含aiAuditScore, aiAuditComment等AI审核相关字段
- **⚠️ TTS字段**: 缺少ttsHashList和ttsMappingDetails字段，需要新增

#### tts_assets表验证结果
- **❌ 表不存在**: `tts_assets`表尚未创建，需要完整创建
- **⚠️ 索引规划**: 需要为textHash字段创建高效索引

### X.2 数据模型验证 ✅

#### 现有数据模型确认
- **✅ contentJson结构**: 现有v2.0格式已经是纯净内容，适合直接升级到v4.0架构
- **✅ 语言字段**: learningLanguage和scaffoldingLanguage字段已存在
- **✅ 状态管理**: publishStatus和auditStatus字段支持工作流管理

#### 新增数据模型需求
- **⚠️ TTS资产模型**: 需要新增完整的TTS资产管理数据结构
- **⚠️ 文本映射模型**: 需要新增文本到TTS资产的映射关系模型

### X.3 架构兼容性验证 ✅

#### 数据分离架构一致性
- **✅ 内容与技术分离**: v4.0设计完全符合内容纯净性原则
- **✅ 智能复用机制**: 通过textHash实现高效的资产复用
- **✅ 多语言支持**: 标准化算法支持项目的国际化需求

#### 技术栈兼容性
- **✅ SQLite**: 项目使用SQLite 3，完全支持所需的表结构和索引
- **✅ Python**: 使用Python 3.x进行数据库操作和文本处理
- **✅ JSON处理**: 现有contentJson字段支持复杂的JSON数据存储

### X.4 潜在风险识别 ⚠️

#### 中等风险项
1. **数据迁移风险**: 大量数据（62,815条记录）的迁移可能耗时较长，建议分批处理
2. **文本标准化一致性**: 不同语言的标准化规则需要充分测试，确保复用机制有效

#### 低风险项
1. **索引性能**: 新增的textHash索引可能影响写入性能，但查询性能将显著提升
2. **存储空间**: 新增字段和表将增加数据库大小，但通过复用机制可节约整体存储

### X.5 修正建议 📝

#### 技术方案微调
1. **分批处理**: 建议将迁移脚本设计为支持断点续传的分批处理模式
2. **备份策略**: 在执行迁移前自动创建数据库备份，确保数据安全

#### 实现优先级调整
1. **优先级1**: 创建数据库表结构和索引
2. **优先级2**: 实现文本标准化工具库
3. **优先级3**: 开发内容迁移脚本
4. **优先级4**: 执行数据迁移和验证

### X.6 结论 ✅

**技术方案与现有项目实现高度兼容**，主要发现：

1. **✅ 数据库基础**: 现有SQLite数据库结构完整，支持v4.0扩展
2. **✅ 内容格式**: 现有contentJson格式为迁移提供了良好基础
3. **✅ 工作流集成**: 新的TTS映射机制与现有审核流程完全兼容
4. **⚠️ 迁移复杂度**: 需要谨慎处理大量数据的迁移过程
5. **📈 性能提升**: v4.0架构将显著提升查询性能和降低存储成本

**建议继续技术方案**，按照优先级顺序实施，重点关注数据迁移的安全性和一致性验证。