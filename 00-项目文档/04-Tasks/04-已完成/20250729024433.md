# iOS getWord 函数多语言参数支持升级

## TODO 用户提供的任务内容
1. func getWord(_ word: String, language: LanguageCode?) async throws -> WordDefinitionResponse
2. 后续会支持多语言，因此参数需要支持提供 learninglanguage和scaffoldinglanguage
3. 具体搜索项目代码和文档的上下文

## AI 分析区域

### 当前架构分析
通过代码搜索发现，SenseWord 系统已经具备完整的多语言架构支持：

**后端多语言架构（已完成）**：
- Cloudflare Worker API 支持双语言参数：`/api/v1/words/{learningLanguage}/{scaffoldingLanguage}/{wordName}`
- 数据库表结构包含 `learningLanguage` 和 `scaffoldingLanguage` 字段
- 支持 22 种语言代码（en, zh, ja, de, fr, es, ko, ru, it, pt, ar, hi, th, vi, tr, pl, nl, sv, da, no, fi, id）
- 默认配置：learningLanguage="en"（英语），scaffoldingLanguage="zh"（中文）

**前端当前实现（需要升级）**：
- `WordAPIAdapter.getWord` 函数目前只接受单一 `language` 参数
- 内部硬编码：learningLanguage="en"，scaffoldingLanguage=language?.rawValue ?? "zh"
- 缺少用户设置中的语言对配置获取机制

### 核心问题诊断
1. **参数设计不完整**：当前函数签名无法让调用方指定完整的语言对配置
2. **硬编码分散**：learningLanguage 在多个地方硬编码为 "en"（WordAPIAdapter、LanguagePairConfigManager）
3. **架构不一致**：函数签名与实际的双语言架构不匹配
4. **扩展性受限**：虽然后端支持多学习语言，但前端架构限制了扩展

### 当前架构现状澄清
- **learningLanguage 来源**：当前固定为英语（.english），在 LanguagePairConfigManager 中硬编码
- **Phase 1 限制**：getSupportedLearningLanguages() 仅返回 [.english]，符合当前产品阶段
- **用户设置角色**：UserSettings.preferredLanguage 实际是脚手架语言（母语），不是学习语言
- **未来扩展计划**：代码注释显示 Phase 2+ 将支持西班牙语、法语、德语等学习语言

### 解决方案设计
采用**架构对齐策略**，使函数签名与实际的双语言架构保持一致：

1. **函数签名重构**：将 getWord 函数改为双语言参数版本，明确 learningLanguage 和 scaffoldingLanguage
2. **架构统一**：消除硬编码，通过 LanguagePairConfigManager 统一管理语言对配置
3. **Phase 1 兼容**：当前阶段 learningLanguage 仍为英语，但通过配置管理器获取而非硬编码
4. **未来扩展就绪**：为 Phase 2+ 的多学习语言支持做好架构准备
5. **调用方更新**：同步更新所有调用 getWord 的地方，使用统一的语言对配置

## CML 任务清单

### 阶段一：协议和接口重构
- [x] 修改 WordAPIAdapterProtocol 中的 getWord 方法签名：将 `func getWord(_ word: String, language: LanguageCode?) async throws -> WordDefinitionResponse` 改为 `func getWord(_ word: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> WordDefinitionResponse`
- [x] 在 WordAPIAdapterProtocol 中添加便捷方法签名：`func getWord(_ word: String) async throws -> WordDefinitionResponse`，使用用户设置中的默认语言对

### 阶段二：WordAPIAdapter 实现重构
- [x] 重构 WordAPIAdapter.getWord 主方法：接受 learningLanguage 和 scaffoldingLanguage 参数，移除硬编码的 "en" 和单一 language 参数处理
- [x] 在 WordAPIAdapter 中添加 LanguagePairConfigManager 依赖：通过构造函数注入获取用户当前语言对设置，遵循现有 DI 架构
- [x] 实现便捷版本的 getWord 方法：从 languagePairConfigManager.getDefaultLanguagePair() 中获取当前激活的语言对，调用主方法

### 阶段三：DIContainer 和调用方代码更新
- [x] 在 DIContainer 中添加 SettingsService 注册：添加 lazy var settingsService: SettingsServiceProtocol 属性，使用单例模式
- [x] 更新 DIContainer 中的 wordAPIAdapter 创建：注入 LanguagePairConfigManager 依赖，通过 createLanguagePairConfigManager(settingsService:) 获取
- [x] 搜索并更新所有调用 WordAPIAdapter.getWord 的地方：将单参数调用改为双语言参数调用或使用新的无参数便捷方法
- [x] 更新 SearchService.getWordContent 方法：传递正确的 learningLanguage 和 scaffoldingLanguage 参数
- [x] 检查并更新测试文件中的 getWord 调用：确保测试用例使用新的函数签名

### 阶段四：日志优化和错误处理
- [x] 更新 getWord 方法中的日志输出：明确显示使用的 learningLanguage 和 scaffoldingLanguage 值，移除旧的单一 language 参数日志
- [x] 添加语言代码验证：确保传入的 LanguageCode 在支持的语言列表中，提供清晰的错误信息
- [x] 优化端点构建日志：显示完整的 RESTful 路径构建过程，包括双语言参数的具体值

## 提交消息区域

```
refactor(word-api): 重构getWord函数支持完整多语言参数架构

✅ 协议和接口重构完成
- 重构WordAPIAdapterProtocol支持双语言参数版本
- 添加便捷方法使用默认语言对配置

✅ WordAPIAdapter实现重构完成
- 移除硬编码的"en"学习语言，通过LanguagePairConfigManager获取
- 集成用户设置中的语言对配置作为智能默认值
- 添加语言代码验证确保类型安全

✅ DIContainer和调用方更新完成
- 在DIContainer中注册SettingsService和LanguagePairConfigManager依赖
- 更新所有调用方使用新的函数签名(SearchService, RealAPIService, JITPreloader)
- 保持向后兼容的便捷方法调用

✅ 日志优化和错误处理完成
- 优化日志输出显示完整的双语言参数信息
- 添加APIError.invalidRequest错误类型
- 集成LanguagePairConfigManager的语言对验证

BREAKING CHANGE: getWord函数签名从单language参数改为双语言参数
架构价值: 为Phase 2+多学习语言支持奠定基础，仅需更新配置即可启用新语言
Closes: iOS多语言架构对齐升级
```