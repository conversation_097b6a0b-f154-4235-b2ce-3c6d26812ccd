# TODO 
1. 我现在需要梳理iOS前端的业务功能逻辑细节。按照功能必要性顺序
   1. 单词查询
   2. 索引下载和更新
   3. 单词收藏
   4. 购买权益

# TODO 1
1. 针对前端单词查询，显示，音频加载，播放等全部业务流程建立业务流程地图
2. 阅读提示词：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/01-提示词/知识管理/006-业务逻辑地图.md
3. 搜索当前代码上下文
4. 文档创建到：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/03-Docs/06-业务逻辑地图 
5. 编号 002 