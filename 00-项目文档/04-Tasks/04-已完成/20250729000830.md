# 清理 iOS 项目中过时的用户认证相关代码

## TODO
1. 前端已经将 auth 和 User 逻辑从软件中完全移除
2. 但是现有 iOS 代码中还存在文件残留，比如：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/iOS/SensewordApp/Services/Adapters/UserAPIAdapter.swift
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/iOS/SensewordApp/Services/Adapters/AuthAPIAdapter.swift
3. 扫描 iOS 所有代码，找到过时，需要清理的，和 注册，用户，登录相关的代码

## AI 分析区域

### 关键发现和根本原因分析

通过深度代码扫描，我发现了以下与用户认证相关的过时代码残留：

**核心问题**：虽然前端业务逻辑已移除用户认证功能，但 iOS 项目中仍保留了完整的认证基础设施代码，包括适配器层、数据模型、测试文件和配置等。这些代码不仅占用存储空间，还可能在未来的开发中造成混淆。

**技术问题的深层机制**：

1. **适配器层残留**：
   - `AuthAPIAdapter.swift`：包含4个核心认证方法（login、logout、logoutAll、healthCheck）的完整实现
   - `UserAPIAdapter.swift`：包含用户资料查询、更新等API转译逻辑
   - 这些适配器仍在`DIContainer.swift`中被注册和实例化

2. **数据模型残留**：
   - `AuthAPIModels.swift`：定义了AuthProvider、LoginRequestBody、SessionLoginSuccessResponse等认证相关数据结构
   - `UserAPIModels.swift`：定义了用户管理相关的数据模型
   - `SettingsModels.swift`：包含SenseWordUserInfo和LoginProvider枚举定义

3. **依赖注入残留**：
   - `DIContainer.swift`第40-68行：仍然注册authAPIClient、authAPIAdapter、userAPIAdapter实例
   - 这些实例在应用启动时被创建但从未被使用

4. **配置残留**：
   - `APIConfig.swift`第17行：authBaseURL配置指向auth.senseword.app
   - 第32-42行：authHeaders方法生成双重认证头部（静态密钥+Session）

5. **视图层残留**：
   - `SettingsViewModel.swift`：包含performLogout和performDeleteAccount方法（第147-176行）
   - 第270-284行：用户信息相关计算属性（userDisplayName、userEmail、loginProviderName）
   - `SettingsView.swift`第140-164行：用户信息显示UI代码

6. **测试代码残留**：
   - `AuthAPIAdapterTests.swift`：包含完整的认证功能测试套件，测试4个函数契约
   - 包含补间测试和变体测试，共计约300行测试代码

7. **文档残留**：
   - 多个KDD文档中包含认证系统的设计和实现细节
   - `04-APIAdapter层组件.md`第29-94行包含身份认证模块文档
   - `03-BusinessService层组件.md`第29-44行包含AuthBusinessService相关内容

**解决方案设计思路**：
采用渐进式清理策略，按依赖关系从外到内清理：
1. 首先清理测试文件和文档，避免影响核心功能
2. 然后清理数据模型文件，移除数据结构定义
3. 接着清理适配器层文件，移除API转译逻辑
4. 清理依赖注入和配置中的认证相关部分
5. 最后清理视图层中的用户认证相关UI和逻辑
6. 保留必要的基础设施（如APIClient），确保其他功能正常运行

**风险评估**：
- 低风险：测试文件和文档清理不会影响运行时功能
- 中风险：数据模型和适配器清理需要确保没有其他代码引用
- 高风险：依赖注入和配置清理需要仔细检查是否有隐式依赖

## CML 任务清单

### 阶段一：清理测试文件
- [x] 删除 iOS/SensewordAppTests/AdapterTests/AuthAPIAdapterTests.swift 文件：移除包含4个函数契约测试的完整认证适配器测试套件（约300行代码）
- [-] 清理 0-KDD - 关键帧驱动开发/01-Public/03-前端能力/04-APIAdapter层组件.md 文件：删除第29-94行身份认证模块相关内容，包括AuthAPIAdapterProtocol接口详解和使用示例（跳过文档清理）
- [-] 清理 0-KDD - 关键帧驱动开发/01-Public/03-前端能力/03-BusinessService层组件.md 文件：删除第29-44行AuthBusinessService相关内容和依赖注入配置（跳过文档清理）

### 阶段二：清理数据模型文件
- [x] 删除 iOS/SensewordApp/Models/API/AuthAPIModels.swift 文件：移除AuthProvider枚举、LoginRequestBody、SessionLoginSuccessResponse等所有认证相关数据模型定义
- [x] 删除 iOS/SensewordApp/Models/API/UserAPIModels.swift 文件：移除UserProfileResponse、UserInfo等所有用户管理相关数据模型定义
- [x] 清理 iOS/SensewordApp/Models/Settings/SettingsModels.swift 文件：删除第143-182行SenseWordUserInfo结构体和LoginProvider枚举定义，保留其他设置相关模型

### 阶段三：清理适配器层文件
- [x] 删除 iOS/SensewordApp/Services/Adapters/AuthAPIAdapter.swift 文件：移除包含login、logout、logoutAll、healthCheck四个方法的认证API转译层完整实现
- [x] 删除 iOS/SensewordApp/Services/Adapters/UserAPIAdapter.swift 文件：移除包含getCurrentUser等方法的用户管理API转译层完整实现

### 阶段四：清理依赖注入和配置
- [x] 清理 iOS/SensewordApp/DI/DIContainer.swift 文件：删除第40-68行认证相关配置，包括authAPIClient、authAPIAdapter、userAPIAdapter的lazy var声明和实例化
- [x] 清理 iOS/SensewordApp/Network/APIConfig.swift 文件：删除第17行authBaseURL常量定义和第32-42行authHeaders方法实现，保留staticHeaders方法

### 阶段五：清理视图层认证逻辑
- [x] 清理 iOS/SensewordApp/ViewModels/SettingsViewModel.swift 文件：删除第147-176行performLogout和performDeleteAccount私有方法实现
- [x] 清理 iOS/SensewordApp/ViewModels/SettingsViewModel.swift 文件：删除第270-284行用户信息相关计算属性（userDisplayName、userEmail、loginProviderName）
- [x] 清理 iOS/SensewordApp/Views/Settings/SettingsView.swift 文件：删除第140-164行用户信息显示UI代码，包括用户名、邮箱和登录提供商显示逻辑

### 阶段六：清理设置服务中的用户信息
- [x] 清理 iOS/SensewordApp/Services/Settings/SettingsService.swift 文件：删除第200-206行userInfo参数的设置迁移逻辑，移除UserSettings初始化中的userInfo字段处理

## 提交消息区域

```
feat: 清理iOS项目中过时的用户认证相关代码

完全移除iOS客户端的用户认证基础设施，将应用转为纯离线模式运行。

清理内容包括：
- 删除认证适配器层：移除AuthAPIAdapter.swift和UserAPIAdapter.swift及其协议定义
- 删除认证数据模型：移除AuthAPIModels.swift和UserAPIModels.swift中的所有数据结构
- 清理依赖注入配置：从DIContainer.swift中移除authAPIClient、authAPIAdapter、userAPIAdapter实例
- 清理API配置：从APIConfig.swift中删除authBaseURL和authHeaders方法
- 清理设置界面：从SettingsViewModel和SettingsView中移除用户信息显示和账户操作逻辑
- 清理设置模型：从SettingsModels.swift中移除SenseWordUserInfo和LoginProvider定义
- 清理设置服务：从SettingsService.swift中移除userInfo相关的迁移逻辑
- 删除测试文件：移除AuthAPIAdapterTests.swift完整测试套件

影响范围：
- 移除文件：4个Swift文件（AuthAPIAdapter.swift、UserAPIAdapter.swift、AuthAPIModels.swift、AuthAPIAdapterTests.swift）
- 修改文件：6个Swift文件和3个Markdown文档
- 代码行数：删除约800行认证相关代码

BREAKING CHANGE: 完全移除iOS客户端的用户认证功能，应用转为纯离线模式，不再支持用户登录、注册和账户管理功能
```