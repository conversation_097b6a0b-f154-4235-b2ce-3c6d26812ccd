# 前端适配预生产模式的内容后端

## 任务背景
当前应用在后端的结构和数据上发生了大量的变更，从原有的单一数据库架构迁移到了基于Cloudflare D1的分布式TTS处理架构。需要在前端以及后端的数据接口的数据类型定义中体现这一点，特别是音频链接的处理方式发生了根本性变化。

## 核心问题
1. **数据库架构变更**: 从原有的`tts_assets`表迁移到新的`tts_tasks`表结构
2. **音频URL生成方式变更**: 从复杂的路径生成改为基于`ttsId`的直接映射
3. **前端数据类型不匹配**: 现有DTO定义与新的数据库字段不一致
4. **音频链接处理逻辑过时**: 需要适配新的CDN域名和文件格式

## AI 分析区域

### 关键发现和根本原因分析

#### 1. 真实数据库结构分析
通过Wrangler查询云端数据库发现，当前系统使用的是`word_definitions`表，其中`contentJson`字段包含完整的单词内容，**已经包含了ttsId字段**：

**真实的数据库结构**:
```sql
-- word_definitions表 (主要数据源)
CREATE TABLE word_definitions (
    sync_id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL,
    scaffoldingLanguage TEXT NOT NULL,
    contentJson TEXT NOT NULL,              -- 包含完整内容和ttsId
    coreDefinition TEXT,
    -- 其他字段...
)
```

**contentJson中的ttsId结构** (基于真实数据):
```json
{
  "content": {
    "phoneticSymbols": [
      {
        "type": "bre",
        "symbol": "/əˈbæk/",
        "ttsId": "1711e4b4002cede83b9eef50"    // ✅ 已存在
      },
      {
        "type": "name",
        "symbol": "/əˈbæk/",
        "ttsId": "47ee18cffdf15eebcca7f027"    // ✅ 已存在
      }
    ],
    "usageExamples": [
      {
        "examples": [
          {
            "learningLanguage": "She was completely taken aback...",
            "translation": "她被他突然的求婚完全惊呆了。",
            "ttsId": "797334b93a9296b59d8840bd",  // ✅ 已存在
            "phraseBreakdown": [
              {
                "phrase": "She was completely taken aback",
                "translation": "她完全惊呆了",
                "ttsId": "e2b56024b428d46a2a7da260"  // ✅ 已存在
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 2. 核心问题重新定义
**真实问题**: 后端API返回的数据中包含`ttsId`，但前端期望的是完整的`audioUrl`。需要在API层面将`ttsId`转换为完整的音频URL。

**音频URL生成规则** (基于代码分析):
```typescript
// 标准音频URL格式
const audioUrl = `https://audio.senseword.app/${ttsId}.wav`;
```

#### 3. 前后端数据契约不匹配
**前端期望** (来自 `iOS/SensewordApp/Models/API/WordAPIModels.swift`):
- `PhoneticSymbol.audioUrl: String?` - 完整的音频URL
- `UsageExample.audioUrl: String?` - 完整的音频URL
- `PhraseBreakdown.audioUrl: String?` - 完整的音频URL

**后端实际存储**:
- `contentJson`中存储的是`ttsId`字段，而不是`audioUrl`字段

**解决方案**: 在API响应构建时，将所有`ttsId`转换为`audioUrl`

### 解决方案设计思路

#### 方案A: 后端完全适配 (推荐)
后端在返回数据时，将`ttsId`转换为完整的音频URL，前端无需修改。

**优势**:
- 前端零修改
- 保持现有架构的简洁性
- 符合RESTful API设计原则

#### 方案B: 前端适配
前端接收`ttsId`，在本地进行URL转换。

**劣势**:
- 增加前端复杂度
- 需要硬编码域名信息
- 违反关注点分离原则

**最终选择**: 方案A - 后端完全适配

## CML 任务清单

### 阶段一：后端API响应转换逻辑
- [x] 在`cloudflare/workers/api/src/services/word.service.ts`中实现`ttsIdToAudioUrl`转换函数：输入`ttsId`字符串，输出`https://audio.senseword.app/${ttsId}.wav`格式的完整URL
- [x] 在单词查询响应构建逻辑中实现contentJson解析：从`word_definitions.contentJson`字段中提取并解析JSON数据
- [x] 在响应构建过程中集成ttsId到audioUrl的转换：遍历`phoneticSymbols`数组，将每个`ttsId`字段转换为`audioUrl`字段

### 阶段二：例句和短语音频URL转换
- [x] 在响应构建逻辑中处理`usageExamples`数组：遍历所有例句，将`ttsId`字段转换为`audioUrl`字段
- [x] 在响应构建逻辑中处理`phraseBreakdown`数组：遍历所有短语分解，将`ttsId`字段转换为`audioUrl`字段
- [x] 实现音频可用性检查：在转换过程中验证ttsId不为空且格式正确（24位哈希）

### 阶段三：API接口类型定义更新
- [x] 在`cloudflare/workers/api/src/types/word-types.ts`中确认`PhoneticSymbol`接口：确保`audioUrl`字段存在且为可选字符串类型
- [x] 在同一文件中确认`UsageExample`接口：确保`audioUrl`字段存在且为可选字符串类型
- [x] 在同一文件中确认`PhraseBreakdown`接口：确保`audioUrl`字段存在且为可选字符串类型

### 阶段四：前端兼容性验证
- [x] 在`iOS/SensewordApp/Services/JITPreloader.swift`中添加URL有效性检查：确保提取的`audioUrl`是有效的HTTPS URL格式
- [x] 在`iOS/SensewordApp/Services/GlobalAudioManager.swift`中增强错误处理：对无效或空的`audioUrl`提供更好的错误提示和降级处理
- [x] 创建端到端测试：验证从API查询到前端音频播放的完整流程正常工作

## 提交消息区域

```
feat(api): 实现contentJson中ttsId到音频URL的转换

- 在API响应构建中实现ttsId到完整音频URL的转换逻辑
- 处理phoneticSymbols、usageExamples、phraseBreakdown中的音频链接
- 统一音频URL格式为https://audio.senseword.app/${ttsId}.wav
- 增强前端音频播放的兼容性和错误处理

技术细节：
- 基于真实word_definitions表的contentJson字段结构
- 保持前端数据模型不变，在后端完成数据转换
- 支持24位哈希ttsId的标准化音频URL生成

Closes #042
```