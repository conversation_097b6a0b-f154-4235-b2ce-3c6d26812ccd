# 单词查询API RESTful化与字段更新任务

## TODO 用户提供的任务内容
1. word 的查询不支持 restful 以及使用当前更新之后的正确字段
2. 阅读：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/002-数据库配置规范.md
3. 后端和前端的端点和参数设置都需要更新
4. 本地需要拿取云端单词的所有字段数据（除时间之外）DTO 也需要进行更新

## AI 分析区域

### 核心问题诊断
通过代码分析发现以下关键问题：

1. **数据库字段不匹配**：当前后端查询使用旧字段名 `language`，但新数据库结构使用 `learningLanguage` 和 `scaffoldingLanguage`
2. **API端点不符合RESTful规范**：当前端点 `/api/v1/word/{word}?lang={lang}` 缺少多语言支持的路径参数
3. **查询字段缺失**：当前查询缺少新增的重要字段如 `sync_id`、`partsOfSpeech`、`culturalRiskRegions` 等
4. **DTO结构过时**：前端DTO模型与新数据库结构不匹配，缺少新字段映射

### 技术问题深层机制
- **数据库演进不同步**：word_definitions表已升级到v2.0结构（支持增量同步），但API查询仍使用v1.0字段
- **多语言架构缺失**：当前API不支持 learningLanguage + scaffoldingLanguage 的双语言架构
- **字段提取逻辑错误**：部分字段需要从contentJson中提取，但查询直接访问表字段

### 解决方案设计思路
1. **RESTful API重构**：采用 `/api/v1/words/{learningLang}/{scaffoldingLang}/{word}` 路径结构
2. **数据库查询更新**：使用新字段名和完整字段列表，包含所有业务字段
3. **DTO模型扩展**：添加缺失字段，保持向后兼容性
4. **前端适配器更新**：修改请求构建逻辑以支持新的API结构

## CML 任务清单

### 阶段一：后端API结构更新
- [x] 在cloudflare/workers/api/src/services/word.service.ts中更新findWordDefinition函数：将查询字段从`language`改为`learningLanguage`和`scaffoldingLanguage`，添加`sync_id`、`partsOfSpeech`、`culturalRiskRegions`等新字段
- [x] 在cloudflare/workers/api/src/index.ts中更新单词查询路由：从`/api/v1/word/:word`改为`/api/v1/words/:learningLang/:scaffoldingLang/:word`，支持RESTful多语言路径参数
- [x] 在cloudflare/workers/api/src/types/word-types.ts中更新WordDefinitionRecord接口：添加sync_id、learningLanguage、scaffoldingLanguage、partsOfSpeech、culturalRiskRegions字段

### 阶段二：前端API适配器更新
- [x] 在iOS/SensewordApp/Services/Adapters/WordAPIAdapter.swift中更新getWord方法：修改endpoint构建逻辑，使用新的RESTful路径格式`/api/v1/words/{learningLang}/{scaffoldingLang}/{word}`
- [x] 在iOS/SensewordApp/Models/API/WordAPIModels.swift中更新WordDefinitionResponse结构：添加对应新字段的属性，保持与后端DTO一致
- [x] 在iOS/SensewordApp/Models/API/WordAPIModels.swift中添加多语言支持：为WordDefinitionResponse添加learningLanguage和scaffoldingLanguage属性

### 阶段三：数据库查询优化
- [x] 在cloudflare/workers/api/src/services/word.service.ts中优化SQL查询：使用COALESCE处理可能为空的字段，确保向后兼容性
- [x] 在cloudflare/workers/api/src/services/word.service.ts中添加字段验证：检查必需字段是否存在，提供默认值处理
- [x] 在cloudflare/workers/api/src/services/word.service.ts中更新查询条件：使用新的唯一约束条件(word, learningLanguage, scaffoldingLanguage)

### 阶段四：API文档和配置更新
- [x] 在0-KDD - 关键帧驱动开发/01-Public/02-后端能力/001-API接口能力.md中更新API文档：修改单词查询端点说明，更新请求示例和响应格式
- [x] 在cloudflare/workers/api/src/index.ts中更新CORS配置：确保新的RESTful端点支持跨域请求
- [x] 在cloudflare/workers/api/src/index.ts中添加向后兼容性：保留旧端点的重定向支持，避免现有客户端中断

## 提交消息区域

```
feat(api): 重构单词查询API支持RESTful多语言架构

✅ 后端API结构更新:
- 更新API端点从/api/v1/word/{word}到/api/v1/words/{learningLang}/{scaffoldingLang}/{word}
- 修复数据库查询字段从language到learningLanguage/scaffoldingLanguage
- 添加sync_id、partsOfSpeech、culturalRiskRegions等新字段支持
- 更新WordDefinitionRecord接口匹配新数据库结构

✅ 前端适配器更新:
- 更新iOS WordAPIAdapter支持新RESTful端点格式
- 扩展WordDefinitionResponse模型添加多语言字段
- 保持向后兼容的默认语言设置(en/zh)

✅ 数据库查询优化:
- 使用COALESCE处理空值字段，确保向后兼容性
- 添加字段验证和默认值处理机制
- 优化SQL查询支持新唯一约束条件

✅ API文档和配置:
- 更新API文档说明新RESTful端点结构
- 保持CORS配置支持所有端点
- 实现向后兼容性支持，避免现有客户端中断

BREAKING CHANGE: API端点结构变更，建议客户端更新到新RESTful格式
```