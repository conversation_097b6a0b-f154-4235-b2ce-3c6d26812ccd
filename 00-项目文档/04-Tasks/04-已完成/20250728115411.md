# Cloudflare D1数据库配置统一化与后端能力文档

## TODO 用户任务内容
1. cloudflare D1 数据库目前在 monorepo 无法随时访问到单一真实来源
2. /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力 创建文档，编号 002
3. 记录 word definition 的字段，索引和示例数据

## AI 分析区域

### 问题诊断与根本原因分析

通过代码上下文搜索，我发现了当前 monorepo 中 Cloudflare D1 数据库配置存在的核心问题：

**1. 数据库配置分散化问题**
- 当前有两个独立的 D1 数据库：`senseword-word-db` (单词数据) 和 `senseword-tts-db` (TTS数据)
- 每个 worker 都有独立的 wrangler.toml 配置文件，数据库配置重复且分散
- 缺乏统一的数据库配置管理机制，导致配置不一致的风险

**2. 配置文件分布现状**
- `cloudflare/workers/api/wrangler.toml` - API Worker 配置
- `cloudflare/workers/tts/wrangler.toml` - TTS Worker 配置
- `cloudflare/d1/word-db/wrangler.toml` - Word DB 管理器配置
- `cloudflare/d1/tts-db/wrangler.toml` - TTS DB 管理器配置

**3. 单一真实来源缺失**
- 数据库 ID、名称、迁移路径在多个文件中重复定义
- 没有中央化的数据库配置文件作为单一真实来源
- 修改数据库配置需要同步更新多个文件，容易出错

**4. Word Definition 数据结构分析**
基于 `word_definitions` 表的 schema 分析：
- 核心字段：`word`, `learningLanguage`, `scaffoldingLanguage`, `contentJson`
- 搜索优化：`coreDefinition`, `sync_id` 支持增量同步
- 元数据字段：`difficulty`, `frequency`, `relatedConcepts`
- 业务字段：`partsOfSpeech`, `culturalRiskRegions`
- 完整索引体系：10个优化索引支持高效查询

### 解决方案设计思路

**1. 创建中央化数据库配置**
- 在 `0-KDD/01-Public/02-后端能力/` 目录下创建 `002-数据库配置规范.md`
- 建立统一的数据库配置标准和最佳实践
- 提供完整的 word_definitions 表结构文档

**2. 配置管理优化**
- 保持现有的分布式 wrangler.toml 结构（符合 Cloudflare Workers 最佳实践）
- 通过文档化确保配置一致性
- 建立配置变更的标准流程

**3. 文档化数据库能力**
- 详细记录 word_definitions 表的字段定义、索引策略
- 提供示例数据和查询模式
- 建立数据库使用指南和最佳实践

## CML 任务清单

### 阶段一：创建数据库配置规范文档
- [x] 在 `0-KDD - 关键帧驱动开发/01-Public/02-后端能力/` 目录下创建 `002-数据库配置规范.md` 文件
- [x] 在文档中添加 Cloudflare D1 数据库概览部分：包含两个数据库的基本信息、ID、用途说明
- [x] 在文档中添加 word_definitions 表完整结构：字段定义、数据类型、约束条件、业务含义
- [x] 在文档中添加索引策略部分：10个索引的用途、性能优化说明、查询模式

### 阶段二：补充配置管理和示例数据
- [x] 在文档中添加 wrangler.toml 配置标准：各 worker 的数据库绑定规范、环境配置要求
- [x] 在文档中添加示例数据部分：word_definitions 表的典型记录示例、contentJson 结构示例
- [x] 在文档中添加最佳实践部分：配置变更流程、数据库迁移指南、性能监控建议
- [x] 在文档中添加故障排查部分：常见配置问题、调试命令、解决方案

### 阶段三：验证和完善
- [x] 验证文档内容与实际代码库配置的一致性：检查数据库 ID、表结构、索引定义
- [x] 补充缺失的技术细节：触发器定义、约束条件、数据类型说明
- [x] 添加文档版本控制信息：创建时间、版本号、维护责任人

## 提交消息区域

```
feat(docs): 新增数据库配置规范文档

- 创建 002-数据库配置规范.md 统一管理 D1 数据库配置
- 详细记录 word_definitions 表结构和索引策略
- 建立 wrangler.toml 配置标准和最佳实践
- 提供示例数据和故障排查指南
- 解决 monorepo 中数据库配置分散化问题

Closes: KDD-002
```