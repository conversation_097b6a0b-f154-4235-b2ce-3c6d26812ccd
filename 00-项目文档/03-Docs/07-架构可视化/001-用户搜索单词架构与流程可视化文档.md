# 001 - 单词搜索与发现架构与流程可视化文档

## 📋 文档概述

本模块的核心使命是为用户提供一个**高性能、离线优先、支持多语言**的单词搜索与发现体验。它涵盖了从用户输入查询、获取实时搜索建议，到最终展示完整单词内容的端到端流程。

**包含的关键文件分析：**

- **iOS 客户端**: `MainContentView.swift`, `SearchView.swift`, `SearchViewModel.swift`, `SearchCoordinator.swift`, `SearchService.swift`, `LocalIndexService.swift`, `IndexDownloadManager.swift`, `IndexDownloadService.swift`, `SearchAPIAdapter.swift`, `WordAPIAdapter.swift`, `SQLiteManager.swift`
- **Cloudflare 后端**: `api/src/index.ts`, `api/src/handlers/wordIndexHandler.ts`, `api/src/handlers/downloadRangeHandler.ts`, `api/src/services/word.service.ts`
- **数据库结构**: `d1/word-db/migrations/0001_initial_word_schema.sql`, `d1/word-db/migrations/0002_fix_multilingual_indexes.sql`

本文档的价值在于通过多维度可视化，清晰揭示系统\*\*“LPLC（本地优先，实时缓存）”\*\*的架构精髓，阐明其后台索引同步与前端实时搜索分离的设计思想，帮助开发者快速建立对该复杂功能的全局认知。

## 🏗️ 系统架构图

### 文件关系与数据流程

```mermaid
graph TD
    %% 定义样式 - 模块级别色彩系统
    classDef entryPoint fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef types fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef utils fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef database fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef api fill:#F0E68C,stroke:#000000,stroke-width:2px,color:#000000
    classDef view fill:#DFF0D8,stroke:#000000,stroke-width:2px,color:#000000
    classDef coordinator fill:#FAEBD7,stroke:#000000,stroke-width:2px,color:#000000
    classDef viewModel fill:#F2F2F2,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "📱 iOS客户端"
        direction TB
        subgraph "视图层 (View)"
            MCV["🎬 MainContentView.swift<br/>应用主入口"]
            SV["🔍 SearchView.swift<br/>搜索UI界面"]
        end

        subgraph "视图模型层 (ViewModel)"
            SVM["🧠 SearchViewModel.swift<br/>UI状态管理、用户交互协调"]
        end

        subgraph "服务层 (Service)"
            SS["🔧 SearchService.swift<br/>LPLC策略协调、核心业务"]
            LIS["📚 LocalIndexService.swift<br/>本地索引管理、毫秒级搜索"]
            IDM["📥 IndexDownloadManager.swift<br/>索引下载协调器"]
        end

        subgraph "适配器层 (Adapter)"
            SAA["🔌 SearchAPIAdapter.swift<br/>索引下载API转译"]
            WAA["🔌 WordAPIAdapter.swift<br/>单词内容API转译"]
        end

        subgraph "基础设施层 (Infrastructure)"
            SQLM["🛠️ SQLiteManager.swift<br/>数据库操作封装"]
            SQLDB[("💾 本地SQLite<br/>word_index表")]
        end

        MCV -->|"手势触发"| SV
        SV -->|"用户输入"| SVM
        SVM -->|"获取建议"| SS
        SS -->|"本地搜索"| LIS
        LIS -->|"数据库查询"| SQLM
        SQLM -->|"读/写"| SQLDB
    end

    subgraph "☁️ Cloudflare后端"
        direction TB
        CFApi["🌐 API Worker<br/>(index.ts, *Handler.ts)"]
        D1DB[("🗄️ D1数据库<br/>word_definitions表")]
    end

    %% 数据流
    LIS -->|"更新索引"| SQLDB
    IDM -->|"调用下载"| SAA
    SAA -->|"HTTP请求<br/>分页索引数据"| CFApi
    SS -->|"获取完整单词"| WAA
    WAA -->|"HTTP请求<br/>完整单词内容"| CFApi
    CFApi -->|"D1查询"| D1DB

    %% 应用样式
    class MCV,SV entryPoint
    class SVM viewModel
    class SS,LIS,IDM service
    class SAA,WAA api
    class SQLM utils
    class SQLDB,D1DB database
    class CFApi external
```

### 架构层次说明

该模块在 iOS 客户端严格遵循了分层架构，实现了清晰的职责分离：

- **🎯 视图层 (View Layer)**: 由 `MainContentView.swift` 和 `SearchView.swift` 构成，负责 UI 渲染和用户交互的捕获。
- **🧠 视图模型层 (ViewModel Layer)**: `SearchViewModel.swift` 作为视图与业务逻辑的桥梁，管理 UI 状态，并将用户操作转换为业务指令。
- **🔧 服务层 (Service Layer)**: 核心业务逻辑层。`SearchService.swift` 协调 LPLC（本地优先，实时缓存）策略；`LocalIndexService.swift` 负责本地 SQLite 索引的毫秒级查询；`IndexDownloadManager.swift` 协调后台的索引下载流程。
- **🔌 适配器层 (Adapter Layer)**: `SearchAPIAdapter.swift` 和 `WordAPIAdapter.swift` 负责将业务层的调用转换为具体的网络请求，实现了业务逻辑与网络实现的解耦。
- **🏗️ 基础设施层 (Infrastructure Layer)**: `SQLiteManager.swift` 封装了所有底层的数据库操作，为上层服务提供线程安全的、标准化的数据访问接口。

## 🔄 详细处理流程时序图

### 流程一：后台索引增量同步流程

```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',
    'primaryTextColor': '#ffffff',
    'primaryBorderColor': '#ffffff',
    'lineColor': '#ffffff',
    'noteTextColor': '#000000',
    'noteBkgColor': '#fbbf24',
    'noteBorderColor': '#ffffff',
    'activationBkgColor': '#fbbf24',
    'sequenceNumberColor': '#ffffff'
  }
}}%%
sequenceDiagram
    participant iOS_IDM as 📥 IndexDownloadManager
    participant iOS_LIS as 📚 LocalIndexService
    participant iOS_SQLite as 💾 SQLite数据库
    participant iOS_SAA as 🔌 SearchAPIAdapter
    participant CF_API as ☁️ API Worker
    participant CF_D1 as 🗄️ D1数据库

    Note over iOS_IDM, CF_D1: 阶段一：确定需要下载的范围
    iOS_IDM->>iOS_LIS: getMaxPage()
    iOS_LIS->>iOS_SQLite: SELECT MAX(page)
    iOS_SQLite-->>iOS_LIS: 返回本地最大页码
    iOS_LIS-->>iOS_IDM: localMaxPage

    iOS_IDM->>iOS_SAA: getDownloadRange(localMaxPage)
    iOS_SAA->>CF_API: GET /download-range?localMaxPage=...
    CF_API->>CF_D1: SELECT MAX(CEIL(sync_id / 1000.0))
    CF_D1-->>CF_API: 返回服务端最大页码
    CF_API-->>iOS_SAA: 返回下载范围 {startPage, endPage}
    iOS_SAA-->>iOS_IDM: downloadRange

    Note over iOS_IDM, CF_D1: 阶段二：分页下载并写入本地数据库
    loop 对每个需要下载的页面
        iOS_IDM->>iOS_SAA: getWordIndexPage(page)
        iOS_SAA->>CF_API: GET /word-index/.../page/{page}
        CF_API->>CF_D1: SELECT ... WHERE sync_id BETWEEN ...
        CF_D1-->>CF_API: 返回页面索引数据
        CF_API-->>iOS_SAA: WordIndexResponse
        iOS_SAA-->>iOS_IDM: indexItems

        iOS_IDM->>iOS_LIS: updateLocalIndex(indexItems)
        iOS_LIS->>iOS_SQLite: BATCH INSERT OR REPLACE
        iOS_SQLite-->>iOS_LIS: 写入成功
        iOS_LIS-->>iOS_IDM: 完成
    end
```

### 流程二：用户实时搜索与内容获取流程

```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',
    'primaryTextColor': '#ffffff',
    'primaryBorderColor': '#ffffff',
    'lineColor': '#ffffff',
    'noteTextColor': '#000000',
    'noteBkgColor': '#fbbf24',
    'noteBorderColor': '#ffffff',
    'activationBkgColor': '#fbbf24',
    'sequenceNumberColor': '#ffffff'
  }
}}%%
sequenceDiagram
    participant User as 👤 用户
    participant iOS_View as 🔍 SearchView
    participant iOS_VM as 🧠 SearchViewModel
    participant iOS_SS as 🔧 SearchService
    participant iOS_LIS as 📚 LocalIndexService
    participant iOS_SQLite as 💾 SQLite数据库
    participant iOS_WAA as 🔌 WordAPIAdapter
    participant CF_API as ☁️ API Worker
    participant CF_D1 as 🗄️ D1数据库

    Note over User, CF_D1: 阶段一：实时搜索建议 (纯本地)
    User->>iOS_View: 输入搜索词 "pro..."
    iOS_View->>iOS_VM: searchQuery = "pro..."
    Note over iOS_VM, iOS_SS: (Debounce 300ms)
    iOS_VM->>iOS_SS: getSuggestions("pro...")
    iOS_SS->>iOS_LIS: searchSuggestions("pro...")
    iOS_LIS->>iOS_SQLite: SELECT ... WHERE word LIKE 'pro%'
    iOS_SQLite-->>iOS_LIS: 返回建议列表
    iOS_LIS-->>iOS_SS: suggestions
    iOS_SS-->>iOS_VM: suggestions
    iOS_VM-->>iOS_View: 更新UI，显示建议
    iOS_View-->>User: 显示 "progressive", "proactive" ...

    Note over User, CF_D1: 阶段二：获取完整单词内容 (网络请求)
    User->>iOS_View: 点击 "progressive"
    iOS_View->>iOS_VM: selectSuggestion("progressive")
    iOS_VM->>iOS_SS: getWordContent("progressive")
    iOS_SS->>iOS_WAA: getWord("progressive")
    iOS_WAA->>CF_API: GET /api/v1/words/en/zh/progressive
    CF_API->>CF_D1: SELECT ... WHERE word = 'progressive'
    CF_D1-->>CF_API: 返回 word_definitions 记录
    CF_API-->>iOS_WAA: WordDefinitionResponse (JSON)
    iOS_WAA-->>iOS_SS: WordDefinitionResponse (Decoded)
    iOS_SS-->>iOS_VM: WordDefinitionResponse
    iOS_VM-->>iOS_View: 更新UI，显示完整单词卡片
    iOS_View-->>User: 展示 "progressive" 详细内容
```

## 🔗 模块依赖关系图

### 导入导出关系与类型共享

```mermaid
graph TD
    %% 定义样式
    classDef view fill:#DFF0D8,stroke:#000000,stroke-width:2px,color:#000000
    classDef viewModel fill:#F2F2F2,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef adapter fill:#F0E68C,stroke:#000000,stroke-width:2px,color:#000000
    classDef infra fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "📱 UI层"
        SV["SearchView.swift"]
    end

    subgraph "🧠 ViewModel层"
        SVM["SearchViewModel.swift"]
    end

    subgraph "🔧 服务层"
        SS["SearchService.swift"]
        LIS["LocalIndexService.swift"]
        IDM["IndexDownloadManager.swift"]
    end

    subgraph "🔌 适配器层"
        SAA["SearchAPIAdapter.swift"]
        WAA["WordAPIAdapter.swift"]
    end

    subgraph "🏗️ 基础设施层"
        SQLM["SQLiteManager.swift"]
    end

    %% 依赖关系
    SV -->|"依赖"| SVM
    SVM -->|"协调"| SS
    SS -->|"使用"| LIS
    SS -->|"使用"| WAA
    LIS -->|"使用"| SQLM
    LIS -->|"使用"| SAA
    IDM -->|"使用"| SAA
    IDM -->|"使用"| LIS
    SAA -->|"使用"| SQLM

    %% 应用样式
    class SV view
    class SVM viewModel
    class SS,LIS,IDM service
    class SAA,WAA adapter
    class SQLM infra
```

## 📊 数据流转生命周期

### 数据在模块间的完整流转

```mermaid
graph LR
    %% 定义样式
    classDef inputData fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef processData fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputData fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageData fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "☁️ 数据源 (Cloudflare D1)"
        D1_TABLE["D1 `word_definitions` 表<br/>{ word, contentJson, ... }"]
    end

    subgraph "🔄 后端处理 (API Worker)"
        API_INDEX_ITEM["`WordIndexItem` 对象<br/>{ syncId, word, coreDefinition }"]
        API_FULL_ITEM["`WordDefinitionResponse` 对象<br/>(完整解析 `contentJson`)"]
    end

    subgraph "📱 客户端数据处理与存储"
        SQLITE_TABLE["本地SQLite `word_index` 表<br/>{ syncId, word, coreDefinition }"]
        SEARCH_SUGGESTION["`SearchSuggestion` 业务模型<br/>{ word, definition, relevanceScore }"]
        WORD_CARD_DATA["`WordDefinitionResponse` 业务模型<br/>(用于UI渲染)"]
    end

    subgraph "🖥️ UI展示"
        SUGGESTION_ROW["`SuggestionRow` 视图<br/>(显示搜索建议)"]
        WORD_CARD_VIEW["`WordCardView` 视图<br/>(显示完整单词)"]
    end

    %% 数据流转
    D1_TABLE -->|"后台API提取索引"| API_INDEX_ITEM
    API_INDEX_ITEM -->|"分页下载"| SQLITE_TABLE
    SQLITE_TABLE -->|"本地FTS查询"| SEARCH_SUGGESTION
    SEARCH_SUGGESTION -->|"渲染"| SUGGESTION_ROW

    D1_TABLE -->|"实时API解析"| API_FULL_ITEM
    API_FULL_ITEM -->|"网络获取"| WORD_CARD_DATA
    WORD_CARD_DATA -->|"渲染"| WORD_CARD_VIEW

    %% 应用样式
    class D1_TABLE storageData
    class API_INDEX_ITEM, API_FULL_ITEM processData
    class SQLITE_TABLE storageData
    class SEARCH_SUGGESTION, WORD_CARD_DATA outputData
    class SUGGESTION_ROW, WORD_CARD_VIEW outputData
```

## 💡 关键设计特点

### 架构设计原则

- **LPLC 原则 (Local Priority, Live Cache - 本地优先，实时缓存)**: 这是该模块的架构灵魂。通过将**搜索索引数据**后台同步至本地，实现了毫秒级的、离线可用的搜索建议功能。而**完整单词内容**则通过网络实时获取，确保了数据的新鲜度和准确性，完美平衡了性能与数据一致性。
- **责任分离 (Separation of Concerns)**: 客户端严格的分层架构（视图、视图模型、服务、适配器、基础设施）确保了代码的高度模块化和可维护性。每个层次只关心自己的职责，例如`SearchAPIAdapter`只负责网络请求的转译，而不关心业务逻辑。
- **异步与并发 (Asynchronicity & Concurrency)**: 大量使用 Swift 的`async/await`和`Task`，确保了数据库 I/O、网络请求等耗时操作不会阻塞 UI 主线程。`SearchCoordinator`通过在后台队列初始化服务，遵循了 Apple 官方的最佳性能实践。
- **协议导向编程 (Protocol-Oriented Programming)**: 通过定义`SearchServiceProtocol`、`SQLiteManagerProtocol`等协议，实现了依赖注入和轻松的单元测试（例如可以使用 Mock 实现替换真实服务）。

### 优化亮点

- **离线优先的搜索建议**: 核心搜索功能不依赖网络，极大地提升了用户体验的稳定性和即时性。
- **增量同步与固定分页**: 后台索引同步采用**增量**和**固定分页**机制，每次只下载更新的部分，并利用 CDN 进行长期缓存，极大地降低了客户端的流量消耗和服务器负载。
- **状态机驱动的服务生命周期**: `SearchCoordinator`使用状态机（`.notInitialized`, `.initializing`, `.ready`, `.failed`）来管理`SearchService`的复杂异步初始化过程，保证了服务的健壮性和状态的明确性。
- **防抖（Debouncing）**: `SearchViewModel`对用户输入进行 300ms 的防抖处理，避免了在用户快速输入时产生大量不必要的搜索请求，优化了性能。

## 🚀 系统优势

- **极致性能**: 搜索建议查询在本地 SQLite 数据库完成，响应时间通常在**50 毫秒**以内，为用户提供了媲美原生应用的即时反馈体验。
- **高可用性**: 核心搜索功能**完全离线可用**。即使在网络不佳或离线环境下，用户依然可以无缝使用搜索建议功能。
- **卓越的可维护性**: 清晰的分层架构和协议导向设计使得代码易于理解、扩展和测试。例如，未来若要支持新的搜索引擎或数据库，只需替换相应的服务或适配器实现即可。
- **可扩展的多语言架构**: 数据库和 API 设计从一开始就考虑了多语言对（`learningLanguage`, `scaffoldingLanguage`），为未来扩展到更多语言市场奠定了坚实的基础。
