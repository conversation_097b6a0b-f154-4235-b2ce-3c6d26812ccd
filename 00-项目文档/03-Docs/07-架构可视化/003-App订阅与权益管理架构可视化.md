# 004 - App 订阅与权益管理架构可视化文档

## 📋 文档概述

本模块负责处理 SenseWord 应用内的所有订阅和购买相关功能，其核心是实现一个**无需后端用户账户、完全依赖 Apple StoreKit 和设备本地存储**的匿名权益管理系统。它涵盖了从产品展示、发起购买、验证票据、恢复购买到在应用内解锁 Pro 功能的完整闭环。

**包含的关键文件分析：**

- **视图层**: `SubscriptionView.swift`, `SettingsView.swift`
- **视图模型层**: `SubscriptionViewModel.swift`
- **服务层**: `AnonymousPurchaseService.swift`, `SettingsService.swift`
- **模型层**: `AnonymousPurchaseModels.swift`, `SubscriptionModels.swift`, `PurchaseAPIModels.swift`
- **外部依赖**: Apple StoreKit 框架, iOS Keychain 安全存储, UserDefaults

本文档的价值在于清晰地展示了该模块如何巧妙地利用客户端技术栈构建一个安全、可靠且独立的订阅系统，揭示了其在简化架构、保护用户隐私方面的设计思想。

## 🏗️ 系统架构图

### 文件关系与数据流程

```mermaid
graph TD
    %% 定义样式 - 模块级别色彩系统
    classDef entryPoint fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef types fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef viewModel fill:#F2F2F2,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef database fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef view fill:#DFF0D8,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "👤 用户"
        User
    end

    subgraph "📱 iOS客户端"
        direction LR
        subgraph "视图层 (View)"
            SubView["💳 SubscriptionView.swift<br/>订阅产品展示与购买UI"]
            SettingsView["⚙️ SettingsView.swift<br/>订阅状态展示入口"]
        end

        subgraph "视图模型层 (ViewModel)"
            SubVM["🧠 SubscriptionViewModel.swift<br/>UI状态管理、处理购买/恢复操作"]
        end

        subgraph "服务层 (Service)"
            APS["🔑 AnonymousPurchaseService.swift<br/>核心权益管理服务"]
            SS["🔧 SettingsService.swift<br/>全局设置管理"]
        end

        subgraph "基础设施层 (Infrastructure)"
            Keychain[("🔐 Keychain<br/>安全存储购买记录")]
            UserDefaults[("📄 UserDefaults<br/>存储应用设置")]
        end
    end

    subgraph "🌐 外部系统"
        StoreKit[(" Apple StoreKit<br/>处理交易、验证权益")]
    end

    %% 数据流
    User -->|"发起购买/恢复"| SubView
    SettingsView -->|"打开订阅页"| SubView
    SubView -->|"用户操作"| SubVM
    SubVM -->|"调用购买/恢复"| APS
    APS -->|"StoreKit API调用"| StoreKit
    StoreKit -->|"返回Transaction"| APS
    APS -->|"写入购买记录"| Keychain
    APS -->|"更新全局状态"| SS
    SS -->|"持久化设置"| UserDefaults
    SS -->|"发布状态更新"| SubVM
    SubVM -->|"更新UI"| SubView

    %% 应用样式
    class SubView,SettingsView entryPoint
    class SubVM viewModel
    class APS,SS service
    class Keychain,UserDefaults database
    class StoreKit external
```

### 架构层次说明

该模块完全在 iOS 客户端内实现，通过清晰的分层来管理复杂性：

- **🎯 视图层 (View Layer)**: 由 `SubscriptionView.swift` 和 `SettingsView.swift` 组成，负责向用户展示订阅产品信息、当前权益状态，并捕获用户的购买和恢复操作。
- **🧠 视图模型层 (ViewModel Layer)**: `SubscriptionViewModel.swift` 作为 UI 与服务的桥梁，管理订阅页面的状态（如加载中、可购买），并将用户的意图转换为对底层服务的调用。
- **🔧 服务层 (Service Layer)**: 这是本模块的核心。
  - `AnonymousPurchaseService.swift`: 封装了所有与 Apple `StoreKit` 的交互，包括获取产品、发起购买、验证票据、恢复历史购买以及处理后台事务更新。它还负责将验证后的权益安全地存储到 Keychain 中。
  - `SettingsService.swift`: 作为一个全局配置服务，它从`AnonymousPurchaseService`获取最新的订阅状态，并将其保存在`UserSettings`模型中，供应用的其他部分统一查询。
- **🏗️ 基础设施层 (Infrastructure Layer)**:
  - **Keychain**: 用于安全地持久化用户的购买凭证（`LocalEntitlementRecord`），确保数据不因应用卸载而轻易丢失。
  - **UserDefaults**: 用于存储非敏感的应用配置，包括用户当前的订阅状态摘要（`SubscriptionStatus`）。

## 🔄 详细处理流程时序图

### 流程一：用户购买订阅流程

```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',
    'primaryTextColor': '#ffffff',
    'primaryBorderColor': '#ffffff',
    'lineColor': '#ffffff',
    'noteTextColor': '#000000',
    'noteBkgColor': '#fbbf24',
    'noteBorderColor': '#ffffff',
    'activationBkgColor': '#fbbf24',
    'sequenceNumberColor': '#ffffff'
  }
}}%%
sequenceDiagram
    participant User as 👤 用户
    participant SubView as 💳 SubscriptionView
    participant SubVM as 🧠 SubscriptionViewModel
    participant APS as 🔑 AnonymousPurchaseService
    participant StoreKit as  Apple StoreKit
    participant Keychain as 🔐 Keychain
    participant SS as 🔧 SettingsService

    User->>SubView: 点击购买 "年度订阅"
    SubView->>SubVM: startPurchase()
    SubVM->>APS: purchaseProduct(product)
    activate APS
    APS->>StoreKit: product.purchase()

    Note over StoreKit,Keychain: StoreKit弹出系统购买界面 (Apple Pay)

    StoreKit-->>APS: 返回.verified(transaction)
    APS->>APS: 创建 LocalEntitlementRecord
    APS->>Keychain: 安全保存购买记录
    APS->>SS: updateSubscriptionStatus(.pro)
    SS->>SS: 更新并保存 UserSettings 到 UserDefaults
    SS-->>SubVM: 发布订阅状态更新
    APS-->>SubVM: 返回购买成功结果
    deactivate APS

    SubVM->>SubView: 更新UI状态
    SubView-->>User: 显示购买成功
```

### 流程二：应用启动时权益检查流程

```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',
    'primaryTextColor': '#ffffff',
    'primaryBorderColor': '#ffffff',
    'lineColor': '#ffffff',
    'noteTextColor': '#000000',
    'noteBkgColor': '#fbbf24',
    'noteBorderColor': '#ffffff',
    'activationBkgColor': '#fbbf24',
    'sequenceNumberColor': '#ffffff'
  }
}}%%
sequenceDiagram
    participant App as 📱 SenseWordApp
    participant APS as 🔑 AnonymousPurchaseService
    participant StoreKit as  Apple StoreKit
    participant Keychain as 🔐 Keychain
    participant SS as 🔧 SettingsService

    Note over App, SS: 应用启动或进入前台
    App->>APS: checkEntitlements()
    activate APS

    APS->>StoreKit: Transaction.currentEntitlements
    StoreKit-->>APS: 返回所有已验证的权益(transactions)

    loop 遍历每个 transaction
        APS->>APS: 检查是否为Pro产品且未撤销
        APS->>Keychain: 更新/保存 LocalEntitlementRecord
    end

    APS->>APS: 计算最终权益状态 (EntitlementStatus)
    APS->>SS: updateSubscriptionStatus(status)
    SS->>SS: 更新并保存 UserSettings 到 UserDefaults
    deactivate APS
```

## 🔗 模块依赖关系图

### 导入导出关系与类型共享

```mermaid
graph TD
    %% 定义样式
    classDef view fill:#DFF0D8,stroke:#000000,stroke-width:2px,color:#000000
    classDef viewModel fill:#F2F2F2,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef model fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "📱 UI层"
        SubView["SubscriptionView.swift"]
    end

    subgraph "🧠 ViewModel层"
        SubVM["SubscriptionViewModel.swift"]
    end

    subgraph "🔧 服务层"
        APS["AnonymousPurchaseService.swift"]
        SS["SettingsService.swift"]
    end

    subgraph "📋 模型层 (Types)"
        SubModels["SubscriptionModels.swift<br/>(SubscriptionProduct)"]
        PurchaseModels["AnonymousPurchaseModels.swift<br/>(EntitlementStatus)"]
        SettingsModels["SettingsModels.swift<br/>(SubscriptionStatus)"]
    end

    %% 依赖关系
    SubView -->|"依赖"| SubVM
    SubVM -->|"使用"| APS
    SubVM -->|"使用"| SubModels

    APS -->|"更新"| SS
    APS -->|"依赖"| PurchaseModels

    SS -->|"依赖"| SettingsModels

    %% 应用样式
    class SubView view
    class SubVM viewModel
    class APS,SS service
    class SubModels,PurchaseModels,SettingsModels model
```

## 📊 数据流转生命周期

### 权益数据从 Apple 到 UI 的完整流转

```mermaid
graph LR
    %% 定义样式
    classDef sourceData fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
    classDef processData fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef transferData fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef clientData fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000

    subgraph "☁️ 真实数据源 (Apple)"
        APPLE_SERVER["Apple App Store 服务器<br/>(交易记录的唯一真实来源)"]
    end

    subgraph "🔄 客户端处理与持久化"
        STOREKIT_TRANS["StoreKit.Transaction 对象<br/>(Apple验证后的原始数据)"]
        KEYCHAIN_RECORD["LocalEntitlementRecord<br/>(存储在Keychain中的安全凭证)"]
        USERDEFAULTS_RECORD["UserSettings 对象<br/>(存储在UserDefaults中的应用配置)"]
    end

    subgraph "✨ 状态抽象与UI绑定"
        ENTITLEMENT_STATUS["EntitlementStatus 枚举<br/>(业务层权益状态)"]
        SUBSCRIPTION_STATUS["SubscriptionStatus 枚举<br/>(全局配置中的订阅状态)"]
        VIEWMODEL_STATE["@Published 属性<br/>(UI可观察的状态)"]
    end

    subgraph "🖥️ UI展示"
        UI_DISPLAY["SubscriptionView/SettingsView<br/>(显示 'Pro用户' 或 '免费用户')"]
    end

    %% 数据流转
    APPLE_SERVER -->|"通过StoreKit获取"| STOREKIT_TRANS
    STOREKIT_TRANS -->|"转换为"| KEYCHAIN_RECORD
    STOREKIT_TRANS -->|"抽象为"| ENTITLEMENT_STATUS
    ENTITLEMENT_STATUS -->|"转换为"| SUBSCRIPTION_STATUS
    SUBSCRIPTION_STATUS -->|"保存到"| USERDEFAULTS_RECORD
    USERDEFAULTS_RECORD -->|"加载并发布"| VIEWMODEL_STATE
    VIEWMODEL_STATE -->|"驱动"| UI_DISPLAY

    %% 应用样式
    class APPLE_SERVER sourceData
    class STOREKIT_TRANS transferData
    class KEYCHAIN_RECORD,USERDEFAULTS_RECORD sourceData
    class ENTITLEMENT_STATUS,SUBSCRIPTION_STATUS,VIEWMODEL_STATE processData
    class UI_DISPLAY clientData
```

## 💡 关键设计特点

### 架构设计原则

- [cite\_start]**无后端依赖 (Backend-Free)**: 这是该模块最核心的设计决策。整个订阅与权益管理流程完全在客户端实现，仅依赖 Apple 的 StoreKit 作为外部服务。这极大地简化了整体架构，降低了开发和维护成本，无需部署和管理一个用于票据验证和用户管理的后端服务器 [cite: 305, 324, 327]。
- [cite\_start]**安全持久化 (Secure Persistence)**: 包含购买日期和产品 ID 的`LocalEntitlementRecord`被安全地存储在 iOS 的**Keychain**中 [cite: 316, 317, 318, 319]。相比`UserDefaults`，Keychain 提供了加密存储，更适合存放敏感的购买凭证，数据在应用卸载后仍可能保留，便于恢复。
- [cite\_start]**状态驱动 UI (State-Driven UI)**: `SubscriptionView`完全由`SubscriptionViewModel`的状态驱动 [cite: 1568]。ViewModel 通过响应`AnonymousPurchaseService`和`SettingsService`的数据变化来更新`@Published`属性，UI 则自动刷新以反映最新的状态（如加载中、购买成功、错误等），遵循了现代 SwiftUI 的最佳实践。
- [cite\_start]**统一权益管理 (Centralized Entitlement Management)**: `SettingsService`充当了全应用权益状态的“单一信息源” [cite: 1390]。`AnonymousPurchaseService`在完成权益检查后，会通过`SettingsService`更新全局的`UserSettings`。应用内的其他任何需要检查 Pro 状态的功能，都应查询`SettingsService`，而不是直接与购买逻辑耦合。

### 优化亮点

- **匿名化处理**: 整个流程不要求用户注册或登录自定义账户，权益直接与用户的 Apple ID 绑定，有效保护了用户隐私，并降低了用户使用门槛。
- [cite\_start]**事务监听与自动恢复**: `AnonymousPurchaseService`启动了一个后台事务监听器（`Transaction.updates`） [cite: 319]。这使得应用能够自动处理在应用外发生的订阅续订、退款或家庭共享变化，确保权益状态始终与 Apple 服务器同步。
- **本地优先的权益检查**: 尽管最终依赖 StoreKit，但通过在 Keychain 缓存权益记录，应用可以在启动时快速检查本地凭证，仅在必要时（如发起恢复购买或定期检查）才与 Apple 服务器通信，提升了检查效率。
- [cite\_start]**清晰的错误处理**: `SubscriptionViewModel`对购买和恢复流程中可能出现的各种错误（如网络问题、用户取消、验证失败）进行了精细化处理，并通过`@Published`属性向 UI 层提供明确的错误信息 [cite: 1568, 1577]。

## 🚀 系统优势

- **简化架构与低成本**: 无需后端服务器，显著降低了开发复杂度和长期运营成本。
- **高可靠性与安全性**: 直接利用 Apple 官方、经过数十亿次验证的 StoreKit 框架进行交易和验证，可靠性和安全性极高。
- **离线可用性**: 用户的 Pro 状态可以在本地验证，即使在离线状态下，已解锁的功能依然可用。
- **无缝的用户体验**: 用户无需创建额外账户即可完成购买和恢复，购买流程与 iOS 系统原生体验无缝集成。
