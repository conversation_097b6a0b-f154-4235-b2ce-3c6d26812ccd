# KDD-018: 分离式用户反馈UI组件系统

## 1. 项目概述

### 1.1 核心目标
基于会议讨论"013｜众包单词审核系统"，将用户的三种不同意图分离为独立的UI组件：
1. **收藏行为** ⭐ - 个人学习管理
2. **质量反馈** 👍👎 - 公共内容评价  
3. **后台优化** - 系统透明自愈

### 1.2 问题解决
- **UX冲突**：解决"刷新按钮点击后内容不变"的用户困惑
- **逻辑分离**：将个人行为与公共反馈解耦
- **体验优化**：提供即时反馈，后台透明优化

### 1.3 技术依赖
- ✅ 用户反馈API (KDD-001已实现)
- ✅ 收藏功能API (KDD-006已实现)  
- 🆕 iOS分离式UI组件 (本方案实现)

## 2. 文件结构概览

```
iOS/Packages/UIComponents/Sources/UIComponents/
├── FeedbackComponents/
│   ├── BookmarkButton.swift           # FC-01: 收藏按钮组件
│   ├── QualityFeedbackButtons.swift   # FC-02: 质量反馈按钮组件
│   ├── FeedbackActionBar.swift        # FC-03: 组合操作栏
│   └── FeedbackButtonStyles.swift     # FC-04: 统一样式系统
├── WordCard/
│   └── WordCardView.swift             # FC-05: 集成新反馈组件
└── Services/
    └── FeedbackService.swift          # FC-06: 反馈服务协调器
```

## 3. Commit规划

### 3.1 开发分支策略
```bash
# 创建功能分支
git checkout -b feature/separated-feedback-ui
```

### 3.2 Commit序列
- [ ] feat(feedback-ui): 创建分离式收藏按钮组件
- [ ] feat(feedback-ui): 创建质量反馈按钮组件  
- [ ] feat(feedback-ui): 创建组合操作栏组件
- [ ] feat(feedback-ui): 集成到WordCardView
- [ ] feat(feedback-ui): 实现反馈服务协调器
- [ ] test(feedback-ui): 添加UI组件单元测试

## 4. 核心函数契约

### [FC-01]: 收藏按钮组件

- **职责**: 独立的收藏/取消收藏按钮，纯个人学习行为
- **函数签名**: `BookmarkButton(word: String, language: String, isBookmarked: Binding<Bool>)`
- **所在文件**: `iOS/Packages/UIComponents/Sources/UIComponents/FeedbackComponents/BookmarkButton.swift`

>>>>> **输入 (Input)**: `BookmarkButtonProps`

```swift
struct BookmarkButtonProps {
    let word: String                    // "progressive"
    let language: String               // "zh"
    @Binding var isBookmarked: Bool    // 收藏状态绑定
    let onToggle: () async -> Void     // 收藏切换回调
}
```

<<<<< **输出 (Output)**: `SwiftUI.View`

```swift
// 视觉输出：独立的收藏按钮
struct BookmarkButtonView: View {
    // 状态：未收藏 ⭐ (灰色) / 已收藏 ⭐ (金色)
    // 动画：点击时缩放+颜色渐变
    // 反馈：触觉反馈 + 乐观更新
}
```

---

### [FC-02]: 质量反馈按钮组件

- **职责**: 独立的赞/踩按钮，公共内容质量评价
- **函数签名**: `QualityFeedbackButtons(word: String, language: String, feedbackState: Binding<FeedbackState>)`
- **所在文件**: `iOS/Packages/UIComponents/Sources/UIComponents/FeedbackComponents/QualityFeedbackButtons.swift`

>>>>> **输入 (Input)**: `QualityFeedbackProps`

```swift
struct QualityFeedbackProps {
    let word: String                           // "progressive"  
    let language: String                      // "zh"
    @Binding var feedbackState: FeedbackState // 反馈状态
    let onFeedback: (FeedbackAction) async -> Void // 反馈回调
}

enum FeedbackState {
    case none           // 未反馈
    case liked          // 已点赞
    case disliked       // 已点踩
}

enum FeedbackAction {
    case like           // 点赞动作
    case dislike        // 点踩动作
}
```

<<<<< **输出 (Output)**: `SwiftUI.View`

```swift
// 视觉输出：水平排列的赞踩按钮
struct QualityFeedbackView: View {
    // 状态：👍 (灰色/绿色) 👎 (灰色/红色)
    // 互斥：点赞后踩按钮重置，反之亦然
    // 动画：点击时按钮缩放+颜色变化
    // 反馈：异步提交，即时UI反馈
}
```

---

### [FC-03]: 组合操作栏组件

- **职责**: 将收藏和质量反馈按钮组合为统一操作栏
- **函数签名**: `FeedbackActionBar(word: String, language: String, state: Binding<FeedbackBarState>)`
- **所在文件**: `iOS/Packages/UIComponents/Sources/UIComponents/FeedbackComponents/FeedbackActionBar.swift`

>>>>> **输入 (Input)**: `FeedbackBarProps`

```swift
struct FeedbackBarProps {
    let word: String                              // "progressive"
    let language: String                         // "zh"  
    @Binding var state: FeedbackBarState        // 组合状态
    let onBookmarkToggle: () async -> Void      // 收藏回调
    let onQualityFeedback: (FeedbackAction) async -> Void // 质量反馈回调
}

struct FeedbackBarState {
    var isBookmarked: Bool                      // 收藏状态
    var feedbackState: FeedbackState           // 质量反馈状态
}
```

<<<<< **输出 (Output)**: `SwiftUI.View`

```swift
// 视觉输出：水平布局的操作栏
struct FeedbackActionBarView: View {
    // 布局：[收藏⭐] [分隔线] [👍 👎]
    // 样式：圆角背景，适度阴影
    // 间距：组件间合理间距
    // 响应：独立按钮响应区域
}
```

## 5. 关键帧数据结构生命周期

### 5.1 用户交互流程

```mermaid
graph TD
    A[用户查看单词] --> B{选择操作类型}
    
    B -->|个人学习| C[点击收藏⭐]
    B -->|内容评价| D[点击赞👍]  
    B -->|内容评价| E[点击踩👎]
    
    C --> F[乐观更新UI]
    D --> G[乐观更新UI]
    E --> H[乐观更新UI]
    
    F --> I[异步调用收藏API]
    G --> J[异步调用反馈API]
    H --> K[异步调用反馈API]
    
    I --> L{API调用结果}
    J --> M{API调用结果}
    K --> N{API调用结果}
    
    L -->|成功| O[保持UI状态]
    L -->|失败| P[回滚UI状态]
    M -->|成功| Q[保持UI状态]
    M -->|失败| R[回滚UI状态]
    N -->|成功| S[保持UI状态]
    N -->|失败| T[回滚UI状态]
```

### 5.2 状态管理架构

```mermaid
graph LR
    A[WordCardView] --> B[FeedbackActionBar]
    B --> C[BookmarkButton]
    B --> D[QualityFeedbackButtons]
    
    C --> E[BookmarkService]
    D --> F[FeedbackService]
    
    E --> G[POST /api/v1/bookmarks]
    F --> H[POST /api/v1/feedback]
    
    G --> I[收藏状态更新]
    H --> J[反馈分数更新]
```

## 6. 伪代码逻辑

### 6.1 收藏按钮核心逻辑

```swift
// FC-01 伪代码
func toggleBookmark() async {
    // 1. 乐观更新UI
    withAnimation(.spring()) {
        isBookmarked.toggle()
    }
    
    // 2. 触觉反馈
    HapticFeedback.impact(.medium)
    
    // 3. 异步API调用
    do {
        if isBookmarked {
            try await BookmarkService.add(word: word, language: language)
        } else {
            try await BookmarkService.remove(word: word, language: language)
        }
    } catch {
        // 4. 失败回滚
        withAnimation(.spring()) {
            isBookmarked.toggle()
        }
        showError(error)
    }
}
```

### 6.2 质量反馈核心逻辑

```swift
// FC-02 伪代码  
func submitFeedback(_ action: FeedbackAction) async {
    let previousState = feedbackState
    
    // 1. 乐观更新UI (互斥逻辑)
    withAnimation(.easeInOut) {
        feedbackState = (action == .like) ? .liked : .disliked
    }
    
    // 2. 异步API调用
    do {
        let newScore = try await FeedbackService.submit(
            word: word, 
            language: language, 
            action: action
        )
        // 成功：保持UI状态，可选显示新分数
    } catch {
        // 3. 失败回滚
        withAnimation(.easeInOut) {
            feedbackState = previousState
        }
        showError(error)
    }
}
```

## 7. 测试策略

### 7.1 单元测试覆盖
- [ ] BookmarkButton状态切换测试
- [ ] QualityFeedbackButtons互斥逻辑测试
- [ ] FeedbackActionBar组合状态测试
- [ ] 错误处理和回滚机制测试

### 7.2 UI测试场景
- [ ] 收藏按钮动画效果测试
- [ ] 质量反馈按钮视觉状态测试
- [ ] 操作栏布局响应式测试
- [ ] 网络错误时UI回滚测试

## 8. 下一步规划

### 8.1 后续优化
- 添加反馈统计显示（可选）
- 支持批量操作（长按菜单）
- 添加反馈历史记录
- 实现离线状态处理

### 8.2 系统集成
- 与推荐算法集成反馈数据
- 与学习进度系统集成收藏数据
- 与用户设置系统集成偏好配置
