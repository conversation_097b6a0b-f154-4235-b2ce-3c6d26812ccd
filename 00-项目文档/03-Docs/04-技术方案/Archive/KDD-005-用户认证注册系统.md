# KDD-005: 用户认证注册系统技术方案

## 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-005 |
| **功能名称** | 用户认证注册系统 |
| **优先级** | P1 (高优先级) |
| **预估工期** | 3天 |
| **依赖关系** | 无 (新增users表) |
| **功能类型** | 核心功能 |
| **被依赖功能** | KDD-006 (用户生词本管理), KDD-008 (App Store购买验证) |

## 核心目标

实现基于Apple Sign In的用户认证和自动注册系统，为SenseWord提供用户身份管理基础，支持用户生词本和付费功能的用户关联。

### 关键价值
- **用户体系基础**: 为用户生词本管理提供必要的用户身份识别
- **付费功能支撑**: 为App Store购买验证提供用户账户关联
- **数据完整性**: 确保用户相关数据的归属和权限控制

---

## 接口规范

### 1. 认证API接口

#### 1.1 用户登录认证
```typescript
// POST /api/v1/auth/login
interface LoginRequest {
  idToken: string;                    // Apple ID Token
  provider: 'apple';                  // 认证提供方 (仅支持Apple)
}

interface LoginResponse {
  success: true;
  user: {
    id: string;
    email: string;
    displayName: string;
    isPro: boolean;
    accessToken: string;              // JWT访问令牌
    refreshToken: string;             // 刷新令牌
  };
}
```

#### 1.2 令牌刷新接口
```typescript
// POST /api/v1/auth/refresh
interface RefreshRequest {
  refreshToken: string;
}

interface RefreshResponse {
  success: true;
  accessToken: string;
  refreshToken: string;
}
```

#### 1.3 用户信息查询
```typescript
// GET /api/v1/auth/me
// Headers: Authorization: Bearer <accessToken>
interface UserInfoResponse {
  success: true;
  user: {
    id: string;
    email: string;
    displayName: string;
    isPro: boolean;
    subscriptionExpiresAt?: string;
    createdAt: string;
    updatedAt: string;
  };
}
```

### 2. 错误响应接口
```typescript
interface AuthErrorResponse {
  success: false;
  error: 'INVALID_TOKEN' | 'TOKEN_EXPIRED' | 'USER_NOT_FOUND' | 'SYSTEM_ERROR' | 'UNAUTHORIZED';
  message: string;
  timestamp: string;
}
```

---

## 数据结构设计

### 关键帧A: Apple ID Token验证输入
```typescript
// Apple ID Token Payload
interface AppleIdTokenPayload {
  iss: string;                        // 发行者 (https://appleid.apple.com)
  aud: string;                        // 受众 (你的App Bundle ID)
  exp: number;                        // 过期时间
  iat: number;                        // 签发时间
  sub: string;                        // 用户唯一标识符
  email?: string;                     // 用户邮箱 (可能不提供)
  email_verified?: boolean;           // 邮箱验证状态
}
```

### 关键帧B: 统一用户信息
```typescript
// 验证后的统一用户信息
interface ValidatedUserInfo {
  id: string;                         // 用户唯一标识符
  email: string;                      // 用户邮箱
  displayName: string;                // 用户显示名称
  provider: 'apple';                  // 认证提供方 (仅Apple)
  emailVerified: boolean;             // 邮箱验证状态
}
```

### 关键帧C: 数据库用户表结构
```typescript
// users表结构
interface UserRecord {
  id: string;                         // 用户唯一标识符 (Apple sub)
  email: string;                      // 用户邮箱
  provider: 'apple';                  // 认证提供方
  displayName: string;                // 用户显示名称
  subscription_expires_at: string | null; // 订阅过期时间
  createdAt: string;                  // 创建时间
  updatedAt: string;                  // 更新时间
}

// 计算属性增强的用户对象
interface UserWithComputedProps extends UserRecord {
  isPro: boolean;                     // 计算得出的Pro状态
}
```

### 关键帧D: JWT令牌结构
```typescript
// 访问令牌载荷
interface AccessTokenPayload {
  sub: string;                        // 用户ID
  email: string;                      // 用户邮箱
  provider: 'apple';                  // 认证提供方
  isPro: boolean;                     // Pro状态
  iat: number;                        // 签发时间
  exp: number;                        // 过期时间 (1小时)
}

// 刷新令牌载荷
interface RefreshTokenPayload {
  sub: string;                        // 用户ID
  type: 'refresh';                    // 令牌类型
  iat: number;                        // 签发时间
  exp: number;                        // 过期时间 (30天)
}
```

---

## 核心实现逻辑

### 1. 数据库表结构

#### 1.1 users表创建
```sql
-- 创建users表
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,                -- Apple用户唯一标识符
  email TEXT NOT NULL,                -- 用户邮箱
  provider TEXT NOT NULL DEFAULT 'apple', -- 认证提供方
  displayName TEXT NOT NULL,          -- 用户显示名称
  subscription_expires_at TEXT,       -- 订阅过期时间 (ISO 8601)
  createdAt TEXT NOT NULL,            -- 创建时间
  updatedAt TEXT NOT NULL             -- 更新时间
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_expires_at);
```

### 2. Apple ID Token验证服务

#### 2.1 Apple认证服务
```typescript
// services/appleAuthService.ts

export class AppleAuthService {

  /**
   * 验证Apple ID Token
   */
  static async verifyIdToken(idToken: string, env: Env): Promise<ValidatedUserInfo> {
    console.log('[Apple认证] 开始验证ID Token');

    try {
      // 1. 获取Apple公钥
      const appleKeys = await this.getApplePublicKeys();

      // 2. 解码Token头部获取kid
      const header = this.decodeTokenHeader(idToken);
      const publicKey = appleKeys.find(key => key.kid === header.kid);

      if (!publicKey) {
        throw new Error('Apple public key not found');
      }

      // 3. 验证Token签名和载荷
      const payload = await this.verifyTokenSignature(idToken, publicKey);

      // 4. 验证Token有效性
      this.validateTokenClaims(payload, env.APPLE_BUNDLE_ID);

      // 5. 构造统一用户信息
      const userInfo: ValidatedUserInfo = {
        id: payload.sub,
        email: payload.email || `${payload.sub}@privaterelay.appleid.com`,
        displayName: this.generateDisplayName(payload.email),
        provider: 'apple',
        emailVerified: payload.email_verified || false
      };

      console.log('[Apple认证] Token验证成功:', userInfo.email);
      return userInfo;

    } catch (error) {
      console.error('[Apple认证] Token验证失败:', error);
      throw new Error(`Apple ID Token validation failed: ${error.message}`);
    }
  }

  /**
   * 获取Apple公钥
   */
  private static async getApplePublicKeys(): Promise<any[]> {
    console.log('[Apple认证] 获取Apple公钥');
    const response = await fetch('https://appleid.apple.com/auth/keys');

    if (!response.ok) {
      throw new Error('Failed to fetch Apple public keys');
    }

    const data = await response.json();
    return data.keys;
  }

  /**
   * 验证Token声明
   */
  private static validateTokenClaims(payload: AppleIdTokenPayload, bundleId: string): void {
    const now = Math.floor(Date.now() / 1000);

    console.log('[Apple认证] 验证Token声明', {
      issuer: payload.iss,
      audience: payload.aud,
      expiry: payload.exp,
      now: now
    });

    // 验证发行者
    if (payload.iss !== 'https://appleid.apple.com') {
      throw new Error('Invalid issuer');
    }

    // 验证受众
    if (payload.aud !== bundleId) {
      throw new Error('Invalid audience');
    }

    // 验证过期时间
    if (payload.exp < now) {
      throw new Error('Token expired');
    }

    // 验证签发时间
    if (payload.iat > now) {
      throw new Error('Token issued in the future');
    }
  }

  /**
   * 生成显示名称
   */
  private static generateDisplayName(email?: string): string {
    if (email && !email.includes('@privaterelay.appleid.com')) {
      return email.split('@')[0];
    }
    return 'SenseWord用户';
  }
}
```

### 3. 用户管理服务

#### 3.1 用户查找或创建
```typescript
// services/userService.ts

export class UserService {

  /**
   * 查找或创建用户
   */
  static async findOrCreateUser(
    userInfo: ValidatedUserInfo,
    env: Env
  ): Promise<UserWithComputedProps> {

    console.log(`[用户管理] 处理用户: ${userInfo.email} (${userInfo.provider})`);

    try {
      // 1. 查找现有用户
      const existingUser = await env.DB.prepare(
        'SELECT * FROM users WHERE id = ?'
      ).bind(userInfo.id).first();

      if (existingUser) {
        console.log(`[用户管理] 用户已存在: ${existingUser.email}`);

        // 更新最后登录时间
        await env.DB.prepare(
          'UPDATE users SET updatedAt = ? WHERE id = ?'
        ).bind(new Date().toISOString(), userInfo.id).run();

        return this.computeUserStatus(existingUser);
      }

      // 2. 创建新用户
      console.log(`[用户管理] 创建新用户: ${userInfo.email}`);

      const newUser = {
        id: userInfo.id,
        email: userInfo.email,
        provider: userInfo.provider,
        displayName: userInfo.displayName,
        subscription_expires_at: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const insertResult = await env.DB.prepare(`
        INSERT INTO users (id, email, provider, displayName, subscription_expires_at, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).bind(
        newUser.id,
        newUser.email,
        newUser.provider,
        newUser.displayName,
        newUser.subscription_expires_at,
        newUser.createdAt,
        newUser.updatedAt
      ).run();

      if (!insertResult.success) {
        throw new Error('Failed to create user');
      }

      console.log(`[用户管理] 新用户创建成功: ${newUser.email}`);

      // 记录用户注册事件
      await this.logUserRegistration(newUser, env);

      return this.computeUserStatus(newUser);

    } catch (error) {
      console.error('[用户管理] 用户处理失败:', error);
      throw error;
    }
  }

  /**
   * 计算用户状态
   */
  static computeUserStatus(user: any): UserWithComputedProps {
    const now = new Date();
    const expiryDate = user.subscription_expires_at
      ? new Date(user.subscription_expires_at)
      : null;

    const isPro = expiryDate ? expiryDate > now : false;

    return {
      ...user,
      isPro
    };
  }

  /**
   * 记录用户注册事件
   */
  private static async logUserRegistration(user: any, env: Env): Promise<void> {
    try {
      console.log(`[用户分析] 新用户注册: ${user.provider} - ${user.email}`);
      // 这里可以发送到分析服务或记录到日志表
    } catch (error) {
      console.warn('[用户分析] 注册事件记录失败:', error);
    }
  }
}
```

### 4. JWT令牌服务

#### 4.1 令牌生成和验证
```typescript
// services/jwtService.ts

export class JWTService {

  /**
   * 生成访问令牌
   */
  static async generateAccessToken(user: UserWithComputedProps, env: Env): Promise<string> {
    console.log(`[JWT服务] 生成访问令牌: ${user.email}`);

    const payload: AccessTokenPayload = {
      sub: user.id,
      email: user.email,
      provider: user.provider,
      isPro: user.isPro,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时
    };

    return await this.signToken(payload, env.JWT_SECRET);
  }

  /**
   * 生成刷新令牌
   */
  static async generateRefreshToken(userId: string, env: Env): Promise<string> {
    console.log(`[JWT服务] 生成刷新令牌: ${userId}`);

    const payload: RefreshTokenPayload = {
      sub: userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30天
    };

    return await this.signToken(payload, env.JWT_SECRET);
  }

  /**
   * 验证访问令牌
   */
  static async verifyAccessToken(token: string, env: Env): Promise<AccessTokenPayload> {
    try {
      console.log('[JWT服务] 验证访问令牌');
      const payload = await this.verifyToken(token, env.JWT_SECRET);

      if (payload.type === 'refresh') {
        throw new Error('Invalid token type');
      }

      return payload as AccessTokenPayload;

    } catch (error) {
      console.error('[JWT服务] 访问令牌验证失败:', error);
      throw new Error(`Access token verification failed: ${error.message}`);
    }
  }

  /**
   * 验证刷新令牌
   */
  static async verifyRefreshToken(token: string, env: Env): Promise<RefreshTokenPayload> {
    try {
      console.log('[JWT服务] 验证刷新令牌');
      const payload = await this.verifyToken(token, env.JWT_SECRET);

      if (payload.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return payload as RefreshTokenPayload;

    } catch (error) {
      console.error('[JWT服务] 刷新令牌验证失败:', error);
      throw new Error(`Refresh token verification failed: ${error.message}`);
    }
  }

  /**
   * 签名令牌
   */
  private static async signToken(payload: any, secret: string): Promise<string> {
    // 使用Web Crypto API进行JWT签名
    const header = { alg: 'HS256', typ: 'JWT' };

    const encodedHeader = btoa(JSON.stringify(header));
    const encodedPayload = btoa(JSON.stringify(payload));

    const data = `${encodedHeader}.${encodedPayload}`;
    const signature = await this.hmacSign(data, secret);

    return `${data}.${signature}`;
  }

  /**
   * 验证令牌
   */
  private static async verifyToken(token: string, secret: string): Promise<any> {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid token format');
    }

    const [encodedHeader, encodedPayload, signature] = parts;
    const data = `${encodedHeader}.${encodedPayload}`;

    // 验证签名
    const expectedSignature = await this.hmacSign(data, secret);
    if (signature !== expectedSignature) {
      throw new Error('Invalid signature');
    }

    // 解析载荷
    const payload = JSON.parse(atob(encodedPayload));

    // 验证过期时间
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp < now) {
      throw new Error('Token expired');
    }

    return payload;
  }

  /**
   * HMAC签名
   */
  private static async hmacSign(data: string, secret: string): Promise<string> {
    const encoder = new TextEncoder();
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(data));
    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  }
}
```

### 5. 认证中间件

#### 5.1 请求认证中间件
```typescript
// middleware/authMiddleware.ts

export class AuthMiddleware {

  /**
   * 验证请求认证
   */
  static async authenticate(request: Request, env: Env): Promise<AccessTokenPayload> {
    const authHeader = request.headers.get('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    try {
      const payload = await JWTService.verifyAccessToken(token, env);
      console.log(`[认证中间件] 用户认证成功: ${payload.email}`);
      return payload;

    } catch (error) {
      console.error('[认证中间件] 用户认证失败:', error);
      throw new Error('Authentication failed');
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(request: Request, env: Env): Promise<UserWithComputedProps> {
    const payload = await this.authenticate(request, env);

    const user = await env.DB.prepare(
      'SELECT * FROM users WHERE id = ?'
    ).bind(payload.sub).first();

    if (!user) {
      throw new Error('User not found');
    }

    return UserService.computeUserStatus(user);
  }
}
```

---

## 测试策略

### 1. 认证流程测试
```typescript
// tests/auth.test.ts

describe('KDD-005 用户认证系统测试', () => {

  test('Apple ID Token验证', async () => {
    const mockAppleToken = generateMockAppleToken();

    const userInfo = await AppleAuthService.verifyIdToken(mockAppleToken, testEnv);

    expect(userInfo.provider).toBe('apple');
    expect(userInfo.id).toBeTruthy();
    expect(userInfo.email).toContain('@');
  });

  test('用户自动注册', async () => {
    const userInfo: ValidatedUserInfo = {
      id: 'test-user-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      provider: 'apple',
      emailVerified: true
    };

    const user = await UserService.findOrCreateUser(userInfo, testEnv);

    expect(user.id).toBe('test-user-123');
    expect(user.email).toBe('<EMAIL>');
    expect(user.isPro).toBe(false);
  });

  test('JWT令牌生成和验证', async () => {
    const mockUser = createMockUser();

    const accessToken = await JWTService.generateAccessToken(mockUser, testEnv);
    const payload = await JWTService.verifyAccessToken(accessToken, testEnv);

    expect(payload.sub).toBe(mockUser.id);
    expect(payload.email).toBe(mockUser.email);
  });

  test('认证中间件测试', async () => {
    const mockUser = createMockUser();
    const accessToken = await JWTService.generateAccessToken(mockUser, testEnv);

    const request = new Request('https://api.example.com/test', {
      headers: { 'Authorization': `Bearer ${accessToken}` }
    });

    const payload = await AuthMiddleware.authenticate(request, testEnv);
    expect(payload.sub).toBe(mockUser.id);
  });
});
```

### 2. 数据库集成测试
```typescript
// tests/database.test.ts

describe('用户数据库操作测试', () => {

  test('用户表创建和查询', async () => {
    // 测试用户创建
    const userInfo: ValidatedUserInfo = {
      id: 'apple-user-456',
      email: '<EMAIL>',
      displayName: 'Test User',
      provider: 'apple',
      emailVerified: true
    };

    const createdUser = await UserService.findOrCreateUser(userInfo, testEnv);
    expect(createdUser.id).toBe('apple-user-456');

    // 测试用户查询
    const foundUser = await UserService.findOrCreateUser(userInfo, testEnv);
    expect(foundUser.id).toBe(createdUser.id);
    expect(foundUser.email).toBe(createdUser.email);
  });

  test('Pro状态计算', async () => {
    const user = {
      id: 'test-user',
      email: '<EMAIL>',
      subscription_expires_at: new Date(Date.now() + 86400000).toISOString() // 明天过期
    };

    const userWithStatus = UserService.computeUserStatus(user);
    expect(userWithStatus.isPro).toBe(true);
  });
});
```

---

## 验收标准

### 功能验收
- [ ] Apple Sign In集成完成
- [ ] 用户自动注册功能正常
- [ ] JWT会话管理正常工作
- [ ] 认证中间件正常工作
- [ ] 错误处理机制完善
- [ ] users表创建和索引正常

### 安全验收
- [ ] Apple ID Token验证严格有效
- [ ] JWT令牌安全生成和验证
- [ ] 敏感信息不泄露
- [ ] 会话过期机制正常
- [ ] 认证中间件安全防护

### 性能验收
- [ ] 认证响应时间 < 2秒
- [ ] 令牌验证时间 < 100ms
- [ ] 数据库查询性能 < 50ms
- [ ] 并发认证支持 > 100/秒

### 依赖关系验收
- [ ] 为KDD-006用户生词本管理提供用户身份基础
- [ ] 为KDD-008 App Store购买验证提供用户账户关联
- [ ] users表结构满足后续功能需求

---

## 部署和环境配置

### 环境变量
```bash
# Apple认证配置
APPLE_BUNDLE_ID=com.yourcompany.senseword

# JWT配置
JWT_SECRET=your-super-secret-jwt-key

# 数据库
DB=your-d1-database-binding
```

### 数据库迁移
```sql
-- 在Cloudflare D1中执行
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  provider TEXT NOT NULL DEFAULT 'apple',
  displayName TEXT NOT NULL,
  subscription_expires_at TEXT,
  createdAt TEXT NOT NULL,
  updatedAt TEXT NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscription_expires_at);
```

这个用户认证注册系统(KDD-005)为SenseWord提供了安全、便捷的用户身份管理基础，是用户生词本管理和付费功能的核心依赖。
