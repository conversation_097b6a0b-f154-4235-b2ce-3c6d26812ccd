# KDD-013 简化搜索入口系统技术方案

## 概述

基于031会议"搜索模式成为进入知识世界的唯一入口"的设计理念，简化搜索功能，实现从搜索到内容流的无缝转换，消除功利性使用与沉浸式学习之间的界限。

## 核心目标

实现"一个核心入口"的极简设计，让搜索成为内容流体验的自然起点，而非独立的功能模块，确保用户从查询单词到持续学习的流畅过渡。

## 接口规范

### 输入接口

#### 1. 搜索查询
```swift
struct SearchQuery {
    let text: String
    let language: String = "en" // 默认英语
    let timestamp: Date = Date()
    let source: SearchSource
}

enum SearchSource {
    case manual        // 用户手动输入
    case suggestion    // 搜索建议选择
    case voice         // 语音输入
    case paste         // 粘贴输入
}
```

#### 2. 搜索建议
```swift
// 来源：GET /api/v1/suggestions
struct SearchSuggestion {
    let word: String
    let frequency: Int
    let isPopular: Bool
    let matchType: MatchType
}

enum MatchType {
    case prefix      // 前缀匹配
    case contains    // 包含匹配
    case fuzzy       // 模糊匹配
}
```

#### 3. 搜索历史
```swift
struct SearchHistory {
    let queries: [SearchQuery]
    let maxSize: Int = 50
    let lastCleanup: Date
}
```

### 输出接口

#### 1. 搜索状态
```swift
struct SearchState {
    let query: String
    let suggestions: [SearchSuggestion]
    let isLoading: Bool
    let error: SearchError?
    let mode: SearchMode
}

enum SearchMode {
    case input       // 输入模式
    case suggestions // 建议模式
    case history     // 历史模式
    case results     // 结果模式
}

enum SearchError {
    case networkUnavailable
    case invalidQuery
    case noResults
    case rateLimited
}
```

#### 2. 转换事件
```swift
struct SearchToStreamTransition {
    let searchQuery: SearchQuery
    let resultWord: String
    let transitionType: TransitionType
    let timestamp: Date
}

enum TransitionType {
    case directSearch    // 直接搜索结果
    case suggestionSelect // 选择搜索建议
    case historySelect   // 选择历史记录
}
```

## 核心数据结构生命周期

### 关键帧1: 搜索入口状态
```swift
struct SearchEntryState {
    let query: String = ""
    let suggestions: [SearchSuggestion] = []
    let isLoading: Bool = false
    let error: SearchError? = nil
    let mode: SearchMode = .input
    let history: SearchHistory = .empty
}
```

### 关键帧2: 输入活跃状态
```swift
struct SearchActiveState {
    let query: String // 用户输入的查询
    let suggestions: [SearchSuggestion] // 实时建议
    let isLoading: Bool = true // 获取建议中
    let error: SearchError? = nil
    let mode: SearchMode = .suggestions
    let history: SearchHistory // 显示相关历史
}
```

### 关键帧3: 结果确认状态
```swift
struct SearchConfirmedState {
    let query: String // 最终查询
    let suggestions: [SearchSuggestion] = [] // 清空建议
    let isLoading: Bool = true // 获取单词详情
    let error: SearchError? = nil
    let mode: SearchMode = .results
    let history: SearchHistory // 更新历史记录
}
```

### 关键帧4: 流转换状态
```swift
struct StreamTransitionState {
    let query: String // 保留查询记录
    let suggestions: [SearchSuggestion] = []
    let isLoading: Bool = false
    let error: SearchError? = nil
    let mode: SearchMode = .input // 重置为输入模式
    let history: SearchHistory // 已更新历史
    let transition: SearchToStreamTransition // 转换记录
}
```

## UI组件设计

### 主搜索组件
```swift
struct SimplifiedSearchView: View {
    @StateObject private var viewModel: SearchViewModel
    @FocusState private var isSearchFocused: Bool
    @State private var showHistory: Bool = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 搜索输入框
            SearchInputField(
                text: $viewModel.query,
                isLoading: viewModel.isLoading,
                onSubmit: handleSearchSubmit
            )
            .focused($isSearchFocused)
            .onTapGesture {
                showHistory = true
            }
            
            // 动态内容区域
            SearchContentArea(
                state: viewModel.state,
                showHistory: showHistory,
                onSuggestionSelect: handleSuggestionSelect,
                onHistorySelect: handleHistorySelect
            )
        }
        .background(Color(.systemBackground))
        .onAppear {
            isSearchFocused = true
        }
    }
    
    private func handleSearchSubmit() {
        guard !viewModel.query.isEmpty else { return }
        
        Task {
            await viewModel.performSearch()
            await transitionToContentStream()
        }
    }
    
    private func handleSuggestionSelect(_ suggestion: SearchSuggestion) {
        viewModel.selectSuggestion(suggestion)
        
        Task {
            await transitionToContentStream()
        }
    }
    
    private func handleHistorySelect(_ query: SearchQuery) {
        viewModel.selectFromHistory(query)
        
        Task {
            await transitionToContentStream()
        }
    }
    
    private func transitionToContentStream() async {
        // 无缝转换到内容流
        await SearchToStreamCoordinator.shared.performTransition(
            from: viewModel.state,
            to: .contentStream
        )
    }
}
```

### 搜索输入框组件
```swift
struct SearchInputField: View {
    @Binding var text: String
    let isLoading: Bool
    let onSubmit: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索图标
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.system(size: 18, weight: .medium))
            
            // 输入框
            TextField("搜索单词...", text: $text)
                .textFieldStyle(.plain)
                .font(.system(size: 18, weight: .regular))
                .submitLabel(.search)
                .onSubmit(onSubmit)
                .autocorrectionDisabled()
                .textInputAutocapitalization(.never)
            
            // 加载指示器或清除按钮
            if isLoading {
                ProgressView()
                    .scaleEffect(0.8)
            } else if !text.isEmpty {
                Button(action: { text = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal, 20)
    }
}
```

### 搜索内容区域
```swift
struct SearchContentArea: View {
    let state: SearchState
    let showHistory: Bool
    let onSuggestionSelect: (SearchSuggestion) -> Void
    let onHistorySelect: (SearchQuery) -> Void
    
    var body: some View {
        Group {
            switch state.mode {
            case .input:
                if showHistory {
                    SearchHistoryView(onSelect: onHistorySelect)
                } else {
                    SearchPlaceholderView()
                }
                
            case .suggestions:
                SearchSuggestionsView(
                    suggestions: state.suggestions,
                    onSelect: onSuggestionSelect
                )
                
            case .history:
                SearchHistoryView(onSelect: onHistorySelect)
                
            case .results:
                SearchResultsView(query: state.query)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: state.mode)
    }
}
```

### 搜索建议列表
```swift
struct SearchSuggestionsView: View {
    let suggestions: [SearchSuggestion]
    let onSelect: (SearchSuggestion) -> Void
    
    var body: some View {
        LazyVStack(spacing: 0) {
            ForEach(suggestions, id: \.word) { suggestion in
                SearchSuggestionRow(
                    suggestion: suggestion,
                    onTap: { onSelect(suggestion) }
                )
            }
        }
        .background(Color(.systemBackground))
    }
}

struct SearchSuggestionRow: View {
    let suggestion: SearchSuggestion
    let onTap: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 匹配类型图标
            Image(systemName: suggestion.matchType.iconName)
                .foregroundColor(.secondary)
                .font(.system(size: 14))
                .frame(width: 20)
            
            // 单词文本
            Text(suggestion.word)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
            
            Spacer()
            
            // 流行度指示器
            if suggestion.isPopular {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .font(.system(size: 12))
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .contentShape(Rectangle())
        .onTapGesture(perform: onTap)
    }
}
```

## 伪代码逻辑

### 主要业务流程
```swift
class SearchViewModel: ObservableObject {
    @Published var state: SearchState = .initial
    @Published var query: String = "" {
        didSet { handleQueryChange() }
    }
    
    private let suggestionService: SearchSuggestionService
    private let historyManager: SearchHistoryManager
    private let debouncer: Debouncer
    
    // [FC-01] 处理查询变化
    private func handleQueryChange() {
        guard !query.isEmpty else {
            state.mode = .input
            state.suggestions = []
            return
        }
        
        // 防抖动搜索建议
        debouncer.debounce(delay: 0.3) { [weak self] in
            await self?.fetchSuggestions()
        }
    }
    
    // [FC-02] 获取搜索建议
    private func fetchSuggestions() async {
        state.isLoading = true
        state.mode = .suggestions
        
        do {
            let suggestions = try await suggestionService.getSuggestions(
                for: query,
                limit: 8
            )
            
            await MainActor.run {
                state.suggestions = suggestions
                state.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                state.error = .networkUnavailable
                state.isLoading = false
            }
        }
    }
    
    // [FC-03] 执行搜索
    func performSearch() async {
        let searchQuery = SearchQuery(
            text: query,
            source: .manual
        )
        
        // 添加到历史记录
        historyManager.addQuery(searchQuery)
        
        // 更新状态
        state.mode = .results
        state.isLoading = true
        
        // 验证单词存在性
        do {
            let wordExists = try await WordService.shared.checkWordExists(query)
            
            if wordExists {
                // 准备转换到内容流
                await prepareStreamTransition(searchQuery)
            } else {
                state.error = .noResults
                state.isLoading = false
            }
            
        } catch {
            state.error = .networkUnavailable
            state.isLoading = false
        }
    }
    
    // [FC-04] 准备流转换
    private func prepareStreamTransition(_ searchQuery: SearchQuery) async {
        let transition = SearchToStreamTransition(
            searchQuery: searchQuery,
            resultWord: query,
            transitionType: .directSearch,
            timestamp: Date()
        )
        
        // 通知内容流系统
        await ContentStreamCoordinator.shared.initializeWithSearch(transition)
        
        // 重置搜索状态
        await MainActor.run {
            resetSearchState()
        }
    }
    
    // [FC-05] 重置搜索状态
    private func resetSearchState() {
        query = ""
        state = SearchState(
            query: "",
            suggestions: [],
            isLoading: false,
            error: nil,
            mode: .input
        )
    }
}
```

### 搜索历史管理
```swift
class SearchHistoryManager {
    private let userDefaults = UserDefaults.standard
    private let historyKey = "searchHistory"
    private let maxHistorySize = 50
    
    func addQuery(_ query: SearchQuery) {
        var history = getHistory()
        
        // 移除重复项
        history.removeAll { $0.text == query.text }
        
        // 添加到开头
        history.insert(query, at: 0)
        
        // 限制大小
        if history.count > maxHistorySize {
            history = Array(history.prefix(maxHistorySize))
        }
        
        saveHistory(history)
    }
    
    func getHistory() -> [SearchQuery] {
        guard let data = userDefaults.data(forKey: historyKey),
              let history = try? JSONDecoder().decode([SearchQuery].self, from: data)
        else { return [] }
        
        return history
    }
    
    func clearHistory() {
        userDefaults.removeObject(forKey: historyKey)
    }
    
    private func saveHistory(_ history: [SearchQuery]) {
        if let data = try? JSONEncoder().encode(history) {
            userDefaults.set(data, forKey: historyKey)
        }
    }
}
```

### 无缝转换协调器
```swift
class SearchToStreamCoordinator {
    static let shared = SearchToStreamCoordinator()
    
    func performTransition(
        from searchState: SearchState,
        to destination: TransitionDestination
    ) async {
        switch destination {
        case .contentStream:
            await transitionToContentStream(searchState)
        }
    }
    
    private func transitionToContentStream(_ searchState: SearchState) async {
        // 1. 准备内容流初始状态
        let initialWord = searchState.query
        
        // 2. 通知内容流系统
        await ContentStreamCoordinator.shared.initializeWithWord(initialWord)
        
        // 3. 执行UI转换动画
        await MainActor.run {
            withAnimation(.easeInOut(duration: 0.5)) {
                AppNavigationState.shared.currentView = .contentStream
            }
        }
        
        // 4. 记录转换事件
        AnalyticsService.shared.trackSearchToStreamTransition(
            query: searchState.query,
            timestamp: Date()
        )
    }
}
```

## 性能优化

### 搜索建议优化
```swift
class SearchSuggestionOptimizer {
    private let cache = NSCache<NSString, NSArray>()
    private let cacheTimeout: TimeInterval = 300 // 5分钟
    
    func getCachedSuggestions(for query: String) -> [SearchSuggestion]? {
        let key = NSString(string: query.lowercased())
        
        if let cached = cache.object(forKey: key) as? [SearchSuggestion] {
            return cached
        }
        
        return nil
    }
    
    func cacheSuggestions(_ suggestions: [SearchSuggestion], for query: String) {
        let key = NSString(string: query.lowercased())
        cache.setObject(suggestions as NSArray, forKey: key)
        
        // 设置过期时间
        DispatchQueue.main.asyncAfter(deadline: .now() + cacheTimeout) {
            self.cache.removeObject(forKey: key)
        }
    }
}
```

### 防抖动处理
```swift
class Debouncer {
    private var workItem: DispatchWorkItem?
    
    func debounce(delay: TimeInterval, action: @escaping () async -> Void) {
        workItem?.cancel()
        
        workItem = DispatchWorkItem {
            Task {
                await action()
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem!)
    }
}
```

## 测试策略

### 单元测试
- 搜索建议获取逻辑
- 历史记录管理功能
- 防抖动机制验证

### 集成测试
- 搜索到内容流转换
- 网络异常处理
- 用户交互响应

### 性能测试
- 搜索建议响应时间 < 300ms
- 转换动画流畅度 60fps
- 内存使用稳定性

## 风险评估

### 高风险
- 网络延迟影响用户体验
- 搜索建议准确性

### 中风险
- 历史记录数据迁移
- 不同输入法兼容性

### 低风险
- UI动画性能影响
- 深色模式适配

## 依赖关系

### 前置依赖
- KDD-012: 无限内容流UI组件系统 (转换目标)

### 后续依赖
- 无 (作为入口功能，不被其他功能依赖)
