# KDD-010: API安全防护中间件系统技术方案

## 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-010 |
| **功能名称** | API安全防护中间件系统 |
| **优先级** | P1 (高优先级) |
| **预估工期** | 3天 |
| **依赖关系** | 无 (独立安全中间件) |
| **功能类型** | 安全功能 |

## 核心目标

实现"静态门票 + 动态安检"双重验证模型，保护SenseWord核心数字资产免受恶意批量爬取，确保API访问的安全性和合法性。

### 威胁分析
- **核心威胁**: 恶意开发者通过服务器脚本批量爬取D1数据库中AI生成的高质量单词JSON内容
- **攻击特征**: 系统性获取、技术能力强、攻击规模大、影响严重
- **防护策略**: 精准识别并阻止恶意脚本，同时不影响正常用户使用

---

## 接口规范

### 1. 安全防护中间件接口

#### 1.1 安全验证入口
```typescript
// 中间件函数签名
interface SecurityMiddleware {
  (request: Request, env: Env, ctx: ExecutionContext): Promise<Response | null>;
}

// 验证结果
interface SecurityValidationResult {
  success: boolean;
  securityLevel: 'BASIC' | 'ENHANCED';
  errorResponse?: Response;
  validationDetails: {
    apiKey: boolean;
    appAttest?: boolean;
  };
}
```

#### 1.2 API密钥验证接口
```typescript
interface ApiKeyValidation {
  validateApiKey(request: Request, expectedKey: string): Promise<{
    isValid: boolean;
    errorMessage?: string;
  }>;
}
```

#### 1.3 App Attest验证接口
```typescript
interface AppAttestValidation {
  verifyAttestation(
    attestationObject: string,
    challenge: string,
    keyId: string,
    bundleId: string
  ): Promise<{
    isValid: boolean;
    attestedApp?: {
      bundleId: string;
      teamId: string;
    };
    errorCode?: string;
    errorMessage?: string;
  }>;
}
```

---

## 数据结构设计

### 关键帧A: 请求输入数据
```typescript
interface SecurityRequestInput {
  request: Request;                    // 原始HTTP请求
  headers: {
    'x-api-key'?: string;             // API密钥
    'x-app-attest'?: string;          // App Attest断言
    'x-challenge'?: string;           // 挑战值
    'user-agent'?: string;            // 用户代理
  };
  metadata: {
    ip?: string;                      // 客户端IP
    timestamp: string;                // 请求时间
  };
}
```

### 关键帧B: 验证处理结果
```typescript
interface SecurityProcessingResult {
  apiKeyValidation: {
    checked: boolean;
    passed: boolean;
    errorMessage?: string;
  };
  appAttestValidation: {
    checked: boolean;
    passed: boolean;
    attestedApp?: {
      bundleId: string;
      teamId: string;
    };
    errorCode?: string;
  };
  overallResult: 'PASS' | 'FAIL' | 'DEGRADED';
  processingTime: number;             // 验证耗时(ms)
}
```

### 关键帧C: 安全响应输出
```typescript
interface SecurityResponseOutput {
  allowed: boolean;                   // 是否允许继续处理
  securityLevel: 'BASIC' | 'ENHANCED' | 'BLOCKED';
  response?: Response;                // 拒绝时的响应对象
  securityEvent?: {
    eventId: string;
    eventType: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH';
    logged: boolean;
  };
  timestamp: string;
}
```

---

## 核心实现逻辑

### 1. 安全防护中间件主函数

```typescript
// middleware/securityMiddleware.ts

export class SecurityMiddleware {
  
  /**
   * 主要安全验证入口
   */
  static async validateRequest(
    request: Request, 
    env: Env, 
    ctx: ExecutionContext
  ): Promise<SecurityValidationResult> {
    
    console.log('[安全防护] 开始请求验证');
    const startTime = Date.now();
    
    try {
      // 1. 提取请求信息
      const securityInput = await this.extractSecurityInput(request);
      
      // 2. API密钥验证 (第一道防线)
      const apiKeyResult = await this.validateApiKey(
        securityInput.headers['x-api-key'], 
        env.API_KEY
      );
      
      if (!apiKeyResult.isValid) {
        console.log('[安全防护] API密钥验证失败');
        return this.createFailureResult('API_KEY_INVALID', apiKeyResult.errorMessage);
      }
      
      // 3. App Attest验证 (第二道防线)
      const appAttestResult = await this.validateAppAttest(
        securityInput.headers['x-app-attest'],
        securityInput.headers['x-challenge'],
        env.APPLE_BUNDLE_ID
      );
      
      // 4. 决定安全级别
      const securityLevel = appAttestResult.isValid ? 'ENHANCED' : 'BASIC';
      
      // 5. 记录安全事件
      await this.logSecurityEvent({
        eventType: 'SECURITY_VALIDATION',
        result: 'SUCCESS',
        securityLevel,
        processingTime: Date.now() - startTime
      }, env);
      
      console.log(`[安全防护] 验证通过 - 安全级别: ${securityLevel}`);
      
      return {
        success: true,
        securityLevel,
        validationDetails: {
          apiKey: true,
          appAttest: appAttestResult.isValid
        }
      };
      
    } catch (error) {
      console.error('[安全防护] 验证过程异常:', error);
      return this.createFailureResult('SYSTEM_ERROR', error.message);
    }
  }
  
  /**
   * API密钥验证
   */
  private static async validateApiKey(
    providedKey: string | undefined, 
    expectedKey: string
  ): Promise<{ isValid: boolean; errorMessage?: string }> {
    
    if (!providedKey) {
      return { isValid: false, errorMessage: 'API key is required' };
    }
    
    if (providedKey !== expectedKey) {
      return { isValid: false, errorMessage: 'Invalid API key' };
    }
    
    return { isValid: true };
  }
  
  /**
   * App Attest验证
   */
  private static async validateAppAttest(
    attestationObject: string | undefined,
    challenge: string | undefined,
    bundleId: string
  ): Promise<{ isValid: boolean; errorCode?: string; errorMessage?: string }> {
    
    if (!attestationObject || !challenge) {
      return { 
        isValid: false, 
        errorCode: 'MISSING_ATTEST_DATA',
        errorMessage: 'App Attest data is missing' 
      };
    }
    
    try {
      // 调用Apple App Attest验证API
      const verificationResult = await this.callAppleAttestAPI(
        attestationObject,
        challenge,
        bundleId
      );
      
      return verificationResult;
      
    } catch (error) {
      console.error('[App Attest] 验证失败:', error);
      return { 
        isValid: false, 
        errorCode: 'ATTEST_VERIFICATION_FAILED',
        errorMessage: error.message 
      };
    }
  }
  
  /**
   * 调用Apple App Attest验证API
   */
  private static async callAppleAttestAPI(
    attestationObject: string,
    challenge: string,
    bundleId: string
  ): Promise<{ isValid: boolean; errorCode?: string; errorMessage?: string }> {
    
    // 这里实现与Apple服务器的通信逻辑
    // 具体实现需要根据Apple App Attest文档
    
    console.log('[App Attest] 调用Apple验证服务');
    
    // 模拟验证逻辑 (实际实现需要真实的Apple API调用)
    const mockVerificationResult = {
      isValid: true,
      attestedApp: {
        bundleId: bundleId,
        teamId: 'TEAM_ID'
      }
    };
    
    return mockVerificationResult;
  }
  
  /**
   * 创建失败结果
   */
  private static createFailureResult(
    errorType: string, 
    errorMessage?: string
  ): SecurityValidationResult {
    
    const errorResponse = new Response(
      JSON.stringify({
        success: false,
        error: errorType,
        message: errorMessage || 'Security validation failed',
        timestamp: new Date().toISOString()
      }),
      {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    return {
      success: false,
      securityLevel: 'BASIC',
      errorResponse,
      validationDetails: {
        apiKey: false,
        appAttest: false
      }
    };
  }
  
  /**
   * 记录安全事件
   */
  private static async logSecurityEvent(
    event: any, 
    env: Env
  ): Promise<void> {
    try {
      console.log('[安全事件]', JSON.stringify(event));
      // 这里可以实现更复杂的日志记录逻辑
      // 例如写入数据库或发送到监控系统
    } catch (error) {
      console.warn('[安全事件] 记录失败:', error);
    }
  }
}
```

---

## 部署和集成

### 1. Cloudflare Worker集成

```typescript
// worker.ts

import { SecurityMiddleware } from './middleware/securityMiddleware';

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    
    // 安全验证
    const securityResult = await SecurityMiddleware.validateRequest(request, env, ctx);
    
    if (!securityResult.success) {
      return securityResult.errorResponse!;
    }
    
    // 继续处理业务逻辑
    return await handleBusinessLogic(request, env, ctx);
  }
};
```

### 2. 环境变量配置

```bash
# 安全配置
API_KEY=your-secret-api-key
APPLE_BUNDLE_ID=com.yourcompany.senseword
APPLE_TEAM_ID=your-apple-team-id

# 安全级别配置
SECURITY_LEVEL=ENHANCED  # BASIC | ENHANCED
ENABLE_APP_ATTEST=true
```

---

## 测试策略

### 1. 安全验证测试
```typescript
describe('KDD-010 安全防护中间件测试', () => {
  
  test('API密钥验证 - 有效密钥', async () => {
    const request = createMockRequest({
      headers: { 'x-api-key': 'valid-api-key' }
    });
    
    const result = await SecurityMiddleware.validateRequest(request, testEnv, testCtx);
    
    expect(result.success).toBe(true);
    expect(result.securityLevel).toBe('BASIC');
  });
  
  test('API密钥验证 - 无效密钥', async () => {
    const request = createMockRequest({
      headers: { 'x-api-key': 'invalid-key' }
    });
    
    const result = await SecurityMiddleware.validateRequest(request, testEnv, testCtx);
    
    expect(result.success).toBe(false);
    expect(result.errorResponse?.status).toBe(403);
  });
  
  test('App Attest验证 - 完整验证', async () => {
    const request = createMockRequest({
      headers: {
        'x-api-key': 'valid-api-key',
        'x-app-attest': 'valid-attestation',
        'x-challenge': 'valid-challenge'
      }
    });
    
    const result = await SecurityMiddleware.validateRequest(request, testEnv, testCtx);
    
    expect(result.success).toBe(true);
    expect(result.securityLevel).toBe('ENHANCED');
  });
});
```

---

## 验收标准

### 功能验收
- [ ] API密钥验证中间件正常工作
- [ ] Apple App Attest集成验证成功
- [ ] 双重验证模型有效防护
- [ ] 安全事件记录完整
- [ ] 降级机制正常运行

### 安全验收
- [ ] 恶意脚本无法通过验证
- [ ] 正常App请求验证通过
- [ ] 无状态验证设计实现
- [ ] 性能影响控制在可接受范围

### 性能验收
- [ ] API密钥验证时间 < 10ms
- [ ] App Attest验证时间 < 500ms
- [ ] 整体安全验证时间 < 600ms
- [ ] 不影响现有API响应时间

这个API安全防护中间件系统为SenseWord提供了强大的安全保障，有效保护核心数字资产。
