# KDD-006: 用户生词本管理技术方案

## 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-006 |
| **功能名称** | 用户生词本管理 |
| **优先级** | P1 (高) |
| **预估工期** | 3天 |
| **依赖关系** | KDD-005 (用户认证注册系统) |
| **功能类型** | 用户功能 |

## 核心目标

实现完整的用户生词本管理功能，支持添加、删除、查询生词，提供个性化的学习体验和单词收藏功能。

### 依赖关系说明
- **依赖KDD-005用户认证系统**: 需要用户身份识别来关联生词本数据
- **数据完整性**: 确保生词本数据与用户账户正确关联
- **权限控制**: 用户只能访问自己的生词本数据

---

## 📊 接口规范

### 1. 生词本管理API接口

#### 1.1 添加生词
```typescript
// POST /api/v1/bookmarks
interface AddBookmarkRequest {
  word: string;                       // 要收藏的单词
  language: string;                   // 语言代码 (en, zh, de等)
}

interface AddBookmarkResponse {
  success: true;
  message: string;
  bookmark: {
    word: string;
    language: string;
    addedAt: string;                  // ISO 8601时间戳
  };
}
```

#### 1.2 删除生词
```typescript
// DELETE /api/v1/bookmarks
interface RemoveBookmarkRequest {
  word: string;                       // 要删除的单词
  language: string;                   // 语言代码
}

interface RemoveBookmarkResponse {
  success: true;
  message: string;
  removed: boolean;                   // 是否实际删除了记录
}
```

#### 1.3 获取生词列表
```typescript
// GET /api/v1/bookmarks?page={page}&limit={limit}&lang={language}&sort={sort}
interface GetBookmarksRequest {
  page?: number;                      // 页码 (默认1)
  limit?: number;                     // 每页数量 (默认20，最大100)
  lang?: string;                      // 语言过滤
  sort?: 'newest' | 'oldest' | 'alphabetical'; // 排序方式 (默认newest)
}

interface GetBookmarksResponse {
  success: true;
  bookmarks: BookmarkItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface BookmarkItem {
  word: string;                       // 单词文本
  language: string;                   // 语言代码
  addedAt: string;                    // 添加时间
  definition?: string;                // 核心释义 (可选，来自words表)
  studyCount?: number;                // 学习次数 (未来扩展)
}
```

#### 1.4 检查收藏状态
```typescript
// GET /api/v1/bookmarks/check?word={word}&lang={language}
interface CheckBookmarkRequest {
  word: string;                       // 要检查的单词
  lang: string;                       // 语言代码
}

interface CheckBookmarkResponse {
  success: true;
  isBookmarked: boolean;
  addedAt?: string;                   // 如果已收藏，返回添加时间
}
```

### 2. 错误响应接口
```typescript
interface BookmarkErrorResponse {
  success: false;
  error: 'UNAUTHORIZED' | 'WORD_NOT_FOUND' | 'ALREADY_BOOKMARKED' | 
         'NOT_BOOKMARKED' | 'INVALID_LANGUAGE' | 'SYSTEM_ERROR';
  message: string;
  details?: any;
}
```

---

## 🔄 数据结构设计

### 关键帧A: 用户操作请求
```typescript
// 用户发起的生词本操作
interface UserBookmarkAction {
  userId: string;                     // 已认证用户ID
  action: 'add' | 'remove' | 'list' | 'check';
  word?: string;                      // 目标单词
  language?: string;                  // 语言代码
  filters?: {                         // 查询过滤条件
    page: number;
    limit: number;
    language?: string;
    sort: string;
  };
  timestamp: number;                  // 操作时间戳
}
```

### 关键帧B: 数据库操作
```typescript
// 数据库层的操作结构
interface DatabaseBookmarkOperation {
  operation: 'INSERT' | 'DELETE' | 'SELECT';
  table: 'bookmarks';
  conditions: {
    user_id: string;
    word?: string;
    language?: string;
  };
  data?: {
    user_id: string;
    word: string;
    language: string;
    created_at: string;
  };
  pagination?: {
    offset: number;
    limit: number;
  };
  orderBy?: string;
}
```

### 关键帧C: 数据库查询结果
```typescript
// 数据库原始查询结果
interface DatabaseBookmarkResult {
  success: boolean;
  results?: DatabaseBookmarkRow[];
  changes?: number;                   // 影响的行数 (INSERT/DELETE)
  total?: number;                     // 总记录数 (SELECT COUNT)
  executionTime: number;              // 查询执行时间
}

interface DatabaseBookmarkRow {
  user_id: string;
  word: string;
  language: string;
  created_at: string;
  core_definition?: string;           // 来自JOIN查询
}
```

### 关键帧D: 最终响应数据
```typescript
// 处理后的最终响应
interface ProcessedBookmarkResponse {
  success: boolean;
  data: BookmarkItem[] | BookmarkItem | boolean;
  metadata: {
    operation: string;
    userId: string;
    responseTime: number;
    dbExecutionTime: number;
    cacheHit?: boolean;
  };
  pagination?: PaginationInfo;
}
```

---

## 💻 核心实现逻辑

### 1. 生词本服务主逻辑

#### 1.1 添加生词逻辑
```typescript
// services/bookmarkService.ts

export class BookmarkService {
  
  /**
   * 添加生词到用户收藏
   */
  static async addBookmark(
    userId: string,
    word: string,
    language: string,
    env: Env
  ): Promise<AddBookmarkResponse> {
    
    console.log(`[生词本] 用户${userId}添加生词: ${word} (${language})`);
    
    try {
      // 1. 验证单词是否存在于words表
      const wordExists = await this.verifyWordExists(word, language, env);
      if (!wordExists) {
        throw new Error('WORD_NOT_FOUND');
      }
      
      // 2. 检查是否已经收藏
      const alreadyBookmarked = await this.checkBookmarkExists(userId, word, language, env);
      if (alreadyBookmarked) {
        return {
          success: true,
          message: '该单词已在生词本中',
          bookmark: {
            word,
            language,
            addedAt: alreadyBookmarked.created_at
          }
        };
      }
      
      // 3. 插入新的收藏记录
      const createdAt = new Date().toISOString();
      
      const insertResult = await env.DB.prepare(`
        INSERT INTO bookmarks (user_id, word, language, created_at) 
        VALUES (?, ?, ?, ?)
      `).bind(userId, word, language, createdAt).run();
      
      if (!insertResult.success) {
        throw new Error('DATABASE_INSERT_FAILED');
      }
      
      console.log(`[生词本] 生词添加成功: ${word}`);
      
      // 4. 记录用户行为事件
      await this.logBookmarkEvent('ADD', userId, word, language, env);
      
      // 5. 清除相关缓存
      await this.invalidateUserBookmarkCache(userId, env);
      
      return {
        success: true,
        message: '生词添加成功',
        bookmark: {
          word,
          language,
          addedAt: createdAt
        }
      };
      
    } catch (error) {
      console.error('[生词本] 添加生词失败:', error);
      
      if (error.message === 'WORD_NOT_FOUND') {
        throw new Error('指定的单词不存在');
      }
      
      throw new Error('添加生词失败，请稍后重试');
    }
  }
  
  /**
   * 验证单词是否存在
   */
  private static async verifyWordExists(
    word: string,
    language: string,
    env: Env
  ): Promise<boolean> {
    
    const result = await env.DB.prepare(`
      SELECT 1 FROM words 
      WHERE word = ? AND language = ? 
      LIMIT 1
    `).bind(word, language).first();
    
    return !!result;
  }
  
  /**
   * 检查收藏是否已存在
   */
  private static async checkBookmarkExists(
    userId: string,
    word: string,
    language: string,
    env: Env
  ): Promise<DatabaseBookmarkRow | null> {
    
    const result = await env.DB.prepare(`
      SELECT user_id, word, language, created_at 
      FROM bookmarks 
      WHERE user_id = ? AND word = ? AND language = ?
    `).bind(userId, word, language).first();
    
    return result as DatabaseBookmarkRow | null;
  }
}
```

#### 1.2 删除生词逻辑
```typescript
/**
 * 从用户收藏中删除生词
 */
static async removeBookmark(
  userId: string,
  word: string,
  language: string,
  env: Env
): Promise<RemoveBookmarkResponse> {
  
  console.log(`[生词本] 用户${userId}删除生词: ${word} (${language})`);
  
  try {
    // 执行删除操作
    const deleteResult = await env.DB.prepare(`
      DELETE FROM bookmarks 
      WHERE user_id = ? AND word = ? AND language = ?
    `).bind(userId, word, language).run();
    
    const removed = deleteResult.changes > 0;
    
    if (removed) {
      console.log(`[生词本] 生词删除成功: ${word}`);
      
      // 记录用户行为事件
      await this.logBookmarkEvent('REMOVE', userId, word, language, env);
      
      // 清除相关缓存
      await this.invalidateUserBookmarkCache(userId, env);
    } else {
      console.log(`[生词本] 生词不存在于收藏中: ${word}`);
    }
    
    return {
      success: true,
      message: removed ? '生词删除成功' : '该单词不在生词本中',
      removed
    };
    
  } catch (error) {
    console.error('[生词本] 删除生词失败:', error);
    throw new Error('删除生词失败，请稍后重试');
  }
}
```

#### 1.3 获取生词列表逻辑
```typescript
/**
 * 获取用户生词列表
 */
static async getUserBookmarks(
  userId: string,
  filters: GetBookmarksRequest,
  env: Env
): Promise<GetBookmarksResponse> {
  
  console.log(`[生词本] 获取用户${userId}的生词列表`);
  
  try {
    // 1. 构建查询参数
    const queryParams = this.buildBookmarkQuery(filters);
    
    // 2. 检查缓存
    const cacheKey = this.generateBookmarkCacheKey(userId, filters);
    const cachedResult = await this.getCachedBookmarks(cacheKey, env);
    
    if (cachedResult) {
      console.log(`[生词本] 缓存命中: ${userId}`);
      return cachedResult;
    }
    
    // 3. 查询总数
    const totalCount = await this.getBookmarkCount(userId, filters.lang, env);
    
    // 4. 查询分页数据
    const bookmarks = await this.getBookmarkPage(userId, queryParams, env);
    
    // 5. 构建分页信息
    const pagination = this.buildPaginationInfo(
      queryParams.page,
      queryParams.limit,
      totalCount
    );
    
    // 6. 构建响应
    const response: GetBookmarksResponse = {
      success: true,
      bookmarks,
      pagination
    };
    
    // 7. 缓存结果
    await this.cacheBookmarks(cacheKey, response, env);
    
    console.log(`[生词本] 返回${bookmarks.length}个生词`);
    
    return response;
    
  } catch (error) {
    console.error('[生词本] 获取生词列表失败:', error);
    throw new Error('获取生词列表失败，请稍后重试');
  }
}

/**
 * 查询分页数据
 */
private static async getBookmarkPage(
  userId: string,
  queryParams: any,
  env: Env
): Promise<BookmarkItem[]> {
  
  // 构建SQL查询，包含JOIN获取单词定义
  let sql = `
    SELECT 
      b.word,
      b.language,
      b.created_at,
      w.core_definition
    FROM bookmarks b
    LEFT JOIN words w ON b.word = w.word AND b.language = w.language
    WHERE b.user_id = ?
  `;
  
  const params = [userId];
  
  // 添加语言过滤
  if (queryParams.language) {
    sql += ' AND b.language = ?';
    params.push(queryParams.language);
  }
  
  // 添加排序
  switch (queryParams.sort) {
    case 'oldest':
      sql += ' ORDER BY b.created_at ASC';
      break;
    case 'alphabetical':
      sql += ' ORDER BY b.word ASC';
      break;
    default: // newest
      sql += ' ORDER BY b.created_at DESC';
  }
  
  // 添加分页
  sql += ' LIMIT ? OFFSET ?';
  params.push(queryParams.limit, queryParams.offset);
  
  const { results } = await env.DB.prepare(sql).bind(...params).all();
  
  return results.map(row => ({
    word: row.word,
    language: row.language,
    addedAt: row.created_at,
    definition: row.core_definition || undefined
  }));
}
```

### 2. 缓存策略

#### 2.1 生词本缓存管理
```typescript
// services/bookmarkCacheService.ts

export class BookmarkCacheService {
  
  private static readonly CACHE_TTL = 600; // 10分钟
  private static readonly CACHE_PREFIX = 'bookmarks:';
  
  /**
   * 生成缓存键
   */
  static generateBookmarkCacheKey(
    userId: string,
    filters: GetBookmarksRequest
  ): string {
    const keyParts = [
      this.CACHE_PREFIX,
      userId,
      filters.page || 1,
      filters.limit || 20,
      filters.lang || 'all',
      filters.sort || 'newest'
    ];
    
    return keyParts.join(':');
  }
  
  /**
   * 获取缓存的生词列表
   */
  static async getCachedBookmarks(
    cacheKey: string,
    env: Env
  ): Promise<GetBookmarksResponse | null> {
    
    try {
      if (!env.CACHE) return null;
      
      const cached = await env.CACHE.get(cacheKey);
      if (!cached) return null;
      
      return JSON.parse(cached) as GetBookmarksResponse;
      
    } catch (error) {
      console.warn('[生词本缓存] 获取缓存失败:', error);
      return null;
    }
  }
  
  /**
   * 缓存生词列表
   */
  static async cacheBookmarks(
    cacheKey: string,
    response: GetBookmarksResponse,
    env: Env
  ): Promise<void> {
    
    try {
      if (!env.CACHE) return;
      
      await env.CACHE.put(
        cacheKey,
        JSON.stringify(response),
        { expirationTtl: this.CACHE_TTL }
      );
      
      console.log(`[生词本缓存] 缓存生词列表: ${cacheKey}`);
      
    } catch (error) {
      console.warn('[生词本缓存] 缓存写入失败:', error);
    }
  }
  
  /**
   * 清除用户生词本缓存
   */
  static async invalidateUserBookmarkCache(userId: string, env: Env): Promise<void> {
    try {
      if (!env.CACHE) return;
      
      // 清除该用户的所有生词本缓存
      // 注意：这里需要根据具体的缓存实现来清除模式匹配的键
      const pattern = `${this.CACHE_PREFIX}${userId}:*`;
      
      // 如果缓存支持模式删除
      // await env.CACHE.deletePattern(pattern);
      
      console.log(`[生词本缓存] 清除用户缓存: ${userId}`);
      
    } catch (error) {
      console.warn('[生词本缓存] 缓存清除失败:', error);
    }
  }
}
```

### 3. 用户行为分析

#### 3.1 行为事件记录
```typescript
/**
 * 记录生词本操作事件
 */
private static async logBookmarkEvent(
  action: 'ADD' | 'REMOVE',
  userId: string,
  word: string,
  language: string,
  env: Env
): Promise<void> {
  
  try {
    const event = {
      timestamp: new Date().toISOString(),
      eventType: `BOOKMARK_${action}`,
      userId,
      word,
      language,
      metadata: {
        userAgent: 'mobile-app', // 可以从请求头获取
        platform: 'ios'
      }
    };
    
    console.log('[用户行为]', JSON.stringify(event));
    
    // 可以发送到分析服务
    // await this.sendToAnalyticsService(event, env);
    
  } catch (error) {
    console.warn('[用户行为] 事件记录失败:', error);
  }
}
```

---

## 🧪 测试策略

### 1. 功能测试
```typescript
// tests/bookmarkService.test.ts

describe('用户生词本管理测试', () => {
  
  test('添加生词功能', async () => {
    const userId = 'test-user-123';
    const word = 'progressive';
    const language = 'en';
    
    const result = await BookmarkService.addBookmark(userId, word, language, testEnv);
    
    expect(result.success).toBe(true);
    expect(result.bookmark.word).toBe(word);
    expect(result.bookmark.language).toBe(language);
  });
  
  test('删除生词功能', async () => {
    const userId = 'test-user-123';
    const word = 'progressive';
    const language = 'en';
    
    // 先添加
    await BookmarkService.addBookmark(userId, word, language, testEnv);
    
    // 再删除
    const result = await BookmarkService.removeBookmark(userId, word, language, testEnv);
    
    expect(result.success).toBe(true);
    expect(result.removed).toBe(true);
  });
  
  test('获取生词列表功能', async () => {
    const userId = 'test-user-123';
    
    // 添加几个测试生词
    await BookmarkService.addBookmark(userId, 'word1', 'en', testEnv);
    await BookmarkService.addBookmark(userId, 'word2', 'en', testEnv);
    
    const result = await BookmarkService.getUserBookmarks(userId, {}, testEnv);
    
    expect(result.success).toBe(true);
    expect(result.bookmarks.length).toBeGreaterThanOrEqual(2);
    expect(result.pagination).toBeDefined();
  });
  
  test('重复添加处理', async () => {
    const userId = 'test-user-123';
    const word = 'duplicate';
    const language = 'en';
    
    // 第一次添加
    const result1 = await BookmarkService.addBookmark(userId, word, language, testEnv);
    expect(result1.success).toBe(true);
    
    // 第二次添加（重复）
    const result2 = await BookmarkService.addBookmark(userId, word, language, testEnv);
    expect(result2.success).toBe(true);
    expect(result2.message).toContain('已在生词本中');
  });
});
```

### 2. 性能测试
```typescript
describe('生词本性能测试', () => {
  
  test('大量生词查询性能', async () => {
    const userId = 'performance-test-user';
    
    // 添加大量测试数据
    for (let i = 0; i < 1000; i++) {
      await BookmarkService.addBookmark(userId, `word${i}`, 'en', testEnv);
    }
    
    const startTime = Date.now();
    const result = await BookmarkService.getUserBookmarks(userId, { limit: 50 }, testEnv);
    const endTime = Date.now();
    
    expect(result.success).toBe(true);
    expect(endTime - startTime).toBeLessThan(500); // 查询时间小于500ms
  });
  
  test('缓存性能测试', async () => {
    const userId = 'cache-test-user';
    
    // 第一次查询（无缓存）
    const start1 = Date.now();
    await BookmarkService.getUserBookmarks(userId, {}, testEnv);
    const time1 = Date.now() - start1;
    
    // 第二次查询（有缓存）
    const start2 = Date.now();
    await BookmarkService.getUserBookmarks(userId, {}, testEnv);
    const time2 = Date.now() - start2;
    
    // 缓存查询应该更快
    expect(time2).toBeLessThan(time1);
  });
});
```

---

## 📊 验收标准

### ✅ 功能验收
- [ ] 添加生词功能正常工作
- [ ] 删除生词功能正常工作
- [ ] 查询生词列表功能正常
- [ ] 分页和排序功能正确
- [ ] 重复操作处理合理
- [ ] 错误处理机制完善

### ✅ 性能验收
- [ ] 添加/删除操作响应时间 < 200ms
- [ ] 生词列表查询时间 < 500ms
- [ ] 缓存命中率 > 70%
- [ ] 支持1000+生词的用户

### ✅ 数据验收
- [ ] 数据一致性保证
- [ ] 外键约束正确工作
- [ ] 用户数据隔离有效
- [ ] 数据备份和恢复可用

这个用户生词本管理系统为SenseWord用户提供了完整的个性化学习体验。
