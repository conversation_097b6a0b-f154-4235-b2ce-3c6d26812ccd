# KDD-007: 付费墙客户端系统技术方案

## 功能单元概览

| 属性 | 值 |
|-----|---|
| **功能单元ID** | KDD-007 |
| **功能名称** | 付费墙客户端系统 |
| **优先级** | P1 (高) |
| **预估工期** | 3天 |
| **依赖关系** | 无 (纯客户端实现) |
| **功能类型** | 商业功能 |
| **被依赖功能** | KDD-009 (付费墙UI组件系统) |

## 核心目标

实现基于搜索次数的客户端付费墙限制系统，通过本地状态管理控制免费用户的每日搜索次数，引导用户进行付费转化。

### 架构特点
- **纯客户端实现**: 无需后端状态管理，降低系统复杂度
- **本地状态管理**: 使用UserDefaults存储每日搜索计数
- **日期重置机制**: 每日自动重置免费搜索次数

---

## 📊 接口规范

### 1. 付费墙状态管理接口

#### 1.1 搜索限制检查
```swift
// iOS客户端接口
protocol PaywallManagerProtocol {
    func checkSearchLimit() -> SearchLimitResult
    func consumeSearchQuota() -> Bool
    func resetDailyQuota()
    func getUserQuotaStatus() -> QuotaStatus
}

enum SearchLimitResult {
    case allowed(remaining: Int)
    case blocked(reason: BlockReason)
}

enum BlockReason {
    case dailyLimitReached
    case userNotAuthenticated
    case systemError
}

struct QuotaStatus {
    let isPro: Bool
    let dailySearchCount: Int
    let dailyLimit: Int
    let lastResetDate: Date
    let nextResetTime: Date
}
```

#### 1.2 用户状态同步接口
```swift
// 与后端同步用户Pro状态
protocol UserStatusSyncProtocol {
    func syncUserProStatus() async -> Result<UserProStatus, SyncError>
    func updateLocalProStatus(_ status: UserProStatus)
    func getLocalProStatus() -> UserProStatus
}

struct UserProStatus {
    let isPro: Bool
    let subscriptionExpiresAt: Date?
    let lastSyncTime: Date
}

enum SyncError {
    case networkError
    case authenticationFailed
    case serverError
}
```

### 2. 本地存储接口

#### 2.1 UserDefaults存储结构
```swift
// 本地存储的数据结构
struct PaywallLocalStorage {
    static let dailySearchCountKey = "senseword_daily_search_count"
    static let lastResetDateKey = "senseword_last_reset_date"
    static let userProStatusKey = "senseword_user_pro_status"
    static let lastSyncTimeKey = "senseword_last_sync_time"
}

// 存储的数据模型
struct LocalPaywallData: Codable {
    let dailySearchCount: Int
    let lastResetDate: Date
    let isPro: Bool
    let subscriptionExpiresAt: Date?
    let lastSyncTime: Date
}
```

---

## 🔄 数据结构设计

### 关键帧A: 用户搜索请求
```swift
// 用户发起搜索时的状态
struct SearchRequest {
    let query: String
    let timestamp: Date
    let userId: String?
    let sessionId: String
    let requestSource: RequestSource
}

enum RequestSource {
    case searchBar
    case suggestion
    case voiceInput
    case deepLink
}
```

### 关键帧B: 付费墙检查结果
```swift
// 付费墙检查后的状态
struct PaywallCheckResult {
    let isAllowed: Bool
    let reason: String
    let currentQuota: QuotaInfo
    let action: PaywallAction
}

struct QuotaInfo {
    let used: Int
    let limit: Int
    let remaining: Int
    let resetTime: Date
}

enum PaywallAction {
    case allowSearch
    case showPaywall(type: PaywallType)
    case requireAuth
    case showError(message: String)
}

enum PaywallType {
    case dailyLimitReached
    case trialExpired
    case subscriptionExpired
}
```

### 关键帧C: 本地状态更新
```swift
// 搜索配额消费后的状态
struct UpdatedLocalState {
    let previousCount: Int
    let newCount: Int
    let timestamp: Date
    let shouldSync: Bool
    let nextAction: NextAction
}

enum NextAction {
    case continueNormal
    case showWarning(remaining: Int)
    case triggerPaywall
    case syncWithServer
}
```

### 关键帧D: 付费墙展示状态
```swift
// 付费墙UI展示的数据
struct PaywallDisplayData {
    let title: String
    let message: String
    let quotaInfo: QuotaInfo
    let upgradeOptions: [UpgradeOption]
    let dismissible: Bool
}

struct UpgradeOption {
    let productId: String
    let title: String
    let price: String
    let description: String
    let isRecommended: Bool
}
```

---

## 💻 核心实现逻辑

### 1. 付费墙管理器主逻辑

#### 1.1 PaywallManager核心实现
```swift
// PaywallManager.swift

class PaywallManager: PaywallManagerProtocol {
    
    private let userDefaults = UserDefaults.standard
    private let calendar = Calendar.current
    private let dailyLimit = 5
    
    // MARK: - Public Methods
    
    func checkSearchLimit() -> SearchLimitResult {
        print("[付费墙] 检查搜索限制")
        
        // 1. 检查用户Pro状态
        let proStatus = getLocalProStatus()
        if proStatus.isPro && !isSubscriptionExpired(proStatus) {
            print("[付费墙] Pro用户，无限制")
            return .allowed(remaining: -1) // -1表示无限制
        }
        
        // 2. 检查是否需要重置每日计数
        resetDailyQuotaIfNeeded()
        
        // 3. 获取当前搜索次数
        let currentCount = getDailySearchCount()
        
        // 4. 检查是否超过限制
        if currentCount >= dailyLimit {
            print("[付费墙] 已达到每日搜索限制: \(currentCount)/\(dailyLimit)")
            return .blocked(reason: .dailyLimitReached)
        }
        
        let remaining = dailyLimit - currentCount
        print("[付费墙] 搜索允许，剩余: \(remaining)")
        
        return .allowed(remaining: remaining)
    }
    
    func consumeSearchQuota() -> Bool {
        print("[付费墙] 消费搜索配额")
        
        // 1. 再次检查限制
        let limitResult = checkSearchLimit()
        
        switch limitResult {
        case .allowed(let remaining):
            if remaining == -1 {
                // Pro用户，直接允许
                return true
            }
            
            // 2. 增加搜索计数
            let newCount = incrementDailySearchCount()
            print("[付费墙] 搜索计数更新: \(newCount)")
            
            // 3. 检查是否需要显示警告
            if newCount >= dailyLimit - 1 {
                schedulePaywallWarning(remaining: dailyLimit - newCount)
            }
            
            return true
            
        case .blocked:
            print("[付费墙] 搜索被阻止")
            return false
        }
    }
    
    func getUserQuotaStatus() -> QuotaStatus {
        let proStatus = getLocalProStatus()
        let currentCount = getDailySearchCount()
        let lastReset = getLastResetDate()
        let nextReset = getNextResetTime()
        
        return QuotaStatus(
            isPro: proStatus.isPro,
            dailySearchCount: currentCount,
            dailyLimit: dailyLimit,
            lastResetDate: lastReset,
            nextResetTime: nextReset
        )
    }
    
    // MARK: - Private Methods
    
    private func resetDailyQuotaIfNeeded() {
        let lastResetDate = getLastResetDate()
        let today = Date()
        
        if !calendar.isDate(lastResetDate, inSameDayAs: today) {
            print("[付费墙] 重置每日搜索计数")
            resetDailyQuota()
        }
    }
    
    func resetDailyQuota() {
        userDefaults.set(0, forKey: PaywallLocalStorage.dailySearchCountKey)
        userDefaults.set(Date(), forKey: PaywallLocalStorage.lastResetDateKey)
        
        print("[付费墙] 每日配额已重置")
    }
    
    private func getDailySearchCount() -> Int {
        return userDefaults.integer(forKey: PaywallLocalStorage.dailySearchCountKey)
    }
    
    private func incrementDailySearchCount() -> Int {
        let currentCount = getDailySearchCount()
        let newCount = currentCount + 1
        userDefaults.set(newCount, forKey: PaywallLocalStorage.dailySearchCountKey)
        return newCount
    }
    
    private func getLastResetDate() -> Date {
        if let date = userDefaults.object(forKey: PaywallLocalStorage.lastResetDateKey) as? Date {
            return date
        } else {
            // 首次使用，设置为今天
            let today = Date()
            userDefaults.set(today, forKey: PaywallLocalStorage.lastResetDateKey)
            return today
        }
    }
    
    private func getNextResetTime() -> Date {
        let lastReset = getLastResetDate()
        return calendar.startOfDay(for: calendar.date(byAdding: .day, value: 1, to: lastReset)!)
    }
    
    private func isSubscriptionExpired(_ status: UserProStatus) -> Bool {
        guard let expiresAt = status.subscriptionExpiresAt else {
            return true // 没有过期时间，视为已过期
        }
        
        return Date() > expiresAt
    }
    
    private func schedulePaywallWarning(remaining: Int) {
        // 在下次搜索时显示警告
        NotificationCenter.default.post(
            name: .paywallWarningScheduled,
            object: nil,
            userInfo: ["remaining": remaining]
        )
    }
}
```

### 2. 用户状态同步服务

#### 2.1 UserStatusSyncService实现
```swift
// UserStatusSyncService.swift

class UserStatusSyncService: UserStatusSyncProtocol {
    
    private let apiClient: APIClient
    private let userDefaults = UserDefaults.standard
    
    init(apiClient: APIClient) {
        self.apiClient = apiClient
    }
    
    func syncUserProStatus() async -> Result<UserProStatus, SyncError> {
        print("[状态同步] 开始同步用户Pro状态")
        
        do {
            // 1. 调用后端API获取用户信息
            let userInfo = try await apiClient.getUserInfo()
            
            // 2. 构建Pro状态
            let proStatus = UserProStatus(
                isPro: userInfo.isPro,
                subscriptionExpiresAt: userInfo.subscriptionExpiresAt,
                lastSyncTime: Date()
            )
            
            // 3. 更新本地状态
            updateLocalProStatus(proStatus)
            
            print("[状态同步] 同步成功: isPro=\(proStatus.isPro)")
            
            return .success(proStatus)
            
        } catch APIError.authenticationFailed {
            print("[状态同步] 认证失败")
            return .failure(.authenticationFailed)
            
        } catch APIError.networkError {
            print("[状态同步] 网络错误")
            return .failure(.networkError)
            
        } catch {
            print("[状态同步] 服务器错误: \(error)")
            return .failure(.serverError)
        }
    }
    
    func updateLocalProStatus(_ status: UserProStatus) {
        do {
            let data = try JSONEncoder().encode(status)
            userDefaults.set(data, forKey: PaywallLocalStorage.userProStatusKey)
            userDefaults.set(Date(), forKey: PaywallLocalStorage.lastSyncTimeKey)
            
            print("[状态同步] 本地状态已更新")
            
            // 通知其他组件状态变化
            NotificationCenter.default.post(
                name: .userProStatusUpdated,
                object: status
            )
            
        } catch {
            print("[状态同步] 本地状态更新失败: \(error)")
        }
    }
    
    func getLocalProStatus() -> UserProStatus {
        guard let data = userDefaults.data(forKey: PaywallLocalStorage.userProStatusKey),
              let status = try? JSONDecoder().decode(UserProStatus.self, from: data) else {
            
            // 返回默认状态
            return UserProStatus(
                isPro: false,
                subscriptionExpiresAt: nil,
                lastSyncTime: Date.distantPast
            )
        }
        
        return status
    }
}
```

### 3. 付费墙UI控制器

#### 3.1 PaywallViewController实现
```swift
// PaywallViewController.swift

class PaywallViewController: UIViewController {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var messageLabel: UILabel!
    @IBOutlet weak var quotaProgressView: UIProgressView!
    @IBOutlet weak var upgradeButton: UIButton!
    @IBOutlet weak var dismissButton: UIButton!
    
    private var displayData: PaywallDisplayData!
    private var onUpgrade: (() -> Void)?
    private var onDismiss: (() -> Void)?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        updateContent()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        
        // 添加动画效果
        animateAppearance()
    }
    
    // MARK: - Public Methods
    
    func configure(
        with data: PaywallDisplayData,
        onUpgrade: @escaping () -> Void,
        onDismiss: @escaping () -> Void
    ) {
        self.displayData = data
        self.onUpgrade = onUpgrade
        self.onDismiss = onDismiss
    }
    
    // MARK: - Private Methods
    
    private func setupUI() {
        view.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        
        // 设置圆角和阴影
        let containerView = view.subviews.first!
        containerView.layer.cornerRadius = 16
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.3
        containerView.layer.shadowOffset = CGSize(width: 0, height: 4)
        containerView.layer.shadowRadius = 8
        
        // 设置按钮样式
        upgradeButton.layer.cornerRadius = 8
        upgradeButton.backgroundColor = UIColor.systemBlue
        
        dismissButton.layer.cornerRadius = 8
        dismissButton.backgroundColor = UIColor.systemGray5
    }
    
    private func updateContent() {
        guard let data = displayData else { return }
        
        titleLabel.text = data.title
        messageLabel.text = data.message
        
        // 更新进度条
        let progress = Float(data.quotaInfo.used) / Float(data.quotaInfo.limit)
        quotaProgressView.setProgress(progress, animated: true)
        
        // 更新按钮状态
        dismissButton.isHidden = !data.dismissible
        
        // 设置升级选项
        if let primaryOption = data.upgradeOptions.first(where: { $0.isRecommended }) {
            upgradeButton.setTitle("升级到Pro - \(primaryOption.price)", for: .normal)
        }
    }
    
    private func animateAppearance() {
        let containerView = view.subviews.first!
        
        containerView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        containerView.alpha = 0
        
        UIView.animate(
            withDuration: 0.3,
            delay: 0,
            usingSpringWithDamping: 0.8,
            initialSpringVelocity: 0,
            options: .curveEaseOut
        ) {
            containerView.transform = .identity
            containerView.alpha = 1
        }
    }
    
    // MARK: - Actions
    
    @IBAction func upgradeButtonTapped(_ sender: UIButton) {
        print("[付费墙UI] 用户点击升级按钮")
        
        // 添加点击动画
        UIView.animate(withDuration: 0.1) {
            sender.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                sender.transform = .identity
            }
        }
        
        onUpgrade?()
    }
    
    @IBAction func dismissButtonTapped(_ sender: UIButton) {
        print("[付费墙UI] 用户点击关闭按钮")
        
        dismiss(animated: true) {
            self.onDismiss?()
        }
    }
}
```

### 4. 搜索流程集成

#### 4.1 SearchCoordinator集成
```swift
// SearchCoordinator.swift

class SearchCoordinator {
    
    private let paywallManager: PaywallManager
    private let userStatusSync: UserStatusSyncService
    private let searchService: SearchService
    
    init(
        paywallManager: PaywallManager,
        userStatusSync: UserStatusSyncService,
        searchService: SearchService
    ) {
        self.paywallManager = paywallManager
        self.userStatusSync = userStatusSync
        self.searchService = searchService
    }
    
    func performSearch(query: String) async {
        print("[搜索协调] 开始搜索: \(query)")
        
        // 1. 检查搜索限制
        let limitResult = paywallManager.checkSearchLimit()
        
        switch limitResult {
        case .allowed(let remaining):
            // 2. 消费搜索配额
            guard paywallManager.consumeSearchQuota() else {
                showPaywall(type: .dailyLimitReached)
                return
            }
            
            // 3. 执行搜索
            do {
                let results = try await searchService.search(query: query)
                showSearchResults(results)
                
                // 4. 检查是否需要显示警告
                if remaining <= 2 && remaining > 0 {
                    showQuotaWarning(remaining: remaining - 1)
                }
                
            } catch {
                print("[搜索协调] 搜索失败: \(error)")
                showSearchError(error)
            }
            
        case .blocked(let reason):
            // 5. 显示付费墙
            handleSearchBlocked(reason: reason)
        }
    }
    
    private func handleSearchBlocked(reason: BlockReason) {
        switch reason {
        case .dailyLimitReached:
            showPaywall(type: .dailyLimitReached)
            
        case .userNotAuthenticated:
            showAuthenticationRequired()
            
        case .systemError:
            showSystemError()
        }
    }
    
    private func showPaywall(type: PaywallType) {
        let quotaStatus = paywallManager.getUserQuotaStatus()
        
        let displayData = PaywallDisplayData(
            title: "今日免费搜索已用完",
            message: "升级到Pro版本，享受无限次搜索和更多功能",
            quotaInfo: QuotaInfo(
                used: quotaStatus.dailySearchCount,
                limit: quotaStatus.dailyLimit,
                remaining: 0,
                resetTime: quotaStatus.nextResetTime
            ),
            upgradeOptions: getUpgradeOptions(),
            dismissible: true
        )
        
        presentPaywall(with: displayData)
    }
    
    private func showQuotaWarning(remaining: Int) {
        let message = "今日还可免费搜索 \(remaining) 次"
        showToast(message: message, style: .warning)
    }
}
```

---

## 🧪 测试策略

### 1. 单元测试
```swift
// PaywallManagerTests.swift

class PaywallManagerTests: XCTestCase {
    
    var paywallManager: PaywallManager!
    
    override func setUp() {
        super.setUp()
        paywallManager = PaywallManager()
        
        // 清除测试数据
        UserDefaults.standard.removeObject(forKey: PaywallLocalStorage.dailySearchCountKey)
        UserDefaults.standard.removeObject(forKey: PaywallLocalStorage.lastResetDateKey)
    }
    
    func testInitialSearchLimit() {
        // 测试初始状态
        let result = paywallManager.checkSearchLimit()
        
        switch result {
        case .allowed(let remaining):
            XCTAssertEqual(remaining, 5)
        case .blocked:
            XCTFail("初始状态应该允许搜索")
        }
    }
    
    func testSearchQuotaConsumption() {
        // 测试配额消费
        for i in 1...5 {
            let consumed = paywallManager.consumeSearchQuota()
            XCTAssertTrue(consumed, "第\(i)次搜索应该成功")
        }
        
        // 第6次应该被阻止
        let result = paywallManager.checkSearchLimit()
        switch result {
        case .blocked(let reason):
            XCTAssertEqual(reason, .dailyLimitReached)
        case .allowed:
            XCTFail("超过限制后应该被阻止")
        }
    }
    
    func testDailyQuotaReset() {
        // 消费所有配额
        for _ in 1...5 {
            _ = paywallManager.consumeSearchQuota()
        }
        
        // 模拟第二天
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: Date())!
        UserDefaults.standard.set(tomorrow, forKey: PaywallLocalStorage.lastResetDateKey)
        
        // 检查是否重置
        let result = paywallManager.checkSearchLimit()
        switch result {
        case .allowed(let remaining):
            XCTAssertEqual(remaining, 5)
        case .blocked:
            XCTFail("新的一天应该重置配额")
        }
    }
}
```

---

## 📊 验收标准

### ✅ 功能验收
- [ ] 每日搜索次数限制正确工作
- [ ] Pro用户无限制搜索
- [ ] 每日配额自动重置
- [ ] 用户状态同步正常
- [ ] 付费墙UI正确显示

### ✅ 用户体验验收
- [ ] 付费墙提示友好清晰
- [ ] 剩余次数提醒及时
- [ ] 升级流程顺畅
- [ ] 错误处理用户友好

### ✅ 技术验收
- [ ] 本地状态管理可靠
- [ ] 网络异常处理完善
- [ ] 内存使用合理
- [ ] 性能影响最小

这个付费墙客户端系统为SenseWord提供了有效的商业化转化机制。
