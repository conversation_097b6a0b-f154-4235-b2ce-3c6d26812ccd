# KDD-012 无限内容流UI组件系统技术方案

## 概述

基于031会议"他只要不断地向下划就好了"的核心理念，重构现有单词展示UI，实现无限滚动内容流体验，将生词本、关联概念等多源内容无缝融合在统一的流式界面中。

## 核心目标

实现"一个核心页面，一种核心交互"的极简设计哲学，让用户通过最直觉的滑动手势即可获得个性化的学习体验，完全消除导航复杂性和认知负荷。

## 接口规范

### 输入接口

#### 1. 推荐内容数据
```swift
// 来源：KDD-011 混合内容流推荐引擎
struct ContentItem {
    let word: String
    let source: RecommendationSource
    let isBookmarked: Bool
    let metadata: ContentMetadata
}

struct ContentMetadata {
    let difficulty: String
    let frequency: String
    let lastReviewedAt: Date?
    let reviewCount: Int
}
```

#### 2. 单词详细内容
```swift
// 来源：GET /api/v1/words/{word}
struct WordContent {
    let word: String
    let pronunciation: String
    let definitions: [Definition]
    let examples: [Example]
    let collocations: [Collocation]
    let relatedConcepts: [String]
}
```

#### 3. 用户交互事件
```swift
enum UserInteraction {
    case swipeDown           // 向下滑动，获取下一个内容
    case swipeUp            // 向上滑动，返回上一个内容
    case bookmark(word: String)    // 收藏单词
    case unbookmark(word: String)  // 取消收藏
    case tapDefinition(index: Int) // 点击特定定义
    case tapExample(index: Int)    // 点击特定例句
}
```

### 输出接口

#### 1. UI状态更新
```swift
struct ContentStreamState {
    let currentItem: ContentItem?
    let previousItems: [ContentItem] // 最近3个
    let nextItems: [ContentItem]     // 预加载2个
    let isLoading: Bool
    let error: StreamError?
}

enum StreamError {
    case networkUnavailable
    case contentExhausted
    case recommendationFailed
}
```

#### 2. 用户反馈事件
```swift
struct UserFeedbackEvent {
    let action: UserInteraction
    let timestamp: Date
    let contentItem: ContentItem
    let sessionContext: SessionContext
}

struct SessionContext {
    let sessionId: UUID
    let startTime: Date
    let itemsViewed: Int
    let bookmarksAdded: Int
}
```

## 核心数据结构生命周期

### 关键帧1: 初始加载状态
```swift
struct InitialLoadingState {
    let currentItem: ContentItem? = nil
    let previousItems: [ContentItem] = []
    let nextItems: [ContentItem] = []
    let isLoading: Bool = true
    let error: StreamError? = nil
    let sessionContext: SessionContext = .new()
}
```

### 关键帧2: 内容就绪状态
```swift
struct ContentReadyState {
    let currentItem: ContentItem // 当前显示的内容
    let previousItems: [ContentItem] = [] // 初始为空
    let nextItems: [ContentItem] // 预加载的2个内容
    let isLoading: Bool = false
    let error: StreamError? = nil
    let sessionContext: SessionContext // 会话开始
}
```

### 关键帧3: 流式浏览状态
```swift
struct StreamingState {
    let currentItem: ContentItem // 当前内容
    let previousItems: [ContentItem] // 历史内容（最多3个）
    let nextItems: [ContentItem] // 预加载内容（保持2个）
    let isLoading: Bool = false // 后台预加载
    let error: StreamError? = nil
    let sessionContext: SessionContext // 更新浏览统计
}
```

### 关键帧4: 交互响应状态
```swift
struct InteractionResponseState {
    let currentItem: ContentItem // 可能更新收藏状态
    let previousItems: [ContentItem]
    let nextItems: [ContentItem] // 可能触发新的推荐
    let isLoading: Bool // 可能触发加载
    let error: StreamError? = nil
    let sessionContext: SessionContext // 更新交互统计
    let lastInteraction: UserInteraction
}
```

## UI组件设计

### 主容器组件
```swift
struct ContentStreamView: View {
    @StateObject private var viewModel: ContentStreamViewModel
    @State private var currentIndex: Int = 0
    @State private var dragOffset: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            LazyVStack(spacing: 0) {
                ForEach(viewModel.visibleItems.indices, id: \.self) { index in
                    ContentItemView(
                        item: viewModel.visibleItems[index],
                        isActive: index == currentIndex
                    )
                    .frame(height: geometry.size.height)
                    .offset(y: dragOffset)
                }
            }
            .gesture(
                DragGesture()
                    .onChanged { value in
                        dragOffset = value.translation.y
                    }
                    .onEnded { value in
                        handleSwipeGesture(value, geometry: geometry)
                    }
            )
        }
        .clipped()
    }
    
    private func handleSwipeGesture(_ value: DragGesture.Value, geometry: GeometryProxy) {
        let threshold = geometry.size.height * 0.3
        
        if value.translation.y > threshold {
            // 向下滑动 - 上一个内容
            viewModel.moveToPrevious()
        } else if value.translation.y < -threshold {
            // 向上滑动 - 下一个内容
            viewModel.moveToNext()
        }
        
        // 重置偏移
        withAnimation(.spring()) {
            dragOffset = 0
        }
    }
}
```

### 内容项组件
```swift
struct ContentItemView: View {
    let item: ContentItem
    let isActive: Bool
    @State private var wordContent: WordContent?
    @State private var isBookmarked: Bool
    
    init(item: ContentItem, isActive: Bool) {
        self.item = item
        self.isActive = isActive
        self._isBookmarked = State(initialValue: item.isBookmarked)
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 收藏标签
            if isBookmarked {
                BookmarkBadgeView()
                    .transition(.scale.combined(with: .opacity))
            }
            
            // 单词标题
            WordTitleView(
                word: item.word,
                pronunciation: wordContent?.pronunciation ?? "",
                metadata: item.metadata
            )
            
            // 内容详情
            if let content = wordContent {
                WordContentView(content: content)
                    .transition(.opacity.combined(with: .slide))
            } else {
                LoadingContentView()
            }
            
            // 交互按钮
            ContentActionButtons(
                isBookmarked: isBookmarked,
                onBookmarkToggle: toggleBookmark,
                onShare: shareContent
            )
        }
        .padding()
        .onAppear {
            if isActive && wordContent == nil {
                loadWordContent()
            }
        }
        .onChange(of: isActive) { active in
            if active && wordContent == nil {
                loadWordContent()
            }
        }
    }
    
    private func loadWordContent() {
        Task {
            do {
                wordContent = try await WordService.shared.getWordContent(item.word)
            } catch {
                // 处理加载错误
                print("Failed to load content for \(item.word): \(error)")
            }
        }
    }
    
    private func toggleBookmark() {
        withAnimation(.spring()) {
            isBookmarked.toggle()
        }
        
        // 通知推荐引擎
        let interaction: UserInteraction = isBookmarked ? 
            .bookmark(word: item.word) : 
            .unbookmark(word: item.word)
        
        ContentStreamCoordinator.shared.handleUserInteraction(interaction)
    }
}
```

### 收藏标签组件
```swift
struct BookmarkBadgeView: View {
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "bookmark.fill")
                .foregroundColor(.orange)
            
            Text("已收藏")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.orange)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            Capsule()
                .fill(Color.orange.opacity(0.1))
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }
}
```

## 伪代码逻辑

### 主要流程控制
```swift
class ContentStreamViewModel: ObservableObject {
    @Published var visibleItems: [ContentItem] = []
    @Published var currentIndex: Int = 0
    @Published var isLoading: Bool = false
    @Published var error: StreamError?
    
    private let recommendationEngine: ContentStreamRecommendationEngine
    private let preloadBuffer: Int = 2
    private let historyBuffer: Int = 3
    
    // [FC-01] 初始化内容流
    func initializeStream() async {
        isLoading = true
        
        do {
            let initialItems = try await recommendationEngine.getInitialRecommendations(count: 3)
            
            await MainActor.run {
                visibleItems = initialItems
                currentIndex = 0
                isLoading = false
            }
            
            // 预加载第一个内容的详情
            preloadContent(for: initialItems.first)
            
        } catch {
            await MainActor.run {
                self.error = .recommendationFailed
                isLoading = false
            }
        }
    }
    
    // [FC-02] 移动到下一个内容
    func moveToNext() {
        guard currentIndex < visibleItems.count - 1 else {
            loadMoreContent()
            return
        }
        
        currentIndex += 1
        
        // 预加载下一个内容
        if currentIndex + 1 < visibleItems.count {
            preloadContent(for: visibleItems[currentIndex + 1])
        }
        
        // 清理历史内容
        maintainBufferSize()
        
        // 检查是否需要加载更多
        if currentIndex >= visibleItems.count - preloadBuffer {
            loadMoreContent()
        }
    }
    
    // [FC-03] 移动到上一个内容
    func moveToPrevious() {
        guard currentIndex > 0 else { return }
        currentIndex -= 1
    }
    
    // [FC-04] 加载更多内容
    private func loadMoreContent() {
        guard !isLoading else { return }
        
        isLoading = true
        
        Task {
            do {
                let moreItems = try await recommendationEngine.getNextRecommendations(
                    count: preloadBuffer,
                    context: getCurrentContext()
                )
                
                await MainActor.run {
                    visibleItems.append(contentsOf: moreItems)
                    isLoading = false
                }
                
                // 预加载新内容
                for item in moreItems {
                    preloadContent(for: item)
                }
                
            } catch {
                await MainActor.run {
                    self.error = .recommendationFailed
                    isLoading = false
                }
            }
        }
    }
    
    // [FC-05] 维护缓冲区大小
    private func maintainBufferSize() {
        // 保持历史缓冲区大小
        if currentIndex > historyBuffer {
            let removeCount = currentIndex - historyBuffer
            visibleItems.removeFirst(removeCount)
            currentIndex -= removeCount
        }
    }
    
    // [FC-06] 预加载内容详情
    private func preloadContent(for item: ContentItem?) {
        guard let item = item else { return }
        
        Task {
            try? await WordService.shared.preloadWordContent(item.word)
        }
    }
}
```

### 性能优化策略
```swift
class ContentStreamPerformanceManager {
    
    // 内存管理
    func optimizeMemoryUsage() {
        // 限制可见项目数量
        let maxVisibleItems = 10
        
        // 清理超出范围的内容缓存
        WordContentCache.shared.cleanupOldEntries(keepRecent: maxVisibleItems)
        
        // 释放不可见的图片资源
        ImageCache.shared.releaseInvisibleImages()
    }
    
    // 渲染优化
    func optimizeRendering() {
        // 使用LazyVStack减少渲染开销
        // 实现视图回收机制
        // 延迟加载非关键UI元素
    }
    
    // 网络优化
    func optimizeNetworking() {
        // 批量预加载请求
        // 智能缓存策略
        // 网络状态自适应
    }
}
```

## 动画和交互设计

### 滑动动画
```swift
struct SwipeTransition: ViewModifier {
    let offset: CGFloat
    let isActive: Bool
    
    func body(content: Content) -> some View {
        content
            .offset(y: offset)
            .scaleEffect(isActive ? 1.0 : 0.95)
            .opacity(isActive ? 1.0 : 0.7)
            .animation(.spring(response: 0.6, dampingFraction: 0.8), value: offset)
            .animation(.easeInOut(duration: 0.3), value: isActive)
    }
}
```

### 收藏动画
```swift
struct BookmarkAnimation: ViewModifier {
    let isBookmarked: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isBookmarked ? 1.2 : 1.0)
            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isBookmarked)
    }
}
```

## 测试策略

### 单元测试
- 滑动手势识别准确性
- 内容预加载逻辑
- 缓冲区管理正确性

### UI测试
- 流畅的滑动体验
- 收藏状态同步
- 错误状态处理

### 性能测试
- 内存使用峰值 < 100MB
- 滑动响应时间 < 16ms (60fps)
- 内容加载时间 < 500ms

## 风险评估

### 高风险
- 大量内容的内存管理
- 复杂手势的性能影响

### 中风险
- 网络异常的用户体验
- 不同设备尺寸的适配

### 低风险
- 动画效果的兼容性
- 深色模式的视觉效果

## 依赖关系

### 前置依赖
- KDD-011: 混合内容流推荐引擎 (提供推荐内容)

### 后续依赖
- KDD-013: 简化搜索入口系统 (集成搜索功能)
- KDD-015: 用户设置极简化 (集成设置入口)
