# TTS Worker 自动化处理系统 - 完整业务逻辑地图

## 📋 系统架构概览

基于 `/map` 模式，以下是 TTS Worker 自动化错误监控、高性能批处理与 30 秒超时保护的完整端到端业务流程：

### 🚀 **核心系统特性**

- **高吞吐量**: 1000 任务/分钟 (5 倍性能提升)
- **超时保护**: Worker 30 秒限制保护机制
- **异步架构**: 监控与下载处理完全分离
- **高并发下载**: 20 并发处理，无积压风险
- **错误监控**: 实时 Azure API 状态检查
- **自动恢复**: 手动错误清除机制 + 多状态任务自愈
- **简化队列**: 统一批量消息格式，移除向后兼容代码

### 🛡️ **30 秒超时保护机制**

- **监控限制**: 每次最多处理 20 个批次
- **时间监控**: 25 秒执行限制，主动退出
- **异步分离**: 监控推送下载消息，独立 Worker 处理
- **高并发**: 20 个 Worker 并发下载处理

### ⚙️ **关键配置参数**

- `AUTO_SUBMISSION_MAX_TASKS=1000` - 每分钟最大提交任务数
- `AUTO_SUBMISSION_BATCH_INTERVAL_MS=200` - 批处理间隔 (5TPS 安全速率)
- `MAX_BATCHES_PER_MONITOR=20` - 监控批次限制 (防止 30 秒超时)

- [x] **TTS 批处理系统 - 自动化错误监控与任务处理流程**
  - [x] **定时任务触发 (Cloudflare Worker Cron)**
    - [x] Cloudflare 平台每分钟触发 `scheduled` 事件
      - [x] `scheduled(event: any, env: Env, ctx: any): Promise<void>` (from: `cloudflare/workers/tts/src/index.ts`)
        - [x] **阶段一：Azure API 错误状态检查**
          - [x] `hasAzureApiError(env: Env): Promise<boolean>` (from: `cloudflare/workers/tts/src/services/workflow-config.service.ts`)
            - [x] `env.TTS_DB.prepare('SELECT config_value FROM workflow_config WHERE config_key = ?').bind('azure_api_error').first()`
            - [x] `JSON.parse(result.config_value as string)` → 解析 `AzureApiErrorConfig`
            - [x] → 返回 `errorConfig.error !== ''` (检查是否有错误)
          - [x] (若有 Azure API 错误)
            - [x] `console.warn('[Scheduled] ⚠️ Azure API 处于错误状态，跳过自动提交但继续监控现有批处理')`
            - [x] → 跳过自动提交，直接进入批处理监控阶段
          - [x] (若无 Azure API 错误)
            - [x] → 继续执行自动提交阶段
        - [x] **阶段二：自动提交 Pending 任务**
          - [x] `checkAndSubmitPendingTasks(env: Env)` (from: `cloudflare/workers/tts/src/services/auto-submission.service.ts`)
            - [x] **错误预检查**
              - [x] `hasAzureApiError(env)` → (若有错误) 返回 `{ success: false, message: 'Azure API 错误状态，跳过提交' }`
            - [x] **自动提交配置检查**
              - [x] `getAutoSubmissionConfig(env: Env): Promise<AutoSubmissionConfig>` (from: `cloudflare/workers/tts/src/services/workflow-config.service.ts`)
                - [x] `env.TTS_DB.prepare('SELECT config_value FROM workflow_config WHERE config_key = ?').bind('auto_submission_enabled').first()`
                - [x] `JSON.parse(result.config_value as string)` → 解析配置
                - [x] → 返回 `{ enabled: boolean, maxTasksPerSubmission: number, intervalMinutes: number }`
              - [x] (若自动提交已禁用) → 返回 `{ success: false, message: '自动提交已禁用' }`
            - [x] **查询未完成任务 (优化后的自愈机制)**
              - [x] `const maxTasks = parseInt(env.AUTO_SUBMISSION_MAX_TASKS || '1000')` → 从环境变量获取最大任务数
              - [x] **分状态优先级查询 (性能优化)**:
                - [x] `const statusPriority = ['pending', 'queued', 'processing']` → 定义优先级顺序
                - [x] **循环查询机制**: `for (const status of statusPriority)` → 按优先级逐个查询
                - [x] **单状态查询**: `env.TTS_DB.prepare('SELECT ttsId, text, type FROM tts_tasks WHERE status = ? LIMIT ?').bind(status, remainingSlots).all()`
                - [x] **性能优化**: 去掉 `ORDER BY` 排序，利用 `LIMIT` 早期终止
                - [x] **避免全表扫描**: 从读取 235 万行优化到<100 行，避免扫描 124 万条 pending 记录
                - [x] **动态限制**: `remainingSlots = maxTasks - tasks.length` → 确保总数不超过限制
              - [x] **包含三种未完成状态**:
                - [x] `pending` (优先级 1): 新任务，正常处理
                - [x] `queued` (优先级 2): 之前因 Azure API 错误卡住的任务，自动恢复
                - [x] `processing` (优先级 3): 可能需要状态同步的任务，重新检查
              - [x] → 获取最多 1000 个未完成任务，按优先级顺序收集，性能优化避免排序开销
              - [x] (若无未完成任务) → 返回 `{ success: true, tasksSubmitted: 0, message: '没有待处理任务' }`
            - [x] **按类型分组任务**
              - [x] 遍历任务，按 `TTSType` 分组到 `Map<TTSType, TTSTaskInput[]>`
            - [x] **按 50 任务批次处理 (SQL 变量限制优化)**
              - [x] 对每个类型的任务，按 50 个一批逐个处理
                - [x] 1000 个任务 → 最多 20 个批处理消息 (1000 ÷ 50 = 20)
              - [x] **逐批次安全处理流程**:
                - [x] `generateBatchId('auto')` → 生成批次 ID
                - [x] 构造 `BatchQueueMessage: { tasks, batchId, ttsType, timestamp }`
                - [x] `env.TTS_QUEUE.send(batchMessage)` → **先提交到队列**
                - [x] (队列提交成功后) → `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, updatedAt = datetime(?) WHERE ttsId IN (...)').bind('queued', 'now', ...batchTaskIds).run()`
                - [x] → **只更新成功提交队列的 50 个任务状态**
              - [x] **错误隔离机制**: 单个批次失败不影响其他批次，继续处理
              - [x] **SQL 安全保障**: 每次最多 50 个变量，远低于 SQLite 999 变量限制
            - [x] **更新自动提交记录（支持累积计数）**
              - [x] `updateLastAutoSubmission(env, totalTasksProcessed, totalBatchesCreated, true)` (from: `cloudflare/workers/tts/src/services/workflow-config.service.ts`)
                - [x] **读取现有记录**: `const existingRecord = await getLastAutoSubmissionRecord(env)`
                - [x] **日期检查逻辑**: 比较 `existingRecord.timestamp` 与当前日期，判断是否同一天
                - [x] **累积计算**: 同一天累加现有值，跨天重置为新值
                - [x] **构造累积记录**: `LastAutoSubmissionRecord` 包含累积的 `tasksSubmitted` 和 `batchesCreated`
                - [x] **数据库更新**: `env.TTS_DB.prepare('INSERT OR REPLACE INTO workflow_config ...').bind('last_auto_submission', JSON.stringify(record)).run()`
                - [x] **增强日志**: 显示当次和当日总计数据，便于监控 78
            - [x] → 返回 `{ success: true, tasksSubmitted, batchesCreated, message }`
        - [x] **阶段三：批处理状态监控 (30 秒超时保护)**
          - [x] `getPendingBatches(env: Env): Promise<PendingBatchJob[]>` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
            - [x] `env.TTS_DB.prepare('SELECT batchId, type, taskCount, submittedAt, taskMapping FROM azure_batch_jobs WHERE status IN (?, ?) ORDER BY submittedAt ASC').bind('submitted', 'running').all()`
            - [x] → 返回进行中的批处理任务列表
          - [x] (若无进行中的批处理) **性能优化：移除昂贵统计查询**
            - [x] `console.log('[Scheduled] ✅ 没有进行中的批处理任务')` → 直接记录日志
            - [x] → **不再执行 COUNT(\*) 统计查询，避免每分钟 130 万次读取操作**
            - [x] → 立即返回，节省执行时间和数据库成本
          - [x] (若有进行中的批处理)
            - [x] `monitorBatchStatus({ pendingBatches }, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
              - [x] **30 秒超时保护机制**:
                - [x] `const maxBatchesPerExecution = parseInt(env.MAX_BATCHES_PER_MONITOR || '20')` → 获取批次限制
                - [x] `const batchesToProcess = input.pendingBatches.slice(0, maxBatchesPerExecution)` → 限制处理数量
                - [x] `const startTime = Date.now(); const maxExecutionTime = 25000` → 时间监控设置
              - [x] **遍历每个批处理任务 (限制 20 个，时间监控)**
                - [x] **执行时间检查**: `if (Date.now() - startTime > maxExecutionTime) break` → 超过 25 秒主动退出
                - [x] **查询 Azure 批处理状态**
                  - [x] `fetch('https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batch.batchId}?api-version=2024-04-01', { headers: { 'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY } })`
                  - [x] (若 API 调用失败)
                    - [x] `setAzureApiError(env, errorMessage, errorType)` (from: `cloudflare/workers/tts/src/services/workflow-config.service.ts`)
                      - [x] 获取当前错误配置，增加错误计数
                      - [x] `env.TTS_DB.prepare('INSERT OR REPLACE INTO workflow_config ...').bind('azure_api_error', JSON.stringify(updatedConfig)).run()`
                    - [x] → 记录错误并继续处理其他批次
                  - [x] (若 API 调用成功) → 解析 `AzureBatchStatus`
                - [x] **更新数据库中的批处理状态**
                  - [x] 根据 Azure 状态映射到本地状态：`NotStarted/Running → 'running'`, `Succeeded → 'ready_for_download'`, `Failed → 'failed'`
                  - [x] `env.TTS_DB.prepare('UPDATE azure_batch_jobs SET status = ?, updatedAt = datetime(?) WHERE batchId = ?').bind(newStatus, 'now', batch.batchId).run()`
                - [x] **处理完成的批次 - 异步下载队列架构**
                  - [x] (若 `azureStatus.status === 'Succeeded'` 且有 `outputs.result`)
                    - [x] **更新状态为 ready_for_download**:
                      - [x] `env.TTS_DB.prepare('UPDATE azure_batch_jobs SET status = ?, updatedAt = datetime(?) WHERE batchId = ?').bind('ready_for_download', 'now', batch.batchId).run()`
                    - [x] **推送下载消息到专用队列**:
                      - [x] 构造 `DownloadQueueMessage`: `{ type: 'batch_download', batchId, zipUrl: azureStatus.outputs.result, taskMapping: batch.taskMapping, timestamp }`
                      - [x] `env.DOWNLOAD_QUEUE.send(downloadMessage)` → 推送到下载队列
                      - [x] `console.log('[Monitor] 📤 推送下载任务到队列: ${batch.batchId}')` → 记录日志
                    - [x] → **不立即处理文件下载，避免 30 秒超时**
                - [x] **处理失败的批次**
                  - [x] (若 `azureStatus.status === 'Failed'`)
                    - [x] `handleFailedBatch(batch.batchId, azureStatus.error?.message || 'Unknown error', env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                      - [x] `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, errorMessage = ?, updatedAt = datetime(?) WHERE batchId = ?').bind('failed', errorMessage, 'now', batchId).run()`
                      - [x] `env.TTS_DB.prepare('UPDATE azure_batch_jobs SET status = ?, completedAt = datetime(?) WHERE batchId = ?').bind('failed', 'now', batchId).run()`
              - [x] → 返回 `ProcessingResult` 包含处理统计信息
  - [x] **队列消费者处理 (Queue Message Handler) - 高性能配置**
    - [x] Cloudflare Queue 系统触发 `queue` 事件
      - [x] **队列配置**: `max_concurrency = 1`, `max_batch_size = 1` (单一并发，逐个处理)
      - [x] `queue(batch: any, env: Env): Promise<void>` (from: `cloudflare/workers/tts/src/index.ts`)
        - [x] 根据 `batch.queue` 路由到不同处理器
        - [x] (若 `batch.queue === 'tts-processing-queue'`)
          - [x] `processQueueBatch(batch, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
            - [x] **Azure API 错误预检查**
              - [x] `hasAzureApiError(env)` → (若有错误) 记录警告并返回，跳过处理
            - [x] **任务验证和批次创建** (已优化，移除重复分组逻辑)
              - [x] 直接处理每个 `BatchQueueMessage`，利用其 `ttsType` 字段
              - [x] 为每个消息直接创建 `ValidatedTaskBatch`，无需重新分组
              - [x] 添加防御性类型一致性检查：`type !== batchMessage.ttsType`
              - [x] **orderIndex 生成**: 基于当前批次内的相对位置 (0, 1, 2, ...) 而非全局索引
              - [x] **映射机制**: `orderIndex` 用于后续 Azure 文件名映射 (`0000.wav` → `orderIndex=0`)
            - [x] **为每个类型创建 Azure 批处理 - 速率限制保护**
              - [x] **速率限制机制**:
                - [x] `const intervalMs = parseInt(env.AUTO_SUBMISSION_BATCH_INTERVAL_MS || '200')` → 从环境变量获取间隔时间
                - [x] `await new Promise(resolve => setTimeout(resolve, intervalMs))` → 每个批处理间隔 200ms
                - [x] → 确保调用速率为 5 TPS (远低于 10 TPS 限制，50% 安全边界)
              - [x] `createAzureBatch({ validatedBatch }, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                - [x] **Azure API 错误预检查**
                  - [x] `hasAzureApiError(env)` → (若有错误) 抛出异常
                - [x] **生成批次 ID**
                  - [x] `generateBatchId()` → 生成唯一批处理标识
                - [x] **构建 Azure 批处理请求**
                  - [x] 构造 `AzureBatchRequest` 包含任务文本、语音配置等
                - [x] **创建任务映射 (taskMapping) - 规范化处理** ✅ 已优化
                  - [x] 格式：`Record<string, string>` = `orderIndex → ttsId` 映射
                  - [x] 实现：`taskMapping[task.orderIndex.toString()] = task.ttsId` (规范化为十进制数字)
                  - [x] 示例：`{ "0": "tts_123", "1": "tts_456", "2": "tts_789" }`
                  - [x] 用途：ZIP 文件解压时通过规范化处理将任意格式文件名映射回 `ttsId`
                  - [x] **规范化优势**：支持任意前导 0 格式，简化映射逻辑
                - [x] **调用 Azure 批处理 API**
                  - [x] `fetch('https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01', { method: 'PUT', headers: { 'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY }, body: JSON.stringify(azureRequest) })`
                  - [x] (若 API 调用失败)
                    - [x] `setAzureApiError(env, errorMessage, errorType)` → 记录错误到 workflow_config
                    - [x] → 抛出异常
                  - [x] (若 API 调用成功)
                    - [x] **保存批处理记录**
                      - [x] `env.TTS_DB.prepare('INSERT INTO azure_batch_jobs (batchId, type, status, taskCount, submittedAt, taskMapping) VALUES (?, ?, ?, ?, ?, ?)').bind(batchId, ttsType, 'submitted', tasks.length, submittedAt, JSON.stringify(taskMapping)).run()`
                    - [x] **更新任务状态为 Processing**
                      - [x] `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, batchId = ?, updatedAt = datetime(?) WHERE ttsId IN (...)').bind('processing', batchId, 'now', ...taskIds).run()`
                    - [x] **推送计费消息**
                      - [x] 构造 `BillingQueueMessage`
                      - [x] `env.BILLING_QUEUE.send(billingMessage)` → 触发计费处理
              - [x] (若 `createAzureBatch` 失败)
                - [x] **任务状态回滚**
                  - [x] `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, updatedAt = datetime(?) WHERE ttsId IN (...)').bind('pending', 'now', ...taskIds).run()`
                - [x] → 任务重新回到 pending 状态，等待下次自动提交
        - [x] (若 `batch.queue === 'tts-billing-queue'`)
          - [x] `handleBillingQueue(batch, env)` (from: `cloudflare/workers/tts/src/services/billing-tracker.service.ts`)
            - [x] **计费队列消息处理与 API Key 使用统计更新**
              - [x] **消息验证与解析**
                - [x] 遍历 `batch.messages` 数组，处理每个 `BillingQueueMessage`
                - [x] **消息类型验证**: 检查 `billingData.type === 'batch_creation'`，跳过无效类型
                - [x] **必需字段验证**: 验证 `batchId`, `taskCount`, `totalCharacters`, `ttsType`, `timestamp` 字段完整性
                - [x] **数据类型验证**: 确保 `totalCharacters` 为正数，跳过无效数据
                - [x] **消息确认机制**: 有效消息调用 `message.ack()`，失败消息调用 `message.retry()`
              - [x] **字符数累计统计**
                - [x] 累加所有有效消息的 `totalCharacters` 字段
                - [x] 记录处理的批次 ID 列表 `processedBatches`
                - [x] 详细日志记录：`批处理 ${batchId}: ${totalCharacters} 字符, ${taskCount} 个任务`
              - [x] **批量计费更新处理**
                - [x] (若 `totalCharacters > 0`) 调用 `handleBatchBilling(env, processedBatches.length, totalCharacters)`
                - [x] **计费统计更新流程**:
                  - [x] `getCurrentBillingStats(env)` → 从 `workflow_config` 表读取 `azure_key_billing` 配置
                  - [x] **首次计费检测**: 若 `currentStats.totalCharacters === 0` 则执行已完成任务统计
                    - [x] `getTotalCharactersFromTasks(env)` → 统计状态为 `completed` 的任务字符数（避免重复计费）
                    - [x] 基准计算：`existingCharacters + processedCharacters` 作为总计
                  - [x] **增量计费处理**: 直接累加 `currentStats.totalCharacters + processedCharacters`
                  - [x] **费用计算**: `(totalCharacters / 1000000) * 15.0` (Azure TTS 定价：$15/百万字符)
                  - [x] **统计数据构造**: 更新 `ApiKeyBillingStats` 包含总字符数、总费用、更新时间、增量字符数
                  - [x] `updateBillingStats(env, updatedStats)` → 写入 `workflow_config` 表
                - [x] **成功日志**: `批量更新计费: +${totalCharacters} 字符, 处理批次: ${processedBatches.length}, 累计: ${billingResult.billing.totalCharacters} 字符`
        - [x] (若 `batch.queue === 'download-queue'`) **🆕 异步下载队列处理器**
          - [x] `processDownloadQueue(batch, env)` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
            - [x] **队列配置**: `max_concurrency = 20`, `max_batch_size = 1` (高并发处理)
            - [x] **执行时间监控**: `const startTime = Date.now(); const maxExecutionTime = 25000` → 25 秒限制
            - [x] **遍历下载消息**:
              - [x] 解析 `DownloadQueueMessage`: `{ type: 'batch_download', batchId, zipUrl, taskMapping, timestamp }`
              - [x] **执行时间检查**: 超过 25 秒主动退出，避免超时
              - [x] **调用原有批次处理函数**:
                - [x] `processCompletedBatch({ zipUrl, taskMapping: JSON.parse(taskMapping), batchId }, env)`
                  - [x] **下载 ZIP 文件**: `fetch(zipUrl)` → 获取 Azure 生成的音频文件压缩包
                  - [x] **解压 ZIP 文件**: `JSZip.loadAsync(zipBuffer)` → 解析压缩包
                  - [x] **处理音频文件**: 遍历 ZIP 中的 `.wav` 文件，解析文件名获取 `orderIndex` ✅ 已优化
                    - [x] **文件名解析**: `filename.match(/^(\d+)\.wav$/)` → 提取数字部分
                    - [x] **规范化处理**: `parseInt(orderMatch[1], 10).toString()` → 转换为十进制数字字符串
                    - [x] **任务映射查找**: `taskMapping[normalizedOrderIndex]` → 获取对应的 `ttsId`
                    - [x] **Azure 格式确认**: 基于官方文档，Azure 使用 4 位前导 0 格式 (`0001.wav`, `0002.wav`)
                    - [x] **兼容性保证**: 规范化处理确保支持任意前导 0 格式的向前兼容
                    - [x] **音频文件提取**: `const audioContent = await file.async('arraybuffer')` → 获取音频文件内容
                  - [x] **上传到 R2 存储**: `env.AUDIO_BUCKET.put(r2Key, audioContent, { httpMetadata: { contentType: 'audio/wav' } })`
                  - [x] **更新任务状态**: `updateTasksAndBilling()` (from: `cloudflare/workers/tts/src/services/batch-processing.service.ts`)
                    - [x] 成功任务: `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, audioUrl = ?, completedAt = ? WHERE ttsId = ?').bind('completed', audioUrl, completedAt, taskId).run()`
                    - [x] 失败任务: `env.TTS_DB.prepare('UPDATE tts_tasks SET status = ?, errorMessage = ? WHERE ttsId = ?').bind('failed', error, taskId).run()`
                    - [x] **批处理状态更新**: `env.TTS_DB.prepare('UPDATE azure_batch_jobs SET status = ?, completedAt = ? WHERE batchId = ?').bind('processed', completedAt, batchId).run()`
              - [x] **错误隔离**: 单个下载任务失败不影响其他任务，继续处理
  - [x] **手动错误恢复机制 (HTTP API)**
    - [x] 管理员发起 `POST /clear-azure-error` 请求
      - [x] `handleClearAzureError(env: Env): Promise<Response>` (from: `cloudflare/workers/tts/src/index.ts`)
        - [x] `clearAzureApiError(env)` (from: `cloudflare/workers/tts/src/services/workflow-config.service.ts`)
          - [x] 构造清空的 `AzureApiErrorConfig`
          - [x] `env.TTS_DB.prepare('INSERT OR REPLACE INTO workflow_config ...').bind('azure_api_error', JSON.stringify(clearedConfig)).run()`
        - [x] → 返回成功响应，系统恢复自动提交功能

---
