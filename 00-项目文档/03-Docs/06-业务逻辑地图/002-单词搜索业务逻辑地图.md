好的，我将为您生成一份关于“单词搜索与发现”功能的业务逻辑地图。

这份地图将从用户在主界面发起搜索开始，追踪到显示搜索建议、查看单词详情，并最终进入无限内容流发现新单词的完整端到端流程。

---

### 完整文件架构树与业务职责视角

```markdown
├── 📱 UI 交互层
│ ├── Views/
│ │ ├── Main/
│ │ │ └── MainContentView.swift // 主界面容器，管理搜索覆盖层与单词详情页的展示逻辑，并响应搜索结果选择通知。
│ │ ├── Search/
│ │ │ ├── SearchView.swift // 搜索主界面，包含搜索输入框和建议列表，处理用户手势与键盘事件。
│ │ │ └── Components/
│ │ │ └── SuggestionRow.swift // 搜索建议列表的单个行项目 UI。
│ │ ├── WordResult/
│ │ │ └── WordResultView.swift // 单词详情页 UI，展示完整的单词释义、例句等内容。
│ │ ├── HorizontalStage/
│ │ │ └── WordPageContainer.swift // 单词详情页内部的水平分页容器，管理不同内容板块（释义、例句、同义词等）的切换。
│ │ └── Components/
│ │ ├── FloatingActionBar.swift // 主界面右下角的悬浮操作栏，提供“搜索”和“我的”入口。
│ │ ├── FloatingBookmarkButton.swift // 主界面左下角的悬浮收藏按钮。
│ │ └── PullUpGestureDetector.swift // 无限内容流的上拉手势检测组件。
│ └── SenseWordApp.swift // App 入口，负责在应用启动时触发后台服务的预初始化。
├── 🔄 状态管理层
│ ├── ViewModels/
│ │ ├── SearchViewModel.swift // 搜索界面的状态管理器，处理搜索查询、防抖、建议列表更新和 UI 状态转换。
│ │ └── HorizontalStageViewModel.swift // 单词详情页内水平分页的状态管理器。
│ ├── Coordinators/
│ │ └── SearchCoordinator.swift // 核心协调器，负责异步、后台初始化所有搜索相关服务（数据库、网络、索引），管理服务生命周期。
│ └── Services/ // (业务逻辑层服务)
│ ├── RecommendationArrayManager.swift // 无限内容流的核心，管理下一个推荐单词的队列。
│ └── JITPreloader.swift // Just-In-Time 预加载器，负责在用户浏览当前单词时，提前在后台加载下一个推荐单词的数据。
├── 🌐 网络服务层
│ ├── Services/
│ │ ├── Adapters/
│ │ │ ├── SearchAPIAdapter.swift // 搜索 API 的转译层，将网络请求封装为获取“索引分页”和“下载范围”的业务方法。
│ │ │ └── WordAPIAdapter.swift // 单词 API 的转译层，将网络请求封装为获取“完整单词内容”的业务方法。
│ │ └── Business/
│ │ ├── SearchService.swift // 搜索业务的核心服务，协调本地索引和网络 API，实现 LPLC（本地优先、实时缓存）原则。
│ │ ├── LocalIndexService.swift // 本地索引服务，管理 SQLite 中的搜索索引，提供毫秒级的搜索建议查询。
│ │ └── IndexDownloadManager.swift // 索引下载管理器，协调用户设置和下载服务，实现多语言索引的按需下载。
│ ├── Network/
│ │ ├── APIClient.swift // 统一的 HTTP 客户端，处理所有网络请求的构建、发送和响应解析。
│ │ └── APIConfig.swift // 存放 API 基础 URL 和静态密钥。
│ └── Models/
│ ├── API/
│ │ ├── SearchAPIModels.swift // 定义与搜索索引 API 交互的数据结构。
│ │ └── WordAPIModels.swift // 定义与单词内容 API 交互的数据结构。
│ └── Business/
│ └── SearchModels.swift // 定义搜索业务逻辑中使用的数据模型，如`SearchSuggestion`。
└── 💾 数据持久化层
└── Infrastructure/
└── Database/
└── SQLiteManager.swift // 底层 SQLite 数据库管理器，提供线程安全的数据库连接和操作接口。
```

### 前端业务逻辑地图

- **用户启动应用到发起搜索**
  - SenseWordApp 视图加载
    - _Lifecycle Context: 应用启动，`onAppear` 生命周期方法被触发，开始执行后台初始化任务。_
    - 后台预初始化搜索服务：`Task.detached(priority: .userInitiated)` \<- [iOS/SenseWordApp.swift]
      - 延迟 1 秒以确保 UI 渲染流畅：`try? await Task.sleep(nanoseconds: 1_000_000_000)`
      - 初始化搜索服务链：`DIContainer.shared.initializeSearchServices()` \<- [iOS/SensewordApp/DI/DIContainer.swift]
        - _State Management Context: `SearchCoordinator` 状态从 `.notInitialized` 变为 `.initializing`。UI 层可以根据此状态显示全局加载指示器（如果需要）。_
        - 创建 SQLite 数据库连接（最耗时操作）：`let sqliteManager = try await SQLiteManager.create()` \<- [iOS/SensewordApp/Infrastructure/Database/SQLiteManager.swift]
          - 打开数据库文件或创建新文件。
          - 执行`PRAGMA`指令优化数据库性能（如 WAL 模式）。
          - 创建`word_index`和`bookmarks`表（如果不存在）。
        - 创建并组装服务实例：`LocalIndexService`, `SearchAPIAdapter`, `WordAPIAdapter`, `SearchService`。
        - 切换到主线程更新状态：`await MainActor.run`
          - _State Management Context: `SearchCoordinator.state` 更新为 `.ready(service)`。此时所有搜索相关的服务都已准备就绪，可以响应用户操作。_
      - 启动索引定期同步：`localIndexService.startPeriodicSync()` \<- [iOS/SensewordApp/Services/Business/LocalIndexService.swift]
  - 用户在 `MainContentView` 看到主界面
    - 显示悬浮操作栏：`FloatingActionBar` \<- [iOS/SensewordApp/Views/Components/FloatingActionBar.swift]
    - 用户点击 "搜索" 按钮
      - 触发 `onSearchTap` 回调
        - 更新状态以显示搜索覆盖层：`viewModel.isSearchActive = true` in `MainContentView` \<- [iOS/SensewordApp/Views/Main/MainContentView.swift]
          - _State Management Context: `isSearchActive` 状态变为 `true`，触发 `SearchView` 以动画形式从顶部滑入，并对 `MainContentView` 应用模糊效果。_
- **用户在搜索框输入并获得建议**
  - `SearchView` 出现
    - _Lifecycle Context: `onAppear` 被触发，键盘被自动激活。_
    - 自动聚焦搜索框：`isSearchFieldFocused = true` \<- [iOS/SensewordApp/Views/Search/SearchView.swift]
    - 用户在 `TextField` 中输入查询文本，例如 "robust"
      - 文本绑定更新 `SearchViewModel` 的 `@Published var searchQuery` 属性 \<- [iOS/SensewordApp/ViewModels/SearchViewModel.swift]
      - _State Management Context: `searchQuery` 的变化被 Combine 的 `.debounce` 操作符捕获，等待 300 毫秒以防止频繁请求。_
      - 防抖时间结束后，触发搜索建议流程：`handleDebouncedSearch(query: "robust")`
        - 更新搜索状态：`searchState = .searching`
        - 调用搜索服务获取建议：`getSuggestions(query: "robust")`
          - `searchService.getSuggestions(query: "robust")` \<- [iOS/SensewordApp/Services/Business/SearchService.swift]
            - 执行 LPLC（本地优先）策略：`performLPLCSearch(query: "robust")`
              - 调用本地索引服务：`localIndexService.getOptimizedSuggestions(query: "robust", limit: 10)` \<- [iOS/SensewordApp/Services/Business/LocalIndexService.swift]
                - 执行 SQLite 查询：`sqliteManager.searchWordSuggestions(query: "robust%", ...)` \<- [iOS/SensewordApp/Infrastructure/Database/SQLiteManager.swift]
                  - `SELECT word, coreDefinition, ... FROM word_index WHERE LOWER(word) LIKE LOWER(?) ...`
                - 返回数据库查询结果。
              - 将数据库结果转换为 `[SearchSuggestion]` 模型。
        - 异步返回搜索建议列表
          - _State Management Context: `SearchViewModel` 的 `@Published var suggestions` 属性被更新。_
          - `SearchView` 的 `suggestionsList` 根据新的 `suggestions` 数组自动刷新 UI，渲染 `SuggestionRow` 列表。
- **用户选择建议并查看单词详情**
  - 用户点击 `SuggestionRow`
    - 触发 `onTap` 回调
      - 调用 ViewModel 处理选择：`viewModel.selectSuggestionAndDismiss(suggestion)` \<- [iOS/SensewordApp/Views/Search/SearchView.swift]
        - 发布通知，将选中的单词传递给 `MainContentView`：`NotificationCenter.default.post(name: .searchResultSelected, object: "robust")`
        - 关闭搜索覆盖层：`dismissSearch()`
          - _State Management Context: `isSearchActive` 状态变为 `false`，触发 `SearchView` 向上滑出并移除，同时 `MainContentView` 的模糊效果消失。_
  - `MainContentView` 接收到通知
    - 触发 `handleSearchResult(word: "robust")` \<- [iOS/SensewordApp/Views/Main/MainContentView.swift]
      - 开始加载单词内容：`loadWordContent(word: "robust")`
        - _State Management Context: `isLoadingWord` 状态变为 `true`，`currentWordContent` 变为 `nil`。UI 上会显示加载状态（背景动画）。_
        - 启动背景动画：`startLoadingAnimation()`
        - 调用搜索服务获取完整内容：`searchService.getWordContent(word: "robust", language: .chinese)` \<- [iOS/SensewordApp/Services/Business/SearchService.swift]
          - 调用网络适配器：`wordAPIAdapter.getWord("robust")` \<- [iOS/SensewordApp/Services/Adapters/WordAPIAdapter.swift]
            - 构建 RESTful 请求：`GET /api/v1/words/en/zh/robust`
            - 调用 API 客户端发送请求：`apiClient.request(...)` \<- [iOS/SensewordApp/Network/APIClient.swift]
              - → 向后端发送网络请求 `GET https://api.senseword.app/api/v1/words/en/zh/robust`
          - 返回 `WordDefinitionResponse` 数据。
        - 内容加载完成
          - _State Management Context: `isLoadingWord` 变为 `false`，`currentWordContent` 被更新为获取到的数据，`showWordResult` 变为 `true`。_
          - `MainContentView` 根据状态变化，显示 `WordDetailContainer` 并传入 `currentWordContent` 数据。
- **用户在单词详情页进行探索与发现（无限内容流）**
  - `WordDetailContainer` 视图出现
    - _Lifecycle Context: `onAppear` 被触发，开始初始化无限内容流。_
    - 初始化推荐流：`setupWordDetailContainer()` \<- [iOS/SensewordApp/Views/Components/WordDetailContainer.swift]
      - 基于当前单词构建推荐队列：`recommendationManager.addSearchBasedRecommendations(from: "robust", wordData: ...)` \<- [iOS/SensewordApp/Services/RecommendationArrayManager.swift]
        - 获取相关概念和同义词：`fetchAllRecommendationWords(for: "robust")`
          - 如果没有预加载数据，则调用 `apiService.fetchWordDefinition(word: "robust")` 获取数据。
        - 创建包含主词和一级推荐词的 `[RecommendationItem]` 数组。
      - 预加载下一个单词的数据：`preloadNextCard()`
        - 从推荐管理器获取下一个单词：`recommendationManager.moveToNext()`
        - 调用 JIT 预加载器：`jitPreloader.preloadWord("strong")` \<- [iOS/SensewordApp/Services/JITPreloader.swift]
          - _Side Effect: 异步在后台获取 "strong" 的 `WordDefinitionResponse` 并缓存。_
        - _State Management Context: `nextWordData` 状态被更新，UI 上下一张卡片已准备好，但不可见。_
  - 用户向上滑动 `WordDetailContainer`
    - `PullUpGestureDetector` 检测到手势
    - 触发 `onTriggerNext` 回调
      - 执行卡片切换：`performCardSwitch()` \<- [iOS/SensewordApp/Views/Components/WordDetailContainer.swift]
        - _UI/State Transition: `isSwitching` 状态变为 `true`。通过动画，`nextWordData` 对应的卡片从屏幕下方滑入并放大，`currentWordData` 对应的卡片向上移出并缩小。_
        - 动画完成后，更新数据状态：
          - `currentWordData` 被更新为之前预加载的 `nextWordData` ("strong")。
          - `nextWordData` 被设为 `nil`。
        - 触发新一轮的预加载：`preloadNextCard()`
          - 从推荐管理器获取下一个单词（例如 "sturdy"）。
          - `jitPreloader.preloadWord("sturdy")`。
          - `nextWordData` 被更新为 "sturdy" 的数据。
        - 这个循环构成了无限内容流。

### 因果台账 (Causal Ledger)

| 触发源 (Trigger)           | 事件/Action (Event)            | 前置条件 (Guard)                           | 状态转移 (State Transition)                                                                                         | UI 变化 (UI Change)                                                             | 副作用 (Side Effect)                                                              | 取消条件 (Cancellation)  |
| :------------------------- | :----------------------------- | :----------------------------------------- | :------------------------------------------------------------------------------------------------------------------ | :------------------------------------------------------------------------------ | :-------------------------------------------------------------------------------- | :----------------------- |
| 应用启动                   | `AppLaunches`                  | 总是                                       | `SearchCoordinator.state: .notInitialized -> .initializing`                                                         | （无明显变化，后台进行）                                                        | 异步初始化数据库和服务 (`SQLiteManager.create`)                                   | 应用关闭                 |
| 初始化完成                 | `ServicesReady`                | `SearchCoordinator.state == .initializing` | `SearchCoordinator.state: .initializing -> .ready`                                                                  | （无明显变化）                                                                  | `SearchService` 实例可用                                                          | 应用关闭                 |
| 用户点击搜索按钮           | `ActivateSearch`               | `SearchViewModel.isSearchActive == false`  | `SearchViewModel.isSearchActive: false -> true`                                                                     | 搜索覆盖层从顶部滑入，主界面模糊                                                | 自动聚焦搜索框，预热键盘                                                          | 用户点击覆盖层外部       |
| 用户在搜索框输入文本       | `QueryChanged(newText)`        | `isSearchActive == true`                   | `SearchViewModel.searchState: .idle -> .searching`                                                                  | （无明显变化）                                                                  | 启动 300ms 防抖计时器                                                             | 新的输入文本会重置计时器 |
| 防抖计时器结束             | `DebouncedQuery(query)`        | `query.count >= 2`                         | `SearchViewModel.suggestions: [] -> [...]`                                                                          | 搜索建议列表刷新，显示匹配结果                                                  | 调用 `searchService.getSuggestions()` 查询本地 SQLite                             | 用户清空搜索框或选择建议 |
| 用户选择一条建议           | `SelectSuggestion(suggestion)` | `isSearchActive == true`                   | 1. `isSearchActive: true -> false`\<br\>2. `isLoadingWord: false -> true`                                           | 1. 搜索覆盖层滑出并消失\<br\>2. 主界面模糊效果消失\<br\>3. 显示加载动画（背景） | 1. 发布 `searchResultSelected` 通知\<br\>2. 调用 `searchService.getWordContent()` | 网络请求失败             |
| 单词内容加载成功           | `WordContentLoaded(data)`      | `isLoadingWord == true`                    | 1. `isLoadingWord: true -> false`\<br\>2. `showWordResult: false -> true`\<br\>3. `currentWordContent: nil -> data` | 加载动画消失，`WordDetailContainer` 显示，并渲染单词内容                        | （无）                                                                            | -                        |
| `WordDetailContainer` 出现 | `EnterDiscoveryMode`           | `currentWordContent != nil`                | `RecommendationState: empty -> populated`                                                                           | （无明显变化）                                                                  | 1. `recommendationManager` 构建推荐队列\<br\>2. `jitPreloader` 预加载下一个单词   | 用户关闭详情页           |
| 用户在底部上拉             | `TriggerNextWord`              | `nextWordData != nil`                      | `isSwitching: false -> true`                                                                                        | 下一张卡片从底部滑入，当前卡片向上移出                                          | 1. 触发触觉反馈\<br\>2. 停止当前音频播放                                          | -                        |
| 卡片切换动画完成           | `SwitchCompleted`              | `isSwitching == true`                      | 1. `isSwitching: true -> false`\<br\>2. `currentWordData = nextWordData`\<br\>3. `nextWordData = nil`               | 当前卡片内容更新为下一个单词                                                    | 1. `recommendationManager` 索引+1\<br\>2. `jitPreloader` 预加载新的下一个单词     | -                        |
