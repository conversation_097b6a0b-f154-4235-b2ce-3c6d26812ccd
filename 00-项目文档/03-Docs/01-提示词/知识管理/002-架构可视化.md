# 016 - 多文件模块架构可视化分析器

## 🎯 提示词使命与背景

### 为什么需要多文件模块级别的可视化？

#### **人类理解复杂项目的根本挑战**
1. **模块边界模糊**: 在复杂项目中，很难快速理解多个文件之间的协作关系和依赖边界
2. **业务流程分散**: 完整的业务逻辑往往分散在多个文件中，难以建立端到端的理解
3. **架构层次不清**: 无法快速识别系统的分层架构和各层的职责划分
4. **数据流转复杂**: 数据在多个模块间的流转路径和转换过程难以追踪

#### **可视化的解决方案**
通过**多文件模块级别的可视化**，我们可以：
- ✅ **建立全局架构视图**: 一次性理解整个模块的文件组织和依赖关系
- ✅ **追踪完整业务流程**: 展示跨文件的端到端业务处理流程
- ✅ **识别架构层次**: 清晰展示系统的分层设计和职责划分
- ✅ **理解数据流转**: 可视化数据在模块间的完整生命周期

---

## 📋 核心任务定义

你的任务是接收多个文件路径，阅读、理解、分析后创建**模块架构可视化文档**，这种文档应该：

### 🎯 **提供模块级别的架构理解**
- 展示文件间的依赖关系和调用模式
- 识别系统的分层架构和模块边界
- 展示完整的业务流程和数据流转
- 突出关键的集成点和接口设计

### 🏗️ **使用一以贯之的可视化风格**
- 统一的马卡龙色彩系统和设计规范
- 语义化的颜色分配和图标使用
- 清晰的视觉层次和信息组织
- 高对比度的文字和边框设计

### 📊 **包含多个维度的分析**
- 系统架构图：文件关系与数据流程
- 业务流程时序图：跨文件的端到端处理
- 模块依赖图：导入导出关系和类型共享
- 数据流转图：数据在模块间的生命周期

---

## 🛠️ 标准化文档结构模板

### 必需的文档结构
```markdown
# [编号] - [模块名称]架构与流程可视化文档

## 📋 文档概述
[模块的作用、包含的文件、本文档的价值]

## 🏗️ 系统架构图
### 文件关系与数据流程
[Mermaid图表 - 展示所有文件、外部系统、依赖关系、数据流]

### 架构层次说明
[按照入口层、服务层、工具层、类型层等分类说明]

## 🔄 详细处理流程时序图
### 完整[业务名称]处理流程
[Mermaid时序图 - 展示跨文件的端到端业务处理过程]

## 🔗 模块依赖关系图
### 导入导出关系与类型共享
[Mermaid图表 - 展示文件间的import/export关系和类型定义共享]

## 📊 数据流转生命周期
### 数据在模块间的完整流转
[Mermaid图表 - 展示数据从输入到输出的完整生命周期]

## 💡 关键设计特点
### 架构设计原则
[分析系统体现的设计原则和模式]

### 优化亮点
[突出系统的优化点和最佳实践]

## 🚀 系统优势
### 性能优势、稳定性保障、可维护性
[总结系统的核心优势和价值]
```

---

## 🎨 Mermaid图表设计规范

### 马卡龙柔和色彩风格
```css
%% 定义样式 - 模块级别色彩系统
classDef entryPoint fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
classDef types fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
classDef utils fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000
classDef database fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000
classDef api fill:#F0E68C,stroke:#000000,stroke-width:2px,color:#000000
```

### 设计原则要求
1. **语义化颜色系统**
   - **入口文件**: `#FFE4E1` (浅粉色) - 系统入口点
   - **服务文件**: `#E6F3FF` (浅蓝色) - 核心业务逻辑
   - **类型文件**: `#F0FFF0` (浅绿色) - 类型定义和接口
   - **工具文件**: `#FFF8DC` (浅黄色) - 工具函数和辅助功能
   - **外部系统**: `#F5F5DC` (米色) - 外部API和服务
   - **数据存储**: `#E6E6FA` (浅紫色) - 数据库和存储

2. **高对比度文字要求**
   - **高对比度**: 文字与背景色对比度 > 4.5:1
   - **可读性优先**: 浅色背景是深色、黑色文字，深色背景，无填充背景使用白色或浅色文字

3. **边框和视觉层次**
   - **统一黑色边框**: `stroke:#000000`
   - **标准边框粗细**: `stroke-width:2px`
   - **重点节点突出**: `stroke-width:3px` (导出函数、开始结束节点)
   - **清晰的视觉层次**: 通过边框粗细区分重要程度

4. **Emoji图标增强**
   - **函数节点**: 使用相关emoji增强概念画面感
   - **数据节点**: 用图标表示数据类型和用途
   - **流程节点**: 用动作图标表示操作类型
   - **连接关系**: 用箭头和虚线表示不同类型的关系

5. **线条注释规范** ⭐ **必须添加**
   - **架构图连接线**: 必须添加功能说明注释
     ```mermaid
     A -->|"数据库操作"| B
     C -->|"API调用"| D
     E -->|"类型导入"| F
     ```
   - **流程图连接线**: 必须添加操作描述注释
     ```mermaid
     START -->|"接收请求"| VALIDATE
     VALIDATE -->|"验证通过"| PROCESS
     VALIDATE -->|"验证失败"| ERROR
     ```
   - **注释内容要求**:
     - 使用双引号包围注释文字
     - 注释应简洁明确，描述具体操作或关系
     - 条件分支必须标明判断条件
     - 数据流必须标明传递的数据类型或内容

6. **避免复杂嵌套**
   - **扁平化设计**: 避免过深的subgraph嵌套
   - **清晰的分组**: 使用subgraph进行逻辑分组，但不超过3层
   - **简化连接**: 避免交叉线过多，保持图表清晰

### 暗色模式适配规范

#### 图表类型的样式配置差异

**Graph/Flowchart（流程图）**
```mermaid
graph TB
    %% 使用classDef定义样式类
    classDef default fill:#E6F3FF,stroke:#FFFFFF,stroke-width:2px,color:#000000

    %% 使用linkStyle设置连接线为白色
    linkStyle default stroke:#FFFFFF,stroke-width:2px
```

**SequenceDiagram（序列图）** ⭐ **强制要求**
```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryTextColor': '#ffffff',    // 主要文字颜色
    'lineColor': '#ffffff',           // 线条颜色
    'noteTextColor': '#000000',       // Note文字颜色（有背景）
    'noteBkgColor': '#fbbf24',        // Note背景颜色
    'activationBkgColor': '#fbbf24'   // 激活框背景
  }
}}%%
sequenceDiagram
```

#### 颜色分配策略

**文字颜色原则**
- **有背景色的文字** → 黑色 (`#000000`)
  - Note标题（黄色背景）
  - 消息框内容（黄色背景）
  - 组件内文字（有填充色背景）

- **无背景色的文字** → 白色 (`#FFFFFF`)
  - 连接线标签
  - Loop/Alt条件文字
  - 裸露的说明文字

**线条和边框**
- **所有连接线** → 白色 (`#FFFFFF`)
- **组件边框** → 白色 (`#FFFFFF`)
- **线条粗细** → 2px（保证可见性）

#### 最佳实践

**统一配置 vs 单独设置**
```typescript
// ✅ 推荐：统一配置（序列图）
%%{init: {'theme': 'dark', 'themeVariables': {...}}}%%

// ✅ 推荐：统一配置（流程图）
linkStyle default stroke:#FFFFFF,stroke-width:2px

// ❌ 不推荐：单独设置
<span style='color:#FFFFFF'>文字</span>  // 可能渲染失败
```

**配置位置**
- **序列图**：在图表开头使用 `%%{init: {...}}%%`
- **流程图**：在图表末尾使用 `linkStyle` 和 `classDef`

#### 推荐的暗色主题配置

**完整的序列图暗色配置**
```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',        // 主色调
    'primaryTextColor': '#ffffff',    // 主要文字
    'primaryBorderColor': '#ffffff',  // 主要边框
    'lineColor': '#ffffff',           // 连接线
    'noteTextColor': '#000000',       // Note文字（黑色，因为有黄色背景）
    'noteBkgColor': '#fbbf24',        // Note背景（黄色）
    'noteBorderColor': '#ffffff',     // Note边框
    'activationBkgColor': '#fbbf24',  // 激活框背景
    'sequenceNumberColor': '#ffffff'  // 序号颜色
  }
}}%%
```

**流程图暗色配置**
```mermaid
graph TB
    %% 组件样式（保持填充色，边框改白色）
    classDef default fill:#E6F3FF,stroke:#FFFFFF,stroke-width:2px,color:#000000
    classDef highlight fill:#FFB6C1,stroke:#FFFFFF,stroke-width:2px,color:#000000

    %% 连接线样式
    linkStyle default stroke:#FFFFFF,stroke-width:2px
```

#### 暗色模式检查清单

**序列图检查项**
- [ ] 添加了 `%%{init: {...}}%%` 配置
- [ ] 设置了 `primaryTextColor: '#ffffff'`
- [ ] 设置了 `lineColor: '#ffffff'`
- [ ] 设置了 `noteTextColor: '#000000'`（因为Note有背景）
- [ ] 测试了所有文字在暗色背景下的可读性

**流程图检查项**
- [ ] 添加了 `linkStyle default stroke:#FFFFFF`
- [ ] 更新了 `classDef` 中的 `stroke` 为白色
- [ ] 保持了组件内文字为黑色（因为有填充背景）
- [ ] 添加了连接线标签的白色设置

---

## 📝 具体执行步骤

### 第1步：接收和分析文件路径
1. **接收多个文件路径**: 理解用户提供的文件列表和分析范围
2. **逐个阅读文件**: 完整阅读每个文件的内容，理解其功能和作用
3. **识别文件类型**: 分类为入口文件、服务文件、类型文件、工具文件等
4. **分析依赖关系**: 识别文件间的import/export关系和函数调用

### 第2步：创建系统架构图
- 使用 `graph TB` 展示文件间的层次结构和依赖关系
- 包含所有分析的文件和相关的外部系统
- 展示数据流向和主要的业务处理路径
- 用不同颜色和图标区分文件类型和系统组件

### 第3步：创建业务流程时序图
- 使用 `sequenceDiagram` 展示完整的端到端业务处理流程
- 包含所有参与的文件和外部系统作为参与者
- 展示跨文件的函数调用顺序和数据传递
- 包含条件分支、循环处理、错误处理等逻辑

### 第4步：创建模块依赖关系图
- 使用 `graph TB` 展示文件间的导入导出关系
- 突出类型定义的共享和接口设计
- 展示模块边界和依赖方向
- 标注关键的集成点和接口

### 第5步：创建数据流转生命周期图
- 使用 `graph LR` 展示数据在模块间的完整流转
- 包含数据的输入、转换、处理、输出各个阶段
- 展示数据结构的变化和转换过程
- 标注关键的数据处理节点

### 第6步：分析和总结
- 识别系统体现的设计原则和架构模式
- 总结系统的优化亮点和最佳实践
- 分析系统的性能优势、稳定性保障、可维护性
- 提供模块级别的理解和洞察

---

## 💡 分析重点指南

### 🔍 **文件分类识别**
1. **入口文件**: 通常是index.ts、main.ts、app.ts等，负责系统启动和路由
2. **服务文件**: 包含核心业务逻辑的文件，通常以.service.ts结尾
3. **类型文件**: 定义接口和类型的文件，通常以.types.ts或.interface.ts结尾
4. **工具文件**: 提供辅助功能的文件，通常以.util.ts或.helper.ts结尾
5. **配置文件**: 包含配置信息的文件，通常以.config.ts结尾

### 🔄 **业务流程追踪**
1. **识别主要业务流程**: 从入口文件开始，追踪主要的业务处理路径
2. **跨文件函数调用**: 识别函数在不同文件间的调用关系
3. **数据传递过程**: 追踪数据在不同模块间的传递和转换
4. **错误处理机制**: 理解错误在模块间的传播和处理方式

### 🏗️ **架构模式识别**
1. **分层架构**: 识别系统是否采用分层架构（表现层、业务层、数据层）
2. **模块化设计**: 分析模块的职责划分和边界设计
3. **依赖注入**: 识别依赖关系的管理方式
4. **设计模式**: 识别使用的设计模式（工厂模式、策略模式等）

---

## 📚 真实示例参考

### TTS Worker模块架构示例 (真实项目)

#### 系统架构图示例
```mermaid
graph TB
    %% 定义样式
    classDef entryPoint fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef service fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef types fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef utils fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 主要文件节点
    INDEX["🚀 index.ts<br/>Worker主入口<br/>HTTP端点 + 定时任务"]
    TYPES["📋 realtime-tts-types.ts<br/>类型定义<br/>接口规范"]
    TASK_MGR["📊 task-manager.service.ts<br/>任务状态管理<br/>数据库操作"]
    REALTIME["🎵 realtime-tts.service.ts<br/>实时TTS处理<br/>核心业务逻辑"]
    RATE_LIMIT["⚡ rate-limiter.service.ts<br/>并发控制<br/>TPS限制"]
    AZURE_UTIL["☁️ azure-tts.util.ts<br/>Azure TTS调用<br/>音频生成"]

    %% 外部系统
    PYTHON["🐍 Python脚本<br/>任务提交器"]
    D1_DB[("💾 D1数据库<br/>tts_tasks表")]
    AZURE_API[("🌐 Azure TTS API<br/>语音合成")]
    R2_STORAGE[("📁 R2存储<br/>音频文件")]

    %% 数据流程
    PYTHON -->|"POST /submit<br/>单词级TTS任务"| INDEX
    INDEX -->|"导入类型定义"| TYPES
    INDEX -->|"调用批量插入"| TASK_MGR
    INDEX -->|"定时任务处理"| REALTIME

    TASK_MGR -->|"导入类型"| TYPES
    TASK_MGR -->|"数据库操作"| D1_DB
    TASK_MGR -->|"状态查询/更新"| D1_DB

    REALTIME -->|"导入类型"| TYPES
    REALTIME -->|"获取待处理任务"| TASK_MGR
    REALTIME -->|"并发控制"| RATE_LIMIT
    REALTIME -->|"调用Azure TTS"| AZURE_UTIL
    REALTIME -->|"更新任务状态"| TASK_MGR

    RATE_LIMIT -->|"导入类型"| TYPES
    AZURE_UTIL -->|"导入类型"| TYPES
    AZURE_UTIL -->|"API调用"| AZURE_API

    REALTIME -->|"上传音频"| R2_STORAGE

    %% 应用样式
    class INDEX entryPoint
    class TASK_MGR,REALTIME,RATE_LIMIT service
    class TYPES types
    class AZURE_UTIL utils
    class PYTHON,D1_DB,AZURE_API,R2_STORAGE external
```

#### 业务流程时序图示例
```mermaid
sequenceDiagram
    participant P as 🐍 Python脚本
    participant I as 🚀 index.ts
    participant TM as 📊 task-manager
    participant RT as 🎵 realtime-tts
    participant RL as ⚡ rate-limiter
    participant AU as ☁️ azure-util
    participant D1 as 💾 D1数据库
    participant AZ as 🌐 Azure TTS
    participant R2 as 📁 R2存储

    Note over P,R2: 📥 任务提交阶段 (HTTP端点)
    P->>I: POST /submit<br/>单词级TTS任务
    I->>TM: batchInsertTasks()
    TM->>D1: INSERT OR IGNORE<br/>批量插入任务
    D1-->>TM: 插入结果
    TM-->>I: 插入统计
    I-->>P: 提交响应<br/>{success, inserted, failed}

    Note over P,R2: ⏰ 定时处理阶段 (Cron任务)
    I->>TM: getPendingTasks(50)
    TM->>D1: SELECT pending tasks
    D1-->>TM: 待处理任务列表
    TM-->>I: 任务列表

    I->>RT: processBatchRealtimeTTS()
    RT->>RL: checkRateLimit()
    RL-->>RT: 限流检查通过

    loop 处理每个任务
        RT->>TM: updateTaskStatus(processing)
        TM->>D1: UPDATE status='processing'

        RT->>AU: callAzureRealtimeTTS()
        AU->>AZ: HTTP POST<br/>SSML语音合成
        AZ-->>AU: 音频数据(ArrayBuffer)
        AU-->>RT: 音频数据

        RT->>R2: uploadAudioToR2()
        R2-->>RT: 音频URL

        RT->>TM: updateTaskStatus(completed)
        TM->>D1: UPDATE status='completed'<br/>audioUrl, completedAt
    end

    RT-->>I: 处理完成统计
    I-->>I: 定时任务完成
```

#### 架构层次说明
- **🎯 入口层**: index.ts负责HTTP端点和定时任务协调
- **🔧 服务层**: task-manager、realtime-tts、rate-limiter提供核心业务功能
- **🛠️ 工具层**: azure-tts.util提供外部API调用功能
- **📋 类型层**: realtime-tts-types确保接口一致性

#### 模块依赖关系图示例
```mermaid
graph TB
    %% 定义样式
    classDef currentFile fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef serviceFile fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef typeFile fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef utilFile fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000

    %% 入口文件
    subgraph ENTRY["🚀 入口层"]
        INDEX_FILE["index.ts<br/>• 导入所有服务<br/>• 定义HTTP路由<br/>• 配置定时任务"]
    end

    %% 服务文件
    subgraph SERVICES["🔧 服务层"]
        REALTIME_FILE["realtime-tts.service.ts<br/>• 导入task-manager<br/>• 导入azure-util<br/>• 导入rate-limiter<br/>• 导入types"]

        TASK_MGR_FILE["task-manager.service.ts<br/>• 导入types<br/>• 数据库操作函数<br/>• 状态管理逻辑"]

        RATE_LIMIT_FILE["rate-limiter.service.ts<br/>• 导入types<br/>• 并发控制逻辑<br/>• TPS限制算法"]
    end

    %% 工具文件
    subgraph UTILS["🛠️ 工具层"]
        AZURE_FILE["azure-tts.util.ts<br/>• 导入types<br/>• Azure API调用<br/>• SSML构建逻辑"]
    end

    %% 类型文件
    subgraph TYPES["📋 类型层"]
        TYPES_FILE["realtime-tts-types.ts<br/>• 导出所有接口<br/>• 导出类型定义<br/>• 导出配置常量"]
    end

    %% 依赖关系
    INDEX_FILE --> REALTIME_FILE
    INDEX_FILE --> TASK_MGR_FILE
    INDEX_FILE --> TYPES_FILE

    REALTIME_FILE --> TASK_MGR_FILE
    REALTIME_FILE --> AZURE_FILE
    REALTIME_FILE --> RATE_LIMIT_FILE
    REALTIME_FILE --> TYPES_FILE

    TASK_MGR_FILE --> TYPES_FILE
    RATE_LIMIT_FILE --> TYPES_FILE
    AZURE_FILE --> TYPES_FILE

    %% 应用样式
    class INDEX_FILE currentFile
    class REALTIME_FILE,TASK_MGR_FILE,RATE_LIMIT_FILE serviceFile
    class AZURE_FILE utilFile
    class TYPES_FILE typeFile
```

#### 数据流转生命周期示例
```mermaid
graph LR
    %% 定义样式
    classDef inputData fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef processData fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputData fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef storageData fill:#E6E6FA,stroke:#000000,stroke-width:2px,color:#000000

    %% 输入阶段
    subgraph INPUT["📥 数据输入阶段"]
        PYTHON_DATA["Python提交数据<br/>{<br/>  word: string,<br/>  tasks: TTSTaskInput[]<br/>}"]

        HTTP_REQUEST["HTTP请求数据<br/>SubmitWordTTSRequest<br/>{<br/>  word: string,<br/>  tasks: Array&lt;{<br/>    ttsId: string,<br/>    text: string,<br/>    type: TTSType<br/>  }&gt;<br/>}"]
    end

    %% 处理阶段
    subgraph PROCESS["🔄 数据处理阶段"]
        DB_INSERT["数据库插入<br/>INSERT OR IGNORE<br/>INTO tts_tasks<br/>(ttsId, text, type, status)"]

        TASK_PROCESSING["任务处理数据<br/>TTSTaskInput → ArrayBuffer<br/>• Azure TTS调用<br/>• 音频数据生成"]

        R2_UPLOAD["R2上传数据<br/>ArrayBuffer → CDN URL<br/>• 文件上传<br/>• URL生成"]
    end

    %% 输出阶段
    subgraph OUTPUT["📤 数据输出阶段"]
        HTTP_RESPONSE["HTTP响应数据<br/>SubmitWordTTSResponse<br/>{<br/>  success: boolean,<br/>  word: string,<br/>  received: number,<br/>  inserted: number,<br/>  failed_tasks: string[]<br/>}"]

        PROCESSING_RESULT["处理结果数据<br/>TTSProcessingResult<br/>{<br/>  ttsId: string,<br/>  success: boolean,<br/>  audioUrl?: string,<br/>  processingTime: number<br/>}"]
    end

    %% 存储阶段
    subgraph STORAGE["💾 数据存储阶段"]
        D1_RECORD["D1数据库记录<br/>tts_tasks表<br/>• 任务状态<br/>• 音频URL<br/>• 时间戳"]

        R2_FILE["R2存储文件<br/>{ttsId}.wav<br/>• 音频文件<br/>• CDN访问"]
    end

    %% 数据流转
    PYTHON_DATA --> HTTP_REQUEST
    HTTP_REQUEST --> DB_INSERT
    DB_INSERT --> D1_RECORD

    DB_INSERT --> TASK_PROCESSING
    TASK_PROCESSING --> R2_UPLOAD
    R2_UPLOAD --> R2_FILE

    DB_INSERT --> HTTP_RESPONSE
    TASK_PROCESSING --> PROCESSING_RESULT

    %% 应用样式
    class PYTHON_DATA,HTTP_REQUEST inputData
    class DB_INSERT,TASK_PROCESSING,R2_UPLOAD processData
    class HTTP_RESPONSE,PROCESSING_RESULT outputData
    class D1_RECORD,R2_FILE storageData
```

#### 关键设计特点
- **单一职责原则**: 每个文件都有明确的职责边界
- **依赖倒置**: 通过类型定义确保接口一致性
- **错误隔离**: 单任务失败不影响批量处理
- **性能监控**: 完整的分段计时和性能指标
- **数据一致性**: 通过状态管理确保数据完整性
- **可扩展性**: 模块化设计便于功能扩展

---

## ✅ 质量检查清单

### 信息完整性检查
- [ ] 包含所有指定文件的分析
- [ ] 展示完整的文件依赖关系
- [ ] 包含主要业务流程的端到端分析
- [ ] 展示数据在模块间的完整流转

### 可视化质量检查
- [ ] 使用统一的马卡龙色彩风格
- [ ] 所有文字使用黑色高对比度
- [ ] 节点使用emoji图标增强概念
- [ ] 图表清晰易读，信息密度适中
- [ ] 包含系统架构图、时序图、依赖图、数据流图

### 理解效果检查
- [ ] 读者可以理解模块的整体架构
- [ ] 读者可以理解主要业务流程
- [ ] 读者可以理解文件间的依赖关系
- [ ] 读者可以理解数据的流转过程

---

## 🚀 开始执行

现在，请按照以上规范为指定的多个文件创建模块架构可视化文档。记住：

### 🎯 核心目标
**帮助人类通过可视化建立对整个项目模块的全局理解，实现从文件级别到模块级别的认知跃升。**

### 📋 执行原则
1. **全局视角优于局部细节**: 重点展示模块级别的架构和流程
2. **一以贯之的可视化风格**: 确保所有图表使用统一的设计规范
3. **业务流程优于技术实现**: 突出业务逻辑和数据流转
4. **架构理解优于代码细节**: 帮助建立系统性的架构认知

### 💡 真实示例的价值
上述TTS Worker模块示例来自真实的生产项目，展示了：

#### ✅ **完整的四维度分析**
- **系统架构图**: 清晰展示6个文件的分层架构和依赖关系
- **业务流程时序图**: 完整的端到端TTS处理流程，包含任务提交和定时处理
- **模块依赖关系图**: 详细的import/export关系和类型共享模式
- **数据流转生命周期**: 从Python输入到R2存储的完整数据流转

#### ✅ **生产级别的设计模式**
- **微服务架构**: 清晰的服务边界和职责划分
- **依赖注入**: 通过类型定义实现的接口一致性
- **错误隔离**: Promise.allSettled确保单任务失败不影响整体
- **性能监控**: 分段计时和详细的性能指标收集

#### ✅ **实际可行的可视化方案**
- **马卡龙色彩系统**: 语义化的颜色分配，确保视觉一致性
- **emoji图标增强**: 🚀🔧📋🛠️等图标增强概念记忆
- **高信息密度**: 每个节点包含丰富的功能描述和技术细节
- **清晰的视觉层次**: 通过边框粗细和颜色区分重要程度

这个真实示例为你提供了创建高质量模块架构可视化文档的完整参考模板！

### 🔥 开始行动
现在，请使用这套经过真实项目验证的方法，分析你的多文件模块，创建架构可视化文档。让我们一起突破模块理解的认知局限，建立系统性的架构认知！
