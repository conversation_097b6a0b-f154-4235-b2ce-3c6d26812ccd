### **"前端服务层架构说明书"生成提示词 (V1.0 - 双层服务架构版)**

#### **🎯 核心角色与使命**

你将扮演"**前端Adapter能力接口文档官 (Frontend Adapter Capability Interface Officer)**"。你的唯一使命是，从项目代码中，为**业务服务层开发者**整理并生成一份**稳定、极简、且完全自包含**的前端Adapter层能力接口规范。这份文档将成为业务服务层进行所有开发工作的、唯一的"服务层架构说明书"。

#### **📝 标准提示词模板**

请使用 Context Engine 和 Readfiles 扫描项目代码的所有前端Adapter层能力，创建一份完整的、面向业务服务层的接口规范（Service Layer Architecture Instructions）。

📋 核心内容清单 (Core Content Checklist)，对于每一个扫描捕捉到的前端Adapter能力，使用以下内容整理格式：

```markdown
## 1. Adapter能力接口总览表 (Adapter Capability Interface Overview Table)
- **职责**: 在文档的最开始，创建一个Markdown表格，作为整个前端Adapter层能力的"执行摘要（Executive Summary）"。
- **分表**：将总表分成多个小表，以功能模块划分，每个功能模块使用标题区分
- **数据来源**: 你必须扫描整个iOS项目的Services/Adapters目录，汇总出所有面向业务服务层的Adapter接口。
- **表格列定义**: 表格必须包含以下六列：`功能模块`、`Adapter类名`、`核心职责`、`主要方法`、`数据转换`、`状态`。
- **内容要求**: 每一行代表一个独立的Adapter类，信息需高度概括。
- **示例格式**:
    | 功能模块 | Adapter类名 | 核心职责 | 主要方法 | 数据转换 | 状态 |
    | :--- | :--- | :--- | :--- | :--- | :--- |
    | 身份认证 | `AuthAPIAdapter` | Apple/Google登录API转译 | login, logout, healthCheck | LoginRequest → UserSession | ✅ 已实现 |
    | 单词服务 | `WordAPIAdapter` | 单词查询和生成API转译 | getWord, getDailyWord, submitFeedback | String → WordDefinition | ✅ 已实现 |
    | 生词本管理 | `BookmarkAPIAdapter` | 生词本CRUD操作API转译 | addBookmark, removeBookmark, getBookmarks | BookmarkRequest → BookmarkList | ✅ 已实现 |
    | 用户管理 | `UserAPIAdapter` | 用户资料管理API转译 | getCurrentUser, deleteAccount | SessionID → UserProfile | ✅ 已实现 |
    | 音频服务 | `AudioAPIAdapter` | 音频状态查询API转译 | getAudioStatus | String → AudioStatus | ✅ 已实现 |

## Adapter功能模块名称
### 1. 模块概述 (Module Overview)
- 简要说明本Adapter模块所服务的核心业务目标和在双层服务架构中的定位。

### 2. 依赖注入配置 (Dependency Injection Configuration)
- **协议定义**: `[AdapterProtocol名称]`
- **实现类**: `[Adapter实现类名称]`
- **注入方式**: 构造函数注入 / 属性注入
- **生命周期**: 单例 / 瞬态

### 3. Adapter接口详解 (Detailed Adapter Interfaces)
- 逐一列出本模块所有可供业务服务层调用的方法接口。
- **对于每个方法，必须包含以下信息**：
    - **职责 (Responsibility)**: 一句话描述这个方法的作用。
    - **方法签名 (Method Signature)**: 完整的Swift方法签名，包含参数类型和返回类型。
    - **输入参数 (Input Parameters)**: 详细说明每个参数的含义和约束。
    - **返回值 (Return Value)**: 说明返回的数据结构和可能的异常。
    - **使用示例 (Usage Example)**: 提供一个可以直接使用的Swift代码调用示例。
    - **错误处理 (Error Handling)**: 列出可能抛出的异常类型和处理建议。

### 4. 核心数据契约 (DTO 定义) (Core Data Contracts)
- 将本Adapter模块所有方法接口中，涉及到的**所有请求和响应的数据结构（DTOs）**，以 `Swift` 结构体的形式完整、清晰地列出。
- **分类说明**:
    - **API DTO**: 与后端API完全一致的数据结构
    - **Business DTO**: 针对业务逻辑优化的数据结构（如果存在转换）
    - **共享模型**: 引用SharedModels包中的通用数据结构

### 5. 测试与调试 (Testing & Debugging)
- **Mock数据 (Mock Data)**: 提供可用于业务服务层单元测试的Mock数据示例。
- **常见错误场景 (Common Error Scenarios)**: 以表格形式，列出本Adapter可能遇到的关键错误场景及其处理方式，指导业务服务层进行恰当的错误处理和重试逻辑。
- **性能考量 (Performance Considerations)**: 说明调用频率限制、缓存策略建议等。

### 6. 架构集成指南 (Architecture Integration Guide)
- **与业务服务层的集成**: 说明如何在Business Service中正确使用此Adapter。
- **错误传播**: 说明错误如何从Adapter层传播到业务服务层。
- **日志记录**: 说明Adapter层的日志记录策略和调试信息。
```

### 📂 文档创建位置
/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/03-前端能力/04-APIAdapter层组件.md

### 🎯 特殊要求

#### **双层服务架构对齐**
- 确保文档完全符合双层服务架构设计原则
- 明确区分Adapter层（纯转译）和Business Service层（业务逻辑）的职责边界
- 强调Adapter层的无状态、无业务逻辑特性

#### **代码实际状态反映**
- 基于实际已完成的7个Adapter文件生成文档
- 反映DailyWord和Feedback功能合并到WordAPIAdapter的实际设计
- 确保所有方法签名、参数类型与实际代码100%一致

#### **面向业务服务层开发**
- 文档的目标读者是Business Service层的开发者
- 重点说明如何正确调用Adapter方法
- 提供充分的错误处理和重试策略指导

请基于当前项目的实际Adapter实现情况，生成一份**仅包含业务服务层开发者绝对必要信息**的、实用、准确、完整的Adapter接口规范。
