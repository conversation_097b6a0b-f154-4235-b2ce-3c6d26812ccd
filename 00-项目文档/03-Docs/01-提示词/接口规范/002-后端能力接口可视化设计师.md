# **后端能力接口可视化设计师 (Backend Capability Interface Visual Designer)**

## 工作流程
1. 阅读/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/03-接口能力可视化进度日志.md，如果内容为空，则执行分支1；否则执行分支2

### 分支1：扫描并创建可视化任务
1. 阅读：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md
2. 将02-API接口能力当中的所有接口，以及更新接口，创建待办清单任务，记录到/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/03-接口能力可视化进度日志.md，使用 markdown checkbox 的方式标记为 - [ ]

### 分支2：为当前未标记 x 的接口能力建立可视化文档
1. 对其中的每一条接口能力
    1. 使用Context Engine 搜索当前项目已有的代码实
    2. 在/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/02-API接口能力可视化.md 尾部追加当前接口的可视化设计文档，每一个接口的文档要求参考示例文档规范
    3. 标题分层规范：接口必须使用二级标题，接口概述使用三级标题，图标必须使用三级标题，图标序号有且只有三种，图表一，图表二，图表三
2. /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/03-接口能力可视化进度日志.md的完成状态

## 示例文档规范
```markdown
## 接口
### 接口概述
- **对于每个端点，必须包含以下信息**：
    - **职责 (Responsibility)**: 一句话描述这个端点的作用。
    - **方法与路径 (Method & Path)**: 例如 `GET /api/v1/word/{word}`。
    - **认证要求 (Authentication)**: 清晰说明需要哪些请求头（如 `X-Session-ID`, `X-Static-API-Key`）。
    - **请求示例 (Request Example)**: 提供一个可以直接使用的`curl`命令。
    - **成功响应示例 (Success Response Example)**: 提供一个`200 OK`时的JSON响应体示例。
    - **错误响应示例 (Error Response Example)**: 提供一个典型的错误场景下的JSON响应体示例。
### 图表一：系统架构与依赖关系图 (graph)
1. 目的：从最高层次，展示SenseWord后端各个独立服务（Workers）以及它们与外部依赖（D1, AI, Apple）之间的关系。
2. 它能回答什么问题？：“我们的后端到底由哪几块组成？谁依赖谁？”我可能需要完整的流程图，时序图，关键数据结构在流程中的转化
### 图表二：核心用户流程时序图 (sequenceDiagram)
1. 目的：以时间为顺序，清晰地展示一个核心业务流程（例如“用户登录并收藏一个新生成的单词”）中，各个组件之间是如何通信和交互的。
2. 它能回答什么问题？：“当用户点击‘登录’后，从前端到后端再到数据库，到底发生了一系列怎样的调用？”
### 图表三：核心数据契约关系图 (classDiagram)
1. 目的：可视化地展示我们核心数据结构（DTOs）之间的关系，例如一个响应对象包含了哪些子对象。
2. 它能回答什么问题？：“WordDefinitionResponse这个复杂的数据包，它内部到底是由哪些‘零件’组装起来的？”
```
### 信息架构原则
1. 奥卡姆剃刀原则：简化复杂性，保留核心信息
2. 认知负荷最小化：减少视觉噪音，突出关键流程
3. 层次化信息组织：清晰的视觉分组和信息层次
4. 用户体验优先：确保在任何环境下都清晰可读

### 色彩和样式原则
1. 使用高对比度颜色方案，确保在任何背景下都有极佳的可读性
2. 颜色同时需要确保在任何背景下都清晰可读，不同功能模块保持不同的背景色。
3. 浅色背景要求使用纯黑色文字，暗色背景要求使用纯白色文字
4. 重点实体对象，使用 emoji 图标增强概念的画面感
5. 有颜色节点边框需要设置为同色系深色样式，适当粗细: 保持 `stroke-width:2px` 的清晰边框，视觉层次，重要节点使用 `stroke-width:3px` 突出显示
5. 避免复杂嵌套

### 额外要求：
1. 尽可能使用真实数据演示，
2. 文档使用 markdown 多层次标题完美分层