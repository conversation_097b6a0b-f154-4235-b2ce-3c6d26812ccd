会话记录过长影响了编码质量，目前你需要移交当前工作任务给新的会话。 

请创建工作移交文档，请在一个四重反引号代码块当中记录
上下文压缩SOP - 9维度详细指导

  1. Primary Request and Intent（主要请求和意图）

  注意事项：
  - 区分显式请求（用户明确说出的）和隐式意图（用户真正想要的）
  - 识别请求的优先级和紧急程度
  - 捕捉用户的情绪和期望

  流程SOP：
  1. 扫描所有用户消息，提取动词（如"实现"、"修复"、"分析"）
  2. 识别目标对象（如"用户登录系统"、"数据库连接"）
  3. 判断请求类型：新功能开发/Bug修复/代码重构/问题咨询
  4. 评估复杂度：简单/中等/复杂
  5. 输出格式：用户[动作]了[对象]，意图是[目标]，当前状态[进展]

  2. Key Technical Concepts（关键技术概念）

  注意事项：
  - 只记录与当前任务相关的技术栈
  - 区分已使用的技术和计划使用的技术
  - 保留版本号和配置信息

  流程SOP：
  1. 提取编程语言、框架、库名称
  2. 识别架构模式（如MVC、微服务等）
  3. 记录数据库、API、协议类型
  4. 收集配置文件、环境变量信息
  5. 分类：核心技术栈/辅助工具/开发环境
  6. 输出格式：技术栈列表+简要说明其在项目中的作用

  3. Files and Code Sections（文件和代码部分）

  注意事项：
  - 记录完整的文件路径
  - 保留行号信息便于快速定位
  - 区分已修改和仅查看的文件

  流程SOP：
  1. 列出所有Read/Edit/Write操作的文件
  2. 记录关键函数/类的名称和位置
  3. 标记修改类型：新增/修改/删除
  4. 保留重要的代码片段（函数签名、关键逻辑）
  5. 输出格式：
     - 已修改：filepath:line_number - 修改内容摘要
     - 已查看：filepath:line_number - 查看目的

  4. Errors and Fixes（错误和修复）

  注意事项：
  - 记录完整的错误信息，包括错误代码
  - 记录修复过程的关键步骤
  - 保留未解决的错误供后续处理

  流程SOP：
  1. 收集所有错误信息：编译错误/运行时错误/逻辑错误
  2. 分类错误类型：语法/类型/逻辑/环境/依赖
  3. 记录诊断过程：如何发现问题根因
  4. 记录修复方案：具体改动+验证方法
  5. 输出格式：
     - 错误：[错误类型] 文件位置 - 错误描述
     - 修复：解决方案 - 验证结果

  5. Problem Solving（问题解决过程）

  注意事项：
  - 记录思考过程，不只是结果
  - 保留替代方案的考虑
  - 记录决策依据

  流程SOP：
  1. 识别问题分解策略：如何将复杂问题拆解
  2. 记录调研过程：搜索了什么/咨询了谁
  3. 记录方案比较：考虑了哪些选择/为什么选择当前方案
  4. 记录实施步骤：先做什么/后做什么/为什么这样安排
  5. 输出格式：问题→分析→方案→实施→结果

  6. All User Messages（所有用户消息）

  注意事项：
  - 完整保留用户的原始表达
  - 保留时间顺序
  - 识别消息之间的关联性

  流程SOP：
  1. 按时间顺序列出所有用户输入
  2. 保留原始语言和格式
  3. 标记消息类型：指令/问题/反馈/澄清
  4. 识别消息间的依赖关系
  5. 输出格式：时间戳/消息类型/原始内容/上下文关系

  7. Pending Tasks（待完成任务）

  注意事项：
  - 区分已承诺的任务和可能的后续工作
  - 记录任务的优先级和依赖关系
  - 保留任务的验收标准

  流程SOP：
  1. 从用户请求中提取未完成的具体任务
  2. 从实施过程中识别发现的新任务
  3. 评估任务优先级：高/中/低
  4. 识别任务依赖：哪些任务需要先完成
  5. 输出格式：
     - 任务描述 - 优先级 - 依赖关系 - 预计工作量

  8. Current Work（当前工作状态）

  注意事项：
  - 记录当前正在进行的具体工作
  - 保留工作的完成程度
  - 记录下一步具体行动

  流程SOP：
  1. 识别当前活跃的工作项目
  2. 评估完成进度：0%-25%-50%-75%-90%-100%
  3. 记录最后一次操作的具体内容
  4. 识别当前阻塞点或等待的输入
  5. 输出格式：工作项目 - 完成度 - 当前状态 - 下一步动作

  9. Optional Next Step（可选的下一步）

  注意事项：
  - 提供多个选择而不是单一建议
  - 考虑用户的技能水平和时间限制
  - 区分必须做的和可以做的

  流程SOP：
  1. 基于当前进展评估逻辑后续步骤
  2. 考虑用户可能的不同需求方向
  3. 评估每个选项的优先级和复杂度
  4. 提供简洁的行动指导
  5. 输出格式：
     - 推荐：[具体行动] - 原因 - 预期结果
     - 备选：[其他选择] - 适用场景

  总体压缩原则

  1. 信息完整性：宁可多记录也不要遗漏关键信息
  2. 结构化：使用统一的格式便于后续处理
  3. 可操作性：摘要应该能够指导后续的具体行动
  4. 上下文保持：确保AI能够无缝继续之前的工作

现在请分析整个对话历史，生成一个全面的结构化摘要，包括以下9个维度：

  1. Primary Request and Intent: 用户的主要请求和意图
  2. Key Technical Concepts: 讨论的关键技术概念
  3. Files and Code Sections: 涉及的文件和代码部分
  4. Errors and fixes: 遇到的错误和修复方案
  5. Problem Solving: 解决问题的过程
  6. All user messages: 所有用户消息的完整记录
  7. Pending Tasks: 待完成的任务
  8. Current Work: 当前正在进行的工作
  9. Optional Next Step: 可选的下一步行动

  对于每个维度，即使没有相关内容也要明确说明"无"或"未涉及"。确保摘要完整、准确，便于恢复对话上下文。

