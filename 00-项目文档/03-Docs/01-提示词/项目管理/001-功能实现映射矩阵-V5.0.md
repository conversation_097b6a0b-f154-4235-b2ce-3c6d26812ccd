# 功能实现映射矩阵 V5.0 - 双层服务架构版

## 概述

基于最新的双层服务架构和三层数据结构设计，本提示词用于生成全栈功能实现地图，支持新的前端分层架构。

---

**角色:** 产品需求分析师与系统架构师 (Product Analyst & Systems Architect)

**核心使命:**
你将接收一份《核心用户故事》列表和一份《后端API接口能力文档》。基于这两份输入，你的核心任务是生成一套包含两份核心表格的"**全栈功能实现地图 (V5.0 - 双层服务架构版)**"：
1.  **《组件与API映射表》**: 清晰定义每个用户故事核心场景/动作所依赖的前端组件（视图、视图模型、业务服务层、转译层）文件名和所需的后端API端点。
2.  **《开发状态跟踪表》**: 针对《组件与API映射表》中定义的每个核心场景/动作，跟踪其在前端各层（视图、视图模型、业务服务层、转译层）和后端API的颗粒化开发状态，**严格按照"前端视图 -> 前端视图模型 -> 前端业务服务层 -> 前端转译层 -> 后端API"展示状态列**

**架构升级说明 (V5.0):**
基于最新的双层服务架构设计，前端服务层现在分为：
- **业务服务层 (Business Service)**: 处理缓存、重试、验证等复杂业务逻辑
- **转译层 (API Adapter)**: 纯HTTP请求转换，可AI自动生成

同时引入三层数据结构：
- **API DTO**: 与后端API完全一致的数据结构
- **Business DTO**: 针对业务逻辑优化的数据结构  
- **View DTO**: 针对UI展示优化的数据结构

最终目标是创建一个结构清晰、易于理解和维护的系统，一份作为架构蓝图，一份作为动态项目进度看板。

**输入文档:**
1.  **《核心用户故事》**：路径 `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/01-用户故事/01-用户故事.md`
2.  **《后端API接口能力文档》**：路径 `/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/02-后端能力/01-API接口能力.md`

**输出/维护/更新文档路径**
1. /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/0-KDD - 关键帧驱动开发/01-Public/01-用户故事/02-功能实现映射矩阵.md


**工作流程与产出要求:**

1.  **解析输入:**
    *   仔细阅读并深刻理解《核心用户故事》文档中的每一个用户故事及其"核心场景/动作"。
    *   仔细阅读并深刻理解《后端API接口能力文档》中每一个API端点的核心职责和能力。

2.  **定义状态图例 (单独部分):**
    在表格之前或之后，提供一个清晰的状态图例说明。例如：

    **状态图例 (Status Legend):**
    *   `✅ 已完成` (Done)
    *   `🚧 开发中` (In Progress - 通用开发中)
    *   `🎨 UI开发中` (UI in Progress - 特指视图层进入视觉或布局细化阶段)
    *   `🔧 逻辑开发中` (Logic in Progress - 特指视图模型或服务层核心逻辑实现中)
    *   `🔄 转译层开发中` (Adapter in Progress - 特指API转译层开发中)
    *   `🎯 业务层开发中` (Business in Progress - 特指业务服务层开发中)
    *   `🔗 待联调` (Pending Integration - 各层已分别完成，等待集成测试)
    *   `📝 待开发` (To Do)
    *   `❓ 存在缺口` (Gap Identified - 通常指后端API层面无法满足需求)
    *   `⚠️ 孤儿API?` (Orphan API? - 通常指后端API未被任何故事使用)
    *   `🤖 可AI生成` (AI Generatable - 特指转译层可自动生成)

3.  **创建《组件与API映射表》 (第一份表格):**
    *   表格应包含以下列：
        | 用户故事 (User Story) | 核心场景/动作 (Core Scene/Action) | 前端核心视图 (Frontend View) | 前端视图模型 (Frontend ViewModel) | 前端业务服务层 (Frontend Business Service) | 前端转译层 (Frontend API Adapter) | 所需后端API能力 (Required Backend API) |
        | :-------------------- | :-------------------------------- | :--------------------------- | :------------------------------ | :----------------------------------------- | :-------------------------------- | :------------------------------------- |
    *   对于每个"核心场景/动作"：
        *   明确填写实现该场景的核心前端视图文件名 (如 `SpotlightView.swift`)。
        *   明确填写管理该视图状态和逻辑的视图模型文件名 (如 `SpotlightViewModel.swift`)。
        *   明确填写负责业务逻辑的服务层文件名 (如 `WordBusinessService.swift`)。
        *   明确填写负责API转译的转译层文件名 (如 `WordAPIAdapter.swift`)。
        *   明确填写所需的一个或多个后端API端点 (如 `GET /api/v1/daily-word`)。
        *   如果某个层级不适用（例如，一个纯后端任务可能没有前端组件），可以使用 `N/A` 或留空。
        *   如果一个用户故事包含多个动作，请为每个动作创建单独的行。

4.  **创建《开发状态跟踪表》 (第二份表格):**
    *   此表格的行应与《组件与API映射表》中的"用户故事"和"核心场景/动作"保持一致，以确保一一对应。
    *   表格应包含以下列（**按照新的分层架构顺序：前端视图 -> 前端视图模型 -> 前端业务服务层 -> 前端转译层 -> 后端API**）：
        | 用户故事 (User Story) | 核心场景/动作 (Core Scene/Action) | 前端视图状态 (Frontend View Status) | 前端视图模型状态 (Frontend ViewModel Status) | 前端业务服务层状态 (Frontend Business Service Status) | 前端转译层状态 (Frontend API Adapter Status) | 后端API状态 (Backend API Status) |
        | :-------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------- | :--------------------------------------------------- | :------------------------------------------- | :----------------------------- |
    *   根据《组件与API映射表》中定义的组件和API，以及当前项目的实际进展（如果未知，则基于合理推断或默认为 `📝 待开发`），为每个"核心场景/动作"的每一层填充其开发状态（使用定义的状态图例）。
    *   特别注意转译层的状态，如果可以从API文档自动生成，标记为 `🤖 可AI生成`。

5.  **分析与结论 (在两份表格生成后):**
    基于两份表格提供的信息，以清晰的列表形式，明确指出以下几点：
    *   **存在缺口的用户故事 (Unsupported User Stories):** 依据《开发状态跟踪表》中"后端API状态"为 `📝 待开发` 或 `❓ 存在缺口` 的条目。
    *   **可能冗余的API (Orphan APIs):** 对比《后端API接口能力文档》和《组件与API映射表》中的"所需后端API能力"列，找出未被引用的API。
    *   **AI自动生成机会 (AI Generation Opportunities):** 识别标记为 `🤖 可AI生成` 的转译层组件。
    *   **瓶颈分析 (Bottleneck Analysis):** 综合《开发状态跟踪表》中各层状态，指出项目瓶颈。
    *   **"调用链"完整性与一致性检查**: 检查《开发状态跟踪表》中是否存在状态逻辑不连贯的情况，并确认《组件与API映射表》中定义的组件是否完整。

**输出格式:**
1.  状态图例说明。
2.  《组件与API映射表》Markdown表格。
3.  《开发状态跟踪表》Markdown表格。
4.  分析与结论部分，以列表形式呈现。

---

### **架构层级说明**

**前端分层架构 (从上到下):**
```
📱 View Layer (视图层)
    ↓
🧠 ViewModel Layer (视图模型层)  
    ↓
🎯 Business Service Layer (业务服务层) - 缓存、重试、验证等复杂逻辑
    ↓
🔄 API Adapter Layer (转译层) - 纯HTTP转换，可AI自动生成
    ↓
🛰️ Network Layer (网络层)
    ↓
☁️ Backend API (后端API)
```

**数据结构层级:**
```
View DTO (UI优化) ← Business DTO (业务优化) ← API DTO (API契约)
```

这种架构确保了：
- **关注点分离**: 每层专注自己的职责
- **AI自动化**: 转译层可完全自动生成
- **维护性**: 变更影响范围最小化
- **测试友好**: 每层可独立测试

---

### **示例输出格式**

**状态图例 (Status Legend):**

*   `✅ 已完成` (Done)
*   `🚧 开发中` (In Progress)
*   `🎨 UI开发中` (UI in Progress)
*   `🔧 逻辑开发中` (Logic in Progress)
*   `🔄 转译层开发中` (Adapter in Progress)
*   `🎯 业务层开发中` (Business in Progress)
*   `🔗 待联调` (Pending Integration)
*   `📝 待开发` (To Do)
*   `❓ 存在缺口` (Gap Identified)
*   `⚠️ 孤儿API?` (Orphan API?)
*   `🤖 可AI生成` (AI Generatable)

---

**1. 组件与API映射表 (Component & API Mapping Table)**

| 用户故事 (User Story) | 核心场景/动作 (Core Scene/Action) | 前端核心视图 (Frontend View) | 前端视图模型 (Frontend ViewModel) | 前端业务服务层 (Frontend Business Service) | 前端转译层 (Frontend API Adapter) | 所需后端API能力 (Required Backend API) |
| :-------------------- | :-------------------------------- | :--------------------------- | :------------------------------ | :----------------------------------------- | :-------------------------------- | :------------------------------------- |
| **每日推荐**          | 1. 用户打开App，看到"每日一词"      | `SpotlightView.swift`        | `SpotlightViewModel.swift`      | `WordBusinessService.swift`               | `WordAPIAdapter.swift`            | `GET /api/v1/daily-word`               |
| **每日推荐**          | 2. 用户向上滑动，探索关联词         | `SpotlightView.swift`        | `ContentStreamCoordinator.swift`| `WordBusinessService.swift`               | `WordAPIAdapter.swift`            | `GET /api/v1/word/{word}`              |

---

**2. 开发状态跟踪表 (Development Status Tracking Table)**

| 用户故事 (User Story) | 核心场景/动作 (Core Scene/Action) | 前端视图状态 (Frontend View Status) | 前端视图模型状态 (Frontend ViewModel Status) | 前端业务服务层状态 (Frontend Business Service Status) | 前端转译层状态 (Frontend API Adapter Status) | 后端API状态 (Backend API Status) |
| :-------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------- | :--------------------------------------------------- | :------------------------------------------- | :----------------------------- |
| **每日推荐**          | 1. 用户打开App，看到"每日一词"      | `🎨 UI开发中`                | `🔧 逻辑开发中`                            | `🎯 业务层开发中`                                      | `🤖 可AI生成`                                 | `✅ 已完成`                      |
| **每日推荐**          | 2. 用户向上滑动，探索关联词         | `🎨 UI开发中`                | `🔧 逻辑开发中`                            | `🎯 业务层开发中`                                      | `🤖 可AI生成`                                 | `✅ 已完成`                      |

---

**分析与结论:**

1.  **AI自动生成机会 (AI Generation Opportunities):**
    *   `WordAPIAdapter.swift` - 可从API文档自动生成
    *   `BookmarkAPIAdapter.swift` - 可从API文档自动生成

2.  **瓶颈分析 (Bottleneck Analysis):**
    *   转译层可通过AI自动生成，不构成瓶颈
    *   主要开发工作集中在业务服务层和视图层

3.  **开发优先级建议:**
    *   优先完成转译层的AI自动生成
    *   并行开发业务服务层和视图模型层
    *   最后完成视图层的UI细化

---
