# 任务类型

根据用户需求，在 `/map` 指令模式下生成**完全嵌入式、端到端的业务流程架构设计方案**（包含层次化结构）。该文档基于用户需求和现有代码基础，设计完整的系统组件交互、关键业务步骤、以及预期的函数调用流程（含签名和从项目根目录开始的**完整相对路径**）或API接口设计，帮助用户规划代码的跨层架构关系和动态执行流程，并支持基于架构设计的开发实施方法。

**重要**: 生成的架构设计地图文档必须在每个函数调用、业务步骤和系统组件前添加 markdown todolist 格式的复选框 `- [ ]`，以支持用户进行开发进度跟踪和任务管理。

# 任务背景与目的

在现代软件开发中，从用户需求到可执行代码的转化过程往往充满挑战。传统的开发方式通常是先有需求，然后进行高层设计，再逐步细化到具体实现。然而，这种方式容易导致架构设计与实际实现之间的脱节，以及在开发过程中频繁的返工和重构。

我们提出的**架构设计地图**方法，旨在建立一个从用户需求直接映射到具体代码实现的桥梁。通过**端到端的业务流程设计**和**精确的函数契约规划**，我们可以在编码之前就明确整个系统的架构蓝图，包括数据流转、组件交互、接口设计等关键要素。

这种方法的核心价值在于：
1. **需求驱动设计**：基于用户的实际业务需求，设计出完整的端到端解决方案
2. **架构先行**：在编码之前就明确系统的整体架构和组件关系
3. **实现可预测**：通过详细的函数契约和调用关系设计，使代码实现变得可预测和可验证
4. **复杂度管理**：通过层次化的设计文档，将复杂的系统分解为可管理的模块和接口

我们进行架构设计地图生成（即生成 `/map` 文档）的目的，正是要构建**基于用户需求的完整业务流程实施方案**，包括明确的业务步骤、组件交互设计、函数签名规划及其预期的调用与嵌套关系。通过将这些设计要素以**严格遵循预期执行流的层次化列表**进行组织，从而为开发团队提供清晰的实施路线图，确保最终的代码实现能够准确满足用户需求。

`/map` 模式旨在生成一份结构化的**端到端业务流程架构设计文档**，基于用户需求和现有代码基础进行设计规划。这份文档的核心特征是：

1. **认知负荷友好**：通过聚焦高层业务步骤、组件交互以及关键的函数/API接口设计（而非完整实现），并以自然的设计流程组织，显著降低理解复杂业务流程的认知成本。
2. **黑盒抽象 (针对函数/模块)**：当涉及到具体的函数调用设计时，文档的阅读建立在"模块功能明确，接口契约清晰"的前提上。每个函数签名代表一个"黑盒"，其内部实现细节在架构设计层面被抽象掉。
3. **复杂度重置的体现**：在设计A调用B，B调用C时，由于B和C被视为功能明确的模块，其内部复杂性不干扰对当前层级交互的设计理解。文档的嵌套结构直观反映了这种"逐层深入，随时抽象"的设计过程。
4. **完全端到端与跨层流程设计**：文档必须设计实现特定业务功能的**完整端到端流程或其关键部分**，**包括所有重要的成功与错误处理分支设计**。清晰展示逻辑如何在不同技术层（如前端用户界面、前端业务逻辑、后端API、服务层、数据访问层、数据网关、底层数据库操作等）之间流动和交互，从用户行为触发到数据最终处理或结果返回的全过程设计。**所有涉及的代码设计，无论属于哪个文件或模块，都应在流程的相应阶段和分支中，根据其预期执行顺序和调用关系呈现。**
5. **精确调用驱动的拓扑关系设计**：通过层次化的 Markdown 列表，清晰、直观地展示业务流程的阶段划分、以及**函数间预期的、直接的调用关系（体现为精确的嵌套深度）**。**一个文件中的不同代码片段会根据其在流程中的预期调用位置，分散出现在文档的不同阶段和分支，而不是被集中呈现。**
6. **接口契约与步骤描述的结合**：文档内容围绕**高级业务步骤**展开，并在合适的粒度下通过**函数签名设计**或**API调用设计**来精确指代实现这些步骤的关键技术接驳点。
7. **组成明确与结构严谨**: 文档由一系列层级化的列表项构成。列表项可以是：
    - a. **组件/层次描述**：标识流程所涉及的主要系统组件、技术层次或高级阶段（例如："- **前端交互与准备**"，"- **后端服务处理 (Unified Service)**"）。
    - b. **业务/逻辑步骤描述**：对在某个组件/层次内发生的具体业务动作或逻辑步骤的文字概括（例如："- 用户点击登录按钮提交表单"，"- 校验请求体"）。
    - c. **函数调用/API交互表述**：在具体的业务步骤下，列出执行该步骤的关键函数调用设计（**优先展示其签名，并包含从项目根目录开始的完整相对文件路径**）、API端点调用、或重要的框架/库方法调用。**所有后续因该调用而产生的跨模块、跨层次的调用设计（例如Service层调用Repository层，Repository层调用ORM/Driver，或后端调用另一个微服务/Worker API），都必须作为子项嵌套在其直接调用者的下方，直至该逻辑分支的终点或达到用户期望的抽象层级。**
    - **严格不包含详细的代码片段或函数内部的实现细节。**

# 任务指令

## 核心指令与工作流 (Core Command & Workflow)

### 0. 项目上下文搜索 (Project Context Search) - **必须优先执行**

**CRITICAL**: 在创建任何函数契约补间链文档之前，必须首先进行全面的项目上下文搜索和分析。这是确保文档技术可行性和架构兼容性的关键步骤。

#### 强制执行步骤：
1. **代码库搜索**: 使用 `search_codebase` 工具搜索相关的现有实现
   - 搜索目标功能的现有代码实现
   - 搜索相关的数据结构定义
   - 搜索API集成和服务调用
   - 搜索队列处理和任务管理逻辑

2. **文件结构分析**: 使用 `list_dir` 工具了解项目架构
   - 查看相关目录的文件组织结构
   - 识别现有的服务层和工具函数
   - 了解配置文件和依赖管理

3. **关键文件检查**: 使用 `view_files` 工具深入分析核心文件
   - 检查现有的类型定义文件
   - 分析相关的服务实现
   - 验证数据库模式和API接口

4. **技术可行性验证**: 基于搜索结果评估
   - 确认现有代码与计划架构的兼容性
   - 识别需要新增的功能模块
   - 评估技术选型的一致性
   - 发现潜在的架构冲突或优化点

#### 输出要求：
- 提供详细的现有代码分析报告
- 明确项目的技术栈和架构设计
- 识别需要新增或扩展的功能
- 确认数据结构的兼容性
- 给出技术可行性结论

**注意**: 只有完成项目上下文搜索并确认技术可行性后，才能继续进行后续的文档创建工作。

### 任务指令 (Task Instructions)

你的任务是根据用户提供的需求描述，创建一份详尽的架构设计地图文档。这份文档应该：

1. **端到端流程覆盖**: 从用户交互的第一个触点开始，到最终结果返回给用户为止
2. **完全嵌入式描述**: 每个步骤都要详细说明其内部实现逻辑和调用关系
3. **严格按照代码实际调用流程**: 文档结构必须反映真实的代码执行路径和层级关系
4. **跨层级流程捕捉**: 涵盖前端UI、API层、服务层、数据层等所有相关层级
5. **接口契约明确**: 每个函数调用都要明确输入输出的数据结构
6. **异常处理完整**: 包含错误处理、重试机制、降级策略等异常流程

### 详细工作流程

1. **需求背景与目标分析 (Requirement Background & Objective Analysis)**
   - AI职责：在开始技术设计前，必须先深入理解和明确阐述需求的业务背景、技术驱动因素和预期目标。基于EARS（Easy Approach to Requirements Syntax）方法，将业务需求转化为结构化、可验证的需求表述。
   - 具体要求：
       1. **问题现状分析（使用EARS异常行为需求模式）**：
          - 使用"如果...，那么..."模式识别当前系统的异常状况和问题
          - 描述系统在特定条件下的不良表现或缺陷
          - 明确问题对业务或用户体验的具体影响
       2. **重构目标制定（使用EARS通用性需求模式）**：
          - 使用"系统应该..."模式定义明确的、可量化的技术目标
          - 将业务需求转化为可测试和验证的系统需求
          - 确保目标具备完整性、一致性且无冲突
       3. **技术决策说明（使用EARS状态驱动和事件驱动需求模式）**：
          - 使用"在...情况下，系统应..."模式说明特定状态下的技术选型
          - 使用"当...时，系统应..."模式解释关键事件触发的架构决策
          - 阐述技术选型的权衡考虑和优势
       4. **收益预期评估（使用EARS可选功能需求模式）**：
          - 使用"在...条件下，系统应该..."模式描述预期收益的实现条件
          - 量化和定性分析重构带来的直接和间接价值
          - 定义收益的可测量指标和验证方法
   - 输出要求：在架构设计文档的顶部，以"需求背景和方案分析"章节形式呈现，所有需求表述必须遵循EARS句法规范

   **EARS句法模式应用指南**：

   1. **通用性需求模式**: `系统应该[具体行为/功能]`
      - 适用于：系统在所有运行状态下都需要满足的基本功能需求
      - 示例：认证系统应该在2秒内完成用户身份验证

   2. **状态驱动需求模式**: `在[特定状态/条件]情况下，系统应[具体行为]`
      - 适用于：在特定系统状态或业务条件下才生效的需求
      - 示例：在用户处于登录状态的情况下，系统应保持会话有效性检查

   3. **事件驱动需求模式**: `当[特定事件]时，系统应[响应行为]`
      - 适用于：由特定事件触发的系统响应和处理逻辑
      - 示例：当用户点击登录按钮时，系统应验证用户凭据并创建会话

   4. **可选功能需求模式**: `在[可选特性启用]条件下，系统应该[相应功能]`
      - 适用于：依赖于可选配置或特性的功能需求
      - 示例：在启用双因素认证的条件下，系统应该要求用户提供额外验证码

   5. **异常行为需求模式**: `如果[异常条件]，那么系统应[处理方式]`
      - 适用于：错误处理、异常情况和系统恢复机制
      - 示例：如果用户连续3次登录失败，那么系统应锁定账户15分钟

   6. **复杂需求模式**: `在[前置条件]情况下，当[触发事件]时，系统应[响应行为]`
      - 适用于：需要同时满足多个条件的复杂业务逻辑
      - 示例：在用户已登录的情况下，当检测到异常IP访问时，系统应要求重新验证身份
2. **上下文感知与依赖审计 (Context Awareness & Dependency Audit)**
   - AI职责：在你开始设计任何新的`架构设计地图`之前，你的首要任务是进行一次全面的"上下文感知与依赖审计"。
   - 具体行动:
       1. 使用 Context Engine、readfile 识别相关既有组件: 基于`[需求描述]`，分析和检索项目上下文中所有相关的既有函数、服务、和数据结构（DTOs）。
       2. 评估影响与重用性: 判断这些既有组件：
           - 是否可以被直接重用？
           - 是否需要被修改或重构以满足新需求？
           - 是否会被新功能彻底替代？
3. **项目文件结构概览 (Project File Structure Overview)**:
   - AI 职责：在进入具体实现规划前，以树状图形式清晰展示为完成此补件链需要新建或修改的文件及其在项目中的完整相对路径。
   - 使用注释或标记（如 `[新增]`、`[修改]、[已实现]`）来说明每个文件的变更状态。
   - 此结构图为后续所有 `planned_commit` 中提到的文件路径提供一个集中的、可视化的上下文。
4. **建议的特性分支名称**: `feature/auth/email-registration-login` 和工作区文件夹命名
   1. 建议的 git worktree 文件路径：/path/to/your/new/worktree-directory（请参考根目录同层工作区，为用户推荐文件命名，不使用该模板占位内容）
   2. 基础分支: `dev`
   3. 为用户提供分支创建模拟命令行语句参考
5. Commit 规划概要 
   - 核心要求： 在详细列表开始前，以一个Markdown To-do List (- [ ]) 的形式，聚合展示所有`planned_commit`的完整Commit消息。
   - **每一个提交概要必须输出为中文**。这为此份开发计划提供了一个高层次的、可动态追踪状态的执行路线图。
6. **作为"首席系统架构师"**，协助人类架构师，将高层次的业务需求，"编译"为一份详尽的、包含完整数据结构定义和业务逻辑地图的架构设计文档。
   1. 数据结构定义: 
       1. 首先定义所有业务流程中涉及的核心数据结构，使用TypeScript类型系统。
       2. 为每个数据字段添加详细的注释说明和数据来源标识。
       3. 将数据结构按照业务领域进行分组组织，类似于后端统一类型文件的组织形式。
       4. **CRITICAL**: 所有数据结构定义必须遵循以下标准：
         1. **字段注释**：简要说明字段的功能和用途
         2. **来源标识**：明确标识字段的真实数据来源，必须精确到具体的数据源、表、字段或计算逻辑，使用以下标准标记：
            - `// 用户提供参数: [具体参数名称]` - 直接来自用户输入或前端传递的参数，需指明具体参数名
            - `// 读取数据库: [数据库名].[表名].[字段名]` - 从数据库查询获取的数据，必须精确到数据库、表和字段
            - `// 组合计算: [字段A] + [字段B] + [计算逻辑]` - 通过多个数据源计算或组合生成的字段，必须列出所有参与计算的字段和计算逻辑
            - `// 系统配置: [配置文件路径].[配置项名称]` - 来自系统配置文件或环境变量，需指明具体配置文件和配置项
            - `// 外部API响应: [API端点].[响应字段路径]` - 从第三方API调用返回的数据，需指明API端点和响应字段路径
            - `// 队列系统生成: [队列名称].[消息字段]` - 由消息队列或任务队列系统生成的数据，需指明队列名称和消息字段
            - `// 时间戳生成: [生成时机].[生成方式]` - 系统自动生成的时间相关字段，需说明生成时机和方式
            - `// 默认值设置: [默认值] from [配置来源]` - 使用预设默认值的字段，需说明默认值和配置来源
            - `// 函数返回值: [函数名]([参数列表]) from [文件路径]` - 来自特定函数计算的返回值，需指明函数签名和文件路径
            - `// 中间件处理: [中间件名称] from [文件路径]` - 经过中间件处理后的数据，需指明中间件名称和文件路径
   2. 业务逻辑地图生成: 
       1. 基于用户需求，设计完整的端到端业务流程实施方案。
       2. 结合现有代码基础和新需求，规划出完整的架构设计和代码实施计划。
       3. 按照预期的代码调用嵌套结构和逻辑分支进行组织，体现设计的执行流和调用栈。
       4. 包含可能尚未实现但需要开发的功能模块和接口设计。
       5. **重要说明**：业务逻辑地图是本架构设计文档的核心输出内容，它基于用户需求和现有代码基础，**设计完整的端到端业务流程实施方案**。这是一个前瞻性的架构设计和代码实施计划，旨在为开发团队提供清晰的实施路线图，确保最终的代码实现能够准确满足用户需求。文档可能包含尚未实现但需要开发的功能模块和接口设计。
       6. 文档（端到端业务逻辑流）结构与内容要求: 生成的文档是一份层次化的 Markdown 无序列表，旨在基于用户需求**设计一个完整端到端业务流程实施方案**，包含各个系统组件、业务步骤、函数调用流（含签名和**完整相对文件路径**）及API交互的**架构设计规划**。**这份文档结合现有代码基础和新需求，按照预期的代码调用嵌套结构和逻辑分支进行组织，实现完全嵌入式的流程设计，直至每个分支的最终处理结果（如成功响应或错误返回）**。文档包含尚未实现但需要开发的功能模块，是架构设计和代码实现的的关键规划工具。请严格遵循以下结构和内容要求：
         1. **整体结构**：
             - 一个多层嵌套的 Markdown 无序列表（统一使用 `- [ ]` 作为列表标记符，包含复选框）。列表的顶层应指明正在分析的**端到端业务流程**。
             - **复选框要求**: 每个列表项都必须以 `- [ ]` 格式开始，支持用户进行开发进度跟踪和任务管理。
         2. 列表项内容：
             列表项用于共同描绘一个完全嵌入式的端到端业务流程，可以包含以下不同层面的信息，并严格按照调用流进行嵌套：
             - a. **系统组件/分层标识 (用于组织高阶结构)**：
                 - 格式: `- [ ] **组件名称**` - 使用复选框 + 加粗标题式文字标明流程当前所处的系统组件、技术分层或主要处理阶段。
             - b. **业务逻辑步骤描述 (用于阐述具体动作)**：
                 - 格式: `- [ ] 业务步骤描述` - 在组件/分层标识下，或作为独立的流程节点，使用复选框 + 简洁文字描述具体的业务行为、用户交互、或系统内部的逻辑处理步骤。
             - c. **函数签名/API调用/关键方法 (核心实现点的精确指代)**：
                 - 格式: `- [ ] functionName(param1: Type1, param2: Type2): ReturnType (from: 文件路径)` - 在最能体现具体计算、数据处理或交互的业务步骤之下，使用复选框 + 函数调用信息。
                 - **函数签名**：应清晰、准确地列出，例如 `functionName(param1: Type1, param2: Type2): ReturnType`。
                 - **API调用**：明确指出调用的目标和方式，例如 `POST /auth/login` 或 `→ 调用 Worker API: GET /user/email/:email`。在其下一层级，应详细展开该API端点的处理逻辑，包括其内部的主要函数调用和数据库操作。
                 - **文件路径/来源 (必选，完整路径)**：为函数签名或关键逻辑点注明其定义的**从项目根目录开始的完整相对文件路径**，例如 `(from: 01-Senseword-Frontend/src/services/userService.ts)`。
                 - **角色/上下文简注 (可选，非常简洁)**：仅在上述信息不足以明晰其在当前步骤的作用时，附加一句高度概括的注释。
                 - **后续流程 (关键)**：**紧跟函数调用或API调用之后，必须清晰展示由于该调用成功或失败（或其他条件判断）而引发的后续逻辑步骤或函数调用，直到该分支的终点。** 例如，如果一个函数调用失败导致返回错误，应明确指出 `→ 返回错误响应: c.json({ error: '...', code: '...' }, statusCode)`。如果成功，则继续列出下一系列调用的嵌套。
         3. **层次关系与流程表达 (核心)**：
             - 列表的顶层或较高层级代表业务流程的起点或主要的系统/逻辑分块。
             - **内层列表项必须代表其紧邻的上一级外层列表项（无论是业务步骤描述还是另一个函数/API调用）所包含的逻辑子步骤或直接调用的下一个函数/API/组件内部逻辑。缩进的深度必须直接反映业务逻辑的分解层次、真实的函数/组件调用栈深度或子步骤的从属关系。**
             - **一个函数调用如果触发了对另一个可识别函数（有签名）、API或组件内部逻辑的调用，则该被调用/触发的逻辑必须作为子项嵌套在其调用者的下方，并持续追踪，直至该逻辑分支的终点（如数据库操作、返回响应或达到用户定义的分析边界）。**
             - 列表项的**垂直顺序**应尽可能反映业务逻辑的**实际执行顺序**或主要的控制流程分支。
         4. **核心关注点与约束**：
             - **完全嵌入式与端到端追踪**：必须捕捉和展现跨越多个系统组件的完整业务流程，并将所有后续调用和处理逻辑（包括错误处理和成功路径）**直接嵌套**在其发起者的上下文中，形成一条连续的、从用户操作到系统响应，以及程序终点的完整调用链。
             - **调用驱动的结构 (Call-Driven Structure)**: 列表的结构**完全**由业务流程中的实际函数/API调用顺序和嵌套关系驱动。即使多个被调用函数来自同一文件或模块，它们也必须出现在各自调用方的上下文中，**严禁集中罗列或单独分区**。
             - **接口、交互与步骤描述优先**：文档的焦点是系统组件间的交互点、API契约、函数签名以及高级别的业务逻辑步骤。**绝对禁止包含任何实际的函数体代码、代码片段或对函数内部实现逻辑的详细描述。**
             - **基于信任的黑盒视角 (针对函数级)**：当列出函数签名时，我们共同假定其功能已通过测试验证，其签名代表了可信的行为契约。
             - **准确性与业务价值的平衡**：力求准确反映对理解端到端业务流程至关重要的所有组件、步骤和关键调用及其精确的嵌套关系。
         5. **输出格式**：
             - 直接输出符合上述要求的、完整的 Markdown 无序列表内容。
             - **强制要求**: 每个列表项都必须以 `- [ ]` 格式开始，包含未勾选的复选框，以支持用户进行开发进度跟踪和任务管理。
             - **进度记录部分**: 在业务逻辑地图末尾添加"开发进度记录"部分，包含：
                 - 当前开发状态跟踪
                 - 重点关注事项
                 - 下一步行动计划
                 - 技术风险和挑战
                 - 待确认的架构决策

7. **AI Agent 需要了解的文件上下文**:
   1. 从项目文件结构中梳理出本次垂直切片开发，AI 代理需要重点理解或可能修改的代码文件上下文。
   2. 提供这些文件的完整路径列表。
   3. 列表应包裹在 `<context_files>` 标签当中。

# 示例输出
```markdown
## 1. 需求背景和方案分析（基于EARS方法）

### 问题现状分析（异常行为需求）
- **认证系统性能问题**: 如果用户登录请求超过1000并发，那么系统应返回503服务不可用错误，当前响应时间超过5秒
- **数据一致性问题**: 如果用户在多设备同时登录，那么系统应保持会话状态同步，当前存在会话冲突导致用户被意外登出
- **安全漏洞风险**: 如果检测到异常登录行为，那么系统应触发安全验证流程，当前缺乏有效的风险检测机制

### 重构目标（通用性需求）
1. **性能目标**: 认证系统应在99%的情况下在2秒内完成用户登录验证
2. **可用性目标**: 系统应支持最多5000并发用户同时进行认证操作
3. **安全性目标**: 系统应实现多因素认证机制，包括邮箱验证和设备指纹识别
4. **可维护性目标**: 认证模块应采用微服务架构，支持独立部署和扩展

### 技术决策原理（状态驱动和事件驱动需求）
- **JWT令牌机制**: 在用户成功登录的情况下，系统应生成包含用户身份和权限信息的JWT令牌
- **会话管理策略**: 当用户发起登录请求时，系统应检查现有会话数量并执行会话限制策略
- **数据库分离设计**: 在高并发访问的情况下，认证数据应存储在独立的数据库实例中以提升性能

### 预期收益（可选功能需求）
- **性能提升**: 在实施新架构的条件下，系统应将平均响应时间从5秒降低到2秒以内
- **成本优化**: 在采用Cloudflare Workers的条件下，系统应减少50%的服务器运维成本
- **用户体验**: 在启用单点登录功能的条件下，用户应能够在所有关联应用间无缝切换
- **安全增强**: 在部署风险检测模块的条件下，系统应能够识别并阻止95%的异常登录尝试
```
## 2. 上下文感知与依赖审计
```markdown 
- [重用] `password.util.ts`: 新的用户注册流程将继续使用其中已有的 `hashPassword` 和 `verifyPassword` 工具函数。
- [修改] `word.service.ts` 中的 `saveWordDefinition` 函数：此函数需要被修改，以接收并处理新增的 `is_candidate` 参数。这是本次实现的关键风险点。
- [替代] 旧的 `LegacyAPIService.swift`: 将被全新的 `AuthAPIService.swift` 完全取代。
```
## 3. 项目文件结构概览
``` bash
project-root
├── cloudflare/
│   ├── d1/
│   │   └── migrations/
│   │       └── 0001_create_users_table.sql  # [新增] D1数据库用户表迁移脚本
│   └── workers/
│       └── api/
│           └── src/
│               ├── auth/
│               │   ├── auth.service.ts      # [新增] 封装注册、登录等核心业务逻辑
│               │   └── utils/
│               │       └── password.util.ts # [已实现] 提供密码哈希与验证的工具函数
│               └── index.ts                 # [修改] 集成并暴露 /api/auth/* 相关路由
├── src/
│   └── app/
│       └── auth/
│           ├── login/
│           │   └── page.tsx                 # [新增] 登录页面的UI和客户端逻辑
│           └── signup/
│               └── page.tsx                 # [新增] 注册页面的UI和客户端逻辑
└── wrangler.toml                            # [修改] 添加D1数据库绑定和Worker环境变量
```  
## 4. 工作区和分支创建建议
```bash
# 概念性命令，用于记录和指导
git checkout dev
git pull origin dev
git worktree add /path/to/your/new/worktree-directory -b feature/auth/email-registration-login dev
```
## 5. Commit 规划概要
```markdown
 - [ ] chore(数据库): 为D1定义初始用户表结构
 - [ ] feat(认证工具): 实现密码哈希和验证工具函数
 - [ ] feat(认证服务): 实现核心用户注册逻辑
 - [ ] ...
```
## 6. 数据结构定义 (Data Structure Definitions)

```typescript
// ============= 核心业务实体 =============

// 用户相关数据结构
interface User {
  id: string;                    // 用户唯一标识符 // 数据库主键
  email: string;                 // 用户邮箱地址 // 用户提供参数
  displayName?: string;          // 用户显示名称 // 用户提供参数
  createdAt: Date;               // 账户创建时间 // 时间戳生成
  status: 'active' | 'inactive'; // 账户状态 // 系统配置
}

// ============= API 请求/响应结构 =============

// 通用API响应结构
interface ApiResponse<T = any> {
  success: boolean;              // 请求是否成功 // 组合计算
  data?: T;                      // 响应数据 // 组合计算
  error?: ApiError;              // 错误信息 // 组合计算
  timestamp: string;             // 响应时间戳 // 时间戳生成
}
```
## 7. 业务逻辑地图

**注意**: 以下示例中的所有列表项都应该以 `- [ ]` 格式开始，包含复选框。为了简洁，此处仅展示结构，实际输出时必须添加复选框。

```markdown
- 用户登录流程
    - **前端 UI 与交互** (`01-Senseword-Frontend`)
        - 用户访问登录页面 (`/auth/login`)
            - `LoginPage` 组件 （from: `01-Senseword-Frontend/src/app/auth/login/page.tsx`）
                - Displays `LoginForm` component.
        - 用户在 `LoginForm` (`01-Senseword-Frontend/src/components/login-form.tsx`) 中操作
            - 输入邮箱、密码。
            - (可选) 勾选“记住我”选项。
            - 点击 "登录" 按钮触发 `onSubmit` 事件
                - `handleSubmit(data: { email, password, rememberMe })` in `LoginPage` component (from: `01-Senseword-Frontend/src/app/auth/login/page.tsx`).
                    - `login(email, password, rememberMe): Promise<void>` in `AuthContext` (`01-Senseword-Frontend/src/lib/auth-context.tsx`).
                        - → 向后端发送 API 请求: `POST /auth/login` with payload `{ email, password, rememberMe }` (发起自 `Workspace` in `login` of `01-Senseword-Frontend/src/lib/auth-context.tsx`).
                            - (若API请求失败或网络错误)
                                - `setError(errorMessage)` and `throw new AuthError(...)` in `login` of `01-Senseword-Frontend/src/lib/auth-context.tsx`.
                            - (若API请求成功)
                                - `setUser(data.user)` in `login` of `01-Senseword-Frontend/src/lib/auth-context.tsx`.
                                - `router.replace('/')` in `login` of `01-Senseword-Frontend/src/lib/auth-context.tsx`.
    - **后端服务处理 (Unified Backend Service)** (`02-Senseword-Backend/unified-backend-service`)
        - **API 端点: `POST /auth/login`**
            - 路由定义: `authRoutes.post('/login', zValidator('json', loginSchema), handleLogin)` (from: `02-Senseword-Backend/unified-backend-service/src/routes/auth/index.ts`).
            - (中间件) `zValidator('json', loginSchema)` (using `loginSchema` from `02-Senseword-Backend/unified-backend-service/src/schemas.ts`).
            - (中间件) `loggerMiddleware` (from: `02-Senseword-Backend/unified-backend-service/src/middlewares/logger.ts`).
            - (中间件) `performanceMiddleware` (from: `02-Senseword-Backend/unified-backend-service/src/middlewares/performance.ts`).
            - 处理器: `handleLogin(c: Context<{ Bindings: Env }>): Promise<Response>` (from: `02-Senseword-Backend/unified-backend-service/src/handlers/login.ts`).
                - 解析请求体: `c.req.json(): Promise<LoginRequestBody>` (Hono Framework).
                - 查找用户: `getUserByEmail(email: string): Promise<(DbUser & { emailVerified: boolean }) | null>` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`).
                    - `DataGatewayClient.getUserByEmail(email: string)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`).
                        - `WorkspaceDataGateway('/user/email/:email', { method: 'GET' })`
                        - → 调用 Worker API: `GET /user/email/:email`
                            - **Cloudflare Worker API 端点: `GET /user/email/:email`**
                                - 路由: `userRoutes.get('/email/:email', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/user.ts`).
                                - (中间件) `authMiddleware()` (from: `03-Cloudflare-Worker/data-api-gateway/src/middlewares/auth.ts`).
                                - 数据库操作: `c.env.DB_USER_CORE.prepare('SELECT id, email, email_verified, name, image, created_at, updated_at FROM user WHERE email = ? LIMIT 1').bind(email).first()` (Targeting D1: `senseword-db-user-core`).
                                - (若未找到用户) → Worker 返回 `c.json(createApiResponse(false, null, '用户不存在', 404), 404)`.
                                - (若找到用户) → Worker 返回 `c.json(createApiResponse(true, dbUserToUser(result)))`.
                    - (若 `DataGatewayClient.getUserByEmail` 抛出错误或返回非期望结果)
                        - `handleLogin` 捕获错误 → 返回错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`.
                - (若 `getUserByEmail` 返回 `null`，即用户不存在)
                    - → 返回错误响应: `c.json({ error: '邮箱或密码不正确', code: AuthErrorCode.EMAIL_NOT_FOUND }, 401)` (from: `02-Senseword-Backend/unified-backend-service/src/handlers/login.ts`).
                - (若用户存在) 检查用户邮箱是否已验证 (`user.emailVerified`)
                    - (若邮箱未验证)
                        - → 返回错误响应: `c.json({ error: '请先验证您的邮箱后再登录', code: AuthErrorCode.EMAIL_NOT_VERIFIED }, 403)` (from: `02-Senseword-Backend/unified-backend-service/src/handlers/login.ts`).
                    - (若邮箱已验证)
                        - 验证密码: `verifyPassword(user.password, password): Promise<boolean>` (from: `02-Senseword-Backend/unified-backend-service/src/auth-utils.ts`).
                            - (内部使用 `crypto.subtle.importKey`, `crypto.subtle.deriveBits` 和 `base64ToBuffer` 进行 PBKDF2 哈希比对).
                        - (若 `verifyPassword` 返回 `false`)
                            - → 返回错误响应: `c.json({ error: '邮箱或密码不正确', code: AuthErrorCode.WRONG_PASSWORD }, 401)` (from: `02-Senseword-Backend/unified-backend-service/src/handlers/login.ts`).
                        - (若 `verifyPassword` 返回 `true`)
                            - 管理会话限制: `getUserSessionCount(user.id): Promise<number>` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`).
                                - `DataGatewayClient.getUserSessionCount(userId: string)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`).
                                    - → 调用 Worker API: `GET /session/count/:userId`
                                        - **Cloudflare Worker API 端点: `GET /session/count/:userId`**
                                            - 路由: `sessionRoutes.get('/count/:userId', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts`).
                                            - (中间件) `authMiddleware()`
                                            - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('SELECT COUNT(*) as count FROM session WHERE user_id = ?').bind(userId).first()`.
                                            - → Worker 返回 `c.json(createApiResponse(true, { count }))`.
                            - (若会话数超限 `MAX_ACTIVE_SESSIONS = 3`)
                                - `deleteOldestUserSession(user.id): Promise<boolean>` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`).
                                    - `DataGatewayClient.deleteOldestUserSession(userId: string)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`).
                                        - → 调用 Worker API: `DELETE /session/oldest/:userId`
                                            - **Cloudflare Worker API 端点: `DELETE /session/oldest/:userId`**
                                                - 路由: `sessionRoutes.delete('/oldest/:userId', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts`).
                                                - (中间件) `authMiddleware()`
                                                - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('DELETE FROM session WHERE id IN (SELECT id FROM session WHERE user_id = ? ORDER BY created_at ASC LIMIT 1)').bind(userId).run()`.
                                                - → Worker 返回 `c.json(createApiResponse(true, { success: true }))` 或 `404`.
                            - 获取请求元数据: `getRequestMetadata(c.req.raw): { ipAddress, userAgent }` (from: `02-Senseword-Backend/unified-backend-service/src/utils.ts`).
                            - 创建会话: `createSession(sessionData: {...}): Promise<boolean>` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`).
                                - `DataGatewayClient.createSession(sessionData: any)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`).
                                    - → 调用 Worker API: `POST /session`
                                        - **Cloudflare Worker API 端点: `POST /session`**
                                            - 路由: `sessionRoutes.post('/', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts`).
                                            - (中间件) `authMiddleware()`
                                            - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('INSERT INTO session ...').bind(...).run()`.
                                            - → Worker 返回 `c.json(createApiResponse(true, { success: true, id: sessionData.id }))` 或 `500`.
                            - (若 `createSession` 返回 `false`)
                                - → 返回错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`.
                            - 生成JWT令牌: `createJwtToken(jwtPayload: JWTPayload): Promise<string>` (from: `02-Senseword-Backend/unified-backend-service/src/auth-utils.ts`).
                            - (若 `createJwtToken` 失败)
                                - → 返回错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`.
                            - 设置认证Cookie: `setAuthCookie(c: Context, token: string, expiresAt: number): void` (from: `02-Senseword-Backend/unified-backend-service/src/cookie.ts`).
                            - 生成刷新令牌: `generateRefreshToken(): Promise<{ token: string, hashedToken: string }>` (from: `02-Senseword-Backend/unified-backend-service/src/auth-utils.ts`).
                            - 存储刷新令牌: `createRefreshToken(tokenData: {...}): Promise<boolean>` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`).
                                - `DataGatewayClient.createRefreshToken(tokenData: any)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`).
                                    - → 调用 Worker API: `POST /refresh-token`
                                        - **Cloudflare Worker API 端点: `POST /refresh-token`**
                                            - 路由: `refreshTokenRoutes.post('/', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/refresh-token.ts`).
                                            - (中间件) `authMiddleware()`
                                            - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('INSERT INTO refresh_token ...').bind(...).run()`.
                                            - → Worker 返回 `c.json(createApiResponse(true, { success: true }))` 或 `500`.
                            - (若 `createRefreshToken` 返回 `false`，记录错误但继续).
                            - 设置刷新令牌Cookie: `setRefreshCookie(c: Context, token: string, expiresAt: number): void` (from: `02-Senseword-Backend/unified-backend-service/src/cookie.ts`).
                            - → 返回成功响应: `c.json({ user: { id, email, name, emailVerified, image }})`.
    - (后续流程如会话验证、刷新令牌、登出等，若被登录流程直接或间接触发，也应按此嵌套结构展示)
        - (示例) 前端后续自动发起的 `GET /auth/session` 请求 (假设已登录，会话有效)
            - **后端服务处理 (Unified Backend Service)**
                - **API 端点: `GET /auth/session`**
                    - 路由定义: `authRoutes.get('/session', authMiddleware, handleSession)` (from: `02-Senseword-Backend/unified-backend-service/src/routes/auth/index.ts`).
                    - (中间件) `authMiddleware(c, next)` (from: `02-Senseword-Backend/unified-backend-service/src/middlewares/auth.ts`)
                        - `getAuthCookie(c)` (from: `02-Senseword-Backend/unified-backend-service/src/cookie.ts`) → (获取到 token)
                        - `verifyJwtToken(token)` (from: `02-Senseword-Backend/unified-backend-service/src/auth-utils.ts`) → (返回 payload)
                        - `getSessionById(sessionIdFromPayload)` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`)
                            - `DataGatewayClient.getSession(sessionId)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`)
                                - → 调用 Worker API: `GET /session/:sessionId`
                                    - **Cloudflare Worker API 端点: `GET /session/:sessionId`**
                                        - 路由: `sessionRoutes.get('/:id', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts`).
                                        - (中间件) `authMiddleware()`
                                        - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('SELECT ... FROM session WHERE id = ?').bind(sessionId).first()`.
                                        - → Worker 返回 `c.json(createApiResponse(true, sessionData))`.
                        - (会话有效且未过期) → `c.set('user', ...)` , `await next()`.
                    - 处理器: `handleSession(c: Context)` (from: `02-Senseword-Backend/unified-backend-service/src/handlers/session.ts`)
                        - (从 `c.get('user')` 获取用户信息)
                        - `getUserById(userId)` (from: `02-Senseword-Backend/unified-backend-service/src/db.ts`) // 实际在 handleSession 中是直接使用 c.get('user') 中的信息，但如果需要再次查询DB，则路径如下
                            - `DataGatewayClient.getUserById(userId)` (from: `02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts`)
                                - → 调用 Worker API: `GET /user/:userId`
                                    - **Cloudflare Worker API 端点: `GET /user/:userId`**
                                        - 路由: `userRoutes.get('/:id', ...)` (from: `03-Cloudflare-Worker/data-api-gateway/src/routes/user.ts`).
                                        - (中间件) `authMiddleware()`
                                        - 数据库操作: `c.env.DB_USER_CORE.prepare('SELECT ... FROM user WHERE id = ?').bind(userId).first()`.
                                        - → Worker 返回 `c.json(createApiResponse(true, userData))`.
                        - → 返回成功响应 `c.json({ user: { ... }, authenticated: true })`.
```
## 8. AI Agent 需要了解的文件上下文
<context_files>
根据具体项目需求，列出AI Agent需要了解的相关文件路径，例如：
- 数据库迁移文件
- 服务层实现文件
- 工具函数文件
- 路由和控制器文件
- 配置文件
- 前端页面组件
- 中间件文件
- 环境变量文件
- 依赖管理文件
- 测试配置文件
</context_files>