# 提示词指令：端到端业务逻辑地图

你的核心任务是基于用户提问的业务需求：生成一份单一、完整、深度整合的业务逻辑地图。

这份地图包含一个必选的文件架构和三个可选的类型模块：前端业务逻辑地图、后端业务逻辑地图、以及因果台账。用户会指定产出文件类型模块，如果没有指定：

1. 如果是后端事务：生成后端业务逻辑地图
2. 如果是前端事务：生成前端业务逻辑地图 + 因果台账

必须将一个端到端业务流程作为主干，并在流程的关键节点，智能地、无缝地融入关于“组件生命周期”和“核心状态与数据流”的上下文解释。

你的输出不仅仅是一个冷冰冰的调用栈追踪，而是一份富有洞察力的分析报告，它解释了“什么(What)”、“为何(Why)”与“如何(How)”。

你必须利用对整个项目代码的理解，进行代码追踪和回溯，并以一个能够反映完整端到端流程、精确调用层级以及多维上下文的层次化 Markdown 无序列表的形式输出。

## 执行步骤

### 1. 需求分析

- 理解业务逻辑的核心目标和用户价值
- 识别涉及的主要技术组件和文件
- 确定业务流程的起点和终点

### 2. 代码验证与分析

- 强制代码验证：使用 codebase-retrieval 工具确认所有涉及的文件存在
- 函数签名验证：使用 view 工具查看关键函数的实际实现，确保签名准确性
- 状态属性确认：验证@Published、@State 等状态包装器的实际声明方式
- 调用链追踪：使用 search-untruncated 工具查找函数调用关系，确保调用链完整性

### 3. 文件架构树构建

- 按技术分层组织文件结构
- 为每个文件明确其在业务中的职责
- 建立文件间的依赖关系

### 4. 业务逻辑嵌入

- 将具体的业务逻辑步骤分布到对应文件中
- 使用精确的代码引用和函数签名
- 添加必要的上下文说明

### 5. 因果台账分析

- 识别关键的用户操作和系统事件
- 分析状态转移和副作用
- 建立完整的因果关系链
- 错误路径分析：追踪异常处理和降级策略
- 边界条件分析：分析并发访问、资源限制等场景

# 文件架构职责描述

## 完整文件架构树与业务职责视角

在业务逻辑地图的开头，必须提供完整的文件架构树，展示业务流程涉及的所有文件及其在整体架构中的职责。架构树应按技术分层组织，每个文件都要明确标注其在当前业务流程中的具体职责。

### 前端架构

```
├── 📱 UI 交互层
│ ├── Views/
│ │ ├── Auth/
│ │ │ ├── LoginViewController.swift // 用户登录界面，处理用户输入和登录交互
│ │ │ └── RegisterViewController.swift // 用户注册界面，处理注册表单验证
│ │ └── Main/
│ │ ├── MainContentView.swift // 主界面容器，管理导航和全局状态
│ │ └── SearchView.swift // 搜索界面，处理搜索输入和结果展示
│ └── Components/
│ ├── LoadingIndicator.swift // 加载状态指示器，提供统一的加载体验
│ └── ErrorAlert.swift // 错误提示组件，统一错误信息展示
├── 🔄 状态管理层
│ ├── ViewModels/
│ │ ├── AuthViewModel.swift // 认证状态管理，处理登录状态和用户信息
│ │ └── SearchViewModel.swift // 搜索状态管理，管理搜索结果和加载状态
│ └── Stores/
│ ├── UserStore.swift // 用户数据存储，管理用户信息的持久化
│ └── AppState.swift // 全局应用状态，协调各模块状态
├── 🌐 网络服务层
│ ├── Services/
│ │ ├── AuthService.swift // 认证服务，处理登录注册 API 调用
│ │ ├── SearchService.swift // 搜索服务，处理搜索 API 调用和数据转换
│ │ └── NetworkManager.swift // 网络管理器，统一网络请求配置和错误处理
│ └── Models/
│ ├── User.swift // 用户数据模型，定义用户信息结构
│ ├── SearchResult.swift // 搜索结果模型，定义搜索返回数据结构
│ └── APIResponse.swift // API 响应模型，统一 API 返回格式
└── 💾 数据持久化层
├── Database/
│ ├── SQLiteManager.swift // SQLite 管理器，处理本地数据库操作
│ └── CoreDataStack.swift // Core Data 栈，管理数据模型和上下文
└── Cache/
├── ImageCache.swift // 图片缓存，优化图片加载性能
└── DataCache.swift // 数据缓存，减少网络请求频率
```

### 后端架构

```
├── 🚪 API 网关层
│ ├── routes/
│ │ ├── auth/
│ │ │ ├── index.ts // 认证路由定义，处理登录注册端点
│ │ │ └── middleware.ts // 认证中间件，验证 JWT 令牌和权限
│ │ └── search/
│ │ ├── index.ts // 搜索路由定义，处理搜索请求端点
│ │ └── validation.ts // 搜索参数验证，确保请求数据格式正确
│ └── middlewares/
│ ├── logger.ts // 请求日志中间件，记录 API 调用信息
│ ├── cors.ts // 跨域处理中间件，配置 CORS 策略
│ └── rateLimit.ts // 限流中间件，防止 API 滥用
├── 🏗️ 业务逻辑层
│ ├── handlers/
│ │ ├── auth/
│ │ │ ├── login.ts // 登录处理器，执行用户认证逻辑
│ │ │ ├── register.ts // 注册处理器，处理用户注册业务逻辑
│ │ │ └── refresh.ts // 令牌刷新处理器，管理 JWT 令牌生命周期
│ │ └── search/
│ │ ├── query.ts // 搜索查询处理器，执行搜索算法和排序
│ │ └── suggest.ts // 搜索建议处理器，提供自动完成功能
│ ├── services/
│ │ ├── AuthService.ts // 认证业务服务，封装认证相关业务规则
│ │ ├── SearchService.ts // 搜索业务服务，实现搜索核心算法
│ │ └── UserService.ts // 用户业务服务，管理用户信息和权限
│ └── utils/
│ ├── crypto.ts // 加密工具，处理密码哈希和 JWT 签名
│ ├── validation.ts // 验证工具，提供数据格式验证函数
│ └── response.ts // 响应工具，统一 API 响应格式
├── 💾 数据访问层
│ ├── repositories/
│ │ ├── UserRepository.ts // 用户数据仓库，封装用户数据 CRUD 操作
│ │ ├── SearchRepository.ts // 搜索数据仓库，管理搜索索引和查询
│ │ └── SessionRepository.ts // 会话数据仓库，管理用户会话信息
│ ├── models/
│ │ ├── User.ts // 用户数据模型，定义数据库用户表结构
│ │ ├── Session.ts // 会话数据模型，定义会话表结构
│ │ └── SearchIndex.ts // 搜索索引模型，定义搜索数据结构
│ └── database/
│ ├── connection.ts // 数据库连接，管理数据库连接池
│ ├── migrations/ // 数据库迁移，管理数据库结构变更
│ └── seeds/ // 数据种子，提供测试和初始数据
└── 🔧 基础设施层
├── config/
│ ├── database.ts // 数据库配置，定义连接参数和选项
│ ├── redis.ts // Redis 配置，配置缓存和会话存储
│ └── environment.ts // 环境配置，管理不同环境的配置参数
├── monitoring/
│ ├── metrics.ts // 性能指标，收集和报告系统性能数据
│ ├── health.ts // 健康检查，监控服务状态和依赖
│ └── logging.ts // 日志系统，统一日志格式和输出
└── deployment/
├── docker/ // Docker 配置，容器化部署配置
└── kubernetes/ // K8s 配置，容器编排和服务发现
```

### 架构树设计原则

1. **分层清晰**：按照技术架构分层（UI 层、业务层、数据层等）
2. **职责明确**：每个文件都要标注在当前业务流程中的具体作用
3. **依赖关系**：通过缩进和分组体现文件间的依赖关系
4. **完整覆盖**：包含业务流程涉及的所有关键文件
5. **图标标识**：使用 emoji 图标区分不同类型的组件和层级

# 前端业务逻辑地图模块生成指令

## 分析视角 (Analysis Perspectives)

1.  端到端全链路视角 (End-to-End Full-Stack Perspective - 默认)

    - 目标: 提供最全面的视图，从前端用户交互开始，贯穿整个技术栈，直到后端数据落盘和响应返回。
    - 追踪范围: 始于 iOS UI 控件的事件，追踪内部状态管理和函数调用，跨越 API 边界，深入后端服务的路由、中间件、处理器、服务层、数据访问层，直至数据库或外部服务调用，并包含成功和错误处理的所有关键分支，最终回到 iOS UI 的更新。

2.  iOS 用户故事/功能流视角 (iOS User Story/Feature Flow Perspective)

    - 目标: 聚焦于 iOS 应用内部为完成一个用户故事所发生的所有逻辑。
    - 追踪范围: 始于 iOS UI 控件的事件，追踪所有相关的 ObservableObject、@State、@StateObject、@EnvironmentObject 等状态管理的调用、业务逻辑函数和服务函数。
    - 示例关注点: `@IBAction buttonTapped` -> `handleSubmit()` -> `authService.login()` -> `URLSession.dataTask(with: loginRequest)`

3.  iOS 视图控制器/视图生命周期视角 (iOS ViewController/View Lifecycle Perspective)

    - 目标: 描绘一个特定 iOS 视图控制器或 SwiftUI 视图从创建、加载、显示到销毁的完整生命过程。
    - 追踪范围:
      - 创建与加载 (Creation & Loading): 首先追溯该视图控制器是在哪个父控制器或导航栈中被实例化的。然后，按照执行顺序列出其加载阶段的生命周期方法（如 UIKit 的`viewDidLoad`, `viewWillAppear`, `viewDidAppear`或 SwiftUI 的`onAppear`），并将在这些方法中调用的任何关键函数（如数据获取`loadData()`）作为其子项嵌套列出。
      - 更新 (Updating): 描述触发更新的典型场景（如数据源变更、用户交互），并列出相关的生命周期方法（如`viewWillLayoutSubviews`或 SwiftUI 的`onChange`）。
      - 销毁 (Deallocation): 列出销毁阶段的生命周期方法（如`viewDidDisappear`, `deinit`或 SwiftUI 的`onDisappear`），并将在其中调用的任何清理函数作为子项嵌套。
    - 结构: 顶层应为 `[视图控制器/视图名称] 的生命周期分析`。

4.  iOS 核心状态与数据流视角 (iOS Core State & Data Flow Perspective)

    - 目标: 追踪一个共享的核心状态（如“购物车”或“用户信息”）是如何被定义、修改和消费的。
    - 追踪范围:
      - 状态定义与存储 (State Definition & Storage): 首先定位状态的来源（如 ObservableObject 类、@StateObject、UserDefaults、Core Data 模型），并列出其定义位置。
      - 状态的写入方 (State Writers): 识别所有用于修改此状态的方法、属性设置器或数据操作函数。然后，对每个写入方法，反向追溯是哪些 UI 交互或业务逻辑最终调用了它。将这些调用链路以嵌套列表的形式展示出来。
      - 状态的读取方 (State Readers): 识别所有订阅或读取此状态的视图控制器、SwiftUI 视图或服务类。将这些“消费者”作为列表项展示，并指明它们在何处以及如何使用这些状态（如通过@ObservedObject、@EnvironmentObject 或 KVO）。
    - 结构: 顶层应为 `核心状态追踪：[状态名称]`，其下分为“状态定义”、“写入方”和“读取方”等主要部分。

## 你必须遵循以下原则，将多个视角编织进一个统一的流程地图中：

1.  以“端到端全链路”为主干:

    - 始终将用户的操作到系统的最终响应（贯穿前后端）作为报告的主线和骨架。列表的顶层结构和基础嵌套关系由这个实际的执行流驱动。

2.  在关键节点注入“生命周期”上下文:

    - 时机: 当追踪流程进入到一个关键的 iOS 视图控制器或视图（尤其是第一次出现时），或者某个行为明显由生命周期方法触发时。
    - 内容: 以一个清晰的、斜体的子列表项形式，解释当前的生命周期背景。
    - 目的: 解释为什么某个动作（如 API 请求）会在这个时间点发生。
    - 示例:
      - HomeViewController 视图加载
        - _Lifecycle Context: 视图控制器首次加载完成，进入 `viewDidLoad` 生命周期方法以执行初始化操作，如获取初始数据。_
        - viewDidLoad() <- [...]
          - loadInitialData() <- [...]

3.  在关键节点注入“核心状态与数据流”上下文:

    - 时机: 当追踪到一个明确的“状态写入”（如调用 ObservableObject 的属性设置、@State 的更新）或“状态读取”（如使用@ObservedObject, @EnvironmentObject）操作时。
    - 内容: 以一个清晰的、斜体的子列表项形式，解释这次状态操作的意义和影响。
      - 对于写入: 解释是哪个状态被改变了，从什么变成了什么（概念上的），以及这次变更可能导致哪些“订阅者”更新。
      - 对于读取: 解释该视图为何依赖此状态，以及状态的值如何影响其渲染或行为。
    - 目的: 解释一个动作如何通过状态变更影响到应用的其他部分，或者一个视图的行为如何被当前状态所驱动。
    - 示例:
      - `cartService.addItem(product)` <- [...]
        - _State Management Context: `@Published var items`状态被更新，商品被添加到购物车数组中。_
        - _State Management Context: 任何观察购物车状态的视图（如顶部的 `CartBadgeView`）都将因此次变更而自动重新渲染。_

4.  用业务逻辑语言串联故事线:
    - 在罗列纯粹的函数调用之前，优先使用简洁的业务语言描述步骤（例如：`- 验证用户输入格式`），使其成为包裹技术细节的父节点。这确保了地图的叙事性和可读性。

## 整体结构

- 直接输出符合上述要求的、完整业务逻辑地图。
- 使用 ```markdown 包裹文件架构树部分，确保编辑器正确解析。

### 列表项内容：

- 列表项用于共同描绘一个完全嵌入式的端到端业务流程，可以包含以下不同层面的信息，并严格按照调用流进行嵌套：
- 系统组件/分层标识 (用于组织高阶结构)：
  - 格式: `- 组件名称` - 使用复选框 + 加粗标题式文字标明流程当前所处的系统组件、技术分层或主要处理阶段。
- 业务逻辑步骤描述 (用于阐述具体动作)：
  - 格式: `- 业务步骤描述` - 在组件/分层标识下，或作为独立的流程节点，使用复选框 + 简洁文字描述具体的业务行为、用户交互、或系统内部的逻辑处理步骤。
- 函数签名/API 调用/关键方法 (核心实现点的精确指代)：
  - 格式: `- 注释说明：functionName(param1: Type1, param2: Type2): ReturnType <- [完整文件路径]` - 在最能体现具体计算、数据处理或交互的业务步骤之下，使用注释前置 + 函数调用信息。
  - 函数签名：应清晰、准确地列出，例如 `- 函数调用说明：functionName(param1: Type1, param2: Type2): ReturnType`。
  - API 调用：明确指出调用的目标和方式，例如 `- POST登录请求：POST /auth/login` 或 `- Worker API调用：GET /user/email/:email`。在其下一层级，应详细展开该 API 端点的处理逻辑，包括其内部的主要函数调用和数据库操作。
  - 文件路径/来源追溯 (必选，完整路径)：
    - 定义：对于当前文件中调用的外部函数、方法、类或服务，必须标注其定义所在的源文件位置
    - 目的：建立跨文件的调用关系追溯，帮助开发者快速定位函数实现和理解依赖关系
    - 适用场景：
      - 调用其他文件中定义的函数：`searchService.getSuggestions()` <- [Services/SearchService.swift]
      - 使用其他文件中的类或结构体：`let user = User(...)` <- [Models/User.swift]
      - 调用外部 API 或服务：`apiClient.request()` <- [Network/APIClient.swift]
      - 依赖注入的服务：`@EnvironmentObject var coordinator: SearchCoordinator` <- [Coordinators/SearchCoordinator.swift]
    - 格式要求：从项目根目录开始的完整相对文件路径，例如 `<- [iOS/SensewordApp/Services/SearchService.swift]`
    - 不适用场景：当前文件内部定义的函数、SwiftUI 内置组件、系统框架函数无需标注来源
  - 后续流程 (关键)：紧跟函数调用或 API 调用之后，必须清晰展示由于该调用成功或失败（或其他条件判断）而引发的后续逻辑步骤或函数调用，直到该分支的终点。 例如，如果一个函数调用失败导致返回错误，应明确指出 `- 错误响应返回：c.json({ error: '...', code: '...' }, statusCode)`。如果成功，则继续列出下一系列调用的嵌套。

### 层次关系与流程表达 (核心)：

- 列表的顶层或较高层级代表业务流程的起点或主要的系统/逻辑分块。
- 内层列表项必须代表其紧邻的上一级外层列表项（无论是业务步骤描述还是另一个函数/API 调用）所包含的逻辑子步骤或直接调用的下一个函数/API/组件内部逻辑。
- 缩进的深度必须直接反映业务逻辑的分解层次、真实的函数/组件调用栈深度或子步骤的从属关系。
- 一个函数调用如果触发了对另一个可识别函数（有签名）、API 或组件内部逻辑的调用，则该被调用/触发的逻辑必须作为子项嵌套在其调用者的下方，并持续追踪，直至该逻辑分支的终点（如数据库操作、返回响应或达到用户定义的分析边界）。
- 列表项的垂直顺序应尽可能反映业务逻辑的实际执行顺序或主要的控制流程分支。
- 所有列表项使用简单的 `-` 符号，不使用复选框格式。

### 核心关注点与约束：

- 完全嵌入式与端到端追踪：必须捕捉和展现跨越多个系统组件的完整业务流程，并将所有后续调用和处理逻辑（包括错误处理和成功路径）直接嵌套在其发起者的上下文中，形成一条连续的、从用户操作到系统响应，以及程序终点的完整调用链。
- 调用驱动的结构 (Call-Driven Structure): 列表的结构完全由业务流程中的实际函数/API 调用顺序和嵌套关系驱动。即使多个被调用函数来自同一文件或模块，它们也必须出现在各自调用方的上下文中，严禁集中罗列或单独分区。
- 接口、交互与步骤描述优先：文档的焦点是系统组件间的交互点、API 契约、函数签名以及高级别的业务逻辑步骤。绝对禁止包含任何实际的函数体代码、代码片段或对函数内部实现逻辑的详细描述。
- 基于信任的黑盒视角 (针对函数级)：当列出函数签名时，我们共同假定其功能已通过测试验证，其签名代表了可信的行为契约。
- 准确性与业务价值的平衡：力求准确反映对理解端到端业务流程至关重要的所有组件、步骤和关键调用及其精确的嵌套关系。

### 输出易错点：

#### 1. 移除复选框格式

- 错误：`- [ ] 业务步骤描述`
- 正确：`- 业务步骤描述`

#### 2. 注释前置格式

- 错误：`let sqliteManager = try await SQLiteManager.create()` SQLite 管理器创建
- 正确：SQLite 管理器创建：`let sqliteManager = try await SQLiteManager.create()`

#### 3. 左箭头引入文件路径

- 错误：`(from: SQLiteManager.swift)`
- 正确：`<- [iOS/SensewordApp/Infrastructure/Database/SQLiteManager.swift]`

#### 前端带状态示例输出格式

```markdown
- 用户登录流程
  - iOS 应用 UI 与交互 [02-Senseword-iOS]
    - 用户访问登录页面
      - LoginViewController 视图加载 <- [`02-Senseword-iOS/Senseword/Views/Auth/LoginViewController.swift]
        - _Lifecycle Context: 视图控制器首次加载完成，准备好响应用户交互。_
        - 显示登录表单界面。
    - 用户在登录表单中操作
      - 输入邮箱、密码。
      - 点击 "登录" 按钮触发 `@IBAction loginButtonTapped` 事件
        - `handleLogin(email: String, password: String, rememberMe: Bool)` in `LoginViewController` <- [`02-Senseword-iOS/Senseword/Views/Auth/LoginViewController.swift].
          - 调用认证服务进行登录
            - `authService.login(email: String, password: String, rememberMe: Bool): async throws -> User` in `AuthService` [02-Senseword-iOS/Senseword/Services/AuthService.swift].
              - → 向后端发送 API 请求: `URLSession.dataTask` with `POST /auth/login` payload `{ email, password, rememberMe }`
                - (若 API 请求失败或网络错误)
                  - 在 `AuthService` 中设置错误状态
                    - `errorMessage = errorDescription` <- [`02-Senseword-iOS/Senseword/Services/AuthService.swift]
                    - _State Management Context: 更新认证状态为 `error`，相关的 UI（如错误提示信息）将会显示。_
                  - 抛出 `AuthError` 异常，由调用方捕获处理。
                - (若 API 请求成功)
                  - 更新全局用户状态
                    - `currentUser = user` <- [`02-Senseword-iOS/Senseword/Services/AuthService.swift]
                    - _State Management Context: 这是核心的状态变更。全局用户状态从 `nil` 更新为具体的用户对象。_
                    - _State Management Context: 所有观察用户状态的视图（如导航栏的用户头像、个人中心页面）将因此次变更而自动重新渲染。_
                  - 执行页面跳转
                    - `navigationController?.pushViewController(mainViewController, animated: true)` <- [`02-Senseword-iOS/Senseword/Views/Auth/LoginViewController.swift]
```

# 后端业务逻辑地图模块生成指令

## 核心理念：端点驱动的线性数据流

后端专用方法：用"端点驱动的线性数据流"来梳理后端代码。

### 后端业务逻辑的本质分析

后端的特点是：

- 端点驱动：从 HTTP 端点开始的明确入口
- 线性流水线：请求 → 验证 → 业务处理 → 数据库操作 → 响应
- 事务边界：明确的数据库事务开始和结束
- 错误传播：异常沿调用栈向上传播，统一错误处理

核心理念：后端业务逻辑的清晰性在于其线性特征和明确的数据流向。每个端点都是一个独立的处理管道，数据从请求开始，经过验证、业务逻辑、持久化，最终返回响应。

### 实践切入方法

从 HTTP 端点开始，沿着数据流向前推进：

1. 端点定义分析：明确 HTTP 方法、路径、参数结构
2. 请求验证链：参数验证、权限检查、业务规则验证
3. 业务处理流：核心业务逻辑、数据转换、外部服务调用
4. 持久化操作：数据库事务、缓存更新、文件操作
5. 响应构建：数据序列化、状态码设置、错误处理

## 核心分析原则

### 端到端全链路驱动

你必须遵循以下原则，将多个视角编织进一个统一的流程地图中：

1.  以"端到端全链路"为主干:

    - 始终将 HTTP 请求到响应返回（贯穿所有处理层）作为报告的主线和骨架。列表的顶层结构和基础嵌套关系由这个实际的执行流驱动。

2.  在关键节点注入"请求生命周期"上下文:

    - 时机: 当追踪流程进入到一个关键的处理阶段（如验证、业务逻辑、数据库操作）时。
    - 内容: 提供具体的代码级别分析，包括参数检查、事务边界、错误处理。
    - 目的: 解释为什么某个操作（如数据库查询）会在这个时间点发生，以及具体的执行条件。
    - 示例:
      - - 用户创建端点处理
          - - 参数验证：`guard let email = request.email, email.isValidEmail else { throw ValidationError.invalidEmail }`
          - - 重复检查：`guard try await !userRepository.existsByEmail(email) else { throw BusinessError.userAlreadyExists }`
          - - 事务开始：`try await database.transaction { ... }`
          - - 用户创建方法：`createUser(request: CreateUserRequest)` <- [src/controllers/UserController.ts]
              - - 密码加密：`let hashedPassword = try await bcrypt.hash(request.password)` <- [src/services/AuthService.ts]

3.  在关键节点注入"数据流与事务"上下文:

    - 时机: 当追踪到明确的"数据写入"（如数据库插入、更新）或"数据读取"（如查询、缓存读取）操作时。
    - 内容: 提供具体的 SQL 语句、事务边界和数据一致性保证。
    - 目的: 解释数据如何在系统中流转，以及事务如何保证数据一致性。
    - 示例:
      - - 用户数据持久化：`userRepository.create(user)` <- [src/repositories/UserRepository.ts]
          - - 事务检查：`guard database.isInTransaction else { throw TransactionError.notInTransaction }`
          - - SQL 执行：`INSERT INTO users (email, password_hash, created_at) VALUES (?, ?, ?)`
          - - 外键约束：确保关联表的数据一致性
          - - 事务提交：`try await transaction.commit()`

4.  用业务逻辑语言串联故事线:
    - 在罗列纯粹的函数调用之前，优先使用简洁的业务语言描述步骤（例如：`- 验证用户邮箱格式`），使其成为包裹技术细节的父节点。这确保了地图的叙事性和可读性。

## 整体结构

- 一个多层嵌套的 Markdown 无序列表（统一使用 `-` 作为列表标记符，不包含复选框）。
- 列表的顶层应指明正在分析的端到端业务流程。
- 支持清晰的业务逻辑梳理和架构理解。

### 后端示例返回

```markdown
- 用户登录端到端处理流程 (POST /auth/login)
  - API 网关层 - 请求接收与路由
    - HTTP 请求接收: `POST /auth/login`
    - 路由匹配: `authRoutes.post('/login', zValidator('json', loginSchema), handleLogin)` <- [02-Senseword-Backend/unified-backend-service/src/routes/auth/index.ts]
    - 中间件链执行
      - 请求体验证: `zValidator('json', loginSchema)` <- [02-Senseword-Backend/unified-backend-service/src/schemas.ts]
        - 验证邮箱格式: `email.isValidEmail()`
        - 验证密码长度: `password.length >= 8`
        - (验证失败) → 返回 400 错误响应
      - 请求日志记录: `loggerMiddleware` <- [02-Senseword-Backend/unified-backend-service/src/middlewares/logger.ts]
        - 记录请求时间戳、IP 地址、User-Agent
      - 性能监控: `performanceMiddleware` <- [02-Senseword-Backend/unified-backend-service/src/middlewares/performance.ts]
        - 开始性能计时器
  - 业务逻辑层 - 认证处理
    - 登录处理器调用: `handleLogin(c: Context<{ Bindings: Env }>): Promise<Response>` <- [02-Senseword-Backend/unified-backend-service/src/handlers/login.ts]
      - 请求体解析: `c.req.json(): Promise<LoginRequestBody>`
        - 提取 email, password, rememberMe 字段
      - 用户查找验证
        - 用户查询: `getUserByEmail(email: string): Promise<(DbUser & { emailVerified: boolean }) | null>` <- [02-Senseword-Backend/unified-backend-service/src/db.ts]
          - 数据网关调用: `DataGatewayClient.getUserByEmail(email: string)` <- [02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts]
            - Worker API 请求: `GET /user/email/:email`
            - Cloudflare Worker 处理
              - 路由匹配: `userRoutes.get('/email/:email', ...)` <- [03-Cloudflare-Worker/data-api-gateway/src/routes/user.ts]
              - 认证中间件: `authMiddleware()` <- [03-Cloudflare-Worker/data-api-gateway/src/middlewares/auth.ts]
                - JWT 令牌验证
                - 权限检查
              - 数据库查询: `c.env.DB_USER_CORE.prepare('SELECT id, email, email_verified, name, image, created_at, updated_at FROM user WHERE email = ? LIMIT 1').bind(email).first()`
                - 目标数据库: [D1: senseword-db-user-core]
                - (用户不存在) → 返回 `c.json(createApiResponse(false, null, '用户不存在', 404), 404)`
                - (用户存在) → 返回 `c.json(createApiResponse(true, dbUserToUser(result)))`
          - (API 调用失败) → 抛出异常，被 handleLogin 捕获
            - 错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`
        - (用户不存在) → 返回错误响应: `c.json({ error: '邮箱或密码不正确', code: AuthErrorCode.EMAIL_NOT_FOUND }, 401)`
        - (用户存在) → 继续邮箱验证检查
          - 邮箱验证状态检查: `user.emailVerified`
            - (邮箱未验证) → 返回错误响应: `c.json({ error: '请先验证您的邮箱后再登录', code: AuthErrorCode.EMAIL_NOT_VERIFIED }, 403)`
            - (邮箱已验证) → 继续密码验证
      - 密码验证流程
        - 密码验证: `verifyPassword(user.password, password): Promise<boolean>` <- [02-Senseword-Backend/unified-backend-service/src/auth-utils.ts]
          - PBKDF2 哈希比对: `crypto.subtle.importKey`, `crypto.subtle.deriveBits`, `base64ToBuffer`
          - (密码错误) → 返回错误响应: `c.json({ error: '邮箱或密码不正确', code: AuthErrorCode.WRONG_PASSWORD }, 401)`
          - (密码正确) → 继续会话管理
      - 会话管理流程
        - 会话数量检查: `getUserSessionCount(user.id): Promise<number>` <- [02-Senseword-Backend/unified-backend-service/src/db.ts]
          - 数据网关调用: `DataGatewayClient.getUserSessionCount(userId: string)` <- [02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts]
            - Worker API 请求: `GET /session/count/:userId`
            - Cloudflare Worker 处理
              - 路由匹配: `sessionRoutes.get('/count/:userId', ...)` <- [03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts]
              - 认证中间件: `authMiddleware()`
              - 数据库查询: `c.env.DB_AUTH_SESSION.prepare('SELECT COUNT(*) as count FROM session WHERE user_id = ?').bind(userId).first()`
              - 返回会话数量: `c.json(createApiResponse(true, { count }))`
        - (会话数超限 MAX_ACTIVE_SESSIONS = 3) → 清理最旧会话
          - 删除最旧会话: `deleteOldestUserSession(user.id): Promise<boolean>` <- [02-Senseword-Backend/unified-backend-service/src/db.ts]
            - 数据网关调用: `DataGatewayClient.deleteOldestUserSession(userId: string)` <- [02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts]
              - Worker API 请求: `DELETE /session/oldest/:userId`
              - Cloudflare Worker 处理
                - 路由匹配: `sessionRoutes.delete('/oldest/:userId', ...)` <- [03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts]
                - 认证中间件: `authMiddleware()`
                - 数据库操作: `c.env.DB_AUTH_SESSION.prepare('DELETE FROM session WHERE id IN (SELECT id FROM session WHERE user_id = ? ORDER BY created_at ASC LIMIT 1)').bind(userId).run()`
                - 返回删除结果: `c.json(createApiResponse(true, { success: true }))` 或 404
        - 请求元数据提取: `getRequestMetadata(c.req.raw): { ipAddress, userAgent }` <- [02-Senseword-Backend/unified-backend-service/src/utils.ts]
          - 提取客户端 IP 地址
          - 提取 User-Agent 信息
        - 新会话创建: `createSession(sessionData: {...}): Promise<boolean>` <- [02-Senseword-Backend/unified-backend-service/src/db.ts]
          - 数据网关调用: `DataGatewayClient.createSession(sessionData: any)` <- [02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts]
            - Worker API 请求: `POST /session`
            - Cloudflare Worker 处理
              - 路由匹配: `sessionRoutes.post('/', ...)` <- [03-Cloudflare-Worker/data-api-gateway/src/routes/session.ts]
              - 认证中间件: `authMiddleware()`
              - 💾 数据库插入: `c.env.DB_AUTH_SESSION.prepare('INSERT INTO session ...').bind(...).run()`
              - 返回创建结果: `c.json(createApiResponse(true, { success: true, id: sessionData.id }))` 或 500
          - (会话创建失败) → 返回错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`
      - 令牌生成与设置
        - JWT 令牌生成: `createJwtToken(jwtPayload: JWTPayload): Promise<string>` <- [02-Senseword-Backend/unified-backend-service/src/auth-utils.ts]
          - 构造 JWT payload: { userId, email, sessionId, exp }
          - 使用私钥签名
          - (令牌生成失败) → 返回错误响应: `c.json({ error: '登录过程中发生错误', code: AuthErrorCode.SERVER_ERROR }, 500)`
        - 认证 Cookie 设置: `setAuthCookie(c: Context, token: string, expiresAt: number): void` <- [02-Senseword-Backend/unified-backend-service/src/cookie.ts]
          - 设置 HttpOnly, Secure, SameSite 属性
          - 设置过期时间
        - 刷新令牌生成: `generateRefreshToken(): Promise<{ token: string, hashedToken: string }>` <- [02-Senseword-Backend/unified-backend-service/src/auth-utils.ts]
          - 生成随机令牌
          - 计算令牌哈希值
        - 刷新令牌存储: `createRefreshToken(tokenData: {...}): Promise<boolean>` <- [02-Senseword-Backend/unified-backend-service/src/db.ts]
          - 数据网关调用: `DataGatewayClient.createRefreshToken(tokenData: any)` <- [02-Senseword-Backend/unified-backend-service/src/data-gateway-client.ts]
            - Worker API 请求: `POST /refresh-token`
            - Cloudflare Worker 处理
              - 路由匹配: `refreshTokenRoutes.post('/', ...)` <- [03-Cloudflare-Worker/data-api-gateway/src/routes/refresh-token.ts]
              - 认证中间件: `authMiddleware()`
              - 💾 数据库插入: `c.env.DB_AUTH_SESSION.prepare('INSERT INTO refresh_token ...').bind(...).run()`
              - 返回存储结果: `c.json(createApiResponse(true, { success: true }))` 或 500
          - (刷新令牌存储失败) → 记录错误但继续执行
        - 刷新令牌 Cookie 设置: `setRefreshCookie(c: Context, token: string, expiresAt: number): void` <- [02-Senseword-Backend/unified-backend-service/src/cookie.ts]
          - 设置 HttpOnly, Secure, SameSite 属性
          - 设置长期过期时间
  - 响应返回
    - 成功响应构造: `c.json({ user: { id, email, name, emailVerified, image }})`
    - 性能指标记录: 记录总处理时间
    - 日志记录: 记录登录成功事件
```

# 因果台账模块生成指令

你的核心任务是为给定的前端功能/业务生成一套完整的可视化文档，将前端复杂度的真实来源——多事件 × 多状态 × 多时间尺度的组合爆炸——收敛为有限的、可追溯的、可回放的因果链。

## 前端复杂性的本质分析

前端的特点是：

- **事件驱动**：完全依赖用户操作和状态变化
- **多入口**：每个用户交互都可能是一个"入口"
- **时序敏感**：同样的操作在不同时机可能产生完全不同的结果
- **状态分散**：状态可能分布在多个组件、服务、存储中

**核心理念**：前端之所以让人觉得乱，根源不是语法或框架差异，而是事件的多源性与时间维度的隐式性。状态机把"时间与分支"外显化，单向数据流把"因果与方向"单一化，这两者结合，能把看似无入口的系统，变成一条条可以复盘的可视化路径。

## 实践切入方法

**不要从文件树或组件层级开始，而是从一条用户任务起点向前推进：**

1. **先按用户旅程走**：把"点击登录""接口返回""进入前台""下拉刷新"都当作叙事里的角色（Action），而不是分散在按钮回调、生命周期函数、网络闭包里的偶然事件
2. **先画状态，再谈 UI**：写下功能的"最小真源状态"，画出互斥且完备的状态节点；UI 只是这些节点的投影，永远不直接改动状态本身
3. **串起因果**：让每个动作只做一件事，每个 Action 的职责被叙述清楚
4. **副作用挂在转移边上**：网络请求、持久化、埋点、路由都以"转移状态的副作用"存在，不直接出现在 UI 层
5. **建模—验证—回放的闭环**：用日志与回放验证，保证能"按下播放键"复现任何一次用户旅程

## 理论基础：因果台账思想

**基于"因果台账"思想，表格可以有效梳理前端复杂性：**

### 因果台账的核心价值

| 梳理对象     | 你要产出的东西                                     | 代码中的落点                                              |
| ------------ | -------------------------------------------------- | --------------------------------------------------------- |
| 行为到因果   | Action 的语义与边界（含幂等性、取消规则）          | 统一的 Action 定义与发出点（如意图处理器/Reducer 的输入） |
| 可变到可见   | 状态图与状态切片（原子状态及其派生关系）           | Store/ViewModel 的 State 结构与只读绑定                   |
| 时间到副作用 | 状态转移时机与副作用清单（网络、存储、导航、埋点） | Effect 层的抽象与注入、可取消的任务管理                   |
| 空间到导航   | 可到达的路由与回传契约                             | 类型安全的 Route/Coordinator 与参数模型                   |
| 证据到回放   | 行为—状态—UI 的时间线                              | Action/State Diff 日志、OSLog/Signpost、可复现的演练脚本  |

| **Action（发生了什么）** | **守卫条件（何时有效）**       | **状态转移（从 → 到）**                      | **触发的副作用**           | **取消点（何时终止）** |
| ------------------------ | ------------------------------ | -------------------------------------------- | -------------------------- | ---------------------- |
| AppLaunched              | 总是                           | 认证：未登录 → 未登录 或 登录中              | 如果有 token，启动静默登录 | 进入后台时取消登录任务 |
| TapLogin(credentials)    | 未登录/登录失败                | 认证：未登录/失败 → 登录中                   | 调 login 接口              | 页面被关闭或任务超时   |
| LoginSucceeded(user)     | 登录中                         | 认证：登录中 → 已登录；列表：未加载 → 加载中 | 调 fetchList               | 退出登录或进入后台     |
| LoginFailed(error)       | 登录中                         | 认证：登录中 → 登录失败                      | 记录埋点、展示错误         | 无（一次性）           |
| PullToRefresh            | 已登录 且 列表为有数据/失败/空 | 列表：任一 → 刷新中                          | 调 fetchList(force:true)   | 离开页面/再次刷新      |
| ListLoaded(items)        | 加载中/刷新中                  | 列表：加载中/刷新中 → 有数据/空              | 缓存列表、打点             | 无（一次性）           |
| SelectItem(id)           | 有数据                         | 导航：首页 → 详情(id)                        | 预取详情                   | 页面返回或 App 退后台  |

### 表格方法的优势

- **全局视图**：一张表格就能看到所有可能的事件流转
- **因果关系清晰**：每一行都明确了"什么条件下，什么事件，导致什么结果"
- **边界条件明确**：前置条件和取消条件让异常情况变得可控
- **团队协作友好**：非技术人员也能理解业务逻辑
- **测试用例生成**：每一行都可以直接转化为测试用例

### 设计原则

- 每一行 Action 在代码里有且只有一个入口
- 每一次状态转移在代码里有且只有一个地方写入
- 每一个副作用都能从触发点追溯到取消点
- 任何一个"怪现象"，都可以用"表里缺了哪个 Action、哪条守卫、哪个取消点"来定位
  、

## 核心产出物：三大表格 + 三类图表

### 1. 主表格：前端事件-状态-副作用映射表

| 触发源 | 事件/Action | 前置条件 | 状态转移 | UI 变化 | 副作用 | 取消条件 |
| ------ | ----------- | -------- | -------- | ------- | ------ | -------- |

### 2. 补充表格：状态依赖关系表

| 状态名称 | 依赖的其他状态 | 互斥状态 | 持续时间 | 存储位置 |
| -------- | -------------- | -------- | -------- | -------- |

### 3. 生命周期管理表

| 生命周期阶段 | 触发时机 | 执行的 Action | 状态检查 | 清理任务 | 异常处理 |
| ------------ | -------- | ------------- | -------- | -------- | -------- |

### 设计原则

- 每一行 Action 在代码里有且只有一个入口
- 每一次状态转移在代码里有且只有一个地方写入
- 每一个副作用都能从触发点追溯到取消点
- 任何一个"怪现象"，都可以用"表里缺了哪个 Action、哪条守卫、哪个取消点"来定位

```

```
