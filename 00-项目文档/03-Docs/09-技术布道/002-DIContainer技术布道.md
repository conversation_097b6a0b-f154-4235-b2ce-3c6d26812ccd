### **🎯 核心目标与要解决的问题 (The Goal & Problem)**

DIContainer 的核心目标是实现统一的依赖注入管理，解决 iOS 应用中各层服务之间的耦合问题。通过集中管理基础设施层、Adapter 层、业务层和 ViewModel 层的所有服务，DIContainer 提供了一种清晰、可维护的方式来组织和获取应用组件，避免了硬编码的依赖关系。

### **💡 核心理念与简单比喻 (The Concept & Analogy)**

可以把 DIContainer 想象成一个大型购物中心的"信息咨询台"。就像在购物中心里，所有店铺（服务）都有固定的位置，顾客（组件）可以通过咨询台获取任何店铺的位置信息，而不需要记住每家店的具体位置一样，DIContainer 作为应用的"咨询台"，管理着所有服务的创建和获取，让每个组件都能轻松获得它需要的依赖，而不需要知道这些依赖的具体实现细节。

### **🗺️ 完整流程图 (Mermaid - Flowchart)**

```mermaid
graph TB
    %% 定义样式
    classDef serviceStyle fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef factoryStyle fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef layerStyle fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000

    A[DIContainer] --> B[基础设施层服务]
    A --> C[网络客户端工厂]
    A --> D[业务层服务]
    A --> E[ViewModel工厂方法]

    B --> F[SQLiteManager]
    B --> G[CacheService]
    B --> H[SettingsService]

    C --> I[MainAPIClient]
    C --> J[WordAPIAdapter]
    C --> K[SearchAPIAdapter]
    C --> L[AnonymousPurchaseService]

    D --> M[LocalIndexService]
    D --> N[SearchService]
    D --> O[LocalBookmarkService]
    D --> P[IndexDownloadService]
    D --> Q[LanguagePairConfigManager]
    D --> R[IndexDownloadManager]

    E --> S[SearchViewModel]

    class A serviceStyle
    class B,C,D,E layerStyle
    class F,G,H,I,J,K,L,M,N,O,P,Q,R,S factoryStyle
```

### **⏳ 详细时序图 (Mermaid - Sequence Diagram)**

```mermaid
%%{init: {
  'theme': 'dark',
  'themeVariables': {
    'primaryColor': '#1f2937',
    'primaryTextColor': '#ffffff',
    'primaryBorderColor': '#ffffff',
    'lineColor': '#ffffff',
    'noteTextColor': '#000000',
    'noteBkgColor': '#fbbf24',
    'noteBorderColor': '#ffffff',
    'activationBkgColor': '#fbbf24',
    'sequenceNumberColor': '#ffffff'
  }
}}%%
sequenceDiagram
    participant Component as 应用组件
    participant DIContainer as DIContainer.shared
    participant Services as 各层服务

    Component->>DIContainer: 访问共享容器
    Note over DIContainer: static let shared = DIContainer()

    alt 获取基础设施服务
        Component->>DIContainer: sqliteManager
        DIContainer->>Services: SQLiteManager()
        Services-->>DIContainer: SQLiteManager实例
        DIContainer-->>Component: SQLiteManager实例
    end

    alt 获取网络客户端
        Component->>DIContainer: mainAPIClient
        DIContainer->>Services: APIClient(baseURL: APIConfig.apiBaseURL)
        Services-->>DIContainer: APIClient实例
        DIContainer-->>Component: APIClient实例
    end

    alt 创建业务服务
        Component->>DIContainer: createSearchService()
        DIContainer->>Services: SearchService()
        Services-->>DIContainer: SearchService实例
        DIContainer-->>Component: SearchService实例
    end

    alt 获取ViewModel
        Component->>DIContainer: makeSearchViewModel()
        DIContainer->>Services: SearchViewModel()
        Services-->>DIContainer: SearchViewModel实例
        DIContainer-->>Component: SearchViewModel实例
    end
```

### **TRANSFORM 关键数据结构转化过程 (Mermaid - Data Transformation)**

```mermaid
graph LR
    A[DIContainer初始化] --> B[基础设施服务创建]
    B --> C[网络客户端配置]
    C --> D[业务服务工厂方法]
    D --> E[ViewModel组装]
    E --> F[服务依赖注入]
    F --> G[组件获取服务]

    style A fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    style B fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    style C fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    style D fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    style E fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    style F fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    style G fill:#90EE90,stroke:#000000,stroke-width:2px,color:#000000
```

### **🏛️ 系统架构图 (Mermaid - System Architecture)**

```mermaid
graph TB
    %% 定义样式
    classDef containerStyle fill:#FFE4E1,stroke:#000000,stroke-width:3px,color:#000000
    classDef layerStyle fill:#E6F3FF,stroke:#000000,stroke-width:2px,color:#000000
    classDef serviceStyle fill:#F0FFF0,stroke:#000000,stroke-width:2px,color:#000000
    classDef componentStyle fill:#FFF8DC,stroke:#000000,stroke-width:2px,color:#000000
    classDef factoryStyle fill:#F5F5DC,stroke:#000000,stroke-width:2px,color:#000000

    A[应用组件] --> B[DIContainer.shared]
    B --> C[基础设施层]
    B --> D[网络客户端层]
    B --> E[业务服务层]
    B --> F[ViewModel层]

    C --> G[SQLiteManager]
    C --> H[CacheService]
    C --> I[SettingsService]

    D --> J[APIClient]
    D --> K[WordAPIAdapter]
    D --> L[SearchAPIAdapter]
    D --> M[AnonymousPurchaseService]

    E --> N[LocalIndexService]
    E --> O[SearchService]
    E --> P[LocalBookmarkService]
    E --> Q[IndexDownloadService]
    E --> R[LanguagePairConfigManager]
    E --> S[IndexDownloadManager]

    F --> T[SearchViewModel]

    class B containerStyle
    class C,D,E,F layerStyle
    class G,H,I,J,K,L,M,N,O,P,Q,R,S serviceStyle
    class T componentStyle
```

### **🤔 备选方案对比与决策依据 (Alternatives & Rationale)**

**替代方案 1：手动依赖管理**

- 每个组件自己创建和管理所需的依赖
- 缺点：代码耦合度高，难以测试和维护
- 缺点：重复代码多，容易出现不一致的配置

**替代方案 2：使用第三方依赖注入框架**

- 如 SwiftyVIPER、Cleanse 等
- 缺点：增加第三方依赖，可能与项目现有架构不兼容
- 缺点：学习成本高，团队成员需要熟悉新框架

**替代方案 3：简单的服务定位器模式**

- 创建一个全局的服务定位器类
- 缺点：隐藏了组件的真实依赖关系
- 缺点：难以进行依赖替换和模拟测试

**为什么选择自定义 DIContainer 方案：**

1. **轻量级**：不依赖第三方框架，减少项目复杂性
2. **清晰的依赖关系**：通过工厂方法明确表达组件的依赖
3. **易于测试**：可以轻松替换依赖进行单元测试
4. **分层管理**：按照架构层次组织服务，结构清晰
5. **延迟初始化**：支持按需创建服务，优化性能
6. **类型安全**：使用 Swift 的类型系统确保依赖正确性

### **✅ 总结与收益 (Summary & Benefits)**

引入 DIContainer 后，我们将获得以下核心好处：

1. **解耦合**：组件之间不再直接依赖具体实现，而是通过协议交互
2. **可测试性**：可以轻松替换依赖进行单元测试和集成测试
3. **可维护性**：集中管理所有服务的创建和配置，便于维护
4. **灵活性**：支持按需创建服务，避免不必要的资源消耗
5. **一致性**：统一的服务创建和配置方式，确保应用行为一致
6. **可扩展性**：新增服务只需在 DIContainer 中注册，不影响现有代码
7. **清晰的架构**：分层管理服务，符合 Clean Architecture 原则
