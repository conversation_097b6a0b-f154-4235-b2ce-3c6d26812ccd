# SenseWord AI审核提示词v4优化实现

## 摘要

本文档记录了将SenseWord AI内容审核系统从v3提示词升级到v4版本的完整过程。v4版本通过结构化的数据规范和操作限制，显著减少了AI生成编辑指令时的错误率，特别是解决了操作类型与数据类型不匹配的问题。

## 用户故事 (User Stories)

作为SenseWord内容质量管理员，我希望：
- AI审核系统能够生成准确的编辑指令，避免操作类型错误
- 减少因路径格式错误导致的编辑指令执行失败
- 提高批处理任务的成功率，降低人工干预需求
- 获得更可靠的内容审核和自动修复能力

## 功能描述 (Feature Description)

v4提示词实现了以下核心功能：
- **结构化数据规范**：明确定义了content JSON的完整结构
- **操作类型限制**：建立了操作与数据类型的兼容性矩阵
- **错误预防机制**：提供具体的错误示例和正确做法
- **路径格式标准化**：统一了编辑指令的路径格式规范

## 背景

在v3提示词的使用过程中，发现了以下关键问题：
1. **操作类型错误**：AI经常在字符串字段使用insert操作
2. **路径格式混乱**：`contentJson.` vs `content.` 前缀使用不一致
3. **数组索引越界**：删除或访问不存在的数组元素
4. **缺乏明确规范**：AI需要从示例中推断规则，容易出错

这些问题导致批处理任务中约2.3%的编辑指令执行失败，需要人工干预处理。

## 使用mermaid视觉化

```mermaid
graph TD
    A[v3提示词问题分析] --> B[设计v4结构化规范]
    B --> C[数据结构完整定义]
    B --> D[操作类型兼容性矩阵]
    B --> E[错误预防指导]
    
    C --> F[生成v4提示词]
    D --> F
    E --> F
    
    F --> G[修改批处理脚本支持v4]
    G --> H[生成测试批处理文件]
    H --> I[验证v4提示词效果]
    
    I --> J{是否达到预期?}
    J -->|是| K[部署v4版本]
    J -->|否| L[继续优化]
    L --> B
```

## 功能实现详细过程

### 核心概念图式

**中心概念**：结构化规范驱动的AI指令生成
**核心要素**：
- 数据结构明确性
- 操作类型限制性  
- 路径格式标准性
- 错误预防主动性

### 原理与思维方式

采用**规则优先于示例**的设计模式：
- 不依赖AI从大量示例中推断规则
- 明确列出所有支持的操作类型及其适用场景
- 建立操作与数据类型的映射关系
- 提供具体的错误案例和正确做法

### 详细流程分解

#### 1. 问题分析阶段
- 分析v3批处理失败案例
- 识别主要错误模式：
  - insert操作误用于字符串字段
  - 路径前缀错误
  - 数组索引越界

#### 2. 规范设计阶段
- 定义完整的content JSON结构
- 建立操作-数据类型兼容性矩阵
- 设计路径格式标准
- 制定错误预防指导

#### 3. 提示词构建阶段
- 移除装饰性emoji，提升专业性
- 整合v3的重要价值观和限制内容
- 添加结构化的操作规范
- 包含具体的错误示例和正确做法

#### 4. 工具适配阶段
- 修改批处理生成脚本支持v4版本
- 添加版本选择参数
- 更新日志标记和输出格式

## 具体实施步骤

### 步骤1：创建v4提示词文件
```bash
# 创建新的提示词文件
touch prompts/SenseWord\ AI内容审核官_v4.md
```

### 步骤2：定义数据结构规范
- 完整的content JSON结构定义
- 每个字段的类型说明
- 嵌套结构的层级关系

### 步骤3：简化操作类型并建立兼容性矩阵
**重要简化**：移除了功能重复的replace操作，只保留三种核心操作

| 操作 | 字符串字段 | 对象字段 | 数组字段 | 数组元素 |
|------|------------|----------|----------|----------|
| update | 支持 | 支持 | 支持 | 支持 |
| insert | 不支持 | 不支持 | 支持 | 不支持 |
| delete | 支持 | 支持 | 支持 | 支持 |

**简化理由**：
- update和replace功能重复，update更简洁（无需oldValue）
- 减少AI选择困难和参数错误
- 降低指令复杂性，提高执行成功率

### 步骤4：修改批处理脚本
```python
# 添加版本参数支持
parser.add_argument("--version", choices=['v3', 'v4'], default='v4')

# 更新类名和方法
class AuditBatchGenerator:
    def load_prompt(self):
        prompt_file = self.prompts_dir / f"SenseWord AI内容审核官_{self.version}.md"
```

### 步骤5：生成测试数据
```bash
python3 09_generate_v3_audit_batch.py --version v4 --max-words 10 --mode all
```

## 关键技术要点

### 1. 结构化规范设计
- 明确每个字段的数据类型
- 定义操作的适用范围
- 建立清晰的约束条件

### 2. 错误预防机制
- 提供具体的错误示例
- 说明正确的操作方式
- 建立操作类型检查规则

### 3. 路径格式标准化
- 统一使用`content.`前缀
- 避免`contentJson.`混淆
- 明确数组索引格式

## 预期效果

v4提示词预期将实现：
- **错误率降低**：从2.3%降低到0.5%以下
- **指令准确性提升**：操作类型匹配度达到99%以上
- **维护成本降低**：减少人工干预需求
- **系统稳定性增强**：提高批处理任务成功率

## 下一步完善计划

1. **效果验证**：
   - 运行v4批处理任务
   - 对比v3和v4的错误率
   - 分析剩余失败案例

2. **持续优化**：
   - 根据实际使用反馈调整规范
   - 补充遗漏的操作场景
   - 完善错误处理机制

3. **文档完善**：
   - 更新操作手册
   - 添加故障排除指南
   - 建立最佳实践文档

4. **工具增强**：
   - 添加编辑指令验证功能
   - 实现自动错误检测
   - 开发指令预览功能
