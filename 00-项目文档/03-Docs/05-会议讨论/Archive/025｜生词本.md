

#### **功能单元二：用户生词本管理 (跨越三个函数契约)**

这个单元是产品的核心个性化功能，它需要三个独立的API能力来支撑：**添加、移除、查询**。这也意味着，我们需要为它增加一张新的表。

**新增数据库表：`bookmarks`**

为了清晰地管理用户和单词之间的多对多关系，并保持`users`和`words`表的纯粹性，增加一张`bookmarks`表是最佳实践。

```sql
-- 生词本/收藏表
CREATE TABLE bookmarks (
    user_id TEXT NOT NULL,
    word TEXT NOT NULL,
    language TEXT NOT NULL,
    created_at TEXT NOT NULL,

    -- 复合主键，确保一个用户对一个词只能收藏一次
    PRIMARY KEY (user_id, word, language),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**[BOOKMARK-FC-01]: 添加生词**

  * **职责**: 将一个单词添加到当前用户的生词本中。
  * **输入**: `POST /api/v1/bookmarks` 请求体 `{ word: string, language: string }`
  * **输出**: `{ success: true }`
  * **后端伪代码**: `INSERT INTO bookmarks (user_id, word, language, created_at) VALUES (?, ?, ?, ?)`

**[BOOKMARK-FC-02]: 移除生词**

  * **职责**: 从当前用户的生词本中移除一个单词。
  * **输入**: `DELETE /api/v1/bookmarks` 请求体 `{ word: string, language: string }`
  * **输出**: `{ success: true }`
  * **后端伪代码**: `DELETE FROM bookmarks WHERE user_id = ? AND word = ? AND language = ?`

**[BOOKMARK-FC-03]: 获取生词列表**

  * **职责**: 获取当前用户的所有生词。
  * **输入**: `GET /api/v1/bookmarks`
  * **输出**: `BookmarkList_BE`
    ```typescript
    interface BookmarkList_BE {
      bookmarks: {
        word: string;
        language: string;
        addedAt: string;
      }[];
    }
    ```
  * **后端伪代码**: `SELECT word, language, created_at FROM bookmarks WHERE user_id = ? ORDER BY created_at DESC`

----
结论先行 (Conclusion First)
bookmarks表是一个极其轻量级的“关系表”，它的唯一职责是记录“哪个用户收藏了哪个单词”。单条记录的体积非常小，因此，即使在拥有数百万用户、每人收藏数百个单词的极端情况下，这张表占用的总空间，也仅仅是D1数据库10GB容量的很小一部分。

一、 成本估算：单条收藏记录有多大？ (Size per Bookmark)
我们来分析一下bookmarks表中，每一行数据大概会占用多少空间。

字段

类型

估算大小 (字节)

说明

user_id

TEXT

36

Apple/Google的用户sub通常是一个较长的字符串，我们按UUID的长度估算。

word

TEXT

15

英文单词的平均长度约为5个字母，我们按15个字母的超长单词进行非常保守的估算。

language

TEXT

2

语言代码，例如 "zh", "de"。

created_at

TEXT

24

ISO 8601格式的时间戳，例如 "2025-06-23T12:00:00Z"。

总计



约 77 字节



为了留出足够的安全边际和考虑数据库的存储开销，我们采用一个极其保守的数字，将每条收藏记录的大小估算为 100 字节。

二、 场景推演：10GB空间能承载多少热情？
现在，我们用这个“100字节/条”的成本，来计算10GB的空间上限能做什么。

D1存储上限：10 GB = 10,737,418,240 字节

理论最大收藏量：10,737,418,240 / 100 ≈ 1.07亿条收藏记录

“1.07亿条收藏”是一个什么概念？让我们代入一个极其成功的App运营场景：

假设您的App拥有 100万 付费用户。

再假设，平均每一个用户都是重度用户，收藏了 100个 单词（这在现实中是一个非常高的平均数）。

总收藏记录数 = 1,000,000 用户 × 100 收藏/用户 = 1亿条收藏记录

所需总存储空间 = 1亿条 × 100 字节/条 ≈ 9.3 GB

推演结论：即使在您的产品取得巨大成功，拥有百万级用户，且平均每人都是收藏了上百个单词的“学习狂人”这种最理想的情况下，bookmarks表也仅仅是将要触及10GB的上限。在更现实的场景中（例如，平均用户收藏20-30个单词），其占用的空间会小得多。

三、 与words表的对比：为什么它不是问题？
为了让您有更直观的感受，我们来对比一下bookmarks表和我们另一张核心表words的存储开销。

bookmarks表单行大小：约 100 字节。

words表单行大小：我们之前根据您的实测数据估算，一个包含完整content_json的单词条目，其大小约为 6.6 KB，即 6600 字节。

这意味着，在存储空间占用上，存储1条word记录 ≈ 存储66条bookmark记录。

我们真正的存储消耗大户，是words表，而我们已经论证过，D1的10GB空间对于words表也是绰绰有余的。因此，bookmarks表对总存储空间的压力，完全可以忽略不计。

最终结论
是的，增加一张bookmarks表是关系型数据建模的最佳实践，它能清晰地管理用户与单词之间的关系。

同时，请您对它的存储开销百分之百地放心。它是一个高价值、低成本的完美实体，在可预见的任何用户规模下，都不会对您的D1存储限制构成任何威胁。我们的“两表（+一个轻量级关系表）打天下”战略，依然坚如磐石。