



# TTS 处理后关键帧

-----

### **“数据结构关键帧”完整示例：`horror-struck`**

```json
{
  "word": "horror-struck",
  "metadata": {
    "promptVersion": "9.0",
    "wordFrequency": "Low",
    "relatedConcepts": [
      "fear",
      "terror",
      "shock",
      "paralyzed",
      "scream"
    ]
  },
  "content": {
    "difficulty": "C1",
    "phoneticSymbols": [
      {
        "type": "BrE",
        "symbol": "/ˈhɒrə strʌk/",
        "audioUrl": "https://r2.senseword.app/audio/en/h/horror-struck/BrE.mp3" // 第一次生成单词时并行写入
      },
      {
        "type": "NAmE",
        "symbol": "/ˈhɔːrər strʌk/",
        "audioUrl": "https://r2.senseword.app/audio/en/h/horror-struck/NAmE.mp3"
      }
    ],
    "coreDefinition": "惊恐万分的，吓得目瞪口呆的；形容因看到或听到极其恐怖的事物而瞬间陷入极度恐惧、震惊和僵直的状态。",
    "contextualExplanation": {
      "nativeSpeakerIntent": "当母语者使用'horror-struck'时，他们想描绘的不仅仅是'害怕'，而是一种更强烈的、带有'打击(struck)'感的瞬间反应。它强调的是一种因突如其来的恐怖景象或消息而导致的、近乎麻痹的、无法动弹的惊恐。",
      "emotionalResonance": "这是一个带有强烈负面情感的词。它传递出一种混杂着恐惧、震惊和无助的复杂情绪。听到这个词，人们脑中浮现的不是逃跑或尖叫，而更多的是一种僵在原地、瞳孔放大、大脑一片空白的画面。",
      "vividImagery": "想象一下，一位图书管理员深夜在古老的书库里整理书籍，一排书架突然毫无征兆地倒向他。在他被砸中的前一刻，他脸上的那种混杂着难以置信和纯粹恐惧的、凝固住的表情，就是'horror-struck'。",
      "etymologicalEssence": "这是一个非常直观的复合词。由'horror'（源自拉丁语，意为'战栗，恐惧'）和'struck'（动词'strike'的过去分词，意为'被打击'）构成。它的字面意思就是‘被恐惧所打击’，生动地描绘了那种仿佛被一股无形力量击中而动弹不得的惊恐状态。"
    },
    "usageExamples": [
      {
        "category": "对恐怖场景的反应",
        "examples": [
          {
            "english": "He stood horror-struck as he watched the house go up in flames.",
            "translation": "他惊恐地站在那里，眼睁睁地看着房子被熊熊大火吞噬。",
            "audioUrl": "https://r2.senseword.app/audio/en/sentences/horror-struck_ex1.mp3",
            "phraseBreakdown": [
              {
                "phrase": "He stood horror-struck",
                "translation": "他惊恐地站在那里",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex1_p1.mp3"
              },
              {
                "phrase": "as he watched",
                "translation": "眼睁睁地看着",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex1_p2.mp3"
              },
              {
                "phrase": "the house go up in flames",
                "translation": "房子被熊熊大火吞噬",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex1_p3.mp3"
              }
            ]
          }
        ]
      },
      {
        "category": "对惊人消息的反应",
        "examples": [
          {
            "english": "The reporters were horror-struck by the testimony of the witness.",
            "translation": "记者们被目击者的证词惊得目瞪口呆。",
            "audioUrl": "https://r2.senseword.app/audio/en/sentences/horror-struck_ex2.mp3", // 第二次写入
            "phraseBreakdown": [
              {
                "phrase": "The reporters were horror-struck",
                "translation": "记者们惊得目瞪口呆",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex2_p1.mp3"
              },
              {
                "phrase": "by the testimony",
                "translation": "被证词",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex2_p2.mp3"
              },
              {
                "phrase": "of the witness",
                "translation": "目击者的",
                "audioUrl": "https://r2.senseword.app/audio/en/phrases/horror-struck_ex2_p3.mp3"
              }
            ]
          }
        ]
      }
    ],
    "usageScenarios": [
      {
        "category": "文学与电影",
        "relevance": "常用词",
        "context": "在恐怖、悬疑或灾难类小说和电影中，用于生动描绘角色在关键时刻的惊恐反应，以渲染紧张气氛。"
      },
      {
        "category": "新闻报道",
        "relevance": "相关词",
        "context": "在报道突发灾难、事故或暴力事件时，用以形容目击者或幸存者的状态。"
      }
    ],
    "collocations": [
      {
        "type": "典型句式",
        "pattern": "to be / stand / look + horror-struck",
        "examples": [
          {
            "collocation": "He was horror-struck.",
            "translation": "他惊恐万分。"
          },
          {
            "collocation": "She looked horror-struck.",
            "translation": "她看起来吓坏了。"
          }
        ]
      }
    ],
    "usageNotes": [
      {
        "aspect": "语法功能",
        "explanation": "'horror-struck'是一个形容词，通常用作表语（跟在be动词后）或后置定语（放在名词后），用来修饰人的状态。",
        "examples": [
          {
            "sentence": "The audience, horror-struck, fell silent.",
            "translation": "惊恐万分的观众们陷入了沉默。（后置定语）"
          }
        ]
      }
    ],
    "synonyms": [
      {
        "word": "petrified",
        "explanation": "意思非常接近，都含有“吓得动弹不得”的含义，'petrified'（石化）的比喻意味更强，形象地描绘了人像石头一样僵住的状态。",
        "examples": [
          {
            "sentence": "I was petrified with fear when I heard footsteps behind me.",
            "translation": "当我听到身后的脚步声时，我吓得僵住了。"
          }
        ]
      },
      {
        "word": "aghast",
        "explanation": "同样表示惊骇，但'aghast'更多了一层因道德上的厌恶或震惊而产生的惊骇，而'horror-struck'更聚焦于纯粹的、视觉或听觉冲击带来的恐惧。",
        "examples": [
          {
            "sentence": "She was aghast at the suggestion that she had lied.",
            "translation": "对于她撒了谎的这种说法，她感到惊骇和愤怒。"
          }
        ]
      }
    ]
  }
}
```
---

### **SenseWord 单词内容与音频端到端生成技术方案 (v3.0 - 最终版)**

**文档状态**: 已批准，可进入执行阶段
**制定日期**: 2025年6月23日
**核心理念**: 串行验证，分层生成，异步解耦

#### **1. 核心流程概述 (Core Flow Overview)**

本方案定义了一个完整的、端到端的工作流，用于处理用户对新单词的查询请求。整个流程被清晰地划分为两个阶段，由两个独立的Worker负责，通过数据库的`ttsStatus`字段进行通信：

1.  **第一阶段 (实时处理 - by `api-worker`)**: 当用户查询一个新词时，此Worker负责**实时**生成核心文本内容和**单词本身**的发音（P0级音频），并立即将这份“部分完整”的数据返回给用户，同时在数据库中将任务标记为“待处理后续音频”。
2.  **第二阶段 (异步处理 - by `tts-worker`)**: 一个定时的后台Worker会扫描数据库，找到那些“待处理后续音频”的任务，并在后台**异步**生成所有**例句和短语**的发音（P1级音频），最终将数据更新为“完整状态”。

#### **2. 关键数据契约 (Key Data Contracts)**

* **数据库核心表**: `word_definitions`
* **关键状态机字段**: `ttsStatus` (TEXT)，其生命周期流转为：
    1.  `pending`
    2.  `processing_phrases` (由`api-worker`在首次写入时设置)
    3.  `processing_locked` (由`tts-worker`在处理时设置，防止重复处理)
    3.  `completed` (由`tts-worker`在处理完成后更新)
    4.  `failed` (处理失败时的状态)
* **核心数据字段**: `contentJson` (TEXT)，其内部的`audioUrl`字段将被分两个阶段填充。

---

#### **3. 端到端函数契约补间链 (End-to-End Function Contract Tweening Chain)**

**第一部分：`api-worker` - 实时处理流程**

**[FC-A1]: API端点主处理器 `handleWordLookupRequest`**
* **职责**: 作为总调度，认证请求，并串行执行查询、生成、与P0级TTS任务。
* **所在文件**: `backend/src/workers/api-worker/index.ts`
>>>>> **输入**: `Request` (e.g., `GET /api/word/progresive?lang=zh`)
<<<<< **输出**: `Response` (一个**部分包含**`audioUrl`的`WordDefinitionResponse`)

**[FC-A2]: AI内容生成服务 `generateContent`**
* **职责**: **(流程第一步)** 接收用户输入，调用AI模型，返回**包含正确单词**的核心内容。
* **函数签名**: `generateContent(userInput: string, language: string): Promise<AIGeneratedData>`
* **所在文件**: `backend/src/workers/api-worker/services/ai.service.ts`
>>>>> **输入**: `{ userInput: "progresive", language: "zh" }`
<<<<< **输出**: `AIGeneratedData` (包含已修正单词`progressive`的对象)

**[FC-A3]: P0级TTS生成服务 `generateWordAudio`**
* **职责**: **(流程第二步)** 接收从[FC-A2]返回的、**100%正确的单词**，调用Azure TTS服务生成单词本身的音频。
* **函数签名**: `generateWordAudio(correctedWord: string): Promise<{ BrE: string, NAmE: string }>`
* **所在文件**: `backend/src/workers/api-worker/services/tts.service.ts`
>>>>> **输入**: `correctedWord: "progressive"`
<<<<< **输出**: `{ BrE: "...", NAmE: "..." }` (两个真实的audioUrl)

**[FC-A4]: 初始数据记录构建与存储服务 `buildAndSaveInitialRecord`**
* **职责**: **(流程第三步)** 将AI文本内容和P0级音频URL组装成一个完整的初始记录，**设置`ttsStatus`为`'processing_phrases'`**，并将其`INSERT`到D1数据库。
* **函数签名**: `buildAndSaveInitialRecord(db: D1Database, ...): Promise<DatabaseRecord>`
* **所在文件**: `backend/src/workers/api-worker/services/database.service.ts`
>>>>> **输入**: 包含`correctedWord`, `language`, `aiData`, `wordAudioUrls`等所有信息的对象。
<<<<< **输出**: `DatabaseRecord` (已存入数据库的、`ttsStatus`为`processing_phrases`的记录)

---

**第二部分：`tts-worker` - 异步处理流程**

**[FC-T1]: 定时任务触发器 `handleScheduledTrigger`**
* **职责**: 由Cron Trigger定时唤醒，启动P1级音频生成流程。
* **函数签名**: `handleScheduled(event: ScheduledEvent, env: Env): Promise<void>`
* **所在文件**: `backend/src/workers/tts-worker/index.ts`
>>>>> **输入**: `ScheduledEvent`
<<<<< **输出**: `void`

**[FC-T2]: 待办任务获取与锁定服务 `fetchAndLockPendingJobs`**
* **职责**: 查询D1数据库，获取一批`ttsStatus`为`'processing_phrases'`的记录，并立即将其状态更新为`'processing_locked'`，以防重复处理。
* **函数签名**: `fetchAndLockPendingJobs(db: D1Database, limit: number): Promise<DatabaseRecord[]>`
* **所在文件**: `backend/src/workers/tts-worker/services/database.service.ts`
>>>>> **输入**: `{ limit: number }`
<<<<< **输出**: `DatabaseRecord[]`

**[FC-T3]: 批量音频生成与上传服务 `generateAndUploadAllPhraseAudio`**
* **职责**: 解析`contentJson`，提取所有例句和短语文本，调用Azure TTS生成音频，并上传至R2。
* **函数签名**: `generateAndUploadAllPhraseAudio(r2: R2Bucket, ...): Promise<Map<string, string>>`
* **所在文件**: `backend/src/workers/tts-worker/services/audio.service.ts`
>>>>> **输入**: `DatabaseRecord`
<<<<< **输出**: `Map<string, string>` (从“文本片段”映射到其“R2音频URL”的字典)

**[FC-T4]: 最终状态更新服务 `finalizeRecordInDB`**
* **职责**: 将所有新生成的音频URL更新回D1记录的`contentJson`字段内，并将`ttsStatus`更新为`'completed'`。
* **函数签名**: `finalizeRecordInDB(db: D1Database, ...): Promise<void>`
* **所在文件**: `backend/src/workers/tts-worker/services/database.service.ts`
>>>>> **输入**: `{ record: DatabaseRecord, urlMap: Map<string, string> }`
<<<<< **输出**: `void`

---
这份完整的端到端技术方案，清晰地定义了两个Worker如何通过数据库状态进行协作，共同完成一个从“无”到“完全体”的内容生成过程。它既保证了用户首次查询的快速响应，又确保了所有衍生数据（P1级音频）能够被异步、可靠地补全。
-----

#### **4. 客户端轮询接口规范**

  * **Endpoint**: `GET /api/word/{wordName}/status?lang={langCode}`
  * **描述**: 一个轻量级的、只读的接口，用于客户端查询TTS任务状态。
  * **成功响应 (200 OK)**:
    ```json
    {
      "word": "progressive",
      "language": "zh",
      "ttsStatus": "completed" 
    }
    ```

#### **5. 音频文件存储规范 (Cloudflare R2)**

  * **命名约定**: 采用确定性路径，便于客户端直接拼接或后台管理。
      * **例句**: `/{lang}/sentences/{word}_{example_index}.mp3` (e.g., `/en/sentences/progressive_ex1.mp3`)
      * **短语**: `/{lang}/phrases/{word}_{example_index}_{phrase_index}.mp3` (e.g., `/en/phrases/progressive_ex1_p1.mp3`)

-----

这份专项技术方案，已将TTS生成的任务，从宏大蓝图中清晰地剥离出来。它包含了所有必要的契约、流程和规范，可以作为一个独立的开发单元，被立即投入实现。