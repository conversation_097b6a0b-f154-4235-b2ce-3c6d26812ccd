# 045｜getDailyWord功能彻底移除影响分析

## 会议信息
- **日期**: 2025-07-02
- **主题**: 分析彻底移除 getDailyWord 功能的影响范围和所需更改
- **参与者**: 用户、AI助手

## 功能概述

`getDailyWord` 是 SenseWord 项目中的每日一词推荐系统，包含以下核心组件：
- AI 智能候选词评估
- 确定性选举服务（基于日期哈希）
- 分布式配置管理（Cloudflare KV）
- 自动化调度服务（Cron Worker）
- 多层降级保护机制

## 需要移除的文件清单

### 1. 后端 Cloudflare Workers

#### 每日一词 Cron Worker（完整删除）
- `cloudflare/workers/daily-word-cron/` - 整个目录
  - `src/index.ts` - Cron Worker 主入口
  - `src/services/election.service.ts` - 选举服务
  - `src/types/cron-types.ts` - Cron 类型定义
  - `package.json` - 依赖配置
  - `wrangler.toml` - Cloudflare 配置
  - `tsconfig.json` - TypeScript 配置

#### API Worker 中的每日一词相关代码
- `cloudflare/workers/api/src/services/daily-word.service.ts` - 删除整个文件
- `cloudflare/workers/api/src/types/daily-word-types.ts` - 删除整个文件

### 2. 数据库相关

#### 数据库迁移文件
- `cloudflare/d1/migrations/0002_add_daily_word_candidate.sql` - 删除文件

#### 数据库字段清理
需要执行反向迁移，删除以下字段：
- `word_definitions.isWordOfTheDayCandidate` - 删除字段
- `idx_isWordOfTheDayCandidate` - 删除索引

### 3. iOS 前端代码

#### API 模型
- `iOS/SensewordApp/Models/API/WordAPIModels.swift`
  - 删除 `DailyWordResponse` 结构体（第140-143行）
  - 删除 `DailyWordErrorResponse` 结构体（第200-202行）
  - 删除 `DailyWordError` 结构体（第205-208行）
  - 删除 `DailyWordErrorCode` 枚举（第211-213行）

#### API 适配器
- `iOS/SensewordApp/Services/Adapters/WordAPIAdapter.swift`
  - 删除 `getDailyWord()` 方法（第97-135行）
  - 从 `WordAPIAdapterProtocol` 中删除 `getDailyWord()` 方法声明（第15行）

#### 主界面组件
- `iOS/SensewordApp/Views/Main/MainContentView.swift`
  - 删除 `dailyWordContent` 状态变量
  - 删除 `isLoadingDailyWord` 状态变量
  - 删除 `dailyWordError` 状态变量
  - 删除 `loadDailyWordIfNeeded()` 方法（第327-375行）
  - 删除每日一词相关的 UI 逻辑和错误处理

#### 推荐系统
- `iOS/SensewordApp/Services/RecommendationArrayManager.swift`
  - 删除 `fetchDailyWord()` 方法（第580-591行）
  - 从 `APIServiceProtocol` 中删除 `fetchDailyWord()` 方法
  - 从 `RealAPIService` 中删除 `fetchDailyWord()` 实现（第768-771行）

## 需要修改的文件清单

### 1. 后端 API Worker

#### 主路由文件
- `cloudflare/workers/api/src/index.ts`
  - 删除每日一词相关的导入（第5行和第8行）
  - 删除每日一词 API 路由处理（第78-80行）
  - 删除 `handleDailyWordAPI` 函数（第761-800行左右）

#### AI 服务
- `cloudflare/workers/api/src/services/ai.service.ts`
  - 删除 AI 提示词中关于每日一词候选评估的部分
- `cloudflare/workers/api/assets/prompts/senseword_ai-v7.md`
  - 删除每日一词候选评估相关的提示词内容

#### 单词服务
- `cloudflare/workers/api/src/services/word.service.ts`
  - 删除保存时对 `isWordOfTheDayCandidate` 字段的处理

### 2. 配置文件

#### Cloudflare 配置
- `cloudflare/workers/api/wrangler.toml`
  - 删除 CONFIG_KV 绑定（如果仅用于每日一词）

### 3. 文档和配置

#### KDD 文档
- `0-KDD - 关键帧驱动开发/02-KDD/ARCHIVE/KDD-010-每日一词推荐/` - 整个目录可以移动到归档
- 相关技术方案文档需要标记为已废弃

## 数据库清理脚本

需要创建反向迁移脚本：

````sql
-- 反向迁移：移除每日一词相关字段
-- 删除索引
DROP INDEX IF EXISTS idx_isWordOfTheDayCandidate;

-- 删除字段
ALTER TABLE word_definitions DROP COLUMN isWordOfTheDayCandidate;
````

## Cloudflare 基础设施清理

### KV 命名空间
- 删除 `CONFIG_KV` 命名空间（ID: 34d6ce52ea0b4929a656c4f50977ba74）
- 清理所有 `daily-word:*` 键值对

### Cron 触发器
- 删除每小时执行的 Cron 触发器配置
- 删除 daily-word-cron Worker 部署

## 影响评估

### 正面影响
- **简化架构**: 移除复杂的每日一词选举和调度系统
- **降低成本**: 减少 Cloudflare Workers 和 KV 存储使用
- **减少维护**: 消除 Cron 任务和相关监控需求
- **提升性能**: 主界面不再需要加载每日一词数据

### 潜在风险
- **用户体验**: 主界面将失去每日一词功能
- **数据丢失**: 已生成的候选词标记数据将被删除
- **API 兼容性**: 现有客户端调用 `/api/v1/daily-word` 将返回 404

## 建议的移除步骤

### 阶段一：前端功能禁用
1. 修改 `MainContentView.swift`，注释掉每日一词加载逻辑
2. 测试主界面在没有每日一词的情况下是否正常工作

### 阶段二：后端 API 禁用
1. 在 API Worker 中注释掉每日一词路由
2. 停止 Cron Worker 的定时任务

### 阶段三：代码清理
1. 删除所有相关文件和代码
2. 执行数据库反向迁移
3. 清理 Cloudflare 基础设施

### 阶段四：文档更新
1. 更新 API 文档，移除每日一词相关接口
2. 归档相关的 KDD 文档

## 替代方案

如果需要保留类似功能，可以考虑：
1. **简化版本**: 在前端硬编码一些推荐单词，定期手动更新
2. **用户驱动**: 基于用户搜索历史推荐热门单词
3. **随机推荐**: 从现有词库中随机选择单词展示

## 总结

移除 `getDailyWord` 功能涉及：
- **删除文件**: 约 15+ 个文件
- **修改文件**: 约 8+ 个文件  
- **数据库更改**: 1 个反向迁移
- **基础设施清理**: KV 命名空间和 Cron Worker

建议分阶段执行，确保每个步骤都经过充分测试。
