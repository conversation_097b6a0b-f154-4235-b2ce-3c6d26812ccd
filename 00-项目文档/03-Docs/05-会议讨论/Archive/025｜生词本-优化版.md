# SenseWord 用户生词本管理功能设计方案

## 📋 会议背景
本次会议讨论了用户生词本管理功能的设计方案，这是产品的核心个性化功能，需要通过**添加、移除、查询**三个独立API能力来支撑，并涉及新增数据库表的设计。

---

## 1. 功能需求定义

### 1.1 核心功能
**用户生词本管理**：跨越三个函数契约的完整个性化功能，管理用户和单词之间的多对多关系。

### 1.2 功能组成
- **添加生词**: 将单词添加到用户生词本
- **移除生词**: 从用户生词本中移除单词  
- **查询生词**: 获取用户的完整生词列表

---

## 2. 数据库设计方案

### 2.1 新增表：`bookmarks`

#### 2.1.1 设计理念
为保持`users`和`words`表的纯粹性，增加独立的`bookmarks`关系表是最佳实践。

#### 2.1.2 表结构设计
```sql
-- 生词本/收藏表
CREATE TABLE bookmarks (
    user_id TEXT NOT NULL,              -- 用户ID，关联users表
    word TEXT NOT NULL,                 -- 单词文本
    language TEXT NOT NULL,             -- 语言代码 (zh, en, de等)
    created_at TEXT NOT NULL,           -- 收藏时间 (ISO 8601格式)

    -- 复合主键，确保一个用户对一个词只能收藏一次
    PRIMARY KEY (user_id, word, language),
    
    -- 外键约束，用户删除时级联删除收藏记录
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引优化查询性能
CREATE INDEX idx_bookmarks_user_id ON bookmarks(user_id);
CREATE INDEX idx_bookmarks_created_at ON bookmarks(created_at);
```

### 2.2 表设计特点

| 设计特点 | 价值说明 |
|---------|---------|
| **复合主键** | 防止重复收藏，确保数据一致性 |
| **级联删除** | 用户删除时自动清理相关收藏记录 |
| **轻量设计** | 只存储必要字段，保持表结构简洁 |
| **索引优化** | 提升查询性能，支持高并发访问 |

---

## 3. API功能设计

### 3.1 [BOOKMARK-FC-01]: 添加生词

#### 3.1.1 基本信息
- **职责**: 将一个单词添加到当前用户的生词本中
- **HTTP方法**: `POST /api/v1/bookmarks`
- **认证要求**: 需要用户登录状态

#### 3.1.2 数据结构
```typescript
// 请求体
interface AddBookmarkRequest {
  word: string;     // 要收藏的单词
  language: string; // 语言代码
}

// 响应体
interface AddBookmarkResponse {
  success: true;
  message: string;
}
```

#### 3.1.3 后端实现逻辑
```typescript
async function addBookmark(
  request: AddBookmarkRequest,
  userId: string,
  env: Env
): Promise<AddBookmarkResponse> {
  
  console.log(`[生词本] 用户${userId}添加生词: ${request.word}`);
  
  try {
    const stmt = env.DB.prepare(`
      INSERT INTO bookmarks (user_id, word, language, created_at) 
      VALUES (?, ?, ?, ?)
    `).bind(
      userId,
      request.word,
      request.language,
      new Date().toISOString()
    );
    
    await stmt.run();
    
    console.log(`[生词本] 生词添加成功: ${request.word}`);
    
    return {
      success: true,
      message: '生词添加成功'
    };
    
  } catch (error) {
    // 处理重复添加的情况
    if (error.message.includes('UNIQUE constraint failed')) {
      console.log(`[生词本] 生词已存在: ${request.word}`);
      return {
        success: true,
        message: '该单词已在生词本中'
      };
    }
    
    console.error('[生词本] 添加生词失败:', error);
    throw error;
  }
}
```

### 3.2 [BOOKMARK-FC-02]: 移除生词

#### 3.2.1 基本信息
- **职责**: 从当前用户的生词本中移除一个单词
- **HTTP方法**: `DELETE /api/v1/bookmarks`
- **认证要求**: 需要用户登录状态

#### 3.2.2 数据结构
```typescript
// 请求体
interface RemoveBookmarkRequest {
  word: string;     // 要移除的单词
  language: string; // 语言代码
}

// 响应体
interface RemoveBookmarkResponse {
  success: true;
  message: string;
}
```

#### 3.2.3 后端实现逻辑
```typescript
async function removeBookmark(
  request: RemoveBookmarkRequest,
  userId: string,
  env: Env
): Promise<RemoveBookmarkResponse> {
  
  console.log(`[生词本] 用户${userId}移除生词: ${request.word}`);
  
  const stmt = env.DB.prepare(`
    DELETE FROM bookmarks 
    WHERE user_id = ? AND word = ? AND language = ?
  `).bind(userId, request.word, request.language);
  
  const result = await stmt.run();
  
  if (result.changes > 0) {
    console.log(`[生词本] 生词移除成功: ${request.word}`);
    return {
      success: true,
      message: '生词移除成功'
    };
  } else {
    console.log(`[生词本] 生词不存在: ${request.word}`);
    return {
      success: true,
      message: '该单词不在生词本中'
    };
  }
}
```

### 3.3 [BOOKMARK-FC-03]: 获取生词列表

#### 3.3.1 基本信息
- **职责**: 获取当前用户的所有生词
- **HTTP方法**: `GET /api/v1/bookmarks`
- **认证要求**: 需要用户登录状态

#### 3.3.2 数据结构
```typescript
// 响应体
interface BookmarkList_BE {
  bookmarks: BookmarkItem[];
  total: number;
}

interface BookmarkItem {
  word: string;      // 单词文本
  language: string;  // 语言代码
  addedAt: string;   // 添加时间 (ISO 8601格式)
}
```

#### 3.3.3 后端实现逻辑
```typescript
async function getBookmarkList(
  userId: string,
  env: Env
): Promise<BookmarkList_BE> {
  
  console.log(`[生词本] 获取用户${userId}的生词列表`);
  
  const stmt = env.DB.prepare(`
    SELECT word, language, created_at 
    FROM bookmarks 
    WHERE user_id = ? 
    ORDER BY created_at DESC
  `).bind(userId);
  
  const { results } = await stmt.all();
  
  const bookmarks: BookmarkItem[] = results.map(row => ({
    word: row.word,
    language: row.language,
    addedAt: row.created_at
  }));
  
  console.log(`[生词本] 返回${bookmarks.length}个生词`);
  
  return {
    bookmarks,
    total: bookmarks.length
  };
}
```

---

## 4. 存储成本分析

### 4.1 单条记录存储分析

#### 4.1.1 字段大小估算

| 字段 | 类型 | 估算大小(字节) | 说明 |
|-----|------|---------------|------|
| `user_id` | TEXT | 36 | Apple/Google用户ID，按UUID长度估算 |
| `word` | TEXT | 15 | 英文单词平均5字母，按15字母超长单词估算 |
| `language` | TEXT | 2 | 语言代码，如"zh", "en" |
| `created_at` | TEXT | 24 | ISO 8601时间戳格式 |
| **总计** | - | **约77字节** | 保守估算为100字节/条 |

### 4.2 存储容量推演

#### 4.2.1 理论最大容量
```
D1存储上限: 10 GB = 10,737,418,240 字节
理论最大收藏量: 10,737,418,240 ÷ 100 ≈ 1.07亿条收藏记录
```

#### 4.2.2 实际场景推演
**极端成功场景假设**:
- App拥有 **100万** 付费用户
- 平均每用户收藏 **100个** 单词（现实中的极高平均数）

```
总收藏记录数: 1,000,000 × 100 = 1亿条
所需存储空间: 1亿条 × 100字节 ≈ 9.3 GB
```

**现实场景推演**:
- 平均每用户收藏 **20-30个** 单词
- 所需存储空间: **2-3 GB**

### 4.3 与words表对比

| 表名 | 单行大小 | 存储比例 |
|-----|---------|---------|
| `words` | 约6.6KB | 1条记录 |
| `bookmarks` | 约100字节 | 66条记录 |

**结论**: `words`表是真正的存储消耗大户，`bookmarks`表的存储压力完全可以忽略不计。

---

## 5. 实施策略

### 5.1 开发阶段规划

| 阶段 | 任务内容 | 预估工期 | 关键交付物 |
|-----|---------|---------|-----------|
| **Phase 1** | 数据库表创建和索引 | 1天 | bookmarks表结构和索引 |
| **Phase 2** | 后端API实现 | 2天 | 三个核心API端点 |
| **Phase 3** | 前端UI集成 | 2天 | 生词本管理界面 |
| **Phase 4** | 功能测试 | 1天 | 完整功能流程验证 |

### 5.2 API端点汇总

| 端点 | 方法 | 功能 | 状态码 |
|-----|------|------|--------|
| `/api/v1/bookmarks` | POST | 添加生词 | 200/409 |
| `/api/v1/bookmarks` | DELETE | 移除生词 | 200/404 |
| `/api/v1/bookmarks` | GET | 获取列表 | 200 |

### 5.3 错误处理策略

#### 5.3.1 常见错误场景
- **重复添加**: 返回友好提示，不报错
- **移除不存在**: 返回成功状态，提示不存在
- **数据库异常**: 记录日志，返回通用错误

#### 5.3.2 错误响应格式
```typescript
interface ErrorResponse {
  success: false;
  error: string;
  message: string;
}
```

---

## 6. 性能优化建议

### 6.1 查询优化
- **索引使用**: 确保user_id和created_at字段有索引
- **分页支持**: 大量收藏时支持分页查询
- **缓存策略**: 考虑对频繁查询的用户列表进行缓存

### 6.2 并发处理
- **事务管理**: 确保添加/删除操作的原子性
- **重复请求**: 前端防抖，避免重复提交
- **批量操作**: 未来可考虑支持批量添加/删除

---

## 📊 方案总结

### 7.1 核心价值
`bookmarks`表是一个**高价值、低成本**的完美实体，能够清晰管理用户与单词之间的关系。

### 7.2 关键特性
- **关系型最佳实践**: 清晰的多对多关系建模
- **存储成本极低**: 在任何可预见的用户规模下都不构成威胁
- **功能完整**: 支持添加、删除、查询的完整生词本管理
- **性能优秀**: 通过索引优化确保查询效率

### 7.3 架构确认
**"两表(+一个轻量级关系表)打天下"**战略依然坚如磐石，这个设计完美符合我们的极简架构哲学。

### 7.4 实施信心
请对生词本管理功能的存储开销**百分之百地放心**，它将为用户提供优秀的个性化体验，同时保持系统的简洁和高效。
