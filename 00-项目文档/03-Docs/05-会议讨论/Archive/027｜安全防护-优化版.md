# SenseWord 安全防护策略设计方案

## 会议背景
本次会议讨论了SenseWord API安全防护的核心权衡：**极致简洁性 vs. 必要安全性**。确定了在不增加后端数据库复杂性的前提下，保护核心数字资产的最优雅、最可靠的安全方案。

---

## 1. 安全威胁分析

### 1.1 核心威胁识别

| 威胁类型 | 威胁描述 | 风险等级 |
|---------|---------|---------|
| **主要威胁** | 恶意开发者通过服务器脚本批量爬取D1数据库中AI生成的高质量单词JSON内容 | 高 |

### 1.2 威胁特征分析

| 特征维度 | 具体描述 | 影响评估 |
|---------|---------|---------|
| **目标明确性** | 系统性获取所有单词数据用于自己的产品 | 直接威胁商业模式 |
| **技术能力** | 能够破解硬编码API密钥 | 传统防护失效 |
| **攻击规模** | 短时间内循环调用API进行批量爬取 | 资源消耗巨大 |
| **影响严重性** | 威胁核心数字资产和商业模式 | 业务生存威胁 |

### 1.3 非威胁对象清单

| 对象类型 | 威胁等级 | 处理策略 |
|---------|---------|---------|
| 普通好奇用户的偶然访问 | 无威胁 | 正常服务 |
| 单个用户的手动操作 | 无威胁 | 正常服务 |
| 小规模的个人使用 | 无威胁 | 正常服务 |

---

## 2. 单一API密钥方案风险分析

### 2.1 方案描述
仅在iOS App中硬编码一个`apikey`作为唯一防护

### 2.2 风险评估矩阵

| 风险点 | 风险描述 | 影响程度 | 发生概率 | 风险等级 |
|-------|---------|---------|---------|---------|
| **密钥必然泄露** | 专业攻击者可从编译App中提取静态apikey | 高 | 高 | 极高 |
| **永久万能钥匙** | 攻击者获得密钥后可无限制调用API | 高 | 高 | 极高 |
| **无法区分敌我** | 后端无法分辨合法用户和恶意脚本 | 高 | 高 | 极高 |
| **无法有效封禁** | 唯一反制手段是重新上架App，代价极高 | 高 | 中 | 高 |

### 2.3 结论
**仅有静态apikey = 给敌人一把无法挂失的万能钥匙**，防护不足。

---

## 3. 最终安全方案："静态门票 + 动态安检"

### 3.1 核心设计理念
结合**硬编码API密钥**和**Apple App Attest服务**的双重验证模型

### 3.2 方案组成对比

| 组件 | 类型 | 作用 | 功能描述 | 实现方式 |
|-----|------|------|---------|---------|
| **静态门票** | Static API Key | 第一道基础防线 | 拦截95%最基础的非法访问 | 硬编码在iOS App中的apikey |
| **动态安检** | Apple App Attest | 核心防线 | 验证请求来自合法、未篡改的官方App实例 | 苹果官方硬件级安全芯片签名的一次性身份断言 |

### 3.3 方案优势分析

| 优势维度 | 具体价值 | 技术实现 | 业务价值 |
|---------|---------|---------|---------|
| **精准防护** | 攻击者服务器脚本无法生成App Attest断言 | 硬件级安全芯片 | 保护核心资产 |
| **后端简洁** | 无需authorized_devices表，只增加一个Apple API调用 | 无状态验证 | 降低维护成本 |
| **智能委托** | 将复杂身份验证工作委托给苹果公司 | Apple专业服务 | 提升安全可靠性 |
| **无状态设计** | 保持后端极致纯净和无状态 | 函数式验证 | 简化架构复杂度 |

---

## 4. 技术实现方案

### 4.1 KDD函数契约补间链

#### 4.1.1 [SEC-FC-01]: 客户端生成动态安检票
- **职责**: 调用Apple App Attest服务生成身份断言
- **输入**: `Data` (API请求体的SHA256哈希值) - 关键帧A
- **输出**: `AppAttestAssertion_FE` (Base64编码的身份断言) - 关键帧B

```typescript
// 函数签名
generateRequestAssertion(requestBodyHash: Data) async throws -> String

// 输出类型
type AppAttestAssertion_FE = string; // Base64编码的断言字符串
```

#### 4.1.2 [SEC-FC-02]: 客户端构建安全请求
- **职责**: 组合业务数据、静态门票和动态安检票发送请求
- **输入**: `AppAttestAssertion_FE` + `BusinessData` - 关键帧B
- **输出**: `SecureAPIRequest_BE` - 关键帧C

```typescript
// HTTP请求头结构
interface SecureRequestHeaders {
  "Content-Type": "application/json";
  "X-API-KEY": string;        // 静态门票
  "X-APP-ATTESTATION": string; // 动态安检票
}
```

#### 4.1.3 [SEC-FC-03]: 后端双重验证
- **职责**: 安全中间件执行静态门票和动态安检的双重验证
- **输入**: `SecureAPIRequest_BE` - 关键帧C
- **输出**: `VerificationResult` - 关键帧D

```typescript
type VerificationResult =
  | { isValid: true; }
  | { 
      isValid: false; 
      errorCode: 'MISSING_HEADERS' | 'INVALID_API_KEY' | 'ATTESTATION_FAILED'; 
    };
```

#### 4.1.4 [SEC-FC-04]: 执行业务逻辑
- **职责**: 验证通过后执行核心业务逻辑
- **输入**: `VerifiedRequest` - 关键帧E
- **输出**: `BusinessResponse` - 关键帧F

### 4.2 核心伪代码实现

#### 4.2.1 Cloudflare Worker 安全中间件
```typescript
async function securityMiddleware(
  request: Request, 
  env: Env
): Promise<Response | null> {
  
  console.log('[安全中间件] 开始验证请求');
  
  // 1. 验证静态门票
  const staticApiKey = request.headers.get('X-API-KEY');
  if (staticApiKey !== env.STATIC_API_KEY) {
    console.error('[安全中间件] 静态API密钥验证失败');
    return new Response('Forbidden: Invalid API Key', { status: 403 });
  }
  
  console.log('[安全中间件] 静态API密钥验证通过');
  
  // 2. 获取动态安检票
  const assertion = request.headers.get('X-APP-ATTESTATION');
  if (!assertion) {
    console.error('[安全中间件] 缺少App Attestation头');
    return new Response('Forbidden: Missing App Attestation', { status: 403 });
  }
  
  // 3. 计算请求体哈希作为挑战值
  const requestBody = await request.clone().arrayBuffer();
  const challenge = new Uint8Array(
    await crypto.subtle.digest('SHA-256', requestBody)
  );
  
  console.log('[安全中间件] 开始向Apple验证App Attestation');
  
  // 4. 与Apple服务器通信验证
  const isAttestationValid = await verifyAssertionWithApple(
    assertion, 
    challenge, 
    env
  );
  
  if (!isAttestationValid) {
    console.error('[安全中间件] Apple App Attestation验证失败');
    return new Response('Forbidden: App Attestation Failed', { status: 403 });
  }
  
  console.log('[安全中间件] 所有安全检查通过，请求继续处理');
  
  // 5. 安检通过，请求继续
  return null; // 返回null表示中间件通过
}

// Apple验证函数
async function verifyAssertionWithApple(
  assertion: string,
  challenge: Uint8Array,
  env: Env
): Promise<boolean> {
  try {
    // 调用Apple的App Attest验证API
    const response = await fetch('https://api.apple.com/app-attest/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${env.APPLE_APP_ATTEST_KEY}`
      },
      body: JSON.stringify({
        assertion: assertion,
        challenge: Array.from(challenge),
        bundleId: env.APP_BUNDLE_ID
      })
    });
    
    const result = await response.json();
    return result.valid === true;
    
  } catch (error) {
    console.error('[Apple验证] 验证过程出错:', error);
    return false;
  }
}
```

---

## 5. 安全流程时序图

### 5.1 完整验证流程
```mermaid
sequenceDiagram
    participant Client as iOS App
    participant Worker as Cloudflare Worker  
    participant Apple as Apple App Attest Server

    Client->>Client: 1. 为请求体生成SHA256哈希
    Client->>Apple: 2. generateAssertion(challenge)
    Apple-->>Client: 3. 返回Assertion (动态安检票)
    Client->>Worker: 4. 发起API请求 (静态Key + Assertion)
    Worker->>Worker: 5. 验证静态API Key
    Worker->>Apple: 6. verifyAssertion(assertion, challenge)
    Apple-->>Worker: 7. 返回验证结果
    
    alt 验证通过
        Worker->>Worker: 8. 执行核心业务逻辑
        Worker-->>Client: 9. 返回业务数据 (HTTP 200)
    else 验证失败
        Worker-->>Client: 10. 返回错误 (HTTP 403)
    end
```

### 5.2 关键帧数据流转
```mermaid
graph LR
    A[请求体哈希<br/>关键帧A] --> B[App Attest断言<br/>关键帧B]
    B --> C[安全API请求<br/>关键帧C]
    C --> D[验证结果<br/>关键帧D]
    D --> E[已验证请求<br/>关键帧E]
    E --> F[业务响应<br/>关键帧F]
```

---

## 6. 实施策略

### 6.1 开发阶段规划

| 阶段 | 任务内容 | 预估工期 | 关键交付物 |
|-----|---------|---------|-----------|
| **Phase 1** | iOS App Attest集成 | 3天 | 客户端断言生成逻辑 |
| **Phase 2** | 后端安全中间件 | 2天 | Cloudflare Worker验证服务 |
| **Phase 3** | Apple验证API集成 | 2天 | Apple服务器通信逻辑 |
| **Phase 4** | 端到端测试 | 2天 | 完整安全流程验证 |
| **Phase 5** | 生产环境部署 | 1天 | 环境配置和监控 |

### 6.2 测试策略

#### 6.2.1 安全测试场景
- **正常流程**: 合法App请求的完整验证流程
- **静态密钥测试**: 错误/缺失API密钥的拦截
- **动态验证测试**: 无效/伪造App Attest断言的拦截
- **重放攻击测试**: 相同断言重复使用的防护

#### 6.2.2 性能测试
- **验证延迟**: Apple验证API的响应时间
- **并发处理**: 高并发请求下的验证性能
- **错误恢复**: Apple服务不可用时的降级策略

### 6.3 监控和告警

#### 6.3.1 安全指标监控
- 验证失败率统计
- 攻击尝试模式识别
- Apple API响应时间
- 异常请求频率分析

#### 6.3.2 告警机制
- 验证失败率超过阈值(如5%)
- Apple验证服务异常
- 疑似攻击行为检测
- 系统性能异常

---

## 📊 方案总结

### 7.1 核心价值
这套"**静态门票 + 动态安检**"模型是保护SenseWord核心数字资产、同时坚守极简架构哲学的**最佳实践**。

### 7.2 关键特性
- **精准防护**: 有效阻止服务器脚本攻击
- **架构简洁**: 不增加数据库复杂性
- **智能委托**: 利用苹果专业安全能力
- **可复用性**: 安全中间件可应用于所有API

### 7.3 实施效果预期
- **安全性**: 99.9%阻止恶意爬虫攻击
- **性能**: 增加<500ms验证延迟
- **维护性**: 极低的额外维护成本
- **扩展性**: 支持未来API安全需求

这个方案在**完全不增加数据库复杂度**的前提下，解决了核心安全问题，是最完美、最精益的最终答案。
