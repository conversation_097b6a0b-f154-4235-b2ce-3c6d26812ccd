
### **SenseWord 后端技术规格说明书 (v1.0)**

**文档目的**: 本文档旨在精确定义SenseWord后端服务的数据库结构、核心API接口规范、数据传输对象（DTO）以及安全认证流程。它是连接产品战略与工程实现的“契约”。

-----

#### **1. 数据库设计 (Database Design)**

  * **数据库类型**: Cloudflare D1
  * **核心表**: `word_definitions`

**1.1. `word_definitions` 表结构 (SQL)**

```sql
-- SenseWord核心内容与状态管理表
CREATE TABLE word_definitions (
    -- === 核心标识符 (由系统写入) ===
    word TEXT NOT NULL,
    language TEXT NOT NULL,

    -- === AI生成的核心内容 (由AI生成) ===
    contentJson TEXT NOT NULL,

    -- === 系统管理的状态与运营字段 ===
    feedbackScore INTEGER NOT NULL DEFAULT 0,    -- [管理方: /api/feedback接口] 用户反馈评分
    isHumanReviewed INTEGER NOT NULL DEFAULT 0,  -- [管理方: 后台运营] 是否人工精修 (0=否, 1=是)
    ttsStatus TEXT NOT NULL DEFAULT 'pending',   -- [管理方: TTS处理服务] TTS音频生成状态
    
    -- === AI分析的或系统注入的元数据 (在首次生成时写入) ===
    difficulty TEXT,                             -- (来自AI) CEFR难度等级
    frequency TEXT,                              -- (来自AI) 词频
    relatedConcepts TEXT,                        -- (来自AI) 相关概念 (存储为JSON字符串)
    promptVersion TEXT NOT NULL,                 -- [管理方: Worker] 提示词版本
    
    -- === 标准时间戳 (由系统写入) ===
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,

    -- === 主键定义 ===
    PRIMARY KEY (word, language)
);

-- === 建议的索引 ===
CREATE INDEX idx_feedback_score ON word_definitions (feedbackScore);
CREATE INDEX idx_is_human_reviewed ON word_definitions (isHumanReviewed);
CREATE INDEX idx_tts_status ON word_definitions (ttsStatus);

```

-----

#### **2. API接口规范 (API Specification)**

**基地址**: `https://api.senseword.app/v1` (示例)

**2.1. 设备认证与动态密钥获取**

  * **Endpoint**: `POST /auth/register-device`
  * **描述**: 使用Apple `App Attest`凭证，为合法的设备实例注册并颁发一个长期的动态密钥。
  * **请求头 (Request Headers)**:
      * `Content-Type: application/json`
      * `X-Static-API-Key: [编译在App中的静态密钥]`
  * **请求体 (Request Body)**:
    ```typescript
    interface DeviceRegistrationRequest {
      attestationObject: string; // Base64编码的Apple Attestation Object
      challenge: string;         // 服务器下发的挑战字符串
      deviceInfo: {
        id: string; // 例如iOS的identifierForVendor
      };
    }
    ```
  * **成功响应 (200 OK)**:
    ```typescript
    interface DeviceRegistrationResponse {
      dynamicKey: string; // 新生成的、长期的动态密钥
      expiresAt: string;  // ISO 8601 格式的过期时间
    }
    ```
  * **错误响应 (4xx/5xx)**: 标准错误JSON `{ "error": { "code": "...", "message": "..." } }`。

**2.2. 单词查询/生成**

  * **Endpoint**: `GET /word/{wordName}?lang={langCode}`
  * **描述**: 查询一个英语单词的深度解析。如果数据库中不存在，则触发AI实时生成。
  * **请求头 (Request Headers)**:
      * `X-Static-API-Key: [静态密钥]`
      * `X-Dynamic-Key: [从/auth/register-device获取的动态密钥]`
  * **路径/查询参数**:
      * `wordName` (string, required): 要查询的英语单词。
      * `langCode` (string, required): 目标受众的母语代码, e.g., `zh`。
  * **成功响应 (200 OK)**:
      * 返回完整的“单词定义”JSON对象（见下方数据结构定义）。
  * **错误响应 (401, 404, 500)**: 标准错误JSON。

**2.3. 用户反馈**

  * **Endpoint**: `POST /feedback`
  * **描述**: 接收用户对特定单词解析的“赞”或“踩”的反馈。
  * **请求头 (Request Headers)**:
      * `Content-Type: application/json`
      * `X-Static-API-Key: [静态密钥]`
      * `X-Dynamic-Key: [动态密钥]`
  * **请求体 (Request Body)**:
    ```typescript
    interface FeedbackRequest {
      word: string;
      language: string;
      action: 'like' | 'dislike';
    }
    ```
  * **成功响应 (200 OK)**:
    ```typescript
    interface FeedbackResponse {
      success: true;
      newScore: number; // 更新后的分数
    }
    ```

-----

#### **3. 核心数据结构定义 (Data Transfer Objects - DTOs)**

这是我们的“函数契约补间链”中，最核心的“数据结构关键帧”，它精确定义了API的输出。

```typescript
// 完整单词查询的响应体
interface WordDefinitionResponse {
  word: string;
  metadata: WordMetadata;
  content: WordContent;
}

// 元数据对象
interface WordMetadata {
  wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare';
  relatedConcepts: string[];
}

// 核心内容对象 (根据V8.0提示词)
interface WordContent {
  difficulty: 'A1' | 'A2' | 'B1' | 'B2' | 'C1' | 'C2';
  phoneticSymbols: {
    type: 'BrE' | 'NAmE';
    symbol: string;
  }[];
  coreDefinition: string;
  contextualExplanation: {
    nativeSpeakerIntent: string;
    emotionalResonance: string;
    vividImagery: string;
    etymologicalEssence: string;
  };
  usageExamples: {
    category: string;
    examples: {
      english: string;
      translation: string;
      phraseBreakdown: {
        phrase: string;
        translation: string;
      }[];
    }[];
  }[];
  usageScenarios: {
    category: string;
    relevance: string;
    context: string;
  }[];
  collocations: {
    type: string;
    pattern: string;
    examples: {
      collocation: string;
      translation: string;
    }[];
  }[];
  usageNotes: {
    aspect: string;
    explanation: string;
    examples: {
      sentence: string;
      translation: string;
    }[];
  }[];
  synonyms: {
    word: string;
    explanation: string;
    examples: {
      sentence: string;
      translation: string;
    }[];
  }[];
}
```

-----

这份技术规格说明书，将作为您执行阶段的“法律”。它以机器可读、无歧义的方式，固化了我们所有的战略决策，为后续高效、精准的开发工作提供了最坚实的基础。

好的，我们继续执行计划。

将每一份计划书都当作独立的子任务来完成，是KDD方法论的核心。现在，我们将聚焦于整个产品的核心功能——**单词查询与实时生成**——并为其创建一份详尽的“函数契约补间链”。

这份文档将作为对我们之前制定的《SenseWord 后端技术规格说明书 (v1.0)》的**核心补充和实现蓝图**。

-----

### **SenseWord 后端技术规格说明书 (v1.0) - 附录B：单词查询/生成流程**

**文档目的**: 本附录使用“函数契约补间链”范式，精确定义了`GET /api/word/{wordName}`接口背后的完整数据处理流程和业务逻辑。

#### **1. 流程概述 (Flow Overview)**

当系统收到一个单词查询请求时，它将遵循一个“**缓存优先，按需生成**”的原则。首先，系统会检查该单词特定语言版本的内容是否已存在于我们的主数据库（D1）中。如果存在，则直接返回；如果不存在，则触发一个由AI驱动的、包含内容生成、数据组装和持久化存储的完整工作流。

#### **2. 函数契约补间链 (Function Contract Tweening Chain)**

**[FC-B1]: API端点主处理器 (Main API Endpoint Handler)**

  * **职责**: 作为`GET /api/word/{wordName}`的入口，负责认证动态密钥，解析请求参数，并编排后续的查询或生成流程，最终返回HTTP响应。
  * **函数签名**: `handleWordLookup(request: Request, env: Env): Promise<Response>`
  * **所在文件**: `cloudflare/workers/api/src/index.ts`

> > > > > **输入 (Input):** `Request`

一个标准的HTTP请求对象，包含了所有请求信息。

```typescript
// Example Request
// GET /api/v1/word/progressive?lang=zh
// Headers: { 'X-Dynamic-Key': '...' }
```

\<\<\<\<\< **输出 (Output):** `Response`

一个标准的HTTP响应对象。成功时Body为`WordDefinitionResponse`，失败时为标准错误JSON。

-----

**[FC-B2]: 数据库查询服务 (Database Lookup Service)**

  * **职责**: 根据`word`和`language`，查询D1数据库，检查对应的单词定义是否已存在。
  * **函数签名**: `findWordDefinition(db: D1Database, word: string, language: string): Promise<WordDefinitionRecord | null>`
  * **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

> > > > > **输入 (Input):** `{ word: string, language: string }`

```typescript
{
  word: "progressive",
  language: "zh"
}
```

\<\<\<\<\< **输出 (Output):** `WordDefinitionRecord | null`

如果找到，则返回数据库中的完整记录对象；如果未找到，则返回`null`。

```typescript
// 数据库记录的数据结构
interface WordDefinitionRecord {
  word: string;
  language: string;
  contentJson: string; // Stored as a JSON string
  feedbackScore: number;
  isHumanReviewed: number; // 0 or 1
  ttsStatus: 'pending' | 'completed' | 'failed';
  difficulty: string;
  frequency: string;
  relatedConcepts: string; // Stored as a JSON string of an array
  promptVersion: string;
  createdAt: string;
  updatedAt: string;
}
```

-----

**[FC-B3]: AI内容生成服务 (AI Content Generation Service)**

  * **职责**: 调用AI模型（使用V7.0版提示词），为一个新单词生成核心的教学内容和分析型元数据。路径地址：/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/Cloudflare/Prompt/senseword_ai-v7.md
  * **函数签名**: `generateWordContentFromAI(word: string, language: string): Promise<AIGeneratedData>`
  * **所在文件**: `cloudflare/workers/api/src/services/ai.service.ts`

> > > > > **输入 (Input):** `{ word: string, language: string }`

```typescript
{
  word: "progressive",
  language: "zh"
}
```

\<\<\<\<\< **输出 (Output):** `AIGeneratedData`

一个只包含由AI直接生成或分析得出的数据的对象。

```typescript
// AI生成的数据结构
interface AIGeneratedData {
  content: WordContent; // 我们在DTO中定义的核心内容对象
  metadata: {
    wordFrequency: 'High' | 'Medium' | 'Low' | 'Rare';
    relatedConcepts: string[];
  }
}
```

-----

**[FC-B4]: 新数据记录组装服务 (New Record Assembly Service)**

  * **职责**: 将AI生成的数据（来自FC-B3）与系统管理的初始状态数据进行合并，组装成一个完整的、可以存入数据库的记录。
  * **函数签名**: `assembleNewRecord(word: string, language: string, aiData: AIGeneratedData): NewWordDefinitionRecord`
  * **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

> > > > > **输入 (Input):** `{ word: string, language: string, aiData: AIGeneratedData }`

从上一步获取的、由AI生成的数据。

\<\<\<\<\< **输出 (Output):** `NewWordDefinitionRecord`

一个包含了所有数据库字段、并设定好初始值的完整记录对象。

```typescript
// 准备写入数据库的完整新记录
interface NewWordDefinitionRecord {
  word: string;
  language: string;
  contentJson: string; // aiData.content is stringified
  feedbackScore: 0; // Initial value
  isHumanReviewed: 0; // Initial value
  ttsStatus: 'pending'; // Initial value
  difficulty: string; // From aiData.content
  frequency: string; // From aiData.metadata
  relatedConcepts: string; // aiData.metadata.relatedConcepts is stringified
  promptVersion: "8.0"; // System-injected value
  createdAt: string; // Current ISO timestamp
  updatedAt: string; // Current ISO timestamp
}
```

-----

**[FC-B5]: 数据库写入服务 (Database Write Service)**

  * **职责**: 将组装好的新记录持久化存入D1数据库。
  * **函数签名**: `saveNewWordDefinition(db: D1Database, record: NewWordDefinitionRecord): Promise<boolean>`
  * **所在文件**: `cloudflare/workers/api/src/services/word.service.ts`

> > > > > **输入 (Input):** `NewWordDefinitionRecord`

与[FC-B4]的输出相同，一个准备写入数据库的完整记录。

\<\<\<\<\< **输出 (Output):** `boolean`

返回一个布尔值，表示写入操作是否成功。

-----

#### **3. 伪代码逻辑 (Pseudocode Logic)**

以下伪代码清晰地展示了主处理器`handleWordLookup`如何编排上述所有函数契约。

```typescript
async function handleWordLookup(request, env) {
  // === FC-B1 (开始): 认证与解析 ===
  // 1. 验证动态密钥 (省略具体实现)
  authenticateRequest(request.headers.get('X-Dynamic-Key'));
  
  // 2. 解析请求参数
  const word = request.params.wordName;
  const language = request.query.langCode;

  // === FC-B2: 数据库查询 ===
  // 3. 检查数据库中是否已存在该词条
  let existingRecord = await findWordDefinition(env.DB, word, language);

  // 4. 如果存在，直接返回结果
  if (existingRecord) {
    // 将数据库记录转换为API响应格式 (省略具体实现)
    const responseDto = formatRecordToResponse(existingRecord);
    return new Response(200, responseDto);
  }

  // === 如果不存在，则进入实时生成流程 ===
  
  // === FC-B3: AI内容生成 ===
  // 5. 调用AI服务生成核心内容
  const aiGeneratedData = await generateWordContentFromAI(word, language);

  // === FC-B4: 新数据记录组装 ===
  // 6. 组装一条完整的、可存入数据库的新记录
  const newRecord = assembleNewRecord(word, language, aiGeneratedData);

  // === FC-B5: 数据库写入 ===
  // 7. 将新记录存入D1数据库
  const success = await saveNewWordDefinition(env.DB, newRecord);
  if (!success) {
    throw new Error(500, "Failed to save new definition.");
  }
  
  // 8. 将新生成的记录转换为API响应格式并返回
  const newResponseDto = formatRecordToResponse(newRecord);
  return new Response(200, newResponseDto); // 返回200表示新建并成功返回
}
```