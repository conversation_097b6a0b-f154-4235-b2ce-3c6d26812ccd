# SenseWord 数据库架构简化方案

## 📋 会议背景
本次会议重点审视和更新数据库设计，确保数据库"骨架"能够完美支撑最终确立的"极简付费墙模型"和"极致简洁"的架构哲学。

---

## 1. Users 表设计优化

### 1.1 核心问题识别
现有`users`表设计坚实，但缺少支撑付费墙的**关键字段：用户订阅状态**

### 1.2 设计方案对比

| 方案 | 字段设计 | 优缺点分析 |
|-----|---------|-----------|
| **方案A** | `is_pro BOOLEAN` | ❌ 简单但功能受限，无法支持多种订阅周期 |
| **方案B** | `subscription_expires_at TEXT` | ✅ **推荐方案** - 灵活、自动计算、便于管理 |

### 1.3 推荐方案详解

#### 1.3.1 `subscription_expires_at` 字段优势
1. **支持多种订阅周期**: 月度、季度、年度等不同时长订阅包
2. **状态自动计算**: 通过时间比较自动计算`isPro`状态，无需手动更新
3. **便于管理**: 轻松查询即将到期的订阅，支持营销和提醒

#### 1.3.2 更新后的表结构
```sql
-- 用户基础信息表 (V2 - 已更新)
CREATE TABLE users (
    id TEXT PRIMARY KEY,                    -- 用户唯一标识符 (来自认证提供方)
    email TEXT NOT NULL UNIQUE,             -- 用户邮箱地址
    provider TEXT NOT NULL,                 -- 认证提供方 ('apple' 或 'google')
    displayName TEXT,                       -- 用户显示名称
    subscription_expires_at TEXT,           -- [新增] ISO 8601格式的订阅到期时间
    createdAt TEXT NOT NULL,                -- ISO 8601格式的账户创建时间
    updatedAt TEXT NOT NULL                 -- ISO 8601格式的最后更新时间
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
```

---

## 2. Authorized_Devices 表战略性审视

### 2.1 表设计初衷
该表设计用于防止API滥用的高级安全模型，通过动态密钥将请求与具体设备绑定，提供极高安全性。

### 2.2 权衡分析

#### 2.2.1 保留优点
- **最高级别安全防护**: 有效防止专业攻击者利用越狱设备进行大规模自动化请求

#### 2.2.2 保留缺点
- **显著复杂度**: 需要开发维护完整的设备注册、动态密钥生成、验证和吊销流程
- **用户状态耦合**: 与追求后端无状态的哲学相悖
- **维护负担**: 需处理用户更换设备、重装App等场景的密钥同步问题

### 2.3 决策建议

#### 2.3.1 核心建议
**在MVP阶段，彻底移除 `authorized_devices` 表，采用极简方案**

#### 2.3.2 简化合理性分析
1. **核心安全策略**: 依赖Apple的`App Attest`服务验证请求来源，过滤99%非人恶意请求
2. **风险接受**: 接受极小概率的技术高手滥用每日5次免费搜索的风险
3. **成本效益**: 防护成本远高于潜在AI调用损失
4. **生成即投资**: 每次AI生成都为核心资产添砖加瓦，即使是滥用触发的

---

## 3. 最终推荐架构

### 3.1 MVP V1 数据库设计
**只使用一张核心的`users`表**

```sql
-- 用户表 (最终推荐版)
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    provider TEXT NOT NULL,
    displayName TEXT,
    -- 核心商业逻辑字段 --
    subscription_expires_at TEXT,           -- ISO 8601格式订阅到期时间, NULL表示非Pro用户
    -- 标准时间戳 --
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
```

### 3.2 设计哲学体现
这次更新使数据库设计达到**前所未有的简洁**：
- 只包含支撑核心业务的**绝对必要**字段
- 移除所有"锦上添花"或"过度防御"的复杂性
- 完美践行"**奥卡姆剃刀**"和"**极致简洁**"设计哲学

---

## 4. 前端状态管理策略

### 4.1 核心决策
**将"每日免费搜索次数"状态完全放在前端(iOS App)管理**

### 4.2 架构决策权衡

#### 4.2.1 优点分析
| 优势维度 | 具体价值 |
|---------|---------|
| **后端零负担** | 保持"纯净"和"无状态"，无需新增数据库表、KV存储或额外逻辑 |
| **实现极简** | iOS客户端使用`UserDefaults`存储，几行代码实现，几乎零维护成本 |
| **用户体验佳** | 本地计数检查瞬间完成，无网络延迟，体验流畅 |

#### 4.2.2 需要接受的权衡
**唯一"缺点"**: 并非绝对安全，技术能力强的用户理论上可通过修改设备时间或越狱设备绕过限制

### 4.3 风险可接受性分析

#### 4.3.1 为什么风险完全可接受？
1. **滥用成本高，收益低**: 用户需付出巨大努力(如越狱手机)，仅获得每天几次额外免费搜索
2. **业务损失极小**: 极少数绕过用户每天只带来几次额外AI调用成本，完全可控
3. **核心安全防线坚固**: `App Attest`防止大规模自动化脚本攻击的核心壁垒依然存在

#### 4.3.2 实现方案
```typescript
// iOS客户端状态管理
interface SearchLimitState {
  lastResetDate: Date;      // 上次重置日期
  dailySearchCount: number; // 当天搜索次数
}

// 核心逻辑伪代码
function checkDailySearchLimit(): boolean {
  const today = new Date().toDateString();
  
  // 检查是否需要重置计数器
  if (lastResetDate !== today) {
    dailySearchCount = 0;
    lastResetDate = today;
    saveToUserDefaults();
  }
  
  // 检查是否超过限制
  if (!isPro && dailySearchCount >= 5) {
    showPaywallModal();
    return false;
  }
  
  return true;
}

function onSearchSuccess() {
  dailySearchCount++;
  saveToUserDefaults();
}
```

---

## 5. 架构决策总结

### 5.1 核心理念
我们做出的是一个**成熟的架构决策**：
- 将99%精力用于构建核心产品体验(通往市中心的高速公路)
- 而不是为罕见的"逃票"行为修建复杂防御工事(乡间小路收费站)

### 5.2 设计哲学体现
将搜索次数状态放在前端管理，是**精益、高效、低成本**开发哲学的最终体现。

### 5.3 决策对比表

| 决策维度 | 复杂方案 | 极简方案 ✅ |
|---------|---------|------------|
| **开发成本** | 高 - 需要完整设备授权系统 | 低 - 几行前端代码 |
| **维护负担** | 重 - 密钥管理、设备同步等 | 轻 - 基本无维护 |
| **安全级别** | 极高 - 防止所有可能滥用 | 高 - App Attest + 合理风险接受 |
| **用户体验** | 一般 - 设备绑定复杂流程 | 优秀 - 无感知限制 |
| **业务风险** | 零风险 | 极小可控风险 |
| **架构复杂度** | 高耦合 | 低耦合、无状态 |

### 5.4 最终结论
**极简方案完胜** - 在保持核心安全防护的前提下，实现了架构的极致简洁和开发效率的最大化。

---

## 📊 实施建议

### 6.1 数据库迁移计划
1. **Phase 1**: 更新`users`表结构，添加`subscription_expires_at`字段
2. **Phase 2**: 移除`authorized_devices`表相关代码和数据
3. **Phase 3**: 更新相关API接口，简化认证逻辑

### 6.2 前端实现计划
1. **Phase 1**: 实现本地搜索次数管理逻辑
2. **Phase 2**: 集成付费墙UI组件
3. **Phase 3**: 测试各种边界场景(时间修改、重装等)

### 6.3 监控策略
- 监控异常搜索模式，识别潜在滥用
- 跟踪付费转化率，验证限制策略有效性
- 收集用户反馈，优化限制体验

这个简化方案为SenseWord提供了一个既安全又简洁的数据库架构基础。
