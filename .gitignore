# ================================
# SenseWord iOS Project .gitignore
# ================================

# ================================
# macOS System Files
# ================================
.DS_Store
.localized
.Trashes
Thumbs.db
ehthumbs.db

# ================================
# Xcode & iOS Development
# ================================

# Build directories
build/
.build/
DerivedData/

# Xcode workspaces and projects
*.xcworkspace/
*.xcodeproj/project.xcworkspace/

# User-specific files
xcuserdata/
*.xcuserdatad/
*.xcscheme

# Archives and packages
*.xcarchive/
*.ipa
*.dSYM

# ================================
# Swift & Package Manager
# ================================

# Swift Package Manager
.swiftpm/

# Swift compilation artifacts
*.d
*.o
*.swiftdeps
*.swiftmodule
*.swiftdoc
*.abi.json
*.swiftsourceinfo
*.swiftinterface

# Swift test outputs
.swift-format/
.swift-test/
.jazzy/

# ================================
# Node.js & JavaScript
# ================================

# Dependencies
node_modules/
.npm/
.yarn/
.pnp/
.pnp.js

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ================================
# Python Development
# ================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# ================================
# Build Outputs & Caches
# ================================

# General build outputs
dist/
build/
public/build/
.cache/

# Dependency caches
.npm/
.yarn/

# Test coverage
coverage/
.nyc_output/

# ================================
# Database Files
# ================================

# SQLite databases
*.sqlite
*.sqlite3
*.db
*.db3
*.mdb

# Database backups
*.backup
*.db.backup

# ================================
# Log Files & Temporary Files
# ================================

# Log files
*.log
*.log.*
logs/

# Temporary files
*.tmp
*.bak
*.swp
*.swo
*.pid

# ================================
# Compression Archives
# ================================
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# ================================
# IDE Configuration Files
# ================================

# VS Code
.vscode/

# IntelliJ IDEA / Android Studio
.idea/
*.iml
*.iws
*.ipr

# Eclipse
.classpath
.project
.settings/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Visual Studio (Windows)
*.sln
*.suo
*.user
*.aps
*.bsc
*.ncb
*.sbr
*.pdb
*.obj
*.res
*.pch
*.idb
*_Res.c
*.vspscc
*.vssscc
*.vsp
*.buildlog
*.docstates

# JetBrains Rider
.Rider/

# ================================
# Environment & Configuration
# ================================

# Environment variables
.env
.env.local
.env.*.local

# Sensitive configurations
config/*.secret
credentials/

# ================================
# SenseWord Project Specific
# ================================

# Content assets and data
senseword-content-factory/content-assets/
SensewordData/dictionary/

# SensewordData build outputs
SensewordData/dist/
SensewordData/**/*.js
SensewordData/**/*.d.ts
SensewordData/**/*.js.map
SensewordData/**/*.d.ts.map

# Keep configuration files
!SensewordData/vitest.config.js
!SensewordData/jest.config.js
!SensewordData/webpack.config.js
!SensewordData/rollup.config.js

# SensewordData dependencies and caches
SensewordData/node_modules/
SensewordData/coverage/
SensewordData/.cache/
SensewordData/tmp/
SensewordData/temp/

# ================================
# Cloudflare Workers
# ================================

# Wrangler development files
**/.wrangler/
**/wrangler.log

# Worker build outputs
cloudflare/workers/**/dist/
cloudflare/workers/**/node_modules/

# SQLite WAL files
**/*.sqlite-shm
**/*.sqlite-wal

# ================================
# Data Migration & Processing
# ================================

# Large SQL migration files
*_migration_batch_*.sql
migration_sql_files/
sql/

# Temporary processing files
temp/
tmp/

# CSV exports and samples
**/csv/tts_quality_samples_*.csv

# ================================
# Development Tools
# ================================

# Claude AI
.claude/

# Jupyter notebooks
.ipynb_checkpoints/

# ================================
# Third-party Dependencies
# ================================

# Carthage
Carthage/Build/

# CocoaPods
Pods/

