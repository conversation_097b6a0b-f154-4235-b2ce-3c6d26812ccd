# SenseWord Backend Environment Variables
# 复制此文件为 .env.development 用于本地开发

# 环境标识
ENVIRONMENT=development

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_TOKEN_EXPIRY=15m
JWT_REFRESH_TOKEN_EXPIRY=7d

# Apple ID 配置
APPLE_CLIENT_ID=com.senseword.app.dev
APPLE_TEAM_ID=XXXXXXXXXX

# App Store 配置
APP_STORE_SHARED_SECRET=your-app-store-shared-secret

# 数据库配置 (本地开发)
DATABASE_URL=local-d1-database

# 缓存配置
KV_NAMESPACE=dev-cache

# 对象存储配置
R2_BUCKET=dev-assets

# 日志配置
LOG_LEVEL=debug

# API 配置
API_BASE_URL=http://localhost:8787
CORS_ORIGINS=http://localhost:3000,https://senseword.app

# 频率限制配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# 监控配置 (可选)
SENTRY_DSN=https://<EMAIL>/project-id
