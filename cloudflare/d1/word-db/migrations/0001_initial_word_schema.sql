-- ===================================================================
-- SenseWord Word 数据库初始架构
-- 版本: 0001_initial_word_schema
-- 创建时间: 2025-07-27
-- 描述: 基于云端数据库实际结构重构的统一迁移文件
-- ===================================================================

-- ===================================================================
-- 表结构创建
-- ===================================================================

-- word_definitions表 (单词定义核心表)
-- 支持增量同步、多语言脚手架、AI生成内容管理
CREATE TABLE IF NOT EXISTS word_definitions (
    -- === 增量同步支持 ===
    sync_id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 增量同步ID，用于本地索引更新

    -- === 核心标识符 ===
    word TEXT NOT NULL,                         -- 英语单词，如 "abandoning"
    learningLanguage TEXT NOT NULL,             -- 学习语言代码，如 "en"
    scaffoldingLanguage TEXT NOT NULL,          -- 脚手架语言，如 "zh", "ja"

    -- === AI生成的核心内容 === 
    contentJson TEXT NOT NULL,                  -- JSON字符串形式的完整内容

    -- === 搜索优化字段 ===
    coreDefinition TEXT,                        -- 核心定义提取字段，避免JSON解析开销

    -- === AI分析的元数据字段 ===
    difficulty TEXT,                            -- CEFR难度等级 (A1-C2, 来自contentJson.content.difficulty)
    frequency TEXT,                             -- 词频等级 (High/Medium/Low/Rare, 来自contentJson.metadata.wordFrequency)
    relatedConcepts TEXT,                       -- 相关概念 (来自contentJson.metadata.relatedConcepts, JSON字符串格式)

    -- === 业务字段 ===
    partsOfSpeech TEXT,                         -- 词性 (来自本地表partsOfSpeech字段)
    culturalRiskRegions TEXT,                   -- 文化风险区域 (来自contentJson.metadata.culturalRiskRegions或本地表)

    -- === 标准时间戳 ===
    createdAt TEXT NOT NULL DEFAULT (datetime('now')),  -- 创建时间 (ISO格式)
    updatedAt TEXT NOT NULL DEFAULT (datetime('now')),  -- 更新时间 (ISO格式)

    -- === 唯一性约束 ===
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
);

-- ===================================================================
-- 索引优化
-- ===================================================================

-- 核心搜索索引
CREATE INDEX IF NOT EXISTS idx_word_search ON word_definitions (word);
CREATE INDEX IF NOT EXISTS idx_core_definition_search ON word_definitions (coreDefinition);

-- 多语言支持索引
CREATE INDEX IF NOT EXISTS idx_scaffolding_language ON word_definitions (scaffoldingLanguage);
CREATE INDEX IF NOT EXISTS idx_word_learning_scaffolding ON word_definitions (word, learningLanguage, scaffoldingLanguage);

-- 增量同步索引
CREATE INDEX IF NOT EXISTS idx_sync_learning_language ON word_definitions (sync_id, learningLanguage);
CREATE INDEX IF NOT EXISTS idx_learning_language_sync ON word_definitions (learningLanguage, sync_id);

-- 业务字段索引
CREATE INDEX IF NOT EXISTS idx_parts_of_speech ON word_definitions (partsOfSpeech);
CREATE INDEX IF NOT EXISTS idx_cultural_risk_regions ON word_definitions (culturalRiskRegions);

-- 元数据字段索引
CREATE INDEX IF NOT EXISTS idx_difficulty ON word_definitions (difficulty);
CREATE INDEX IF NOT EXISTS idx_frequency ON word_definitions (frequency);

-- ===================================================================
-- 触发器
-- ===================================================================

-- 自动更新时间戳触发器
CREATE TRIGGER IF NOT EXISTS trigger_word_definitions_updated_at
    AFTER UPDATE ON word_definitions
    FOR EACH ROW
    BEGIN
        UPDATE word_definitions 
        SET updatedAt = datetime('now') 
        WHERE sync_id = NEW.sync_id;
    END;

-- ===================================================================
-- 验证查询示例 (注释形式，用于确认表结构正确性)
-- ===================================================================
-- SELECT 
--   sync_id,
--   word,
--   learningLanguage,
--   scaffoldingLanguage,
--   coreDefinition,
--   difficulty,
--   frequency,
--   partsOfSpeech,
--   culturalRiskRegions,
--   createdAt
-- FROM word_definitions 
-- ORDER BY sync_id DESC
-- LIMIT 5;

-- ===================================================================
-- 迁移完成标记
-- ===================================================================
-- 架构版本: v1.0 (统一重构版)
-- 基于云端数据库实际结构: 2025-07-27
-- 包含表: word_definitions
-- 包含索引: 10个优化索引
-- 包含触发器: 1个自动更新触发器
-- 支持特性: 增量同步、多语言脚手架、AI内容管理、搜索优化
-- ===================================================================
