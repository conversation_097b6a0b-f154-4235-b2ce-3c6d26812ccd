# SenseWord Word 数据库架构文档

## 基本信息

- **数据库名称**: `senseword-word-db`
- **数据库ID**: `9f3369db-eb90-4917-8791-6b7f05e972c5`
- **绑定名称**: `DB` (在API Worker中)
- **类型**: Cloudflare D1 SQLite
- **Schema版本**: v1.0 (重构至2025年7月27日)
- **最后更新**: 2025年7月27日

## 迁移历史

### 当前迁移版本 (重构后)
- **0001_initial_word_schema.sql**: 统一架构 - 基于云端数据库实际结构重构的完整迁移文件

### 历史迁移版本 (已归档)
- **archive/0003_restructure_word_definitions.sql**: 结构重构 - 历史重构文件

## 数据库架构概览

### 表结构总览
1. **word_definitions** - 单词定义核心表，支持增量同步和多语言脚手架

## 表结构详情

### word_definitions 表（单词定义核心表）

单词定义的详细信息存储，支持增量同步、多语言脚手架、AI生成内容管理。

#### 字段说明

**增量同步支持**
- `sync_id` (INTEGER PRIMARY KEY AUTOINCREMENT): 增量同步ID，用于本地索引更新

**核心标识符**
- `word` (TEXT NOT NULL): 英语单词，如 "abandoning"
- `learningLanguage` (TEXT NOT NULL): 学习语言代码，如 "en"
- `scaffoldingLanguage` (TEXT NOT NULL): 脚手架语言，如 "zh", "ja"

**AI生成的核心内容**
- `contentJson` (TEXT NOT NULL): JSON字符串形式的完整内容

**搜索优化字段**
- `coreDefinition` (TEXT): 核心定义提取字段，避免JSON解析开销

**AI分析的元数据字段**
- `difficulty` (TEXT): CEFR难度等级 (A1-C2)
- `frequency` (TEXT): 词频等级 (High/Medium/Low/Rare)
- `relatedConcepts` (TEXT): 相关概念 (JSON字符串格式)

**业务字段**
- `partsOfSpeech` (TEXT): 词性
- `culturalRiskRegions` (TEXT): 文化风险区域

**标准时间戳**
- `createdAt` (TEXT NOT NULL): 创建时间 (ISO格式)
- `updatedAt` (TEXT NOT NULL): 更新时间 (ISO格式)

#### 约束条件
- **唯一性约束**: `UNIQUE(word, learningLanguage, scaffoldingLanguage)`

#### 索引优化

**核心搜索索引**
- `idx_word_search`: 单词搜索优化
- `idx_core_definition_search`: 核心定义搜索优化

**多语言支持索引**
- `idx_scaffolding_language`: 脚手架语言索引
- `idx_word_learning_scaffolding`: 复合语言索引

**增量同步索引**
- `idx_sync_learning_language`: 同步ID和学习语言复合索引
- `idx_learning_language_sync`: 学习语言和同步ID复合索引

**业务字段索引**
- `idx_parts_of_speech`: 词性索引
- `idx_cultural_risk_regions`: 文化风险区域索引

**元数据字段索引**
- `idx_difficulty`: 难度等级索引
- `idx_frequency`: 词频等级索引

#### 触发器
- `trigger_word_definitions_updated_at`: 自动更新时间戳触发器

## 数据库管理

### 数据库迁移命令
```bash
# 查看当前数据库状态
npx wrangler d1 execute senseword-word-db --command="SELECT name FROM sqlite_master WHERE type='table';"

# 检查迁移状态
npx wrangler d1 execute senseword-word-db --command="SELECT * FROM d1_migrations ORDER BY applied_at DESC;"

# 执行迁移（如果需要）
npx wrangler d1 migrations apply senseword-word-db

# 验证表结构
npx wrangler d1 execute senseword-word-db --command="PRAGMA table_info(word_definitions);"
```

### 性能监控
```sql
-- 数据统计
SELECT 
  scaffoldingLanguage,
  COUNT(*) as word_count 
FROM word_definitions 
GROUP BY scaffoldingLanguage;

-- 难度分布
SELECT 
  difficulty,
  COUNT(*) as count 
FROM word_definitions 
GROUP BY difficulty 
ORDER BY count DESC;

-- 词频分布
SELECT 
  frequency,
  COUNT(*) as count 
FROM word_definitions 
GROUP BY frequency 
ORDER BY count DESC;
```

## 配置文件

### wrangler.toml 配置
```toml
[[d1_databases]]
binding = "WORD_DB"
database_name = "senseword-word-db"
database_id = "9f3369db-eb90-4917-8791-6b7f05e972c5"
migrations_dir = "migrations"
```

## 重构说明

本数据库架构基于2025年7月27日的云端数据库实际结构重构，确保：
- 本地迁移文件与云端结构完全一致
- 支持增量同步和多语言脚手架
- 优化的索引设计提升查询性能
- 完整的触发器支持自动化操作
