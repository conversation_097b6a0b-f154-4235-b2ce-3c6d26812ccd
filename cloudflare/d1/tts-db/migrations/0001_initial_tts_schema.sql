-- ================================
-- SenseWord TTS 数据库初始架构
-- 版本: 0001_initial_tts_schema
-- 创建时间: 2025-07-27
-- 描述: 基于云端数据库实际结构重构的统一迁移文件
-- ================================

-- ================================
-- 表结构创建
-- ================================

-- 表1: tts_tasks (TTS任务表)
-- 用于存储所有TTS任务的详细信息和状态
CREATE TABLE IF NOT EXISTS tts_tasks (
  id INTEGER PRIMARY KEY AUTOINCREMENT,     -- 自增主键ID
  ttsId TEXT NOT NULL,                      -- 24位哈希ID: "a1b2c3d4e5f6789012345678"
  text TEXT NOT NULL,                       -- 文本内容: "hello" 或 "ˈheləʊ"
  type TEXT NOT NULL,                       -- 类型: "phonetic_name", "phonetic_bre", "example_sentence", "phrase_breakdown"
  status TEXT DEFAULT 'pending',            -- 状态: pending → batched → processing → completed/failed
  audioUrl TEXT,                            -- 音频URL: "https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3"
  errorMessage TEXT,                        -- 错误信息
  createdAt TEXT DEFAULT (datetime('now')), -- 创建时间: "2024-01-15T10:00:00.000Z"
  updatedAt TEXT DEFAULT (datetime('now')), -- 更新时间
  completedAt TEXT,                         -- 完成时间
  
  -- 约束
  CHECK (type IN ('phonetic_name', 'phonetic_bre', 'phonetic_ipa', 'example_sentence', 'phrase_breakdown')),
  CHECK (status IN ('pending', 'batched', 'processing', 'completed', 'failed'))
);

-- 表2: azure_batch_jobs (Azure批处理任务表)
-- 用于管理Azure TTS批处理任务的状态和元数据
CREATE TABLE IF NOT EXISTS azure_batch_jobs (
  batchId TEXT PRIMARY KEY,                 -- 批处理ID: "batch_20240115_001"
  taskCount INTEGER NOT NULL,               -- 任务数量: 25
  type TEXT NOT NULL,                       -- 批处理类型: "phonetic_name", "phonetic_bre", "example_sentence", "phrase_breakdown"
  status TEXT DEFAULT 'submitted',          -- 状态: submitted → running → succeeded/failed → processed
  submittedAt TEXT NOT NULL,                -- 提交时间: "2024-01-15T10:01:00.000Z"
  completedAt TEXT,                         -- 完成时间
  downloadUrl TEXT,                         -- 下载URL
  errorMessage TEXT,                        -- 错误信息
  taskMapping TEXT,                         -- 任务映射JSON
  createdAt TEXT DEFAULT (datetime('now')), -- 创建时间
  updatedAt TEXT DEFAULT (datetime('now')), -- 更新时间
  
  -- 约束
  CHECK (type IN ('phonetic_name', 'phonetic_bre', 'phonetic_ipa', 'example_sentence', 'phrase_breakdown')),
  CHECK (status IN ('submitted', 'running', 'succeeded', 'failed', 'processed')),
  CHECK (taskCount > 0)
);

-- 表3: workflow_config (工作流配置表)
-- 用于存储全局配置和监控数据
CREATE TABLE IF NOT EXISTS workflow_config (
  config_key TEXT PRIMARY KEY,             -- 配置键
  config_value TEXT NOT NULL,              -- 配置值
  created_at DATETIME DEFAULT (datetime('now')), -- 创建时间
  updated_at DATETIME DEFAULT (datetime('now'))  -- 更新时间
);

-- ================================
-- 初始数据插入
-- ================================

-- 插入workflow_config表的初始配置数据（基于云端数据库现有数据）
INSERT OR IGNORE INTO workflow_config (config_key, config_value, created_at, updated_at) VALUES
('auto_submission_enabled', '{"enabled": true, "maxTasksPerSubmission": 200, "intervalMinutes": 1}', '2025-07-21 15:07:49', '2025-07-21 15:07:49'),
('azure_api_error', '{"error":"","timestamp":"","errorCount":0,"lastErrorType":""}', '2025-07-25 13:10:23', '2025-07-25 13:10:23'),
('azure_key_billing_current', '{"totalCharacters":0,"totalCostUSD":0,"lastUpdated":""}', datetime('now'), datetime('now')),
('azure_key_billing_historical', '{"totalCharacters":0,"totalCostUSD":0,"lastUpdated":""}', datetime('now'), datetime('now')),
('last_auto_submission', '{"timestamp":"","tasksSubmitted":0,"batchesCreated":0,"success":false}', datetime('now'), datetime('now')),
('polling_window', '{"enabled":false,"endTime":"","estimatedTasks":0,"lastExtended":""}', '2025-07-17 01:57:48', '2025-07-17 01:57:48');

-- ================================
-- 索引优化
-- ================================

-- tts_tasks表索引
CREATE INDEX IF NOT EXISTS idx_tts_tasks_status ON tts_tasks(status);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_type ON tts_tasks(type);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_created ON tts_tasks(createdAt);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_updated_at ON tts_tasks(updatedAt);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_completed_at ON tts_tasks(completedAt);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_status_created_at ON tts_tasks(status, createdAt);
CREATE INDEX IF NOT EXISTS idx_tts_tasks_completed_status_type ON tts_tasks(status, completedAt, type, audioUrl);
CREATE UNIQUE INDEX IF NOT EXISTS idx_tts_tasks_ttsId ON tts_tasks(ttsId);

-- azure_batch_jobs表索引
CREATE INDEX IF NOT EXISTS idx_azure_batch_jobs_status ON azure_batch_jobs(status);
CREATE INDEX IF NOT EXISTS idx_azure_batch_jobs_type ON azure_batch_jobs(type);
CREATE INDEX IF NOT EXISTS idx_azure_batch_jobs_submitted_at ON azure_batch_jobs(submittedAt);
CREATE INDEX IF NOT EXISTS idx_azure_batch_jobs_status_submitted ON azure_batch_jobs(status, submittedAt);

-- workflow_config表索引
CREATE INDEX IF NOT EXISTS idx_workflow_config_key ON workflow_config(config_key);

-- ================================
-- 触发器
-- ================================

-- azure_batch_jobs表更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_azure_batch_jobs_updated_at
    AFTER UPDATE ON azure_batch_jobs
    FOR EACH ROW
    WHEN NEW.updatedAt = OLD.updatedAt
BEGIN
    UPDATE azure_batch_jobs SET updatedAt = datetime('now') WHERE batchId = NEW.batchId;
END;

-- workflow_config表更新时间触发器
CREATE TRIGGER IF NOT EXISTS trigger_workflow_config_updated_at
    AFTER UPDATE ON workflow_config
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE workflow_config SET updated_at = datetime('now') WHERE config_key = NEW.config_key;
END;

-- ================================
-- 迁移完成标记
-- ================================
-- 架构版本: v1.0 (统一重构版)
-- 基于云端数据库实际结构: 2025-07-27
-- 包含表: tts_tasks, azure_batch_jobs, workflow_config
-- 包含索引: 13个优化索引
-- 包含触发器: 2个自动更新触发器
-- ================================
