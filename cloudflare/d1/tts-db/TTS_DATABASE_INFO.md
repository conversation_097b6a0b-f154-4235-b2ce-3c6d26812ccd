# SenseWord TTS 数据库架构文档

## 基本信息

- **数据库名称**: `senseword-tts-db`
- **数据库ID**: `253bb3ab-6300-4d92-b0f7-e746ef8885b3`
- **绑定名称**: `TTS_DB`
- **类型**: Cloudflare D1 SQLite
- **Schema版本**: v1.7 (迁移至2025年7月21日)
- **最后更新**: 2025年7月22日

## 迁移历史

### 关键迁移版本
- **0001_pure_database_schema.sql**: 基础架构 - 创建核心表和索引
- **0004_polling_window_config.sql**: 工作流配置 - 轮询窗口和计费统计
- **0006_add_batch_processing_support.sql**: 批处理支持 - Azure批处理任务管理
- **0007_azure_error_monitoring.sql**: 错误监控 - API错误跟踪和自动提交控制

## 数据库架构概览

### 表结构总览
1. **tts_tasks** - TTS任务详情和状态跟踪
2. **azure_batch_jobs** - Azure批处理任务状态管理
3. **workflow_config** - 全局配置和监控数据
4. **d1_migrations** - 迁移记录（系统表）

## 表结构详情

### 1. tts_tasks 表（TTS任务核心表）

TTS任务的详细信息存储，支持单个任务的完整生命周期管理。

```sql
CREATE TABLE tts_tasks (
  ttsId TEXT PRIMARY KEY,                    -- 24位哈希ID
  text TEXT NOT NULL,                        -- 文本内容
  type TEXT NOT NULL,                        -- TTS类型
  status TEXT DEFAULT 'pending',            -- 任务状态
  audioUrl TEXT,                             -- 音频URL
  batchId TEXT,                              -- 批处理ID（关联字段）
  errorMessage TEXT,                         -- 错误信息
  createdAt TEXT DEFAULT (datetime('now')), -- 创建时间
  updatedAt TEXT DEFAULT (datetime('now')), -- 更新时间
  completedAt TEXT,                          -- 完成时间
  
  CHECK (type IN ('phonetic_name', 'phonetic_bre', 'phonetic_ipa', 'example_sentence', 'phrase_breakdown')),
  CHECK (status IN ('pending', 'batched', 'processing', 'completed', 'failed')),
  CHECK (length(ttsId) = 24)
);
```

#### 字段说明

| 字段名 | 类型 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|
| ttsId | TEXT | PRIMARY KEY | 24位哈希任务ID | "a1b2c3d4e5f6789012345678" |
| text | TEXT | NOT NULL | 需要转换的文本内容 | "hello" / "ˈheləʊ" |
| type | TEXT | NOT NULL | TTS类型（枚举） | "phonetic_bre", "example_sentence" |
| status | TEXT | DEFAULT 'pending' | 任务状态（枚举） | "pending", "batched", "processing" |
| audioUrl | TEXT | NULL | 生成的音频URL | "https://audio.senseword.app/a1b2c3d4e5f6789012345678.mp3" |
| batchId | TEXT | NULL | 关联批处理ID | "batch_20240115_001" |
| errorMessage | TEXT | NULL | 错误详情 | "Azure API timeout" |
| createdAt | TEXT | AUTO | 创建时间戳 | "2024-01-15T10:00:00.000Z" |
| updatedAt | TEXT | AUTO | 更新时间戳 | "2024-01-15T10:05:00.000Z" |
| completedAt | TEXT | NULL | 完成时间戳 | "2024-01-15T10:05:00.000Z" |

#### 索引策略
```sql
CREATE INDEX idx_tts_tasks_status ON tts_tasks(status);
CREATE INDEX idx_tts_tasks_type ON tts_tasks(type);
CREATE INDEX idx_tts_tasks_batch_id ON tts_tasks(batchId);
CREATE INDEX idx_tts_tasks_created_at ON tts_tasks(createdAt);
CREATE INDEX idx_tts_tasks_status_type ON tts_tasks(status, type);
```

### 2. azure_batch_jobs 表（批处理任务管理）

Azure TTS批处理任务的状态跟踪和结果管理。

```sql
CREATE TABLE azure_batch_jobs (
  batchId TEXT PRIMARY KEY,                  -- 批处理ID
  taskCount INTEGER NOT NULL,               -- 任务数量
  type TEXT NOT NULL,                       -- TTS类型
  status TEXT DEFAULT 'submitted',          -- 批处理状态
  submittedAt TEXT NOT NULL,                -- 提交时间
  completedAt TEXT,                         -- 完成时间
  downloadUrl TEXT,                         -- 下载链接
  errorMessage TEXT,                        -- 错误信息
  taskMapping TEXT,                         -- 任务映射JSON
  createdAt TEXT DEFAULT (datetime('now')), -- 创建时间
  updatedAt TEXT DEFAULT (datetime('now')), -- 更新时间
  
  CHECK (type IN ('phonetic_name', 'phonetic_bre', 'phonetic_ipa', 'example_sentence', 'phrase_breakdown')),
  CHECK (status IN ('submitted', 'running', 'succeeded', 'failed', 'processed', 'ready_for_download')),
  CHECK (taskCount > 0)
);
```

#### 字段说明

| 字段名 | 类型 | 约束 | 描述 | 示例 |
|--------|------|------|------|------|
| batchId | TEXT | PRIMARY KEY | 批处理唯一标识 | "batch_20240115_001" |
| taskCount | INTEGER | NOT NULL | 包含任务数量 | 50 |
| type | TEXT | NOT NULL | TTS类型（枚举） | "phonetic_name" |
| status | TEXT | DEFAULT 'submitted' | 批处理状态（枚举） | "submitted", "running", "succeeded" |
| submittedAt | TEXT | NOT NULL | Azure提交时间 | "2024-01-15T10:01:00.000Z" |
| completedAt | TEXT | NULL | 处理完成时间 | "2024-01-15T10:15:00.000Z" |
| downloadUrl | TEXT | NULL | 结果ZIP下载链接 | "https://azure.blob.core.windows.net/..." |
| errorMessage | TEXT | NULL | 错误详情 | "Batch processing timeout" |
| taskMapping | TEXT | NULL | 任务映射JSON | `{"0": "task_id_1", "1": "task_id_2"}` |

#### 索引策略
```sql
CREATE INDEX idx_azure_batch_jobs_status ON azure_batch_jobs(status);
CREATE INDEX idx_azure_batch_jobs_type ON azure_batch_jobs(type);
CREATE INDEX idx_azure_batch_jobs_submitted_at ON azure_batch_jobs(submittedAt);
CREATE INDEX idx_azure_batch_jobs_status_submitted ON azure_batch_jobs(status, submittedAt);
```

### 3. workflow_config 表（全局配置管理）

工作流全局配置和监控数据的统一存储。

```sql
CREATE TABLE workflow_config (
  config_key TEXT PRIMARY KEY,           -- 配置键
  config_value TEXT NOT NULL,            -- JSON格式配置值
  created_at DATETIME DEFAULT (datetime('now')),
  updated_at DATETIME DEFAULT (datetime('now'))
);
```

#### 关键配置项

| 配置键 | 描述 | JSON结构示例 |
|--------|------|--------------|
| polling_window | 轮询窗口配置 | `{"enabled": false, "endTime": "", "estimatedTasks": 0}` |
| azure_key_billing | API Key计费统计 | `{"totalCharacters": 0, "totalCostUSD": 0, "lastUpdated": ""}` |
| azure_api_error | API错误监控 | `{"error": "", "timestamp": "", "errorCount": 0}` |
| auto_submission_enabled | 自动提交控制 | `{"enabled": true, "maxTasksPerSubmission": 1000}` |
| last_auto_submission | 最后提交记录 | `{"timestamp": "", "tasksSubmitted": 0, "success": true}` |

## 状态转换流程

### TTS任务状态转换
```mermaid
graph LR
    A[pending] --> B[batched]
    B --> C[processing]
    C --> D[completed]
    
    A --> E[failed]
    B --> E
    C --> E
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#9f9,stroke:#333,stroke-width:2px
    style E fill:#f99,stroke:#333,stroke-width:2px
```

### 批处理状态转换
```mermaid
graph LR
    A[submitted] --> B[running]
    B --> C[succeeded]
    C --> D[processed]
    C --> E[ready_for_download]
    
    A --> F[failed]
    B --> F
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#9f9,stroke:#333,stroke-width:2px
    style F fill:#f99,stroke:#333,stroke-width:2px
```

## 触发器和自动化

### 自动时间戳更新
```sql
-- tts_tasks表更新触发器
CREATE TRIGGER trigger_tts_tasks_updated_at
    AFTER UPDATE ON tts_tasks
    FOR EACH ROW
    WHEN NEW.updatedAt = OLD.updatedAt
BEGIN
    UPDATE tts_tasks SET updatedAt = datetime('now') WHERE ttsId = NEW.ttsId;
END;

-- azure_batch_jobs表更新触发器
CREATE TRIGGER trigger_azure_batch_jobs_updated_at
    AFTER UPDATE ON azure_batch_jobs
    FOR EACH ROW
    WHEN NEW.updatedAt = OLD.updatedAt
BEGIN
    UPDATE azure_batch_jobs SET updatedAt = datetime('now') WHERE batchId = NEW.batchId;
END;

-- workflow_config表更新触发器
CREATE TRIGGER trigger_workflow_config_updated_at
    AFTER UPDATE ON workflow_config
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE workflow_config SET updated_at = datetime('now') WHERE config_key = NEW.config_key;
END;
```

## 数据关系图

```mermaid
erDiagram
    tts_tasks ||--o{ azure_batch_jobs : "batchId"
    workflow_config ||--|| workflow_config : "configurations"
    
    tts_tasks {
        TEXT ttsId PK
        TEXT text
        TEXT type
        TEXT status
        TEXT audioUrl
        TEXT batchId FK
        TEXT errorMessage
        TEXT createdAt
        TEXT updatedAt
        TEXT completedAt
    }
    
    azure_batch_jobs {
        TEXT batchId PK
        INTEGER taskCount
        TEXT type
        TEXT status
        TEXT submittedAt
        TEXT completedAt
        TEXT downloadUrl
        TEXT errorMessage
        TEXT taskMapping
        TEXT createdAt
        TEXT updatedAt
    }
    
    workflow_config {
        TEXT config_key PK
        TEXT config_value
        DATETIME created_at
        DATETIME updated_at
    }
```

## 常用查询模式

### 基础查询
```sql
-- 查询待处理任务
SELECT * FROM tts_tasks WHERE status = 'pending' ORDER BY createdAt;

-- 按类型统计任务
SELECT type, status, COUNT(*) as count 
FROM tts_tasks 
GROUP BY type, status;

-- 查询失败任务详情
SELECT ttsId, text, type, errorMessage, createdAt 
FROM tts_tasks 
WHERE status = 'failed' 
ORDER BY createdAt DESC;
```

### 批处理相关查询
```sql
-- 查询进行中的批处理
SELECT * FROM azure_batch_jobs 
WHERE status IN ('submitted', 'running', 'succeeded') 
ORDER BY submittedAt DESC;

-- 查询批处理任务详情
SELECT 
    b.batchId,
    b.type,
    b.status as batch_status,
    b.taskCount,
    COUNT(t.ttsId) as tasks_in_db,
    b.submittedAt
FROM azure_batch_jobs b
LEFT JOIN tts_tasks t ON b.batchId = t.batchId
GROUP BY b.batchId;

-- 查询特定批处理的任务
SELECT t.*, b.status as batch_status 
FROM tts_tasks t 
JOIN azure_batch_jobs b ON t.batchId = b.batchId 
WHERE b.batchId = 'batch_20240115_001';
```

### 配置管理查询
```sql
-- 获取轮询窗口配置
SELECT config_value FROM workflow_config WHERE config_key = 'polling_window';

-- 获取计费统计
SELECT config_value FROM workflow_config WHERE config_key = 'azure_key_billing';

-- 检查API错误状态
SELECT config_value FROM workflow_config WHERE config_key = 'azure_api_error';

-- 更新自动提交配置
UPDATE workflow_config 
SET config_value = '{"enabled": true, "maxTasksPerSubmission": 500, "intervalMinutes": 2}' 
WHERE config_key = 'auto_submission_enabled';
```

## 部署和维护

### 数据库迁移命令
```bash
# 查看当前数据库状态
npx wrangler d1 execute senseword-tts-db --command="SELECT name FROM sqlite_master WHERE type='table';"

# 检查迁移状态
npx wrangler d1 execute senseword-tts-db --command="SELECT * FROM d1_migrations ORDER BY applied_at DESC;"

# 执行迁移（如果需要）
npx wrangler d1 migrations apply senseword-tts-db

# 验证表结构
npx wrangler d1 execute senseword-tts-db --command="PRAGMA table_info(tts_tasks);"
npx wrangler d1 execute senseword-tts-db --command="PRAGMA table_info(azure_batch_jobs);"
npx wrangler d1 execute senseword-tts-db --command="PRAGMA table_info(workflow_config);"
```

### 性能监控
```sql
-- 任务状态分布
SELECT status, COUNT(*) as count FROM tts_tasks GROUP BY status;

-- 批处理性能统计
SELECT 
    type,
    AVG(taskCount) as avg_batch_size,
    AVG(julianday(completedAt) - julianday(submittedAt)) * 24 * 60 as avg_minutes
FROM azure_batch_jobs 
WHERE status = 'processed' AND completedAt IS NOT NULL
GROUP BY type;

-- 每日任务统计
SELECT 
    DATE(createdAt) as date,
    type,
    COUNT(*) as tasks_created
FROM tts_tasks 
WHERE createdAt >= datetime('now', '-7 days')
GROUP BY DATE(createdAt), type
ORDER BY date DESC;
```

### 数据清理和归档
```sql
-- 清理超过30天的已完成任务（谨慎使用）
DELETE FROM tts_tasks 
WHERE status = 'completed' 
AND createdAt < datetime('now', '-30 days');

-- 清理超过7天的失败任务（谨慎使用）
DELETE FROM tts_tasks 
WHERE status = 'failed' 
AND createdAt < datetime('now', '-7 days');

-- 归档已处理的批处理记录
UPDATE azure_batch_jobs 
SET status = 'archived' 
WHERE status = 'processed' 
AND completedAt < datetime('now', '-14 days');
```

## 最佳实践

### 数据写入模式
1. **批量插入**: 使用事务包装多个INSERT操作
2. **状态更新**: 通过batchId批量更新关联任务状态
3. **配置更新**: 使用JSON格式存储复杂配置数据

### 查询优化
1. **利用索引**: 状态和类型查询使用组合索引
2. **限制结果**: 使用LIMIT避免大结果集
3. **时间范围**: 创建时间查询使用索引

### 错误处理
1. **约束验证**: 依赖CHECK约束确保数据完整性
2. **外键模拟**: 通过应用逻辑维护batchId关联
3. **事务回滚**: 批量操作使用事务确保一致性