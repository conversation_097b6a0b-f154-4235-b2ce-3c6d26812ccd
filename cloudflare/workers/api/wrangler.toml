name = "senseword-api-worker"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# D1数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "senseword-word-db"
database_id = "9f3369db-eb90-4917-8791-6b7f05e972c5"
migrations_dir = "../../d1/migrations"

[env.development.vars]
STATIC_API_KEY = "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
[env.production]
name = "senseword-api-worker"

# 生产环境路由配置
[[env.production.routes]]
pattern = "api.senseword.app/*"
zone_name = "senseword.app"

# 生产环境D1数据库绑定
[[env.production.d1_databases]]
binding = "DB"
database_name = "senseword-word-db"
database_id = "9f3369db-eb90-4917-8791-6b7f05e972c5"

[env.production.vars]
STATIC_API_KEY = "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"

# TypeScript配置
[build]
command = ""





