// ================================
// 基于页码的下载范围计算处理器
// 实现智能下载范围计算，基于用户本地最大页码返回需要下载的页面范围
// ================================

import type { Env } from '../types/word-types';

// ================================
// 数据类型定义
// ================================

/**
 * 下载范围请求参数
 */
interface DownloadRangeRequest {
  learningLanguage: string;     // 学习语言代码，如 "en"
  scaffoldingLanguage: string;  // 脚手架语言代码，如 "zh"
  localMaxPage: number;         // 本地最大页码
}

/**
 * 下载范围响应
 */
interface DownloadRangeResponse {
  success: boolean;
  startPage: number;            // 起始页码（包含）
  endPage: number;              // 结束页码（包含）
  totalPages: number;           // 需要下载的总页数
  estimatedItems: number;       // 预估单词数量
  message?: string;             // 状态消息
}

/**
 * 错误响应
 */
interface DownloadRangeErrorResponse {
  success: boolean;
  error: string;
  code: string;
}

// ================================
// 核心处理函数
// ================================

/**
 * 解析下载范围请求参数
 */
function parseDownloadRangeParameters(pathname: string, searchParams: URLSearchParams): DownloadRangeRequest | { error: string } {
  console.log(`[DownloadRange] 开始解析请求参数: ${pathname}`);

  // 匹配路径格式: /api/v1/word-index/:learningLang/:scaffoldingLang/download-range
  const routeMatch = pathname.match(/^\/api\/v1\/word-index\/([^\/]+)\/([^\/]+)\/download-range$/);

  if (!routeMatch) {
    return { error: '无效的路径格式，期望: /api/v1/word-index/{learningLang}/{scaffoldingLang}/download-range' };
  }

  const [, learningLang, scaffoldingLang] = routeMatch;

  // 验证学习语言代码
  const learningLanguage = learningLang.trim().toLowerCase();
  if (!/^[a-z]{2}$/.test(learningLanguage)) {
    return { error: `无效的学习语言代码格式: ${learningLang}` };
  }

  // 验证脚手架语言代码
  const scaffoldingLanguage = scaffoldingLang.trim().toLowerCase();
  if (!/^[a-z]{2}$/.test(scaffoldingLanguage)) {
    return { error: `无效的脚手架语言代码格式: ${scaffoldingLang}` };
  }

  // 获取本地最大页码
  const localMaxPageStr = searchParams.get('localMaxPage');
  if (!localMaxPageStr) {
    return { error: '缺少必需参数: localMaxPage' };
  }

  const localMaxPage = parseInt(localMaxPageStr, 10);
  if (isNaN(localMaxPage) || localMaxPage < 0) {
    return { error: `无效的本地最大页码: ${localMaxPageStr}，必须是非负整数` };
  }

  console.log(`[DownloadRange] 解析成功: learningLanguage=${learningLanguage}, scaffoldingLanguage=${scaffoldingLanguage}, localMaxPage=${localMaxPage}`);

  return {
    learningLanguage,
    scaffoldingLanguage,
    localMaxPage
  };
}

/**
 * 计算下载范围
 */
async function calculateDownloadRange(
  params: DownloadRangeRequest,
  env: Env
): Promise<DownloadRangeResponse> {
  console.log(`[DownloadRange] 开始计算下载范围: learningLanguage=${params.learningLanguage}, scaffoldingLanguage=${params.scaffoldingLanguage}, localMaxPage=${params.localMaxPage}`);

  const startTime = Date.now();

  try {
    // 查询服务端该语言对的最大页码
    const sql = `
      SELECT MAX(CEIL(sync_id / 1000.0)) as max_page
      FROM word_definitions
      WHERE learningLanguage = ? AND scaffoldingLanguage = ?
    `;

    const queryParams = [params.learningLanguage, params.scaffoldingLanguage];
    
    console.log(`[DownloadRange] 执行SQL查询:`, { sql: sql.replace(/\s+/g, ' ').trim(), params: queryParams });
    
    // 执行查询
    const result = await env.DB.prepare(sql).bind(...queryParams).first();
    
    const executionTime = Date.now() - startTime;
    const serverMaxPage = result?.max_page as number || 0;
    
    console.log(`[DownloadRange] 查询完成: serverMaxPage=${serverMaxPage}, ${executionTime}ms`);
    
    // 计算下载范围
    if (params.localMaxPage >= serverMaxPage) {
      // 本地已是最新，无需下载
      console.log(`[DownloadRange] 本地已是最新: localMaxPage=${params.localMaxPage} >= serverMaxPage=${serverMaxPage}`);
      return {
        success: true,
        startPage: 0,
        endPage: 0,
        totalPages: 0,
        estimatedItems: 0,
        message: '本地索引已是最新，无需下载'
      };
    }

    // 计算需要下载的范围
    const startPage = params.localMaxPage + 1;
    const endPage = serverMaxPage;
    const totalPages = endPage - startPage + 1;
    const estimatedItems = totalPages * 1000;

    console.log(`[DownloadRange] 计算完成: startPage=${startPage}, endPage=${endPage}, totalPages=${totalPages}, estimatedItems=${estimatedItems}`);

    return {
      success: true,
      startPage,
      endPage,
      totalPages,
      estimatedItems,
      message: `需要下载 ${totalPages} 页，约 ${estimatedItems} 个单词`
    };
    
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`[DownloadRange] 计算失败 (${executionTime}ms):`, error);
    throw error;
  }
}

/**
 * 创建成功响应
 */
function createDownloadRangeSuccessResponse(data: DownloadRangeResponse): Response {
  return new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key',
      'Cache-Control': 'public, max-age=300', // 5分钟缓存
    }
  });
}

/**
 * 创建错误响应
 */
function createDownloadRangeErrorResponse(error: string, code: string, status: number = 400): Response {
  const errorResponse: DownloadRangeErrorResponse = {
    success: false,
    error,
    code
  };

  return new Response(JSON.stringify(errorResponse), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}

// ================================
// 主处理器导出
// ================================

/**
 * 处理下载范围计算请求
 */
export async function handleDownloadRange(request: Request, env: Env): Promise<Response> {
  const requestStartTime = Date.now();
  console.log(`[DownloadRange] 开始处理下载范围计算请求`);

  try {
    // 1. 验证API密钥
    const apiKey = request.headers.get('X-Static-API-Key');
    if (!apiKey || apiKey !== env.STATIC_API_KEY) {
      console.log(`[DownloadRange] API密钥验证失败`);
      return createDownloadRangeErrorResponse(
        'API密钥无效或缺失',
        'INVALID_API_KEY',
        401
      );
    }

    console.log(`[DownloadRange] API密钥验证成功`);

    // 2. 解析和验证请求参数
    const url = new URL(request.url);
    const params = parseDownloadRangeParameters(url.pathname, url.searchParams);

    if ('error' in params) {
      console.log(`[DownloadRange] 参数验证失败: ${params.error}`);
      return createDownloadRangeErrorResponse(
        params.error,
        'INVALID_PARAMETERS'
      );
    }

    console.log(`[DownloadRange] 参数验证成功`);

    // 3. 计算下载范围
    const result = await calculateDownloadRange(params, env);

    // 4. 返回成功响应
    const requestTime = Date.now() - requestStartTime;
    console.log(`[DownloadRange] 下载范围计算完成: ${requestTime}ms`);
    return createDownloadRangeSuccessResponse(result);

  } catch (error) {
    const requestTime = Date.now() - requestStartTime;
    console.error(`[DownloadRange] 处理请求失败 (${requestTime}ms):`, error);
    
    return createDownloadRangeErrorResponse(
      '服务器内部错误',
      'INTERNAL_ERROR',
      500
    );
  }
}

/**
 * 处理OPTIONS预检请求
 */
export function handleDownloadRangeOptions(): Response {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key',
      'Access-Control-Max-Age': '86400'
    }
  });
}
