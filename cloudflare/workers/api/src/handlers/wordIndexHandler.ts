// ================================
// KDD-009: 增量同步API端点处理器  
// 实现FC-02的增量同步处理逻辑
// 替代实时搜索建议，支持本地索引构建
// ================================

import type { Env } from '../types/word-types';

// ================================
// 数据类型定义
// ================================

/**
 * 固定分页请求参数
 */
interface WordIndexRequest {
  learningLanguage: string;     // 学习语言代码，如 "en"
  scaffoldingLanguage: string;  // 脚手架语言代码，如 "zh"
  pageNumber: number;           // 页码，从1开始
}

/**
 * 固定分页单个词条数据
 */
interface WordIndexItem {
  syncId: number;
  word: string;
  learningLanguage: string;           // 学习语言
  scaffoldingLanguage: string;        // 脚手架语言
  coreDefinition: string;             // 从contentJson提取的核心释义
}



/**
 * 增量同步响应数据
 */
interface WordIndexResponse {
  success: boolean;
  data: WordIndexItem[];
  lastSyncId: number;
  metadata: {
    count: number;
    requestTime: number;
    fromSyncId: number;
    toSyncId: number;
  };
}

/**
 * 错误响应数据
 */
interface WordIndexErrorResponse {
  success: false;
  error: 'INVALID_LANGUAGE' | 'INVALID_SYNC_ID' | 'DATABASE_ERROR' | 'AUTHENTICATION_FAILED';
  message: string;
  code?: number;
}

// ================================
// FC-02: 增量同步API处理器
// ================================

/**
 * 解析分页路径参数（新版API）
 */
function parsePageParameters(pathname: string): WordIndexRequest | { error: string } {
  console.log(`[WordIndex] 开始解析分页路径参数: ${pathname}`);

  // 匹配分页路径格式: /api/v1/word-index/:learningLang/:scaffoldingLang/page/:pageNumber
  const pageRouteMatch = pathname.match(/^\/api\/v1\/word-index\/([^\/]+)\/([^\/]+)\/page\/(\d+)$/);

  if (!pageRouteMatch) {
    return { error: '无效的分页路径格式，期望: /api/v1/word-index/{learningLang}/{scaffoldingLang}/page/{pageNumber}' };
  }

  const [, learningLang, scaffoldingLang, pageStr] = pageRouteMatch;

  // 验证学习语言代码
  const learningLanguage = learningLang.trim().toLowerCase();
  if (!/^[a-z]{2}$/.test(learningLanguage)) {
    return { error: `无效的学习语言代码格式: ${learningLang}` };
  }

  // 验证脚手架语言代码
  const scaffoldingLanguage = scaffoldingLang.trim().toLowerCase();
  if (!/^[a-z]{2}$/.test(scaffoldingLanguage)) {
    return { error: `无效的脚手架语言代码格式: ${scaffoldingLang}` };
  }

  // 验证支持的语言列表
  const supportedLanguages = [
    'en', 'zh', 'ja', 'de', 'fr', 'es',     // 核心语言（英语、中文等）
    'ko', 'ru', 'it', 'pt', 'ar',          // 新增亚洲和欧洲语言
    'hi', 'th', 'vi', 'tr', 'pl',          // 新增南亚和东南亚语言
    'nl', 'sv', 'da', 'no', 'fi',          // 新增北欧语言
    'id'                                   // 印度尼西亚语
  ];

  if (!supportedLanguages.includes(learningLanguage)) {
    return { error: `不支持的学习语言代码: ${learningLang}` };
  }

  if (!supportedLanguages.includes(scaffoldingLanguage)) {
    return { error: `不支持的脚手架语言代码: ${scaffoldingLang}` };
  }

  // 验证页码
  const pageNumber = parseInt(pageStr, 10);
  if (isNaN(pageNumber) || pageNumber < 1 || pageNumber > 100) {
    return { error: `无效的页码: ${pageStr}，页码范围: 1-100` };
  }

  console.log(`[WordIndex] 解析成功: learningLanguage=${learningLanguage}, scaffoldingLanguage=${scaffoldingLanguage}, pageNumber=${pageNumber}`);

  return {
    learningLanguage,
    scaffoldingLanguage,
    pageNumber
  };
}



/**
 * 执行增量同步数据查询
 */
async function executeWordIndexQuery(
  params: WordIndexRequest,
  env: Env
): Promise<{ results: any[]; lastSyncId: number }> {
  console.log(`[WordIndex] 执行分页查询: learningLanguage=${params.learningLanguage}, scaffoldingLanguage=${params.scaffoldingLanguage}, pageNumber=${params.pageNumber}`);

  const startTime = Date.now();

  try {
    // 计算分页范围
    const pageStart = (params.pageNumber - 1) * 1000 + 1;
    const pageEnd = params.pageNumber * 1000;

    // 构建固定分页查询SQL（优化：直接使用coreDefinition字段）
    const sql = `
      SELECT
        sync_id,
        word,
        learningLanguage,
        scaffoldingLanguage,
        coreDefinition as core_definition
      FROM word_definitions
      WHERE learningLanguage = ? AND scaffoldingLanguage = ? AND sync_id BETWEEN ? AND ? AND coreDefinition IS NOT NULL
      ORDER BY sync_id ASC
    `;

    const queryParams = [params.learningLanguage, params.scaffoldingLanguage, pageStart, pageEnd];
    
    console.log(`[WordIndex] 执行SQL查询:`, { sql: sql.replace(/\s+/g, ' ').trim(), params: queryParams });
    
    // 执行查询
    const result = await env.DB.prepare(sql).bind(...queryParams).all();
    
    const executionTime = Date.now() - startTime;
    const results = result.results || [];
    
    // 计算最后的sync_id
    const lastSyncId = results.length > 0
      ? Math.max(...results.map((row: any) => row.sync_id))
      : pageEnd;
    
    console.log(`[WordIndex] 查询完成: ${results.length}条结果, ${executionTime}ms, lastSyncId=${lastSyncId}`);
    
    return { results, lastSyncId };
    
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`[WordIndex] 数据库查询失败 (${executionTime}ms):`, error);
    throw error;
  }
}

/**
 * 处理数据库结果，构建响应数据
 */
function processWordIndexResults(
  dbResults: any[],
  lastSyncId: number,
  fromSyncId: number,
  requestTime: number
): WordIndexResponse {
  console.log(`[WordIndex] 处理查询结果: ${dbResults.length}条记录`);
  
  const data: WordIndexItem[] = dbResults.map(row => ({
    syncId: row.sync_id,
    page: Math.ceil(row.sync_id / 1000), // 基于sync_id计算页码
    word: row.word,
    learningLanguage: row.learningLanguage,
    scaffoldingLanguage: row.scaffoldingLanguage,
    coreDefinition: row.core_definition || '定义处理中...'
  }));
  
  const response: WordIndexResponse = {
    success: true,
    data,
    lastSyncId,
    metadata: {
      count: data.length,
      requestTime,
      fromSyncId,
      toSyncId: lastSyncId
    }
  };
  
  console.log(`[WordIndex] 响应数据构建完成: ${data.length}条记录, syncId范围: ${fromSyncId} -> ${lastSyncId}`);
  return response;
}

/**
 * 创建成功响应（带长期缓存策略）
 */
function createWordIndexSuccessResponse(response: WordIndexResponse, isPageRequest: boolean = false): Response {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key'
  };

  // 固定分页请求使用长期缓存策略
  if (isPageRequest) {
    headers['Cache-Control'] = 'public, max-age=86400';           // 浏览器缓存 24小时
    headers['CDN-Cache-Control'] = 'max-age=604800';              // CDN 缓存 7天
    headers['Browser-Cache-Control'] = 'max-age=3600';           // 浏览器缓存 1小时
    headers['Vary'] = 'Accept-Encoding';                         // 支持压缩缓存
    console.log('[WordIndex] 应用长期缓存策略: CDN 7天, 浏览器 1小时');
  } else {
    // 兼容性API使用短期缓存
    headers['Cache-Control'] = 'public, max-age=300';            // 5分钟缓存
    console.log('[WordIndex] 应用短期缓存策略: 5分钟');
  }

  return new Response(JSON.stringify(response), {
    status: 200,
    headers
  });
}

/**
 * 创建错误响应
 */
function createWordIndexErrorResponse(
  error: WordIndexErrorResponse['error'],
  message: string,
  statusCode: number = 400
): Response {
  const response: WordIndexErrorResponse = {
    success: false,
    error,
    message,
    code: statusCode
  };
  
  return new Response(JSON.stringify(response), {
    status: statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key'
    }
  });
}

/**
 * 主要的增量同步API处理器
 * 处理 GET /api/v1/word-index/updates 请求
 */
export async function handleWordIndexUpdates(
  request: Request,
  env: Env
): Promise<Response> {
  console.log(`[WordIndex] 开始处理增量同步请求: ${request.url}`);
  
  const requestStartTime = Date.now();

  try {
    // 1. 验证API密钥（所有环境都需要）
    const apiKey = request.headers.get('X-Static-API-Key');
    console.log(`[WordIndex] 收到API密钥: ${apiKey ? '***已提供***' : '未提供'}`);

    if (!apiKey) {
      console.log(`[WordIndex] API密钥验证失败 - 缺少 X-Static-API-Key 头`);
      return createWordIndexErrorResponse(
        'AUTHENTICATION_FAILED',
        '缺少必需的 X-Static-API-Key 头',
        401
      );
    }

    if (!env.STATIC_API_KEY) {
      console.error(`[WordIndex] 服务器配置错误 - 环境变量 STATIC_API_KEY 未设置`);
      return createWordIndexErrorResponse(
        'AUTHENTICATION_FAILED',
        '服务器认证配置错误',
        500
      );
    }

    if (apiKey !== env.STATIC_API_KEY) {
      console.log(`[WordIndex] API密钥验证失败 - 密钥不匹配`);
      return createWordIndexErrorResponse(
        'AUTHENTICATION_FAILED',
        'API密钥无效',
        401
      );
    }

    console.log(`[WordIndex] API密钥验证成功`);

    // 2. 解析和验证请求参数
    const url = new URL(request.url);

    // 只支持分页API路径
    if (!url.pathname.includes('/page/')) {
      console.log(`[WordIndex] 错误: 不支持的API路径格式`);
      return createWordIndexErrorResponse(
        'INVALID_LANGUAGE',
        '不支持的API端点，请使用分页格式: /api/v1/word-index/{learningLang}/{scaffoldingLang}/page/{pageNumber}'
      );
    }

    console.log(`[WordIndex] 解析分页API参数`);
    const params = parsePageParameters(url.pathname);

    if ('error' in params) {
      console.log(`[WordIndex] 参数验证失败: ${params.error}`);
      return createWordIndexErrorResponse(
        'INVALID_LANGUAGE',
        params.error
      );
    }

    console.log(`[WordIndex] 参数验证成功: learningLanguage=${params.learningLanguage}, scaffoldingLanguage=${params.scaffoldingLanguage}, pageNumber=${params.pageNumber}`);

    // 3. 执行增量数据查询
    const { results, lastSyncId } = await executeWordIndexQuery(params, env);

    // 4. 处理查询结果
    const requestTime = Date.now() - requestStartTime;
    const response = processWordIndexResults(
      results,
      lastSyncId,
      (params.pageNumber - 1) * 1000,  // 将页码转换为起始 sync_id
      requestTime
    );

    // 5. 返回成功响应（根据API类型应用不同缓存策略）
    const isPageRequest = url.pathname.includes('/page/');
    console.log(`[WordIndex] 增量同步完成: ${response.data.length}条记录, ${requestTime}ms, 分页请求: ${isPageRequest}`);
    return createWordIndexSuccessResponse(response, isPageRequest);

  } catch (error) {
    const requestTime = Date.now() - requestStartTime;
    console.error(`[WordIndex] 处理增量同步请求失败 (${requestTime}ms):`, error);
    
    return createWordIndexErrorResponse(
      'DATABASE_ERROR',
      '数据库查询失败，请稍后重试',
      500
    );
  }
}

/**
 * 处理CORS预检请求
 */
export function handleWordIndexOptions(): Response {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key',
      'Access-Control-Max-Age': '86400'
    }
  });
} 