// ================================
// SenseWord 后端数据类型定义
// 作为前后端类型映射的唯一事实来源
// ================================



// 支持的语言枚举 - 扩展更多语言支持
export type SupportedLanguageCode =
  | 'en' | 'zh' | 'ja' | 'de' | 'fr' | 'es'     // 核心语言（英语、中文等）
  | 'ko' | 'ru' | 'it' | 'pt' | 'ar'    // 新增亚洲和欧洲语言
  | 'hi' | 'th' | 'vi' | 'tr' | 'pl'    // 新增南亚和东南亚语言
  | 'nl' | 'sv' | 'da' | 'no' | 'fi'    // 新增北欧语言
  | 'id';                               // 印度尼西亚语

export interface LanguageMapping {
  [key: string]: string;
  // 核心语言
  en: 'English';
  zh: 'Chinese';
  ja: 'Japanese';
  de: 'German';
  fr: 'French';
  es: 'Spanish';

  // 新增亚洲语言
  ko: 'Korean';
  ru: 'Russian';
  ar: 'Arabic';
  hi: 'Hindi';
  th: 'Thai';
  vi: 'Vietnamese';
  tr: 'Turkish';
  id: 'Indonesian';

  // 新增欧洲语言
  it: 'Italian';
  pt: 'Portuguese';
  pl: 'Polish';
  nl: 'Dutch';
  sv: 'Swedish';
  da: 'Danish';
  no: 'Norwegian';
  fi: 'Finnish';
}

// 词频等级
export type WordFrequency = 'High' | 'Medium' | 'Low' | 'Rare';

// CEFR难度等级
export type CEFRLevel = 'A1' | 'A2' | 'B1' | 'B2' | 'C1' | 'C2';



// ================================
// 数据库记录接口 (FC-03 输出) - 更新为新数据库结构
// ================================
export interface WordDefinitionRecord {
  sync_id: number;                 // 增量同步ID，用于本地索引更新
  word: string;                    // 英语单词
  learningLanguage: string;        // 学习语言代码，如 "en"
  scaffoldingLanguage: string;     // 脚手架语言代码，如 "zh", "ja"
  contentJson: string;             // JSON字符串形式的完整内容
  coreDefinition?: string;         // 核心定义提取字段，避免JSON解析开销
  difficulty?: CEFRLevel;          // CEFR等级 (A1-C2)
  frequency?: WordFrequency;       // 词频等级 (High/Medium/Low/Rare)
  relatedConcepts?: string;        // 相关概念，JSON字符串格式
  partsOfSpeech?: string;          // 词性信息
  culturalRiskRegions?: string;    // 文化风险区域，JSON字符串格式
  createdAt: string;              // 创建时间，ISO格式
  updatedAt: string;              // 更新时间，ISO格式
}

// ================================
// API响应接口 (FC-01, FC-02, FC-07)
// ================================
export interface PhoneticSymbol {
  type: 'BrE' | 'NAmE';
  symbol: string;
  audioUrl?: string; // TTS音频链接，可选字段
}

export interface ContextualExplanation {
  nativeSpeakerIntent: string;
  emotionalResonance: string;
  vividImagery: string;
  etymologicalEssence: string;
}

export interface UsageExample {
  english: string;
  translation: string;
  audioUrl?: string; // TTS音频链接，可选字段
  phraseBreakdown?: Array<{
    phrase: string;
    translation: string;
    audioUrl?: string; // TTS音频链接，可选字段
  }>;
}

export interface UsageExampleCategory {
  category: string;
  examples: UsageExample[];
}

export interface UsageScenario {
  category: string;
  relevance: string;
  context: string;
}

export interface Collocation {
  type: string;
  pattern: string;
  examples: Array<{
    collocation: string;
    translation: string;
  }>;
}

export interface UsageNote {
  aspect: string;
  explanation: string;
  examples: Array<{
    sentence: string;
    translation: string;
  }>;
}

export interface Synonym {
  word: string;
  explanation: string;
  examples: Array<{
    sentence: string;
    translation: string;
  }>;
}

export interface WordMetadata {
  wordFrequency: WordFrequency;
  relatedConcepts: string[];
  isWordOfTheDayCandidate?: boolean; // KDD-010: 每日一词候选标记
}

export interface WordContent {
  difficulty: CEFRLevel;
  phoneticSymbols: PhoneticSymbol[];
  coreDefinition: string;
  contextualExplanation: ContextualExplanation;
  usageExamples: UsageExampleCategory[];
  usageScenarios: UsageScenario[];
  collocations: Collocation[];
  usageNotes: UsageNote[];
  synonyms: Synonym[];
}

// API最终响应格式
export interface WordDefinitionResponse {
  word: string;
  metadata: WordMetadata;
  content: WordContent;
}



// ================================
// 错误处理接口
// ================================
export type ErrorCode = 'INVALID_WORD' | 'INVALID_LANGUAGE' | 'AI_GENERATION_FAILED' | 'DATABASE_ERROR' | 'NETWORK_ERROR' | 'PROMPT_PROCESSING_FAILED' | 'INVALID_ACTION' | 'AUTHENTICATION_REQUIRED';

export interface SenseWordError {
  code: ErrorCode;
  message: string;
  details?: Record<string, any>;
}

export interface ErrorResponse {
  error: SenseWordError;
}



// ================================
// Worker环境接口
// ================================
export interface Env {
  DB: any; // Cloudflare D1Database类型在运行时由Workers环境提供 (主数据库)
  STATIC_API_KEY: string;

}

// ================================
// 请求上下文接口 (FC-02)
// ================================
export interface RequestContext {
  params: { wordName: string };
  query: { lang: string };
  headers: { 'X-Static-API-Key': string };
} 