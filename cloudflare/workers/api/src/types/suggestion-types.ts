// ================================
// KDD-004: 实时搜索建议服务类型定义
// ================================

import type { SupportedLanguageCode } from './word-types';

// ================================
// FC-04: 查询参数接口
// ================================

/**
 * 查询参数验证结果 (FC-04输出)
 */
export interface QueryParameters {
  isValid: boolean;
  query?: string;        // 验证通过的查询字符串
  limit?: number;        // 验证通过的数量限制 (1-20)
  language?: string;     // 验证通过的语言代码
  error?: string;        // 验证失败时的错误信息
}

/**
 * 原始查询参数 (FC-04输入)
 */
export interface RawQueryParams {
  q: string | null;      // 可能为null的查询字符串
  limit: string | null;  // 可能为null的限制参数
  lang: string | null;   // 可能为null的语言参数
}

// ================================
// FC-05: 预处理查询接口
// ================================

/**
 * 预处理后的查询对象 (FC-05输出)
 */
export interface ProcessedSearchQuery {
  originalQuery: string;     // "Progressive" - 保留原始输入
  normalizedQuery: string;   // "progressive" - 标准化后的查询
  queryPattern: string;      // "progressive%" - SQL LIKE模式
  minLength: boolean;        // true - 是否满足最小长度要求
  language?: string;         // "zh" - 语言过滤
  limit: number;            // 10 - 结果数量限制
}

// ================================
// FC-06: 数据库查询接口
// ================================

/**
 * 数据库原始行记录 (FC-06输出)
 */
export interface DatabaseRow {
  word: string;              // "progressive"
  language: string;          // "zh"
  coreDefinition: string;    // "逐步的，渐进的" (优化：直接读取，驼峰命名)
  word_length: number;       // 11 (计算得出，用于相关性评分)
}

/**
 * 数据库查询结果 (FC-06输出)
 */
export interface DatabaseQueryResult {
  results: DatabaseRow[];
  executionTime: number;     // 查询执行时间(毫秒)
  rowsScanned: number;       // 扫描的行数
}

// ================================
// FC-07.5: 相关性评分接口
// ================================

/**
 * 带评分的中间结果 (FC-07.5输出，仅内部使用)
 */
export interface ScoredSuggestionItem {
  word: string;              // "progressive"
  definition: string;        // "逐步的，渐进的"
  language: string;          // "zh"
  relevanceScore: number;    // 0.85 (内部评分)
  matchLength: number;       // 4 (匹配的字符数)
  wordLength: number;        // 11 (单词总长度)
  exactMatch: boolean;       // false (是否完全匹配)
}

// ================================
// FC-07: 处理后建议接口
// ================================

/**
 * 最终建议项 (FC-07输出，移除内部评分)
 */
export interface SuggestionItem {
  word: string;              // "progressive"
  definition: string;        // "逐步的，渐进的"
  language: string;          // "zh"
}

// ================================
// FC-08: 最终响应接口
// ================================

/**
 * 响应元数据 (FC-08输入)
 */
export interface ResponseMetadata {
  originalQuery: string;     // "progressive"
  startTime: number;         // 请求开始时间戳
}

/**
 * 最终API响应 (FC-08输出)
 */
export interface FinalSuggestionResult {
  suggestions: SuggestionItem[];
  metadata: {
    query: string;           // "progressive"
    total: number;           // 3
    responseTime: number;    // 67 (总响应时间)
  };
}

// ================================
// HTTP请求响应接口
// ================================

/**
 * 搜索建议成功响应
 */
export interface SearchSuggestionsSuccessResponse {
  success: true;
  suggestions: SuggestionItem[];
  query: string;
  total: number;
  responseTime: number;
}

/**
 * 搜索建议错误响应
 */
export interface SearchSuggestionsErrorResponse {
  success: false;
  error: 'INVALID_QUERY' | 'QUERY_TOO_SHORT' | 'RATE_LIMITED' | 'SYSTEM_ERROR';
  message: string;
  query?: string;
}

/**
 * 搜索建议API响应联合类型
 */
export type SearchSuggestionsResponse = SearchSuggestionsSuccessResponse | SearchSuggestionsErrorResponse;

// ================================
// 错误处理
// ================================

/**
 * 搜索建议相关错误
 */
export interface SearchSuggestionError {
  code: 'INVALID_QUERY' | 'QUERY_TOO_SHORT' | 'DATABASE_ERROR' | 'SYSTEM_ERROR';
  message: string;
  details?: {
    query?: string;
    originalError?: string;
  };
} 