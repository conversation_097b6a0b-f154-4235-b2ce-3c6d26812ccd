import type {
  WordDefinitionRecord,
  WordDefinitionResponse,
  SenseWordError
} from '../types/word-types';

// ================================
// FC-03: 数据库查询服务
// ================================
export async function findWordDefinition(
  db: any, // Cloudflare D1Database
  word: string,
  learningLanguage: string,
  scaffoldingLanguage: string
): Promise<WordDefinitionRecord | null> {
  console.log(`[Word Service] 开始查询数据库，单词: ${word}, 学习语言: ${learningLanguage}, 脚手架语言: ${scaffoldingLanguage}`);

  try {
    // 标准化输入参数
    const normalizedWord = word.toLowerCase().trim();
    const normalizedLearningLanguage = learningLanguage.toLowerCase().trim();
    const normalizedScaffoldingLanguage = scaffoldingLanguage.toLowerCase().trim();

    console.log(`[Word Service] 标准化参数: ${normalizedWord}, ${normalizedLearningLanguage}, ${normalizedScaffoldingLanguage}`);

    // 执行数据库查询 - 使用新的数据库结构，COALESCE处理空值
    const query = `
      SELECT
        sync_id,
        word,
        learningLanguage,
        scaffoldingLanguage,
        contentJson,
        COALESCE(coreDefinition, JSON_EXTRACT(contentJson, '$.content.coreDefinition')) as coreDefinition,
        COALESCE(difficulty, JSON_EXTRACT(contentJson, '$.content.difficulty')) as difficulty,
        COALESCE(frequency, JSON_EXTRACT(contentJson, '$.metadata.wordFrequency')) as frequency,
        COALESCE(relatedConcepts, JSON_EXTRACT(contentJson, '$.metadata.relatedConcepts')) as relatedConcepts,
        COALESCE(partsOfSpeech, '') as partsOfSpeech,
        COALESCE(culturalRiskRegions, JSON_EXTRACT(contentJson, '$.metadata.culturalRiskRegions'), '[]') as culturalRiskRegions,
        createdAt,
        updatedAt
      FROM word_definitions
      WHERE word = ? AND learningLanguage = ? AND scaffoldingLanguage = ?
      LIMIT 1
    `;

    const result = await db.prepare(query)
      .bind(normalizedWord, normalizedLearningLanguage, normalizedScaffoldingLanguage)
      .first();

    if (result) {
      console.log(`[Word Service] 找到数据库记录，sync_id: ${result.sync_id}, 创建时间: ${result.createdAt}`);

      // 字段验证和默认值处理
      const validatedResult = {
        ...result,
        // 确保必需字段存在
        sync_id: result.sync_id || 0,
        word: result.word || '',
        learningLanguage: result.learningLanguage || 'en',
        scaffoldingLanguage: result.scaffoldingLanguage || 'zh',
        contentJson: result.contentJson || '{}',
        // 可选字段提供默认值
        coreDefinition: result.coreDefinition || '',
        difficulty: result.difficulty || 'B1',
        frequency: result.frequency || 'Medium',
        relatedConcepts: result.relatedConcepts || '[]',
        partsOfSpeech: result.partsOfSpeech || '',
        culturalRiskRegions: result.culturalRiskRegions || '[]',
        createdAt: result.createdAt || new Date().toISOString(),
        updatedAt: result.updatedAt || new Date().toISOString()
      };

      console.log(`[Word Service] 字段验证完成，所有字段已标准化`);
      return validatedResult as WordDefinitionRecord;
    } else {
      console.log(`[Word Service] 未找到数据库记录`);
      return null;
    }

  } catch (error) {
    console.error(`[Word Service] 数据库查询失败:`, error);

    throw {
      code: 'DATABASE_ERROR',
      message: '数据库查询时发生错误',
      details: {
        word: word,
        learningLanguage: learningLanguage,
        scaffoldingLanguage: scaffoldingLanguage,
        originalError: error instanceof Error ? error.message : String(error)
      }
    } as SenseWordError;
  }
}

// ================================
// FC-06: TTS音频URL转换服务
// ================================

/**
 * 将ttsId转换为完整的音频URL
 * @param ttsId - 24位哈希ID
 * @returns 完整的音频URL或null（如果ttsId无效）
 */
export function ttsIdToAudioUrl(ttsId: string | undefined | null): string | null {
  // 验证ttsId是否存在且格式正确
  if (!ttsId || typeof ttsId !== 'string' || ttsId.trim().length === 0) {
    return null;
  }

  const cleanTtsId = ttsId.trim();

  // 验证ttsId格式（24位哈希）
  if (!/^[a-f0-9]{24}$/i.test(cleanTtsId)) {
    console.warn(`[Word Service] 无效的ttsId格式: ${cleanTtsId}`);
    return null;
  }

  // 生成标准音频URL
  return `https://audio.senseword.app/${cleanTtsId}.wav`;
}

/**
 * 递归处理对象，将所有ttsId字段转换为audioUrl字段
 * @param obj - 要处理的对象
 * @returns 处理后的对象
 */
function convertTtsIdsToAudioUrls(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertTtsIdsToAudioUrls(item));
  }

  if (typeof obj === 'object') {
    const result: any = {};

    for (const [key, value] of Object.entries(obj)) {
      if (key === 'ttsId' && typeof value === 'string') {
        // 将ttsId转换为audioUrl
        const audioUrl = ttsIdToAudioUrl(value);
        if (audioUrl) {
          result.audioUrl = audioUrl;
        }
        // 保留原始ttsId用于调试（可选）
        // result.ttsId = value;
      } else {
        result[key] = convertTtsIdsToAudioUrls(value);
      }
    }

    return result;
  }

  return obj;
}

// ================================
// FC-07: 响应格式化服务
// ================================
export function formatWordResponse(
  source: WordDefinitionRecord
): WordDefinitionResponse {
  console.log(`[Word Service] 开始格式化响应数据`);

  try {
    // 处理数据库记录
    console.log(`[Word Service] 格式化数据库记录: ${source.word}`);

    const content = JSON.parse(source.contentJson);
    const relatedConcepts = source.relatedConcepts
      ? JSON.parse(source.relatedConcepts)
      : [];

    // 转换content中的所有ttsId为audioUrl
    const processedContent = convertTtsIdsToAudioUrls(content);

    console.log(`[Word Service] 已处理ttsId转换，音标数量: ${processedContent.phoneticSymbols?.length || 0}`);

    return {
      word: source.word,
      metadata: {
        wordFrequency: source.frequency,
        relatedConcepts: relatedConcepts
      },
      content: processedContent
    };

  } catch (error) {
    console.error(`[Word Service] 响应格式化失败:`, error);

    throw {
      code: 'DATABASE_ERROR',
      message: '响应数据格式化时发生错误',
      details: {
        sourceType: 'database',
        originalError: error instanceof Error ? error.message : String(error)
      }
    } as SenseWordError;
  }
}





