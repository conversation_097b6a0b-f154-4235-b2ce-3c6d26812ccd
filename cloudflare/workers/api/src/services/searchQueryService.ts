// ================================
// KDD-004: 核心搜索查询服务
// 实现FC-05到FC-07.5的搜索处理逻辑
// ================================

import type {
  QueryParameters,
  ProcessedSearchQuery,
  DatabaseRow,
  DatabaseQueryResult,
  ScoredSuggestionItem,
  SuggestionItem,
  SearchSuggestionError
} from '../types/suggestion-types';
import type { Env } from '../types/word-types';

// ================================
// FC-05: 查询预处理器
// ================================

/**
 * 预处理验证后的查询参数，生成标准化的搜索查询对象
 */
export function preprocessQuery(params: QueryParameters): ProcessedSearchQuery {
  console.log(`[SearchQuery] 开始预处理查询参数: ${params.query}`);
  
  if (!params.isValid || !params.query) {
    throw {
      code: 'INVALID_QUERY',
      message: '查询参数无效',
      details: { query: params.query }
    } as SearchSuggestionError;
  }

  const originalQuery = params.query;
  const normalizedQuery = originalQuery.toLowerCase().trim();
  const queryPattern = `${normalizedQuery}%`;
  const minLength = normalizedQuery.length >= 2;
  
  const processed: ProcessedSearchQuery = {
    originalQuery,
    normalizedQuery,
    queryPattern,
    minLength,
    language: params.language,
    limit: params.limit || 10
  };

  console.log(`[SearchQuery] 预处理完成: ${normalizedQuery} -> ${queryPattern}`);
  return processed;
}

// ================================
// FC-06: 数据库查询执行器
// ================================

/**
 * 执行优化的数据库查询，直接读取core_definition列
 */
export async function executeSearchQuery(
  processedQuery: ProcessedSearchQuery,
  env: Env
): Promise<DatabaseQueryResult> {
  console.log(`[SearchQuery] 开始执行数据库查询: ${processedQuery.queryPattern}`);
  
  const startTime = Date.now();
  
  try {
    // 构建优化的SQL查询 - 直接读取coreDefinition
    let sql = `
      SELECT 
        word, 
        language, 
        coreDefinition, 
        LENGTH(word) as word_length
      FROM word_definitions
      WHERE word LIKE ?
    `;
    
    const params: (string | number)[] = [processedQuery.queryPattern];

    // 添加语言过滤（如果指定）
    if (processedQuery.language) {
      sql += ` AND language = ?`;
      params.push(processedQuery.language);
    }

    // 只查询有coreDefinition的记录
    sql += ` AND coreDefinition IS NOT NULL`;
    
    // 添加排序和限制
    sql += ` ORDER BY LENGTH(word) ASC, word ASC LIMIT ?`;
    params.push(processedQuery.limit);

    console.log(`[SearchQuery] 执行SQL: ${sql}`);
    console.log(`[SearchQuery] 参数: ${JSON.stringify(params)}`);

    // 执行查询
    const result = await env.DB.prepare(sql).bind(...params).all();
    
    const executionTime = Date.now() - startTime;
    const results = result.results as DatabaseRow[];
    
    console.log(`[SearchQuery] 查询完成: ${results.length}条结果, ${executionTime}ms`);
    
    return {
      results,
      executionTime,
      rowsScanned: results.length
    };

  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`[SearchQuery] 数据库查询失败 (${executionTime}ms):`, error);
    
    throw {
      code: 'DATABASE_ERROR',
      message: '数据库查询失败',
      details: {
        query: processedQuery.originalQuery,
        originalError: error instanceof Error ? error.message : String(error)
      }
    } as SearchSuggestionError;
  }
}

// ================================
// FC-07.5: 相关性评分计算器
// ================================

/**
 * 为数据库结果计算相关性评分
 */
export function calculateRelevanceScores(
  dbResults: DatabaseRow[],
  query: ProcessedSearchQuery
): ScoredSuggestionItem[] {
  console.log(`[SearchQuery] 开始计算相关性评分: ${dbResults.length}条结果`);
  
  const queryLength = query.normalizedQuery.length;
  
  const scoredResults: ScoredSuggestionItem[] = dbResults.map(row => {
    const wordLength = row.word_length;
    const matchLength = Math.min(queryLength, wordLength);
    const exactMatch = row.word === query.normalizedQuery;
    
    // 相关性评分算法:
    // 1. 完全匹配得满分
    // 2. 前缀匹配按匹配长度和单词长度计算
    // 3. 短单词获得额外奖励
    let relevanceScore: number;
    
    if (exactMatch) {
      relevanceScore = 1.0;
    } else {
      // 基础评分：匹配长度与单词长度的比例
      const lengthRatio = matchLength / wordLength;
      
      // 查询覆盖度：匹配长度与查询长度的比例
      const queryRatio = matchLength / queryLength;
      
      // 组合评分，优先考虑查询覆盖度
      relevanceScore = (queryRatio * 0.7) + (lengthRatio * 0.3);
      
      // 短单词奖励 (8个字符以下)
      if (wordLength <= 8) {
        relevanceScore += 0.1;
      }
      
      // 确保评分在[0, 1]范围内
      relevanceScore = Math.min(1.0, Math.max(0.0, relevanceScore));
    }
    
    return {
      word: row.word,
      definition: row.coreDefinition || '定义处理中...',
      language: row.language,
      relevanceScore,
      matchLength,
      wordLength,
      exactMatch
    };
  });

  console.log(`[SearchQuery] 相关性评分完成`);
  return scoredResults;
}

// ================================
// FC-07: 搜索结果处理器
// ================================

/**
 * 处理评分后的搜索结果，排序并移除内部评分信息
 */
export function processSearchResults(
  scoredResults: ScoredSuggestionItem[]
): SuggestionItem[] {
  console.log(`[SearchQuery] 开始处理搜索结果: ${scoredResults.length}条`);
  
  // 按相关性评分排序（降序）
  const sortedResults = scoredResults.sort((a, b) => {
    // 主要按评分排序
    if (b.relevanceScore !== a.relevanceScore) {
      return b.relevanceScore - a.relevanceScore;
    }
    
    // 评分相同时，按单词长度排序（短的优先）
    if (a.wordLength !== b.wordLength) {
      return a.wordLength - b.wordLength;
    }
    
    // 长度也相同时，按字母顺序排序
    return a.word.localeCompare(b.word);
  });

  // 移除内部评分信息，只保留公开字段
  const finalResults: SuggestionItem[] = sortedResults.map(item => ({
    word: item.word,
    definition: item.definition,
    language: item.language
  }));

  console.log(`[SearchQuery] 结果处理完成: ${finalResults.length}条，按相关性排序`);
  return finalResults;
}

// ================================
// 综合搜索服务 (组合多个FC)
// ================================

/**
 * 执行完整的搜索流程：预处理 -> 查询 -> 评分 -> 排序
 */
export async function executeFullSearchFlow(
  params: QueryParameters,
  env: Env
): Promise<SuggestionItem[]> {
  console.log(`[SearchQuery] 开始完整搜索流程: ${params.query}`);
  
  try {
    // FC-05: 预处理查询
    const processedQuery = preprocessQuery(params);
    
    if (!processedQuery.minLength) {
      console.log(`[SearchQuery] 查询字符串过短: ${processedQuery.normalizedQuery}`);
      return [];
    }

    // FC-06: 执行数据库查询
    const dbResult = await executeSearchQuery(processedQuery, env);
    
    if (dbResult.results.length === 0) {
      console.log(`[SearchQuery] 无匹配结果`);
      return [];
    }

    // FC-07.5: 计算相关性评分
    const scoredResults = calculateRelevanceScores(dbResult.results, processedQuery);

    // FC-07: 处理并排序结果
    const finalResults = processSearchResults(scoredResults);

    console.log(`[SearchQuery] 搜索流程完成: ${finalResults.length}条结果`);
    return finalResults;

  } catch (error) {
    console.error(`[SearchQuery] 搜索流程失败:`, error);
    
    if (error && typeof error === 'object' && 'code' in error) {
      throw error;
    }
    
    throw {
      code: 'SYSTEM_ERROR',
      message: '搜索服务内部错误',
      details: {
        query: params.query,
        originalError: error instanceof Error ? error.message : String(error)
      }
    } as SearchSuggestionError;
  }
} 