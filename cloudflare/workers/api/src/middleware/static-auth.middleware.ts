// ================================
// FC-01: 统一静态密钥验证中间件 (API Worker版)
// 提供可重用的静态API密钥验证逻辑，确保所有需要保护的端点都有统一的安全检查
// ================================

import type { Env } from '../types/word-types';

/**
 * 验证静态API密钥
 * @param request HTTP请求对象
 * @param env Cloudflare Worker环境变量
 * @returns Promise<{ isValid: boolean; error?: string }> 验证结果
 */
export async function validateStaticApiKey(
  request: Request,
  env: Env
): Promise<{ isValid: boolean; error?: string }> {
  console.log('[Static Auth] 开始验证静态API密钥');
  
  try {
    // 提取静态API密钥
    const apiKey = request.headers.get('X-Static-API-Key');
    
    if (!apiKey) {
      console.warn('[Static Auth] 缺少X-Static-API-Key头');
      return { 
        isValid: false, 
        error: 'Missing X-Static-API-Key header' 
      };
    }
    
    // 验证密钥
    if (!env.STATIC_API_KEY) {
      console.error('[Static Auth] 环境变量STATIC_API_KEY未配置');
      return { 
        isValid: false, 
        error: 'Static API key not configured' 
      };
    }
    
    if (apiKey !== env.STATIC_API_KEY) {
      console.warn('[Static Auth] 静态API密钥验证失败');
      return { 
        isValid: false, 
        error: 'Invalid X-Static-API-Key' 
      };
    }
    
    console.log('[Static Auth] 静态API密钥验证成功');
    return { isValid: true };
    
  } catch (error) {
    console.error('[Static Auth] 验证过程中发生错误:', error);
    return { 
      isValid: false, 
      error: 'Static API key validation failed' 
    };
  }
}

/**
 * 创建标准化的静态密钥验证失败响应
 * @param error 错误信息
 * @returns Response 401响应
 */
export function createStaticKeyErrorResponse(error: string): Response {
  const errorResponse = {
    error: {
      code: 'INVALID_API_KEY',
      message: error
    }
  };
  
  return new Response(JSON.stringify(errorResponse), {
    status: 401,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    }
  });
} 