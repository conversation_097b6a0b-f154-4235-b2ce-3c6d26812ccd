import { findWordDefinition, formatWordResponse } from './services/word.service';

import { handleWordIndexUpdates, handleWordIndexOptions } from './handlers/wordIndexHandler';
import { handleDownloadRange, handleDownloadRangeOptions } from './handlers/downloadRangeHandler';

import type { Env, ErrorResponse, SenseWordError } from './types/word-types';

// ================================
// Worker主入口点
// ================================
export default {
  async fetch(request: Request, env: Env, ctx: any): Promise<Response> {
    console.log(`[Worker] 接收请求: ${request.method} ${request.url}`);
    
    const corsHeaders = {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Static-API-Key',
    };

    // 处理OPTIONS预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders
      });
    }

    try {
      const url = new URL(request.url);

      // ================================
      // KDD-003: 固定分页增量同步API路由 (重构)
      // ================================

      // 路由匹配: GET /api/v1/word-index/:learningLang/:scaffoldingLang/page/:pageNumber - 固定分页API
      const pageRouteMatch = url.pathname.match(/^\/api\/v1\/word-index\/([^\/]+)\/([^\/]+)\/page\/(\d+)$/);
      if (pageRouteMatch && request.method === 'GET') {
        return await handleWordIndexUpdates(request, env);
      }

      // 路由匹配: OPTIONS /api/v1/word-index/:learningLang/:scaffoldingLang/page/:pageNumber - CORS预检
      if (pageRouteMatch && request.method === 'OPTIONS') {
        return handleWordIndexOptions();
      }

      // 路由匹配: GET /api/v1/word-index/:learningLang/:scaffoldingLang/download-range - 下载范围计算API
      const downloadRangeRouteMatch = url.pathname.match(/^\/api\/v1\/word-index\/([^\/]+)\/([^\/]+)\/download-range$/);
      if (downloadRangeRouteMatch && request.method === 'GET') {
        return await handleDownloadRange(request, env);
      }

      // 路由匹配: OPTIONS /api/v1/word-index/:learningLang/:scaffoldingLang/download-range - CORS预检
      if (downloadRangeRouteMatch && request.method === 'OPTIONS') {
        return handleDownloadRangeOptions();
      }

      // ================================
      // RESTful API路由 (新版本)
      // ================================

      // 路由匹配: GET /api/v1/words/{learningLang}/{scaffoldingLang}/{wordName} - RESTful单词查询
      const restfulWordMatch = url.pathname.match(/^\/api\/v1\/words\/([^\/]+)\/([^\/]+)\/([^\/]+)$/);
      if (restfulWordMatch && request.method === 'GET') {
        return await handleRestfulWordQuery(request, env, corsHeaders);
      }

      // ================================
      // 向后兼容API路由 (保持兼容性)
      // ================================

      // 路由匹配: GET /api/v1/word/{wordName} - 单词查询 (向后兼容)
      const wordMatch = url.pathname.match(/^\/api\/v1\/word\/([^\/]+)$/);
      if (wordMatch && request.method === 'GET') {
        return await handleWordQuery(request, env, corsHeaders);
      }

      // 404 - 路由未找到
      console.log(`[Worker] 未找到匹配的路由: ${url.pathname}`);
      const errorResponse: ErrorResponse = {
        error: {
          code: 'INVALID_WORD',
          message: '请求的路径未找到'
        }
      };

      return new Response(JSON.stringify(errorResponse), {
        status: 404,
        headers: corsHeaders
      });

    } catch (error) {
      console.error('[Worker] 处理请求时发生未捕获的错误:', error);
      
      const errorResponse: ErrorResponse = {
        error: {
          code: 'NETWORK_ERROR',
          message: '服务器内部错误'
        }
      };

      return new Response(JSON.stringify(errorResponse), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
};



// ================================
// RESTful API端点处理器 (新版本)
// ================================
async function handleRestfulWordQuery(
  request: Request,
  env: Env,
  corsHeaders: Record<string, string>
): Promise<Response> {
  console.log(`[Worker] 开始处理RESTful单词查询请求`);

  try {
    // 1. 解析和验证请求参数
    const url = new URL(request.url);
    const wordMatch = url.pathname.match(/^\/api\/v1\/words\/([^\/]+)\/([^\/]+)\/([^\/]+)$/);

    if (!wordMatch) {
      throw {
        code: 'INVALID_WORD',
        message: '单词参数无效'
      } as SenseWordError;
    }

    const learningLanguage = decodeURIComponent(wordMatch[1]).trim();
    const scaffoldingLanguage = decodeURIComponent(wordMatch[2]).trim();
    const wordName = decodeURIComponent(wordMatch[3]).trim();

    console.log(`[Worker] 解析RESTful参数: 单词=${wordName}, 学习语言=${learningLanguage}, 脚手架语言=${scaffoldingLanguage}`);

    // 2. 验证静态API密钥
    const apiKey = request.headers.get('X-Static-API-Key');
    if (!apiKey || apiKey !== env.STATIC_API_KEY) {
      console.log(`[Worker] API密钥验证失败`);

      const errorResponse: ErrorResponse = {
        error: {
          code: 'INVALID_WORD',
          message: 'API密钥无效或缺失'
        }
      };

      return new Response(JSON.stringify(errorResponse), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
      });
    }

    console.log(`[Worker] API密钥验证成功`);

    // 3. 验证输入参数
    if (!wordName || wordName.length === 0) {
      throw {
        code: 'INVALID_WORD',
        message: '单词参数不能为空'
      } as SenseWordError;
    }

    // 验证支持的语言列表
    const supportedLanguages = [
      'en', 'zh', 'ja', 'de', 'fr', 'es',
      'ko', 'ru', 'it', 'pt', 'ar',
      'hi', 'th', 'vi', 'tr', 'pl',
      'nl', 'sv', 'da', 'no', 'fi',
      'id'
    ];

    if (!supportedLanguages.includes(learningLanguage) || !supportedLanguages.includes(scaffoldingLanguage)) {
      throw {
        code: 'INVALID_LANGUAGE',
        message: `不支持的语言代码: ${learningLanguage}/${scaffoldingLanguage}，支持的语言: ${supportedLanguages.join(', ')}`
      } as SenseWordError;
    }

    // 4. 查询数据库中是否已存在该单词定义
    const existingRecord = await findWordDefinition(env.DB, wordName, learningLanguage, scaffoldingLanguage);

    if (existingRecord) {
      console.log(`[Worker] 找到现有记录，直接返回`);

      // 格式化并返回现有记录
      const response = formatWordResponse(existingRecord);

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: {
          ...corsHeaders,
          'Cache-Control': 'public, max-age=86400'
        }
      });
    }

    console.log(`[Worker] 未找到现有记录，返回404`);

    // 数据库中不存在的单词返回404错误
    const errorResponse: ErrorResponse = {
      error: {
        code: 'INVALID_WORD',
        message: `单词定义不存在: ${wordName} (${learningLanguage}/${scaffoldingLanguage})`
      }
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 404,
      headers: {
        ...corsHeaders,
        'Cache-Control': 'public, max-age=300'
      }
    });

  } catch (error) {
    console.error(`[Worker] 处理RESTful单词查询失败:`, error);

    const senseWordError = error as SenseWordError;
    const errorResponse: ErrorResponse = {
      error: {
        code: senseWordError.code || 'NETWORK_ERROR',
        message: senseWordError.message || '处理请求时发生错误'
      }
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });
  }
}

// ================================
// FC-02: Cloudflare Worker API端点处理器 (向后兼容)
// ================================
async function handleWordQuery(
  request: Request,
  env: Env,
  corsHeaders: Record<string, string>
): Promise<Response> {
  console.log(`[Worker] 开始处理单词查询请求 (向后兼容模式)`);

  try {
    // 1. 解析和验证请求参数
    const url = new URL(request.url);
    const wordMatch = url.pathname.match(/^\/api\/v1\/word\/([^\/]+)$/);

    if (!wordMatch) {
      throw {
        code: 'INVALID_WORD',
        message: '单词参数无效'
      } as SenseWordError;
    }

    const wordName = decodeURIComponent(wordMatch[1]).trim();
    const langCode = url.searchParams.get('lang') || 'zh';

    console.log(`[Worker] 解析参数: 单词=${wordName}, 语言=${langCode} (向后兼容模式，默认学习语言=en)`);

    // 向后兼容：将单一语言参数转换为双语言参数
    const learningLanguage = 'en'; // 默认学习语言为英语
    const scaffoldingLanguage = langCode; // 脚手架语言使用原lang参数

    // 2. 验证静态API密钥
    const apiKey = request.headers.get('X-Static-API-Key');
    if (!apiKey || apiKey !== env.STATIC_API_KEY) {
      console.log(`[Worker] API密钥验证失败`);
      
      const errorResponse: ErrorResponse = {
        error: {
          code: 'INVALID_WORD',
          message: 'API密钥无效或缺失'
        }
      };

      return new Response(JSON.stringify(errorResponse), {
        status: 401,
        headers: {
          ...corsHeaders,
          'Cache-Control': 'no-cache, no-store, must-revalidate' // 认证错误：不缓存
        }
      });
    }

    console.log(`[Worker] API密钥验证成功`);

    // 3. 验证输入参数
    if (!wordName || wordName.length === 0) {
      throw {
        code: 'INVALID_WORD',
        message: '单词参数不能为空'
      } as SenseWordError;
    }

    // 验证支持的语言列表（与types/word-types.ts保持同步）
    const supportedLanguages = [
      'en', 'zh', 'ja', 'de', 'fr', 'es',     // 核心语言（英语、中文等）
      'ko', 'ru', 'it', 'pt', 'ar',    // 新增亚洲和欧洲语言
      'hi', 'th', 'vi', 'tr', 'pl',    // 新增南亚和东南亚语言
      'nl', 'sv', 'da', 'no', 'fi',    // 新增北欧语言
      'id'                             // 印度尼西亚语
    ];
    
    if (!supportedLanguages.includes(langCode)) {
      throw {
        code: 'INVALID_LANGUAGE',
        message: `不支持的语言代码: ${langCode}，支持的语言: ${supportedLanguages.join(', ')}`
      } as SenseWordError;
    }

    // 4. 查询数据库中是否已存在该单词定义 (使用新的函数签名)
    const existingRecord = await findWordDefinition(env.DB, wordName, learningLanguage, scaffoldingLanguage);

    if (existingRecord) {
      console.log(`[Worker] 找到现有记录，直接返回`);

      // 格式化并返回现有记录
      const response = formatWordResponse(existingRecord);

      return new Response(JSON.stringify(response), {
        status: 200,
        headers: {
          ...corsHeaders,
          'Cache-Control': 'public, max-age=86400'
        }
      });
    }

    console.log(`[Worker] 未找到现有记录，返回404`);

    // 数据库中不存在的单词返回404错误
    const errorResponse: ErrorResponse = {
      error: {
        code: 'INVALID_WORD',
        message: `单词定义不存在: ${wordName} (${learningLanguage}/${scaffoldingLanguage})`
      }
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 404,
      headers: {
        ...corsHeaders,
        'Cache-Control': 'public, max-age=300' // 短缓存，避免频繁查询不存在的单词
      }
    });

  } catch (error) {
    console.error('[Worker] 处理单词查询时发生错误:', error);
    
    let errorResponse: ErrorResponse;
    let statusCode = 500;

    if (error && typeof error === 'object' && 'code' in error) {
      // 处理已知的SenseWordError
      errorResponse = { error: error as SenseWordError };
      
      switch ((error as SenseWordError).code) {
        case 'INVALID_WORD':
        case 'INVALID_LANGUAGE':
          statusCode = 400;
          break;
        case 'AI_GENERATION_FAILED':
        case 'PROMPT_PROCESSING_FAILED':
          statusCode = 503;
          break;
        case 'DATABASE_ERROR':
          statusCode = 500;
          break;
        default:
          statusCode = 500;
      }
    } else {
      // 处理未知错误
      errorResponse = {
        error: {
          code: 'NETWORK_ERROR',
          message: '处理请求时发生未知错误'
        }
      };
    }

    return new Response(JSON.stringify(errorResponse), {
      status: statusCode,
      headers: {
        ...corsHeaders,
        'Cache-Control': 'public, max-age=300' // 错误响应：5分钟缓存
      }
    });
  }
}







