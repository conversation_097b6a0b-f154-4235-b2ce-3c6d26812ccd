# SenseWord API Worker 部署配置指南

## 🎯 已完成的配置

✅ **D1数据库创建**: `senseword-word-db` (ID: 9f3369db-eb90-4917-8791-6b7f05e972c5)
✅ **数据库表结构**: `word_definitions` 表已创建并同步到远程
✅ **Assets配置**: 提示词文件管理已配置
✅ **wrangler.toml**: D1绑定配置已更新

## 📝 待配置的环境变量

请在 `wrangler.toml` 中更新以下环境变量：

### 开发环境 (Development)
```toml
[env.development.vars]
GEMINI_API_KEY = "your-actual-gemini-api-key"
GEMINI_MODEL_ID = "gemini-2.5-flash-lite-preview-06-17"
STATIC_API_KEY = "your-development-static-api-key"
```

### 生产环境 (Production)
```toml
[env.production.vars]
GEMINI_API_KEY = "your-production-gemini-api-key"
GEMINI_MODEL_ID = "gemini-2.5-flash-lite-preview-06-17"
STATIC_API_KEY = "your-production-static-api-key"
```

## 🚀 部署命令

### 开发环境测试
```bash
# 启动本地开发服务器
npx wrangler dev --env development

# 测试API端点
curl -H "X-Static-API-Key: your-dev-key" \
  "http://localhost:8080/api/v1/word/progressive?lang=zh"
```

### 生产环境部署
```bash
# 部署到生产环境
npx wrangler deploy --env production

# 验证部署
curl -H "X-Static-API-Key: your-prod-key" \
  "https://your-worker-domain.workers.dev/api/v1/word/progressive?lang=zh"
```

## 🔧 数据库状态

- **数据库ID**: `9f3369db-eb90-4917-8791-6b7f05e972c5`
- **表结构**: 已创建 `word_definitions` 表
- **远程同步**: ✅ 完成
- **本地同步**: ✅ 完成

## 📊 API端点

- `GET /api/v1/word/{word}?lang={lang}` - 查询/生成单词定义
- `POST /api/v1/feedback` - 用户反馈接口

## 🔒 安全配置

1. 请确保API密钥足够复杂
2. 生产环境建议使用 Cloudflare Workers Secrets
3. 定期轮换API密钥

## 下一步

1. 配置实际的API密钥
2. 运行开发环境测试
3. 部署到生产环境
4. 配置iOS应用的API端点URL 