{"name": "senseword-api-worker", "version": "1.0.0", "description": "SenseWord单词查询与生成API - Cloudflare Worker", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "build": "wrangler deploy --dry-run", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"@cloudflare/workers-types": "^4.20231025.0", "typescript": "^5.8.3", "vitest": "^1.0.0", "wrangler": "^4.21.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["cloudflare", "worker", "api", "senseword", "gemini", "ai", "word-learning"], "author": "SenseWord Team", "license": "MIT"}