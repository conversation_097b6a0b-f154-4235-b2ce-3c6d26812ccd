#!/bin/bash

# D1 批量迁移执行脚本
# 执行剩余的 26 个批次（002-027）

set -e  # 遇到错误立即退出

MIGRATION_DIR="/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/cloudflare/d1/tts-db/migrations/local_sync"
DATABASE_NAME="senseword-tts-db"
START_BATCH=4
END_BATCH=27

echo "🚀 开始执行 D1 批量迁移"
echo "📁 迁移目录: $MIGRATION_DIR"
echo "🗄️ 数据库: $DATABASE_NAME"
echo "📊 批次范围: $START_BATCH - $END_BATCH"
echo "================================"

success_count=0
failed_count=0
failed_batches=()

for i in $(seq -f "%03g" $START_BATCH $END_BATCH); do
    batch_file="$MIGRATION_DIR/migration_batch_${i}_of_027.sql"
    
    if [ ! -f "$batch_file" ]; then
        echo "❌ 批次文件不存在: $batch_file"
        failed_count=$((failed_count + 1))
        failed_batches+=("$i")
        continue
    fi
    
    echo "📤 执行批次 $i/27: $(basename "$batch_file")"
    
    # 执行 wrangler d1 execute (自动确认)
    if echo "y" | wrangler d1 execute "$DATABASE_NAME" --remote --file "$batch_file"; then
        echo "✅ 批次 $i 执行成功"
        success_count=$((success_count + 1))
    else
        echo "❌ 批次 $i 执行失败"
        failed_count=$((failed_count + 1))
        failed_batches+=("$i")
    fi
    
    # 短暂延迟，避免过快请求
    sleep 2
    
    echo "--------------------------------"
done

echo ""
echo "🎉 迁移执行完成！"
echo "================================"
echo "✅ 成功: $success_count 个批次"
echo "❌ 失败: $failed_count 个批次"

if [ $failed_count -gt 0 ]; then
    echo ""
    echo "❌ 失败的批次:"
    for batch in "${failed_batches[@]}"; do
        echo "   - 批次 $batch"
    done
    echo ""
    echo "💡 可以手动重试失败的批次:"
    for batch in "${failed_batches[@]}"; do
        echo "   wrangler d1 execute $DATABASE_NAME --remote --file \"$MIGRATION_DIR/migration_batch_${batch}_of_027.sql\""
    done
fi

echo ""
echo "📊 查看最终数据库状态:"
echo "   wrangler d1 execute $DATABASE_NAME --remote --command \"SELECT COUNT(*) as total_tasks FROM tts_tasks;\""
