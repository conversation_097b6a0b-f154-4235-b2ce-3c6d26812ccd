{"name": "senseword-tts-worker", "version": "1.0.0", "description": "SenseWord TTS音频处理Worker - 线性处理版", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "build": "wrangler deploy --dry-run", "test": "vitest", "test:coverage": "vitest --coverage"}, "devDependencies": {"@cloudflare/workers-types": "^4.20231025.0", "typescript": "^5.0.0", "vitest": "^1.0.0", "wrangler": "^4.25.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["cloudflare", "worker", "tts", "audio", "senseword", "azure"], "author": "SenseWord Team", "license": "MIT", "dependencies": {"jszip": "^3.10.1"}}