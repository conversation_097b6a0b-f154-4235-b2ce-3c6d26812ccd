name = "senseword-tts-worker"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 定时轮询配置 - 每1分钟执行一次
[triggers]
crons = ["*/1 * * * *"]

# 日志观察配置
[observability]
enabled = true
# head_sampling_rate = 0.01 # 1% 采样率，降低日志量

# TTS数据库绑定
[[d1_databases]]
binding = "TTS_DB"
database_name = "senseword-tts-db"
database_id = "253bb3ab-6300-4d92-b0f7-e746ef8885b3"
migrations_dir = "../../d1/tts-db/migrations"

# 音频资产存储绑定
[[r2_buckets]]
binding = "AUDIO_BUCKET"
bucket_name = "senseword-audio"

# Durable Objects 配置
[[durable_objects.bindings]]
name = "RATE_LIMITER"
class_name = "GlobalRateLimiter"
script_name = "senseword-tts-worker"

# Durable Objects 迁移配置
[[migrations]]
tag = "v1"
new_sqlite_classes = ["GlobalRateLimiter"]

# 生产环境配置（TTS接口仅个人使用，无需开发环境）
[env.production]
name = "senseword-tts-worker"

# 生产环境Durable Objects配置
[[env.production.durable_objects.bindings]]
name = "RATE_LIMITER"
class_name = "GlobalRateLimiter"
script_name = "senseword-tts-worker"

# 生产环境队列配置
[[env.production.queues.producers]]
queue = "tts-processing-queue"
binding = "TTS_QUEUE"

[[env.production.queues.producers]]
queue = "billing-queue"
binding = "BILLING_QUEUE"

[[env.production.queues.producers]]
queue = "download-queue"
binding = "DOWNLOAD_QUEUE"

[[env.production.queues.producers]]
queue = "monitor-queue"
binding = "MONITOR_QUEUE"

[[env.production.queues.consumers]]
queue = "tts-processing-queue"
max_batch_size = 5         # 批量处理：每次处理5个批处理消息
max_batch_timeout = 28     # 28秒内组装批次，给批次组装更多时间窗口
max_retries = 3           # 失败重试3次
max_concurrency = 10      # 提升并发：10个消费者并发处理，依赖全局限流器控制速率
dead_letter_queue = "tts-dead-letter-queue"

[[env.production.queues.consumers]]
queue = "billing-queue"
max_batch_size = 100
max_batch_timeout = 30
max_retries = 5
max_concurrency = 1

[[env.production.queues.consumers]]
queue = "download-queue"
max_batch_size = 1         # 每次处理1个下载任务
max_batch_timeout = 30     # 30秒超时保护，给下载处理更多缓冲时间，留5秒缓冲
max_retries = 3           # 下载失败重试3次
max_concurrency = 100     # 高并发处理，快速消耗ready_for_download积压
dead_letter_queue = "download-dead-letter-queue"

[[env.production.queues.consumers]]
queue = "monitor-queue"
max_batch_size = 1         # 每次处理1个监控任务
max_batch_timeout = 30     # 30秒超时，支持稳定的重新推送机制
max_retries = 3           # 监控失败重试3次
max_concurrency = 20      # 降低并发避免Azure TTS 429错误
dead_letter_queue = "monitor-dead-letter-queue"

# 生产环境路由配置已移除，使用 Custom Domain 模式
# Custom Domain 在 Cloudflare 面板中配置: tts.senseword.app

# 生产环境TTS数据库绑定
[[env.production.d1_databases]]
binding = "TTS_DB"
database_name = "senseword-tts-db"
database_id = "253bb3ab-6300-4d92-b0f7-e746ef8885b3"

# 生产环境R2存储绑定
[[env.production.r2_buckets]]
binding = "AUDIO_BUCKET"
bucket_name = "senseword-audio"

# 生产环境变量配置
[env.production.vars]
# 生产环境当前使用单Key
AZURE_TTS_KEY = "3sDZH5cI7BA0a9E4ZvJYzcKRVYHOrJ2WC1BkZtoisGIvwSz72G8fJQQJ99BGACYeBjFXJ3w3AAAYACOGQESV"
# 生产环境多Key配置（建议使用，需要时启用）
# AZURE_TTS_KEYS = "key1,key2,key3"
AZURE_TTS_REGION = "eastus"
VOICE_BRE = "en-GB-MiaNeural"
VOICE_NAME = "en-US-AndrewNeural"

R2_ACCESS_KEY_ID = "99af964b1328add9393c77aea94307e7"
R2_SECRET_ACCESS_KEY = "5012ceb7e11a3c5a7a9d0feedce750652bb93675277a99d2a1f982bdaa2a9f53"

# 计费配置
API_KEY_COST_LIMIT_USD = "100"

# 自动提交配置 - 暂停模式：专注处理现有积压任务
AUTO_SUBMISSION_MAX_TASKS = "200"      # 每分钟最大提交任务数（建议从200 - 500 - 800 - 1000 - 1200 - 1500 - 2000逐步提升）
AUTO_SUBMISSION_BATCH_INTERVAL_MS = "0"  # 批处理间隔毫秒数（全局限流器进行管理，无需设置手动延时）
AUTO_SUBMISSION_BATCH_SIZE = "50"     # 每个批次的任务数量（可调节以适应不同速率需求）

# 30秒超时保护配置
MAX_BATCHES_PER_MONITOR = "20"        # 每次监控的最大批次数（网络查询+数据库更新较轻量）

# TypeScript配置
[build]
command = ""