// ================================
// KDD-039.7: TTS批处理系统 - Worker主入口
// TTS内容冲突处理和状态流转优化
// FC-03: Worker简化HTTP端点处理器
// ================================

import type {
  Env
} from './types/realtime-tts-types';

import { GlobalRateLimiter } from './durable-objects/global-rate-limiter';
import { getRateLimiterStatus, getRateLimiterStats } from './services/rate-limiter.service';

import { getTaskStatistics, queryTaskById, queryTasksByIds } from './services/unified-task.service';
import { resetApiKeyBilling, handleBillingQueue, getCurrentBillingStats } from './services/billing-tracker.service';

// KDD-039.5: 批处理队列架构导入
import {
  monitorSingleBatch,
  cleanupOrphanedBatches,
  getFailedMonitorBatches,
  getLongRunningBatches,
  pushToMonitorQueue
} from './services/batch-processing.service';
import {
  processQueueBatch,
  handleProductionSubmit,
  processDownloadQueue
} from './services/batch-processing.service';

// 自动提交服务导入
import {
  checkAndSubmitPendingTasks
} from './services/auto-submission.service';

// 工作流配置服务导入
import {
  hasAzureApiError,
  getAzureApiError,
  getWorkflowConfig,
  clearAzureApiError
} from './services/workflow-config.service';
import { ProductionSubmitRequest, DownloadQueueMessage, MonitorQueueMessage } from './types/realtime-tts-types';




// ================================
// Worker主入口 - 批处理模式
// ================================
export default {
  // HTTP请求处理
  async fetch(request: Request, env: Env, _ctx: any): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    console.log(`[TTS Worker] 收到请求: ${request.method} ${path}`);

    // CORS处理
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      });
    }

    try {
      // 路由处理 - 简化版本，只保留核心监控端点
      if (path === '/health' && request.method === 'GET') {
        return await handleHealth(env);
      } else if (path === '/dashboard' && request.method === 'GET') {
        return await handleGetDashboard(env);
      } else if (path === '/sample-completed-tasks' && request.method === 'GET') {
        return await handleSampleCompletedTasks(env, request);
      } else if (path === '/billing/reset' && (request.method === 'POST' || request.method === 'GET')) {
        return await handleResetBilling(env);
      } else if (path === '/production/submit' && request.method === 'POST') {
        return await handleProductionSubmitEndpoint(request, env);
      } else if (path === '/clear-azure-error' && (request.method === 'POST' || request.method === 'GET')) {
        return await handleClearAzureError(env);
      } else if (path === '/cleanup-orphaned-batches' && (request.method === 'POST' || request.method === 'GET')) {
        return await handleCleanupOrphanedBatches(env);
      } else if (path.startsWith('/query-task/') && request.method === 'GET') {
        // 单个TTSID查询端点
        const ttsId = path.split('/')[2];
        return await handleQueryTask(ttsId, env);
      } else if (path === '/query-tasks' && request.method === 'POST') {
        // 批量TTSID查询端点
        return await handleQueryTasks(request, env);
      } else if (path === '/retry-downloads' && (request.method === 'POST' || request.method === 'GET')) {
        // 重新推送ready_for_download批处理到下载队列
        return await handleRetryDownloads(request, env);
      } else if (path === '/reset-orphaned-tasks' && request.method === 'GET') {
        // 重置所有processing和failed任务为pending状态
        return await handleResetOrphanedTasks(request, env);
      } else if (path === '/push-running-batches' && request.method === 'GET') {
        // 手动推送所有running状态的批处理到监控队列
        return await handlePushRunningBatches(env);
      } else if (path === '/rate-limit-status' && request.method === 'GET') {
        // 查询全局限流器状态
        return await handleRateLimitStatus(env);
      } else {
        return new Response('Not Found', {
          status: 404,
          headers: {
            'Access-Control-Allow-Origin': '*'
          }
        });
      }
    } catch (error) {
      console.error('[TTS Worker] 请求处理失败:', error);
      return new Response('Internal Server Error', {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  },

  async scheduled(event: any, env: Env, ctx: any): Promise<void> {
    console.log('[Scheduled] 🕐 开始执行定时任务:', new Date().toISOString());

    try {
      // 1. 检查 Azure API 错误状态
      const hasError = await hasAzureApiError(env);
      if (hasError) {
        console.warn('[Scheduled] ⚠️ Azure API 处于错误状态，跳过自动提交但继续监控现有批处理');
      }

      // 2. 自动提交 pending 任务（如果没有错误）
      if (!hasError) {
        console.log('[Scheduled] 🚀 开始自动提交 pending 任务');
        const autoSubmitResult = await checkAndSubmitPendingTasks(env);

        if (autoSubmitResult.success && autoSubmitResult.tasksSubmitted > 0) {
          console.log(`[Scheduled] ✅ 自动提交成功: ${autoSubmitResult.tasksSubmitted} 任务, ${autoSubmitResult.batchesCreated} 批次`);
        } else if (!autoSubmitResult.success) {
          console.warn(`[Scheduled] ⚠️ 自动提交失败: ${autoSubmitResult.message}`);
        } else {
          console.log('[Scheduled] ℹ️ 没有待处理任务需要提交');
        }
      }

      // 3. 保底监控：只处理监控队列推送失败的批处理任务
      const failedMonitorBatches = await getFailedMonitorBatches(env);

      if (failedMonitorBatches.length > 0) {
        console.log(`[Scheduled] 🔧 发现 ${failedMonitorBatches.length} 个监控队列失败的批处理，启动保底监控`);

        // 使用循环调用单批处理监控，提高精确度
        let completedCount = 0;
        let failedCount = 0;
        let retryCount = 0;

        for (const batch of failedMonitorBatches) {
          try {
            const singleBatchInput = {
              batchId: batch.batchId,
              type: batch.type,
              taskCount: batch.taskCount,
              submittedAt: batch.submittedAt,
              taskMapping: batch.taskMapping
            };

            const result = await monitorSingleBatch(singleBatchInput, env);

            // 统计结果
            switch (result.action) {
              case 'completed':
                completedCount++;
                break;
              case 'failed':
                failedCount++;
                break;
              case 'retry_needed':
              case 'retry_429':
                retryCount++;
                break;
            }

            console.log(`[Scheduled] 📊 批次${batch.batchId}监控结果: ${result.action} - ${result.nextAction}`);
          } catch (error) {
            failedCount++;
            console.error(`[Scheduled] ❌ 批次${batch.batchId}监控失败:`, error);
          }
        }

        console.log('[Scheduled] 📈 保底监控汇总结果:', {
          totalBatches: failedMonitorBatches.length,
          completed: completedCount,
          failed: failedCount,
          retry: retryCount
        });
      } else {
        console.log('[Scheduled] ✅ 没有需要保底监控的批处理任务');
      }

      // 4. 处理长时间running状态的批处理任务
      const longRunningBatches = await getLongRunningBatches(env);

      if (longRunningBatches.length > 0) {
        console.log(`[Scheduled] 🕐 发现 ${longRunningBatches.length} 个长时间running的批处理，启动保底监控`);

        let longRunningCompletedCount = 0;
        let longRunningFailedCount = 0;
        let longRunningRetryCount = 0;

        for (const batch of longRunningBatches) {
          try {
            const singleBatchInput = {
              batchId: batch.batchId,
              type: batch.type,
              taskCount: batch.taskCount,
              submittedAt: batch.submittedAt,
              taskMapping: batch.taskMapping
            };

            const result = await monitorSingleBatch(singleBatchInput, env);

            // 统计结果
            switch (result.action) {
              case 'completed':
                longRunningCompletedCount++;
                break;
              case 'failed':
                longRunningFailedCount++;
                break;
              case 'retry_needed':
              case 'retry_429':
                longRunningRetryCount++;
                break;
            }

            console.log(`[Scheduled] 📊 长时间running批次${batch.batchId}监控结果: ${result.action} - ${result.nextAction}`);
          } catch (error) {
            longRunningFailedCount++;
            console.error(`[Scheduled] ❌ 长时间running批次${batch.batchId}监控失败:`, error);
          }
        }

        console.log('[Scheduled] 📈 长时间running批处理监控汇总结果:', {
          totalBatches: longRunningBatches.length,
          completed: longRunningCompletedCount,
          failed: longRunningFailedCount,
          retry: longRunningRetryCount
        });
      } else {
        console.log('[Scheduled] ✅ 没有长时间running的批处理任务');
      }

      console.log('[Scheduled] ✅ 定时任务执行完毕');
    } catch (error) {
      console.error('[Scheduled] ❌ 定时任务执行失败:', error);
    }
  },

  // FC-03: Queue Consumer (Queue Message)
    async queue(batch: any, env: Env): Promise<void> {
    console.log(`[Queue Handler] 🚀 处理队列: ${batch.queue}, 消息数量: ${batch.messages.length}`);

    switch (batch.queue) {
      case 'tts-processing-queue':
        await processQueueBatch(batch, env);
        break;
      case 'billing-queue':
        await handleBillingQueue(batch, env);
        break;
      case 'download-queue':
        await processDownloadQueue(batch, env);
        break;
      case 'monitor-queue':
        await processMonitorQueue(batch, env);
        break;
      default:
        console.warn(`[Queue Handler] ⚠️ Received message for unhandled queue '${batch.queue}', acknowledging to prevent retries.`);
        batch.ackAll();
        break;
    }
  }
};



// ================================
// FC-03: Worker简化HTTP端点处理器
// ================================



// handleGetStatus 函数已删除 - 简化监控端点

// handleGetBillingStatus 函数已删除 - 简化监控端点

/**
 * 重置API Key计费统计
 */
async function handleResetBilling(env: Env): Promise<Response> {
  try {
    const resetResult = await resetApiKeyBilling(env);

    return new Response(JSON.stringify({
      success: resetResult.success,
      message: resetResult.message,
      timestamp: new Date().toISOString()
    }), {
      status: resetResult.success ? 200 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('[Billing Reset] 重置计费统计失败:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to reset billing statistics'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 健康检查
 */
async function handleHealth(env: Env): Promise<Response> {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      mode: 'batch-tts'
    };

    return new Response(JSON.stringify(health), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}



/**
 * 处理综合状态信息查询
 * 返回任务统计、计费信息、轮询窗口状态等综合信息
 */
async function handleGetDashboard(env: Env): Promise<Response> {
  try {
    console.log('[TTS Worker] 📊 查询综合状态信息');

    // 1. 获取任务统计信息
    const taskStats = await getTaskStatistics(env);

    // 2. 获取Azure API错误状态
    const azureApiStatus = await getAzureApiError(env);

    // 3. 获取关键批处理任务统计（只查询需要关注的状态）
    let batchJobsStats = {
      submitted: 0,
      running: 0,
      ready_for_download: 0,
      total: 0
    };

    try {
      // 只查询关键状态，大幅降低数据库读取量
      const batchStatsQuery = `
        SELECT
          status,
          type,
          COUNT(*) as count
        FROM azure_batch_jobs
        WHERE status IN ('submitted', 'running', 'ready_for_download')
        GROUP BY status, type
      `;
      const batchStatsResult = await env.TTS_DB.prepare(batchStatsQuery).all();

      if (batchStatsResult.results) {
        for (const row of batchStatsResult.results) {
          const status = row.status as string;
          const count = row.count as number;

          // 按状态统计（只统计关键状态）
          if (status === 'submitted') {
            batchJobsStats.submitted += count;
          } else if (status === 'running') {
            batchJobsStats.running += count;
          } else if (status === 'ready_for_download') {
            batchJobsStats.ready_for_download += count;
          }

          batchJobsStats.total += count;
        }
      }
    } catch (error) {
      console.error('[Dashboard] 获取批处理统计失败:', error);
    }

    // 4. 获取计费信息（使用包含初始化逻辑的函数）
    let billingData = null;
    try {
      // 调用getCurrentBillingStats函数，会自动触发初始化逻辑
      const billingStats = await getCurrentBillingStats(env);

      // 计算当前API Key使用率（基于current数据）
      const apiKeyLimit = 100; // $100 limit
      const currentUsagePercent = (billingStats.current.totalCostUSD / apiKeyLimit * 100).toFixed(1);

      billingData = {
        success: true,
        billing: {
          historical: {
            totalCharacters: billingStats.historical.totalCharacters,
            totalCostUSD: billingStats.historical.totalCostUSD
          },
          current: {
            totalCharacters: billingStats.current.totalCharacters,
            totalCostUSD: billingStats.current.totalCostUSD,
            usagePercent: `${currentUsagePercent}%`
          }
        }
      };
    } catch (error) {
      console.error('[Dashboard] 获取计费信息失败:', error);
      billingData = { success: false, error: 'Database query failed' };
    }

    // 5. 获取限流器状态信息
    let rateLimiterData = null;
    try {
      const rateLimiterStatus = await getRateLimiterStatus(env);
      rateLimiterData = {
        success: true,
        rateLimiter: rateLimiterStatus
      };
    } catch (error) {
      console.error('[Dashboard] 获取限流器状态失败:', error);
      rateLimiterData = {
        success: false,
        error: 'Failed to fetch rate limiter status',
        rateLimiter: {
          usedSlots: 0,
          availableSlots: 100,
          efficiency: '0%',
          error: true
        }
      };
    }

    // 3. 简化系统健康状态检查
    const isHealthy = true; // 队列架构下简化健康检查

    // 5. 构建综合状态响应
    const dashboardData = {
      success: true,
      timestamp: new Date().toISOString(),

      // 任务统计
      tasks: {
        total: taskStats.total,
        pending: taskStats.pending,
        processing: taskStats.processing,
        completed: taskStats.completed,
        failed: taskStats.failed,
        queued: taskStats.queued,
        completionRate: taskStats.total > 0 ? ((taskStats.completed / taskStats.total) * 100).toFixed(1) + '%' : '0%'
      },

      // 计费信息
      billing: billingData?.success && billingData.billing ? billingData.billing : {
        error: billingData?.error || 'Failed to fetch billing information'
      },

      // Azure API状态
      azureApi: {
        hasError: azureApiStatus.error !== '',
        errorMessage: azureApiStatus.error,
        timestamp: azureApiStatus.timestamp,
        errorCount: azureApiStatus.errorCount,
        lastErrorType: azureApiStatus.lastErrorType
      },

      // 批处理任务统计（只显示需要关注的状态）
      batchJobs: {
        submitted: batchJobsStats.submitted,
        running: batchJobsStats.running,
        ready_for_download: batchJobsStats.ready_for_download,
        total: batchJobsStats.total
      },

      // 队列状态 (简化版本)
      queue: {
        status: 'active'
      },

      // 限流器状态
      rateLimiter: rateLimiterData?.success && rateLimiterData.rateLimiter ? {
        usedSlots: rateLimiterData.rateLimiter.usedSlots,
        availableSlots: rateLimiterData.rateLimiter.availableSlots,
        totalSlots: 100,
        usagePercent: rateLimiterData.rateLimiter.efficiency,
        pressureLevel: (() => {
          const usage = rateLimiterData.rateLimiter.usedSlots;
          if (usage >= 80) return 'high';
          if (usage >= 50) return 'medium';
          return 'low';
        })(),
        windowSizeSeconds: 10,
        status: rateLimiterData.rateLimiter.error ? 'error' : 'active',
        timestamp: rateLimiterData.rateLimiter.timestamp,
        nextResetTime: rateLimiterData.rateLimiter.nextWindowReset || (Date.now() + 10000),
        timeToReset: Math.max(0, (rateLimiterData.rateLimiter.nextWindowReset || (Date.now() + 10000)) - Date.now()),
        requestTypes: rateLimiterData.rateLimiter.requests ? {
          batch_submit: rateLimiterData.rateLimiter.requests.filter((r: any) => r.requestType === 'batch_submit').length,
          batch_monitor: rateLimiterData.rateLimiter.requests.filter((r: any) => r.requestType === 'batch_monitor').length
        } : { batch_submit: 0, batch_monitor: 0 }
      } : {
        error: rateLimiterData?.error || 'Rate limiter status unavailable',
        usedSlots: 0,
        availableSlots: 100,
        totalSlots: 100,
        usagePercent: '0%',
        pressureLevel: 'low',
        status: 'error',
        nextResetTime: Date.now() + 10000,
        timeToReset: 10000,
        requestTypes: { batch_submit: 0, batch_monitor: 0 }
      },

      // 系统状态
      system: {
        healthy: isHealthy,
        databaseConnected: isHealthy,
        workerStatus: 'running',
        lastCheck: new Date().toISOString()
      }
    };

    return new Response(JSON.stringify(dashboardData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[TTS Worker] ❌ 查询综合状态信息失败:', error);

    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch dashboard information',
      message: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

// handleDebugErrors 函数已删除 - 简化监控端点

/**
 * 处理生产环境批处理提交端点
 * 封装 /production/submit 的HTTP处理逻辑
 */
async function handleProductionSubmitEndpoint(request: Request, env: Env): Promise<Response> {
  try {
    // 解析URL参数
    const url = new URL(request.url);
    const batchSizeParam = url.searchParams.get('batch_size');
    const ttsTypeParam = url.searchParams.get('tts_type');

    // 构造请求数据
    const requestData: ProductionSubmitRequest = {
      batchSize: batchSizeParam ? parseInt(batchSizeParam, 50) : undefined,
      ttsType: ttsTypeParam as any
    };

    // 调用服务函数处理批处理提交
    const result = await handleProductionSubmit(requestData, env);

    // 构建响应
    return buildProductionSubmitResponse(result);

  } catch (error) {
    console.error('Production submit endpoint error:', error);
    return new Response(JSON.stringify({
      success: false,
      message: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 构建生产环境批处理提交响应
 * 封装响应构建逻辑，支持成功和失败场景
 */
function buildProductionSubmitResponse(result: any): Response {
  return new Response(JSON.stringify(result), {
    status: result.success ? 200 : 500,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    }
  });
}

/**
 * 清除 Azure API 错误状态
 */
async function handleClearAzureError(env: Env): Promise<Response> {
  try {
    console.log('[Clear Azure Error] 🧹 开始清除 Azure API 错误状态');

    // 清除错误状态
    await clearAzureApiError(env);

    return new Response(JSON.stringify({
      success: true,
      message: 'Azure API 错误状态已清除',
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Clear Azure Error] ❌ 清除错误状态失败:', error);

    return new Response(JSON.stringify({
      success: false,
      message: `清除错误状态失败: ${error instanceof Error ? error.message : '未知错误'}`,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 清理孤儿批处理任务
 */
async function handleCleanupOrphanedBatches(env: Env): Promise<Response> {
  try {
    console.log('[Cleanup Orphaned Batches] 🧹 开始清理孤儿批处理任务');

    // 调用清理函数
    const cleanupResult = await cleanupOrphanedBatches(env);

    const responseData = {
      success: true,
      message: `清理完成: 删除 ${cleanupResult.cleanedBatches} 个批处理，重置 ${cleanupResult.resetTasks} 个任务`,
      cleanedBatches: cleanupResult.cleanedBatches,
      resetTasks: cleanupResult.resetTasks,
      cleanedBatchIds: cleanupResult.cleanedBatchIds,
      resetTaskIds: cleanupResult.resetTaskIds,
      errors: cleanupResult.errors,
      timestamp: new Date().toISOString()
    };

    console.log('[Cleanup Orphaned Batches] ✅ 清理完成:', responseData);

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Cleanup Orphaned Batches] ❌ 清理失败:', error);

    return new Response(JSON.stringify({
      success: false,
      message: `清理孤儿批处理任务失败: ${error instanceof Error ? error.message : '未知错误'}`,
      cleanedBatches: 0,
      resetTasks: 0,
      cleanedBatchIds: [],
      resetTaskIds: [],
      errors: [error instanceof Error ? error.message : '未知错误'],
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 处理单个TTSID查询
 */
async function handleQueryTask(ttsId: string, env: Env): Promise<Response> {
  try {
    console.log(`[Query Task] 🔍 查询TTSID: ${ttsId}`);

    // 验证TTSID格式（24位字符）
    if (!ttsId || ttsId.length !== 24 || !/^[a-f0-9]{24}$/.test(ttsId)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid TTSID format. Expected 24-character hexadecimal string.'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const taskInfo = await queryTaskById(ttsId, env);

    if (!taskInfo) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Task not found'
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      data: taskInfo
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Query Task] ❌ 查询任务失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 处理批量TTSID查询
 */
async function handleQueryTasks(request: Request, env: Env): Promise<Response> {
  try {
    console.log('[Query Tasks] 🔍 批量查询TTSID');

    const body = await request.json() as { ttsIds: string[] };

    if (!body.ttsIds || !Array.isArray(body.ttsIds)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid request body. Expected { ttsIds: string[] }'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // 限制批量查询数量
    if (body.ttsIds.length > 100) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Too many IDs. Maximum 100 IDs per request.'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    // 验证所有TTSID格式
    const invalidIds = body.ttsIds.filter(id => !id || id.length !== 24 || !/^[a-f0-9]{24}$/.test(id));
    if (invalidIds.length > 0) {
      return new Response(JSON.stringify({
        success: false,
        error: `Invalid TTSID format: ${invalidIds.slice(0, 5).join(', ')}${invalidIds.length > 5 ? '...' : ''}`
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const tasksInfo = await queryTasksByIds(body.ttsIds, env);

    return new Response(JSON.stringify({
      success: true,
      data: tasksInfo,
      total: tasksInfo.length
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Query Tasks] ❌ 批量查询任务失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 重新推送ready_for_download批处理到下载队列
 */
async function handleRetryDownloads(request: Request, env: Env): Promise<Response> {
  try {
    console.log('[Retry Downloads] 🔄 开始重新推送ready_for_download批处理');

    // 查询所有ready_for_download状态的批处理
    const query = `
      SELECT batchId, downloadUrl, taskMapping
      FROM azure_batch_jobs
      WHERE status = 'ready_for_download'
      ORDER BY updatedAt ASC
    `;

    const result = await env.TTS_DB.prepare(query).all();

    if (!result.results || result.results.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        retriedBatches: 0,
        message: '没有需要重新推送的批处理',
        errors: []
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    let successCount = 0;
    const errors: string[] = [];

    // 遍历每个批处理并重新推送下载消息
    for (const batch of result.results) {
      try {
        const batchId = batch.batchId as string;
        const downloadUrl = batch.downloadUrl as string;
        const taskMapping = batch.taskMapping as string;

        // 构造下载消息（复用现有逻辑）
        const downloadMessage: DownloadQueueMessage = {
          type: 'batch_download',
          batchId: batchId,
          zipUrl: downloadUrl,
          taskMapping: taskMapping,
          timestamp: new Date().toISOString()
        };

        // 推送到下载队列
        await env.DOWNLOAD_QUEUE.send(downloadMessage);
        console.log(`[Retry Downloads] 📤 重新推送下载任务: ${batchId}`);

        successCount++;
      } catch (error) {
        const errorMsg = `批处理 ${batch.batchId} 推送失败: ${error instanceof Error ? error.message : 'Unknown error'}`;
        errors.push(errorMsg);
        console.error(`[Retry Downloads] ❌ ${errorMsg}`);
      }
    }

    console.log(`[Retry Downloads] ✅ 完成推送: ${successCount}/${result.results.length} 成功`);

    return new Response(JSON.stringify({
      success: true,
      retriedBatches: successCount,
      totalBatches: result.results.length,
      errors: errors
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Retry Downloads] ❌ 重新推送失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 重置所有processing和failed任务为pending状态
 */
async function handleResetOrphanedTasks(request: Request, env: Env): Promise<Response> {
  try {
    console.log('[Reset Orphaned Tasks] 🔄 开始重置异常状态任务');

    // 查询重置前的任务数量
    const processingCountQuery = await env.TTS_DB.prepare('SELECT COUNT(*) as count FROM tts_tasks WHERE status = ?').bind('processing').first();
    const failedCountQuery = await env.TTS_DB.prepare('SELECT COUNT(*) as count FROM tts_tasks WHERE status = ?').bind('failed').first();
    const queuedCountQuery = await env.TTS_DB.prepare('SELECT COUNT(*) as count FROM tts_tasks WHERE status = ?').bind('queued').first();

    const processingCount = (processingCountQuery?.count as number) || 0;
    const failedCount = (failedCountQuery?.count as number) || 0;
    const queuedCount = (queuedCountQuery?.count as number) || 0;

    console.log(`[Reset Orphaned Tasks] 📊 重置前统计: processing=${processingCount}, failed=${failedCount}, queued=${queuedCount}`);

    // 重置所有processing任务
    const resetProcessingResult = await env.TTS_DB.prepare(`
      UPDATE tts_tasks
      SET status = 'pending', updatedAt = datetime('now')
      WHERE status = 'processing'
    `).run();

    // 重置所有failed任务
    const resetFailedResult = await env.TTS_DB.prepare(`
      UPDATE tts_tasks
      SET status = 'pending', updatedAt = datetime('now')
      WHERE status = 'failed'
    `).run();

    // 重置所有queued任务（孤儿队列消息）
    const resetQueuedResult = await env.TTS_DB.prepare(`
      UPDATE tts_tasks
      SET status = 'pending', updatedAt = datetime('now')
      WHERE status = 'queued'
    `).run();

    const resetProcessingCount = resetProcessingResult.meta.changes || 0;
    const resetFailedCount = resetFailedResult.meta.changes || 0;
    const resetQueuedCount = resetQueuedResult.meta.changes || 0;
    const totalReset = resetProcessingCount + resetFailedCount + resetQueuedCount;

    console.log(`[Reset Orphaned Tasks] ✅ 重置完成: processing=${resetProcessingCount}, failed=${resetFailedCount}, queued=${resetQueuedCount}, total=${totalReset}`);

    return new Response(JSON.stringify({
      success: true,
      beforeReset: {
        processing: processingCount,
        failed: failedCount,
        queued: queuedCount,
        total: processingCount + failedCount + queuedCount
      },
      resetResults: {
        resetProcessing: resetProcessingCount,
        resetFailed: resetFailedCount,
        resetQueued: resetQueuedCount,
        totalReset: totalReset
      },
      message: `成功重置 ${totalReset} 个异常状态任务为pending (processing: ${resetProcessingCount}, failed: ${resetFailedCount}, queued: ${resetQueuedCount})`
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Reset Orphaned Tasks] ❌ 重置失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 处理监控队列消息
 * 职责：接收监控队列消息，对单个批处理任务进行状态监控
 */
async function processMonitorQueue(batch: any, env: Env): Promise<void> {
  console.log(`[Monitor Queue] 🔍 开始处理监控队列，消息数量: ${batch.messages.length}`);

  const startTime = Date.now();
  const maxExecutionTime = 25000; // 25秒限制，留5秒缓冲

  for (const message of batch.messages) {
    // 执行时间检查
    const currentTime = Date.now();
    if (currentTime - startTime > maxExecutionTime) {
      console.warn(`[Monitor Queue] ⏰ 执行时间超过 ${maxExecutionTime/1000}秒，主动退出避免超时`);
      break;
    }

    try {
      const monitorMessage = message.body as MonitorQueueMessage;

      if (monitorMessage.type !== 'batch_monitor') {
        console.warn('[Monitor Queue] ⚠️ 无效的消息类型:', monitorMessage.type);
        continue;
      }

      console.log(`[Monitor Queue] 🔍 监控批处理: ${monitorMessage.batchId}`);

      // 构造单个批处理监控输入
      const singleBatchInput = {
        batchId: monitorMessage.batchId,
        type: monitorMessage.batchType,
        taskCount: monitorMessage.taskCount,
        submittedAt: monitorMessage.submittedAt,
        taskMapping: monitorMessage.taskMapping
      };

      // 调用新的单批处理监控逻辑
      const result = await monitorSingleBatch(singleBatchInput, env);

      console.log(`[Monitor Queue] 📊 监控结果 ${monitorMessage.batchId}:`, {
        status: result.status,
        action: result.action,
        nextAction: result.nextAction,
        errorMessage: result.errorMessage || 'none'
      });

      // 根据action决定是否重新推送到监控队列
      if (result.action === 'retry_needed' || result.action === 'retry_429' || 
          result.action === 'retry_rate_limited' || result.action === 'retry_running') {
        try {
          // 更新时间戳，避免无限循环
          const updatedMessage = {
            ...monitorMessage,
            timestamp: new Date().toISOString()
          };

          // 对于retry_running，添加5秒延迟
          if (result.action === 'retry_running') {
            // 使用Cloudflare Queue的延迟功能实现延迟推送
            // delaySeconds最大支持43200秒(12小时)，5秒完全在支持范围内
            await env.MONITOR_QUEUE.send(updatedMessage, { delaySeconds: 5 });
            console.log(`[Monitor Queue] ⏰ 运行中状态延迟重新推送: ${monitorMessage.batchId} - 等待Azure处理完成`);
          } else {
            await env.MONITOR_QUEUE.send(updatedMessage);

            if (result.action === 'retry_429') {
              console.log(`[Monitor Queue] 🚫 429错误立即重新推送: ${monitorMessage.batchId} - 将通过队列自然间隔重新处理`);
            } else if (result.action === 'retry_rate_limited') {
              console.log(`[Monitor Queue] 🎯 限流槽位不足立即重新推送: ${monitorMessage.batchId} - 等待限流槽位释放`);
            } else {
              // retry_needed的情况
              console.log(`[Monitor Queue] 🔄 运行中状态立即重新推送: ${monitorMessage.batchId} - 等待Azure处理完成`);
            }
          }
        } catch (error) {
          console.error(`[Monitor Queue] ❌ 重新推送失败 ${monitorMessage.batchId}:`, error);
        }
      } else if (result.action === 'completed') {
        console.log(`[Monitor Queue] ✅ 批处理完成，已推送到下载队列: ${monitorMessage.batchId}`);
      } else if (result.action === 'failed') {
        console.log(`[Monitor Queue] ❌ 批处理失败，已处理失败任务: ${monitorMessage.batchId}`);
      }

    } catch (error) {
      console.error('[Monitor Queue] ❌ 处理监控消息失败:', error);
    }
  }

  console.log(`[Monitor Queue] ✅ 监控队列处理完成，耗时: ${Date.now() - startTime}ms`);
}

/**
 * 手动推送所有running状态的批处理到监控队列
 * 用于解决积压的running批处理无法进入监控队列的问题
 */
async function handlePushRunningBatches(env: Env): Promise<Response> {
  try {
    console.log('[Push Running Batches] 🚀 开始推送running状态的批处理到监控队列');

    // 查询所有running状态的批处理
    const runningBatches = await env.TTS_DB.prepare(`
      SELECT batchId, type, taskCount, submittedAt, taskMapping, updatedAt
      FROM azure_batch_jobs
      WHERE status = 'running'
      ORDER BY updatedAt ASC
    `).all();

    if (!runningBatches.results || runningBatches.results.length === 0) {
      return new Response(JSON.stringify({
        success: true,
        message: '没有找到running状态的批处理任务',
        pushedCount: 0,
        failedCount: 0
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    let pushedCount = 0;
    let failedCount = 0;
    const results = [];

    // 遍历所有running批处理，推送到监控队列
    for (const batch of runningBatches.results) {
      try {
        const success = await pushToMonitorQueue(
          batch.batchId as string,
          batch.type as any,
          batch.taskCount as number,
          batch.submittedAt as string,
          batch.taskMapping as string,
          env
        );

        if (success) {
          pushedCount++;
          results.push({
            batchId: batch.batchId,
            status: 'pushed',
            updatedAt: batch.updatedAt
          });
          console.log(`[Push Running Batches] ✅ 成功推送: ${batch.batchId}`);
        } else {
          failedCount++;
          results.push({
            batchId: batch.batchId,
            status: 'failed',
            error: 'pushToMonitorQueue returned false',
            updatedAt: batch.updatedAt
          });
          console.error(`[Push Running Batches] ❌ 推送失败: ${batch.batchId}`);
        }
      } catch (error) {
        failedCount++;
        results.push({
          batchId: batch.batchId,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: batch.updatedAt
        });
        console.error(`[Push Running Batches] ❌ 推送异常 ${batch.batchId}:`, error);
      }
    }

    console.log(`[Push Running Batches] 📊 推送完成: 成功${pushedCount}个, 失败${failedCount}个`);

    return new Response(JSON.stringify({
      success: true,
      message: `成功推送${pushedCount}个running批处理到监控队列`,
      totalBatches: runningBatches.results.length,
      pushedCount,
      failedCount,
      results
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Push Running Batches] ❌ 推送失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 查询全局限流器状态
 */
async function handleRateLimitStatus(env: Env): Promise<Response> {
  try {
    console.log('[Rate Limit Status] 📊 查询限流器状态');

    // 获取基本状态信息
    const status = await getRateLimiterStatus(env);

    // 获取详细统计信息
    const stats = await getRateLimiterStats(env);

    const response = {
      status: 'success',
      timestamp: new Date().toISOString(),
      rateLimiter: {
        basic: status,
        detailed: stats,
        config: {
          maxSlots: 100,
          windowSizeMs: 10000,
          windowSizeSeconds: 10
        }
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Rate Limit Status] ❌ 查询失败:', error);
    return new Response(JSON.stringify({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * 获取指定日期完成的TTS任务样本（用于质量抽样测试）
 * 支持URL参数：
 * - ?hour_offset=1 (查询最近1小时的数据)
 * - ?type=example_sentence (筛选特定类型)
 * - ?limit=30 (返回数量，默认10，最大100)
 */
async function handleSampleCompletedTasks(env: Env, request?: Request): Promise<Response> {
  try {
    // 解析URL参数
    const url = new URL(request?.url || 'https://example.com');
    const hourOffset = parseInt(url.searchParams.get('hour_offset') || '-1'); // 默认最近1小时
    const typeFilter = url.searchParams.get('type');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10'), 100); // 最大100个

    const dateDescription = `最近${Math.abs(hourOffset)}小时`;
    console.log(`[Sample Tasks] 🎯 获取${dateDescription}完成的TTS任务样本 (偏移: ${hourOffset}小时, 数量: ${limit})`);

    // 性能优化：预计算时间边界，避免在WHERE子句中重复计算函数
    const startTime = performance.now();
    const timeThreshold = new Date(Date.now() + hourOffset * 60 * 60 * 1000).toISOString();

    // 构建优化的查询条件，利用新创建的复合索引
    let whereClause = `
      WHERE status = 'completed'
        AND completedAt >= '${timeThreshold}'
        AND audioUrl IS NOT NULL
    `;

    const typeDescription = typeFilter ? ` (类型: ${typeFilter})` : '';

    // 优化WHERE子句顺序：将高选择性条件前置，利用复合索引的最左前缀原则
    if (typeFilter) {
      // 重新构建WHERE子句，优化索引使用
      whereClause = `
        WHERE status = 'completed'
          AND type = '${typeFilter}'
          AND completedAt >= '${timeThreshold}'
          AND audioUrl IS NOT NULL
      `;
    }

    // 使用优化的查询，移除排序以避免性能瓶颈
    const query = `
      SELECT ttsId, text, type, audioUrl, completedAt
      FROM tts_tasks
      ${whereClause}
      LIMIT ${limit}
    `;

    console.log(`[Sample Tasks] 🔍 执行优化查询: ${query.replace(/\s+/g, ' ').trim()}`);
    const result = await env.TTS_DB.prepare(query).all();
    const queryTime = performance.now() - startTime;

    console.log(`[Sample Tasks] ⚡ 查询性能: ${queryTime.toFixed(2)}ms, 返回记录: ${result.results?.length || 0}`);

    if (!result.results) {
      return new Response(JSON.stringify({
        success: true,
        samples: [],
        count: 0,
        message: `${dateDescription}暂无完成的TTS任务${typeDescription}`,
        hourOffset: hourOffset,
        typeFilter: typeFilter,
        limit: limit
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    const samples = result.results.map((row: any) => ({
      ttsId: row.ttsId,
      text: row.text,
      type: row.type,
      audioUrl: row.audioUrl,
      completedAt: row.completedAt
    }));

    console.log(`[Sample Tasks] ✅ 获取到 ${samples.length} 个${dateDescription}的样本任务${typeDescription}`);

    return new Response(JSON.stringify({
      success: true,
      samples: samples,
      count: samples.length,
      queryDate: new Date().toISOString().split('T')[0],
      hourOffset: hourOffset,
      typeFilter: typeFilter,
      limit: limit,
      dateDescription: dateDescription
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });

  } catch (error) {
    console.error('[Sample Tasks] ❌ 获取样本任务失败:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to fetch sample tasks',
      details: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

// 导出Durable Object类，使其可被Worker运行时识别
export { GlobalRateLimiter };