// ================================
// TTS批处理系统类型定义
// KDD-039.7: TTS内容冲突处理和状态流转优化
// ================================

// ================================
// 核心TTS类型
// ================================

export type TTSType = 
  | 'phonetic_name'      // 美式音标
  | 'phonetic_bre'       // 英式音标  
  | 'phonetic_ipa'       // IPA音标
  | 'example_sentence'   // 例句
  | 'phrase_breakdown';  // 短语分解

export type TaskStatus = 
  | 'pending'            // 等待处理
  | 'queued'             // 已推送到队列
  | 'processing'         // 处理中
  | 'completed'          // 已完成
  | 'failed';            // 处理失败

// ================================
// FC-02: Python脚本数据结构
// ================================

// 单词级别的TTS任务数据
export interface WordWithTasks {
  word: string;                      // 来自 words_for_publish.word
  status: string;                    // 来自 words_for_publish.ttsStatus (pending/submitted)
  tts_hash_list: string;             // 来自 words_for_publish.ttsHashList (JSON字符串)
  tts_tasks: TTSTask[];              // 来自 tts_assets 表的查询结果
}

// 单个TTS任务
export interface TTSTask {
  ttsId: string;                     // 来自 tts_assets.ttsId (24位哈希ID)
  text: string;                      // 来自 tts_assets.textToSpeak (实际TTS文本)
  type: TTSType;                     // 来自 tts_assets.ttsType
  status: TaskStatus;                // 来自 tts_assets.status (pending/failed)
}

// Python脚本最终汇总结果
export interface SubmissionResult {
  words_processed: number;           // 处理的单词数
  words_submitted: number;           // 成功提交的单词数 (failed_tasks为空)
  words_failed: number;              // 提交失败的单词数 (failed_tasks非空)
  total_tasks: number;               // 总TTS任务数
  total_inserted: number;            // 总入库任务数
  failed_tasks: string[];            // 汇总所有失败的ttsId
  submission_rate: number;           // 单词提交成功率
  task_success_rate: number;         // 任务入库成功率
  total_time: number;                // 总耗时(秒)
}

// ================================
// FC-03: Worker HTTP端点数据结构
// ================================

// 单词级别的TTS任务提交请求
export interface SubmitWordTTSRequest {
  word: string;                      // 单词名称
  tasks: Array<{
    ttsId: string;                   // 24位哈希ID
    text: string;                    // 文本内容
    type: TTSType;                   // TTS类型
  }>;
}

// API Key累计计费信息结构
export interface BillingInfo {
  totalCharacters: number;           // API Key累计总字符数
  totalCostUSD: number;             // API Key累计总成本（美元）
  lastUpdated: string;              // 最后更新时间
  charactersSinceLastBilling: number; // 自上次计费以来的字符数
}

// 单词级别的TTS任务提交响应（增强版：包含完整系统状态）
export interface SubmitWordTTSResponse {
  success: boolean;                  // 是否成功入库
  word: string;                      // 处理的单词
  received: number;                  // 接收的任务数
  inserted: number;                  // 成功入库的任务数
  skipped?: number;                  // 跳过的任务数（已完成）
  failed_tasks: string[];            // 失败的ttsId列表
  timestamp: string;                 // 处理时间戳
  billing?: BillingInfo | null;      // API Key计费信息 (保留)
  queue_info?: QueueInfo | null;     // 队列信息 (KDD-039.5: 替代轮询窗口)
  system_stats?: {                   // 高性能系统统计（只返回核心指标）
    total: number;                   // 总任务数（MAX(id)）
    completed: number;               // 已完成数（total - 非completed）
    backlog: number;                 // 积压任务数（所有非completed任务）
    completion_rate: string;         // 完成率
  } | null;
}

// ================================
// FC-04: Worker任务管理数据结构
// ================================

// 任务输入数据
export interface TTSTaskInput {
  ttsId: string;                     // 24位哈希ID
  text: string;                      // 文本内容
  type: TTSType;                     // TTS类型
}

// 任务处理结果
export interface TaskProcessingResult {
  received: number;                  // 接收任务数
  pending_tasks: TTSTaskInput[];     // 需要处理的pending任务
  skipped_completed: number;         // 跳过的已完成任务数
  skipped_processing: number;        // 跳过的处理中任务数
  processed_successfully: number;    // 成功处理数
  processing_failed: number;         // 处理失败数
  processing_results: TaskResult[];  // 详细处理结果
}

// 单个任务结果
export interface TaskResult {
  ttsId: string;
  status: 'completed' | 'failed' | 'skipped';
  audioUrl?: string;
  errorMessage?: string;
  processingTime: number;
}

// ================================
// FC-05: TTS批处理结果数据结构
// ================================

// TTS处理结果
export interface TTSProcessingResult {
  ttsId: string;                     // 任务ID
  success: boolean;                  // 处理是否成功
  status: 'completed' | 'failed';    // 最终状态
  audioUrl?: string;                 // 音频URL(成功时)
  errorMessage?: string;             // 错误信息(失败时)
  processingTime: number;            // 处理耗时(毫秒)
  azureCallTime: number;             // Azure调用耗时
  r2UploadTime: number;              // R2上传耗时
  dbUpdateTime: number;              // 数据库更新耗时
}

// ================================
// FC-06: Azure TTS调用数据结构
// ================================

// Azure TTS调用参数
export interface AzureTTSParams {
  text: string;                      // 文本内容
  type: TTSType;                     // TTS类型(决定语音选择)
  azureKey: string;                  // Azure API密钥
  azureRegion: string;               // Azure区域
}

// ================================
// FC-07: R2存储上传数据结构
// ================================

// R2上传参数
export interface R2UploadParams {
  ttsId: string;                     // 24位哈希ID作为文件名
  audioBuffer: ArrayBuffer;          // 音频数据
  r2Bucket: R2Bucket;                // R2存储桶
}

// ================================
// FC-08: 数据库状态更新数据结构
// ================================

// 数据库更新参数
export interface TaskUpdateParams {
  ttsId: string;                     // 任务ID
  status: TaskStatus;                // 新状态
  result: TaskUpdateData;            // 更新数据
  db: any;                           // D1数据库连接 (Cloudflare运行时提供)
}

// 任务更新数据
export interface TaskUpdateData {
  audioUrl?: string;                 // 音频URL(成功时)
  errorMessage?: string;             // 错误信息(失败时)
  completedAt?: string;              // 完成时间
  processingTime?: number;           // 处理耗时
}

// ================================
// 全局限流器接口
// ================================

export interface GlobalRateLimiterInterface {
  acquireSlot(workerId: string, requestType: string): Promise<boolean>;
  getCurrentStatus(): Promise<{
    usedSlots: number;
    availableSlots: number;
    windowStart: number;
    requests: Array<{timestamp: number, workerId: string, requestType: string}>;
  }>;
  getDetailedStats(): Promise<{
    totalRequests: number;
    submitRequests: number;
    monitorRequests: number;
    oldestRequest: number | null;
    newestRequest: number | null;
    efficiency: number;
  }>;
}

// ================================
// 环境变量接口
// ================================

export interface Env {
  TTS_DB: D1Database;                // TTS任务数据库
  AUDIO_BUCKET: R2Bucket;            // 音频文件存储
  TTS_QUEUE: Queue;                  // 🔑 TTS处理队列（统一队列）
  BILLING_QUEUE: Queue;              // 🧾 计费队列
  DOWNLOAD_QUEUE: Queue;             // 📥 下载处理队列
  MONITOR_QUEUE: Queue;              // 🔍 监控队列
  RATE_LIMITER: DurableObjectNamespace; // 🎯 全局限流器
  AZURE_TTS_KEY?: string;            // Azure TTS API密钥（单个，向后兼容）
  AZURE_TTS_KEYS?: string;           // Azure TTS API密钥（多个，逗号分隔）
  AZURE_TTS_REGION: string;          // Azure TTS区域
  VOICE_BRE: string;                 // 英式语音：en-GB-MiaNeural
  VOICE_NAME: string;                // 美式语音：en-US-AndrewNeural
  API_KEY_COST_LIMIT_USD: string;    // API Key成本限额（美元）

  // 自动提交配置
  AUTO_SUBMISSION_MAX_TASKS?: string;        // 每分钟最大提交任务数
  AUTO_SUBMISSION_BATCH_INTERVAL_MS?: string; // 批处理间隔毫秒数
  AUTO_SUBMISSION_BATCH_SIZE?: string;       // 每个批次的任务数量

  // 30秒超时保护配置
  MAX_BATCHES_PER_MONITOR?: string;          // 每次监控的最大批次数
}

// ================================
// 语音映射配置
// ================================

export const VOICE_MAPPING: Record<TTSType, string> = {
  'phonetic_bre': 'en-GB-MiaNeural',      // 英式发音
  'phonetic_name': 'en-US-AndrewNeural',  // 美式发音
  'phonetic_ipa': 'en-US-AndrewNeural',   // 美式发音
  'example_sentence': 'en-US-AndrewNeural', // 美式发音
  'phrase_breakdown': 'en-US-AndrewNeural'  // 美式发音
};

// ================================
// 常量配置
// ================================

// ================================
// KDD-039.5: 队列相关类型定义
// ================================

// 批量队列消息类型 (KDD-039.7: 简化批处理方案)
export interface BatchQueueMessage {
  tasks: TTSTaskInput[];  // 最多50个任务
  batchId: string;        // 批次标识
  ttsType: TTSType;       // 统一的TTS类型
  timestamp: string;      // 创建时间
}

// 005极简批处理架构 - 批处理任务消息类型（修正版）
export interface BatchTaskMessage {
  batchId: string;        // 批处理ID
  ttsType: TTSType;       // TTS类型
  tasks: Array<{          // 任务列表（最多50个）
    id: string;           // 任务ID
    text: string;         // 文本内容
    type: string;         // TTS类型
    createdAt: string;    // 创建时间
  }>;
  voiceName: string;      // 简化：只需要语音名称（如：en-US-AndrewNeural）
  timestamp: string;      // 创建时间戳
}

// 005极简批处理架构 - Azure批处理创建响应（修正版）
// 本地批处理创建响应（用于服务层）
export interface LocalBatchCreationResponse {
  success: boolean;       // 本地处理是否成功
  batchId: string;        // 批处理ID
  azureBatchId?: string;  // Azure批处理ID（成功时存在）
  taskCount: number;      // 任务数量
  status: string;         // 状态
  message: string;        // 消息
  // Azure API原始响应字段（成功时）
  id?: string;                        // Azure批处理ID
  azureStatus?: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed'; // Azure状态
  createdDateTime?: string;           // 创建时间
  lastActionDateTime?: string;        // 最后操作时间
  description?: string;               // 批处理描述
  inputKind?: 'PlainText' | 'SSML';   // 输入类型
}



// 队列消息格式 (只支持批量任务)
export type QueueMessage = BatchQueueMessage;



// 队列批次处理结果
export interface QueueBatchResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: string[];
}

// KDD-039.5: 队列信息 (替代轮询窗口信息)
export interface QueueInfo {
  queue_name: string;
  queued_tasks: number;
  estimated_processing_time: string;
}

export const TTS_CONFIG = {
  MAX_CONCURRENT_REQUESTS: 50,       // 最大并发请求数
  REQUEST_TIMEOUT: 30000,            // 请求超时时间(毫秒)
  RETRY_ATTEMPTS: 3,                 // 重试次数
  RETRY_DELAY: 1000,                 // 重试延迟(毫秒)
  AUDIO_FORMAT: 'riff-24khz-16bit-mono-pcm', // 音频格式
  CACHE_CONTROL: 'public, max-age=31536000'  // 缓存控制(1年)
} as const;

// ================================
// KDD-039.7: 批处理相关类型定义
// ================================

// 批处理状态类型
export type BatchStatus = 
  | 'submitted'          // 已提交到Azure
  | 'running'            // Azure处理中
  | 'succeeded'          // Azure处理成功
  | 'failed'             // Azure处理失败
  | 'processed';         // 本地处理完成

// FC-01: Production Submit端点输入
export interface ProductionSubmitRequest {
  batchSize?: number;               // 批次大小限制 | 来源：用户提供参数
  ttsType?: TTSType;               // TTS类型过滤 | 来源：用户提供参数
}

// FC-01: Production Submit端点输出（005架构更新）
export interface ProductionSubmitResponse {
  success: boolean;                  // 提交是否成功
  message: string;                   // 响应消息
  tasksFound: number;               // 找到的任务数
  batchesCreated: number;           // 创建的批处理数
  estimatedProcessingTime: string;  // 预估处理时间
  details?: {
    tasksByType: Array<{
      type: string;                  // TTS类型
      count: number;                 // 任务数量
    }>;
    batchDetails: Array<{
      batchId: string;               // 批处理ID
      taskCount: number;             // 任务数量
      ttsType: string;               // TTS类型
    }>;
  };
  timestamp: string;                 // 处理时间戳
}

// FC-02: 任务查询与队列推送输入
export interface TaskQueryInput {
  batchSize: number;                // 批次大小 | 来源：用户提供参数
  ttsType?: TTSType;               // TTS类型过滤 | 来源：用户提供参数
}

// FC-02: 任务查询与队列推送输出
export interface TaskSubmissionResult {
  queuedTasks: number;              // 推送到队列的任务数 | 来源：组合计算
  groupedByType: Record<TTSType, number>; // 按类型分组的任务数 | 来源：组合计算
  queueMessages: QueueMessage[];    // 队列消息列表 | 来源：组合计算
}

// FC-03: 队列消费者输入
export interface QueueConsumerInput {
  messages: QueueMessage[];          // 队列消息列表 | 来源：Cloudflare队列系统
  maxBatchSize: number;           // 最大批次大小 | 来源：系统配置
  maxConcurrency: number;          // 最大并发数 | 来源：系统配置
}

// FC-03: 验证后的任务批次
export interface ValidatedTaskBatch {
  ttsType: TTSType;                // 验证后的TTS类型 | 来源：组合计算（类型验证）
  tasks: Array<{
    ttsId: string;                   // 任务ID | 来源：队列消息
    text: string;                    // 文本内容 | 来源：队列消息
    orderIndex: number;             // 顺序索引 | 来源：组合计算（数组索引）
  }>;
  taskCount: number;               // 任务数量 | 来源：组合计算
}

// FC-04: Azure批处理创建输入
export interface BatchCreationInput {
  validatedBatch: ValidatedTaskBatch; // 验证后的任务批次 | 来源：FC-03输出
}

// FC-04: Azure批处理创建输出
export interface BatchCreationResult {
  batchId: string;                  // 批处理ID（与Azure返回的ID相同） | 来源：generateBatchId函数
  taskCount: number;                // 任务数量 | 来源：输入参数
  ttsType: TTSType;                // TTS类型 | 来源：输入参数
  submittedAt: string;              // 提交时间 | 来源：组合计算（当前时间）
  taskMapping: Record<string, string>; // 任务映射 | 来源：组合计算
}

// Azure批处理创建API响应（完整类型定义）
export interface AzureBatchCreationResponse {
  id: string;                        // Azure批处理ID | 来源：Azure API响应
  status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed'; // 批处理状态 | 来源：Azure API响应
  createdDateTime: string;           // 创建时间 | 来源：Azure API响应
  lastActionDateTime: string;        // 最后操作时间 | 来源：Azure API响应
  description?: string;              // 批处理描述 | 来源：Azure API响应
  inputKind?: string;               // 输入类型 | 来源：Azure API响应
  customProperties?: Record<string, any>; // 自定义属性 | 来源：Azure API响应
}

// FC-04: Azure批处理请求
export interface AzureBatchRequest {
  description: string;               // 批处理描述 | 来源：组合计算
  inputKind: 'PlainText';           // 输入类型（固定值） | 来源：系统配置
  inputs: Array<{
    content: string;                 // 文本内容 | 来源：任务数据
  }>;
  synthesisConfig?: {
    voice: string;                   // 语音名称 | 来源：类型映射
  };
  properties?: {
    outputFormat?: string;           // 输出格式 | 来源：系统配置
    concatenateResult?: boolean;     // 是否合并结果 | 来源：系统配置
    wordBoundaryEnabled?: boolean;   // 词边界检测 | 来源：系统配置
    sentenceBoundaryEnabled?: boolean; // 句边界检测 | 来源：系统配置
    decompressOutputFiles?: boolean; // 解压输出文件 | 来源：系统配置
  };
}

// FC-05: 批处理状态监控输入
export interface BatchMonitoringInput {
  pendingBatches: PendingBatchJob[]; // 进行中的批次 | 来源：读取数据库
}

// FC-05: 进行中的批处理任务
export interface PendingBatchJob {
  batchId: string;                   // 批次ID（与Azure批处理ID相同） | 来源：读取数据库
  type: TTSType;                    // TTS类型 | 来源：读取数据库
  taskCount: number;                // 任务数量 | 来源：读取数据库
  submittedAt: string;              // 提交时间 | 来源：读取数据库
  taskMapping: string;              // 任务映射JSON | 来源：读取数据库
}

// FC-05: 批处理状态监控输出
export interface ProcessingResult {
  checkedBatches: number;           // 检查的批次数 | 来源：组合计算
  completedBatches: number;         // 完成的批次数 | 来源：组合计算
  failedBatches: number;           // 失败的批次数 | 来源：组合计算
  processedTasks: number;          // 处理的任务数 | 来源：组合计算
  errors: string[];                 // 错误信息列表 | 来源：组合计算
}

// 单批处理监控输入
export interface SingleBatchMonitoringInput {
  batchId: string;                  // 批次ID | 来源：队列消息或数据库
  type: TTSType;                   // TTS类型 | 来源：队列消息或数据库
  taskCount: number;               // 任务数量 | 来源：队列消息或数据库
  submittedAt: string;             // 提交时间 | 来源：队列消息或数据库
  taskMapping: string;             // 任务映射JSON | 来源：队列消息或数据库
}

// 批处理监控动作类型
export type BatchMonitoringAction =
  | 'completed'                    // 批处理已完成
  | 'failed'                       // 批处理失败
  | 'retry_needed'                 // 需要重试（运行中状态，立即重试）
  | 'retry_running'                // 需要重试（运行中状态，延迟重试）
  | 'retry_429'                    // 需要重试（429错误）
  | 'retry_rate_limited';          // 需要重试（限流槽位不足）

// 单批处理监控结果
export interface SingleBatchMonitoringResult {
  batchId: string;                 // 批次ID | 来源：输入参数
  status: BatchStatus;             // 批处理状态 | 来源：Azure API响应
  action: BatchMonitoringAction;   // 建议的后续动作 | 来源：状态判断逻辑
  errorMessage?: string;           // 错误信息（如果有） | 来源：异常捕获
  nextAction?: string;             // 下一步动作描述 | 来源：业务逻辑
}

// FC-05: Azure批处理状态
export interface AzureBatchStatus {
  id: string;                        // 批处理ID | 来源：Azure API响应
  status: 'NotStarted' | 'Running' | 'Succeeded' | 'Failed'; // Azure状态 | 来源：Azure API响应
  createdDateTime: string;           // 创建时间 | 来源：Azure API响应
  lastActionDateTime: string;        // 最后操作时间 | 来源：Azure API响应
  outputs?: {
    result: string;                  // 结果下载链接 | 来源：Azure API响应
  };
  error?: {
    code: string;                    // 错误代码 | 来源：Azure API响应
    message: string;                 // 错误消息 | 来源：Azure API响应
  };
}

// FC-06: ZIP文件处理输入
export interface ZipProcessingInput {
  zipUrl: string;                    // ZIP文件下载链接 | 来源：Azure API响应
  taskMapping: Record<string, string>; // 任务ID到顺序索引的映射 | 来源：读取数据库（JSON解析）
  batchId: string;                  // 批次ID | 来源：FC-05传递
}

// FC-06: ZIP文件处理输出
export interface UploadResult {
  successfulUploads: number;        // 成功上传的文件数量 | 来源：组合计算
  failedUploads: number;           // 失败上传的文件数量 | 来源：组合计算
  uploadedFiles: Array<{
    taskId: string;                 // 任务ID | 来源：任务映射查找
    r2Key: string;                  // R2存储键名 | 来源：组合计算（路径+文件名）
    fileSize: number;               // 文件大小（字节） | 来源：音频文件属性
  }>;
  errors: Array<{
    taskId: string;                 // 失败的任务ID | 来源：任务映射查找
    error: string;                   // 错误信息 | 来源：异常捕获
  }>;
}

// FC-06: 解压后的音频文件
export interface ExtractedAudioFile {
  filename: string;                  // 文件名（如: "0001.wav"） | 来源：ZIP文件内容
  content: ArrayBuffer;              // 音频文件二进制内容 | 来源：ZIP文件解压
  orderIndex: number;               // 基于文件名解析的顺序索引 | 来源：组合计算（文件名解析）
  taskId: string;                  // 通过orderIndex映射得到的任务ID | 来源：任务映射查找
}

// FC-07: 任务状态更新输入
export interface TaskUpdateInput {
  uploadResults: UploadResult[];     // 文件上传结果列表 | 来源：FC-06输出
  batchId: string;                  // 批次ID | 来源：FC-05传递
  completedAt: string;              // 完成时间（ISO时间戳） | 来源：组合计算（当前时间）
}

// FC-07: 完成的TTS任务
export interface CompletedTTSTask {
  id: string;                       // 任务ID | 来源：读取数据库
  text: string;                     // 原始文本 | 来源：读取数据库
  ttsType: TTSType;               // TTS类型 | 来源：读取数据库
  status: 'completed';             // 完成状态（固定值） | 来源：组合计算
  audioUrl: string;               // R2存储路径 | 来源：组合计算（域名+R2键名）
  batchId: string;                // 批次ID | 来源：输入参数
  completedAt: string;            // 完成时间 | 来源：组合计算（当前时间）
}

// 队列配置
export const QUEUE_CONFIG = {
  maxBatchSize: 50,               // 最大批次大小
  maxConcurrency: 10,              // 最大并发数
  batchTimeout: 30000,             // 批次超时时间（毫秒）
} as const;

// ================================
// 工具函数
// ================================

/**
 * 生成符合Azure要求的批处理ID
 * 要求：3-64字符，字母数字加连字符下划线，首尾必须是字母或数字
 * @param prefix 前缀（默认为'batch'）
 * @returns 符合要求的批处理ID
 */
export function generateBatchId(prefix: string = 'batch'): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).slice(2, 11);
  return `${prefix}_${timestamp}_${random}`;
}

// ================================
// 计费队列相关类型定义
// ================================

// 计费队列消息类型
export interface BillingQueueMessage {
  type: 'batch_creation';           // 消息类型（固定值）
  batchId: string;                 // 批处理ID
  taskCount: number;               // 任务数量
  totalCharacters: number;         // 总字符数
  ttsType: TTSType;               // TTS类型
  timestamp: string;               // 时间戳
}

// 下载队列消息类型
export interface DownloadQueueMessage {
  type: 'batch_download';          // 消息类型（固定值）
  batchId: string;                 // 批处理ID
  zipUrl: string;                  // Azure ZIP文件下载URL
  taskMapping: string;             // JSON字符串格式的任务映射
  timestamp: string;               // 时间戳
}

// ================================
// 工作流配置相关类型定义
// ================================

// Azure API 错误监控配置
export interface AzureApiErrorConfig {
  error: string;                   // 错误信息
  timestamp: string;               // 错误发生时间
  errorCount: number;              // 错误次数统计
  lastErrorType: string;           // 最后一次错误类型 (401, 403, 429, 500等)
}

// 自动提交配置
export interface AutoSubmissionConfig {
  enabled: boolean;                // 是否启用自动提交
  maxTasksPerSubmission: number;   // 每次提交的最大任务数
  intervalMinutes: number;         // 提交间隔（分钟）
}

// 最后一次自动提交记录
export interface LastAutoSubmissionRecord {
  timestamp: string;               // 提交时间
  tasksSubmitted: number;          // 提交的任务数
  batchesCreated: number;          // 创建的批次数
  success: boolean;                // 是否成功
}

// 工作流配置统一接口
export interface WorkflowConfig {
  azureApiError: AzureApiErrorConfig;
  autoSubmissionEnabled: AutoSubmissionConfig;
  lastAutoSubmission: LastAutoSubmissionRecord;
}

// ================================
// 孤儿批处理任务清理相关类型定义
// ================================

// 孤儿批处理清理结果
export interface OrphanedBatchCleanupResult {
  cleanedBatches: number;          // 清理的批处理数量
  resetTasks: number;              // 重置的任务数量
  cleanedBatchIds: string[];       // 清理的批处理ID列表
  resetTaskIds: string[];          // 重置的任务ID列表
  errors: string[];                // 清理过程中的错误信息
}

// 监控队列消息类型
export interface MonitorQueueMessage {
  type: 'batch_monitor';           // 消息类型（固定值）
  batchId: string;                 // 批处理ID
  batchType: TTSType;             // TTS类型
  taskCount: number;               // 任务数量
  submittedAt: string;             // 提交时间
  taskMapping: string;             // JSON字符串格式的任务映射
  timestamp: string;               // 监控消息创建时间戳
}
