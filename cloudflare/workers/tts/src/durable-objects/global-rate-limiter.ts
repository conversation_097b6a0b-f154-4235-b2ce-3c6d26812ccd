/**
 * 全局限流器 Durable Object
 * 实现Azure TTS API的10秒100个请求限流控制
 * 使用纯内存滑动窗口，无需持久化存储
 */

import { DurableObject } from "cloudflare:workers";

interface RateLimitRequest {
  timestamp: number;        // 请求时间戳，用于10秒窗口计算
  workerId: string;         // Worker标识，用于调试和统计
  requestType: 'batch_submit' | 'batch_monitor'; // 请求类型，便于分类统计
}

interface RateLimitStatus {
  usedSlots: number;        // 当前已使用槽位数
  availableSlots: number;   // 剩余可用槽位数
  windowStart: number;      // 当前时间窗口开始时间
  requests: RateLimitRequest[]; // 当前窗口内的所有请求记录
}

export class GlobalRateLimiter extends DurableObject {
  private requests: RateLimitRequest[] = [];
  private readonly maxSlots = 100;      // Azure API限制：10秒内最多100个请求
  private readonly windowSize = 10000;  // 10秒时间窗口

  constructor(ctx: any, env: any) {
    super(ctx, env);
    console.log('[Rate Limiter] 🎯 全局限流器初始化完成');
  }

  /**
   * Durable Object fetch处理器
   * 处理来自Worker的HTTP请求
   */
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      if (path === '/acquire-slot' && request.method === 'POST') {
        const body = await request.json() as { workerId: string, requestType: string };
        const success = await this.acquireSlot(body.workerId, body.requestType);

        return new Response(JSON.stringify({ success }), {
          headers: { 'Content-Type': 'application/json' }
        });

      } else if (path === '/status' && request.method === 'GET') {
        const status = await this.getCurrentStatus();

        return new Response(JSON.stringify(status), {
          headers: { 'Content-Type': 'application/json' }
        });

      } else if (path === '/stats' && request.method === 'GET') {
        const stats = await this.getDetailedStats();

        return new Response(JSON.stringify(stats), {
          headers: { 'Content-Type': 'application/json' }
        });

      } else {
        return new Response('Not Found', { status: 404 });
      }

    } catch (error) {
      console.error('[Rate Limiter] ❌ fetch处理器错误:', error);
      return new Response(JSON.stringify({
        error: error instanceof Error ? error.message : 'Unknown error'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 清理过期的请求记录
   * 移除10秒前的请求，维护滑动窗口
   */
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    this.requests = this.requests.filter(req => now - req.timestamp < this.windowSize);
  }

  /**
   * 获取限流槽位
   * @param workerId Worker标识
   * @param requestType 请求类型
   * @returns 是否成功获得槽位
   */
  async acquireSlot(workerId: string, requestType: string): Promise<boolean> {
    // 参数验证
    if (!workerId || !requestType) {
      console.error('[Rate Limiter] ❌ 参数验证失败:', { workerId, requestType });
      return false;
    }

    if (!['batch_submit', 'batch_monitor'].includes(requestType)) {
      console.error('[Rate Limiter] ❌ 无效的请求类型:', requestType);
      return false;
    }

    // 清理过期请求
    this.cleanupExpiredRequests();

    // 检查槽位可用性
    if (this.requests.length >= this.maxSlots) {
      console.log(`[Rate Limiter] 🚫 槽位已满: ${this.requests.length}/${this.maxSlots}, workerId: ${workerId}`);
      return false;
    }

    // 分配槽位
    const request: RateLimitRequest = {
      timestamp: Date.now(),
      workerId,
      requestType: requestType as 'batch_submit' | 'batch_monitor'
    };

    this.requests.push(request);

    console.log(`[Rate Limiter] ✅ 槽位分配成功: ${this.requests.length}/${this.maxSlots}, workerId: ${workerId}, type: ${requestType}`);
    return true;
  }

  /**
   * 获取当前限流状态
   * @returns 限流器当前状态信息
   */
  async getCurrentStatus(): Promise<RateLimitStatus> {
    // 清理过期请求以获得准确状态
    this.cleanupExpiredRequests();

    const now = Date.now();
    const windowStart = now - this.windowSize;

    return {
      usedSlots: this.requests.length,
      availableSlots: this.maxSlots - this.requests.length,
      windowStart,
      requests: [...this.requests] // 返回副本，避免外部修改
    };
  }

  /**
   * 获取详细统计信息（用于调试和监控）
   */
  async getDetailedStats(): Promise<{
    totalRequests: number;
    submitRequests: number;
    monitorRequests: number;
    oldestRequest: number | null;
    newestRequest: number | null;
    efficiency: number;
  }> {
    this.cleanupExpiredRequests();

    const submitRequests = this.requests.filter(req => req.requestType === 'batch_submit').length;
    const monitorRequests = this.requests.filter(req => req.requestType === 'batch_monitor').length;
    
    const timestamps = this.requests.map(req => req.timestamp);
    const oldestRequest = timestamps.length > 0 ? Math.min(...timestamps) : null;
    const newestRequest = timestamps.length > 0 ? Math.max(...timestamps) : null;
    
    const efficiency = (this.requests.length / this.maxSlots) * 100;

    return {
      totalRequests: this.requests.length,
      submitRequests,
      monitorRequests,
      oldestRequest,
      newestRequest,
      efficiency: Math.round(efficiency * 100) / 100 // 保留2位小数
    };
  }
}
