/**
 * Azure API Key 计费统计服务
 * 
 * 功能：
 * 1. 统计每个API Key的字符消耗
 * 2. 计算对应的美元成本
 * 3. 提供透明的资费信息
 */

import { Env, BillingQueueMessage } from '../types/realtime-tts-types';

// Azure TTS 定价：每100万字符 $15
const AZURE_TTS_PRICE_PER_MILLION_CHARS = 15.0;

/**
 * API Key计费统计数据结构
 */
interface ApiKeyBillingStats {
  historical: {
    totalCharacters: number;
    totalCostUSD: number;
  };
  current: {
    totalCharacters: number;
    totalCostUSD: number;
  };
  lastUpdated: string;              // 最后更新时间
}

/**
 * 计算字符数对应的美元成本
 */
function calculateCost(characters: number): number {
  return (characters / 1000000) * AZURE_TTS_PRICE_PER_MILLION_CHARS;
}

/**
 * 统计已完成TTS任务的总字符数（用于首次计费基准）
 */
async function getTotalCharactersFromTasks(env: Env): Promise<number> {
  try {
    const query = `
      SELECT SUM(LENGTH(text)) as totalChars
      FROM tts_tasks
      WHERE status = 'completed'
    `;

    const result = await env.TTS_DB.prepare(query).first();
    const totalChars = result?.totalChars as number || 0;

    console.log(`[Billing Tracker] 统计已完成任务总字符数: ${totalChars}`);
    return totalChars;
  } catch (error) {
    console.error('[Billing Tracker] 统计字符数失败:', error);
    return 0;
  }
}

/**
 * 统计新提交任务的字符数
 */
function calculateSubmittedTasksCharacters(tasks: Array<{ text: string }>): number {
  return tasks.reduce((total, task) => total + task.text.length, 0);
}

/**
 * 获取当前API Key计费统计（支持historical和current分离）
 */
export async function getCurrentBillingStats(env: Env): Promise<ApiKeyBillingStats> {
  try {
    // 同时查询historical和current两个配置项
    const results = await env.TTS_DB.prepare(
      'SELECT config_key, config_value FROM workflow_config WHERE config_key IN (?, ?)'
    ).bind('azure_key_billing_historical', 'azure_key_billing_current').all();

    let historicalStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    let currentStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    if (results.results) {
      for (const row of results.results) {
        const configKey = row.config_key as string;
        const configValue = JSON.parse(row.config_value as string);

        if (configKey === 'azure_key_billing_historical') {
          historicalStats = configValue;
        } else if (configKey === 'azure_key_billing_current') {
          currentStats = configValue;
        }
      }
    }

    // 初始化逻辑：如果historical为0，汇总计算所有completed任务的text字符数量
    if (historicalStats.totalCharacters === 0) {
      console.log('[Billing Tracker] Historical为0，开始汇总计算completed任务字符数...');

      try {
        const completedTasksQuery = `
          SELECT SUM(LENGTH(text)) as totalCharacters
          FROM tts_tasks
          WHERE status = 'completed'
        `;
        const completedResult = await env.TTS_DB.prepare(completedTasksQuery).first();

        if (completedResult && completedResult.totalCharacters) {
          const calculatedCharacters = completedResult.totalCharacters as number;

          // 更新historical统计
          historicalStats = {
            totalCharacters: calculatedCharacters,
            totalCostUSD: calculateCost(calculatedCharacters),
            lastUpdated: new Date().toISOString()
          };

          // 保存到数据库
          await env.TTS_DB.prepare(`
            INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
            VALUES (?, ?, datetime('now'))
          `).bind('azure_key_billing_historical', JSON.stringify(historicalStats)).run();

          console.log(`[Billing Tracker] ✅ Historical初始化完成: ${calculatedCharacters}字符, $${historicalStats.totalCostUSD.toFixed(4)}`);
        }
      } catch (error) {
        console.error('[Billing Tracker] Historical初始化失败:', error);
      }
    }

    // 返回统计信息（只包含historical和current数据）
    return {
      historical: {
        totalCharacters: historicalStats.totalCharacters,
        totalCostUSD: historicalStats.totalCostUSD
      },
      current: {
        totalCharacters: currentStats.totalCharacters,
        totalCostUSD: currentStats.totalCostUSD
      },
      lastUpdated: currentStats.lastUpdated || historicalStats.lastUpdated
    };
  } catch (error) {
    console.error('[Billing Tracker] 获取计费统计失败:', error);
    return {
      historical: { totalCharacters: 0, totalCostUSD: 0 },
      current: { totalCharacters: 0, totalCostUSD: 0 },
      lastUpdated: ''
    };
  }
}



/**
 * 处理批处理任务的计费更新
 *
 * @param env Cloudflare环境
 * @param processedTasksCount 已处理的任务数量
 * @param processedCharacters 已处理任务的字符数（由本地计算）
 * @returns API Key累计消耗统计
 */
export async function handleBatchBilling(
  env: Env,
  processedTasksCount: number,
  processedCharacters: number
): Promise<{
  success: boolean;
  billing: {
    totalCharacters: number;
    totalCostUSD: number;
    lastUpdated: string;
    charactersSinceLastBilling: number; // 保留此字段用于兼容性，值为current.totalCharacters
  };
}> {
  try {
    console.log(`[Billing Tracker] 处理 ${processedTasksCount} 个批处理任务的计费更新，字符数: ${processedCharacters}`);

    // 1. 获取当前的historical和current数据
    const results = await env.TTS_DB.prepare(
      'SELECT config_key, config_value FROM workflow_config WHERE config_key IN (?, ?)'
    ).bind('azure_key_billing_historical', 'azure_key_billing_current').all();

    let historicalStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    let currentStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    if (results.results) {
      for (const row of results.results) {
        const configKey = row.config_key as string;
        const configValue = JSON.parse(row.config_value as string);

        if (configKey === 'azure_key_billing_historical') {
          historicalStats = configValue;
        } else if (configKey === 'azure_key_billing_current') {
          currentStats = configValue;
        }
      }
    }

    // 初始化逻辑：如果historical为0，汇总计算所有completed任务的text字符数量
    if (historicalStats.totalCharacters === 0) {
      console.log('[Billing Tracker] Historical为0，开始汇总计算completed任务字符数...');

      try {
        const completedTasksQuery = `
          SELECT SUM(LENGTH(text)) as totalCharacters
          FROM tts_tasks
          WHERE status = 'completed'
        `;
        const completedResult = await env.TTS_DB.prepare(completedTasksQuery).first();

        if (completedResult && completedResult.totalCharacters) {
          const calculatedCharacters = completedResult.totalCharacters as number;

          // 更新historical统计
          historicalStats = {
            totalCharacters: calculatedCharacters,
            totalCostUSD: calculateCost(calculatedCharacters),
            lastUpdated: new Date().toISOString()
          };

          // 保存到数据库
          await env.TTS_DB.prepare(`
            INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
            VALUES (?, ?, datetime('now'))
          `).bind('azure_key_billing_historical', JSON.stringify(historicalStats)).run();

          console.log(`[Billing Tracker] ✅ Historical初始化完成: ${calculatedCharacters}字符, $${historicalStats.totalCostUSD.toFixed(4)}`);
        }
      } catch (error) {
        console.error('[Billing Tracker] Historical初始化失败:', error);
      }
    }

    // 2. 更新historical统计（累积总数）
    const newHistoricalTotal = historicalStats.totalCharacters + processedCharacters;
    const updatedHistoricalStats = {
      totalCharacters: newHistoricalTotal,
      totalCostUSD: calculateCost(newHistoricalTotal),
      lastUpdated: new Date().toISOString()
    };

    // 3. 更新current统计（当前API Key消耗）
    const newCurrentTotal = currentStats.totalCharacters + processedCharacters;
    const updatedCurrentStats = {
      totalCharacters: newCurrentTotal,
      totalCostUSD: calculateCost(newCurrentTotal),
      lastUpdated: new Date().toISOString()
    };

    console.log(`[Billing Tracker] 增量计费完成: historical=${newHistoricalTotal}, current=${newCurrentTotal}, 新增${processedCharacters}字符`);

    // 4. 同时更新两个配置项
    await env.TTS_DB.prepare(`
      INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
      VALUES (?, ?, datetime('now'))
    `).bind('azure_key_billing_historical', JSON.stringify(updatedHistoricalStats)).run();

    await env.TTS_DB.prepare(`
      INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
      VALUES (?, ?, datetime('now'))
    `).bind('azure_key_billing_current', JSON.stringify(updatedCurrentStats)).run();

    // 5. 返回API Key累计消耗统计（用于密钥轮换决策）
    return {
      success: true,
      billing: {
        totalCharacters: updatedHistoricalStats.totalCharacters, // historical是累积总数
        totalCostUSD: updatedHistoricalStats.totalCostUSD,
        lastUpdated: updatedHistoricalStats.lastUpdated,
        charactersSinceLastBilling: updatedCurrentStats.totalCharacters // current是当前API Key消耗
      }
    };

  } catch (error) {
    console.error('[Billing Tracker] 处理计费更新失败:', error);
    return {
      success: false,
      billing: {
        totalCharacters: 0,
        totalCostUSD: 0,
        lastUpdated: '',
        charactersSinceLastBilling: 0
      }
    };
  }
}

/**
 * 处理计费队列消息
 */
export async function handleBillingQueue(batch: { messages: Array<{ body: BillingQueueMessage; ack: () => void; retry: () => void }> }, env: Env): Promise<void> {
  console.log(`[Billing Queue] 🧾 开始处理计费队列，消息数量: ${batch.messages.length}`);

  try {
    // 计算总字符数
    let totalCharacters = 0;
    const processedBatches: string[] = [];

    for (const message of batch.messages) {
      try {
        const billingData = message.body;
        
        // 验证消息类型
        if (billingData.type !== 'batch_creation') {
          console.warn(`[Billing Queue] ⚠️ 无效的消息类型: ${billingData.type}，期望: batch_creation`);
          message.ack(); // 确认无效消息以避免重复处理
          continue;
        }
        
        // 验证必需字段
        if (!billingData.batchId || !billingData.taskCount || !billingData.totalCharacters || !billingData.ttsType || !billingData.timestamp) {
          console.warn(`[Billing Queue] ⚠️ 计费消息格式不完整:`, {
            batchId: !!billingData.batchId,
            taskCount: !!billingData.taskCount,
            totalCharacters: !!billingData.totalCharacters,
            ttsType: !!billingData.ttsType,
            timestamp: !!billingData.timestamp
          });
          message.ack(); // 确认格式错误的消息
          continue;
        }
        
        // 验证数据类型
        if (typeof billingData.totalCharacters !== 'number' || billingData.totalCharacters <= 0) {
          console.warn(`[Billing Queue] ⚠️ 无效的字符数: ${billingData.totalCharacters}`);
          message.ack();
          continue;
        }
        
        // 处理有效的计费消息
        totalCharacters += billingData.totalCharacters;
        processedBatches.push(billingData.batchId);
        console.log(`[Billing Queue] 📊 批处理 ${billingData.batchId}: ${billingData.totalCharacters} 字符, ${billingData.taskCount} 个任务`);

        // 确认消息处理成功
        message.ack();
      } catch (error) {
        console.error(`[Billing Queue] ❌ 处理消息失败:`, error);
        message.retry();
      }
    }

    if (totalCharacters > 0) {
      // 使用handleBatchBilling进行批量计费更新
      const billingResult = await handleBatchBilling(env, processedBatches.length, totalCharacters);
      if (billingResult.success) {
        console.log(`[Billing Queue] ✅ 批量更新计费: +${totalCharacters} 字符, 处理批次: ${processedBatches.length}, 累计: ${billingResult.billing.totalCharacters} 字符`);
      } else {
        console.error(`[Billing Queue] ❌ 计费更新失败`);
      }
    }

  } catch (error) {
    console.error('[Billing Queue] ❌ 计费队列处理失败:', error);
    throw error;
  }
}

/**
 * 重置API Key计费统计（只重置current，保留historical）
 * 只重置当前API Key消耗统计，保留历史累积数据
 */
export async function resetApiKeyBilling(env: Env): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log('[Billing Tracker] 🔄 开始重置当前API Key消耗统计...');

    // 重置current计费统计为0
    const resetCurrentStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: new Date().toISOString()
    };

    console.log('[Billing Tracker] 🧹 将current计费统计重置为0，保留historical历史数据');

    // 只更新current配置项，保留historical不变
    await env.TTS_DB.prepare(`
      UPDATE workflow_config
      SET config_value = ?,
          updated_at = datetime('now')
      WHERE config_key = 'azure_key_billing_current'
    `).bind(JSON.stringify(resetCurrentStats)).run();

    // 获取historical数据用于返回详情
    const historicalResult = await env.TTS_DB.prepare(
      'SELECT config_value FROM workflow_config WHERE config_key = ?'
    ).bind('azure_key_billing_historical').first();

    let historicalStats = { totalCharacters: 0, totalCostUSD: 0 };
    if (historicalResult) {
      const historical = JSON.parse(historicalResult.config_value as string);
      historicalStats = {
        totalCharacters: historical.totalCharacters || 0,
        totalCostUSD: historical.totalCostUSD || 0
      };
    }

    console.log('[Billing Tracker] ✅ 当前API Key消耗统计重置完成，历史数据保留');

    return {
      success: true,
      message: '当前API Key消耗统计已重置为0，历史累积数据保留',
      details: {
        currentCharacters: 0,
        currentCostUSD: 0,
        historicalCharacters: historicalStats.totalCharacters,
        historicalCostUSD: historicalStats.totalCostUSD,
        resetTime: resetCurrentStats.lastUpdated
      }
    };
  } catch (error) {
    console.error('[Billing Tracker] ❌ 重置当前API Key消耗统计失败:', error);
    return {
      success: false,
      message: '重置当前API Key消耗统计失败: ' + (error instanceof Error ? error.message : '未知错误')
    };
  }
}

/**
 * 获取当前计费状态（返回historical、current、总计的完整信息）
 */
export async function getBillingStatus(env: Env): Promise<{
  totalCharacters: number;
  totalCostUSD: number;
  charactersSinceLastBilling: number;
  lastUpdated: string;
  pricePerMillionChars: number;
  historical: {
    totalCharacters: number;
    totalCostUSD: number;
  };
  current: {
    totalCharacters: number;
    totalCostUSD: number;
  };
}> {
  try {
    // 获取historical和current分离的数据
    const results = await env.TTS_DB.prepare(
      'SELECT config_key, config_value FROM workflow_config WHERE config_key IN (?, ?)'
    ).bind('azure_key_billing_historical', 'azure_key_billing_current').all();

    let historicalStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    let currentStats = {
      totalCharacters: 0,
      totalCostUSD: 0,
      lastUpdated: ''
    };

    if (results.results) {
      for (const row of results.results) {
        const configKey = row.config_key as string;
        const configValue = JSON.parse(row.config_value as string);

        if (configKey === 'azure_key_billing_historical') {
          historicalStats = configValue;
        } else if (configKey === 'azure_key_billing_current') {
          currentStats = configValue;
        }
      }
    }

    return {
      totalCharacters: historicalStats.totalCharacters, // historical本身就是累积总数
      totalCostUSD: historicalStats.totalCostUSD,
      charactersSinceLastBilling: currentStats.totalCharacters, // current即为"自上次重置以来"的字符数
      lastUpdated: currentStats.lastUpdated || historicalStats.lastUpdated,
      pricePerMillionChars: AZURE_TTS_PRICE_PER_MILLION_CHARS,
      historical: {
        totalCharacters: historicalStats.totalCharacters,
        totalCostUSD: historicalStats.totalCostUSD
      },
      current: {
        totalCharacters: currentStats.totalCharacters,
        totalCostUSD: currentStats.totalCostUSD
      }
    };
  } catch (error) {
    console.error('[Billing Tracker] 获取计费状态失败:', error);
    return {
      totalCharacters: 0,
      totalCostUSD: 0,
      charactersSinceLastBilling: 0,
      lastUpdated: '',
      pricePerMillionChars: AZURE_TTS_PRICE_PER_MILLION_CHARS,
      historical: { totalCharacters: 0, totalCostUSD: 0 },
      current: { totalCharacters: 0, totalCostUSD: 0 }
    };
  }
}
