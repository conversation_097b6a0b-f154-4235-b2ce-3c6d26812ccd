// ================================
// 工作流配置服务
// 管理 Azure API 错误监控和自动提交配置
// ================================

import type {
  Env,
  AzureApiErrorConfig,
  AutoSubmissionConfig,
  LastAutoSubmissionRecord,
  WorkflowConfig
} from '../types/realtime-tts-types';

/**
 * 获取 Azure API 错误状态
 */
export async function getAzureApiError(env: Env): Promise<AzureApiErrorConfig> {
  try {
    const result = await env.TTS_DB.prepare(
      'SELECT config_value FROM workflow_config WHERE config_key = ?'
    ).bind('azure_api_error').first();
    
    if (result) {
      return JSON.parse(result.config_value as string);
    }
    
    // 返回默认状态
    return {
      error: '',
      timestamp: '',
      errorCount: 0,
      lastErrorType: ''
    };
  } catch (error) {
    console.error('[Workflow Config] 获取 Azure API 错误状态失败:', error);
    return {
      error: '',
      timestamp: '',
      errorCount: 0,
      lastErrorType: ''
    };
  }
}

/**
 * 设置 Azure API 错误信息
 */
export async function setAzureApiError(
  env: Env, 
  errorMessage: string, 
  errorType: string = 'unknown'
): Promise<void> {
  try {
    // 获取当前错误配置
    const currentConfig = await getAzureApiError(env);
    
    // 更新错误信息
    const updatedConfig: AzureApiErrorConfig = {
      error: errorMessage,
      timestamp: new Date().toISOString(),
      errorCount: currentConfig.errorCount + 1,
      lastErrorType: errorType
    };
    
    await env.TTS_DB.prepare(`
      INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
      VALUES (?, ?, datetime('now'))
    `).bind('azure_api_error', JSON.stringify(updatedConfig)).run();
    
    console.log(`[Workflow Config] 🚨 记录 Azure API 错误: ${errorType} - ${errorMessage}`);
  } catch (error) {
    console.error('[Workflow Config] 设置 Azure API 错误失败:', error);
  }
}

/**
 * 清空 Azure API 错误状态
 */
export async function clearAzureApiError(env: Env): Promise<void> {
  try {
    const clearedConfig: AzureApiErrorConfig = {
      error: '',
      timestamp: '',
      errorCount: 0,
      lastErrorType: ''
    };
    
    await env.TTS_DB.prepare(`
      INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
      VALUES (?, ?, datetime('now'))
    `).bind('azure_api_error', JSON.stringify(clearedConfig)).run();
    
    console.log('[Workflow Config] ✅ 清除 Azure API 错误状态');
  } catch (error) {
    console.error('[Workflow Config] 清除 Azure API 错误失败:', error);
  }
}

/**
 * 检查是否启用自动提交
 */
export async function isAutoSubmissionEnabled(env: Env): Promise<boolean> {
  try {
    const result = await env.TTS_DB.prepare(
      'SELECT config_value FROM workflow_config WHERE config_key = ?'
    ).bind('auto_submission_enabled').first();
    
    if (result) {
      const config: AutoSubmissionConfig = JSON.parse(result.config_value as string);
      return config.enabled;
    }
    
    return true; // 默认启用
  } catch (error) {
    console.error('[Workflow Config] 检查自动提交状态失败:', error);
    return false;
  }
}

/**
 * 获取自动提交配置
 */
export async function getAutoSubmissionConfig(env: Env): Promise<AutoSubmissionConfig> {
  try {
    const result = await env.TTS_DB.prepare(
      'SELECT config_value FROM workflow_config WHERE config_key = ?'
    ).bind('auto_submission_enabled').first();
    
    if (result) {
      return JSON.parse(result.config_value as string);
    }
    
    // 返回默认配置
    return {
      enabled: true,
      maxTasksPerSubmission: 200,
      intervalMinutes: 1
    };
  } catch (error) {
    console.error('[Workflow Config] 获取自动提交配置失败:', error);
    return {
      enabled: false,
      maxTasksPerSubmission: 200,
      intervalMinutes: 1
    };
  }
}

/**
 * 更新最后一次自动提交记录（支持累积计数）
 */
export async function updateLastAutoSubmission(
  env: Env,
  tasksSubmitted: number,
  batchesCreated: number,
  success: boolean
): Promise<void> {
  try {
    // 1.1 读取现有数据逻辑
    const existingRecord = await getLastAutoSubmissionRecord(env);

    // 1.2 日期检查逻辑 - 判断是否为同一天
    const currentDate = new Date().toISOString().split('T')[0];
    const existingDate = existingRecord.timestamp ? existingRecord.timestamp.split('T')[0] : '';
    const isSameDay = existingDate === currentDate;

    // 1.3 累积计算逻辑 - 同一天累加，跨天重置
    const record: LastAutoSubmissionRecord = {
      timestamp: new Date().toISOString(),
      tasksSubmitted: isSameDay ? existingRecord.tasksSubmitted + tasksSubmitted : tasksSubmitted,
      batchesCreated: isSameDay ? existingRecord.batchesCreated + batchesCreated : batchesCreated,
      success
    };

    await env.TTS_DB.prepare(`
      INSERT OR REPLACE INTO workflow_config (config_key, config_value, updated_at)
      VALUES (?, ?, datetime('now'))
    `).bind('last_auto_submission', JSON.stringify(record)).run();

    const actionType = isSameDay ? '累积' : '重置';
    console.log(`[Workflow Config] 📝 ${actionType}自动提交记录: 当次${tasksSubmitted}任务, 当日总计${record.tasksSubmitted}任务, 当次${batchesCreated}批次, 当日总计${record.batchesCreated}批次, 成功: ${success}`);
  } catch (error) {
    console.error('[Workflow Config] 更新自动提交记录失败:', error);
  }
}

/**
 * 检查 Azure API 是否处于错误状态
 */
export async function hasAzureApiError(env: Env): Promise<boolean> {
  const errorConfig = await getAzureApiError(env);
  return errorConfig.error !== '';
}

/**
 * 获取完整的工作流配置
 */
export async function getWorkflowConfig(env: Env): Promise<WorkflowConfig> {
  const [azureApiError, autoSubmissionEnabled, lastAutoSubmission] = await Promise.all([
    getAzureApiError(env),
    getAutoSubmissionConfig(env),
    getLastAutoSubmissionRecord(env)
  ]);
  
  return {
    azureApiError,
    autoSubmissionEnabled,
    lastAutoSubmission
  };
}

/**
 * 获取最后一次自动提交记录
 */
async function getLastAutoSubmissionRecord(env: Env): Promise<LastAutoSubmissionRecord> {
  try {
    const result = await env.TTS_DB.prepare(
      'SELECT config_value FROM workflow_config WHERE config_key = ?'
    ).bind('last_auto_submission').first();
    
    if (result) {
      return JSON.parse(result.config_value as string);
    }
    
    return {
      timestamp: '',
      tasksSubmitted: 0,
      batchesCreated: 0,
      success: true
    };
  } catch (error) {
    console.error('[Workflow Config] 获取自动提交记录失败:', error);
    return {
      timestamp: '',
      tasksSubmitted: 0,
      batchesCreated: 0,
      success: false
    };
  }
}
