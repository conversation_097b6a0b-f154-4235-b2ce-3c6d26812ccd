/**
 * 限流服务接口层
 * 封装全局限流器的调用接口，提供简化的API
 */

import type { Env } from '../types/realtime-tts-types';

/**
 * 获取全局限流器的Durable Object实例
 * @param env Worker环境变量
 * @returns 限流器DO实例
 */
function getRateLimiterStub(env: Env) {
  // 使用固定的ID确保所有Worker实例访问同一个限流器
  const id = env.RATE_LIMITER.idFromName('global');
  return env.RATE_LIMITER.get(id) as any; // 类型断言，避免TypeScript类型检查问题
}

/**
 * 获取Azure API请求槽位
 * @param env Worker环境变量
 * @param workerId Worker标识
 * @param requestType 请求类型
 * @returns 是否成功获得槽位
 */
export async function acquireAzureAPISlot(
  env: Env,
  workerId: string,
  requestType: string
): Promise<boolean> {
  try {
    const stub = getRateLimiterStub(env);

    // 使用fetch方式调用DO方法
    const response = await stub.fetch('http://localhost/acquire-slot', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ workerId, requestType })
    });

    if (!response.ok) {
      throw new Error(`DO调用失败: ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      console.log(`[Rate Limiter Service] ✅ 槽位获取成功: ${workerId}, type: ${requestType}`);
    } else {
      console.log(`[Rate Limiter Service] 🚫 槽位获取失败: ${workerId}, type: ${requestType}`);
    }

    return result.success;
  } catch (error) {
    // 限流器异常时，记录错误但不阻断业务（降级策略）
    console.error('[Rate Limiter Service] 🚨 限流器调用失败，允许请求通过:', error);
    return true; // 降级策略：允许请求通过
  }
}

/**
 * 等待可用槽位
 * 当无可用槽位时，等待下个时间窗口或可用槽位
 * @param env Worker环境变量
 * @param workerId Worker标识
 * @param requestType 请求类型
 * @param maxWaitMs 最大等待时间（毫秒）
 * @returns 是否在超时前获得槽位
 */
export async function waitForAvailableSlot(
  env: Env,
  workerId: string,
  requestType: string,
  maxWaitMs: number = 5000
): Promise<boolean> {
  const startTime = Date.now();
  const checkInterval = 100; // 每100ms检查一次
  
  console.log(`[Rate Limiter Service] ⏳ 开始等待槽位: ${workerId}, 最大等待时间: ${maxWaitMs}ms`);
  
  while (Date.now() - startTime < maxWaitMs) {
    try {
      const hasSlot = await acquireAzureAPISlot(env, workerId, requestType);
      
      if (hasSlot) {
        const waitTime = Date.now() - startTime;
        console.log(`[Rate Limiter Service] ✅ 等待${waitTime}ms后获得槽位: ${workerId}`);
        return true;
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      
    } catch (error) {
      console.error(`[Rate Limiter Service] ❌ 等待槽位时发生错误: ${workerId}`, error);
      // 发生错误时也继续等待，直到超时
    }
  }
  
  const totalWaitTime = Date.now() - startTime;
  console.log(`[Rate Limiter Service] ⏰ 等待槽位超时: ${workerId}, 等待时间: ${totalWaitTime}ms`);
  return false;
}

/**
 * 获取限流器当前状态
 * @param env Worker环境变量
 * @returns 限流器状态信息
 */
export async function getRateLimiterStatus(env: Env): Promise<any> {
  try {
    const stub = getRateLimiterStub(env);

    const response = await stub.fetch('http://localhost/status', {
      method: 'GET'
    });

    if (!response.ok) {
      throw new Error(`DO状态查询失败: ${response.status}`);
    }

    const status = await response.json();

    return {
      ...status,
      efficiency: ((status.usedSlots / 100) * 100).toFixed(2) + '%',
      nextWindowReset: status.windowStart + 10000,
      timestamp: Date.now()
    };
  } catch (error) {
    console.error('[Rate Limiter Service] ❌ 获取限流器状态失败:', error);
    return {
      error: 'Failed to get rate limiter status',
      timestamp: Date.now()
    };
  }
}

/**
 * 获取限流器详细统计信息
 * @param env Worker环境变量
 * @returns 详细统计信息
 */
export async function getRateLimiterStats(env: Env): Promise<any> {
  try {
    const stub = getRateLimiterStub(env);

    const response = await stub.fetch('http://localhost/stats', {
      method: 'GET'
    });

    if (!response.ok) {
      throw new Error(`DO统计查询失败: ${response.status}`);
    }

    const stats = await response.json();

    return {
      ...stats,
      timestamp: Date.now(),
      windowSizeMs: 10000,
      maxSlots: 100
    };
  } catch (error) {
    console.error('[Rate Limiter Service] ❌ 获取限流器统计失败:', error);
    return {
      error: 'Failed to get rate limiter stats',
      timestamp: Date.now()
    };
  }
}
