/**
 * KDD-039.7: TTS批处理服务
 * 实现Azure TTS批处理API的完整工作流程
 * 
 * 核心功能：
 * - FC-01: Production Submit端点处理
 * - FC-02: 任务查询与队列推送
 * - FC-03: 队列消费者处理
 * - FC-04: Azure批处理创建
 * - FC-05: 批处理状态监控
 * - FC-06: ZIP文件处理与音频上传
 * - FC-07: 任务状态更新与计费统计
 */

import JSZip from 'jszip';
import {
  Env,
  TTSType,
  TaskStatus,
  BatchStatus,
  ProductionSubmitRequest,
  ProductionSubmitResponse,
  TaskQueryInput,
  TaskSubmissionResult,
  ValidatedTaskBatch,
  BatchCreationInput,
  BatchCreationResult,
  AzureBatchRequest,
  BatchMonitoringInput,
  ProcessingResult,
  AzureBatchStatus,
  AzureBatchCreationResponse,
  ZipProcessingInput,
  UploadResult,
  TaskUpdateInput,
  PendingBatchJob,
  ExtractedAudioFile,
  CompletedTTSTask,
  QueueMessage,
  BatchQueueMessage,
  BillingQueueMessage,
  DownloadQueueMessage,
  TTSTaskInput,
  OrphanedBatchCleanupResult,
  MonitorQueueMessage,
  SingleBatchMonitoringInput,
  SingleBatchMonitoringResult,
  BatchMonitoringAction,
  VOICE_MAPPING,
  TTS_CONFIG,
  QUEUE_CONFIG,
  generateBatchId
} from '../types/realtime-tts-types';

import { acquireAzureAPISlot, waitForAvailableSlot } from './rate-limiter.service';

import {
  hasAzureApiError,
  setAzureApiError
} from './workflow-config.service';

/**
 * FC-01: Production Submit端点处理
 * 职责：接收HTTP请求，查询pending任务，推送到队列
 */
export async function handleProductionSubmit(
  request: ProductionSubmitRequest,
  env: Env
): Promise<ProductionSubmitResponse> {
  try {
    const batchSize = request.batchSize || QUEUE_CONFIG.maxBatchSize;
    const ttsType = request.ttsType;
    
    // 调用FC-02进行任务查询与队列推送
    const submissionResult = await queryAndSubmitTasks({
      batchSize,
      ttsType
    }, env);
    
    return {
      success: true,
      message: `Successfully queued ${submissionResult.queuedTasks} tasks for batch processing`,
      tasksFound: submissionResult.queuedTasks,
      batchesCreated: submissionResult.queueMessages.length,
      estimatedProcessingTime: '5-10 minutes',
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('Production submit failed:', error);
    return {
      success: false,
      message: `Failed to submit tasks: ${error instanceof Error ? error.message : 'Unknown error'}`,
      tasksFound: 0,
      batchesCreated: 0,
      estimatedProcessingTime: '0 minutes',
      timestamp: new Date().toISOString()
    };
  }
}



/**
 * FC-02: 任务查询与队列推送（KDD-039.7: 简化批处理方案）
 * 职责：查询pending任务，按类型分组，打包成批量消息推送到队列
 */
export async function queryAndSubmitTasks(
  input: TaskQueryInput,
  env: Env
): Promise<TaskSubmissionResult> {
  // 构建查询SQL
  let query = `
    SELECT ttsId, text, type 
    FROM tts_tasks 
    WHERE status = 'pending'
  `;
  
  const params: any[] = [];
  
  if (input.ttsType) {
    query += ` AND type = ?`;
    params.push(input.ttsType);
  }
  
  query += ` ORDER BY createdAt ASC LIMIT ?`;
  params.push(input.batchSize);
  
  // 查询pending任务
  const result = await env.TTS_DB.prepare(query).bind(...params).all();
  const tasks = result.results as Array<{
    ttsId: string;
    text: string;
    type: TTSType;
  }>;
  
  if (tasks.length === 0) {
    return {
      queuedTasks: 0,
      groupedByType: {} as Record<TTSType, number>,
      queueMessages: []
    };
  }
  
  // 按类型分组任务
  const tasksByType = new Map<TTSType, TTSTaskInput[]>();
  
  for (const task of tasks) {
    if (!tasksByType.has(task.type)) {
      tasksByType.set(task.type, []);
    }
    tasksByType.get(task.type)!.push({
      ttsId: task.ttsId,
      text: task.text,
      type: task.type as TTSType
    });
  }
  
  // 创建批量队列消息
  const queueMessages: QueueMessage[] = [];
  const groupedByType: Record<TTSType, number> = {} as Record<TTSType, number>;
  
  for (const [ttsType, typeTasks] of tasksByType) {
    // 按每50个任务分批
    const batchSize = 50;
    for (let i = 0; i < typeTasks.length; i += batchSize) {
      const batchTasks = typeTasks.slice(i, i + batchSize);
      
      const batchMessage: BatchQueueMessage = {
        tasks: batchTasks,
        batchId: generateBatchId('queue'),
        ttsType: ttsType,
        timestamp: new Date().toISOString()
      };
      
      queueMessages.push(batchMessage);
    }
    
    groupedByType[ttsType] = typeTasks.length;
  }
  
  // 更新任务状态为queued
  const taskIds = tasks.map(t => t.ttsId);
  const placeholders = taskIds.map(() => '?').join(',');
  await env.TTS_DB.prepare(`
    UPDATE tts_tasks 
    SET status = 'queued', updatedAt = datetime('now') 
    WHERE ttsId IN (${placeholders})
  `).bind(...taskIds).run();
  
  // 推送批量消息到队列
  for (const message of queueMessages) {
    await env.TTS_QUEUE.send(message);
  }
  
  return {
    queuedTasks: tasks.length,
    groupedByType: groupedByType,
    queueMessages: queueMessages
  };
}

/**
 * FC-03: 队列消费者处理（KDD-039.7: 简化批处理方案）
 * 职责：解包批量消息，验证任务，创建Azure批处理
 * 注意：只处理BatchQueueMessage类型的消息，已移除向后兼容的单个任务处理
 * 优化：移除重复的类型分组逻辑，直接处理每个BatchQueueMessage
 */
export async function processQueueBatch(
  batch: any,
  env: Env
): Promise<void> {
  // Azure API 错误预检查
  const hasError = await hasAzureApiError(env);
  if (hasError) {
    console.warn('[Queue Batch] ⚠️ Azure API 处于错误状态，确认所有消息以避免无限重试');
    batch.ackAll(); // 确认所有消息，避免队列消息无限循环
    return;
  }

  // 直接处理每个BatchQueueMessage，无需重新分组
  let batchIndex = 0;

  for (const message of batch.messages) {
    // 处理批量消息
    const batchMessage = message.body as BatchQueueMessage;

    // 防御性检查：确保消息体和任务数组存在
    if (!batchMessage || !batchMessage.tasks) {
      console.warn('Invalid batch message: missing body or tasks array', message);
      continue;
    }

    console.log(`Processing batch message: ${batchMessage.batchId} with ${batchMessage.tasks.length} tasks`);

    // 验证任务并创建ValidatedTaskBatch
    const validTasks: Array<{
      ttsId: string;
      text: string;
      orderIndex: number;
    }> = [];

    for (const task of batchMessage.tasks) {
      const { ttsId, text, type } = task;

      // 验证必要字段
      if (!ttsId || !text || !type) {
        console.warn('Invalid task in batch message:', task);
        continue;
      }

      // 验证类型一致性（防御性检查）
      if (type !== batchMessage.ttsType) {
        console.warn(`Task type mismatch: expected ${batchMessage.ttsType}, got ${type}`);
        continue;
      }

      validTasks.push({
        ttsId,
        text,
        orderIndex: validTasks.length // 插入前的数组长度 = 当前任务在批次中的索引位置 (0, 1, 2, ...)
      });
    }

    // 如果没有有效任务，跳过此批次
    if (validTasks.length === 0) {
      console.warn(`No valid tasks in batch message: ${batchMessage.batchId}`);
      continue;
    }

    // 创建ValidatedTaskBatch
    const validatedBatch: ValidatedTaskBatch = {
      ttsType: batchMessage.ttsType,
      tasks: validTasks,
      taskCount: validTasks.length
    };

    try {
      // 直接创建Azure批处理，依赖全局限流器进行速率控制
      await createAzureBatch({ validatedBatch: validatedBatch }, env);
      console.log(`✅ Successfully created Azure batch for ${batchMessage.ttsType} with ${validatedBatch.taskCount} tasks`);
      batchIndex++;
    } catch (error) {
      console.error(`❌ Failed to create Azure batch for ${batchMessage.ttsType}:`, error);
      // 将任务状态重置为pending以便重试
      const taskIds = validatedBatch.tasks.map(t => t.ttsId);
      const placeholders = taskIds.map(() => '?').join(',');
      await env.TTS_DB.prepare(`
          UPDATE tts_tasks
          SET status = 'pending', updatedAt = datetime('now')
          WHERE ttsId IN (${placeholders})
        `).bind(...taskIds).run();
    }
  }
}

/**
 * FC-04: Azure批处理创建
 * 职责：调用Azure批处理API，保存批处理记录
 */
export async function createAzureBatch(
  input: BatchCreationInput,
  env: Env
): Promise<BatchCreationResult> {
  const { validatedBatch } = input;
  const { ttsType, tasks } = validatedBatch;

  // Azure API 错误预检查
  const hasError = await hasAzureApiError(env);
  if (hasError) {
    throw new Error('Azure API 处于错误状态，停止批处理创建。请检查 workflow_config 表中的 azure_api_error 字段。');
  }

  // 生成批次ID
  const batchId = generateBatchId();
  
  // 构建Azure批处理请求
  const azureRequest: AzureBatchRequest = {
    description: `Batch processing for ${tasks.length} ${ttsType} tasks`,
    inputKind: 'PlainText',
    inputs: tasks.map(task => ({ content: task.text })),
    synthesisConfig: {
      voice: VOICE_MAPPING[ttsType]
    },
    properties: {
      outputFormat: TTS_CONFIG.AUDIO_FORMAT,
      concatenateResult: false,
      wordBoundaryEnabled: false,
      sentenceBoundaryEnabled: false,
      decompressOutputFiles: false
    }
  };

  // 🎯 限流检查 - 在批处理提交前先获取槽位
  const hasSlot = await waitForAvailableSlot(env, `submit-${Date.now()}`, 'batch_submit', 10000);
  if (!hasSlot) {
    throw new Error('Rate limit exceeded: unable to acquire slot within 10 seconds');
  }

  console.log(`[Submit] 🎯 限流控制: 获得槽位，开始提交批处理 ${batchId}`);

  // 调用Azure批处理API
  const response = await fetch(
    `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`,
    {
      method: 'PUT',
      headers: {
        'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY || '',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(azureRequest)
    }
  );
  
  if (!response.ok) {
    const errorText = await response.text();
    const errorType = response.status.toString();
    const errorMessage = `Azure batch creation failed: ${response.status} ${errorText}`;

    // 记录 Azure API 错误到 workflow_config
    await setAzureApiError(env, errorMessage, errorType);

    throw new Error(errorMessage);
  }
  // 创建任务映射（orderIndex -> taskId）- 使用规范化数字键
  const taskMapping: Record<string, string> = {};
  tasks.forEach(task => {
    // 规范化处理：使用十进制数字作为键，确保与任何前导0格式兼容
    const normalizedKey = task.orderIndex.toString();
    taskMapping[normalizedKey] = task.ttsId;
  });
  
  const submittedAt = new Date().toISOString();
  
  // 保存批处理记录到数据库
  await env.TTS_DB.prepare(`
    INSERT INTO azure_batch_jobs (
      batchId, type, status, taskCount, 
      submittedAt, taskMapping
    ) VALUES (?, ?, ?, ?, ?, ?)
  `).bind(
    batchId,
    ttsType,
    'submitted',
    tasks.length,
    submittedAt,
    JSON.stringify(taskMapping)
  ).run();
  
  // 更新任务状态为processing
  const taskIds = tasks.map(t => t.ttsId);
  const placeholders = taskIds.map(() => '?').join(',');
  await env.TTS_DB.prepare(`
    UPDATE tts_tasks
    SET status = 'processing', updatedAt = datetime('now')
    WHERE ttsId IN (${placeholders})
  `).bind(...taskIds).run();
  
  // 计算总字符数并推送到计费队列
  const totalCharacters = tasks.reduce((sum, task) => sum + task.text.length, 0);
  
  // 向计费队列发送计费消息
  const billingMessage: BillingQueueMessage = {
    type: 'batch_creation',
    batchId: batchId,
    taskCount: tasks.length,
    totalCharacters: totalCharacters,
    ttsType: ttsType,
    timestamp: submittedAt
  };
  await env.BILLING_QUEUE.send(billingMessage);

  console.log(`💰 计费信息已推送到队列: 批次${batchId}, ${tasks.length}个任务, 总字符数${totalCharacters}`);

  // 推送到监控队列，实现异步并发监控
  const monitorQueueSuccess = await pushToMonitorQueue(
    batchId,
    ttsType,
    tasks.length,
    submittedAt,
    JSON.stringify(taskMapping),
    env
  );

  // 如果监控队列推送失败，标记批处理状态为特殊状态
  if (!monitorQueueSuccess) {
    await env.TTS_DB.prepare(`
      UPDATE azure_batch_jobs
      SET status = 'monitor_queue_failed', updatedAt = datetime('now')
      WHERE batchId = ?
    `).bind(batchId).run();

    console.warn(`[Batch Creation] ⚠️ 批处理 ${batchId} 监控队列推送失败，已标记为 monitor_queue_failed 状态`);
  }

  return {
    batchId: batchId,
    taskCount: tasks.length,
    ttsType,
    submittedAt: submittedAt,
    taskMapping: taskMapping
  };
}

/**
 * FC-05: 批处理状态监控
 * 职责：查询Azure批处理状态，处理完成的批次
 * @deprecated 推荐使用 monitorSingleBatch 函数处理单个批处理任务
 */
export async function monitorBatchStatus(
  input: BatchMonitoringInput,
  env: Env
): Promise<ProcessingResult> {
  const result: ProcessingResult = {
    checkedBatches: 0,
    completedBatches: 0,
    failedBatches: 0,
    processedTasks: 0,
    errors: []
  };

  // 30秒超时保护：限制处理批次数量，避免 Worker 超时
  const maxBatchesPerExecution = parseInt(env.MAX_BATCHES_PER_MONITOR || '5');
  const batchesToProcess = input.pendingBatches.slice(0, maxBatchesPerExecution);

  if (input.pendingBatches.length > maxBatchesPerExecution) {
    console.log(`[Monitor] ⚠️ 限制处理批次数量: ${batchesToProcess.length}/${input.pendingBatches.length} (防止30秒超时)`);
  }

  // 执行时间监控
  const startTime = Date.now();
  const maxExecutionTime = 25000; // 25秒限制，留5秒缓冲

  for (const batch of batchesToProcess) {
    // 执行时间检查：超过25秒时主动退出
    const currentTime = Date.now();
    if (currentTime - startTime > maxExecutionTime) {
      console.warn(`[Monitor] ⏰ 执行时间超过 ${maxExecutionTime/1000}秒，主动退出避免超时`);
      result.errors.push(`执行时间超限，已处理 ${result.checkedBatches} 个批次`);
      break;
    }

    result.checkedBatches++;

    try {
      // 查询Azure批处理状态
      const statusResponse = await fetch(
        `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batch.batchId}?api-version=2024-04-01`,
        {
          headers: {
            'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY || ''
          }
        }
      );
      
      if (!statusResponse.ok) {
        throw new Error(`Status check failed: ${statusResponse.status}`);
      }
      
      const azureStatus = await statusResponse.json() as AzureBatchStatus;
      
      // 更新数据库中的批处理状态
      let newStatus: BatchStatus;
      switch (azureStatus.status) {
        case 'NotStarted':
        case 'Running':
          newStatus = 'running';
          break;
        case 'Succeeded':
          newStatus = 'succeeded';
          break;
        case 'Failed':
          newStatus = 'failed';
          break;
        default:
          newStatus = 'running';
      }
      
      await env.TTS_DB.prepare(`
        UPDATE azure_batch_jobs 
        SET status = ?, updatedAt = datetime('now') 
        WHERE batchId = ?
      `).bind(newStatus, batch.batchId).run();
      
      // 处理完成的批次 - 推送到下载队列，不重新推送到监控队列
      if (azureStatus.status === 'Succeeded' && azureStatus.outputs?.result) {
        // 更新状态为 ready_for_download 并记录 ZIP URL
        await env.TTS_DB.prepare(`
          UPDATE azure_batch_jobs
          SET status = 'ready_for_download', downloadUrl = ?, updatedAt = datetime('now')
          WHERE batchId = ?
        `).bind(azureStatus.outputs.result, batch.batchId).run();

        // 推送下载消息到专用队列
        const downloadMessage: DownloadQueueMessage = {
          type: 'batch_download',
          batchId: batch.batchId,
          zipUrl: azureStatus.outputs.result,
          taskMapping: batch.taskMapping,
          timestamp: new Date().toISOString()
        };

        await env.DOWNLOAD_QUEUE.send(downloadMessage);
        console.log(`[Monitor] 📤 推送下载任务到队列: ${batch.batchId}`);

        result.completedBatches++;
        result.processedTasks += batch.taskCount;
      } else if (azureStatus.status === 'Failed') {
        // 处理失败的批次
        await handleFailedBatch(batch.batchId, azureStatus.error?.message || 'Unknown error', batch.taskMapping, env);
        result.failedBatches++;
      }
      
    } catch (error) {
      const errorMsg = `Batch ${batch.batchId} monitoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMsg);
      console.error(errorMsg);

      // 如果是 Azure API 相关错误，记录到 workflow_config
      // 429错误不记录到workflow_config（不是账号级别错误）
      if (error instanceof Error && (
        error.message.includes('401') ||
        error.message.includes('403') ||
        error.message.includes('Status check failed')
      )) {
        const errorType = error.message.includes('401') ? '401' :
                         error.message.includes('403') ? '403' : 'api_error';
        await setAzureApiError(env, `监控批处理状态失败: ${error.message}`, errorType);
      } else if (error instanceof Error && error.message.includes('429')) {
        // 429错误专用处理：不写入workflow_config，只记录日志
        console.log(`[Monitor] 🚫 批次${batch.batchId}遇到429限流，将通过队列延迟重试`);
      }
    }
  }
  
  return result;
}

/**
 * 单批处理状态监控
 * 职责：查询单个Azure批处理状态，处理状态转换和队列推送
 */
export async function monitorSingleBatch(
  input: SingleBatchMonitoringInput,
  env: Env
): Promise<SingleBatchMonitoringResult> {
  const { batchId, type, taskCount, submittedAt, taskMapping } = input;

  try {
    // 🎯 限流检查 - 在Azure API调用前先获取槽位
    const hasSlot = await acquireAzureAPISlot(env, `monitor-${batchId}`, 'batch_monitor');
    if (!hasSlot) {
      console.log(`[Monitor] 🚫 限流槽位不足: ${batchId}`);
      return {
        batchId,
        status: 'running',
        action: 'retry_rate_limited',
        errorMessage: 'Rate limit exceeded',
        nextAction: '等待限流槽位释放后重试'
      };
    }

    console.log(`[Monitor] 🎯 获得限流槽位: ${batchId}`);

    // 查询Azure批处理状态
    const statusResponse = await fetch(
      `https://${env.AZURE_TTS_REGION}.api.cognitive.microsoft.com/texttospeech/batchsyntheses/${batchId}?api-version=2024-04-01`,
      {
        headers: {
          'Ocp-Apim-Subscription-Key': env.AZURE_TTS_KEY || ''
        }
      }
    );

    if (!statusResponse.ok) {
      throw new Error(`Status check failed: ${statusResponse.status}`);
    }

    const azureStatus = await statusResponse.json() as AzureBatchStatus;

    // 记录Azure API状态用于调试
    console.log(`[Monitor] 🔍 Azure API状态 ${batchId}:`, {
      azureStatus: azureStatus.status,
      hasOutputs: !!azureStatus.outputs,
      hasResult: !!azureStatus.outputs?.result,
      hasError: !!azureStatus.error
    });

    // 更新数据库中的批处理状态
    let newStatus: BatchStatus;
    let action: BatchMonitoringAction;
    let nextAction: string;

    switch (azureStatus.status) {
      case 'NotStarted':
      case 'Running':
        newStatus = 'running';
        action = 'retry_running';
        nextAction = '批处理运行中，稍后重新监控';
        break;
      case 'Succeeded':
        newStatus = 'succeeded';
        action = 'completed';
        nextAction = '批处理成功，推送到下载队列';
        break;
      case 'Failed':
        newStatus = 'failed';
        action = 'failed';
        nextAction = '批处理失败，处理失败任务';
        break;
      default:
        newStatus = 'running';
        action = 'retry_needed';
        nextAction = '未知状态，延迟5秒后重新监控';
    }

    // 更新数据库状态
    await env.TTS_DB.prepare(`
      UPDATE azure_batch_jobs
      SET status = ?, updatedAt = datetime('now')
      WHERE batchId = ?
    `).bind(newStatus, batchId).run();

    // 处理成功状态 - 推送到下载队列
    if (azureStatus.status === 'Succeeded' && azureStatus.outputs?.result) {
      await env.TTS_DB.prepare(`
        UPDATE azure_batch_jobs
        SET status = 'ready_for_download', downloadUrl = ?, updatedAt = datetime('now')
        WHERE batchId = ?
      `).bind(azureStatus.outputs.result, batchId).run();

      const downloadMessage: DownloadQueueMessage = {
        type: 'batch_download',
        batchId: batchId,
        zipUrl: azureStatus.outputs.result,
        taskMapping: taskMapping,
        timestamp: new Date().toISOString()
      };

      await env.DOWNLOAD_QUEUE.send(downloadMessage);
      console.log(`[Monitor] 📤 推送下载任务到队列: ${batchId}`);
    } else if (azureStatus.status === 'Failed') {
      // 处理失败状态
      await handleFailedBatch(batchId, azureStatus.error?.message || 'Unknown error', taskMapping, env);
    }

    // 注意：重新推送逻辑已移到processMonitorQueue中处理
    // 这里只返回action状态，由调用方决定是否重新推送

    return {
      batchId,
      status: newStatus,
      action,
      nextAction
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`[Monitor] ❌ 批次${batchId}监控失败: ${errorMessage}`);

    // 检查是否为429错误
    if (error instanceof Error && error.message.includes('429')) {
      console.log(`[Monitor] 🚫 批次${batchId}遇到429限流，将通过队列重试`);

      return {
        batchId,
        status: 'running',
        action: 'retry_429',
        errorMessage,
        nextAction: '429错误，需要重新推送到监控队列'
      };
    }

    // 其他错误处理
    if (error instanceof Error && (
      error.message.includes('401') ||
      error.message.includes('403') ||
      error.message.includes('Status check failed')
    )) {
      const errorType = error.message.includes('401') ? '401' :
                       error.message.includes('403') ? '403' : 'api_error';
      await setAzureApiError(env, `监控批处理状态失败: ${error.message}`, errorType);
    }

    return {
      batchId,
      status: 'running',
      action: 'failed',
      errorMessage,
      nextAction: '监控失败，需要手动检查'
    };
  }
}

// ================================
// 下载队列处理器 - 阶段八-3 实现
// ================================

/**
 * 处理下载队列消息 - 专门处理ZIP下载、解压、上传任务
 */
export async function processDownloadQueue(
  batch: any,
  env: Env
): Promise<void> {
  console.log(`[Download Queue] 🚀 开始处理下载队列，消息数量: ${batch.messages.length}`);

  // 执行时间监控 - 28秒超时保护
  const startTime = Date.now();
  const maxExecutionTime = 28000; // 28秒限制，匹配队列30秒配置并留2秒缓冲

  for (const message of batch.messages) {
    // 执行时间检查
    const currentTime = Date.now();
    if (currentTime - startTime > maxExecutionTime) {
      console.warn(`[Download Queue] ⏰ 执行时间超过 ${maxExecutionTime/1000}秒，主动退出避免超时`);
      break;
    }

    try {
      const downloadMessage = message.body as DownloadQueueMessage;

      if (downloadMessage.type !== 'batch_download') {
        console.warn('[Download Queue] ⚠️ 无效的消息类型:', downloadMessage.type);
        continue;
      }

      console.log(`[Download Queue] 📥 处理下载任务: ${downloadMessage.batchId}`);

      // 调用原有的批次处理函数
      await processCompletedBatch({
        zipUrl: downloadMessage.zipUrl,
        taskMapping: JSON.parse(downloadMessage.taskMapping),
        batchId: downloadMessage.batchId
      }, env);

      console.log(`[Download Queue] ✅ 完成下载任务: ${downloadMessage.batchId}`);

    } catch (error) {
      console.error(`[Download Queue] ❌ 处理下载任务失败:`, error);

      // 单个任务失败不影响其他任务，继续处理
      continue;
    }
  }

  console.log(`[Download Queue] 🏁 下载队列处理完成`);
}

/**
 * FC-06: ZIP文件处理与音频上传
 * 职责：下载ZIP文件，解压音频，上传到R2存储
 */
export async function processCompletedBatch(
  input: ZipProcessingInput,
  env: Env
): Promise<UploadResult> {
  const { zipUrl, taskMapping, batchId } = input;
  
  // 下载ZIP文件
  const zipResponse = await fetch(zipUrl);
  if (!zipResponse.ok) {
    throw new Error(`Failed to download ZIP: ${zipResponse.status}`);
  }
  
  const zipBuffer = await zipResponse.arrayBuffer();
  
  // 使用JSZip解压ZIP文件
  const zip = await JSZip.loadAsync(zipBuffer);
  
  const result: UploadResult = {
    successfulUploads: 0,
    failedUploads: 0,
    uploadedFiles: [],
    errors: []
  };
  
  // 收集所有上传任务，准备并行执行
  const uploadTasks: Array<() => Promise<{taskId: string, r2Key: string, fileSize: number}>> = [];

  for (const [filename, file] of Object.entries(zip.files)) {
    // 跳过目录和非音频文件
    if (file.dir || !filename.endsWith('.wav')) continue;

    // 解析orderIndex并规范化为十进制数字
    const orderMatch = filename.match(/^(\d+)\.wav$/);
    if (!orderMatch) {
      result.errors.push({ taskId: 'unknown', error: `Invalid filename: ${filename}` });
      result.failedUploads++;
      continue;
    }

    // 修复索引偏移：Azure 文件从 1 开始，orderIndex 从 0 开始
    const azureFileIndex = parseInt(orderMatch[1], 10);
    const normalizedOrderIndex = (azureFileIndex - 1).toString();

    // 边界检查
    if (azureFileIndex < 1) {
      result.errors.push({ taskId: 'unknown', error: `Invalid Azure file index: ${azureFileIndex}` });
      result.failedUploads++;
      continue;
    }

    const taskId = taskMapping[normalizedOrderIndex];

    if (!taskId) {
      result.errors.push({ taskId: 'unknown', error: `No task mapping for index: ${normalizedOrderIndex}` });
      result.failedUploads++;
      continue;
    }

    // 创建上传任务（延迟执行）
    uploadTasks.push(async () => {
      // 获取音频文件内容
      const audioContent = await file.async('arraybuffer');

      // 生成R2存储键名（移除前缀，保持与现有文件格式一致）
      const r2Key = `${taskId}.wav`;

      // 上传到R2存储
      await env.AUDIO_BUCKET.put(r2Key, audioContent, {
        httpMetadata: {
          contentType: 'audio/wav',
          cacheControl: TTS_CONFIG.CACHE_CONTROL
        }
      });

      return {
        taskId: taskId,
        r2Key: r2Key,
        fileSize: audioContent.byteLength
      };
    });
  }

  // 并行执行上传任务，限制并发数为10
  console.log(`🚀 开始并行上传 ${uploadTasks.length} 个音频文件，并发限制: 10`);
  const concurrencyLimit = 10;

  for (let i = 0; i < uploadTasks.length; i += concurrencyLimit) {
    const batch = uploadTasks.slice(i, i + concurrencyLimit);

    try {
      const batchResults = await Promise.all(
        batch.map(async (task) => {
          try {
            return await task();
          } catch (error) {
            throw error;
          }
        })
      );

      // 处理成功的上传结果
      for (const uploadResult of batchResults) {
        result.uploadedFiles.push(uploadResult);
        result.successfulUploads++;
        console.log(`✅ Successfully uploaded audio for task ${uploadResult.taskId}, file size: ${uploadResult.fileSize} bytes`);
      }

    } catch (error) {
      // 处理批次中的错误
      result.errors.push({
        taskId: 'batch_error',
        error: error instanceof Error ? error.message : 'Unknown batch upload error'
      });
      result.failedUploads++;
      console.error(`❌ Failed to upload batch starting at index ${i}:`, error);
    }
  }
  
  // 调用FC-07更新任务状态
  await updateTasksAndBilling({
    uploadResults: [result],
    batchId,
    completedAt: new Date().toISOString()
  }, env);
  
  return result;
}

/**
 * FC-07: 任务状态更新与计费统计
 * 职责：更新TTS任务状态为completed，记录R2文件路径，更新计费统计
 */
export async function updateTasksAndBilling(
  input: TaskUpdateInput,
  env: Env
): Promise<void> {
  const { uploadResults, batchId, completedAt } = input;
  
  for (const uploadResult of uploadResults) {
    // 更新成功上传的任务
    for (const uploadedFile of uploadResult.uploadedFiles) {
      const audioUrl = `https://audio.senseword.app/${uploadedFile.r2Key}`;
      
      await env.TTS_DB.prepare(`
          UPDATE tts_tasks 
          SET 
            status = 'completed',
            audioUrl = ?,
            completedAt = ?,
            updatedAt = datetime('now')
          WHERE ttsId = ?
        `).bind(audioUrl, completedAt, uploadedFile.taskId).run();
    }
    
    // 更新失败的任务
    for (const error of uploadResult.errors) {
      if (error.taskId !== 'unknown') {
        await env.TTS_DB.prepare(`
          UPDATE tts_tasks 
          SET 
            status = 'failed',
            errorMessage = ?,
            updatedAt = datetime('now')
          WHERE ttsId = ?
        `).bind(error.error, error.taskId).run();
      }
    }
  }
  
  // 更新批处理状态为processed
  await env.TTS_DB.prepare(`
    UPDATE azure_batch_jobs 
    SET status = 'processed', completedAt = ? 
    WHERE batchId = ?
  `).bind(completedAt, batchId).run();
  
  console.log(`✅ 批处理${batchId}处理完成，任务状态已更新`);
}

/**
 * 处理失败的批次
 */
async function handleFailedBatch(
  batchId: string,
  errorMessage: string,
  taskMapping: string,
  env: Env
): Promise<void> {
  // 解析任务映射JSON
  const parsedMapping: Record<string, string> = JSON.parse(taskMapping);

  // 提取所有任务ID
  const taskIds = Object.values(parsedMapping);

  if (taskIds.length > 0) {
    // 构建动态SQL占位符
    const placeholders = taskIds.map(() => '?').join(',');

    // 将批次中的所有任务标记为失败
    await env.TTS_DB.prepare(`
      UPDATE tts_tasks
      SET
        status = 'failed',
        errorMessage = ?,
        updatedAt = datetime('now')
      WHERE ttsId IN (${placeholders})
    `).bind(errorMessage, ...taskIds).run();
  }

  // 更新批处理状态
  await env.TTS_DB.prepare(`
    UPDATE azure_batch_jobs
    SET status = 'failed', completedAt = datetime('now')
    WHERE batchId = ?
  `).bind(batchId).run();
}

/**
 * 获取进行中的批处理任务
 */
export async function getPendingBatches(env: Env): Promise<PendingBatchJob[]> {
  const result = await env.TTS_DB.prepare(`
    SELECT
      batchId,
      type,
      taskCount,
      submittedAt,
      taskMapping
    FROM azure_batch_jobs
    WHERE status IN ('submitted', 'running')
    ORDER BY submittedAt ASC
  `).all();

  return result.results as unknown as PendingBatchJob[];
}

/**
 * 获取长时间运行的批处理任务
 * 职责：查询超过30分钟的running状态批处理，用于保底监控
 */
export async function getLongRunningBatches(env: Env): Promise<PendingBatchJob[]> {
  try {
    const result = await env.TTS_DB.prepare(`
      SELECT batchId, type, taskCount, submittedAt, taskMapping
      FROM azure_batch_jobs
      WHERE status = 'running'
        AND updatedAt < datetime('now', '-30 minutes')
      ORDER BY updatedAt ASC
      LIMIT 50
    `).all();

    if (!result.results) {
      return [];
    }

    return result.results.map(row => ({
      batchId: row.batchId as string,
      type: row.type as TTSType,
      taskCount: row.taskCount as number,
      submittedAt: row.submittedAt as string,
      taskMapping: row.taskMapping as string
    }));

  } catch (error) {
    console.error('[Get Long Running Batches] ❌ 查询失败:', error);
    return [];
  }
}

/**
 * 获取监控队列推送失败的批处理任务
 */
export async function getFailedMonitorBatches(env: Env): Promise<PendingBatchJob[]> {
  const result = await env.TTS_DB.prepare(`
    SELECT
      batchId,
      type,
      taskCount,
      submittedAt,
      taskMapping
    FROM azure_batch_jobs
    WHERE status = 'monitor_queue_failed'
    ORDER BY submittedAt ASC
  `).all();

  return result.results as unknown as PendingBatchJob[];
}

/**
 * FC-07: 清理孤儿批处理任务
 * 职责：清理API Key切换后无法查询的submitted/running状态批处理任务，重置对应的TTS任务状态
 */
export async function cleanupOrphanedBatches(env: Env): Promise<OrphanedBatchCleanupResult> {
  const result: OrphanedBatchCleanupResult = {
    cleanedBatches: 0,
    resetTasks: 0,
    cleanedBatchIds: [],
    resetTaskIds: [],
    errors: []
  };

  try {
    console.log('[Cleanup] 🧹 开始清理孤儿批处理任务');

    // 1. 查询所有submitted和running状态的批处理记录
    const orphanedBatchesResult = await env.TTS_DB.prepare(`
      SELECT
        batchId,
        type,
        taskCount,
        submittedAt,
        taskMapping
      FROM azure_batch_jobs
      WHERE status IN ('submitted', 'running')
      ORDER BY submittedAt ASC
    `).all();

    const orphanedBatches = orphanedBatchesResult.results as unknown as PendingBatchJob[];

    if (orphanedBatches.length === 0) {
      console.log('[Cleanup] ✅ 没有发现孤儿批处理任务');
      return result;
    }

    console.log(`[Cleanup] 📊 发现 ${orphanedBatches.length} 个孤儿批处理任务`);

    // 2. 处理每个孤儿批处理任务
    for (const batch of orphanedBatches) {
      try {
        // 解析任务映射
        const taskMapping: Record<string, string> = JSON.parse(batch.taskMapping);
        const taskIds = Object.values(taskMapping);

        console.log(`[Cleanup] 🔄 处理批处理 ${batch.batchId}，包含 ${taskIds.length} 个任务`);

        // 3. 重置对应的TTS任务状态为pending
        if (taskIds.length > 0) {
          const placeholders = taskIds.map(() => '?').join(',');
          const resetResult = await env.TTS_DB.prepare(`
            UPDATE tts_tasks
            SET status = 'pending', updatedAt = datetime('now')
            WHERE ttsId IN (${placeholders}) AND status = 'processing'
          `).bind(...taskIds).run();

          const resetCount = resetResult.meta?.changes || 0;
          result.resetTasks += resetCount;
          result.resetTaskIds.push(...taskIds.slice(0, resetCount));

          console.log(`[Cleanup] 📝 重置了 ${resetCount} 个任务状态为 pending`);
        }

        // 4. 删除孤儿批处理记录
        await env.TTS_DB.prepare(`
          DELETE FROM azure_batch_jobs
          WHERE batchId = ?
        `).bind(batch.batchId).run();

        result.cleanedBatches++;
        result.cleanedBatchIds.push(batch.batchId);

        console.log(`[Cleanup] 🗑️ 删除了批处理记录 ${batch.batchId}`);

      } catch (batchError) {
        const errorMessage = `处理批处理 ${batch.batchId} 时出错: ${batchError instanceof Error ? batchError.message : 'Unknown error'}`;
        result.errors.push(errorMessage);
        console.error(`[Cleanup] ❌ ${errorMessage}`);
      }
    }

    console.log(`[Cleanup] ✅ 清理完成: 删除 ${result.cleanedBatches} 个批处理，重置 ${result.resetTasks} 个任务`);

  } catch (error) {
    const errorMessage = `清理孤儿批处理任务失败: ${error instanceof Error ? error.message : 'Unknown error'}`;
    result.errors.push(errorMessage);
    console.error(`[Cleanup] ❌ ${errorMessage}`);
  }

  return result;
}

/**
 * FC-08: 推送批处理任务到监控队列
 * 职责：将新创建的批处理任务推送到监控队列，实现异步并发监控
 */
export async function pushToMonitorQueue(
  batchId: string,
  batchType: TTSType,
  taskCount: number,
  submittedAt: string,
  taskMapping: string,
  env: Env
): Promise<boolean> {
  try {
    const monitorMessage: MonitorQueueMessage = {
      type: 'batch_monitor',
      batchId: batchId,
      batchType: batchType,
      taskCount: taskCount,
      submittedAt: submittedAt,
      taskMapping: taskMapping,
      timestamp: new Date().toISOString()
    };

    // 推送到监控队列
    await env.MONITOR_QUEUE.send(monitorMessage);

    console.log(`[Monitor Queue] 📤 推送监控消息: ${batchId} (${taskCount} 任务)`);
    return true;

  } catch (error) {
    console.error(`[Monitor Queue] ❌ 推送监控消息失败 ${batchId}:`, error);
    // 监控队列推送失败时，返回false，由调用方处理
    return false;
  }
}