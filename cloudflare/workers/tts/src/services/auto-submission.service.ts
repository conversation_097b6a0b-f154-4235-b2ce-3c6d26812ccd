// ================================
// 自动提交服务
// 自动查询 pending 任务并提交到队列
// ================================

import type {
  Env,
  TTSType,
  TTSTaskInput,
  BatchQueueMessage
} from '../types/realtime-tts-types';

import { generateBatchId } from '../types/realtime-tts-types';

import { 
  hasAzureApiError, 
  getAutoSubmissionConfig,
  updateLastAutoSubmission 
} from './workflow-config.service';

/**
 * 检查并自动提交 pending 任务到队列
 */
export async function checkAndSubmitPendingTasks(env: Env): Promise<{
  success: boolean;
  tasksSubmitted: number;
  batchesCreated: number;
  message: string;
}> {
  try {
    console.log('[Auto Submission] 🔍 开始检查 pending 任务');
    
    // 1. 错误预检查
    const hasError = await hasAzureApiError(env);
    if (hasError) {
      console.warn('[Auto Submission] ⚠️ Azure API 处于错误状态，跳过自动提交');
      return {
        success: false,
        tasksSubmitted: 0,
        batchesCreated: 0,
        message: 'Azure API 错误状态，跳过提交'
      };
    }
    
    // 2. 检查自动提交是否启用
    const config = await getAutoSubmissionConfig(env);
    if (!config.enabled) {
      console.log('[Auto Submission] ⏸️ 自动提交已禁用');
      return {
        success: false,
        tasksSubmitted: 0,
        batchesCreated: 0,
        message: '自动提交已禁用'
      };
    }
    
    // 3. 优化查询：按优先级分别查询，避免全表扫描
    const maxTasks = parseInt(env.AUTO_SUBMISSION_MAX_TASKS || '1000');
    const tasks: Array<{
      ttsId: string;
      text: string;
      type: TTSType;
    }> = [];

    // 只查询真正待处理的任务，避免重复推送已在队列中的任务
    const statusPriority = ['pending'];

    for (const status of statusPriority) {
      if (tasks.length >= maxTasks) break;

      const remainingSlots = maxTasks - tasks.length;
      // 性能优化：去掉 ORDER BY 排序，利用 LIMIT 早期终止
      // 避免扫描124万条 pending 记录，从读取235万行优化到<100行
      const query = `
        SELECT ttsId, text, type
        FROM tts_tasks
        WHERE status = ?
        LIMIT ?
      `;

      const result = await env.TTS_DB.prepare(query).bind(status, remainingSlots).all();
      const statusTasks = result.results as Array<{
        ttsId: string;
        text: string;
        type: TTSType;
      }>;

      tasks.push(...statusTasks);

      if (statusTasks.length > 0) {
        console.log(`[Auto Submission] 📋 查询 ${status} 任务: ${statusTasks.length} 个`);
      }
    }
    
    if (tasks.length === 0) {
      console.log('[Auto Submission] ✅ 没有未完成任务需要提交');
      return {
        success: true,
        tasksSubmitted: 0,
        batchesCreated: 0,
        message: '没有待处理任务'
      };
    }

    console.log(`[Auto Submission] 📋 发现 ${tasks.length} 个待处理任务 (pending)`);
    
    // 4. 按类型分组任务
    const tasksByType = new Map<TTSType, TTSTaskInput[]>();
    for (const task of tasks) {
      if (!tasksByType.has(task.type)) {
        tasksByType.set(task.type, []);
      }
      tasksByType.get(task.type)!.push({
        ttsId: task.ttsId,
        text: task.text,
        type: task.type
      });
    }
    
    // 5. 按小批次处理：先提交队列，成功后再更新数据库状态
    let totalBatchesCreated = 0;
    let totalTasksProcessed = 0;

    for (const [ttsType, typeTasks] of tasksByType) {
      // 从环境变量获取批次大小，便于动态调整
      const batchSize = parseInt(env.AUTO_SUBMISSION_BATCH_SIZE || '50');
      for (let i = 0; i < typeTasks.length; i += batchSize) {
        const batchTasks = typeTasks.slice(i, i + batchSize);

        try {
          // 创建批次消息
          const batchMessage: BatchQueueMessage = {
            tasks: batchTasks,
            batchId: generateBatchId('auto'),
            ttsType: ttsType,
            timestamp: new Date().toISOString()
          };

          // 先提交到队列
          await env.TTS_QUEUE.send(batchMessage);
          console.log(`[Auto Submission] 📤 提交批次: ${batchMessage.batchId} (${batchTasks.length} 任务)`);

          // 队列提交成功后，更新这批任务的状态
          const batchTaskIds = batchTasks.map(t => t.ttsId);
          const placeholders = batchTaskIds.map(() => '?').join(',');

          await env.TTS_DB.prepare(`
            UPDATE tts_tasks
            SET status = 'queued', updatedAt = datetime('now')
            WHERE ttsId IN (${placeholders})
          `).bind(...batchTaskIds).run();

          console.log(`[Auto Submission] 📝 更新任务状态: ${batchTaskIds.length} 个任务已标记为 queued`);

          totalBatchesCreated++;
          totalTasksProcessed += batchTasks.length;

        } catch (error) {
          console.error(`[Auto Submission] ❌ 批次处理失败 (${ttsType}, ${batchTasks.length} 任务):`, error);
          // 单个批次失败不影响其他批次，继续处理
        }
      }
    }
    
    // 6. 更新最后一次自动提交记录
    await updateLastAutoSubmission(env, totalTasksProcessed, totalBatchesCreated, true);

    const successMessage = `成功提交 ${totalTasksProcessed} 个任务，创建 ${totalBatchesCreated} 个批次`;
    console.log(`[Auto Submission] ✅ ${successMessage}`);

    return {
      success: true,
      tasksSubmitted: totalTasksProcessed,
      batchesCreated: totalBatchesCreated,
      message: successMessage
    };
    
  } catch (error) {
    const errorMessage = `自动提交失败: ${error instanceof Error ? error.message : '未知错误'}`;
    console.error('[Auto Submission] ❌', errorMessage);
    
    // 记录失败的自动提交
    await updateLastAutoSubmission(env, 0, 0, false);
    
    return {
      success: false,
      tasksSubmitted: 0,
      batchesCreated: 0,
      message: errorMessage
    };
  }
}

/**
 * 获取 pending 任务统计信息
 */
export async function getPendingTasksStats(env: Env): Promise<{
  totalPending: number;
  byType: Record<string, number>;
}> {
  try {
    // 获取总数
    const totalResult = await env.TTS_DB.prepare(
      'SELECT COUNT(*) as count FROM tts_tasks WHERE status = ?'
    ).bind('pending').first();
    
    const totalPending = (totalResult?.count as number) || 0;
    
    // 按类型统计
    const typeResult = await env.TTS_DB.prepare(`
      SELECT type, COUNT(*) as count 
      FROM tts_tasks 
      WHERE status = 'pending' 
      GROUP BY type
    `).all();
    
    const byType: Record<string, number> = {};
    if (typeResult.results) {
      for (const row of typeResult.results) {
        byType[row.type as string] = row.count as number;
      }
    }
    
    return { totalPending, byType };
    
  } catch (error) {
    console.error('[Auto Submission] 获取 pending 任务统计失败:', error);
    return { totalPending: 0, byType: {} };
  }
}

/**
 * 检查是否需要自动提交（基于时间间隔）
 */
export async function shouldAutoSubmit(env: Env): Promise<boolean> {
  try {
    const config = await getAutoSubmissionConfig(env);
    if (!config.enabled) {
      return false;
    }
    
    // 检查是否有 pending 任务
    const stats = await getPendingTasksStats(env);
    if (stats.totalPending === 0) {
      return false;
    }
    
    // 检查 Azure API 状态
    const hasError = await hasAzureApiError(env);
    if (hasError) {
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('[Auto Submission] 检查自动提交条件失败:', error);
    return false;
  }
}
