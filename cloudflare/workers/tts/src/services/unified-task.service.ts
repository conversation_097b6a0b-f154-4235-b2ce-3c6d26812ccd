// ================================
// 统一任务与工具服务
// 合并了task-manager和utils的功能
// ================================

import type {
  TTSTaskInput,
  TaskProcessingResult,
  TaskStatus,
  TaskUpdateParams,
  TaskUpdateData,
  Env
} from '../types/realtime-tts-types';

// ================================
// R2存储工具函数 (来自 utils.service.ts)
// ================================

/**
 * 验证R2存储连接
 */
export async function validateR2Connection(env: Env): Promise<boolean> {
  try {
    // 尝试列出存储桶内容（限制1个对象）
    const objects = await env.AUDIO_BUCKET.list({ limit: 1 });
    console.log(`[R2 Validation] ✅ R2连接正常, 对象数量: ${objects.objects.length}`);
    return true;
  } catch (error) {
    console.error(`[R2 Validation] ❌ R2连接失败:`, error);
    return false;
  }
}

/**
 * 清理过期的处理中任务 (来自 utils.service.ts)
 */
export async function cleanupStaleProcessingTasks(env: Env, timeoutMinutes: number = 30): Promise<number> {
  try {
    const timeoutThreshold = new Date(Date.now() - timeoutMinutes * 60 * 1000).toISOString();
    
    const query = `
      UPDATE tts_tasks 
      SET status = 'failed', 
          errorMessage = 'Processing timeout', 
          updatedAt = datetime('now')
      WHERE status = 'processing' 
        AND updatedAt < ?
    `;
    
    const result = await env.TTS_DB.prepare(query).bind(timeoutThreshold).run();
    
    const cleanedCount = result.meta?.changes || 0;
    
    if (cleanedCount > 0) {
      console.log(`[Cleanup] 🧹 清理了 ${cleanedCount} 个超时的处理中任务`);
    }
    
    return cleanedCount;
    
  } catch (error) {
    console.error(`[Cleanup] ❌ 清理超时任务失败:`, error);
    return 0;
  }
}

// ================================
// FC-04: 任务状态管理核心服务 (来自 task-manager.service.ts)
// ================================

/**
 * Worker端核心状态管理，筛选pending任务，更新状态，协调并发处理
 * 
 * @param tasks 输入的任务列表
 * @param env Worker环境
 * @returns 任务处理结果
 */
export async function manageTaskProcessing(
  tasks: TTSTaskInput[], 
  env: Env
): Promise<TaskProcessingResult> {
  console.log(`[Task Manager] 🔄 开始管理 ${tasks.length} 个任务`);
  
  const startTime = Date.now();
  
  // 初始化结果统计
  const result: TaskProcessingResult = {
    received: tasks.length,
    pending_tasks: [],
    skipped_completed: 0,
    skipped_processing: 0,
    processed_successfully: 0,
    processing_failed: 0,
    processing_results: []
  };
  
  try {
    // 1. 批量检查任务状态，筛选需要处理的任务
    const statusCheckResults = await batchCheckTaskStatus(tasks, env);
    
    // 2. 分类任务
    for (const task of tasks) {
      const existingStatus = statusCheckResults.get(task.ttsId);
      
      if (existingStatus === 'completed') {
        result.skipped_completed++;
        result.processing_results.push({
          ttsId: task.ttsId,
          status: 'skipped',
          processingTime: 0
        });
      } else if (existingStatus === 'processing') {
        result.skipped_processing++;
        result.processing_results.push({
          ttsId: task.ttsId,
          status: 'skipped',
          processingTime: 0
        });
      } else {
        // pending 或 failed 状态的任务需要处理
        result.pending_tasks.push(task);
      }
    }
    
    const processingTime = Date.now() - startTime;
    console.log(`[Task Manager] ✅ 任务分类完成 (${processingTime}ms): 待处理=${result.pending_tasks.length}, 已完成=${result.skipped_completed}, 处理中=${result.skipped_processing}`);
    
    return result;
    
  } catch (error) {
    console.error('[Task Manager] ❌ 任务管理失败:', error);
    throw error;
  }
}

/**
 * 获取任务统计信息
 */
export async function getTaskStatistics(env: Env): Promise<Record<string, number>> {
  try {
    // 快速获取total - 使用递增ID
    const totalResult = await env.TTS_DB.prepare('SELECT MAX(id) as maxId FROM tts_tasks').first();

    // 直接查询所有非completed状态，获取准确统计
    const query = `
      SELECT status, COUNT(*) as count
      FROM tts_tasks
      WHERE status IN ('pending', 'queued', 'processing', 'failed')
      GROUP BY status
    `;

    const result = await env.TTS_DB.prepare(query).all();
    
    const stats: Record<string, number> = {
      total: (totalResult?.maxId as number) || 0,
      queued: 0,
      processing: 0,
      failed: 0,
      pending: 0,
      completed: 0
    };
    
    if (result.results) {
      for (const row of result.results) {
        const status = row.status as string;
        const count = row.count as number;
        // 只处理查询的核心状态
        stats[status] = count;
      }
    }

    // 直接通过数学计算获得completed数量
    stats.completed = Math.max(0, stats.total - stats.pending - stats.queued - stats.processing - stats.failed);

    return stats;
    
  } catch (error) {
    console.error('[Task Manager] ❌ 获取统计信息失败:', error);
    return { total: 0, queued: 0, processing: 0, failed: 0, pending: 0, completed: 0 };
  }
}

// ================================
// 批量状态检查 (来自 task-manager.service.ts)
// ================================

/**
 * 批量检查任务状态
 */
async function batchCheckTaskStatus(
  tasks: TTSTaskInput[], 
  env: Env
): Promise<Map<string, TaskStatus | null>> {
  const statusMap = new Map<string, TaskStatus | null>();
  
  if (tasks.length === 0) {
    return statusMap;
  }

  const taskIds = tasks.map(task => task.ttsId);
  const query = `SELECT id, status FROM tts_tasks WHERE id IN (${taskIds.map(() => '?').join(',')})`;
  
  const results = await env.TTS_DB.prepare(query).bind(...taskIds).all();
  
  if (results.results) {
    for (const row of results.results) {
      statusMap.set(row.id as string, row.status as TaskStatus);
    }
  }
  
  return statusMap;
}

// ================================
// TTSID查询功能 (索引偏移问题验证)
// ================================

/**
 * 根据TTSID查询单个任务信息
 */
export async function queryTaskById(ttsId: string, env: Env): Promise<any | null> {
  try {
    const query = `
      SELECT ttsId, text, type, status, audioUrl, createdAt, updatedAt
      FROM tts_tasks
      WHERE ttsId = ?
    `;

    const result = await env.TTS_DB.prepare(query).bind(ttsId).first();

    if (!result) {
      return null;
    }

    return {
      ttsId: result.ttsId,
      text: result.text,
      type: result.type,
      status: result.status,
      audioUrl: result.audioUrl,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt
    };

  } catch (error) {
    console.error(`[Query Task] ❌ 查询任务失败 (${ttsId}):`, error);
    throw error;
  }
}

/**
 * 根据TTSID数组批量查询任务信息
 */
export async function queryTasksByIds(ttsIds: string[], env: Env): Promise<any[]> {
  try {
    if (ttsIds.length === 0) {
      return [];
    }

    const placeholders = ttsIds.map(() => '?').join(',');
    const query = `
      SELECT ttsId, text, type, status, audioUrl, createdAt, updatedAt
      FROM tts_tasks
      WHERE ttsId IN (${placeholders})
      ORDER BY createdAt
    `;

    const result = await env.TTS_DB.prepare(query).bind(...ttsIds).all();

    if (!result.results) {
      return [];
    }

    return result.results.map((row: any) => ({
      ttsId: row.ttsId,
      text: row.text,
      type: row.type,
      status: row.status,
      audioUrl: row.audioUrl,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    }));

  } catch (error) {
    console.error(`[Query Tasks] ❌ 批量查询任务失败 (${ttsIds.length} IDs):`, error);
    throw error;
  }
}