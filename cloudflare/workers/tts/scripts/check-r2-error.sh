#!/bin/bash

# R2错误检查脚本
# 检查特定时间段内的R2上传错误和任务状态

echo "🔍 检查R2上传错误和任务恢复状态..."
echo "================================================"

# 1. 检查队列状态
echo "📋 1. 检查队列状态..."
wrangler queues list | grep -E "(download-queue|download-dead-letter-queue)"

echo ""
echo "📋 2. 检查最近的失败任务..."

# 查询最近的失败任务
wrangler d1 execute senseword-tts-db --command "
SELECT 
  ttsId, 
  status, 
  errorMessage, 
  updatedAt,
  batchId
FROM tts_tasks 
WHERE status = 'failed' 
  AND updatedAt > datetime('now', '-1 hour')
ORDER BY updatedAt DESC 
LIMIT 10;
" --remote

echo ""
echo "📋 3. 检查相关批次状态..."

# 查询相关批次的状态
wrangler d1 execute senseword-tts-db --command "
SELECT 
  batchId,
  status,
  taskCount,
  updatedAt
FROM azure_batch_jobs 
WHERE updatedAt > datetime('now', '-1 hour')
  AND status IN ('ready_for_download', 'processed', 'failed')
ORDER BY updatedAt DESC 
LIMIT 5;
" --remote

echo ""
echo "📋 4. 统计当前任务状态分布..."

# 统计任务状态
wrangler d1 execute senseword-tts-db --command "
SELECT 
  status,
  COUNT(*) as count
FROM tts_tasks 
GROUP BY status
ORDER BY count DESC;
" --remote

echo ""
echo "✅ R2错误检查完成！"
echo "================================================"
echo "💡 如果发现failed任务，系统会在下次自动提交时重新处理"
echo "🔄 队列重试机制：download-queue最多重试3次"
echo "📤 死信队列：最终失败的消息会进入download-dead-letter-queue"
