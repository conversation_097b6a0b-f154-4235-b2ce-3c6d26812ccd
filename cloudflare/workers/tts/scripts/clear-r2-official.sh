#!/bin/bash

# R2 Bucket清理脚本 - 官方推荐方法
# 使用AWS CLI和Cloudflare R2的S3兼容API

set -e

# 配置信息（从wrangler.toml获取）
ACCESS_KEY_ID="99af964b1328add9393c77aea94307e7"
SECRET_ACCESS_KEY="5012ceb7e11a3c5a7a9d0feedce750652bb93675277a99d2a1f982bdaa2a9f53"
BUCKET_NAME="senseword-audio"
ACCOUNT_ID="5b5f2240cac4108f08675fc44a00978b"
ENDPOINT="https://${ACCOUNT_ID}.r2.cloudflarestorage.com"

echo "🚀 开始清理R2 bucket: ${BUCKET_NAME}"
echo "📡 使用端点: ${ENDPOINT}"
echo ""

# 检查AWS CLI是否安装
if ! command -v aws &> /dev/null; then
    echo "⚠️  AWS CLI未安装，正在安装..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            echo "📦 使用Homebrew安装AWS CLI..."
            brew install awscli
        else
            echo "❌ 请先安装AWS CLI"
            echo "💡 macOS安装命令:"
            echo "   curl 'https://awscli.amazonaws.com/AWSCLIV2.pkg' -o 'AWSCLIV2.pkg'"
            echo "   sudo installer -pkg AWSCLIV2.pkg -target /"
            exit 1
        fi
    else
        echo "❌ 请先安装AWS CLI"
        echo "💡 Linux安装命令:"
        echo "   curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'"
        echo "   unzip awscliv2.zip"
        echo "   sudo ./aws/install"
        exit 1
    fi
fi

echo "✅ AWS CLI已安装"

# 临时配置AWS凭证
export AWS_ACCESS_KEY_ID="$ACCESS_KEY_ID"
export AWS_SECRET_ACCESS_KEY="$SECRET_ACCESS_KEY"
export AWS_DEFAULT_REGION="auto"

echo "🔍 正在检查bucket状态..."

# 检查bucket是否存在并列出对象
echo "📋 列出bucket中的所有对象..."
objects_output=$(aws s3api list-objects-v2 \
    --bucket "$BUCKET_NAME" \
    --endpoint-url "$ENDPOINT" \
    --output json 2>/dev/null || echo '{"Contents": []}')

# 解析对象数量
object_count=$(echo "$objects_output" | jq -r '.Contents | length' 2>/dev/null || echo "0")

if [ "$object_count" = "0" ] || [ "$object_count" = "null" ]; then
    echo "✨ Bucket已经是空的！"
    echo "🎯 无需清理"
else
    echo "📁 发现 $object_count 个对象需要删除"
    echo ""
    
    # 显示前几个对象作为确认
    echo "📄 前5个对象预览:"
    echo "$objects_output" | jq -r '.Contents[0:5][] | "  - " + .Key' 2>/dev/null || echo "  (无法解析对象列表)"
    echo ""
    
    # 确认删除
    read -p "⚠️  确定要删除所有 $object_count 个对象吗？(y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        echo ""
        echo "🗑️  开始批量删除所有对象..."
        
        # 使用aws s3 rm命令递归删除所有对象
        aws s3 rm "s3://${BUCKET_NAME}" \
            --recursive \
            --endpoint-url "$ENDPOINT"
        
        echo ""
        echo "🔍 验证清理结果..."
        
        # 验证清理结果
        remaining_output=$(aws s3api list-objects-v2 \
            --bucket "$BUCKET_NAME" \
            --endpoint-url "$ENDPOINT" \
            --output json 2>/dev/null || echo '{"Contents": []}')
        
        remaining_count=$(echo "$remaining_output" | jq -r '.Contents | length' 2>/dev/null || echo "0")
        
        if [ "$remaining_count" = "0" ] || [ "$remaining_count" = "null" ]; then
            echo "🎉 清理完成！"
            echo "🎯 Bucket已完全清空！"
        else
            echo "⚠️  清理可能不完整，还剩余 $remaining_count 个对象"
            echo "📄 剩余对象:"
            echo "$remaining_output" | jq -r '.Contents[] | "  - " + .Key' 2>/dev/null || echo "  (无法解析剩余对象)"
        fi
    else
        echo "❌ 用户取消操作"
        exit 0
    fi
fi

# 清理环境变量
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY
unset AWS_DEFAULT_REGION

echo ""
echo "✅ 清理脚本执行完成"
echo "📊 最终状态: Bucket '$BUCKET_NAME' 已清空"
