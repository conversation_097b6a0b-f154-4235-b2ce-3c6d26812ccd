#!/bin/bash

# TTS Worker 部署验证脚本
# 验证生产环境部署是否成功

echo "🚀 开始验证 TTS Worker 生产环境部署..."
echo "================================================"

# 1. 检查 Worker 状态
echo "📋 1. 检查 Worker 部署状态..."
wrangler deployments list --env production | head -10

echo ""
echo "📋 2. 检查队列配置..."
wrangler queues list | grep -E "(tts-processing-queue|billing-queue|download-queue)"

echo ""
echo "📋 3. 检查 D1 数据库连接..."
wrangler d1 execute senseword-tts-db --command "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;" | head -10

echo ""
echo "📋 4. 检查 R2 存储桶..."
wrangler r2 bucket list | grep senseword-audio

echo ""
echo "📋 5. 测试 Worker 端点响应..."
WORKER_URL="https://senseword-tts-worker.zhouqi-aaha.workers.dev"
echo "测试 URL: $WORKER_URL"

# 测试健康检查端点
curl -s -o /dev/null -w "HTTP Status: %{http_code}, Response Time: %{time_total}s\n" "$WORKER_URL/health" || echo "健康检查端点测试失败"

echo ""
echo "📋 6. 检查定时任务配置..."
echo "定时任务: */1 * * * * (每分钟执行一次)"
echo "下次执行时间: $(date -d '+1 minute' '+%Y-%m-%d %H:%M:00')"

echo ""
echo "✅ 部署验证完成！"
echo "================================================"
echo "🔗 Worker URL: $WORKER_URL"
echo "📊 监控面板: https://dash.cloudflare.com/5b5f2240cac4108f08675fc44a00978b/workers/services/view/senseword-tts-worker/production"
echo "📈 队列监控: https://dash.cloudflare.com/5b5f2240cac4108f08675fc44a00978b/workers/queues"
echo ""
echo "🎯 系统已准备就绪，将在下一分钟开始自动处理 TTS 任务！"
