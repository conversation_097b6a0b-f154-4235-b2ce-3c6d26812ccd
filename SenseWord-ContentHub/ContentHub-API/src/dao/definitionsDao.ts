/**
 * Definitions 表数据访问层
 */
import db from '../database/connection.js';
import type { 
  Definition, 
  PaginationParams, 
  FilterParams, 
  PaginatedResult,
  AuditStatus,
  SearchResult,
  DatabaseResult
} from '../types/index.js';

export class DefinitionsDao {
  /**
   * 获取定义列表（分页）
   */
  async getDefinitions(
    filters: FilterParams = {}, 
    pagination: PaginationParams = {}
  ): Promise<PaginatedResult<Definition>> {
    const { page = 1, limit = 20, search, sortBy = 'word', sortOrder = 'asc' } = pagination;
    const offset = (page - 1) * limit;

    // 构建 WHERE 条件
    const conditions: string[] = [];
    const params: any[] = [];

    if (search) {
      conditions.push('word LIKE ?');
      params.push(`%${search}%`);
    }

    if (filters.auditStatus) {
      conditions.push('auditStatus = ?');
      params.push(filters.auditStatus);
    }

    if (filters.frequency) {
      conditions.push('frequency = ?');
      params.push(filters.frequency);
    }

    if (filters.priorityScore) {
      conditions.push('priorityScore = ?');
      params.push(filters.priorityScore);
    }

    if (filters.learningLanguage) {
      conditions.push('learningLanguage = ?');
      params.push(filters.learningLanguage);
    }

    if (filters.scaffoldingLanguage) {
      conditions.push('scaffoldingLanguage = ?');
      params.push(filters.scaffoldingLanguage);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // 验证排序字段
    const allowedSortFields = ['word', 'priorityScore', 'frequency', 'auditStatus', 'updatedAt'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'word';
    const validSortOrder = sortOrder.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    // 查询数据
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, priorityScore,
             frequency, definitionJson, auditStatus, updatedAt
      FROM definitions
      ${whereClause}
      ORDER BY ${validSortBy} ${validSortOrder}
      LIMIT ? OFFSET ?
    `;

    const countSql = `SELECT COUNT(*) as total FROM definitions ${whereClause}`;

    const [rows, countResult] = await Promise.all([
      db.query<Definition>(sql, [...params, limit, offset]),
      db.queryOne<{ total: number }>(countSql, params)
    ]);

    const total = countResult?.total || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    };
  }

  /**
   * 根据ID获取单个定义
   */
  async getDefinitionById(id: number): Promise<Definition | undefined> {
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, priorityScore, 
             frequency, definitionJson, auditStatus, updatedAt
      FROM definitions 
      WHERE id = ?
    `;
    return await db.queryOne<Definition>(sql, [id]);
  }

  /**
   * 根据单词获取定义
   */
  async getDefinitionByWord(
    word: string, 
    learningLanguage: string = 'en', 
    scaffoldingLanguage: string = 'zh'
  ): Promise<Definition | undefined> {
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, priorityScore, 
             frequency, definitionJson, auditStatus, updatedAt
      FROM definitions 
      WHERE word = ? AND learningLanguage = ? AND scaffoldingLanguage = ?
    `;
    return await db.queryOne<Definition>(sql, [word, learningLanguage, scaffoldingLanguage]);
  }

  /**
   * 更新审核状态
   */
  async updateAuditStatus(id: number, auditStatus: AuditStatus): Promise<DatabaseResult> {
    const sql = `
      UPDATE definitions 
      SET auditStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    return await db.run(sql, [auditStatus, id]);
  }

  /**
   * 批量更新审核状态
   */
  async batchUpdateAuditStatus(ids: number[], auditStatus: AuditStatus): Promise<DatabaseResult> {
    const placeholders = ids.map(() => '?').join(',');
    const sql = `
      UPDATE definitions 
      SET auditStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `;
    return await db.run(sql, [auditStatus, ...ids]);
  }

  /**
   * 获取统计信息
   */
  async getStats() {
    const statusSql = `
      SELECT auditStatus, COUNT(*) as count 
      FROM definitions 
      GROUP BY auditStatus
    `;

    const frequencySql = `
      SELECT frequency, COUNT(*) as count 
      FROM definitions 
      WHERE frequency IS NOT NULL
      GROUP BY frequency
    `;

    const prioritySql = `
      SELECT 
        CASE 
          WHEN priorityScore >= 8 THEN 'high'
          WHEN priorityScore >= 5 THEN 'medium'
          ELSE 'low'
        END as priority_level,
        COUNT(*) as count
      FROM definitions 
      GROUP BY priority_level
    `;

    const [statusStats, frequencyStats, priorityStats] = await Promise.all([
      db.query<{ auditStatus: AuditStatus; count: number }>(statusSql),
      db.query<{ frequency: string; count: number }>(frequencySql),
      db.query<{ priority_level: string; count: number }>(prioritySql)
    ]);

    // 格式化统计结果
    const stats = {
      total: 0,
      pending_review: 0,
      approved: 0,
      rejected: 0,
      in_translation: 0
    };

    statusStats.forEach(row => {
      stats[row.auditStatus] = row.count;
      stats.total += row.count;
    });

    const frequency = {
      High: 0,
      Medium: 0,
      Low: 0,
      Rare: 0,
      'Medium-Low': 0
    };

    frequencyStats.forEach(row => {
      if (frequency.hasOwnProperty(row.frequency)) {
        frequency[row.frequency as keyof typeof frequency] = row.count;
      }
    });

    const priority = {
      high: 0,
      medium: 0,
      low: 0
    };

    priorityStats.forEach(row => {
      priority[row.priority_level as keyof typeof priority] = row.count;
    });

    return { stats, frequency, priority };
  }

  /**
   * 搜索单词
   */
  async searchWords(query: string, limit: number = 10): Promise<SearchResult[]> {
    const sql = `
      SELECT word, frequency, priorityScore, auditStatus
      FROM definitions
      WHERE word LIKE ?
      ORDER BY priorityScore DESC, word ASC
      LIMIT ?
    `;
    return await db.query<SearchResult>(sql, [`%${query}%`, limit]);
  }






}

export default new DefinitionsDao();
