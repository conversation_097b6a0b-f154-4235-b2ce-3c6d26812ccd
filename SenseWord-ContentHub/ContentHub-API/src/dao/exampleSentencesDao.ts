/**
 * Example Sentences 表数据访问层
 */
import db from '../database/connection.js';
import type { 
  ExampleSentence, 
  PaginationParams, 
  FilterParams, 
  PaginatedResult,
  AuditStatus,
  TtsStatus,
  DatabaseResult
} from '../types/index.js';

export class ExampleSentencesDao {
  /**
   * 获取例句列表（分页）
   */
  async getExampleSentences(
    filters: FilterParams = {}, 
    pagination: PaginationParams = {}
  ): Promise<PaginatedResult<ExampleSentence>> {
    const { page = 1, limit = 20, search, sortBy = 'word', sortOrder = 'asc' } = pagination;
    const offset = (page - 1) * limit;

    // 构建 WHERE 条件
    const conditions: string[] = [];
    const params: any[] = [];

    if (search) {
      conditions.push('word LIKE ?');
      params.push(`%${search}%`);
    }

    if (filters.auditStatus) {
      conditions.push('auditStatus = ?');
      params.push(filters.auditStatus);
    }

    if (filters.ttsStatus) {
      conditions.push('ttsStatus = ?');
      params.push(filters.ttsStatus);
    }

    if (filters.learningLanguage) {
      conditions.push('learningLanguage = ?');
      params.push(filters.learningLanguage);
    }

    if (filters.scaffoldingLanguage) {
      conditions.push('scaffoldingLanguage = ?');
      params.push(filters.scaffoldingLanguage);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // 验证排序字段
    const allowedSortFields = ['word', 'auditStatus', 'ttsStatus', 'updatedAt'];
    const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'word';
    const validSortOrder = sortOrder.toLowerCase() === 'desc' ? 'DESC' : 'ASC';

    // 查询数据
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage,
             examplesJson, auditStatus, ttsStatus, updatedAt
      FROM example_sentences
      ${whereClause}
      ORDER BY ${validSortBy} ${validSortOrder}
      LIMIT ? OFFSET ?
    `;

    const countSql = `SELECT COUNT(*) as total FROM example_sentences ${whereClause}`;

    const [rows, countResult] = await Promise.all([
      db.query<ExampleSentence>(sql, [...params, limit, offset]),
      db.queryOne<{ total: number }>(countSql, params)
    ]);

    const total = countResult?.total || 0;
    const totalPages = Math.ceil(total / limit);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    };
  }

  /**
   * 根据ID获取单个例句
   */
  async getExampleSentenceById(id: number): Promise<ExampleSentence | undefined> {
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, 
             examplesJson, auditStatus, ttsStatus, updatedAt
      FROM example_sentences 
      WHERE id = ?
    `;
    return await db.queryOne<ExampleSentence>(sql, [id]);
  }

  /**
   * 根据单词获取例句
   */
  async getExampleSentenceByWord(
    word: string, 
    learningLanguage: string = 'en', 
    scaffoldingLanguage: string = 'zh'
  ): Promise<ExampleSentence | undefined> {
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, 
             examplesJson, auditStatus, ttsStatus, updatedAt
      FROM example_sentences 
      WHERE word = ? AND learningLanguage = ? AND scaffoldingLanguage = ?
    `;
    return await db.queryOne<ExampleSentence>(sql, [word, learningLanguage, scaffoldingLanguage]);
  }

  /**
   * 更新审核状态
   */
  async updateAuditStatus(id: number, auditStatus: AuditStatus): Promise<DatabaseResult> {
    const sql = `
      UPDATE example_sentences 
      SET auditStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    return await db.run(sql, [auditStatus, id]);
  }

  /**
   * 更新TTS状态
   */
  async updateTtsStatus(id: number, ttsStatus: TtsStatus): Promise<DatabaseResult> {
    const sql = `
      UPDATE example_sentences 
      SET ttsStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    return await db.run(sql, [ttsStatus, id]);
  }

  /**
   * 批量更新审核状态
   */
  async batchUpdateAuditStatus(ids: number[], auditStatus: AuditStatus): Promise<DatabaseResult> {
    const placeholders = ids.map(() => '?').join(',');
    const sql = `
      UPDATE example_sentences 
      SET auditStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `;
    return await db.run(sql, [auditStatus, ...ids]);
  }

  /**
   * 批量更新TTS状态
   */
  async batchUpdateTtsStatus(ids: number[], ttsStatus: TtsStatus): Promise<DatabaseResult> {
    const placeholders = ids.map(() => '?').join(',');
    const sql = `
      UPDATE example_sentences 
      SET ttsStatus = ?, updatedAt = CURRENT_TIMESTAMP 
      WHERE id IN (${placeholders})
    `;
    return await db.run(sql, [ttsStatus, ...ids]);
  }

  /**
   * 获取统计信息
   */
  async getStats() {
    const auditStatusSql = `
      SELECT auditStatus, COUNT(*) as count 
      FROM example_sentences 
      GROUP BY auditStatus
    `;

    const ttsStatusSql = `
      SELECT ttsStatus, COUNT(*) as count 
      FROM example_sentences 
      GROUP BY ttsStatus
    `;

    const [auditStats, ttsStats] = await Promise.all([
      db.query<{ auditStatus: AuditStatus; count: number }>(auditStatusSql),
      db.query<{ ttsStatus: TtsStatus; count: number }>(ttsStatusSql)
    ]);

    // 格式化统计结果
    const stats = {
      total: 0,
      pending_review: 0,
      approved: 0,
      rejected: 0,
      in_translation: 0,
      tts_pending: 0,
      tts_completed: 0,
      tts_failed: 0
    };

    auditStats.forEach(row => {
      if (row.auditStatus in stats) {
        (stats as any)[row.auditStatus] = row.count;
        stats.total += row.count;
      }
    });

    ttsStats.forEach(row => {
      if (row.ttsStatus === 'pending') {
        stats.tts_pending = row.count;
      } else if (row.ttsStatus === 'completed') {
        stats.tts_completed = row.count;
      } else if (row.ttsStatus === 'failed') {
        stats.tts_failed = row.count;
      }
    });

    return stats;
  }

  /**
   * 获取待TTS处理的例句
   */
  async getPendingTtsExamples(limit: number = 100): Promise<ExampleSentence[]> {
    const sql = `
      SELECT id, word, learningLanguage, scaffoldingLanguage, 
             examplesJson, auditStatus, ttsStatus, updatedAt
      FROM example_sentences 
      WHERE auditStatus = 'approved' AND ttsStatus = 'pending'
      ORDER BY updatedAt ASC
      LIMIT ?
    `;
    return await db.query<ExampleSentence>(sql, [limit]);
  }
}

export default new ExampleSentencesDao();
