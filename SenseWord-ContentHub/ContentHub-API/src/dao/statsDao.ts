/**
 * 统计信息数据访问层
 */
import db from '../database/connection.js';
import type { DatabaseStats } from '../types/index.js';

export class StatsDao {
  /**
   * 获取完整的数据库统计信息
   */
  async getDatabaseStats(): Promise<DatabaseStats> {
    // 并行执行所有统计查询
    const [
      definitionsStats,
      exampleSentencesStats,
      wordsForPublishStats,
      frequencyStats,
      priorityStats
    ] = await Promise.all([
      this.getDefinitionsStats(),
      this.getExampleSentencesStats(),
      this.getWordsForPublishStats(),
      this.getFrequencyStats(),
      this.getPriorityStats()
    ]);

    return {
      tables: {
        definitions: definitionsStats,
        example_sentences: exampleSentencesStats,
        words_for_publish: wordsForPublishStats
      },
      frequency: frequencyStats,
      priority: priorityStats
    };
  }

  /**
   * 获取 definitions 表统计
   */
  private async getDefinitionsStats() {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN auditStatus = 'pending_review' THEN 1 ELSE 0 END) as pending_review,
        SUM(CASE WHEN auditStatus = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN auditStatus = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN auditStatus = 'in_translation' THEN 1 ELSE 0 END) as in_translation
      FROM definitions
    `;

    const result = await db.queryOne<{
      total: number;
      pending_review: number;
      approved: number;
      rejected: number;
      in_translation: number;
    }>(sql);

    return result || {
      total: 0,
      pending_review: 0,
      approved: 0,
      rejected: 0,
      in_translation: 0
    };
  }

  /**
   * 获取 example_sentences 表统计
   */
  private async getExampleSentencesStats() {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN auditStatus = 'pending_review' THEN 1 ELSE 0 END) as pending_review,
        SUM(CASE WHEN auditStatus = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN auditStatus = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN ttsStatus = 'pending' THEN 1 ELSE 0 END) as tts_pending,
        SUM(CASE WHEN ttsStatus = 'completed' THEN 1 ELSE 0 END) as tts_completed,
        SUM(CASE WHEN ttsStatus = 'failed' THEN 1 ELSE 0 END) as tts_failed
      FROM example_sentences
    `;

    const result = await db.queryOne<{
      total: number;
      pending_review: number;
      approved: number;
      rejected: number;
      tts_pending: number;
      tts_completed: number;
      tts_failed: number;
    }>(sql);

    return result || {
      total: 0,
      pending_review: 0,
      approved: 0,
      rejected: 0,
      tts_pending: 0,
      tts_completed: 0,
      tts_failed: 0
    };
  }

  /**
   * 获取 words_for_publish 表统计
   */
  private async getWordsForPublishStats() {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN publishStatus = 'pending_upload' THEN 1 ELSE 0 END) as pending_upload,
        SUM(CASE WHEN publishStatus = 'published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN publishStatus = 'failed' THEN 1 ELSE 0 END) as failed
      FROM words_for_publish
    `;

    const result = await db.queryOne<{
      total: number;
      pending_upload: number;
      published: number;
      failed: number;
    }>(sql);

    return result || {
      total: 0,
      pending_upload: 0,
      published: 0,
      failed: 0
    };
  }

  /**
   * 获取频率分布统计
   */
  private async getFrequencyStats() {
    const sql = `
      SELECT 
        SUM(CASE WHEN frequency = 'High' THEN 1 ELSE 0 END) as High,
        SUM(CASE WHEN frequency = 'Medium' THEN 1 ELSE 0 END) as Medium,
        SUM(CASE WHEN frequency = 'Low' THEN 1 ELSE 0 END) as Low,
        SUM(CASE WHEN frequency = 'Rare' THEN 1 ELSE 0 END) as Rare
      FROM definitions
      WHERE frequency IS NOT NULL
    `;

    const result = await db.queryOne<{
      High: number;
      Medium: number;
      Low: number;
      Rare: number;
    }>(sql);

    return result || {
      High: 0,
      Medium: 0,
      Low: 0,
      Rare: 0
    };
  }

  /**
   * 获取优先级分布统计
   */
  private async getPriorityStats() {
    const sql = `
      SELECT 
        SUM(CASE WHEN priorityScore >= 8 THEN 1 ELSE 0 END) as high,
        SUM(CASE WHEN priorityScore >= 5 AND priorityScore < 8 THEN 1 ELSE 0 END) as medium,
        SUM(CASE WHEN priorityScore < 5 THEN 1 ELSE 0 END) as low
      FROM definitions
    `;

    const result = await db.queryOne<{
      high: number;
      medium: number;
      low: number;
    }>(sql);

    return result || {
      high: 0,
      medium: 0,
      low: 0
    };
  }

  /**
   * 获取审核进度统计
   */
  async getReviewProgress() {
    const sql = `
      SELECT 
        d.auditStatus as definition_status,
        e.auditStatus as example_status,
        e.ttsStatus as tts_status,
        COUNT(*) as count
      FROM definitions d
      LEFT JOIN example_sentences e ON d.word = e.word 
        AND d.learningLanguage = e.learningLanguage 
        AND d.scaffoldingLanguage = e.scaffoldingLanguage
      GROUP BY d.auditStatus, e.auditStatus, e.ttsStatus
      ORDER BY d.auditStatus, e.auditStatus, e.ttsStatus
    `;

    return await db.query<{
      definition_status: string;
      example_status: string;
      tts_status: string;
      count: number;
    }>(sql);
  }

  /**
   * 获取最近活动统计
   */
  async getRecentActivity(days: number = 7) {
    const sql = `
      SELECT 
        DATE(updatedAt) as date,
        'definitions' as table_name,
        auditStatus as status,
        COUNT(*) as count
      FROM definitions 
      WHERE updatedAt >= datetime('now', '-${days} days')
      GROUP BY DATE(updatedAt), auditStatus
      
      UNION ALL
      
      SELECT 
        DATE(updatedAt) as date,
        'example_sentences' as table_name,
        auditStatus as status,
        COUNT(*) as count
      FROM example_sentences 
      WHERE updatedAt >= datetime('now', '-${days} days')
      GROUP BY DATE(updatedAt), auditStatus
      
      ORDER BY date DESC, table_name, status
    `;

    return await db.query<{
      date: string;
      table_name: string;
      status: string;
      count: number;
    }>(sql);
  }
}

export default new StatsDao();
