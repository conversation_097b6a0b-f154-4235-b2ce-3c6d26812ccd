/**
 * SenseWord ContentHub API 服务器
 */
import Fastify from 'fastify';
import cors from '@fastify/cors';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { config } from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import db from './database/connection.js';
import definitionsRoutes from './routes/definitions.js';
import type { EnvConfig, ApiResponse } from './types/index.js';

// 加载环境变量
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 环境配置
const envConfig: EnvConfig = {
  PORT: parseInt(process.env['PORT'] || '3000'),
  HOST: process.env['HOST'] || 'localhost',
  DATABASE_PATH: process.env['DATABASE_PATH'] || path.resolve(__dirname, '../../senseword-content-factory/01-EN/SQLite/senseword_content.db'),
  LOG_LEVEL: process.env['LOG_LEVEL'] || 'info',
  CORS_ORIGIN: process.env['CORS_ORIGIN'] || 'http://localhost:5173',
  API_PREFIX: process.env['API_PREFIX'] || '/api/v1'
};

// 创建 Fastify 实例
const fastify = Fastify({
  logger: {
    level: envConfig.LOG_LEVEL
  }
});

// 注册配置到 fastify 实例
fastify.decorate('config', envConfig);

// 注册 CORS
await fastify.register(cors, {
  origin: [
    envConfig.CORS_ORIGIN,
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174'
  ],
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
});

// 注册 Swagger 文档
await fastify.register(swagger, {
  openapi: {
    openapi: '3.0.0',
    info: {
      title: 'SenseWord ContentHub API',
      description: 'SenseWord 内容生产与审核中台 API 文档',
      version: '1.0.0',
      contact: {
        name: 'SenseWord Team',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `http://${envConfig.HOST}:${envConfig.PORT}`,
        description: '开发环境'
      }
    ],
    tags: [
      { name: 'definitions', description: '单词定义管理' },
      { name: 'examples', description: '例句管理' },
      { name: 'publish', description: '发布管理' },
      { name: 'stats', description: '统计信息' },
      { name: 'system', description: '系统管理' }
    ]
  }
});

// 注册 Swagger UI
await fastify.register(swaggerUi, {
  routePrefix: '/docs',
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
  transformSpecification: (swaggerObject) => {
    return swaggerObject;
  },
  transformSpecificationClone: true
});

// 健康检查路由
fastify.get('/health', async (request, reply) => {
  const response: ApiResponse = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      database: db.isConnected() ? 'connected' : 'disconnected'
    }
  };
  return reply.code(200).send(response);
});

// 根路由
fastify.get('/', async (request, reply) => {
  const response: ApiResponse = {
    success: true,
    message: 'SenseWord ContentHub API v1.0.0',
    data: {
      documentation: '/docs',
      health: '/health',
      api: envConfig.API_PREFIX
    }
  };
  return reply.code(200).send(response);
});

// 注册 API 路由
await fastify.register(definitionsRoutes, { prefix: `${envConfig.API_PREFIX}/definitions` });

// 404 处理
fastify.setNotFoundHandler(async (request, reply) => {
  const response: ApiResponse = {
    success: false,
    error: '接口不存在',
    message: `路径 ${request.url} 未找到`
  };
  return reply.code(404).send(response);
});

// 全局错误处理
fastify.setErrorHandler(async (error, request, reply) => {
  fastify.log.error(error);

  const response: ApiResponse = {
    success: false,
    error: error.message || '服务器内部错误'
  };

  // 根据错误类型设置状态码
  let statusCode = 500;
  if (error.statusCode) {
    statusCode = error.statusCode;
  } else if (error.validation) {
    statusCode = 400;
    response.error = '请求参数验证失败';
    response.data = error.validation;
  }

  return reply.code(statusCode).send(response);
});

// 优雅关闭处理
const gracefulShutdown = async (signal: string) => {
  fastify.log.info(`收到 ${signal} 信号，开始优雅关闭...`);
  
  try {
    // 关闭数据库连接
    await db.close();
    fastify.log.info('数据库连接已关闭');
    
    // 关闭 Fastify 服务器
    await fastify.close();
    fastify.log.info('服务器已关闭');
    
    process.exit(0);
  } catch (error) {
    fastify.log.error('优雅关闭失败:', error);
    process.exit(1);
  }
};

// 注册信号处理
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// 启动服务器
const start = async () => {
  try {
    // 连接数据库
    await db.connect();
    fastify.log.info('数据库连接成功');

    // 启动服务器
    await fastify.listen({ 
      port: envConfig.PORT, 
      host: envConfig.HOST 
    });

    fastify.log.info(`🚀 SenseWord ContentHub API 启动成功!`);
    fastify.log.info(`📖 API 文档: http://${envConfig.HOST}:${envConfig.PORT}/docs`);
    fastify.log.info(`🏥 健康检查: http://${envConfig.HOST}:${envConfig.PORT}/health`);
    fastify.log.info(`🔗 API 前缀: ${envConfig.API_PREFIX}`);
    
  } catch (error) {
    fastify.log.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 启动应用
start();
