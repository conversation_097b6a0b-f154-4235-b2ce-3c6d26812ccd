/**
 * TypeScript 类型定义
 */

// 基础枚举类型
export type AuditStatus = 'pending_review' | 'approved' | 'rejected' | 'in_translation';
export type TtsStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'skipped';
export type PublishStatus = 'pending_upload' | 'uploading' | 'published' | 'failed' | 'outdated';
export type Frequency = 'High' | 'Medium' | 'Low' | 'Rare' | 'Medium-Low';

// 数据库表接口
export interface Definition {
  id?: number;
  word: string;
  learningLanguage: string;
  scaffoldingLanguage: string;
  priorityScore: number;
  frequency?: Frequency;
  definitionJson: string;
  auditStatus: AuditStatus;
  updatedAt?: string;
}

export interface ExampleSentence {
  id?: number;
  word: string;
  learningLanguage: string;
  scaffoldingLanguage: string;
  examplesJson: string;
  auditStatus: AuditStatus;
  ttsStatus: TtsStatus;
  updatedAt?: string;
}

export interface WordForPublish {
  id?: number;
  word: string;
  learningLanguage: string;
  scaffoldingLanguage: string;
  contentJson: string;
  publishStatus: PublishStatus;
  contentVersion: string;
  updatedAt?: string;
}

// 查询参数接口
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  auditStatus?: AuditStatus;
  ttsStatus?: TtsStatus;
  publishStatus?: PublishStatus;
  frequency?: Frequency;
  priorityScore?: number;
  learningLanguage?: string;
  scaffoldingLanguage?: string;
}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: PaginationInfo;
}

// 统计信息接口
export interface DatabaseStats {
  tables: {
    definitions: {
      total: number;
      pending_review: number;
      approved: number;
      rejected: number;
      in_translation: number;
    };
    example_sentences: {
      total: number;
      pending_review: number;
      approved: number;
      rejected: number;
      tts_pending: number;
      tts_completed: number;
      tts_failed: number;
    };
    words_for_publish: {
      total: number;
      pending_upload: number;
      published: number;
      failed: number;
    };
  };
  frequency: {
    High: number;
    Medium: number;
    Low: number;
    Rare: number;
  };
  priority: {
    high: number;
    medium: number;
    low: number;
  };
}

// 批量操作接口
export interface BatchOperation {
  wordIds: number[];
  operation: 'approve' | 'reject' | 'reset' | 'delete';
  auditStatus?: AuditStatus;
  ttsStatus?: TtsStatus;
  publishStatus?: PublishStatus;
}

// 状态更新接口
export interface StatusUpdate {
  auditStatus?: AuditStatus;
  ttsStatus?: TtsStatus;
  publishStatus?: PublishStatus;
}

// 数据库操作结果
export interface DatabaseResult {
  lastID?: number;
  changes: number;
}

// 环境变量接口
export interface EnvConfig {
  PORT: number;
  HOST: string;
  DATABASE_PATH: string;
  LOG_LEVEL: string;
  CORS_ORIGIN: string;
  API_PREFIX: string;
}

// Fastify 扩展类型
declare module 'fastify' {
  interface FastifyInstance {
    config: EnvConfig;
  }
}

// 错误类型
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 日志级别
export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

// 搜索结果
export interface SearchResult {
  word: string;
  frequency?: Frequency;
  priorityScore: number;
  auditStatus: AuditStatus;
}

// 单词详情（包含定义和例句）
export interface WordDetail {
  definition: Definition;
  exampleSentence: ExampleSentence;
  wordForPublish?: WordForPublish;
}
