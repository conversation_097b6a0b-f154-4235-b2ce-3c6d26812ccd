/**
 * Definitions 业务逻辑服务层
 */
import definitionsDao from '../dao/definitionsDao.js';
import exampleSentencesService from './exampleSentencesService.js';
import statsDao from '../dao/statsDao.js';
import type {
  Definition,
  PaginationParams,
  FilterParams,
  PaginatedResult,
  AuditStatus,
  SearchResult
} from '../types/index.js';
import { ApiError } from '../types/index.js';

export class DefinitionsService {
  /**
   * 获取定义列表
   */
  async getDefinitions(
    filters: FilterParams = {}, 
    pagination: PaginationParams = {}
  ): Promise<PaginatedResult<Definition>> {
    try {
      return await definitionsDao.getDefinitions(filters, pagination);
    } catch (error) {
      console.error('获取定义列表失败:', error);
      throw new ApiError('获取定义列表失败', 500);
    }
  }

  /**
   * 根据ID获取定义详情
   */
  async getDefinitionById(id: number): Promise<Definition> {
    try {
      const definition = await definitionsDao.getDefinitionById(id);
      if (!definition) {
        throw new ApiError('定义不存在', 404);
      }
      return definition;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('获取定义详情失败:', error);
      throw new ApiError('获取定义详情失败', 500);
    }
  }

  /**
   * 根据单词获取定义
   */
  async getDefinitionByWord(
    word: string, 
    learningLanguage: string = 'en', 
    scaffoldingLanguage: string = 'zh'
  ): Promise<Definition> {
    try {
      const definition = await definitionsDao.getDefinitionByWord(word, learningLanguage, scaffoldingLanguage);
      if (!definition) {
        throw new ApiError('单词定义不存在', 404);
      }
      return definition;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('获取单词定义失败:', error);
      throw new ApiError('获取单词定义失败', 500);
    }
  }

  /**
   * 更新审核状态
   */
  async updateAuditStatus(id: number, auditStatus: AuditStatus): Promise<void> {
    try {
      // 先检查定义是否存在
      const definition = await definitionsDao.getDefinitionById(id);
      if (!definition) {
        throw new ApiError('定义不存在', 404);
      }

      const result = await definitionsDao.updateAuditStatus(id, auditStatus);
      if (result.changes === 0) {
        throw new ApiError('更新失败，没有记录被修改', 400);
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('更新审核状态失败:', error);
      throw new ApiError('更新审核状态失败', 500);
    }
  }

  /**
   * 批量更新审核状态
   */
  async batchUpdateAuditStatus(ids: number[], auditStatus: AuditStatus): Promise<number> {
    try {
      if (ids.length === 0) {
        throw new ApiError('ID列表不能为空', 400);
      }

      const result = await definitionsDao.batchUpdateAuditStatus(ids, auditStatus);
      return result.changes;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('批量更新审核状态失败:', error);
      throw new ApiError('批量更新审核状态失败', 500);
    }
  }

  /**
   * 搜索单词
   */
  async searchWords(query: string, limit: number = 10): Promise<SearchResult[]> {
    try {
      if (!query || query.trim().length === 0) {
        return [];
      }

      return await definitionsDao.searchWords(query.trim(), limit);
    } catch (error) {
      console.error('搜索单词失败:', error);
      throw new ApiError('搜索单词失败', 500);
    }
  }

  /**
   * 获取统计信息
   */
  async getStats() {
    try {
      return await definitionsDao.getStats();
    } catch (error) {
      console.error('获取统计信息失败:', error);
      throw new ApiError('获取统计信息失败', 500);
    }
  }

  /**
   * 获取TTS统计信息
   */
  async getTtsStats(): Promise<{ [key: string]: number }> {
    try {
      return await exampleSentencesService.getTtsStats();
    } catch (error) {
      console.error('获取TTS统计信息失败:', error);
      throw new ApiError('获取TTS统计信息失败', 500);
    }
  }

  /**
   * 获取发布统计信息
   */
  async getPublishStats(): Promise<{ [key: string]: number }> {
    try {
      const stats = await statsDao.getDatabaseStats();
      return {
        pending_upload: stats.tables.words_for_publish.pending_upload,
        uploading: 0, // 需要根据实际业务逻辑调整
        published: stats.tables.words_for_publish.published,
        failed: stats.tables.words_for_publish.failed,
        outdated: 0 // 需要根据实际业务逻辑调整
      };
    } catch (error) {
      console.error('获取发布统计信息失败:', error);
      throw new ApiError('获取发布统计信息失败', 500);
    }
  }

  /**
   * 根据单词获取例句数据
   */
  async getExampleSentenceByWord(word: string): Promise<any | null> {
    try {
      if (!word || word.trim().length === 0) {
        throw new ApiError('单词参数不能为空', 400);
      }

      return await exampleSentencesService.getExampleSentenceByWord(word.trim());
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('获取例句数据失败:', error);
      throw new ApiError('获取例句数据失败', 500);
    }
  }

  /**
   * 更新例句审核状态
   */
  async updateExampleAuditStatus(id: number, auditStatus: AuditStatus): Promise<void> {
    try {
      await exampleSentencesService.updateAuditStatus(id, auditStatus);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('更新例句审核状态失败:', error);
      throw new ApiError('更新例句审核状态失败', 500);
    }
  }

  /**
   * 验证定义JSON格式
   */
  validateDefinitionJson(definitionJson: string): boolean {
    try {
      const parsed = JSON.parse(definitionJson);
      
      // 基本结构验证
      if (!parsed || typeof parsed !== 'object') {
        return false;
      }

      // 可以添加更多具体的验证逻辑
      // 例如检查必需字段、数据类型等
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 审核定义
   */
  async reviewDefinition(id: number, approved: boolean, comment?: string): Promise<void> {
    try {
      const definition = await definitionsDao.getDefinitionById(id);
      if (!definition) {
        throw new ApiError('定义不存在', 404);
      }

      // 验证JSON格式
      if (!this.validateDefinitionJson(definition.definitionJson)) {
        throw new ApiError('定义JSON格式无效', 400);
      }

      const newStatus: AuditStatus = approved ? 'approved' : 'rejected';
      await this.updateAuditStatus(id, newStatus);

      // 这里可以添加审核日志记录
      console.log(`定义 ${id} 审核${approved ? '通过' : '拒绝'}${comment ? `，备注：${comment}` : ''}`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('审核定义失败:', error);
      throw new ApiError('审核定义失败', 500);
    }
  }

  /**
   * 获取待审核的定义
   */
  async getPendingReviewDefinitions(limit: number = 50): Promise<Definition[]> {
    try {
      const result = await definitionsDao.getDefinitions(
        { auditStatus: 'pending_review' },
        { limit, sortBy: 'priorityScore', sortOrder: 'desc' }
      );
      return result.data;
    } catch (error) {
      console.error('获取待审核定义失败:', error);
      throw new ApiError('获取待审核定义失败', 500);
    }
  }
}

export default new DefinitionsService();
