/**
 * ExampleSentences 业务逻辑层
 */
import exampleSentencesDao from '../dao/exampleSentencesDao.js';
import { ApiError } from '../types/index.js';
import type { 
  ExampleSentence, 
  PaginationParams, 
  FilterParams, 
  PaginatedResult,
  AuditStatus,
  TtsStatus,
  DatabaseResult
} from '../types/index.js';

export class ExampleSentencesService {
  /**
   * 获取例句列表
   */
  async getExampleSentences(
    filters: FilterParams = {}, 
    pagination: PaginationParams = {}
  ): Promise<PaginatedResult<ExampleSentence>> {
    try {
      return await exampleSentencesDao.getExampleSentences(filters, pagination);
    } catch (error) {
      console.error('获取例句列表失败:', error);
      throw new ApiError('获取例句列表失败', 500);
    }
  }

  /**
   * 根据ID获取例句
   */
  async getExampleSentenceById(id: number): Promise<ExampleSentence> {
    try {
      if (!id || id <= 0) {
        throw new ApiError('例句ID无效', 400);
      }

      const exampleSentence = await exampleSentencesDao.getExampleSentenceById(id);
      
      if (!exampleSentence) {
        throw new ApiError('例句不存在', 404);
      }

      return exampleSentence;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('获取例句失败:', error);
      throw new ApiError('获取例句失败', 500);
    }
  }

  /**
   * 根据单词获取例句
   */
  async getExampleSentenceByWord(
    word: string, 
    learningLanguage: string = 'en', 
    scaffoldingLanguage: string = 'zh'
  ): Promise<ExampleSentence | null> {
    try {
      if (!word || word.trim().length === 0) {
        throw new ApiError('单词参数不能为空', 400);
      }

      return await exampleSentencesDao.getExampleSentenceByWord(
        word.trim(), 
        learningLanguage, 
        scaffoldingLanguage
      );
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('获取例句失败:', error);
      throw new ApiError('获取例句失败', 500);
    }
  }

  /**
   * 更新审核状态
   */
  async updateAuditStatus(id: number, auditStatus: AuditStatus): Promise<void> {
    try {
      if (!id || id <= 0) {
        throw new ApiError('例句ID无效', 400);
      }

      if (!['pending_review', 'approved', 'rejected', 'in_translation'].includes(auditStatus)) {
        throw new ApiError('审核状态无效', 400);
      }

      const result = await exampleSentencesDao.updateAuditStatus(id, auditStatus);
      
      if (result.changes === 0) {
        throw new ApiError('例句不存在或状态未更改', 404);
      }

      console.log(`例句 ${id} 审核状态已更新为: ${auditStatus}`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('更新例句审核状态失败:', error);
      throw new ApiError('更新例句审核状态失败', 500);
    }
  }

  /**
   * 更新TTS状态
   */
  async updateTtsStatus(id: number, ttsStatus: TtsStatus): Promise<void> {
    try {
      if (!id || id <= 0) {
        throw new ApiError('例句ID无效', 400);
      }

      if (!['pending', 'processing', 'completed', 'failed', 'skipped'].includes(ttsStatus)) {
        throw new ApiError('TTS状态无效', 400);
      }

      const result = await exampleSentencesDao.updateTtsStatus(id, ttsStatus);
      
      if (result.changes === 0) {
        throw new ApiError('例句不存在或状态未更改', 404);
      }

      console.log(`例句 ${id} TTS状态已更新为: ${ttsStatus}`);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('更新例句TTS状态失败:', error);
      throw new ApiError('更新例句TTS状态失败', 500);
    }
  }

  /**
   * 批量更新审核状态
   */
  async batchUpdateAuditStatus(ids: number[], auditStatus: AuditStatus): Promise<number> {
    try {
      if (!ids || ids.length === 0) {
        throw new ApiError('例句ID列表不能为空', 400);
      }

      if (!['pending_review', 'approved', 'rejected', 'in_translation'].includes(auditStatus)) {
        throw new ApiError('审核状态无效', 400);
      }

      const result = await exampleSentencesDao.batchUpdateAuditStatus(ids, auditStatus);
      
      console.log(`批量更新 ${result.changes} 个例句的审核状态为: ${auditStatus}`);
      return result.changes;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('批量更新例句审核状态失败:', error);
      throw new ApiError('批量更新例句审核状态失败', 500);
    }
  }

  /**
   * 批量更新TTS状态
   */
  async batchUpdateTtsStatus(ids: number[], ttsStatus: TtsStatus): Promise<number> {
    try {
      if (!ids || ids.length === 0) {
        throw new ApiError('例句ID列表不能为空', 400);
      }

      if (!['pending', 'processing', 'completed', 'failed', 'skipped'].includes(ttsStatus)) {
        throw new ApiError('TTS状态无效', 400);
      }

      const result = await exampleSentencesDao.batchUpdateTtsStatus(ids, ttsStatus);
      
      console.log(`批量更新 ${result.changes} 个例句的TTS状态为: ${ttsStatus}`);
      return result.changes;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      console.error('批量更新例句TTS状态失败:', error);
      throw new ApiError('批量更新例句TTS状态失败', 500);
    }
  }

  /**
   * 获取统计信息
   */
  async getStats() {
    try {
      return await exampleSentencesDao.getStats();
    } catch (error) {
      console.error('获取例句统计信息失败:', error);
      throw new ApiError('获取例句统计信息失败', 500);
    }
  }

  /**
   * 获取TTS统计信息
   */
  async getTtsStats(): Promise<{ [key: string]: number }> {
    try {
      const stats = await exampleSentencesDao.getStats();
      return {
        pending: stats.tts_pending,
        processing: 0, // 需要根据实际业务逻辑调整
        completed: stats.tts_completed,
        failed: stats.tts_failed,
        skipped: 0 // 需要根据实际业务逻辑调整
      };
    } catch (error) {
      console.error('获取TTS统计信息失败:', error);
      throw new ApiError('获取TTS统计信息失败', 500);
    }
  }

  /**
   * 获取待TTS处理的例句
   */
  async getPendingTtsExamples(limit: number = 100): Promise<ExampleSentence[]> {
    try {
      return await exampleSentencesDao.getPendingTtsExamples(limit);
    } catch (error) {
      console.error('获取待TTS处理例句失败:', error);
      throw new ApiError('获取待TTS处理例句失败', 500);
    }
  }
}

export default new ExampleSentencesService();
