/**
 * SQLite 数据库连接管理
 */
import sqlite3 from 'sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';
import type { DatabaseResult } from '../types/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class DatabaseConnection {
  private db: sqlite3.Database | null = null;
  private dbPath: string;

  constructor(dbPath: string) {
    this.dbPath = dbPath;
  }

  /**
   * 连接数据库
   */
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('数据库连接失败:', err.message);
          reject(err);
        } else {
          console.log('数据库连接成功:', this.dbPath);
          // 启用外键约束
          this.db!.run('PRAGMA foreign_keys = ON');
          resolve();
        }
      });
    });
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('数据库连接已关闭');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 执行查询 (SELECT)
   */
  async query<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未连接'));
        return;
      }

      this.db.all(sql, params, (err, rows) => {
        if (err) {
          console.error('查询执行失败:', err.message);
          console.error('SQL:', sql);
          console.error('参数:', params);
          reject(err);
        } else {
          resolve(rows as T[]);
        }
      });
    });
  }

  /**
   * 执行单行查询
   */
  async queryOne<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未连接'));
        return;
      }

      this.db.get(sql, params, (err, row) => {
        if (err) {
          console.error('单行查询执行失败:', err.message);
          console.error('SQL:', sql);
          console.error('参数:', params);
          reject(err);
        } else {
          resolve(row as T | undefined);
        }
      });
    });
  }

  /**
   * 执行更新/插入/删除操作
   */
  async run(sql: string, params: any[] = []): Promise<DatabaseResult> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('数据库未连接'));
        return;
      }

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('执行操作失败:', err.message);
          console.error('SQL:', sql);
          console.error('参数:', params);
          reject(err);
        } else {
          resolve({
            lastID: this.lastID,
            changes: this.changes
          });
        }
      });
    });
  }

  /**
   * 开始事务
   */
  async beginTransaction(): Promise<DatabaseResult> {
    return this.run('BEGIN TRANSACTION');
  }

  /**
   * 提交事务
   */
  async commit(): Promise<DatabaseResult> {
    return this.run('COMMIT');
  }

  /**
   * 回滚事务
   */
  async rollback(): Promise<DatabaseResult> {
    return this.run('ROLLBACK');
  }

  /**
   * 执行事务
   */
  async transaction<T>(callback: (db: DatabaseConnection) => Promise<T>): Promise<T> {
    try {
      await this.beginTransaction();
      const result = await callback(this);
      await this.commit();
      return result;
    } catch (error) {
      await this.rollback();
      throw error;
    }
  }

  /**
   * 检查数据库连接状态
   */
  isConnected(): boolean {
    return this.db !== null;
  }
}

// 创建数据库连接实例
const defaultDbPath = '/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content.db';
const dbPath = process.env['DATABASE_PATH'] || defaultDbPath;
const db = new DatabaseConnection(dbPath);

export default db;
