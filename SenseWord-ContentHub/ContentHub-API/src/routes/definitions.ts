/**
 * Definitions 路由
 */
import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import definitionsService from '../services/definitionsService.js';
import { PaginationSchema, FilterSchema, StatusUpdateSchema, BatchOperationSchema } from '../schemas/index.js';
import type { ApiResponse, PaginationParams, FilterParams } from '../types/index.js';

// 请求类型定义
interface GetDefinitionsRequest extends FastifyRequest {
  query: PaginationParams & FilterParams;
}

interface GetDefinitionByIdRequest extends FastifyRequest {
  params: { id: string };
}

interface UpdateStatusRequest extends FastifyRequest {
  params: { id: string };
  body: { auditStatus: string };
}

interface BatchUpdateRequest extends FastifyRequest {
  body: {
    wordIds: number[];
    operation: string;
    auditStatus?: string;
  };
}

interface SearchRequest extends FastifyRequest {
  query: { q: string; limit?: string };
}

export default async function definitionsRoutes(fastify: FastifyInstance) {
  // 获取定义列表
  fastify.get<{ Querystring: PaginationParams & FilterParams }>(
    '/',
    {
      schema: {
        description: '获取定义列表',
        tags: ['definitions'],
        querystring: {
          type: 'object',
          properties: {
            page: { type: 'number', minimum: 1, default: 1 },
            limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
            search: { type: 'string' },
            sortBy: { type: 'string' },
            sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'asc' },
            auditStatus: { type: 'string', enum: ['pending_review', 'approved', 'rejected', 'in_translation'] },
            frequency: { type: 'string', enum: ['High', 'Medium', 'Low', 'Rare', 'Medium-Low'] },
            priorityScore: { type: 'number', minimum: 1, maximum: 10 },
            learningLanguage: { type: 'string', default: 'en' },
            scaffoldingLanguage: { type: 'string', default: 'zh' }
          }
        },
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              data: { type: 'array' },
              pagination: {
                type: 'object',
                properties: {
                  page: { type: 'number' },
                  limit: { type: 'number' },
                  total: { type: 'number' },
                  totalPages: { type: 'number' }
                }
              }
            }
          }
        }
      }
    },
    async (request: GetDefinitionsRequest, reply: FastifyReply) => {
      try {
        // 验证查询参数
        const pagination = PaginationSchema.parse(request.query);
        const filters = FilterSchema.parse(request.query);

        const result = await definitionsService.getDefinitions(filters, pagination);

        const response: ApiResponse = {
          success: true,
          data: result.data,
          pagination: result.pagination
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取定义列表失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '获取定义列表失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 根据ID获取定义
  fastify.get<{ Params: { id: string } }>(
    '/:id',
    {
      schema: {
        description: '根据ID获取定义详情',
        tags: ['definitions'],
        params: {
          type: 'object',
          properties: {
            id: { type: 'string', pattern: '^[0-9]+$' }
          },
          required: ['id']
        }
      }
    },
    async (request: GetDefinitionByIdRequest, reply: FastifyReply) => {
      try {
        const id = parseInt(request.params.id);
        if (isNaN(id)) {
          const response: ApiResponse = {
            success: false,
            error: 'ID必须是有效的数字'
          };
          return reply.code(400).send(response);
        }

        const definition = await definitionsService.getDefinitionById(id);

        const response: ApiResponse = {
          success: true,
          data: definition
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取定义详情失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '获取定义详情失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 更新审核状态
  fastify.patch<{ Params: { id: string }; Body: { auditStatus: string } }>(
    '/:id/status',
    {
      schema: {
        description: '更新定义审核状态',
        tags: ['definitions'],
        params: {
          type: 'object',
          properties: {
            id: { type: 'string', pattern: '^[0-9]+$' }
          },
          required: ['id']
        },
        body: {
          type: 'object',
          properties: {
            auditStatus: { type: 'string', enum: ['pending_review', 'approved', 'rejected', 'in_translation'] }
          },
          required: ['auditStatus']
        }
      }
    },
    async (request: UpdateStatusRequest, reply: FastifyReply) => {
      try {
        const id = parseInt(request.params.id);
        if (isNaN(id)) {
          const response: ApiResponse = {
            success: false,
            error: 'ID必须是有效的数字'
          };
          return reply.code(400).send(response);
        }

        const { auditStatus } = StatusUpdateSchema.parse(request.body);
        
        if (!auditStatus) {
          const response: ApiResponse = {
            success: false,
            error: '审核状态不能为空'
          };
          return reply.code(400).send(response);
        }

        await definitionsService.updateAuditStatus(id, auditStatus);

        const response: ApiResponse = {
          success: true,
          message: '审核状态更新成功'
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('更新审核状态失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '更新审核状态失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 搜索单词
  fastify.get<{ Querystring: { q: string; limit?: string } }>(
    '/search',
    {
      schema: {
        description: '搜索单词',
        tags: ['definitions'],
        querystring: {
          type: 'object',
          properties: {
            q: { type: 'string', minLength: 1 },
            limit: { type: 'string', pattern: '^[0-9]+$' }
          },
          required: ['q']
        }
      }
    },
    async (request: SearchRequest, reply: FastifyReply) => {
      try {
        const { q, limit } = request.query;
        const searchLimit = limit ? parseInt(limit) : 10;

        if (isNaN(searchLimit) || searchLimit < 1 || searchLimit > 50) {
          const response: ApiResponse = {
            success: false,
            error: 'limit必须是1-50之间的数字'
          };
          return reply.code(400).send(response);
        }

        const results = await definitionsService.searchWords(q, searchLimit);

        const response: ApiResponse = {
          success: true,
          data: results
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('搜索单词失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '搜索单词失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 获取统计信息
  fastify.get(
    '/stats',
    {
      schema: {
        description: '获取定义统计信息',
        tags: ['definitions']
      }
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const stats = await definitionsService.getStats();

        const response: ApiResponse = {
          success: true,
          data: stats
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取统计信息失败:', error);
        
        const response: ApiResponse = {
          success: false,
          error: error.message || '获取统计信息失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 获取TTS统计信息
  fastify.get(
    '/tts-stats',
    {
      schema: {
        description: '获取TTS统计信息',
        tags: ['definitions']
      }
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const stats = await definitionsService.getTtsStats();

        const response: ApiResponse = {
          success: true,
          data: stats
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取TTS统计信息失败:', error);

        const response: ApiResponse = {
          success: false,
          error: error.message || '获取TTS统计信息失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 获取发布统计信息
  fastify.get(
    '/publish-stats',
    {
      schema: {
        description: '获取发布统计信息',
        tags: ['definitions']
      }
    },
    async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        const stats = await definitionsService.getPublishStats();

        const response: ApiResponse = {
          success: true,
          data: stats
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取发布统计信息失败:', error);

        const response: ApiResponse = {
          success: false,
          error: error.message || '获取发布统计信息失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 根据单词获取例句数据
  fastify.get(
    '/:word/examples',
    {
      schema: {
        description: '根据单词获取例句数据',
        tags: ['definitions'],
        params: {
          type: 'object',
          properties: {
            word: { type: 'string' }
          },
          required: ['word']
        }
      }
    },
    async (request: FastifyRequest<{ Params: { word: string } }>, reply: FastifyReply) => {
      try {
        const { word } = request.params;
        const exampleData = await definitionsService.getExampleSentenceByWord(word);

        const response: ApiResponse = {
          success: true,
          data: exampleData
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('获取例句数据失败:', error);

        const response: ApiResponse = {
          success: false,
          error: error.message || '获取例句数据失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );

  // 更新例句审核状态
  fastify.patch(
    '/:word/examples/audit-status',
    {
      schema: {
        description: '更新例句审核状态',
        tags: ['definitions'],
        params: {
          type: 'object',
          properties: {
            word: { type: 'string' }
          },
          required: ['word']
        },
        body: {
          type: 'object',
          properties: {
            auditStatus: {
              type: 'string',
              enum: ['pending_review', 'approved', 'rejected', 'in_translation']
            }
          },
          required: ['auditStatus']
        }
      }
    },
    async (request: FastifyRequest<{
      Params: { word: string },
      Body: { auditStatus: string }
    }>, reply: FastifyReply) => {
      try {
        const { word } = request.params;
        const { auditStatus } = request.body;

        // 先获取例句数据以获得ID
        const exampleData = await definitionsService.getExampleSentenceByWord(word);
        if (!exampleData) {
          const response: ApiResponse = {
            success: false,
            error: '例句数据不存在'
          };
          return reply.code(404).send(response);
        }

        await definitionsService.updateExampleAuditStatus(exampleData.id, auditStatus as any);

        const response: ApiResponse = {
          success: true,
          message: '例句审核状态更新成功'
        };

        return reply.code(200).send(response);
      } catch (error: any) {
        fastify.log.error('更新例句审核状态失败:', error);

        const response: ApiResponse = {
          success: false,
          error: error.message || '更新例句审核状态失败'
        };

        return reply.code(error.statusCode || 500).send(response);
      }
    }
  );
}
