/**
 * Zod 验证 schemas
 */
import { z } from 'zod';

// 基础枚举定义
export const AuditStatusSchema = z.enum(['pending_review', 'approved', 'rejected', 'in_translation']);
export const TtsStatusSchema = z.enum(['pending', 'processing', 'completed', 'failed', 'skipped']);
export const PublishStatusSchema = z.enum(['pending_upload', 'uploading', 'published', 'failed', 'outdated']);
export const FrequencySchema = z.enum(['High', 'Medium', 'Low', 'Rare', 'Medium-Low']);

// 分页参数
export const PaginationSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

// 筛选参数
export const FilterSchema = z.object({
  auditStatus: AuditStatusSchema.optional(),
  ttsStatus: TtsStatusSchema.optional(),
  publishStatus: PublishStatusSchema.optional(),
  frequency: FrequencySchema.optional(),
  priorityScore: z.coerce.number().min(1).max(10).optional(),
  learningLanguage: z.string().default('en'),
  scaffoldingLanguage: z.string().default('zh')
});

// JSON 验证函数
const jsonStringSchema = z.string().refine((val) => {
  try {
    JSON.parse(val);
    return true;
  } catch {
    return false;
  }
}, { message: "必须是有效的JSON字符串" });

// Definitions 表 schema
export const DefinitionSchema = z.object({
  id: z.number().optional(),
  word: z.string().min(1),
  learningLanguage: z.string().default('en'),
  scaffoldingLanguage: z.string().default('zh'),
  priorityScore: z.number().min(0).max(10).default(0),
  frequency: FrequencySchema.optional(),
  definitionJson: jsonStringSchema,
  auditStatus: AuditStatusSchema.default('pending_review'),
  updatedAt: z.string().optional()
});

// Example Sentences 表 schema
export const ExampleSentenceSchema = z.object({
  id: z.number().optional(),
  word: z.string().min(1),
  learningLanguage: z.string().default('en'),
  scaffoldingLanguage: z.string().default('zh'),
  examplesJson: jsonStringSchema,
  auditStatus: AuditStatusSchema.default('pending_review'),
  ttsStatus: TtsStatusSchema.default('pending'),
  updatedAt: z.string().optional()
});

// Words for Publish 表 schema
export const WordForPublishSchema = z.object({
  id: z.number().optional(),
  word: z.string().min(1),
  learningLanguage: z.string().default('en'),
  scaffoldingLanguage: z.string().default('zh'),
  contentJson: jsonStringSchema,
  publishStatus: PublishStatusSchema.default('pending_upload'),
  contentVersion: z.string().default('v1.0'),
  updatedAt: z.string().optional()
});

// 状态更新 schema
export const StatusUpdateSchema = z.object({
  auditStatus: AuditStatusSchema.optional(),
  ttsStatus: TtsStatusSchema.optional(),
  publishStatus: PublishStatusSchema.optional()
});

// 批量操作 schema
export const BatchOperationSchema = z.object({
  wordIds: z.array(z.number()).min(1),
  operation: z.enum(['approve', 'reject', 'reset', 'delete']),
  auditStatus: AuditStatusSchema.optional(),
  ttsStatus: TtsStatusSchema.optional(),
  publishStatus: PublishStatusSchema.optional()
});

// API 响应 schema
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  message: z.string().optional(),
  error: z.string().optional(),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number()
  }).optional()
});

// 统计信息 schema
export const StatsSchema = z.object({
  tables: z.object({
    definitions: z.object({
      total: z.number(),
      pending_review: z.number(),
      approved: z.number(),
      rejected: z.number(),
      in_translation: z.number()
    }),
    example_sentences: z.object({
      total: z.number(),
      pending_review: z.number(),
      approved: z.number(),
      rejected: z.number(),
      tts_pending: z.number(),
      tts_completed: z.number(),
      tts_failed: z.number()
    }),
    words_for_publish: z.object({
      total: z.number(),
      pending_upload: z.number(),
      published: z.number(),
      failed: z.number()
    })
  }),
  frequency: z.object({
    High: z.number(),
    Medium: z.number(),
    Low: z.number(),
    Rare: z.number()
  }),
  priority: z.object({
    high: z.number(),
    medium: z.number(),
    low: z.number()
  })
});

// 导出类型推断
export type PaginationParams = z.infer<typeof PaginationSchema>;
export type FilterParams = z.infer<typeof FilterSchema>;
export type Definition = z.infer<typeof DefinitionSchema>;
export type ExampleSentence = z.infer<typeof ExampleSentenceSchema>;
export type WordForPublish = z.infer<typeof WordForPublishSchema>;
export type StatusUpdate = z.infer<typeof StatusUpdateSchema>;
export type BatchOperation = z.infer<typeof BatchOperationSchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
export type DatabaseStats = z.infer<typeof StatsSchema>;
