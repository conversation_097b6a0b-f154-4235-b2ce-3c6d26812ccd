{"name": "senseword-contenthub-api", "version": "1.0.0", "description": "SenseWord ContentHub API - 内容生产与审核中台后端服务", "main": "dist/server.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "vitest", "lint": "eslint src/", "format": "prettier --write src/", "type-check": "tsc --noEmit"}, "keywords": ["senseword", "<PERSON>hub", "api", "sqlite", "fastify", "typescript", "content-management"], "author": "SenseWord Team", "license": "MIT", "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/swagger": "^9.1.0", "@fastify/swagger-ui": "^5.0.1", "dotenv": "^16.4.7", "fastify": "^5.1.0", "pino-pretty": "^13.0.0", "sqlite3": "^5.1.7", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^22.10.5", "@types/sqlite3": "^3.1.11", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "prettier": "^3.4.2", "tsx": "^4.19.2", "typescript": "^5.7.3", "vitest": "^2.1.8"}, "engines": {"node": ">=18.0.0"}}