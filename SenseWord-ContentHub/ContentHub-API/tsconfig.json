{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "types": ["node"], "lib": ["ES2022"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/database/*": ["src/database/*"], "@/dao/*": ["src/dao/*"], "@/services/*": ["src/services/*"], "@/routes/*": ["src/routes/*"], "@/schemas/*": ["src/schemas/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}