import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { TopNav } from '@/components/layout/top-nav'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import {
  BookOpen,
  CheckCircle,
  Clock,
  Database,
  TrendingUp,
  Volume2,
  Users,
  BarChart3,
  RefreshCw,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { SenseWordIcon } from '@/components/icons/senseword-icon'
import { useDefinitionStats } from '@/hooks/use-definitions'

const topNav = [
  {
    title: '生产概览',
    href: '/',
    isActive: true,
  },
  {
    title: '内容管理',
    href: '/words',
    isActive: false,
  },
  {
    title: '审核中心',
    href: '/review',
    isActive: false,
  },
]

export default function Dashboard() {
  const { data: stats, loading, error, refresh } = useDefinitionStats();

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <TopNav links={topNav} />
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <div>
            <div className='flex items-center gap-3 mb-2'>
              <SenseWordIcon className="size-8" />
              <h1 className='text-3xl font-bold tracking-tight'>SenseWord ContentHub</h1>
            </div>
            <p className='text-muted-foreground'>
              内容生产与审核中台 - 智能化管理英语词汇内容全生命周期
            </p>
          </div>
          <div className='flex items-center space-x-2'>
            <Button
              variant="outline"
              size="sm"
              onClick={refresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? '刷新中...' : '刷新数据'}
            </Button>
            <Button size="sm">
              <BarChart3 className="h-4 w-4 mr-2" />
              查看报告
            </Button>
          </div>
        </div>

        {/* 核心指标卡片 */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">加载数据中...</span>
          </div>
        )}

        {error && (
          <div className="flex items-center justify-center py-8 text-red-500">
            <AlertCircle className="h-8 w-8" />
            <span className="ml-2">加载失败: {error}</span>
          </div>
        )}

        {stats && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总单词数</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.stats.total.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  完全对齐的解析和例句
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">待审核</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.stats.pending_review.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  需要人工审核的内容
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">已批准</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.stats.approved.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  审核通过的内容
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">高优先级</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.priority.high.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  优先级 8-10 的单词
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs
          orientation='vertical'
          defaultValue='overview'
          className='space-y-4'
        >
          <div className='w-full overflow-x-auto pb-2'>
            <TabsList>
              <TabsTrigger value='overview'>系统概览</TabsTrigger>
              <TabsTrigger value='progress'>处理进度</TabsTrigger>
              <TabsTrigger value='analytics'>数据分析</TabsTrigger>
              <TabsTrigger value='recent'>最近活动</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value='overview' className='space-y-4'>
            {stats && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {/* 频率分布 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">单词频率分布</CardTitle>
                    <CardDescription>按使用频率分类的单词数量</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {Object.entries(stats.frequency).map(([freq, count]) => {
                      const percentage = ((count / stats.stats.total) * 100).toFixed(1);
                      const getVariant = (frequency: string) => {
                        switch (frequency) {
                          case 'High': return 'destructive';
                          case 'Medium': return 'default';
                          case 'Low': return 'secondary';
                          case 'Rare': return 'outline';
                          default: return 'outline';
                        }
                      };

                      return (
                        <div key={freq}>
                          <div className="flex justify-between items-center">
                            <span className="text-sm">{freq === 'High' ? '高频词' : freq === 'Medium' ? '中频词' : freq === 'Low' ? '低频词' : '罕见词'}</span>
                            <div className="flex items-center gap-2">
                              <Badge variant={getVariant(freq)}>{count.toLocaleString()}</Badge>
                              <span className="text-xs text-muted-foreground">{percentage}%</span>
                            </div>
                          </div>
                          <Progress value={parseFloat(percentage)} />
                        </div>
                      );
                    })}
                  </CardContent>
                </Card>

                {/* 优先级分布 */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">优先级分布</CardTitle>
                    <CardDescription>基于AI分析的优先级评分</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">高优先级 (8-10)</span>
                      <Badge variant="destructive">{stats.priority.high.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">中优先级 (5-7)</span>
                      <Badge variant="default">{stats.priority.medium.toLocaleString()}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">低优先级 (1-4)</span>
                      <Badge variant="secondary">{stats.priority.low.toLocaleString()}</Badge>
                    </div>

                    <div className="pt-2 border-t">
                      <div className="text-xs text-muted-foreground space-y-1">
                        <div>高优先级: {((stats.priority.high / stats.stats.total) * 100).toFixed(1)}%</div>
                        <div>中优先级: {((stats.priority.medium / stats.stats.total) * 100).toFixed(1)}%</div>
                        <div>低优先级: {((stats.priority.low / stats.stats.total) * 100).toFixed(1)}%</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

              {/* 系统状态 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">系统状态</CardTitle>
                  <CardDescription>数据库和服务状态</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      <span className="text-sm">数据库</span>
                    </div>
                    <Badge variant="default">正常</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Volume2 className="h-4 w-4" />
                      <span className="text-sm">TTS服务</span>
                    </div>
                    <Badge variant="default">运行中</Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span className="text-sm">审核队列</span>
                    </div>
                    <Badge variant="secondary">58K待处理</Badge>
                  </div>

                  <div className="text-xs text-muted-foreground">
                    最后更新: 2025-01-07 11:30:00
                  </div>
                </CardContent>
              </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value='progress' className='space-y-4'>
            <div className="grid gap-4 md:grid-cols-2">
              {/* 审核进度 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">审核进度</CardTitle>
                  <CardDescription>内容审核的整体进度</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>定义审核</span>
                      <span>3,245 / 62,815 (5.2%)</span>
                    </div>
                    <Progress value={5.2} />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>例句审核</span>
                      <span>2,890 / 62,815 (4.6%)</span>
                    </div>
                    <Progress value={4.6} />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>TTS生成</span>
                      <span>1,245 / 62,815 (2.0%)</span>
                    </div>
                    <Progress value={2.0} />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>发布准备</span>
                      <span>892 / 62,815 (1.4%)</span>
                    </div>
                    <Progress value={1.4} />
                  </div>
                </CardContent>
              </Card>

              {/* 最近活动 */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">最近活动</CardTitle>
                  <CardDescription>系统最近的操作记录</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">优先级更新完成</p>
                      <p className="text-xs text-muted-foreground">51,010个单词 • 2分钟前</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Volume2 className="h-4 w-4 text-blue-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">TTS批量生成</p>
                      <p className="text-xs text-muted-foreground">125个音频文件 • 15分钟前</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">内容审核</p>
                      <p className="text-xs text-muted-foreground">89个单词通过审核 • 1小时前</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Database className="h-4 w-4 text-purple-500" />
                    <div className="flex-1">
                      <p className="text-sm font-medium">数据库备份</p>
                      <p className="text-xs text-muted-foreground">自动备份完成 • 3小时前</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value='analytics' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle>数据分析</CardTitle>
                <CardDescription>详细的数据分析功能正在开发中</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  这里将显示详细的数据分析图表和报告。
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='recent' className='space-y-4'>
            <Card>
              <CardHeader>
                <CardTitle>最近活动</CardTitle>
                <CardDescription>系统的详细活动日志</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  这里将显示详细的活动日志和操作记录。
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </Main>
    </>
  )
}
