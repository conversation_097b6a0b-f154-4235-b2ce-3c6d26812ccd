import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Database, 
  RefreshCw, 
  Download, 
  Upload, 
  Settings, 
  BarChart3, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react'

export const Route = createFileRoute('/_authenticated/database/')({
  component: DatabasePage,
})

// 模拟数据库统计数据
const dbStats = {
  tables: {
    definitions: {
      total: 62815,
      pending_review: 58420,
      approved: 3245,
      rejected: 1150
    },
    example_sentences: {
      total: 62815,
      pending_review: 59180,
      approved: 2890,
      tts_completed: 1245,
      tts_pending: 61570
    },
    words_for_publish: {
      total: 892,
      pending_upload: 645,
      published: 247
    }
  },
  priority: {
    priority_10: 1349,
    priority_9: 4479,
    priority_8: 10157,
    priority_7: 18260,
    priority_6: 14417,
    priority_5: 2335,
    priority_0: 11818
  },
  frequency: {
    High: 14324,
    Medium: 29000,
    Low: 14117,
    Rare: 5373,
    'Medium-Low': 1
  }
}

const recentOperations = [
  {
    id: 1,
    operation: '优先级更新',
    status: 'completed',
    records: 51010,
    timestamp: '2025-01-07T10:30:00Z',
    duration: '45秒'
  },
  {
    id: 2,
    operation: '数据库初始化',
    status: 'completed',
    records: 62815,
    timestamp: '2025-01-06T15:20:00Z',
    duration: '2分钟'
  },
  {
    id: 3,
    operation: '审核状态更新',
    status: 'running',
    records: 1250,
    timestamp: '2025-01-07T11:00:00Z',
    duration: '进行中'
  }
]

function DatabasePage() {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = () => {
    setIsRefreshing(true)
    setTimeout(() => setIsRefreshing(false), 2000)
  }

  const totalWords = dbStats.tables.definitions.total
  const approvedDefinitions = dbStats.tables.definitions.approved
  const approvedExamples = dbStats.tables.example_sentences.approved
  const completedTTS = dbStats.tables.example_sentences.tts_completed
  const readyToPublish = dbStats.tables.words_for_publish.total

  const overallProgress = ((approvedDefinitions + approvedExamples + completedTTS) / (totalWords * 3)) * 100

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">数据库管理</h1>
          <p className="text-muted-foreground">
            监控和管理 SenseWord SQLite 数据库的状态和操作
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            备份
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            设置
          </Button>
        </div>
      </div>

      {/* 总体状态 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">数据库大小</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1.2 GB</div>
            <p className="text-xs text-muted-foreground">
              包含索引和元数据
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总记录数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWords.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              完全对齐的单词数据
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">整体进度</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallProgress.toFixed(1)}%</div>
            <Progress value={overallProgress} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              审核和处理进度
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">可发布</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{readyToPublish}</div>
            <p className="text-xs text-muted-foreground">
              准备发布的单词
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <Tabs defaultValue="tables" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tables">表统计</TabsTrigger>
          <TabsTrigger value="priority">优先级分布</TabsTrigger>
          <TabsTrigger value="frequency">频率分布</TabsTrigger>
          <TabsTrigger value="operations">操作记录</TabsTrigger>
        </TabsList>

        <TabsContent value="tables" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            {/* definitions表 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">definitions 表</CardTitle>
                <CardDescription>单词定义和解析数据</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">总记录数</span>
                  <Badge variant="outline">{dbStats.tables.definitions.total.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">待审核</span>
                  <Badge variant="secondary">{dbStats.tables.definitions.pending_review.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">已批准</span>
                  <Badge variant="default">{dbStats.tables.definitions.approved.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">已拒绝</span>
                  <Badge variant="destructive">{dbStats.tables.definitions.rejected.toLocaleString()}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* example_sentences表 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">example_sentences 表</CardTitle>
                <CardDescription>例句和TTS数据</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">总记录数</span>
                  <Badge variant="outline">{dbStats.tables.example_sentences.total.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">待审核</span>
                  <Badge variant="secondary">{dbStats.tables.example_sentences.pending_review.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">已批准</span>
                  <Badge variant="default">{dbStats.tables.example_sentences.approved.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">TTS完成</span>
                  <Badge variant="default">{dbStats.tables.example_sentences.tts_completed.toLocaleString()}</Badge>
                </div>
              </CardContent>
            </Card>

            {/* words_for_publish表 */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">words_for_publish 表</CardTitle>
                <CardDescription>发布准备数据</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">总记录数</span>
                  <Badge variant="outline">{dbStats.tables.words_for_publish.total.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">待上传</span>
                  <Badge variant="secondary">{dbStats.tables.words_for_publish.pending_upload.toLocaleString()}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">已发布</span>
                  <Badge variant="default">{dbStats.tables.words_for_publish.published.toLocaleString()}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="priority" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>优先级分布</CardTitle>
              <CardDescription>基于AI分析的单词优先级分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(dbStats.priority).map(([priority, count]) => {
                  const percentage = (count / totalWords) * 100
                  return (
                    <div key={priority} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>优先级 {priority.replace('priority_', '')}</span>
                        <span>{count.toLocaleString()} ({percentage.toFixed(1)}%)</span>
                      </div>
                      <Progress value={percentage} />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="frequency" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>频率分布</CardTitle>
              <CardDescription>单词使用频率的分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(dbStats.frequency).map(([freq, count]) => {
                  const percentage = (count / totalWords) * 100
                  return (
                    <div key={freq} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{freq} 频率</span>
                        <span>{count.toLocaleString()} ({percentage.toFixed(1)}%)</span>
                      </div>
                      <Progress value={percentage} />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>最近操作记录</CardTitle>
              <CardDescription>数据库的最近操作和状态</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOperations.map((op) => (
                  <div key={op.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{op.operation}</span>
                        {op.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                        {op.status === 'running' && <Clock className="h-4 w-4 text-blue-500 animate-pulse" />}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        处理 {op.records.toLocaleString()} 条记录 • {op.duration}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(op.timestamp).toLocaleString('zh-CN')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 系统状态警告 */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>系统状态</AlertTitle>
        <AlertDescription>
          数据库运行正常。最后备份时间：2025-01-07 09:00:00。
          建议定期备份重要数据。
        </AlertDescription>
      </Alert>
    </div>
  )
}
