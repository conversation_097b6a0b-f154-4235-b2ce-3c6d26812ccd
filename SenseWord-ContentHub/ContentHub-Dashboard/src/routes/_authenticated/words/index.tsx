import { createFileRoute } from '@tanstack/react-router'
import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Search, Filter, Download, Upload, Eye, Edit, CheckCircle, XCircle, Clock, Loader2, AlertCircle, RefreshCw } from 'lucide-react'
import { useDefinitions, useDefinitionActions } from '@/hooks/use-definitions'
import { WordDetailDialog } from '@/components/word-detail-dialog'
import { Main } from '@/components/layout/main'
import { definitionsApi } from '@/lib/api'
import type { AuditStatus, Frequency, Definition } from '@/lib/api'

export const Route = createFileRoute('/_authenticated/words/')({
  component: WordsPage,
})

// 模拟数据
const mockWords = [
  {
    id: 1,
    word: 'hello',
    frequency: 'High',
    priorityScore: 10,
    definitionStatus: 'approved',
    exampleStatus: 'approved',
    ttsStatus: 'completed',
    updatedAt: '2025-01-07T10:30:00Z'
  },
  {
    id: 2,
    word: 'world',
    frequency: 'High',
    priorityScore: 9,
    definitionStatus: 'pending_review',
    exampleStatus: 'pending_review',
    ttsStatus: 'pending',
    updatedAt: '2025-01-07T09:15:00Z'
  },
  {
    id: 3,
    word: 'beautiful',
    frequency: 'Medium',
    priorityScore: 8,
    definitionStatus: 'approved',
    exampleStatus: 'in_translation',
    ttsStatus: 'processing',
    updatedAt: '2025-01-07T08:45:00Z'
  },
  {
    id: 4,
    word: 'amazing',
    frequency: 'Medium',
    priorityScore: 7,
    definitionStatus: 'rejected',
    exampleStatus: 'pending_review',
    ttsStatus: 'pending',
    updatedAt: '2025-01-06T16:20:00Z'
  },
  {
    id: 5,
    word: 'wonderful',
    frequency: 'Low',
    priorityScore: 6,
    definitionStatus: 'pending_review',
    exampleStatus: 'pending_review',
    ttsStatus: 'pending',
    updatedAt: '2025-01-06T14:10:00Z'
  }
]

const getStatusBadge = (status: string) => {
  const statusConfig = {
    'pending_review': { variant: 'secondary' as const, icon: Clock, label: '待审核' },
    'approved': { variant: 'default' as const, icon: CheckCircle, label: '已批准' },
    'rejected': { variant: 'destructive' as const, icon: XCircle, label: '已拒绝' },
    'in_translation': { variant: 'outline' as const, icon: Clock, label: '翻译中' },
    'processing': { variant: 'outline' as const, icon: Clock, label: '处理中' },
    'completed': { variant: 'default' as const, icon: CheckCircle, label: '已完成' },
    'pending': { variant: 'secondary' as const, icon: Clock, label: '待处理' },
    'failed': { variant: 'destructive' as const, icon: XCircle, label: '失败' }
  }
  
  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['pending_review']
  const Icon = config.icon
  
  return (
    <Badge variant={config.variant} className="flex items-center gap-1">
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  )
}

const getFrequencyBadge = (frequency: string) => {
  const colors = {
    'High': 'bg-red-100 text-red-800',
    'Medium': 'bg-yellow-100 text-yellow-800',
    'Low': 'bg-blue-100 text-blue-800',
    'Rare': 'bg-gray-100 text-gray-800'
  }
  
  return (
    <Badge className={colors[frequency as keyof typeof colors] || colors['Medium']}>
      {frequency}
    </Badge>
  )
}

function WordsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [frequencyFilter, setFrequencyFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [selectedWord, setSelectedWord] = useState<Definition | null>(null)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)
  const [ttsStats, setTtsStats] = useState<{ [key: string]: number } | null>(null)
  const [publishStats, setPublishStats] = useState<{ [key: string]: number } | null>(null)

  // 使用真实数据
  const {
    data: words,
    loading,
    error,
    pagination,
    updateFilters,
    updatePagination,
    refresh
  } = useDefinitions(
    {
      frequency: frequencyFilter !== 'all' ? frequencyFilter as Frequency : undefined,
      auditStatus: statusFilter !== 'all' ? statusFilter as AuditStatus : undefined,
    },
    {
      search: searchTerm || undefined,
      limit: 20,
      page: 1,
    }
  );

  const { updateAuditStatus, loading: actionLoading } = useDefinitionActions();

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    updatePagination({ search: value || undefined, page: 1 });
  };

  // 处理筛选
  const handleFrequencyFilter = (value: string) => {
    setFrequencyFilter(value);
    updateFilters({
      frequency: value !== 'all' ? value as Frequency : undefined
    });
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    updateFilters({
      auditStatus: value !== 'all' ? value as AuditStatus : undefined
    });
  };

  // 处理状态更新
  const handleStatusUpdate = async (id: number, newStatus: AuditStatus) => {
    const success = await updateAuditStatus(id, newStatus);
    if (success) {
      refresh(); // 刷新数据
    }
  };

  // 处理查看详情
  const handleViewDetail = (word: Definition) => {
    setSelectedWord(word);
    setDetailDialogOpen(true);
  };

  // 获取TTS和发布统计
  const fetchAdditionalStats = async () => {
    try {
      const [ttsResponse, publishResponse] = await Promise.all([
        definitionsApi.getTtsStats(),
        definitionsApi.getPublishStats()
      ]);

      if (ttsResponse.success) {
        setTtsStats(ttsResponse.data);
      }

      if (publishResponse.success) {
        setPublishStats(publishResponse.data);
      }
    } catch (error) {
      console.error('获取额外统计信息失败:', error);
    }
  };

  // 在组件加载时获取额外统计
  React.useEffect(() => {
    fetchAdditionalStats();
  }, []);

  return (
    <Main>
      <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">单词管理</h1>
          <p className="text-muted-foreground">
            管理 SenseWord 词库中的单词定义、例句和音频资产
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? '刷新中...' : '刷新'}
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            导入
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前显示</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{words.length}</div>
            <p className="text-xs text-muted-foreground">
              共 {pagination.total.toLocaleString()} 个单词
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前页</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.page}</div>
            <p className="text-xs text-muted-foreground">
              共 {pagination.totalPages} 页
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成TTS</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {ttsStats && ttsStats.completed != null ? ttsStats.completed.toLocaleString() : '-'}
            </div>
            <p className="text-xs text-muted-foreground">
              待处理: {ttsStats && ttsStats.pending != null ? ttsStats.pending.toLocaleString() : '-'}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已发布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {publishStats && publishStats.published != null ? publishStats.published.toLocaleString() : '-'}
            </div>
            <p className="text-xs text-muted-foreground">
              待发布: {publishStats && publishStats.pending_upload != null ? publishStats.pending_upload.toLocaleString() : '-'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 筛选和搜索 */}
      <Card>
        <CardHeader>
          <CardTitle>筛选和搜索</CardTitle>
          <CardDescription>
            使用下面的工具来查找和筛选单词
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索单词..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={frequencyFilter} onValueChange={handleFrequencyFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="频率筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有频率</SelectItem>
                <SelectItem value="High">高频词</SelectItem>
                <SelectItem value="Medium">中频词</SelectItem>
                <SelectItem value="Low">低频词</SelectItem>
                <SelectItem value="Rare">罕见词</SelectItem>
                <SelectItem value="Medium-Low">中低频词</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="pending_review">待审核</SelectItem>
                <SelectItem value="approved">已批准</SelectItem>
                <SelectItem value="rejected">已拒绝</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 单词列表 */}
      <Card>
        <CardHeader>
          <CardTitle>单词列表</CardTitle>
          <CardDescription>
            显示 {words.length} 个单词，共 {pagination.total.toLocaleString()} 个
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载数据中...</span>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-8 text-red-500">
              <AlertCircle className="h-8 w-8" />
              <span className="ml-2">加载失败: {error}</span>
            </div>
          )}

          {!loading && !error && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>单词</TableHead>
                  <TableHead>频率</TableHead>
                  <TableHead>优先级</TableHead>
                  <TableHead>审核状态</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {words.map((word) => (
                  <TableRow key={word.id}>
                    <TableCell className="font-medium">{word.word}</TableCell>
                    <TableCell>{getFrequencyBadge(word.frequency || 'Medium')}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{word.priorityScore}</Badge>
                    </TableCell>
                    <TableCell>{getStatusBadge(word.auditStatus)}</TableCell>
                    <TableCell>
                      {new Date(word.updatedAt).toLocaleDateString('zh-CN')}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusUpdate(word.id, 'approved')}
                          disabled={actionLoading || word.auditStatus === 'approved'}
                          className="flex items-center gap-1"
                        >
                          <CheckCircle className="h-4 w-4" />
                          <span>批准</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleStatusUpdate(word.id, 'rejected')}
                          disabled={actionLoading || word.auditStatus === 'rejected'}
                          className="flex items-center gap-1"
                        >
                          <XCircle className="h-4 w-4" />
                          <span>拒绝</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetail(word)}
                          className="flex items-center gap-1"
                        >
                          <Eye className="h-4 w-4" />
                          <span>详情</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页控件 */}
          {!loading && !error && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                第 {pagination.page} 页，共 {pagination.totalPages} 页
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page <= 1}
                  onClick={() => updatePagination({ page: pagination.page - 1 })}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page >= pagination.totalPages}
                  onClick={() => updatePagination({ page: pagination.page + 1 })}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 单词详情对话框 */}
      <WordDetailDialog
        word={selectedWord}
        open={detailDialogOpen}
        onOpenChange={setDetailDialogOpen}
        onStatusUpdate={handleStatusUpdate}
      />
      </div>
    </Main>
  )
}
