/**
 * 定义数据管理 Hook
 */
import { useState, useEffect, useCallback } from 'react';
import { definitionsApi, type Definition, type FilterParams, type PaginationParams, type DatabaseStats } from '@/lib/api';

// 定义列表 Hook
export function useDefinitions(
  initialFilters: FilterParams = {},
  initialPagination: PaginationParams = {}
) {
  const [data, setData] = useState<Definition[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  const [filters, setFilters] = useState<FilterParams>(initialFilters);
  const [paginationParams, setPaginationParams] = useState<PaginationParams>({
    page: 1,
    limit: 20,
    ...initialPagination,
  });

  const fetchDefinitions = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await definitionsApi.getDefinitions(filters, paginationParams);
      
      if (response.success && response.data) {
        setData(response.data);
        if (response.pagination) {
          setPagination(response.pagination);
        }
      } else {
        throw new Error(response.error || '获取数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取定义列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, paginationParams]);

  // 更新筛选条件
  const updateFilters = useCallback((newFilters: Partial<FilterParams>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPaginationParams(prev => ({ ...prev, page: 1 })); // 重置到第一页
  }, []);

  // 更新分页参数
  const updatePagination = useCallback((newPagination: Partial<PaginationParams>) => {
    setPaginationParams(prev => ({ ...prev, ...newPagination }));
  }, []);

  // 刷新数据
  const refresh = useCallback(() => {
    fetchDefinitions();
  }, [fetchDefinitions]);

  // 初始加载和依赖更新时重新获取数据
  useEffect(() => {
    fetchDefinitions();
  }, [fetchDefinitions]);

  return {
    data,
    loading,
    error,
    pagination,
    filters,
    paginationParams,
    updateFilters,
    updatePagination,
    refresh,
  };
}

// 单个定义 Hook
export function useDefinition(id: number | null) {
  const [data, setData] = useState<Definition | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDefinition = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await definitionsApi.getDefinitionById(id);
      
      if (response.success && response.data) {
        setData(response.data);
      } else {
        throw new Error(response.error || '获取定义失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取定义失败:', err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchDefinition();
  }, [fetchDefinition]);

  return {
    data,
    loading,
    error,
    refresh: fetchDefinition,
  };
}

// 统计数据 Hook
export function useDefinitionStats() {
  const [data, setData] = useState<DatabaseStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await definitionsApi.getStats();
      
      if (response.success && response.data) {
        setData(response.data);
      } else {
        throw new Error(response.error || '获取统计数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('获取统计数据失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    data,
    loading,
    error,
    refresh: fetchStats,
  };
}

// 搜索 Hook
export function useWordSearch() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (query: string, limit: number = 10) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await definitionsApi.searchWords(query, limit);
      
      if (response.success && response.data) {
        setResults(response.data);
      } else {
        throw new Error(response.error || '搜索失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('搜索失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    search,
    clearResults,
  };
}

// 状态更新 Hook
export function useDefinitionActions() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateAuditStatus = useCallback(async (id: number, auditStatus: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await definitionsApi.updateAuditStatus(id, auditStatus);
      
      if (!response.success) {
        throw new Error(response.error || '更新状态失败');
      }

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '未知错误';
      setError(errorMessage);
      console.error('更新状态失败:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    updateAuditStatus,
  };
}
