/**
 * 单词详情对话框组件
 */
import React, { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Star, CheckCircle, XCircle, Loader2, AlertCircle } from 'lucide-react'
import { definitionsApi } from '@/lib/api'
import type { Definition, AuditStatus } from '@/lib/api'

interface WordDetailDialogProps {
  word: Definition | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onStatusUpdate?: (id: number, status: AuditStatus) => void
}

export function WordDetailDialog({ word, open, onOpenChange, onStatusUpdate }: WordDetailDialogProps) {
  const [exampleData, setExampleData] = useState<any>(null)
  const [exampleLoading, setExampleLoading] = useState(false)
  const [exampleError, setExampleError] = useState<string | null>(null)
  const [definitionComment, setDefinitionComment] = useState('')
  const [exampleComment, setExampleComment] = useState('')
  const [definitionAction, setDefinitionAction] = useState<'approve' | 'reject' | null>(null)
  const [exampleAction, setExampleAction] = useState<'approve' | 'reject' | null>(null)
  const [actionLoading, setActionLoading] = useState(false)

  // 获取例句数据
  const fetchExampleData = async () => {
    if (!word?.word) return

    setExampleLoading(true)
    setExampleError(null)

    try {
      const response = await definitionsApi.getExamplesByWord(word.word)
      if (response.success) {
        setExampleData(response.data)
      } else {
        setExampleError(response.error || '获取例句数据失败')
      }
    } catch (error) {
      setExampleError(error instanceof Error ? error.message : '获取例句数据失败')
    } finally {
      setExampleLoading(false)
    }
  }

  // 处理定义审核操作
  const handleDefinitionReview = async () => {
    if (!definitionAction || !word || !onStatusUpdate) return

    setActionLoading(true)

    try {
      const newStatus: AuditStatus = definitionAction === 'approve' ? 'approved' : 'rejected'
      await onStatusUpdate(word.id, newStatus)

      // 重置状态
      setDefinitionAction(null)
      setDefinitionComment('')

      // 关闭对话框
      onOpenChange(false)
    } catch (error) {
      console.error('定义审核操作失败:', error)
    } finally {
      setActionLoading(false)
    }
  }

  // 处理例句审核操作
  const handleExampleReview = async () => {
    if (!exampleAction || !exampleData || !word) return

    setActionLoading(true)

    try {
      const newStatus: AuditStatus = exampleAction === 'approve' ? 'approved' : 'rejected'
      await definitionsApi.updateExampleAuditStatus(word.word, newStatus)

      // 重置状态
      setExampleAction(null)
      setExampleComment('')

      // 刷新例句数据
      await fetchExampleData()
    } catch (error) {
      console.error('例句审核操作失败:', error)
    } finally {
      setActionLoading(false)
    }
  }

  // 当单词变化时获取例句数据
  useEffect(() => {
    if (word && open) {
      fetchExampleData()
      // 重置审核状态
      setDefinitionAction(null)
      setExampleAction(null)
      setDefinitionComment('')
      setExampleComment('')
    }
  }, [word, open])

  const formatJsonContent = (jsonString: string | undefined) => {
    if (!jsonString) return '暂无数据'

    try {
      const parsed = JSON.parse(jsonString)
      return JSON.stringify(parsed, null, 2)
    } catch (error) {
      return `解析错误: ${jsonString}`
    }
  }

  if (!word) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[65vw] sm:max-w-[65vw] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className="text-2xl">{word.word}</span>
            <Badge variant="outline">{word.frequency || 'Medium'}</Badge>
            <Badge variant="outline">
              <Star className="h-3 w-3 mr-1" />
              {word.priorityScore}
            </Badge>
            <Badge variant={
              word.auditStatus === 'approved' ? 'default' :
              word.auditStatus === 'rejected' ? 'destructive' :
              word.auditStatus === 'in_translation' ? 'secondary' :
              'outline'
            }>
              {word.auditStatus}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            语言: {word.learningLanguage} → {word.scaffoldingLanguage} |
            更新时间: {new Date(word.updatedAt).toLocaleString('zh-CN')}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="definition" className="w-full h-[calc(90vh-120px)]">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="definition">定义内容</TabsTrigger>
            <TabsTrigger value="examples">例句内容</TabsTrigger>
            <TabsTrigger value="review">内容审核</TabsTrigger>
          </TabsList>

          <TabsContent value="definition" className="mt-4 h-[calc(100%-60px)]">
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">定义内容 (JSON)</CardTitle>
                <CardDescription>
                  单词的详细定义、用法说明和语境解释
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <ScrollArea className="h-full w-full">
                  <div className="bg-muted/50 rounded-lg p-4 border">
                    <pre className="text-sm font-mono whitespace-pre-wrap">
                      {formatJsonContent(word.definitionJson)}
                    </pre>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="examples" className="mt-4 h-[calc(100%-60px)]">
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">例句内容 (JSON)</CardTitle>
                <CardDescription>
                  单词的使用例句、发音符号和语境示例
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                {exampleLoading && (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">加载例句中...</span>
                  </div>
                )}

                {exampleError && (
                  <div className="flex items-center justify-center h-full text-red-500">
                    <AlertCircle className="h-6 w-6" />
                    <span className="ml-2">{exampleError}</span>
                  </div>
                )}

                {!exampleLoading && !exampleError && (
                  <ScrollArea className="h-full w-full">
                    <div className="bg-muted/50 rounded-lg p-4 border">
                      <pre className="text-sm font-mono whitespace-pre-wrap">
                        {exampleData ? formatJsonContent(exampleData.examplesJson) : '暂无例句数据'}
                      </pre>
                    </div>
                  </ScrollArea>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="review" className="mt-4 h-[calc(100%-60px)]">
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">内容审核</CardTitle>
                <CardDescription>
                  分别对单词定义和例句进行质量审核
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 overflow-hidden">
                <ScrollArea className="h-full w-full">
                  <div className="space-y-8">
                    {/* 定义审核区域 */}
                    <div className="space-y-4 p-4 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold">定义内容审核</h3>
                        <Badge variant={
                          word.auditStatus === 'approved' ? 'default' :
                          word.auditStatus === 'rejected' ? 'destructive' :
                          word.auditStatus === 'in_translation' ? 'secondary' :
                          'outline'
                        }>
                          {word.auditStatus}
                        </Badge>
                      </div>

                      {/* 定义审核决策 */}
                      <div className="space-y-3">
                        <h4 className="font-medium">审核决策</h4>
                        <div className="space-y-2">
                          <Button
                            variant={definitionAction === 'approve' ? 'default' : 'outline'}
                            className="w-full justify-start"
                            onClick={() => setDefinitionAction('approve')}
                            disabled={word.auditStatus === 'approved'}
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            批准定义
                          </Button>
                          <Button
                            variant={definitionAction === 'reject' ? 'destructive' : 'outline'}
                            className="w-full justify-start"
                            onClick={() => setDefinitionAction('reject')}
                            disabled={word.auditStatus === 'rejected'}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            拒绝定义
                          </Button>
                        </div>
                      </div>

                      {/* 定义审核评论 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">审核评论</h4>
                        <Textarea
                          placeholder="请输入对定义内容的审核意见..."
                          value={definitionComment}
                          onChange={(e) => setDefinitionComment(e.target.value)}
                          className="min-h-[80px]"
                        />
                      </div>

                      {/* 定义提交按钮 */}
                      <Button
                        className="w-full"
                        onClick={handleDefinitionReview}
                        disabled={!definitionAction || actionLoading}
                      >
                        {actionLoading ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            提交中...
                          </>
                        ) : (
                          '提交定义审核'
                        )}
                      </Button>
                    </div>

                    {/* 例句审核区域 */}
                    {exampleData && (
                      <div className="space-y-4 p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-semibold">例句内容审核</h3>
                          <Badge variant={
                            exampleData.auditStatus === 'approved' ? 'default' :
                            exampleData.auditStatus === 'rejected' ? 'destructive' :
                            exampleData.auditStatus === 'in_translation' ? 'secondary' :
                            'outline'
                          }>
                            {exampleData.auditStatus}
                          </Badge>
                        </div>

                        {/* 例句审核决策 */}
                        <div className="space-y-3">
                          <h4 className="font-medium">审核决策</h4>
                          <div className="space-y-2">
                            <Button
                              variant={exampleAction === 'approve' ? 'default' : 'outline'}
                              className="w-full justify-start"
                              onClick={() => setExampleAction('approve')}
                              disabled={exampleData.auditStatus === 'approved'}
                            >
                              <CheckCircle className="h-4 w-4 mr-2" />
                              批准例句
                            </Button>
                            <Button
                              variant={exampleAction === 'reject' ? 'destructive' : 'outline'}
                              className="w-full justify-start"
                              onClick={() => setExampleAction('reject')}
                              disabled={exampleData.auditStatus === 'rejected'}
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              拒绝例句
                            </Button>
                          </div>
                        </div>

                        {/* 例句审核评论 */}
                        <div className="space-y-2">
                          <h4 className="font-medium">审核评论</h4>
                          <Textarea
                            placeholder="请输入对例句内容的审核意见..."
                            value={exampleComment}
                            onChange={(e) => setExampleComment(e.target.value)}
                            className="min-h-[80px]"
                          />
                        </div>

                        {/* 例句提交按钮 */}
                        <Button
                          className="w-full"
                          onClick={handleExampleReview}
                          disabled={!exampleAction || actionLoading}
                        >
                          {actionLoading ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              提交中...
                            </>
                          ) : (
                            '提交例句审核'
                          )}
                        </Button>
                      </div>
                    )}

                    {/* 例句加载状态 */}
                    {!exampleData && !exampleLoading && !exampleError && (
                      <div className="p-4 border rounded-lg text-center text-muted-foreground">
                        暂无例句数据可供审核
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
