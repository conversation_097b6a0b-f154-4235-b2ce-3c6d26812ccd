import {
  IconBarrierBlock,
  IconBrowserCheck,
  IconBug,
  IconChecklist,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconLockAccess,
  IconMessages,
  IconNotification,
  IconPackages,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
} from '@tabler/icons-react'
import {
  AudioWaveform,
  Command,
  GalleryVerticalEnd,
  Database,
  BookOpen,
  CheckCircle,
  Volume2,
  Upload,
  BarChart3
} from 'lucide-react'
import { ClerkLogo } from '@/assets/clerk-logo'
import { SenseWordIcon } from '@/components/icons/senseword-icon'
import { type SidebarData } from '../types'

export const sidebarData: SidebarData = {
  user: {
    name: 'ContentHub Admin',
    email: '<EMAIL>',
    avatar: '/images/favicon_me.JPG',
  },
  teams: [
    {
      name: 'ContentHub',
      logo: SenseWordIcon,
      plan: '内容生产中台',
    },
    {
      name: 'Production',
      logo: GalleryVerticalEnd,
      plan: '生产环境',
    },
    {
      name: 'Staging',
      logo: AudioWaveform,
      plan: '预发布环境',
    },
  ],
  navGroups: [
    {
      title: '内容管理',
      items: [
        {
          title: '仪表板',
          url: '/',
          icon: IconLayoutDashboard,
        },
        {
          title: '单词管理',
          url: '/words',
          badge: '62K',
          icon: BookOpen,
        },
        {
          title: 'TTS管理',
          url: '/tts',
          icon: Volume2,
        },
        {
          title: '数据导入',
          url: '/import',
          icon: Upload,
        },
      ],
    },
    {
      title: '系统管理',
      items: [
        {
          title: '数据库管理',
          url: '/database',
          icon: Database,
        },
        {
          title: '统计分析',
          url: '/analytics',
          icon: BarChart3,
        },
        {
          title: '用户管理',
          url: '/users',
          icon: IconUsers,
        },
        {
          title: '系统监控',
          icon: IconBug,
          items: [
            {
              title: '错误日志',
              url: '/logs/errors',
              icon: IconError404,
            },
            {
              title: '性能监控',
              url: '/logs/performance',
              icon: IconServerOff,
            },
            {
              title: '系统状态',
              url: '/logs/status',
              icon: IconBarrierBlock,
            },
          ],
        },
      ],
    },
    {
      title: '其他',
      items: [
        {
          title: '系统设置',
          icon: IconSettings,
          items: [
            {
              title: '个人资料',
              url: '/settings',
              icon: IconUserCog,
            },
            {
              title: '数据库配置',
              url: '/settings/database',
              icon: IconTool,
            },
            {
              title: '界面设置',
              url: '/settings/appearance',
              icon: IconPalette,
            },
            {
              title: '通知设置',
              url: '/settings/notifications',
              icon: IconNotification,
            },
            {
              title: 'API配置',
              url: '/settings/api',
              icon: IconBrowserCheck,
            },
          ],
        },
        {
          title: '帮助文档',
          url: '/help-center',
          icon: IconHelp,
        },
      ],
    },
  ],
}
