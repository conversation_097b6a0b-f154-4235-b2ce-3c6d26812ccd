# SenseWord ContentHub - 内容生产与审核中台

## 🎯 项目概述

SenseWord ContentHub 是一个智能化的英语词汇内容生产与审核中台，专注于管理英语单词的深度解析、例句资产和音频生成的全生命周期。

### 核心特性
- 📊 **62,815个单词** 的统一管理和智能审核
- 🎨 **现代化界面** 基于 React + TypeScript + shadcn/ui
- ⚡ **高性能API** 使用 Fastify + SQLite 构建
- 🔄 **状态驱动** 完整的审核和发布工作流
- 📖 **完整文档** 职责单一、数字编号的文档体系

## 🚀 快速开始

### 本地开发环境

#### 1. 启动后端服务
```bash
cd ContentHub-API
npm install
npm run dev
# 服务地址: http://localhost:3000
# API文档: http://localhost:3000/docs
```

#### 2. 启动前端服务
```bash
cd ContentHub-Dashboard
pnpm install
pnpm dev
# 服务地址: http://localhost:5173
```

### 访问地址
- **前端管理界面**: http://localhost:5173/
- **后端API服务**: http://localhost:3000/
- **API接口文档**: http://localhost:3000/docs
- **健康检查**: http://localhost:3000/health

## 📚 完整文档

我们提供了职责单一、数字编号的完整文档体系，便于查找和使用：

### � [文档中心](./docs/README.md)

| 文档 | 职责 | 适用人群 |
|------|------|----------|
| **[01-项目概览](./docs/01-项目概览.md)** | 项目定位、核心功能、技术架构 | 所有人员 |
| **[02-技术架构](./docs/02-技术架构.md)** | 系统分层、技术栈、设计原则 | 技术人员 |
| **[03-数据库设计](./docs/03-数据库设计.md)** | 数据模型、状态流转、性能优化 | 后端开发 |
| **[04-API接口文档](./docs/04-API接口文档.md)** | 接口规范、参数说明、测试示例 | 前后端开发 |
| **[05-部署指南](./docs/05-部署指南.md)** | 环境搭建、部署流程、CI/CD | 运维人员 |
| **[06-开发指南](./docs/06-开发指南.md)** | 开发规范、工作流程、最佳实践 | 开发人员 |
| **[07-故障排除](./docs/07-故障排除.md)** | 问题诊断、性能优化、调试工具 | 技术支持 |
| **[08-更新日志](./docs/08-更新日志.md)** | 版本历史、功能更新、发布记录 | 项目管理 |

### 🎯 快速导航
- **新手入门** → [01-项目概览](./docs/01-项目概览.md)
- **环境搭建** → [05-部署指南](./docs/05-部署指南.md)
- **API调用** → [04-API接口文档](./docs/04-API接口文档.md)
- **参与开发** → [06-开发指南](./docs/06-开发指南.md)
- **问题排查** → [07-故障排除](./docs/07-故障排除.md)

## 🏗️ 技术架构

### 系统分层
```
┌─────────────────────────────────────────┐
│           用户界面层 (UI Layer)           │
│    ContentHub-Dashboard (React)        │
└─────────────────────────────────────────┘
                    │ HTTP/REST
┌─────────────────────────────────────────┐
│          业务逻辑层 (API Layer)          │
│     ContentHub-API (Fastify)           │
└─────────────────────────────────────────┘
                    │ SQL
┌─────────────────────────────────────────┐
│          数据存储层 (Data Layer)         │
│        SQLite Database                 │
└─────────────────────────────────────────┘
```

### 核心数据
- **📊 词汇规模**: 62,815个英语单词
- **🗄️ 三表架构**: definitions, example_sentences, words_for_publish
- **🔄 状态管理**: 多层次的审核和发布工作流
- **🎯 优先级系统**: 1-10级优先级分布

## 📞 技术支持

- **项目负责人**: SenseWord Team
- **技术支持**: <EMAIL>
- **问题反馈**: GitHub Issues
- **文档维护**: 开发团队

## 📋 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2025-01-07
- **维护状态**: 活跃开发中
- **更新记录**: [查看更新日志](./docs/08-更新日志.md)

---

**SenseWord ContentHub** - 智能化英语词汇内容生产与审核中台
