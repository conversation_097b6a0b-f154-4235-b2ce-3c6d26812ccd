# 03-数据库设计

## 🗄️ 数据库概览

### 基本信息
- **数据库类型**: SQLite 3
- **文件位置**: `senseword-content-factory/01-EN/SQLite/senseword_content.db`
- **文件大小**: ~674MB
- **总记录数**: 62,815个单词

### 设计原则
- **三表分离**: 按功能职责分离数据
- **状态驱动**: 基于状态机的工作流设计
- **版本控制**: 支持内容版本管理
- **扩展性**: 支持多语言扩展

## 📊 表结构设计

### 1. definitions 表 - 单词定义

**职责**: 存储单词的核心定义和深度解析

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 自增主键 |
| word | TEXT | NOT NULL | 单词 |
| learningLanguage | TEXT | NOT NULL | 学习语言 (默认: en) |
| scaffoldingLanguage | TEXT | NOT NULL | 脚手架语言 (默认: zh) |
| priorityScore | INTEGER | NOT NULL | 优先级分数 (0-10) |
| frequency | TEXT | NULL | 频率等级 (High/Medium/Low/Rare) |
| definitionJson | TEXT | NOT NULL | 定义JSON数据 |
| auditStatus | TEXT | NOT NULL | 审核状态 |
| updatedAt | TEXT | NOT NULL | 更新时间 |

**索引设计**:
```sql
CREATE INDEX idx_definitions_word ON definitions(word);
CREATE INDEX idx_definitions_priority ON definitions(priorityScore);
CREATE INDEX idx_definitions_audit ON definitions(auditStatus);
CREATE INDEX idx_definitions_frequency ON definitions(frequency);
```

**definitionJson 结构示例**:
```json
{
  "word": "example",
  "pronunciation": "/ɪɡˈzæmpəl/",
  "definitions": [
    {
      "partOfSpeech": "noun",
      "definition": "a thing characteristic of its kind",
      "examples": ["for example", "set an example"]
    }
  ],
  "etymology": "...",
  "usage": "..."
}
```

### 2. example_sentences 表 - 例句管理

**职责**: 存储单词的使用例句和音标信息

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 自增主键 |
| word | TEXT | NOT NULL | 单词 |
| learningLanguage | TEXT | NOT NULL | 学习语言 |
| scaffoldingLanguage | TEXT | NOT NULL | 脚手架语言 |
| examplesJson | TEXT | NOT NULL | 例句JSON数据 |
| auditStatus | TEXT | NOT NULL | 审核状态 |
| ttsStatus | TEXT | NOT NULL | TTS处理状态 |
| updatedAt | TEXT | NOT NULL | 更新时间 |

**索引设计**:
```sql
CREATE INDEX idx_examples_word ON example_sentences(word);
CREATE INDEX idx_examples_audit ON example_sentences(auditStatus);
CREATE INDEX idx_examples_tts ON example_sentences(ttsStatus);
```

**examplesJson 结构示例**:
```json
{
  "word": "example",
  "phonetics": "/ɪɡˈzæmpəl/",
  "sentences": [
    {
      "english": "This is a good example.",
      "chinese": "这是一个好例子。",
      "audioUrl": "",
      "difficulty": "basic"
    }
  ],
  "phrases": [
    {
      "phrase": "for example",
      "meaning": "例如",
      "usage": "..."
    }
  ]
}
```

### 3. words_for_publish 表 - 发布内容

**职责**: 存储最终合并后的发布内容

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | INTEGER | PRIMARY KEY | 自增主键 |
| word | TEXT | NOT NULL | 单词 |
| learningLanguage | TEXT | NOT NULL | 学习语言 |
| scaffoldingLanguage | TEXT | NOT NULL | 脚手架语言 |
| contentJson | TEXT | NOT NULL | 完整内容JSON |
| publishStatus | TEXT | NOT NULL | 发布状态 |
| contentVersion | TEXT | NOT NULL | 内容版本 |
| updatedAt | TEXT | NOT NULL | 更新时间 |

**索引设计**:
```sql
CREATE INDEX idx_publish_word ON words_for_publish(word);
CREATE INDEX idx_publish_status ON words_for_publish(publishStatus);
CREATE INDEX idx_publish_version ON words_for_publish(contentVersion);
```

**contentJson 结构示例**:
```json
{
  "word": "example",
  "definition": { /* 来自 definitions 表 */ },
  "examples": { /* 来自 example_sentences 表 */ },
  "metadata": {
    "version": "v1.0",
    "lastUpdated": "2025-01-07",
    "priority": 8,
    "frequency": "High"
  }
}
```

## 🔄 状态流转设计

### 审核状态流转 (auditStatus)
```
pending_review → approved → (可选) in_translation
              ↘ rejected
```

### TTS状态流转 (ttsStatus)
```
pending → processing → completed
        ↘ failed
        ↘ skipped
```

### 发布状态流转 (publishStatus)
```
pending_upload → uploading → published
               ↘ failed
```

## 📈 数据统计

### 当前数据分布

#### 按优先级分布
- **Priority 10**: 1,349个单词 (最高优先级)
- **Priority 9-8**: 14,636个单词 (高优先级)
- **Priority 7-6**: 32,677个单词 (中等优先级)
- **Priority 5-1**: 2,335个单词 (低优先级)

#### 按频率分布
- **High**: 高频词汇
- **Medium**: 中频词汇
- **Low**: 低频词汇
- **Rare**: 罕见词汇

#### 按状态分布
- **待审核**: pending_review
- **已批准**: approved
- **已拒绝**: rejected
- **翻译中**: in_translation

## 🔍 查询优化

### 常用查询模式

#### 1. 分页查询
```sql
SELECT * FROM definitions 
WHERE auditStatus = 'pending_review'
ORDER BY priorityScore DESC 
LIMIT 20 OFFSET 0;
```

#### 2. 统计查询
```sql
SELECT auditStatus, COUNT(*) as count 
FROM definitions 
GROUP BY auditStatus;
```

#### 3. 关联查询
```sql
SELECT d.word, d.priorityScore, e.ttsStatus
FROM definitions d
LEFT JOIN example_sentences e ON d.word = e.word
WHERE d.auditStatus = 'approved';
```

### 性能优化建议

#### 索引策略
- **单字段索引**: 高频查询字段
- **复合索引**: 多条件查询
- **覆盖索引**: 减少回表查询

#### 查询优化
- **LIMIT使用**: 避免全表扫描
- **WHERE条件**: 利用索引过滤
- **ORDER BY**: 配合索引排序

## 🛠️ 维护操作

### 数据备份
```bash
# 备份数据库
cp senseword_content.db senseword_content_backup_$(date +%Y%m%d).db

# 压缩备份
gzip senseword_content_backup_*.db
```

### 数据完整性检查
```sql
-- 检查数据一致性
PRAGMA integrity_check;

-- 检查外键约束
PRAGMA foreign_key_check;

-- 分析表统计信息
ANALYZE;
```

### 性能监控
```sql
-- 查看查询计划
EXPLAIN QUERY PLAN 
SELECT * FROM definitions WHERE word = 'example';

-- 查看索引使用情况
.schema definitions
```

## 🔧 数据库配置

### SQLite 配置
```sql
-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 设置WAL模式
PRAGMA journal_mode = WAL;

-- 设置同步模式
PRAGMA synchronous = NORMAL;

-- 设置缓存大小
PRAGMA cache_size = 10000;
```

### 连接配置
```typescript
// TypeScript 连接配置
const dbConfig = {
  filename: 'senseword_content.db',
  mode: sqlite3.OPEN_READWRITE,
  verbose: console.log
};
```

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [02-技术架构](./02-技术架构.md)  
**下一篇**: [04-API接口文档](./04-API接口文档.md)
