# 06-开发指南

## 👨‍💻 开发环境搭建

### 系统要求
- **操作系统**: macOS / Linux / Windows
- **Node.js**: 18.0.0 或更高版本
- **包管理器**: npm (后端) / pnpm (前端)
- **编辑器**: VS Code (推荐)
- **Git**: 版本控制

### VS Code 扩展推荐
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 项目结构
```
SenseWord-ContentHub/
├── ContentHub-API/          # 后端 API 服务
│   ├── src/
│   │   ├── database/        # 数据库连接
│   │   ├── dao/            # 数据访问层
│   │   ├── services/       # 业务逻辑层
│   │   ├── routes/         # 路由层
│   │   ├── schemas/        # 验证模式
│   │   ├── types/          # 类型定义
│   │   └── server.ts       # 服务器入口
│   ├── dist/               # 编译输出
│   ├── package.json
│   └── tsconfig.json
├── ContentHub-Dashboard/    # 前端管理界面
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── features/       # 功能模块
│   │   ├── routes/         # 路由配置
│   │   ├── lib/           # 工具库
│   │   └── types/         # 类型定义
│   ├── public/            # 静态资源
│   ├── package.json
│   └── vite.config.ts
└── docs/                  # 项目文档
```

## 🔧 开发工作流

### 1. 分支管理

#### Git Flow 策略
```
main                 # 生产分支
├── develop          # 开发分支
├── feature/*        # 功能分支
├── hotfix/*         # 热修复分支
└── release/*        # 发布分支
```

#### 分支命名规范
```bash
# 功能开发
feature/add-user-management
feature/improve-search-performance

# 问题修复
bugfix/fix-pagination-issue
hotfix/fix-critical-security-issue

# 发布准备
release/v1.1.0
```

### 2. 提交规范

#### Commit Message 格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- **feat**: 新功能
- **fix**: 修复问题
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建工具、依赖更新

#### 示例
```bash
feat(api): 添加单词搜索接口

- 实现基于关键词的模糊搜索
- 支持按优先级和频率排序
- 添加搜索结果缓存机制

Closes #123
```

### 3. 代码规范

#### TypeScript 配置
```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitReturns": true,
    "exactOptionalPropertyTypes": false
  }
}
```

#### ESLint 配置
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "prefer-const": "error"
  }
}
```

#### Prettier 配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2
}
```

## 🏗️ 后端开发

### 1. 项目结构

#### 分层架构
```
Controller (Routes) → Service → DAO → Database
```

#### 文件组织
```typescript
// src/routes/definitions.ts - 路由层
export default async function definitionsRoutes(fastify: FastifyInstance) {
  fastify.get('/', async (request, reply) => {
    const result = await definitionsService.getDefinitions();
    return reply.send({ success: true, data: result });
  });
}

// src/services/definitionsService.ts - 业务逻辑层
export class DefinitionsService {
  async getDefinitions(filters: FilterParams): Promise<Definition[]> {
    return await definitionsDao.getDefinitions(filters);
  }
}

// src/dao/definitionsDao.ts - 数据访问层
export class DefinitionsDao {
  async getDefinitions(filters: FilterParams): Promise<Definition[]> {
    const sql = 'SELECT * FROM definitions WHERE ...';
    return await db.query(sql);
  }
}
```

### 2. 数据库操作

#### 连接管理
```typescript
export class DatabaseConnection {
  private db: sqlite3.Database | null = null;

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async query<T>(sql: string, params: any[] = []): Promise<T[]> {
    return new Promise((resolve, reject) => {
      this.db!.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows as T[]);
      });
    });
  }
}
```

#### 事务处理
```typescript
async function updateWordStatus(wordId: number, status: string): Promise<void> {
  await db.transaction(async (tx) => {
    await tx.run('UPDATE definitions SET auditStatus = ? WHERE id = ?', [status, wordId]);
    await tx.run('INSERT INTO audit_log (word_id, action) VALUES (?, ?)', [wordId, 'status_update']);
  });
}
```

### 3. API 设计

#### 路由定义
```typescript
// 使用 Fastify Schema 验证
fastify.get<{ Querystring: PaginationParams }>('/', {
  schema: {
    querystring: {
      type: 'object',
      properties: {
        page: { type: 'number', minimum: 1 },
        limit: { type: 'number', minimum: 1, maximum: 100 }
      }
    }
  }
}, async (request, reply) => {
  // 处理逻辑
});
```

#### 错误处理
```typescript
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 全局错误处理
fastify.setErrorHandler(async (error, request, reply) => {
  if (error instanceof ApiError) {
    return reply.code(error.statusCode).send({
      success: false,
      error: error.message
    });
  }
  
  return reply.code(500).send({
    success: false,
    error: '服务器内部错误'
  });
});
```

## 🎨 前端开发

### 1. 组件开发

#### 组件结构
```typescript
// src/components/WordCard.tsx
interface WordCardProps {
  word: Definition;
  onStatusChange: (id: number, status: AuditStatus) => void;
}

export const WordCard: React.FC<WordCardProps> = ({ word, onStatusChange }) => {
  return (
    <Card className="p-4">
      <CardHeader>
        <CardTitle>{word.word}</CardTitle>
        <Badge variant={getStatusVariant(word.auditStatus)}>
          {word.auditStatus}
        </Badge>
      </CardHeader>
      <CardContent>
        <p>Priority: {word.priorityScore}</p>
        <p>Frequency: {word.frequency}</p>
      </CardContent>
      <CardFooter>
        <Button onClick={() => onStatusChange(word.id!, 'approved')}>
          Approve
        </Button>
      </CardFooter>
    </Card>
  );
};
```

#### 自定义 Hooks
```typescript
// src/hooks/useDefinitions.ts
export function useDefinitions(filters: FilterParams) {
  const [data, setData] = useState<Definition[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDefinitions = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.getDefinitions(filters);
      setData(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchDefinitions();
  }, [fetchDefinitions]);

  return { data, loading, error, refetch: fetchDefinitions };
}
```

### 2. 状态管理

#### Context 使用
```typescript
// src/contexts/AppContext.tsx
interface AppContextType {
  user: User | null;
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => void;
}

export const AppContext = createContext<AppContextType | undefined>(undefined);

export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within AppProvider');
  }
  return context;
}
```

#### 本地状态管理
```typescript
// 使用 useReducer 管理复杂状态
interface WordListState {
  words: Definition[];
  filters: FilterParams;
  pagination: PaginationInfo;
  loading: boolean;
}

type WordListAction = 
  | { type: 'SET_WORDS'; payload: Definition[] }
  | { type: 'SET_FILTERS'; payload: FilterParams }
  | { type: 'SET_LOADING'; payload: boolean };

function wordListReducer(state: WordListState, action: WordListAction): WordListState {
  switch (action.type) {
    case 'SET_WORDS':
      return { ...state, words: action.payload };
    case 'SET_FILTERS':
      return { ...state, filters: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    default:
      return state;
  }
}
```

### 3. 路由管理

#### TanStack Router 配置
```typescript
// src/routes/__root.tsx
export const Route = createRootRoute({
  component: RootComponent,
  errorComponent: ({ error }) => <ErrorBoundary error={error} />,
  notFoundComponent: () => <NotFound />
});

// src/routes/words/index.tsx
export const Route = createFileRoute('/words/')({
  component: WordsPage,
  loader: async () => {
    return await api.getDefinitions({ limit: 20 });
  }
});
```

## 🧪 测试策略

### 1. 后端测试

#### 单元测试
```typescript
// tests/services/definitionsService.test.ts
describe('DefinitionsService', () => {
  let service: DefinitionsService;
  let mockDao: jest.Mocked<DefinitionsDao>;

  beforeEach(() => {
    mockDao = {
      getDefinitions: jest.fn(),
      getDefinitionById: jest.fn(),
    } as any;
    service = new DefinitionsService(mockDao);
  });

  it('should return definitions', async () => {
    const mockDefinitions = [{ id: 1, word: 'test' }];
    mockDao.getDefinitions.mockResolvedValue(mockDefinitions);

    const result = await service.getDefinitions({});
    
    expect(result).toEqual(mockDefinitions);
    expect(mockDao.getDefinitions).toHaveBeenCalledWith({});
  });
});
```

#### 集成测试
```typescript
// tests/integration/api.test.ts
describe('API Integration', () => {
  let app: FastifyInstance;

  beforeAll(async () => {
    app = await createTestApp();
  });

  afterAll(async () => {
    await app.close();
  });

  it('GET /api/v1/definitions should return definitions', async () => {
    const response = await app.inject({
      method: 'GET',
      url: '/api/v1/definitions?limit=5'
    });

    expect(response.statusCode).toBe(200);
    const data = JSON.parse(response.payload);
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });
});
```

### 2. 前端测试

#### 组件测试
```typescript
// src/components/__tests__/WordCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { WordCard } from '../WordCard';

describe('WordCard', () => {
  const mockWord = {
    id: 1,
    word: 'test',
    auditStatus: 'pending_review' as const,
    priorityScore: 8
  };

  it('should render word information', () => {
    render(<WordCard word={mockWord} onStatusChange={jest.fn()} />);
    
    expect(screen.getByText('test')).toBeInTheDocument();
    expect(screen.getByText('pending_review')).toBeInTheDocument();
    expect(screen.getByText('Priority: 8')).toBeInTheDocument();
  });

  it('should call onStatusChange when approve button is clicked', () => {
    const mockOnStatusChange = jest.fn();
    render(<WordCard word={mockWord} onStatusChange={mockOnStatusChange} />);
    
    fireEvent.click(screen.getByText('Approve'));
    
    expect(mockOnStatusChange).toHaveBeenCalledWith(1, 'approved');
  });
});
```

#### E2E 测试
```typescript
// tests/e2e/word-management.spec.ts
import { test, expect } from '@playwright/test';

test('word management flow', async ({ page }) => {
  await page.goto('/words');
  
  // 检查页面加载
  await expect(page.getByText('单词管理')).toBeVisible();
  
  // 搜索单词
  await page.fill('[placeholder="搜索单词..."]', 'example');
  await page.press('[placeholder="搜索单词..."]', 'Enter');
  
  // 检查搜索结果
  await expect(page.getByText('example')).toBeVisible();
  
  // 更新状态
  await page.click('[data-testid="approve-button"]');
  await expect(page.getByText('approved')).toBeVisible();
});
```

## 📋 开发最佳实践

### 1. 代码质量

#### 类型安全
```typescript
// 使用严格的类型定义
interface StrictDefinition {
  readonly id: number;
  readonly word: string;
  readonly auditStatus: AuditStatus;
  readonly createdAt: Date;
}

// 避免 any 类型
function processData<T extends Record<string, unknown>>(data: T): T {
  return data;
}
```

#### 错误处理
```typescript
// 使用 Result 模式
type Result<T, E = Error> = 
  | { success: true; data: T }
  | { success: false; error: E };

async function safeApiCall<T>(fn: () => Promise<T>): Promise<Result<T>> {
  try {
    const data = await fn();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error as Error };
  }
}
```

### 2. 性能优化

#### 前端优化
```typescript
// 使用 React.memo 优化组件
export const WordCard = React.memo<WordCardProps>(({ word, onStatusChange }) => {
  // 组件实现
}, (prevProps, nextProps) => {
  return prevProps.word.id === nextProps.word.id &&
         prevProps.word.auditStatus === nextProps.word.auditStatus;
});

// 使用 useMemo 优化计算
const filteredWords = useMemo(() => {
  return words.filter(word => 
    word.word.toLowerCase().includes(searchTerm.toLowerCase())
  );
}, [words, searchTerm]);
```

#### 后端优化
```typescript
// 数据库查询优化
async function getDefinitionsOptimized(filters: FilterParams) {
  // 使用索引
  const sql = `
    SELECT id, word, auditStatus, priorityScore
    FROM definitions 
    WHERE auditStatus = ? 
    ORDER BY priorityScore DESC 
    LIMIT ? OFFSET ?
  `;
  
  return await db.query(sql, [filters.auditStatus, filters.limit, filters.offset]);
}
```

### 3. 安全考虑

#### 输入验证
```typescript
// 使用 Zod 进行严格验证
const CreateDefinitionSchema = z.object({
  word: z.string().min(1).max(100).regex(/^[a-zA-Z\s-']+$/),
  priorityScore: z.number().int().min(0).max(10),
  auditStatus: z.enum(['pending_review', 'approved', 'rejected'])
});
```

#### SQL 注入防护
```typescript
// 始终使用参数化查询
async function getDefinitionByWord(word: string): Promise<Definition | null> {
  const sql = 'SELECT * FROM definitions WHERE word = ?';
  const result = await db.queryOne<Definition>(sql, [word]);
  return result || null;
}
```

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [05-部署指南](./05-部署指南.md)  
**下一篇**: [07-故障排除](./07-故障排除.md)
