# 01-项目概览

## 🎯 项目定位

SenseWord ContentHub 是一个智能化的英语词汇内容生产与审核中台，专注于管理英语单词的深度解析、例句资产和音频生成的全生命周期。

## 📋 核心功能

### 内容生产中台
- **统一管理**: 词汇内容的生产流程
- **智能审核**: 内容审核和质量控制
- **资产管理**: 文本、音频、翻译等多媒体资产
- **发布管道**: 自动化的内容发布和同步流程

### 技术架构
```
SenseWord ContentHub
├── ContentHub-Dashboard (前端管理界面)
│   ├── React 18 + TypeScript
│   ├── Vite + TanStack Router
│   ├── shadcn/ui 组件库
│   └── 响应式设计
└── ContentHub-API (后端API服务)
    ├── Node.js + Fastify + TypeScript
    ├── SQLite 数据库
    ├── RESTful API
    └── 数据处理管道
```

## 🗄️ 数据规模

- **总词汇量**: 62,815个英语单词
- **数据表**: 3个核心表（definitions, example_sentences, words_for_publish）
- **状态管理**: 多层次的审核和发布状态
- **优先级系统**: 1-10级优先级分布

## 🌐 部署架构

### 开发环境
- **前端**: http://localhost:5173/ (Vite Dev Server)
- **后端**: http://localhost:3000/ (Fastify Server)
- **数据库**: SQLite 本地文件
- **文档**: http://localhost:3000/docs (Swagger UI)

### 生产环境
- **前端**: Vercel / Netlify
- **后端**: Cloudflare Workers
- **数据库**: Cloudflare D1
- **存储**: Cloudflare R2

## 📊 项目状态

- **当前版本**: v1.0.0
- **开发状态**: 活跃开发中
- **最后更新**: 2025-01-07
- **维护团队**: SenseWord Team

## 🔗 相关链接

- **API 文档**: http://localhost:3000/docs
- **健康检查**: http://localhost:3000/health
- **前端界面**: http://localhost:5173/
- **项目仓库**: SenseWord ContentHub

## 📞 联系方式

- **项目负责人**: SenseWord Team
- **技术支持**: <EMAIL>
- **文档维护**: 开发团队

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**下一篇**: [02-技术架构](./02-技术架构.md)
