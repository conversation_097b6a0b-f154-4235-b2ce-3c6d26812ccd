# 05-部署指南

## 🚀 部署概览

### 部署架构
```
开发环境 → 预发布环境 → 生产环境
   ↓           ↓           ↓
本地开发    Staging     Production
SQLite    Test DB    Cloudflare D1
```

### 环境要求
- **Node.js**: 18.0.0+
- **npm/pnpm**: 最新版本
- **SQLite**: 3.x (开发环境)
- **Git**: 版本控制

## 🏠 本地开发环境

### 1. 环境准备

#### 克隆项目
```bash
git clone <repository-url>
cd SenseWord-ContentHub
```

#### 安装依赖
```bash
# 后端依赖
cd ContentHub-API
npm install

# 前端依赖
cd ../ContentHub-Dashboard
pnpm install
```

### 2. 环境配置

#### 后端配置 (.env)
```bash
# ContentHub-API/.env
PORT=3000
HOST=localhost
DATABASE_PATH=/path/to/senseword_content.db
LOG_LEVEL=info
CORS_ORIGIN=http://localhost:5173
API_PREFIX=/api/v1
```

#### 前端配置
```bash
# ContentHub-Dashboard/.env
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_TITLE=SenseWord ContentHub
```

### 3. 启动服务

#### 启动后端
```bash
cd ContentHub-API
npm run dev
# 服务地址: http://localhost:3000
# API文档: http://localhost:3000/docs
```

#### 启动前端
```bash
cd ContentHub-Dashboard
pnpm dev
# 服务地址: http://localhost:5173
```

### 4. 开发工具

#### TypeScript 编译
```bash
# 后端编译
cd ContentHub-API
npm run build

# 前端编译
cd ContentHub-Dashboard
pnpm build
```

#### 代码检查
```bash
# 后端
npm run lint
npm run type-check

# 前端
pnpm lint
pnpm type-check
```

## 🧪 预发布环境

### 1. 环境特点
- **目的**: 生产前最后验证
- **数据**: 生产数据副本
- **配置**: 接近生产环境
- **访问**: 内部团队

### 2. 部署配置

#### Docker 容器化
```dockerfile
# ContentHub-API/Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/server.js"]
```

#### Docker Compose
```yaml
# docker-compose.staging.yml
version: '3.8'
services:
  api:
    build: ./ContentHub-API
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - DATABASE_PATH=/data/senseword_content.db
    volumes:
      - ./data:/data
  
  dashboard:
    build: ./ContentHub-Dashboard
    ports:
      - "5173:80"
    environment:
      - VITE_API_BASE_URL=http://api:3000
```

### 3. 部署命令
```bash
# 构建镜像
docker-compose -f docker-compose.staging.yml build

# 启动服务
docker-compose -f docker-compose.staging.yml up -d

# 查看日志
docker-compose -f docker-compose.staging.yml logs -f
```

## ☁️ 生产环境部署

### 1. Cloudflare 架构

#### 服务分布
- **前端**: Cloudflare Pages
- **后端**: Cloudflare Workers
- **数据库**: Cloudflare D1
- **存储**: Cloudflare R2

### 2. 前端部署 (Cloudflare Pages)

#### 构建配置
```bash
# 构建命令
pnpm build

# 输出目录
dist/

# 环境变量
VITE_API_BASE_URL=https://api.contenthub.senseword.app
```

#### 部署脚本
```bash
#!/bin/bash
# deploy-frontend.sh

echo "🏗️ 构建前端..."
cd ContentHub-Dashboard
pnpm install
pnpm build

echo "📦 部署到 Cloudflare Pages..."
npx wrangler pages publish dist --project-name=contenthub-dashboard

echo "✅ 前端部署完成!"
```

### 3. 后端部署 (Cloudflare Workers)

#### Worker 配置
```toml
# wrangler.toml
name = "contenthub-api"
main = "src/worker.ts"
compatibility_date = "2024-01-01"

[env.production]
name = "contenthub-api-prod"

[[env.production.d1_databases]]
binding = "DB"
database_name = "senseword-content"
database_id = "your-d1-database-id"
```

#### Worker 适配
```typescript
// src/worker.ts
import { Hono } from 'hono';
import { cors } from 'hono/cors';

const app = new Hono();

app.use('*', cors({
  origin: ['https://contenthub.senseword.app'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
}));

// API 路由
app.get('/api/v1/definitions', async (c) => {
  const db = c.env.DB;
  // 数据库查询逻辑
});

export default app;
```

#### 部署脚本
```bash
#!/bin/bash
# deploy-backend.sh

echo "🏗️ 构建后端..."
cd ContentHub-API
npm run build

echo "📦 部署到 Cloudflare Workers..."
npx wrangler deploy

echo "✅ 后端部署完成!"
```

### 4. 数据库迁移 (D1)

#### 创建数据库
```bash
# 创建 D1 数据库
npx wrangler d1 create senseword-content

# 执行迁移
npx wrangler d1 execute senseword-content --file=./schema.sql
```

#### 数据导入
```bash
# 导出本地数据
sqlite3 senseword_content.db .dump > data.sql

# 导入到 D1
npx wrangler d1 execute senseword-content --file=./data.sql
```

## 🔄 CI/CD 流程

### 1. GitHub Actions

#### 工作流配置
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd ContentHub-Dashboard
          pnpm install
      
      - name: Build
        run: |
          cd ContentHub-Dashboard
          pnpm build
      
      - name: Deploy to Cloudflare Pages
        run: |
          npx wrangler pages publish ContentHub-Dashboard/dist
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}

  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: |
          cd ContentHub-API
          npm install
      
      - name: Build
        run: |
          cd ContentHub-API
          npm run build
      
      - name: Deploy to Cloudflare Workers
        run: |
          cd ContentHub-API
          npx wrangler deploy
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
```

### 2. 部署策略

#### 蓝绿部署
```bash
# 部署到绿色环境
npx wrangler deploy --env green

# 验证绿色环境
curl https://green.api.contenthub.senseword.app/health

# 切换流量到绿色环境
npx wrangler route update --env production
```

#### 回滚策略
```bash
# 快速回滚到上一版本
npx wrangler rollback

# 回滚到指定版本
npx wrangler rollback --version-id=<version-id>
```

## 📊 监控与日志

### 1. 应用监控

#### Cloudflare Analytics
- **请求量**: 实时请求统计
- **响应时间**: 平均响应时间
- **错误率**: 4xx/5xx 错误统计
- **地理分布**: 用户地理位置分析

#### 自定义监控
```typescript
// 监控中间件
app.use('*', async (c, next) => {
  const start = Date.now();
  await next();
  const duration = Date.now() - start;
  
  // 记录指标
  c.env.ANALYTICS.writeDataPoint({
    blobs: [c.req.path, c.req.method],
    doubles: [duration],
    indexes: [c.res.status]
  });
});
```

### 2. 日志管理

#### 结构化日志
```typescript
const logger = {
  info: (message: string, meta?: object) => {
    console.log(JSON.stringify({
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      ...meta
    }));
  }
};
```

#### 日志聚合
- **Cloudflare Logpush**: 日志推送到外部系统
- **实时日志**: `wrangler tail` 实时查看
- **日志分析**: 使用 Cloudflare Analytics

## 🔒 安全配置

### 1. 环境变量管理
```bash
# 设置生产环境变量
npx wrangler secret put DATABASE_URL
npx wrangler secret put JWT_SECRET
npx wrangler secret put API_KEY
```

### 2. 访问控制
```typescript
// IP 白名单
const allowedIPs = ['***********/24'];

app.use('*', async (c, next) => {
  const clientIP = c.req.header('CF-Connecting-IP');
  if (!isIPAllowed(clientIP, allowedIPs)) {
    return c.text('Forbidden', 403);
  }
  await next();
});
```

### 3. CORS 配置
```typescript
app.use('*', cors({
  origin: [
    'https://contenthub.senseword.app',
    'https://staging.contenthub.senseword.app'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
```

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [04-API接口文档](./04-API接口文档.md)  
**下一篇**: [06-开发指南](./06-开发指南.md)
