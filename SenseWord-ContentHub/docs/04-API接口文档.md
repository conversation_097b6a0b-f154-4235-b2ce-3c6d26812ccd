# 04-API接口文档

## 🌐 API 概览

### 基本信息
- **Base URL**: `http://localhost:3000`
- **API 前缀**: `/api/v1`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证方式
- **开发环境**: 无需认证
- **生产环境**: JWT Token (待实现)

### 通用响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;        // 请求是否成功
  data?: T;               // 响应数据
  message?: string;       // 成功消息
  error?: string;         // 错误消息
  pagination?: {          // 分页信息
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

## 📚 Definitions API - 单词定义管理

### 1. 获取定义列表

**接口**: `GET /api/v1/definitions`

**描述**: 分页获取单词定义列表，支持筛选和搜索

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | number | 否 | 1 | 页码 |
| limit | number | 否 | 20 | 每页数量 (1-100) |
| search | string | 否 | - | 搜索关键词 |
| sortBy | string | 否 | word | 排序字段 |
| sortOrder | string | 否 | asc | 排序方向 (asc/desc) |
| auditStatus | string | 否 | - | 审核状态筛选 |
| frequency | string | 否 | - | 频率筛选 |
| priorityScore | number | 否 | - | 优先级筛选 |
| learningLanguage | string | 否 | en | 学习语言 |
| scaffoldingLanguage | string | 否 | zh | 脚手架语言 |

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "word": "example",
      "learningLanguage": "en",
      "scaffoldingLanguage": "zh",
      "priorityScore": 8,
      "frequency": "High",
      "auditStatus": "approved",
      "updatedAt": "2025-01-07T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 62815,
    "totalPages": 3141
  }
}
```

### 2. 获取单个定义

**接口**: `GET /api/v1/definitions/:id`

**描述**: 根据ID获取单词定义详情

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 定义ID |

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "word": "example",
    "learningLanguage": "en",
    "scaffoldingLanguage": "zh",
    "priorityScore": 8,
    "frequency": "High",
    "definitionJson": "{\"word\":\"example\",\"definitions\":[...]}",
    "auditStatus": "approved",
    "updatedAt": "2025-01-07T10:00:00Z"
  }
}
```

### 3. 更新审核状态

**接口**: `PATCH /api/v1/definitions/:id/status`

**描述**: 更新单词定义的审核状态

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 定义ID |

**请求体**:
```json
{
  "auditStatus": "approved"  // pending_review | approved | rejected | in_translation
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "审核状态更新成功"
}
```

### 4. 搜索单词

**接口**: `GET /api/v1/definitions/search`

**描述**: 根据关键词搜索单词

**查询参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| q | string | 是 | - | 搜索关键词 |
| limit | number | 否 | 10 | 返回数量 (1-50) |

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "word": "example",
      "frequency": "High",
      "priorityScore": 8,
      "auditStatus": "approved"
    }
  ]
}
```

### 5. 获取统计信息

**接口**: `GET /api/v1/definitions/stats`

**描述**: 获取定义表的统计信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "stats": {
      "total": 62815,
      "pending_review": 15000,
      "approved": 45000,
      "rejected": 2000,
      "in_translation": 815
    },
    "frequency": {
      "High": 5000,
      "Medium": 25000,
      "Low": 30000,
      "Rare": 2815
    },
    "priority": {
      "high": 15985,
      "medium": 32677,
      "low": 14153
    }
  }
}
```

## 🔧 系统管理 API

### 1. 健康检查

**接口**: `GET /health`

**描述**: 检查系统健康状态

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-01-07T10:00:00Z",
    "version": "1.0.0",
    "database": "connected"
  }
}
```

### 2. 系统信息

**接口**: `GET /`

**描述**: 获取系统基本信息

**响应示例**:
```json
{
  "success": true,
  "message": "SenseWord ContentHub API v1.0.0",
  "data": {
    "documentation": "/docs",
    "health": "/health",
    "api": "/api/v1"
  }
}
```

## 📋 状态码说明

### HTTP 状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 业务状态码
| 字段 | 值 | 说明 |
|------|----|----|
| success | true | 请求成功 |
| success | false | 请求失败 |

## 🔍 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": "错误描述",
  "message": "详细错误信息"
}
```

### 常见错误示例

#### 参数验证错误 (400)
```json
{
  "success": false,
  "error": "请求参数验证失败",
  "data": [
    {
      "field": "auditStatus",
      "message": "必须是有效的审核状态"
    }
  ]
}
```

#### 资源不存在 (404)
```json
{
  "success": false,
  "error": "定义不存在"
}
```

#### 服务器错误 (500)
```json
{
  "success": false,
  "error": "服务器内部错误"
}
```

## 🧪 测试示例

### 使用 curl 测试

#### 获取定义列表
```bash
curl -X GET "http://localhost:3000/api/v1/definitions?limit=5&auditStatus=approved"
```

#### 搜索单词
```bash
curl -X GET "http://localhost:3000/api/v1/definitions/search?q=example&limit=3"
```

#### 更新审核状态
```bash
curl -X PATCH "http://localhost:3000/api/v1/definitions/1/status" \
  -H "Content-Type: application/json" \
  -d '{"auditStatus": "approved"}'
```

### 使用 JavaScript 测试

#### Fetch API 示例
```javascript
// 获取定义列表
const response = await fetch('/api/v1/definitions?limit=10');
const data = await response.json();

// 更新审核状态
const updateResponse = await fetch('/api/v1/definitions/1/status', {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    auditStatus: 'approved'
  })
});
```

## 📖 在线文档

### Swagger UI
- **地址**: http://localhost:3000/docs
- **功能**: 交互式API文档
- **特性**: 在线测试、参数验证、响应示例

### 文档特性
- **实时更新**: 代码变更自动同步
- **交互测试**: 直接在文档中测试API
- **参数验证**: 实时验证请求参数
- **响应预览**: 查看真实响应数据

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [03-数据库设计](./03-数据库设计.md)  
**下一篇**: [05-部署指南](./05-部署指南.md)
