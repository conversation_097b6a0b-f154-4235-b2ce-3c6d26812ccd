# 02-技术架构

## 🏗️ 整体架构

### 系统分层
```
┌─────────────────────────────────────────┐
│           用户界面层 (UI Layer)           │
│    ContentHub-Dashboard (React)        │
└─────────────────────────────────────────┘
                    │ HTTP/REST
┌─────────────────────────────────────────┐
│          业务逻辑层 (API Layer)          │
│     ContentHub-API (Fastify)           │
└─────────────────────────────────────────┘
                    │ SQL
┌─────────────────────────────────────────┐
│          数据存储层 (Data Layer)         │
│        SQLite Database                 │
└─────────────────────────────────────────┘
```

## 🎨 前端架构 (ContentHub-Dashboard)

### 技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite 7.0
- **路由**: TanStack Router
- **UI组件**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: React Hooks

### 目录结构
```
src/
├── components/          # 可复用组件
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   └── icons/          # 图标组件
├── features/           # 功能模块
│   ├── dashboard/      # 仪表板
│   ├── words/          # 单词管理
│   ├── review/         # 审核中心
│   └── database/       # 数据库管理
├── routes/             # 路由配置
├── lib/                # 工具库
└── types/              # 类型定义
```

### 核心页面
1. **仪表板** (`/`) - 系统概览和核心指标
2. **单词管理** (`/words`) - 62,815个单词的列表展示
3. **审核中心** (`/review`) - 审核队列管理
4. **数据库管理** (`/database`) - 数据库状态监控

## ⚙️ 后端架构 (ContentHub-API)

### 技术栈
- **运行时**: Node.js 18+
- **框架**: Fastify 5.x
- **语言**: TypeScript
- **数据库**: SQLite 3
- **验证**: Zod
- **文档**: Swagger/OpenAPI

### 目录结构
```
src/
├── database/           # 数据库连接
├── dao/                # 数据访问层
├── services/           # 业务逻辑层
├── routes/             # 路由层
├── schemas/            # 验证模式
├── types/              # 类型定义
└── server.ts           # 服务器入口
```

### API 设计原则
- **RESTful**: 遵循REST设计规范
- **类型安全**: 完整的TypeScript类型支持
- **参数验证**: 使用Zod进行请求验证
- **错误处理**: 统一的错误响应格式
- **文档化**: 自动生成Swagger文档

## 🗄️ 数据库架构

### 三表分离设计
```
definitions              example_sentences         words_for_publish
├── id                  ├── id                    ├── id
├── word                ├── word                  ├── word
├── learningLanguage    ├── learningLanguage      ├── learningLanguage
├── scaffoldingLanguage ├── scaffoldingLanguage   ├── scaffoldingLanguage
├── priorityScore       ├── examplesJson          ├── contentJson
├── frequency           ├── auditStatus           ├── publishStatus
├── definitionJson      ├── ttsStatus             ├── contentVersion
├── auditStatus         └── updatedAt             └── updatedAt
└── updatedAt
```

### 数据流转
1. **definitions** → 单词定义和深度解析
2. **example_sentences** → 例句和音标管理
3. **words_for_publish** → 最终发布内容

## 🔄 状态管理

### 审核状态 (auditStatus)
- `pending_review` - 待审核
- `approved` - 已批准
- `rejected` - 已拒绝
- `in_translation` - 翻译中

### TTS状态 (ttsStatus)
- `pending` - 待处理
- `processing` - 处理中
- `completed` - 已完成
- `failed` - 处理失败
- `skipped` - 已跳过

### 发布状态 (publishStatus)
- `pending_upload` - 待上传
- `uploading` - 上传中
- `published` - 已发布
- `failed` - 发布失败
- `outdated` - 已过期

## 🔌 API 接口设计

### 核心端点
```
GET    /api/v1/definitions          # 获取定义列表
GET    /api/v1/definitions/:id      # 获取单个定义
PATCH  /api/v1/definitions/:id/status # 更新审核状态
GET    /api/v1/definitions/search   # 搜索单词
GET    /api/v1/definitions/stats    # 获取统计信息
```

### 响应格式
```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}
```

## 🛡️ 安全设计

### 数据验证
- **输入验证**: Zod schema验证
- **类型安全**: TypeScript编译时检查
- **SQL注入防护**: 参数化查询

### 错误处理
- **统一错误格式**: 标准化错误响应
- **日志记录**: 详细的错误日志
- **优雅降级**: 服务异常时的备用方案

## 📈 性能优化

### 前端优化
- **代码分割**: 路由级别的懒加载
- **组件缓存**: React.memo优化
- **虚拟滚动**: 大列表性能优化

### 后端优化
- **数据库索引**: 关键字段索引优化
- **连接池**: 数据库连接复用
- **缓存策略**: 热点数据缓存

## 🔧 开发工具

### 前端工具
- **开发服务器**: Vite Dev Server
- **类型检查**: TypeScript Compiler
- **代码格式化**: Prettier + ESLint
- **组件开发**: Storybook (可选)

### 后端工具
- **开发服务器**: tsx watch
- **API文档**: Swagger UI
- **类型检查**: TypeScript Compiler
- **测试框架**: Vitest

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [01-项目概览](./01-项目概览.md)  
**下一篇**: [03-数据库设计](./03-数据库设计.md)
