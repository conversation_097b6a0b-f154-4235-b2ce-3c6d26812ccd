# 07-故障排除

## 🚨 常见问题诊断

### 问题分类
- **启动问题**: 服务无法启动
- **连接问题**: 数据库连接失败
- **性能问题**: 响应缓慢或超时
- **数据问题**: 数据不一致或丢失
- **部署问题**: 部署失败或配置错误

## 🔧 后端问题排查

### 1. 服务启动失败

#### 问题现象
```bash
数据库连接失败: SQLITE_CANTOPEN: unable to open database file
{"level":50,"msg":"服务器启动失败"}
```

#### 排查步骤
```bash
# 1. 检查数据库文件是否存在
ls -la /path/to/senseword_content.db

# 2. 检查文件权限
chmod 644 /path/to/senseword_content.db

# 3. 检查目录权限
chmod 755 /path/to/database/directory

# 4. 验证数据库完整性
sqlite3 senseword_content.db "PRAGMA integrity_check;"
```

#### 解决方案
```typescript
// 添加数据库路径验证
import { existsSync } from 'fs';

const dbPath = process.env['DATABASE_PATH'] || defaultPath;
if (!existsSync(dbPath)) {
  throw new Error(`数据库文件不存在: ${dbPath}`);
}
```

### 2. 端口占用问题

#### 问题现象
```bash
Error: listen EADDRINUSE: address already in use :::3000
```

#### 排查步骤
```bash
# 查看端口占用
lsof -i :3000
netstat -tulpn | grep :3000

# 杀死占用进程
kill -9 <PID>

# 或者更改端口
export PORT=3001
```

#### 解决方案
```typescript
// 动态端口分配
const port = process.env['PORT'] || findAvailablePort(3000);

async function findAvailablePort(startPort: number): Promise<number> {
  for (let port = startPort; port < startPort + 100; port++) {
    if (await isPortAvailable(port)) {
      return port;
    }
  }
  throw new Error('No available port found');
}
```

### 3. 内存泄漏问题

#### 问题现象
```bash
# 内存使用持续增长
FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
```

#### 排查步骤
```bash
# 监控内存使用
node --inspect src/server.ts
# 在 Chrome DevTools 中分析内存

# 使用 clinic.js 分析
npm install -g clinic
clinic doctor -- node src/server.ts
```

#### 解决方案
```typescript
// 正确关闭数据库连接
process.on('SIGTERM', async () => {
  await db.close();
  process.exit(0);
});

// 避免内存泄漏的查询
async function getDefinitionsWithLimit(limit: number = 100) {
  // 限制查询结果数量
  const sql = 'SELECT * FROM definitions LIMIT ?';
  return await db.query(sql, [Math.min(limit, 1000)]);
}
```

### 4. 数据库锁定问题

#### 问题现象
```bash
SQLITE_BUSY: database is locked
```

#### 排查步骤
```bash
# 检查数据库连接
sqlite3 senseword_content.db ".timeout 30000"

# 查看活跃连接
sqlite3 senseword_content.db "PRAGMA database_list;"
```

#### 解决方案
```typescript
// 设置数据库超时
const db = new sqlite3.Database(dbPath, {
  timeout: 30000,
  busyTimeout: 30000
});

// 使用连接池
class ConnectionPool {
  private connections: sqlite3.Database[] = [];
  private maxConnections = 5;

  async getConnection(): Promise<sqlite3.Database> {
    if (this.connections.length < this.maxConnections) {
      return new sqlite3.Database(dbPath);
    }
    // 等待可用连接
    return await this.waitForConnection();
  }
}
```

## 🎨 前端问题排查

### 1. 页面白屏问题

#### 问题现象
- 页面加载后显示空白
- 控制台有 JavaScript 错误

#### 排查步骤
```bash
# 检查控制台错误
# 打开浏览器开发者工具 -> Console

# 检查网络请求
# 打开浏览器开发者工具 -> Network

# 检查构建输出
pnpm build
ls -la dist/
```

#### 解决方案
```typescript
// 添加错误边界
class ErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }
    return this.props.children;
  }
}
```

### 2. API 请求失败

#### 问题现象
```javascript
Failed to fetch
CORS error
Network Error
```

#### 排查步骤
```bash
# 检查 API 服务状态
curl http://localhost:3000/health

# 检查 CORS 配置
curl -H "Origin: http://localhost:5173" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS \
     http://localhost:3000/api/v1/definitions
```

#### 解决方案
```typescript
// 前端 API 配置
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);
```

### 3. 路由问题

#### 问题现象
- 页面刷新后 404
- 路由跳转失败

#### 排查步骤
```typescript
// 检查路由配置
import { createRouter } from '@tanstack/react-router';

// 检查路由定义
console.log('Available routes:', router.routeTree);
```

#### 解决方案
```typescript
// 配置 Vite 开发服务器
// vite.config.ts
export default defineConfig({
  server: {
    historyApiFallback: true
  }
});

// 添加路由错误处理
export const Route = createRootRoute({
  component: RootComponent,
  errorComponent: ({ error }) => {
    console.error('Route Error:', error);
    return <div>页面加载失败: {error.message}</div>;
  }
});
```

## 📊 性能问题排查

### 1. 查询性能问题

#### 问题现象
```bash
# API 响应缓慢
GET /api/v1/definitions - 5000ms
```

#### 排查步骤
```sql
-- 分析查询计划
EXPLAIN QUERY PLAN 
SELECT * FROM definitions 
WHERE auditStatus = 'pending_review' 
ORDER BY priorityScore DESC;

-- 检查索引使用
.schema definitions
```

#### 解决方案
```sql
-- 添加复合索引
CREATE INDEX idx_definitions_audit_priority 
ON definitions(auditStatus, priorityScore);

-- 优化查询
SELECT id, word, auditStatus, priorityScore 
FROM definitions 
WHERE auditStatus = 'pending_review' 
ORDER BY priorityScore DESC 
LIMIT 20;
```

### 2. 前端性能问题

#### 问题现象
- 页面渲染缓慢
- 滚动卡顿
- 内存使用过高

#### 排查步骤
```typescript
// 使用 React DevTools Profiler
// 检查组件渲染次数和时间

// 使用 Performance API
const start = performance.now();
// 执行操作
const end = performance.now();
console.log(`Operation took ${end - start} milliseconds`);
```

#### 解决方案
```typescript
// 虚拟滚动优化
import { FixedSizeList as List } from 'react-window';

const WordList = ({ words }: { words: Definition[] }) => (
  <List
    height={600}
    itemCount={words.length}
    itemSize={100}
    itemData={words}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <WordCard word={data[index]} />
      </div>
    )}
  </List>
);

// 组件优化
const WordCard = React.memo(({ word }: { word: Definition }) => {
  return <div>{word.word}</div>;
});
```

## 🔍 调试工具

### 1. 后端调试

#### 日志配置
```typescript
// 结构化日志
const logger = {
  debug: (message: string, meta?: object) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(JSON.stringify({
        level: 'debug',
        message,
        timestamp: new Date().toISOString(),
        ...meta
      }));
    }
  },
  
  error: (message: string, error?: Error) => {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: error?.message,
      stack: error?.stack,
      timestamp: new Date().toISOString()
    }));
  }
};
```

#### 数据库调试
```typescript
// 查询日志
class DatabaseConnection {
  async query<T>(sql: string, params: any[] = []): Promise<T[]> {
    const start = Date.now();
    
    try {
      const result = await this.executeQuery<T>(sql, params);
      const duration = Date.now() - start;
      
      logger.debug('Database Query', {
        sql: sql.replace(/\s+/g, ' ').trim(),
        params,
        duration,
        rowCount: result.length
      });
      
      return result;
    } catch (error) {
      logger.error('Database Query Failed', {
        sql,
        params,
        error: error as Error
      });
      throw error;
    }
  }
}
```

### 2. 前端调试

#### React DevTools
```typescript
// 组件调试信息
const WordCard = ({ word }: { word: Definition }) => {
  // 开发环境下添加调试信息
  if (process.env.NODE_ENV === 'development') {
    console.log('WordCard render:', word.id, word.word);
  }
  
  return <div>{word.word}</div>;
};
```

#### 网络请求调试
```typescript
// API 请求日志
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', {
      url: response.config.url,
      status: response.status,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message
    });
    return Promise.reject(error);
  }
);
```

## 📋 故障排除清单

### 启动前检查
- [ ] Node.js 版本 >= 18.0.0
- [ ] 依赖包已安装 (`npm install` / `pnpm install`)
- [ ] 环境变量已配置 (`.env` 文件)
- [ ] 数据库文件存在且可访问
- [ ] 端口未被占用

### 运行时检查
- [ ] 服务健康检查通过 (`/health`)
- [ ] 数据库连接正常
- [ ] API 接口响应正常
- [ ] 前端页面加载正常
- [ ] 控制台无错误信息

### 性能检查
- [ ] API 响应时间 < 1000ms
- [ ] 页面加载时间 < 3000ms
- [ ] 内存使用稳定
- [ ] CPU 使用率正常

### 部署检查
- [ ] 构建成功无错误
- [ ] 环境变量正确配置
- [ ] 数据库迁移完成
- [ ] 服务正常启动
- [ ] 健康检查通过

## 🆘 获取帮助

### 日志收集
```bash
# 收集系统信息
node --version
npm --version
sqlite3 --version

# 收集应用日志
tail -f logs/app.log

# 收集错误信息
npm run dev 2>&1 | tee debug.log
```

### 问题报告模板
```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- OS: macOS 14.0
- Node.js: 18.17.0
- npm: 9.6.7

## 重现步骤
1. 启动服务
2. 访问 /api/v1/definitions
3. 出现错误

## 错误信息
```
粘贴完整的错误信息
```

## 预期行为
描述期望的正确行为

## 实际行为
描述实际发生的行为
```

### 联系方式
- **技术支持**: <EMAIL>
- **问题反馈**: GitHub Issues
- **文档问题**: 开发团队

---

**文档版本**: v1.0  
**更新日期**: 2025-01-07  
**上一篇**: [06-开发指南](./06-开发指南.md)  
**下一篇**: [08-更新日志](./08-更新日志.md)
