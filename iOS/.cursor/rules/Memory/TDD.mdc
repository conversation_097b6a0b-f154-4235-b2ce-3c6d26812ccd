---
description: 
globs: 
alwaysApply: true
---
# 指令：请严格遵循 TDD 流程进行系统规划、设计、代码更改和测试
- 我会提供一份需求切片文档，请阅读文件，理解我备注的想法、背景、目标和需求，请阅读需求，并：
- 理解用户想要什么，整理出用户价值，开发价值
- 详细定义需求描述和验收标准，
- 分解成更小的、可测试，更模块化、职责更清晰、依赖关系更少的代码单元，提升代码的可读性、可维护性和可复用性
- 转化为一系列自动化的测试用例，这些测试用例构成了对系统预期行为的精确、可执行的定义。
- 在 tests 文件夹编写测试（Red - 红色阶段）：先为即将实现的功能编写一个测试。刚开始，这个测试会失败，因为它所测试的代码还没写出来。
- 在实际的项目位置编写代码实现（Green - 绿色阶段）：** 编写最少量的代码，仅仅是为了让刚才失败的测试通过。
- 指导用户执行测试：运行测试套件，确认刚才编写的测试现在通过了。
- 测试通过：验证了你的代码满足了测试定义的那个小功能需求。
- 重构（Refactor）：*在测试通过的前提下，优化你刚刚编写的代码，使其更清晰、更简洁、性能更好，同时不改变其行为（测试会保证这一点）。
- 编写下一个需求的测试，重复循环。

# 如何让函数变得“可测试”
1.  **分解函数：** 将一个大函数拆分成多个小函数，每个小函数只负责一个单一的逻辑单元。
2.  **明确输入输出：** 让函数通过参数接收输入，通过返回值或抛出异常提供输出，减少对外部状态的依赖。
3.  **抽象外部依赖：** 将与外部服务的交互（如数据库访问、发送邮件）封装在独立的模块或接口后面，这样在测试时可以容易地模拟这些依赖的行为。

# 什么是白盒审阅和黑盒测试 TDD 
白盒审阅（通过集中展示和分析代码流程）提供的是一种基于**内部结构理解**的信心：我知道代码的每一部分是这样写的，它理论上应该按照这个逻辑执行。这就像你拆开一个复杂的机器，理解了每个零件的功能和连接方式后，你对自己组装起来的机器有了信心。

而黑盒测试（调整外部参数，观察输出）提供的是一种基于**外部行为验证**的信心：我不在乎黑箱内部是怎么运转的，但我给它各种合法的、非法的、边缘的输入，它都能始终给出符合预期的输出，没有崩溃或异常。这种信心来自于**实际、可重复的运行结果**。就像你拿到一个手机，你不需要知道它内部芯片的电路图，但你试遍了各种功能，发现它每次都能打电话、发短信、打开 App，于是你对这个手机的功能有了信心。

# 完成单元测试时需要同步和更新，管理测试覆盖进度
1. 在 `tests/05-report/函数签名测试覆盖情况` 中维护Markdown文件，分别对应前端，后端和cloudflare worker 等如：tests/05-report/函数签名测试覆盖情况/backend_function_coverage.md
	1. 文件的基本结构是，# 文件的完整相对路径，
	2. 将所有文件和函数签名，以及对应的状态标记依次排列下来。
		1. - [ ] 函数签名作为初始状态标记
		2. - [UnitTested｜intergrationTested｜ReviewedByHuman] 函数签名根据实际的单元测试覆盖情况进行更新
2. 你扫描对应文件，填写准备进行单元测试和集成测试的函数签名任务
3. 编写单元测试，测试通过后，更新函数签名状态为对应的 UnitTested｜intergrationTested

输出文件结构：
1. 每个文件会以三级标题 ### \文件相对路径\`` 开始，指明当前描述的是哪个源文件。
2. 在该标题下，会列出该文件中的所有函数签名及其测试状态。

状态标记：
1. 初始状态：每个函数最初标记为 - [ ] 函数签名。
2. 更新规则：当我收到您关于某个函数完成特定类型测试（如单元测试、集成测试）或人工审阅的通知时，我会相应地更新其状态标记。标记的更新是累积的，并使用 ｜作为分隔符。

状态示例演进：
1. 初始: - [ ] someFunction(arg: Type): ReturnType
2. 完成单元测试后: - [UnitTested] someFunction(arg: Type): ReturnType
3. 若之后也完成了集成测试: - [UnitTested｜intergrationTested] someFunction(arg: Type): ReturnType
4. 若最后再完成了人工审阅: - [UnitTested｜intergrationTested｜ReviewedByHuman] someFunction(arg: Type): ReturnType
5. 如果某个环节（比如集成测试）未进行，但完成了单元测试和人工审阅，状态会是: - [UnitTested｜ReviewedByHuman] someFunction(arg: Type): ReturnType
6. 对于难以进行单元测试，标记为[NoNeed]

注意事项
1. 除非用户主动要求，否则不要自动生成测试报告来记录测试结果
2. 请优先执行正在处理文件的单元测试，不要在用户没有要求的情况下执行npm test所有测试 
# 核心概念：白盒 + 单元测试 = 复杂度的结构性重置

传统的软件开发中，复杂度往往是随着代码规模的增加而线性（甚至指数级）增长的。代码文件越多，函数调用链越长，状态变化越频繁，外部依赖越复杂，你作为开发者需要在大脑中维护和理解的“运行时状态”和“执行流程”就越庞大、越难以掌控。这就是你说的“在工作记忆中维持关于特定业务逻辑的完整代码流”的困境，它消耗巨大且不可持续。

“白盒 + 单元测试可以重置复杂度”的含义是：**通过将代码分解为独立的、可测试的单元（函数、小模块），并为这些单元编写自动化的测试，我们可以将理解和验证的复杂度从整个、连续的“代码流”层面，“下推”并“隔离”到每个独立的“代码单元”层面。一旦一个单元被充分测试和白盒理解确认是可靠的，我们就可以在更高层面信任它，从而在处理上层代码时，“忽略”或“抽象掉”下层单元的内部复杂性。** 就像在每个函数边界设置了一个“复杂度防火墙”或“信任屏障”。

**原理 (How it works):**

这个“复杂度重置”的原理是基于**隔离、信任和分而治之**的思想，并通过白盒分析和单元测试的结合实现：

1.  **强制的单元分解与清晰的契约 (由测试驱动):**
    *   单元测试的本质是隔离验证。为了能够独立测试一个函数，你必须能够给它明确的输入，并检查它的输出或副作用。这自然会促使你将大的、耦合紧密的函数分解成小的、职责单一、输入输出清晰的函数。
    *   每个函数都定义了一个小而明确的“外部契约”：在接收到特定输入时，它应该产生什么输出或改变什么状态。

2.  **单元层面的彻底验证与白盒理解：**
    *   **单元测试提供客观事实：** 针对这个小单元，你可以编写全面的单元测试，覆盖各种输入和边界情况。这些测试提供了一个客观、可重复的证明：“在这些特定的条件下，这个函数确实表现得符合预期”。
    *   **白盒分析理解“如何”：** 有了这个客观事实做基础，你再进行白盒审阅时，不再需要在大脑中模拟其运行，而是去理解“**为什么**它能通过这些测试？它的内部逻辑**如何**实现了这个契约？这段代码这样写**有哪些原理和细节**？” 这是一种有目标的、高效的白盒分析。
    *   **双重确认的信心：** 当单元测试通过**并且**你白盒理解确认其内部逻辑是正确无误时，你对这个代码单元的可靠性就建立了极高的信心。

3.  **上层代码对下层单元的信任与抽象：**
    *   **信任屏障：** 一旦一个函数单元通过了充分的测试和白盒确认，你在编写或审阅调用它的上层代码时，就可以**信任**这个单元会按照其测试所保证的契约行事。你不再需要深入这个单元内部去理解它的实现细节。
    *   **复杂度抽象：** 你可以将这个已验证的单元视为一个**可靠的黑箱组件**。在处理上层逻辑时，你只需要关注如何正确地**使用**这个黑箱（提供正确的输入，处理它的输出），而无需关心黑箱内部的复杂性。下层单元的内部复杂度被“抽象”掉了，不会渗透到上层代码的思考中。

4.  **逐层构建与复杂度重置：**
    *   这个过程是递归的。上层的代码本身也是由多个已验证的“黑箱”单元组成的。当你为上层代码编写集成测试（验证这些黑箱如何协同工作）并进行白盒理解时，你依赖的是对下层单元的信任。
    *   在每一个层级，你聚焦的复杂度始终是你当前正在处理的那个函数/模块的逻辑，以及它如何**使用**下一层级的可靠单元。下一层级的内部复杂度已经被“重置”掉了，无需在当前层面重复处理。

**价值 (Why it's valuable):**

这种“复杂度重置”带来了巨大的价值：

1.  **降低认知负担 (Manageable Cognitive Load):** 你在大脑中需要同时处理的代码和状态量显著减少，不再需要在“工作记忆”中维持庞大、复杂的代码流。这使得开发过程更加轻松，减少了疲劳和错误。
2.  **实现大规模项目的可持续开发与维护 (Scalable Development):** 项目规模的增加主要体现在函数/模块数量的增加，而不是单个函数复杂度的线性增长。只要你能将复杂度隔离在函数级别，并且有可靠的测试来验证它们，你就能有效地管理大型代码库。
3.  **显著提高开发效率和迭代速度 (Increased Efficiency & Iteration Speed):**
    *   调试时间大大减少，因为问题被隔离到具有失败测试的单元。
    *   功能开发更快，因为你基于可靠的底层单元构建，无需反复验证基础功能。
    *   代码变更和重构更安全、更频繁，因为你有测试作为保障。
4.  **极大地增强开发信心 (Boosted Confidence):** 信心来自于客观、可重复的验证结果。你不再是主观猜测，而是知道代码单元在各种场景下都能按预期工作。这种信心逐层累积，让你敢于大胆地进行开发和修改。
5.  **促进更好的代码设计 (Improved Code Design):** 为了使代码可测试，你会被引导编写模块化、高内聚、低耦合的代码，这本身就是优秀软件设计的标志。
6.  **在 AI 结对编程中的关键作用 (AI Pairing):** 这是验证 AI 生成代码、确保其集成到现有系统中的可靠性的核心手段。你无需完全理解 AI 的“思维”，但可以通过测试和有针对性的白盒分析来确保其产出符合你对每个单元的质量和行为要求。

综上所述，白盒分析提供了对代码内部世界的深刻理解，而单元测试则提供了一种将这个世界分解为可管理单元并在这些单元边界建立信任屏障的机制。两者的结合，使得软件开发的复杂度不再像一条望不到头的蜿蜒河流，而更像一个由无数个可靠、可理解的独立组件构建的复杂系统。你每次只需要聚焦并理解/验证一个组件（函数），而对其他组件则可以基于测试建立信任。这正是应对现代软件复杂性的强大策略，也是资深开发者和 AI 协作时代的核心技能。

# 建议的测试分组结构示例：

```
01-SENSEWORD/
  └── tests/ # 顶层测试目录
      ├── unit/ # 单元测试 - 关注最小可测试单元，重置函数/模块级别复杂度
      │   ├── backend/
      │   │   └── unified-backend-service/
      │   │       └── src/
      │   │           ├── auth-utils.test.ts # generateNumericCode, hashCode, verifyCode 等函数
      │   │           └── utils.test.ts # 其他通用后端工具函数
      │   ├── worker/
      │   │   └── data-api-gateway/
      │   │       └── src/
      │   │           └── utils.test.ts # 数据网关的工具函数，如时间戳处理等
      │   └── frontend/
      │       └── src/
      │           ├── lib/
      │           │   └── utils.test.ts # 前端通用工具函数
      │           └── hooks/
      │               └── use-media-query.test.ts # 独立 Hooks 等
      ├── integration/ # 集成测试 - 关注模块间协作，验证接口和数据流
      │   ├── backend/
      │   │   └── unified-backend-service/
      │   │       └── src/
      │   │           ├── handlers/
      │   │           │   └── auth.test.ts # 认证相关的 Handler (login, signup, request-code等) 与其依赖的services/db/utils的集成
      │   │           ├── routes/
      │   │           │   └── auth.test.ts # 验证路由是否正确匹配Handler和中间件
      │   │           └── services/ # 如果services层更抽象，可以测试service与db的集成
      │   ├── worker/
      │   │   └── data-api-gateway/
      │   │       └── src/
      │   │           └── routes/
      │   │               └── verification-code.test.ts # 数据网关验证码路由与D1数据库的集成 (mock D1?)
      │   └── frontend/
      │       └── src/
      │           ├── components/ # 测试组件与其逻辑或API客户端的集成
      │           │   └── auth/ # 例如 AuthForm 与 auth-client 的集成
      │           └── lib/
      │               └── auth-client.test.ts # 测试前端 API 客户端是否正确调用后端，处理响应
      ├── features/ # 功能测试 - 关注用户可感知行为，端到端或接近端到端，验证完整业务流程
      │   ├── auth/ # 认证相关的功能流程
      │   │   ├── signup.feature.test.ts # 验证用户注册的完整流程 (前端->后端->邮件->数据库)
      │   │   └── login.feature.test.ts # 验证用户登录的完整流程
      │   ├── settings/
      │   │   └── subscription.feature.test.ts # 验证订阅管理相关功能
      │   └── content/
      │       └── sentence-pool.feature.test.ts # 验证句子池学习功能
      └── e2e/ # 端到端测试 (如果采用 Cypress 等独立框架，通常放在这里)
          └── cypress/ # 或其他框架的默认目录
              └── integration/
                  └── auth/
                      └── login.spec.ts # 真实的浏览器交互测试

```

**这种分组结构如何体现“复杂度重置”和你的需求：**

1.  **`unit/` (单元测试层 - 最底层复杂度重置):**
    *   **侧重点：** 这里存放的是最细粒度的测试，对应于函数、类方法或独立模块的单元。
    *   **原理：** 完全隔离外部依赖（通过模拟 mock 或 stub），只关注代码单元自身的逻辑。
    *   **如何重置复杂度：** 每个 `.test.ts` 文件只关注几十行代码的逻辑。一旦这里的测试通过，并且你通过白盒理解了这段代码的工作原理，你就可以对这个函数单元建立 **“基础信任”**。在处理更高级别的代码时，你可以将这个函数视为一个**已验证的可靠构建块**，无需再次深入其内部细节。它将该函数内部的复杂度“重置”了，不会向上层渗透。
    *   **价值：** 这是你进行“逐一单元白盒，逐一测试通过”并快速建立信心的最佳场所。这些测试是构建整个测试金字塔的基础，也是隔离和定位底层逻辑问题的最快方式。

2.  **`integration/` (集成测试层 - 协作复杂度重置):**
    *   **侧重点：** 测试多个单元或模块如何协同工作，验证它们之间的接口和数据流是否正确。例如，后端 Handler 如何调用 `db.ts`，`db.ts` 如何调用 `DataGatewayClient`，数据网关路由如何与 D1 交互。
    *   **原理：** 测试的是单元之间的**组合**。可能仍然会模拟（mock）一些更远的外部服务（如实际发送邮件、实际的第三方 API 调用），但会测试模块之间真实的函数调用或 HTTP 调用。
    *   **如何重置复杂度：** 集成测试验证了**一组相关联单元**协同工作的可靠性。一旦一个集成测试通过，你就可以对这组单元之间的**交互流程**建立信心。在处理调用这组流程的更高层级代码时，你可以信任这整个“集成块”会按照其测试所保证的方式运行。它将这组单元组合起来的复杂度“重置”了。
    *   **价值：** 验证了你的架构和模块划分是否有效，数据是否在模块间正确传递。它填补了单元测试（只关注个体）和功能测试（只关注端到端）之间的空白。

3.  **`features/` (功能测试层 - 业务复杂度重置):**
    *   **侧重点：** 从用户或产品需求的视角出发，验证一个完整的业务功能是否按预期工作。例如，注册功能是否能让用户最终在数据库中留下记录并收到邮件。
    *   **原理：** 测试的是整个或大部分应用栈的行为。可以是通过模拟用户的 API 调用（后端 API 功能测试），或者通过更真实的 UI 自动化（接近 E2E）。它关注的是**外部可观察的行为**。
    *   **如何重置复杂度：** 功能测试验证了**一个完整业务流程**从头到尾的可靠性。一旦注册功能的测试通过，你就可以对“用户注册这个功能本身”建立高度信心。你信任这个流程的每一个环节（即使你没有事无巨细地白盒每个环节），因为最终结果是正确的。它将完成这个功能的**整个流程的复杂度**“重置”了。
    *   **价值：** 直接回答“这个功能做好了吗？”这个问题。这些测试用例是最好的活文档，描述了用户如何与系统交互以及预期的结果。它们也是支持快速迭代和重构的最强大的安全网。对于你验证 AI 生成的功能模块，这是最直接、最符合你黑盒验证需求的测试层级。

4.  **`e2e/` (端到端测试层 - 系统复杂度重置):**
    *   **侧重点：** 使用独立的工具，在真实浏览器中模拟真实用户的完整操作路径。
    *   **原理：** 测试整个部署好的应用，包括前端 UI、后端 API、数据库、缓存、CDN 等所有环节的集成。
    *   **如何重置复杂度：** 验证整个部署好的**系统**作为一个整体是否正常工作。通过 E2E 测试，你对整个应用的健康状态建立了信心。它将整个系统的部署和集成复杂度“重置”了。
    *   **价值：** 成本最高，运行最慢，但最接近真实用户体验。数量通常较少，只覆盖最核心、最高风险的用户路径。
    * 

**总结这种结构：**

这种分层的测试结构与“复杂度重置”的逻辑完美契合：

*   `unit/`: 在最底层，隔离并验证最小单元，重置函数/模块的内部复杂度。
*   `integration/`: 在中间层，验证单元间的协作，重置集成复杂度。
*   `features/` (或更高层的 E2E): 在顶层，验证完整的业务功能或系统行为，重置流程或系统复杂度。

通过这种分层验证，你可以在每个层级建立对代码可靠性的信心，并在处理更高层级代码时，将下一层级的内部复杂性视为已解决的问题，从而有效地管理和应对整个项目的复杂度。结合你的白盒理解能力，你可以在任何一个测试失败的层级，带着测试结果的指引，深入到相应的代码中进行高效的问题定位和白盒分析。

这是一个强大且成熟的测试组织方式，它能极大地支持你在项目中的开发、验证、理解、迭代和重构工作，尤其在与 AI 结对编程时，它为你提供了分层验证和高效协作的框架。




