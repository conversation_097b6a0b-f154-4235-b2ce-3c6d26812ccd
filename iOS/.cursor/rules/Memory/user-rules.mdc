---
description: 
globs: 
alwaysApply: false
---
# 偏好
## 语言偏好
- !!!重要：使用中文回复用户的请求和提问
- 在编写任何代码之前，请逐步解释你的方法 

# Agent 行为 

## 如何调查、分析、修复问题
- 收到用户请求和提问之后，在进行计划和实际的代码编辑之前，最大化你的Thinking Tokens 预算，先思考如何更好地完成用户提供的任务，思考用户的目标，思考达成目标的最佳策略，将任务拆解为多个连续的，逻辑清楚的待办清单。
- !!!IMPORTANT 积极使用 Web 搜索工具了解当前问题所依赖的文档和公开可访问的方案。
- 从第一性原理思考导致问题的5-7个可能原因
- 在问题出现的上下文添加更加详细的打印输出日志，观察问题是如何引起和发生的，验证你的假设。
- 请使用代码跟踪，调用回溯，逐步推理，分析核心原因，进行完整的深入分析，并最终提供你偏好的推荐方案，并解释这样选择的原因。
- 先制定计划，经过审阅，和我达成共识以后，最后再修改代码。
- Think carefully and only action the specific task I have given you with the most concise and elegant solution that changes as little code as possible.

# 工作流程
## ODD（Oriented-Document Development，ODD）
- 请提前理解我们的协作范式，面向文档开发（Oriented-Document Development，ODD）是一种新的软件开发范式。它强调在AI深度赋能的时代，软件开发流程的核心不再是编写代码，而是构建和维护高质量的文档 
- 文档在ODD中被置于软件开发流程的核心地位。它们不再是开发的附属品或事后补充，而是驱动整个开发活动的中心和驱动力 。开发模式变为“文档先行，AI实现，持续迭代文档与代码。
- ODD旨在通过将文档置于开发流程的核心，确保AI产出与业务需求的深度对齐，提高开发效率和软件质量，并保障系统的长期可持续性 1。通过文档化的过程，模糊的想法被转化为清晰、明确的描述，减少了因需求理解偏差导致的错误
- AI围绕文档的核心任务是：理解文档中表达的意图和需求，并以此为基础，生成具体的代码或其他产出，并在持续的迭代过程中，将文档作为指导其执行和人机协同的依据，辅助人类生成或更新部分文档内容。 文档是AI执行任务的蓝图、规范和“共同语言” 
- 你需要了解的重要文档结构有：
- 标准开发流程：docs/06-开发计划、docs/04-技术方案、docs/08-进度日志
- 标准知识管理和审阅理解流程：docs/02-知识库、docs/03-使用说明、docs/07-业务逻辑地图

## 开发前准备：引入上下文，校验计划可行性
- 读取所提供文件的计划内容以及已完成的进度日志，理解当前所处于的计划阶段  
- 主动地扩展你对项目的理解和上下文，使用 read file工具，打开和阅读计划文件中<context_files>标签中所提及的重要代码文件。 
- readfile  最多可以阅读250行，请务必重复执行直到获取完整代码文件。 在没有获取完整代码文件之前，禁止进入下一阶段。 
- 出于调查目的的阅读代码文件操作，单次查看不低于250行代码，尽量减少 Tools 使用次数，确保你有足够的信息和信心来解决用户提出的问题。  
- 在推理中发现自己缺失了任何代码和上下文，都再次启动read file 工作流程，持续地吸收项目代码内容，确保拥有足够的上下文信息，以提供准确的回答。
- 大量地搜寻、使用 codebase search 从codebase当中搜集相关代码  
- 使用 Context7 MCP 获取实时的文档和技术框架最佳实践，Context7 MCP pulls up-to-date, version-specific documentation and code examples straight from the source — and places them directly into your prompt.
- 调查，校验和分析计划是否有效、可行和准确；评估除此之外的多种方案的优缺点，并为用户推荐你认为最佳的方案。
- 和用户分享和讨论你对于计划的分析和看法，解释计划实现的核心思想，技术原理以及主要步骤分解，重点关注计划中模糊和没有明确说明的内容，以及潜在的风险，并主动要求用户澄清和明确需求。
- 提出用户可能感兴趣的问题，尤其是计划是如何实现预期目标的。

#  校验计划可行和准确性
- 调查，校验和分析计划是否有效、可行和准确；评估除此之外的多种方案的优缺点，并为用户推荐你认为最佳的方案。
- 和用户分享和讨论你对于计划的分析和看法，解释计划实现的核心思想，技术原理以及主要步骤分解，重点关注计划中模糊和没有明确说明的内容，以及潜在的风险，并主动要求用户澄清和明确需求。
- 提出用户可能感兴趣的问题，尤其是计划是如何实现预期目标的。
- - !!! IMPORTANT 请务必遵循 interactive_feedback_rules 在规则约定的时机，调用 interactive_feedback 工具

# 实施计划
- 严格遵循以上代码上下文，
- 遵循计划逐步实施代码修改。
- - !!! IMPORTANT 请务必遵循 interactive_feedback_rules 在规则约定的时机，调用 interactive_feedback 工具


## 知识库整理（禁止自动进行，需要等待用户指令）
- 在 /docs/02-知识库中，创建markdown文件，记录当前会话功能实现或者问题解决的全过程。 
- 每完成一个独立的完整功能交付、漏洞修复自动创建一个总结技术方案，方便后续学习的知识笔记，名称为xxx｜当前知识聚焦核心.md 如：docs/02-知识库/xxx｜后端函数单元测试添加与修复.md，xxx 不需要实际替换为三位数字序列，我后续自己会手动更改。
- 笔记结构
1. 标题：比如 # Hono框架全面迁移和性能监控实现
2. 摘要：比如，本文档记录了将认证服务（auth-service）完全迁移到Hono框架，并添加性能监控系统的过程。
3. 背景：比如，之前的auth-service使用了Hono作为基础框架，但处理函数仍采用原始Request/Response模式处理请求，未能充分利用Hono的特性。现在，我们将处理函数完全迁移到Hono原生方式，并添加更完善的性能监控。
4. 对话互动的过程：我们是怎么发现以及遇到问题，我们如何进行分析
5. 功能实现 / 问题解决的完整过程 / 考虑到我是一名初级开发人员，我希望你从头到尾地，详细耐心地解释它，你会提及: 核心概念图式（中心概念、核心要素）、原理、思维方式（用什么模式来实现目标和解决特定问题）、技术方案，实际代码，详细流程分解与示例。

## 记录进度：结束时请整理用户本次编辑修改，方案实施的情况（禁止自动进行，需要等待用户指令）
在 docs/08-进度日志文件夹中创建一份进度日志文件
1. 每完成一个独立的完整功能交付、漏洞修复自动创建一个进度日志，名称为xxx｜当前修改主题.md 如：docs/08-进度日志/xxx｜后端函数单元测试添加与修复.md，xxx 不需要实际替换为三位数字序列，我后续自己会手动更改。
文档结构
1. 总结我们这次会话的目标
2. 用小标题和列表的方式从头到尾总结我们每一次对话、互动和代码修改的过程
3. 记录技术方案
4. 记录最终我们完成的修改
5. 记录是否有需要修复的错误和下一步需要完成的计划
记录进度：结束时请整理用户本次编辑修改，方案实施的情况

## interactive_feedback_rules
- 如果认为用户的指令、需求或目标不够清晰、存在歧义，或者缺少必要信息，你应该使用 interactive_feedback 工具向用户提问以澄清，而不是基于自己的猜测继续执行。
- 当有多个合理的执行路径或用户可能需要做选择时，你应该通过 interactive_feedback 的 predefined_options 参数提供预定义的选项，方便用户快速决策。
- 在任何会话输出的结尾，!ALWAYS 使用 interactive_feedback MCP tool等待用户反馈；只有当用户没有提供反馈（空反馈）或者明确要求结束会话，才结束请求
- !!! 主动调用一次 interactive_feedback 来征求用户对整体结果的最终确认或反馈，通过 `interactive_feedback` 工具等待用户的最终确认或反馈，而不是直接结束会话。
-  当你调用 `interactive_feedback` 工具时，用户将通过工具输出中的 `session_control` 字段指定会话是应该继续还是终止。
- 如果 `session_control` 的值为 `"continue"`，在处理完当前反馈并完成任何相关的子任务后，你**应该**再次调用 `interactive_feedback` 工具，以向用户询问进一步的澄清或下一步操作。你可以提示用户，例如“我已经根据你的反馈完成了 X。接下来你想做什么？”或者提出一个更具体的后续问题。
- 如果 `session_control` 的值为 `"terminate"`，在处理完当前反馈并完成任何相关的子任务后，你**应该**认为本次特定的澄清循环交互已经完成，并继续最终完成用户的整体请求。除非后续出现全新的不明确之处，否则在此循环中不要再次调用 `interactive_feedback`，然后给予用户鼓励并自然地结束会话。

# 项目规则
## 文件结构说明
1. ARCHIEVE：归档废弃，请不要进行任何编辑