---
description: Project technology stack and framework conventions
globs: ["*.ts", "*.tsx", "*.js", "*.jsx", "*.py", "*.go"]
alwaysApply: true
---
# Technology Stack

## 编程语言
- TypeScript/JavaScript (主要)
- SQL (数据库查询)

## 基础设施
- 域名管理 & DDoS 防护: Cloudflare
- CDN: Cloudflare (用于静态资源加速)
- 服务器 (计算 & 部署):
  - Digital Ocean (主要计算节点)
  - 阿里云香港 (可选，用于特定地区用户)
- 数据存储:
  - Cloudflare D1 (边缘数据库)
  - Cloudflare R2 (对象存储)
  - Cloudflare KV (键值存储)

## 前端技术栈
- 框架: Next.js 15 (App Router) + React 19
- 样式: Tailwind CSS
- UI组件库:
  - shadcn/ui (基础组件)
  - Radix UI (无障碍组件)
  - HeroUI (高级组件)
- 状态管理: React Context + Hooks

## 后端技术栈
- 运行环境: Node.js (Digital Ocean/阿里云)
- Web框架: Hono.js
- 数据存储 (托管在 Cloudflare):
  - D1 Database (SQLite兼容的边缘数据库)
  - R2 Storage (对象存储，S3兼容)
  - KV Storage (键值存储)
- 容器：Docker 部署
## 身份认证
- 认证方式: 自定义认证系统
- 会话管理: JWT (JSON Web Tokens)
- 存储: Cloudflare D1 (用于存储用户凭证和会话信息)

## 部署
- 前端: Digital Ocean/阿里云服务器 (通过 Nginx 等)
- 后端: Digital Ocean/阿里云服务器 (Node.js 运行时)

## 开发工具
- 包管理: npm/pnpm
- 代码质量:
  - TypeScript
  - ESLint
  - Prettier

## TDD 测试
1. Vitest
2. React Testing Library:
3. Cypress (可选):
