---
description: Code style and formatting conventions
globs: ["*.ts", "*.tsx", "*.js", "*.jsx"]
alwaysApply: true
---
# 将大型可访问路径的 page.tsx 文件分拆为多个小组件
**中心概念：组件化**
**核心要素：**
    *  **模块化 (Modularity):** 将大型、复杂的事物分解为更小、更独立、更易于管理和理解的模块 (组件)。
    *  **单一职责原则 (Single Responsibility Principle):**  每个模块 (组件) 应该只负责做好一件事情，职责清晰明确，避免功能混杂。
    *  **可复用性 (Reusability):**  设计出的模块 (组件) 应该可以在不同的场景和项目中重复使用，减少重复开发工作。
    *  **可维护性 (Maintainability):**  模块化和单一职责使得代码结构更清晰，更容易理解、修改和维护。
    *  **组合 (Composition):**  通过组合多个小的、独立的模块 (组件) 来构建更大的、更复杂的功能。
1. 提高代码的可维护性和可复用性，同时降低了单个文件的体积。
2. 采用组件化和单一职责原则进行重构：
    1. 按照功能将页面拆分为多个独立组件，在 xxx/components 下放置组件
    2. 将工具函数抽取到独立的工具文件中，在 xxx/utils 下放置工具函数
    3. 保证每个组件和工具函数只负责一个明确的功能
    4. 通过适当的参数传递实现组件间的数据流通
3. 主页面page.tsx主要负责：
    - 状态管理（loading状态、数据获取等）
    - 数据获取（通过服务函数获取数据）
    - 组装子组件（传递必要的属性和回调函数）
4. 数据流设计
    - 页面组件（page.tsx）管理全局状态并通过props向下传递
    - 每个子组件只接收必要的数据和回调函数
    - 子组件通过回调函数通知页面组件状态变化