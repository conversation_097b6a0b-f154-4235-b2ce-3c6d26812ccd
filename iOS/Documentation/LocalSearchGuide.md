# SenseWord本地搜索系统使用指南

## 概述

SenseWord本地搜索系统提供毫秒级响应的词典搜索功能，支持离线使用和自动数据同步。本指南将帮助您快速集成和使用本地搜索功能。

## 核心特性

- 🚀 **毫秒级响应**: 本地搜索响应时间 < 10ms
- 📱 **离线可用**: 完全支持离线搜索，无需网络连接
- 🔄 **自动同步**: 智能增量同步，最小化数据传输
- 🔍 **多种搜索**: 支持单词、定义、全文搜索
- 💾 **本地存储**: SQLite存储，高效索引优化
- 🛡️ **安全认证**: API Key保护，确保数据安全

## 快速开始

### 1. 基础集成

```swift
import CoreDataDomain
import SharedModels

// 创建本地搜索管理器
let searchManager = LocalSearchManager()

// 初始化（会自动检查并同步数据）
await searchManager.initialize()

// 执行搜索
let result = searchManager.search(query: "apple")
print("找到 \(result.totalMatches) 个结果，耗时 \(result.searchTimeText)")
```

### 2. SwiftUI集成

```swift
import SwiftUI
import CoreDataDomain

struct SearchView: View {
    @StateObject private var searchManager = LocalSearchManager()
    @State private var searchText = ""
    @State private var searchResults: [WordSummaryDTO] = []
    
    var body: some View {
        NavigationView {
            VStack {
                SearchBar(text: $searchText, onSearchButtonClicked: performSearch)
                
                if searchManager.isSyncing {
                    ProgressView("正在同步数据...")
                } else {
                    List(searchResults, id: \.id) { word in
                        WordRowView(word: word)
                    }
                }
            }
            .navigationTitle("词典搜索")
            .task {
                await searchManager.initialize()
            }
        }
    }
    
    private func performSearch() {
        let result = searchManager.search(query: searchText)
        searchResults = result.results
    }
}
```

## 详细使用

### LocalSearchManager

`LocalSearchManager`是本地搜索系统的核心管理器，协调搜索和同步功能。

#### 初始化

```swift
// 使用默认配置
let searchManager = LocalSearchManager()

// 自定义配置
let customSearchService = LocalSearchService()
let customAPIService = APIService.shared
let searchManager = LocalSearchManager(
    localSearchService: customSearchService,
    apiService: customAPIService
)
```

#### 状态监控

```swift
// 监控同步状态
searchManager.$isSyncing
    .sink { isSyncing in
        print("同步状态: \(isSyncing ? "进行中" : "已完成")")
    }
    .store(in: &cancellables)

// 监控数据统计
print("本地词汇总数: \(searchManager.totalWordsCount)")
print("同步状态: \(searchManager.syncStatusText)")
```

### 搜索功能

#### 基础搜索

```swift
// 全文搜索（默认）
let result = searchManager.search(query: "apple")

// 仅搜索单词
let wordResult = searchManager.searchWords("app")

// 仅搜索定义
let definitionResult = searchManager.searchDefinitions("fruit")

// 自定义搜索
let customResult = searchManager.search(
    query: "computer",
    maxResults: 50,
    searchType: .both
)
```

#### 搜索结果处理

```swift
let result = searchManager.search(query: "technology")

// 基础信息
print("查询: \(result.query)")
print("结果数: \(result.totalMatches)")
print("搜索时间: \(result.searchTimeText)")
print("摘要: \(result.summaryText)")

// 处理结果
for word in result.results {
    print("\(word.word) (\(word.difficulty)): \(word.definition)")
}

// 按难度分组
let groupedResults = result.resultsByDifficulty
for (difficulty, words) in groupedResults {
    print("\(difficulty)级别: \(words.count)个单词")
}
```

### 数据同步

#### 自动同步

```swift
// 初始化时自动检查并同步
await searchManager.initialize()

// 检查是否需要同步
if searchManager.shouldSync(maxAge: 3600) { // 1小时
    let result = await searchManager.performSync()
    print("同步结果: \(result.summaryText)")
}
```

#### 手动同步

```swift
// 手动触发同步
let syncResult = await searchManager.performSync()

if syncResult.success {
    print("同步成功: 新增 \(syncResult.newWordsCount) 个单词")
    print("总词汇数: \(syncResult.totalWordsCount)")
    print("同步耗时: \(syncResult.syncTimeText)")
} else {
    print("同步失败: \(syncResult.error?.localizedDescription ?? "未知错误")")
}
```

#### 同步状态管理

```swift
// 获取详细同步状态
let status = searchManager.getSyncStatus()
print("上次同步时间: \(status["lastSyncTime"] ?? 0)")
print("是否需要同步: \(status["shouldSync"] ?? false)")

// 重置本地数据
searchManager.resetLocalData()
```

### 高级功能

#### 搜索建议

```swift
// 获取搜索建议
let suggestions = searchManager.getSearchSuggestions("app")
// 返回: ["apple", "application", "approach", "appropriate", "appreciate"]
```

#### 性能监控

```swift
// 监控搜索性能
let result = searchManager.search(query: "complex query")
if result.searchTime > 10 {
    print("警告: 搜索时间超过10ms")
}

// 获取数据统计
let stats = searchManager.dataStatistics
print("数据统计: \(stats)")
```

#### 错误处理

```swift
// 监控同步错误
searchManager.$syncError
    .compactMap { $0 }
    .sink { error in
        switch error {
        case .networkError(let message):
            print("网络错误: \(message)")
        case .authenticationError(let message):
            print("认证错误: \(message)")
        case .dataParsingError(let message):
            print("数据解析错误: \(message)")
        case .storageError(let message):
            print("存储错误: \(message)")
        }
    }
    .store(in: &cancellables)
```

## 最佳实践

### 1. 初始化策略

```swift
// 应用启动时初始化
class AppDelegate: UIResponder, UIApplicationDelegate {
    let searchManager = LocalSearchManager()
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        Task {
            await searchManager.initialize()
        }
        
        return true
    }
}
```

### 2. 搜索优化

```swift
// 防抖搜索
class SearchViewModel: ObservableObject {
    @Published var searchText = ""
    @Published var searchResults: [WordSummaryDTO] = []
    
    private var searchTask: Task<Void, Never>?
    private let searchManager = LocalSearchManager()
    
    init() {
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] query in
                self?.performSearch(query: query)
            }
            .store(in: &cancellables)
    }
    
    private func performSearch(query: String) {
        searchTask?.cancel()
        
        searchTask = Task {
            let result = searchManager.search(query: query)
            
            await MainActor.run {
                self.searchResults = result.results
            }
        }
    }
}
```

### 3. 同步策略

```swift
// 智能同步策略
extension LocalSearchManager {
    func smartSync() async {
        // 检查网络状态
        guard NetworkMonitor.shared.isConnected else { return }
        
        // 检查是否需要同步
        guard shouldSync(maxAge: 3600) else { return }
        
        // 检查是否有本地数据
        if !hasLocalData {
            // 首次同步，显示进度
            await performSyncWithProgress()
        } else {
            // 增量同步，后台进行
            await performSync()
        }
    }
    
    private func performSyncWithProgress() async {
        // 显示同步进度UI
        await performSync()
    }
}
```

### 4. 内存管理

```swift
// 合理的搜索结果限制
let result = searchManager.search(
    query: query,
    maxResults: 20, // 避免过多结果占用内存
    searchType: .both
)

// 及时清理搜索结果
searchResults.removeAll()
```

## 故障排除

### 常见问题

1. **搜索无结果**
   - 检查是否已完成初始同步
   - 验证搜索关键词拼写
   - 尝试不同的搜索类型

2. **同步失败**
   - 检查网络连接
   - 验证API Key配置
   - 查看错误日志

3. **性能问题**
   - 检查本地数据库大小
   - 优化搜索查询
   - 限制搜索结果数量

### 调试技巧

```swift
// 启用详细日志
#if DEBUG
print("[LocalSearch] 搜索查询: \(query)")
print("[LocalSearch] 搜索结果: \(result.totalMatches)个")
print("[LocalSearch] 搜索耗时: \(result.searchTime)ms")
#endif

// 性能分析
let startTime = CFAbsoluteTimeGetCurrent()
let result = searchManager.search(query: query)
let endTime = CFAbsoluteTimeGetCurrent()
print("总耗时: \((endTime - startTime) * 1000)ms")
```

## 版本兼容性

- **iOS**: 15.0+
- **macOS**: 12.0+
- **Swift**: 5.5+
- **Xcode**: 13.0+

## 更新日志

- **v1.0** (2025-06-21): 初始版本，支持基础搜索和同步功能
