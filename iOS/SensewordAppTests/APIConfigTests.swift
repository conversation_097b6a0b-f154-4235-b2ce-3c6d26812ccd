//
//  APIConfigTests.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import XCTest
@testable import SensewordApp

/// API配置测试类
/// 为[FC-02]和[FC-03] APIConfig头部生成器实现补间测试和变体测试
class APIConfigTests: XCTestCase {
    
    // MARK: - 补间测试 (Tweening Tests) - 验证核心功能正确性
    
    /// 补间测试1: [FC-02] 静态头部生成器 - 基础功能验证
    func testAPIConfig_StaticHeaders_ReturnsCorrectHeaders() {
        // When - 获取静态头部
        let headers = APIConfig.staticHeaders
        
        // Then - 验证输出结构完全匹配契约定义
        XCTAssertEqual(headers.count, 1)
        XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
    }
    
    /// 补间测试2: [FC-02] 静态头部生成器 - 多次调用一致性
    func testAPIConfig_StaticHeaders_ConsistentAcrossMultipleCalls() {
        // When - 多次获取静态头部
        let headers1 = APIConfig.staticHeaders
        let headers2 = APIConfig.staticHeaders
        let headers3 = APIConfig.staticHeaders
        
        // Then - 验证所有调用结果一致
        XCTAssertEqual(headers1, headers2)
        XCTAssertEqual(headers2, headers3)
        XCTAssertEqual(headers1["X-Static-API-Key"], headers3["X-Static-API-Key"])
    }
    
    /// 补间测试3: [FC-03] 双重认证头部生成器 - 正常session ID
    func testAPIConfig_AuthHeaders_ReturnsCorrectHeaders() {
        // Given - 正常的session ID
        let sessionId = "sess_1a2b3c4d5e6f7g8h9i0j"
        
        // When - 生成双重认证头部
        let headers = APIConfig.authHeaders(sessionId: sessionId)
        
        // Then - 验证输出结构完全匹配契约定义
        XCTAssertEqual(headers.count, 2)
        XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
        XCTAssertEqual(headers["Authorization"], "Bearer sess_1a2b3c4d5e6f7g8h9i0j")
    }
    
    /// 补间测试4: [FC-03] 双重认证头部生成器 - 不同session ID格式
    func testAPIConfig_AuthHeaders_DifferentSessionIdFormats() {
        // Given - 不同格式的session ID
        let sessionIds = [
            "short_session",
            "very_long_session_id_with_many_characters_12345",
            "session-with-dashes",
            "session_with_underscores",
            "SessionWithCamelCase",
            "session123with456numbers"
        ]
        
        // When & Then - 验证每个session ID都能正确处理
        for sessionId in sessionIds {
            let headers = APIConfig.authHeaders(sessionId: sessionId)
            
            XCTAssertEqual(headers.count, 2)
            XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
            XCTAssertEqual(headers["Authorization"], "Bearer \(sessionId)")
        }
    }
    
    /// 补间测试5: [FC-03] 双重认证头部生成器 - 包含静态头部内容
    func testAPIConfig_AuthHeaders_ContainsStaticHeaders() {
        // Given
        let sessionId = "test_session_123"
        
        // When
        let authHeaders = APIConfig.authHeaders(sessionId: sessionId)
        let staticHeaders = APIConfig.staticHeaders
        
        // Then - 确保双重认证头部包含所有静态头部内容
        for (key, value) in staticHeaders {
            XCTAssertEqual(authHeaders[key], value)
        }
    }
    
    // MARK: - 变体测试 (Variant Tests) - 验证边界情况和错误处理
    
    /// 变体测试1: [FC-03] 空字符串session ID
    func testAPIConfig_AuthHeaders_EmptySessionId() {
        // Given - 空字符串session ID
        let sessionId = ""
        
        // When - 生成双重认证头部
        let headers = APIConfig.authHeaders(sessionId: sessionId)
        
        // Then - 应该仍然生成有效的头部结构
        XCTAssertEqual(headers.count, 2)
        XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
        XCTAssertEqual(headers["Authorization"], "Bearer ")
    }
    
    /// 变体测试2: [FC-03] 包含特殊字符的session ID
    func testAPIConfig_AuthHeaders_SpecialCharactersInSessionId() {
        // Given - 包含特殊字符的session ID
        let sessionIds = [
            "session@with#special$chars",
            "session with spaces",
            "session\nwith\nnewlines",
            "session\twith\ttabs",
            "session'with\"quotes",
            "session&with<xml>chars"
        ]
        
        // When & Then - 验证特殊字符被正确处理
        for sessionId in sessionIds {
            let headers = APIConfig.authHeaders(sessionId: sessionId)
            
            XCTAssertEqual(headers.count, 2)
            XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
            XCTAssertEqual(headers["Authorization"], "Bearer \(sessionId)")
        }
    }
    
    /// 变体测试3: [FC-03] 极长session ID
    func testAPIConfig_AuthHeaders_VeryLongSessionId() {
        // Given - 极长的session ID (1000个字符)
        let longSessionId = String(repeating: "a", count: 1000)
        
        // When - 生成双重认证头部
        let headers = APIConfig.authHeaders(sessionId: longSessionId)
        
        // Then - 应该正确处理长字符串
        XCTAssertEqual(headers.count, 2)
        XCTAssertEqual(headers["X-Static-API-Key"], "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
        XCTAssertEqual(headers["Authorization"], "Bearer \(longSessionId)")
        XCTAssertEqual(headers["Authorization"]?.count, 1007) // "Bearer " + 1000个字符
    }
    
    /// 变体测试4: 基础URL配置验证
    func testAPIConfig_BaseURLs_AreValid() {
        // When & Then - 验证预定义的URL是有效的
        XCTAssertNotNil(URL(string: APIConfig.authBaseURL))
        XCTAssertNotNil(URL(string: APIConfig.apiBaseURL))
        
        XCTAssertEqual(APIConfig.authBaseURL, "https://auth.senseword.app")
        XCTAssertEqual(APIConfig.apiBaseURL, "https://api.senseword.app")
    }
    
    /// 变体测试5: 静态API密钥格式验证
    func testAPIConfig_StaticAPIKey_HasCorrectFormat() {
        // When
        let apiKey = APIConfig.staticAPIKey
        
        // Then - 验证API密钥格式符合预期
        XCTAssertTrue(apiKey.hasPrefix("sk-senseword-"))
        XCTAssertTrue(apiKey.hasSuffix("-2025"))
        XCTAssertEqual(apiKey, "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025")
        XCTAssertEqual(apiKey.count, 56) // 验证长度
    }
} 