//
//  AdapterContainerTests.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import XCTest
@testable import SensewordApp

/// DIContainer测试类
/// 为[FC-04] DIContainer网络客户端工厂实现补间测试和变体测试
class DIContainerTests: XCTestCase {

    var container: DIContainer!
    
    override func setUp() {
        super.setUp()
        container = DIContainer.shared
    }
    
    override func tearDown() {
        container = nil
        super.tearDown()
    }
    
    // MARK: - 补间测试 (Tweening Tests) - 验证核心功能正确性
    
    /// 补间测试1: [FC-04] 认证API客户端创建
    func testDIContainer_AuthAPIClient_CreatesCorrectInstance() {
        // When - 获取认证API客户端
        let authClient = container.authAPIClient
        
        // Then - 验证客户端类型和配置
        XCTAssertTrue(authClient is APIClient)
        
        // 通过类型转换验证内部配置
        if let apiClient = authClient as? APIClient {
            // 验证基础URL配置正确（通过反射或者测试调用验证）
            // 这里我们通过APIConfig验证URL配置
            XCTAssertEqual(APIConfig.authBaseURL, "https://auth.senseword.app")
        }
    }
    
    /// 补间测试2: [FC-04] 主API客户端创建
    func testDIContainer_MainAPIClient_CreatesCorrectInstance() {
        // When - 获取主API客户端
        let mainClient = container.mainAPIClient
        
        // Then - 验证客户端类型和配置
        XCTAssertTrue(mainClient is APIClient)
        
        // 验证配置正确
        if let apiClient = mainClient as? APIClient {
            XCTAssertEqual(APIConfig.apiBaseURL, "https://api.senseword.app")
        }
    }
    
    /// 补间测试3: [FC-04] 单例模式验证
    func testDIContainer_Shared_ReturnsSameInstance() {
        // When - 多次获取共享实例
        let container1 = DIContainer.shared
        let container2 = DIContainer.shared
        let container3 = DIContainer.shared
        
        // Then - 验证返回相同的实例
        XCTAssertTrue(container1 === container2)
        XCTAssertTrue(container2 === container3)
        XCTAssertTrue(container1 === container3)
    }
    
    /// 补间测试4: [FC-04] 懒加载验证 - 认证客户端
    func testDIContainer_AuthAPIClient_LazyInitialization() {
        // When - 多次访问认证客户端
        let authClient1 = container.authAPIClient
        let authClient2 = container.authAPIClient
        let authClient3 = container.authAPIClient
        
        // Then - 验证返回相同的实例（懒加载特性）
        // 将协议类型转换为具体类型以进行身份比较
        if let client1 = authClient1 as? APIClient,
           let client2 = authClient2 as? APIClient,
           let client3 = authClient3 as? APIClient {
            XCTAssertTrue(client1 === client2)
            XCTAssertTrue(client2 === client3)
            XCTAssertTrue(client1 === client3)
        } else {
            XCTFail("AuthAPIClient应该是APIClient类型")
        }
    }
    
    /// 补间测试5: [FC-04] 懒加载验证 - 主API客户端
    func testDIContainer_MainAPIClient_LazyInitialization() {
        // When - 多次访问主API客户端
        let mainClient1 = container.mainAPIClient
        let mainClient2 = container.mainAPIClient
        let mainClient3 = container.mainAPIClient
        
        // Then - 验证返回相同的实例（懒加载特性）
        // 将协议类型转换为具体类型以进行身份比较
        if let client1 = mainClient1 as? APIClient,
           let client2 = mainClient2 as? APIClient,
           let client3 = mainClient3 as? APIClient {
            XCTAssertTrue(client1 === client2)
            XCTAssertTrue(client2 === client3)
            XCTAssertTrue(client1 === client3)
        } else {
            XCTFail("MainAPIClient应该是APIClient类型")
        }
    }
    
    /// 补间测试6: [FC-04] 不同客户端实例独立性
    func testDIContainer_DifferentClients_AreIndependent() {
        // When - 获取两个不同的客户端
        let authClient = container.authAPIClient
        let mainClient = container.mainAPIClient
        
        // Then - 验证它们是不同的实例
        if let authAPIClient = authClient as? APIClient,
           let mainAPIClient = mainClient as? APIClient {
            XCTAssertFalse(authAPIClient === mainAPIClient)
        } else {
            XCTFail("客户端应该是APIClient类型")
        }
        
        // 验证两个客户端都符合协议
        XCTAssertTrue(authClient is APIClientProtocol)
        XCTAssertTrue(mainClient is APIClientProtocol)
    }
    
    // MARK: - 变体测试 (Variant Tests) - 验证边界情况
    
    /// 变体测试1: 并发访问安全性测试
    func testDIContainer_ConcurrentAccess_ThreadSafe() async {
        // Given - 创建并发任务
        let expectation = XCTestExpectation(description: "Concurrent access completed")
        expectation.expectedFulfillmentCount = 10
        
        // When - 并发访问容器
        for _ in 0..<10 {
            Task {
                let authClient = container.authAPIClient
                let mainClient = container.mainAPIClient
                
                // 验证客户端有效
                XCTAssertNotNil(authClient)
                XCTAssertNotNil(mainClient)
                
                expectation.fulfill()
            }
        }
        
        // Then - 等待所有并发任务完成
        await fulfillment(of: [expectation], timeout: 5.0)
    }
    
    /// 变体测试2: 内存压力测试
    func testDIContainer_MemoryPressure_HandlesMultipleReferences() {
        // Given - 创建多个引用
        var authClients: [APIClientProtocol] = []
        var mainClients: [APIClientProtocol] = []
        
        // When - 创建大量引用
        for _ in 0..<100 {
            authClients.append(container.authAPIClient)
            mainClients.append(container.mainAPIClient)
        }
        
        // Then - 验证所有引用指向相同实例
        let firstAuthClient = authClients.first!
        let firstMainClient = mainClients.first!
        
        // 转换为具体类型进行身份比较
        guard let firstAuth = firstAuthClient as? APIClient,
              let firstMain = firstMainClient as? APIClient else {
            XCTFail("客户端应该是APIClient类型")
            return
        }
        
        for authClient in authClients {
            if let apiClient = authClient as? APIClient {
                XCTAssertTrue(apiClient === firstAuth)
            } else {
                XCTFail("所有认证客户端都应该是APIClient类型")
            }
        }
        
        for mainClient in mainClients {
            if let apiClient = mainClient as? APIClient {
                XCTAssertTrue(apiClient === firstMain)
            } else {
                XCTFail("所有主API客户端都应该是APIClient类型")
            }
        }
    }
    
    /// 变体测试3: 配置验证
    func testDIContainer_Configuration_UsesCorrectURLs() {
        // When - 获取客户端并验证配置
        let authClient = container.authAPIClient
        let mainClient = container.mainAPIClient
        
        // Then - 验证基础URL配置
        XCTAssertNotNil(authClient)
        XCTAssertNotNil(mainClient)
        
        // 验证APIConfig中的URL配置是有效的
        XCTAssertFalse(APIConfig.authBaseURL.isEmpty)
        XCTAssertFalse(APIConfig.apiBaseURL.isEmpty)
        XCTAssertTrue(APIConfig.authBaseURL.hasPrefix("https://"))
        XCTAssertTrue(APIConfig.apiBaseURL.hasPrefix("https://"))
    }
} 