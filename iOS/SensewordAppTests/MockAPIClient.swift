//
//  MockAPIClient.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import Foundation
@testable import SensewordApp

/// Mock API客户端
/// 提供与APIClient相同接口的Mock实现，用于单元测试和离线开发
/// 注意：实现APIClientProtocol的基础版本，参数无默认值（与APIClient的扩展版本不同）
class MockAPIClient: APIClientProtocol {
    /// 内部状态：预设的Mock响应数据
    private var mockResponses: [String: Any] = [:]
    
    /// 内部状态：模拟的错误
    var shouldThrowError: APIError?
    
    // MARK: - 测试验证属性
    /// 记录最后一次调用的参数，用于测试验证
    var lastEndpoint: String?
    var lastMethod: HTTPMethod?
    var lastHeaders: [String: String]?
    var lastBody: Data?
    
    /// 新的测试模式属性
    var mockResponse: Any?
    var shouldSucceed: Bool = true
    var mockError: APIError?
    
    /// [FC-05]: MockAPIClient测试请求接口
    /// 职责: 提供与APIClient相同接口的Mock实现，用于单元测试和离线开发
    /// 输入: endpoint: String, method: HTTPMethod, headers: [String: String]?, body: Data?
    /// 注意：MockAPIClient的参数没有默认值（与APIClient不同）
    /// 输出: T where T: Codable 或抛出预设的Mock错误
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        headers: [String: String]?,
        body: Data?
    ) async throws -> T {
        // 记录调用参数用于测试验证
        lastEndpoint = endpoint
        lastMethod = method
        lastHeaders = headers
        lastBody = body
        
        // 检查新的测试模式
        if !shouldSucceed {
            if let error = mockError {
                throw error
            }
            if let error = shouldThrowError {
                throw error
            }
            throw APIError.invalidResponse
        }
        
        // 使用新的mockResponse属性
        if let response = mockResponse {
            do {
                let data = try JSONSerialization.data(withJSONObject: response)
                let decoder = JSONDecoder()
                return try decoder.decode(T.self, from: data)
            } catch {
                // 如果是Codable对象，直接返回
                if let codableResponse = response as? T {
                    return codableResponse
                }
                throw APIError.decodingError(error)
            }
        }
        
        // 回退到原有逻辑
        if let error = shouldThrowError {
            throw error
        }
        
        // 查找预设的响应数据
        guard let mockData = mockResponses[endpoint] else {
            throw APIError.invalidResponse
        }
        
        // 尝试将Mock数据转换为JSON数据
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: mockData)
            let decoder = JSONDecoder()
            let response = try decoder.decode(T.self, from: jsonData)
            return response
        } catch {
            throw APIError.decodingError(error)
        }
    }
    
    /// [FC-06]: MockAPIClient响应配置器
    /// 职责: 为特定端点设置Mock响应数据，支持测试用例的数据准备
    /// 输入: endpoint: String, response: T where T: Codable
    /// 输出: 无返回值（配置内部状态）
    func setMockResponse<T: Codable>(for endpoint: String, response: T) {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(response)
            let json = try JSONSerialization.jsonObject(with: data)
            mockResponses[endpoint] = json
        } catch {
            // 如果编码失败，静默处理（测试环境）
            print("Warning: Failed to set mock response for endpoint \(endpoint): \(error)")
        }
    }
    
    /// 清除所有Mock响应数据（测试辅助方法）
    func clearAllMockResponses() {
        mockResponses.removeAll()
        shouldThrowError = nil
        mockResponse = nil
        shouldSucceed = true
        mockError = nil
        lastEndpoint = nil
        lastMethod = nil
        lastHeaders = nil
        lastBody = nil
    }
} 