//
//  MockAPIClientTests.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import XCTest
@testable import SensewordApp

/// MockAPIClient测试类
/// 为[FC-05]和[FC-06] MockAPIClient实现补间测试和变体测试
class MockAPIClientTests: XCTestCase {
    
    var mockClient: MockAPIClient!
    
    override func setUp() {
        super.setUp()
        mockClient = MockAPIClient()
    }
    
    override func tearDown() {
        mockClient.clearAllMockResponses()
        mockClient = nil
        super.tearDown()
    }
    
    // MARK: - 补间测试 (Tweening Tests) - 验证核心功能正确性
    
    /// 补间测试1: [FC-06] 响应配置器 + [FC-05] 请求接口 - 基础流程
    func testMockAPIClient_SetAndGetResponse_BasicFlow() async throws {
        // Given - 准备测试数据
        let testResponse = TestResponse(message: "mock success", status: 200)
        
        // [FC-06] 设置Mock响应
        mockClient.setMockResponse(for: "/test", response: testResponse)
        
        // When - [FC-05] 执行Mock请求
        let result: TestResponse = try await mockClient.request(
            endpoint: "/test",
            method: .GET,
            headers: nil,
            body: nil
        )
        
        // Then - 验证结果完全匹配
        XCTAssertEqual(result.message, "mock success")
        XCTAssertEqual(result.status, 200)
    }
    
    /// 补间测试2: [FC-05] 不同HTTP方法支持
    func testMockAPIClient_DifferentHTTPMethods_AllSupported() async throws {
        // Given - 为不同方法设置响应
        let getResponse = TestResponse(message: "GET response", status: 200)
        let postResponse = TestResponse(message: "POST response", status: 201)
        let deleteResponse = TestResponse(message: "DELETE response", status: 204)
        
        mockClient.setMockResponse(for: "/get", response: getResponse)
        mockClient.setMockResponse(for: "/post", response: postResponse)
        mockClient.setMockResponse(for: "/delete", response: deleteResponse)
        
        // When & Then - 验证所有HTTP方法
        let getResult: TestResponse = try await mockClient.request(
            endpoint: "/get",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(getResult.message, "GET response")
        
        let postResult: TestResponse = try await mockClient.request(
            endpoint: "/post",
            method: .POST,
            headers: ["Content-Type": "application/json"],
            body: Data()
        )
        XCTAssertEqual(postResult.message, "POST response")
        
        let deleteResult: TestResponse = try await mockClient.request(
            endpoint: "/delete",
            method: .DELETE,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(deleteResult.message, "DELETE response")
    }
    
    /// 补间测试3: [FC-06] 复杂数据结构支持
    func testMockAPIClient_ComplexDataStructures_SupportedCorrectly() async throws {
        // Given - 复杂的嵌套数据结构
        let complexResponse = ComplexTestResponse(
            id: "test_123",
            data: ComplexData(
                numbers: [1, 2, 3, 4, 5],
                nested: NestedData(
                    flag: true,
                    description: "Complex nested structure"
                )
            ),
            metadata: [
                "key1": "value1",
                "key2": "value2"
            ]
        )
        
        // [FC-06] 设置复杂响应
        mockClient.setMockResponse(for: "/complex", response: complexResponse)
        
        // When - [FC-05] 获取复杂响应
        let result: ComplexTestResponse = try await mockClient.request(
            endpoint: "/complex",
            method: .GET,
            headers: nil,
            body: nil
        )
        
        // Then - 验证复杂结构完整性
        XCTAssertEqual(result.id, "test_123")
        XCTAssertEqual(result.data.numbers, [1, 2, 3, 4, 5])
        XCTAssertEqual(result.data.nested.flag, true)
        XCTAssertEqual(result.data.nested.description, "Complex nested structure")
        XCTAssertEqual(result.metadata["key1"], "value1")
        XCTAssertEqual(result.metadata["key2"], "value2")
    }
    
    /// 补间测试4: [FC-05] 错误模拟功能
    func testMockAPIClient_ErrorSimulation_WorksCorrectly() async {
        // Given - 设置要抛出的错误
        mockClient.shouldThrowError = APIError.unauthorized
        
        // When & Then - 验证错误被正确抛出
        do {
            let _: TestResponse = try await mockClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出unauthorized错误")
        } catch APIError.unauthorized {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.unauthorized，但抛出了: \(error)")
        }
    }
    
    /// 补间测试5: [FC-06] 多端点响应管理
    func testMockAPIClient_MultipleEndpoints_ManagedSeparately() async throws {
        // Given - 为多个端点设置不同响应
        let userResponse = TestResponse(message: "user data", status: 200)
        let productResponse = TestResponse(message: "product data", status: 200)
        let orderResponse = TestResponse(message: "order data", status: 200)
        
        mockClient.setMockResponse(for: "/users", response: userResponse)
        mockClient.setMockResponse(for: "/products", response: productResponse)
        mockClient.setMockResponse(for: "/orders", response: orderResponse)
        
        // When & Then - 验证每个端点返回正确响应
        let userResult: TestResponse = try await mockClient.request(
            endpoint: "/users",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(userResult.message, "user data")
        
        let productResult: TestResponse = try await mockClient.request(
            endpoint: "/products",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(productResult.message, "product data")
        
        let orderResult: TestResponse = try await mockClient.request(
            endpoint: "/orders",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(orderResult.message, "order data")
    }
    
    // MARK: - 变体测试 (Variant Tests) - 验证边界情况和错误处理
    
    /// 变体测试1: [FC-05] 未配置端点的处理
    func testMockAPIClient_UnconfiguredEndpoint_ThrowsInvalidResponse() async {
        // Given - 没有为端点设置响应
        
        // When & Then - 应该抛出invalidResponse错误
        do {
            let _: TestResponse = try await mockClient.request(
                endpoint: "/unconfigured",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出invalidResponse错误")
        } catch APIError.invalidResponse {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.invalidResponse，但抛出了: \(error)")
        }
    }
    
    /// 变体测试2: [FC-06] 无效响应数据处理
    func testMockAPIClient_InvalidResponseType_HandlesGracefully() async throws {
        // Given - 设置一个响应，然后尝试用错误类型解析
        let stringResponse = TestResponse(message: "string response", status: 200)
        mockClient.setMockResponse(for: "/test", response: stringResponse)
        
        // When & Then - 尝试用错误类型解析应该失败
        do {
            let _: ComplexTestResponse = try await mockClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出decodingError错误")
        } catch APIError.decodingError {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.decodingError，但抛出了: \(error)")
        }
    }
    
    /// 变体测试3: [FC-05] 错误优先级测试
    func testMockAPIClient_ErrorPriority_ErrorTakesPrecedence() async {
        // Given - 同时设置响应和错误
        let response = TestResponse(message: "should not be returned", status: 200)
        mockClient.setMockResponse(for: "/test", response: response)
        mockClient.shouldThrowError = APIError.networkError(NSError(domain: "test", code: 1, userInfo: nil))
        
        // When & Then - 错误应该优先于响应
        do {
            let _: TestResponse = try await mockClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出networkError错误")
        } catch APIError.networkError {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.networkError，但抛出了: \(error)")
        }
    }
    
    /// 变体测试4: [FC-06] 响应覆盖测试
    func testMockAPIClient_ResponseOverwrite_UpdatesCorrectly() async throws {
        // Given - 为同一端点设置初始响应
        let initialResponse = TestResponse(message: "initial", status: 200)
        mockClient.setMockResponse(for: "/test", response: initialResponse)
        
        // 验证初始响应
        let initialResult: TestResponse = try await mockClient.request(
            endpoint: "/test",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(initialResult.message, "initial")
        
        // When - 覆盖响应
        let updatedResponse = TestResponse(message: "updated", status: 201)
        mockClient.setMockResponse(for: "/test", response: updatedResponse)
        
        // Then - 验证响应被更新
        let updatedResult: TestResponse = try await mockClient.request(
            endpoint: "/test",
            method: .GET,
            headers: nil,
            body: nil
        )
        XCTAssertEqual(updatedResult.message, "updated")
        XCTAssertEqual(updatedResult.status, 201)
    }
    
    /// 变体测试5: 清理功能测试
    func testMockAPIClient_ClearAllResponses_RemovesAllData() async {
        // Given - 设置多个响应和错误
        let response1 = TestResponse(message: "response1", status: 200)
        let response2 = TestResponse(message: "response2", status: 200)
        
        mockClient.setMockResponse(for: "/test1", response: response1)
        mockClient.setMockResponse(for: "/test2", response: response2)
        mockClient.shouldThrowError = APIError.unauthorized
        
        // When - 清理所有响应
        mockClient.clearAllMockResponses()
        
        // Then - 验证所有响应被清理
        do {
            let _: TestResponse = try await mockClient.request(
                endpoint: "/test1",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出invalidResponse错误")
        } catch APIError.invalidResponse {
            // 预期的错误，说明响应被清理
        } catch {
            XCTFail("应该抛出APIError.invalidResponse，但抛出了: \(error)")
        }
        
        // 验证错误状态也被清理
        XCTAssertNil(mockClient.shouldThrowError)
    }
}

// MARK: - 测试辅助数据结构

/// 复杂测试响应结构
struct ComplexTestResponse: Codable {
    let id: String
    let data: ComplexData
    let metadata: [String: String]
}

struct ComplexData: Codable {
    let numbers: [Int]
    let nested: NestedData
}

struct NestedData: Codable {
    let flag: Bool
    let description: String
} 