//
//  APIClientTests.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import XCTest
@testable import SensewordApp

/// API客户端测试类
/// 为[FC-01] APIClient统一请求接口实现补间测试和变体测试
class APIClientTests: XCTestCase {
    
    var mockSession: MockURLSessionProtocol!
    var apiClient: TestableAPIClient!
    
    override func setUp() {
        super.setUp()
        mockSession = MockURLSessionImpl()
        apiClient = TestableAPIClient(baseURL: "https://test.example.com", session: mockSession)
    }
    
    override func tearDown() {
        mockSession = nil
        apiClient = nil
        super.tearDown()
    }
    
    // MARK: - 补间测试 (Tweening Tests) - 验证核心转换逻辑
    
    /// 补间测试1: GET请求成功场景
    func testAPIClient_GetRequest_Success() async throws {
        // Given - 准备测试数据
        let testResponse = TestResponse(message: "success", status: 200)
        let responseData = try JSONEncoder().encode(testResponse)
        
        mockSession.data = responseData
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/test")!,
            statusCode: 200,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When - 执行请求
        let result: TestResponse = try await apiClient.request(
            endpoint: "/test",
            method: .GET,
            headers: ["Content-Type": "application/json"],
            body: nil
        )
        
        // Then - 验证结果
        XCTAssertEqual(result.message, "success")
        XCTAssertEqual(result.status, 200)
    }
    
    /// 补间测试2: POST请求带body成功场景
    func testAPIClient_PostRequestWithBody_Success() async throws {
        // Given
        let requestBody = TestRequest(name: "test", value: 123)
        let requestData = try JSONEncoder().encode(requestBody)
        
        let testResponse = TestResponse(message: "created", status: 201)
        let responseData = try JSONEncoder().encode(testResponse)
        
        mockSession.data = responseData
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/create")!,
            statusCode: 201,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When
        let result: TestResponse = try await apiClient.request(
            endpoint: "/create",
            method: .POST,
            headers: ["Content-Type": "application/json"],
            body: requestData
        )
        
        // Then
        XCTAssertEqual(result.message, "created")
        XCTAssertEqual(result.status, 201)
    }
    
    /// 补间测试3: DELETE请求成功场景
    func testAPIClient_DeleteRequest_Success() async throws {
        // Given
        let testResponse = TestResponse(message: "deleted", status: 200)
        let responseData = try JSONEncoder().encode(testResponse)
        
        mockSession.data = responseData
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/delete/123")!,
            statusCode: 200,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When
        let result: TestResponse = try await apiClient.request(
            endpoint: "/delete/123",
            method: .DELETE,
            headers: nil,
            body: nil
        )
        
        // Then
        XCTAssertEqual(result.message, "deleted")
        XCTAssertEqual(result.status, 200)
    }
    
    // MARK: - 变体测试 (Variant Tests) - 验证错误处理和边界情况
    
    /// 变体测试1: 无效URL处理
    func testAPIClient_InvalidURL_ThrowsInvalidURLError() async {
        // Given - 使用无效的baseURL创建客户端
        let invalidClient = TestableAPIClient(baseURL: "invalid://url", session: mockSession)
        
        // When & Then
        do {
            let _: TestResponse = try await invalidClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出invalidURL错误")
        } catch APIError.invalidURL {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.invalidURL，但抛出了: \(error)")
        }
    }
    
    /// 变体测试2: 401未授权响应
    func testAPIClient_UnauthorizedResponse_ThrowsUnauthorizedError() async {
        // Given
        mockSession.data = "Unauthorized".data(using: .utf8)!
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/test")!,
            statusCode: 401,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When & Then
        do {
            let _: TestResponse = try await apiClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出unauthorized错误")
        } catch APIError.unauthorized {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.unauthorized，但抛出了: \(error)")
        }
    }
    
    /// 变体测试3: 403禁止访问（API密钥无效）
    func testAPIClient_ForbiddenResponse_ThrowsInvalidAPIKeyError() async {
        // Given
        mockSession.data = "Invalid API Key".data(using: .utf8)!
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/test")!,
            statusCode: 403,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When & Then
        do {
            let _: TestResponse = try await apiClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出invalidAPIKey错误")
        } catch APIError.invalidAPIKey {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.invalidAPIKey，但抛出了: \(error)")
        }
    }
    
    /// 变体测试4: 服务器错误响应
    func testAPIClient_ServerErrorResponse_ThrowsServerError() async {
        // Given
        let errorMessage = "Internal Server Error"
        mockSession.data = errorMessage.data(using: .utf8)!
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/test")!,
            statusCode: 500,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When & Then
        do {
            let _: TestResponse = try await apiClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出serverError错误")
        } catch APIError.serverError(let statusCode, let message) {
            XCTAssertEqual(statusCode, 500)
            XCTAssertEqual(message, errorMessage)
        } catch {
            XCTFail("应该抛出APIError.serverError，但抛出了: \(error)")
        }
    }
    
    /// 变体测试5: JSON解析错误
    func testAPIClient_InvalidJSONResponse_ThrowsDecodingError() async {
        // Given - 无效的JSON数据
        mockSession.data = "invalid json data".data(using: .utf8)!
        mockSession.response = HTTPURLResponse(
            url: URL(string: "https://test.example.com/test")!,
            statusCode: 200,
            httpVersion: nil,
            headerFields: nil
        )
        
        // When & Then
        do {
            let _: TestResponse = try await apiClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出decodingError错误")
        } catch APIError.decodingError {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.decodingError，但抛出了: \(error)")
        }
    }
    
    /// 变体测试6: 网络连接错误
    func testAPIClient_NetworkError_ThrowsNetworkError() async {
        // Given
        let networkError = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet, userInfo: nil)
        mockSession.error = networkError
        
        // When & Then
        do {
            let _: TestResponse = try await apiClient.request(
                endpoint: "/test",
                method: .GET,
                headers: nil,
                body: nil
            )
            XCTFail("应该抛出networkError错误")
        } catch APIError.networkError {
            // 预期的错误
        } catch {
            XCTFail("应该抛出APIError.networkError，但抛出了: \(error)")
        }
    }
}

// MARK: - 测试辅助数据结构

/// 测试用的请求数据结构
struct TestRequest: Codable {
    let name: String
    let value: Int
}

/// 测试用的响应数据结构
struct TestResponse: Codable {
    let message: String
    let status: Int
}

// MARK: - 测试专用的Mock和协议

/// URLSession协议抽象
protocol MockURLSessionProtocol {
    var data: Data? { get set }
    var response: URLResponse? { get set }
    var error: Error? { get set }
    
    func data(for request: URLRequest) async throws -> (Data, URLResponse)
}

/// Mock URLSession实现
class MockURLSessionImpl: MockURLSessionProtocol {
    var data: Data?
    var response: URLResponse?
    var error: Error?
    
    func data(for request: URLRequest) async throws -> (Data, URLResponse) {
        if let error = error {
            throw error
        }
        
        let responseData = data ?? Data()
        let urlResponse = response ?? HTTPURLResponse(
            url: request.url!,
            statusCode: 200,
            httpVersion: nil,
            headerFields: nil
        )!
        
        return (responseData, urlResponse)
    }
}

/// 可测试的APIClient版本
class TestableAPIClient: APIClientProtocol {
    private let baseURL: String
    private let session: MockURLSessionProtocol
    
    init(baseURL: String, session: MockURLSessionProtocol) {
        self.baseURL = baseURL
        self.session = session
    }
    
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil
    ) async throws -> T {
        // 1. 构建完整URL
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidURL
        }
        
        // 2. 创建URLRequest
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        
        // 3. 设置HTTP头部
        if let headers = headers {
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        // 4. 执行网络请求
        do {
            let (data, response) = try await session.data(for: request)
            
            // 5. 验证HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }
            
            // 6. 处理HTTP状态码
            switch httpResponse.statusCode {
            case 200...299:
                // 成功响应，继续JSON解析
                break
            case 401:
                throw APIError.unauthorized
            case 403:
                throw APIError.invalidAPIKey
            default:
                let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown server error"
                throw APIError.serverError(httpResponse.statusCode, errorMessage)
            }
            
            // 7. JSON解析
            do {
                let decoder = JSONDecoder()
                let result = try decoder.decode(T.self, from: data)
                return result
            } catch {
                throw APIError.decodingError(error)
            }
            
        } catch let error as APIError {
            // 重新抛出APIError类型的错误
            throw error
        } catch {
            // 包装其他网络错误
            throw APIError.networkError(error)
        }
    }
} 