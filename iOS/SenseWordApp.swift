#if os(iOS)
import SwiftUI

// Import UI Components
import UIComponents

/// SenseWord 主应用视图
/// 集成搜索覆盖层的完整应用界面
/// 架构特点：遵循Apple性能指南，零启动成本，按需初始化
@available(iOS 15.0, *)
struct SenseWordMainView: View {

    var body: some View {
        // 遵循Apple指南：应用启动时不执行任何耗时操作
        // 搜索服务将在用户手势触发时按需初始化
        NavigationStack {
            MainContentView()
                .navigationBarHidden(true)
        }
    }

}



@main
struct SenseWordApp: App {
    var body: some Scene {
        WindowGroup {
            // 使用基于最佳实践的主界面
            SenseWordMainView()
                .background(
                    // 隐藏的键盘预热组件
                    KeyboardPreloader()
                        .frame(width: 0, height: 0)
                        .opacity(0)
                )
                .onAppear {
                    NSLog("🚀 SenseWordApp: 应用已启动，采用零启动成本架构")
                    NSLog("🚀 功能配置: 主界面 + 键盘预热 + 后台预初始化搜索功能")
                    NSLog("🚀 性能优化: 遵循Apple指南，主线程专注UI渲染")



                    // 在应用启动后立即在后台开始预初始化搜索服务和索引同步
                    // 这样当用户真正需要时，服务已经准备就绪，索引也已同步完成
                    Task.detached(priority: .userInitiated) {
                        // 延迟1秒开始，确保应用启动完成
                        try? await Task.sleep(nanoseconds: 1_000_000_000)



                        // 检查权益状态
                        do {
                            let _ = try await AnonymousPurchaseService.shared.checkEntitlements()
                            NSLog("✅ SenseWordApp: 权益状态检查完成")
                        } catch {
                            NSLog("⚠️ SenseWordApp: 权益状态检查失败 - \(error)")
                        }

                        // 处理未完成的交易
                        await AnonymousPurchaseService.shared.processUnfinishedTransactions()

                        NSLog("🔄 SenseWordApp: 开始后台预初始化搜索服务...")

                        do {
                            // 1. 初始化搜索服务
                            let searchService = try await DIContainer.shared.initializeSearchServices()
                            NSLog("✅ SenseWordApp: 搜索服务初始化完成")

                            // 2. 索引同步改为按需下载，不再自动启动
                            let localIndexService = DIContainer.shared.localIndexService
                            NSLog("✅ SenseWordApp: 索引服务已准备就绪，等待用户按需下载")

                            // 3. 启动定期同步
                            localIndexService.startPeriodicSync()
                            NSLog("✅ SenseWordApp: 定期同步已启动")

                        } catch {
                            NSLog("❌ SenseWordApp: 后台初始化失败 - \(error)")
                        }
                    }
                }
        }
    }
}

// 添加预览支持
#if DEBUG
@available(iOS 15.0, *)
struct SenseWordMainView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SenseWordMainView()
                .previewDisplayName("SenseWord 主界面")
                .preferredColorScheme(.dark)
        }
    }
}
#endif

#endif // os(iOS)