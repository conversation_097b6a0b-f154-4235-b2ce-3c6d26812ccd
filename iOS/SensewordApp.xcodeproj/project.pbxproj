// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		DA2908812E01D25C000B7668 /* UIComponents in Frameworks */ = {isa = PBXBuildFile; productRef = DA2908802E01D25C000B7668 /* UIComponents */; };
		DA2908852E01DE12000B7668 /* SenseWordApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = DA2908842E01DE12000B7668 /* SenseWordApp.swift */; };
		DA73FFC92E0EC5D000F80FAF /* SQLite in Frameworks */ = {isa = PBXBuildFile; productRef = DA73FFC82E0EC5D000F80FAF /* SQLite */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		DAFC41B52E005DE2009F56B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DAFC41952E005DE0009F56B1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DAFC419C2E005DE0009F56B1;
			remoteInfo = SensewordApp;
		};
		DAFC41BF2E005DE2009F56B1 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DAFC41952E005DE0009F56B1 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DAFC419C2E005DE0009F56B1;
			remoteInfo = SensewordApp;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		DA2908842E01DE12000B7668 /* SenseWordApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SenseWordApp.swift; sourceTree = "<group>"; };
		DA7FA9BE2E0073DB00EE9395 /* UIComponents */ = {isa = PBXFileReference; lastKnownFileType = wrapper; name = UIComponents; path = Packages/UIComponents; sourceTree = "<group>"; };
		DAFC419D2E005DE0009F56B1 /* SensewordApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SensewordApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DAFC41B42E005DE2009F56B1 /* SensewordAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SensewordAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		DAFC41BE2E005DE2009F56B1 /* SensewordAppUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SensewordAppUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		DAFC41C62E005DE2009F56B1 /* Exceptions for "SensewordApp" folder in "SensewordApp" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = DAFC419C2E005DE0009F56B1 /* SensewordApp */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		DAFC419F2E005DE0009F56B1 /* SensewordApp */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				DAFC41C62E005DE2009F56B1 /* Exceptions for "SensewordApp" folder in "SensewordApp" target */,
			);
			path = SensewordApp;
			sourceTree = "<group>";
		};
		DAFC41B72E005DE2009F56B1 /* SensewordAppTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SensewordAppTests;
			sourceTree = "<group>";
		};
		DAFC41C12E005DE2009F56B1 /* SensewordAppUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = SensewordAppUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		DAFC419A2E005DE0009F56B1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DA73FFC92E0EC5D000F80FAF /* SQLite in Frameworks */,
				DA2908812E01D25C000B7668 /* UIComponents in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41B12E005DE2009F56B1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41BB2E005DE2009F56B1 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DA7FA9BD2E0073DA00EE9395 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DA7FA9BE2E0073DB00EE9395 /* UIComponents */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DAFC41942E005DE0009F56B1 = {
			isa = PBXGroup;
			children = (
				DA2908842E01DE12000B7668 /* SenseWordApp.swift */,
				DAFC419F2E005DE0009F56B1 /* SensewordApp */,
				DAFC41B72E005DE2009F56B1 /* SensewordAppTests */,
				DAFC41C12E005DE2009F56B1 /* SensewordAppUITests */,
				DA7FA9BD2E0073DA00EE9395 /* Frameworks */,
				DAFC419E2E005DE0009F56B1 /* Products */,
			);
			sourceTree = "<group>";
		};
		DAFC419E2E005DE0009F56B1 /* Products */ = {
			isa = PBXGroup;
			children = (
				DAFC419D2E005DE0009F56B1 /* SensewordApp.app */,
				DAFC41B42E005DE2009F56B1 /* SensewordAppTests.xctest */,
				DAFC41BE2E005DE2009F56B1 /* SensewordAppUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DAFC419C2E005DE0009F56B1 /* SensewordApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DAFC41C72E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordApp" */;
			buildPhases = (
				DAFC41992E005DE0009F56B1 /* Sources */,
				DAFC419A2E005DE0009F56B1 /* Frameworks */,
				DAFC419B2E005DE0009F56B1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				DAFC419F2E005DE0009F56B1 /* SensewordApp */,
			);
			name = SensewordApp;
			packageProductDependencies = (
				DA2908802E01D25C000B7668 /* UIComponents */,
				DA73FFC82E0EC5D000F80FAF /* SQLite */,
			);
			productName = SensewordApp;
			productReference = DAFC419D2E005DE0009F56B1 /* SensewordApp.app */;
			productType = "com.apple.product-type.application";
		};
		DAFC41B32E005DE2009F56B1 /* SensewordAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DAFC41CC2E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordAppTests" */;
			buildPhases = (
				DAFC41B02E005DE2009F56B1 /* Sources */,
				DAFC41B12E005DE2009F56B1 /* Frameworks */,
				DAFC41B22E005DE2009F56B1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DAFC41B62E005DE2009F56B1 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				DAFC41B72E005DE2009F56B1 /* SensewordAppTests */,
			);
			name = SensewordAppTests;
			packageProductDependencies = (
			);
			productName = SensewordAppTests;
			productReference = DAFC41B42E005DE2009F56B1 /* SensewordAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		DAFC41BD2E005DE2009F56B1 /* SensewordAppUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DAFC41CF2E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordAppUITests" */;
			buildPhases = (
				DAFC41BA2E005DE2009F56B1 /* Sources */,
				DAFC41BB2E005DE2009F56B1 /* Frameworks */,
				DAFC41BC2E005DE2009F56B1 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DAFC41C02E005DE2009F56B1 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				DAFC41C12E005DE2009F56B1 /* SensewordAppUITests */,
			);
			name = SensewordAppUITests;
			packageProductDependencies = (
			);
			productName = SensewordAppUITests;
			productReference = DAFC41BE2E005DE2009F56B1 /* SensewordAppUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DAFC41952E005DE0009F56B1 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					DAFC419C2E005DE0009F56B1 = {
						CreatedOnToolsVersion = 16.2;
						LastSwiftMigration = 1620;
					};
					DAFC41B32E005DE2009F56B1 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = DAFC419C2E005DE0009F56B1;
					};
					DAFC41BD2E005DE2009F56B1 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = DAFC419C2E005DE0009F56B1;
					};
				};
			};
			buildConfigurationList = DAFC41982E005DE0009F56B1 /* Build configuration list for PBXProject "SensewordApp" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = DAFC41942E005DE0009F56B1;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				DA29087B2E01D1D9000B7668 /* XCLocalSwiftPackageReference "Packages/UIComponents" */,
				DA73FFC72E0EC5CF00F80FAF /* XCRemoteSwiftPackageReference "SQLite.swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = DAFC419E2E005DE0009F56B1 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DAFC419C2E005DE0009F56B1 /* SensewordApp */,
				DAFC41B32E005DE2009F56B1 /* SensewordAppTests */,
				DAFC41BD2E005DE2009F56B1 /* SensewordAppUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DAFC419B2E005DE0009F56B1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41B22E005DE2009F56B1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41BC2E005DE2009F56B1 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DAFC41992E005DE0009F56B1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DA2908852E01DE12000B7668 /* SenseWordApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41B02E005DE2009F56B1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DAFC41BA2E005DE2009F56B1 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		DAFC41B62E005DE2009F56B1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DAFC419C2E005DE0009F56B1 /* SensewordApp */;
			targetProxy = DAFC41B52E005DE2009F56B1 /* PBXContainerItemProxy */;
		};
		DAFC41C02E005DE2009F56B1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DAFC419C2E005DE0009F56B1 /* SensewordApp */;
			targetProxy = DAFC41BF2E005DE2009F56B1 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		DAFC41C82E005DE2009F56B1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SensewordApp/SensewordApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SensewordApp/Preview Content\"";
				DEVELOPMENT_TEAM = 827FSB6BK2;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SensewordApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Senseword;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.Senseword;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		DAFC41C92E005DE2009F56B1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SensewordApp/SensewordApp.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SensewordApp/Preview Content\"";
				DEVELOPMENT_TEAM = 827FSB6BK2;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SensewordApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Senseword;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.Senseword;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		DAFC41CA2E005DE2009F56B1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DAFC41CB2E005DE2009F56B1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DAFC41CD2E005DE2009F56B1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 827FSB6BK2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.SensewordAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SensewordApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SensewordApp";
			};
			name = Debug;
		};
		DAFC41CE2E005DE2009F56B1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 827FSB6BK2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.SensewordAppTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SensewordApp.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/SensewordApp";
			};
			name = Release;
		};
		DAFC41D02E005DE2009F56B1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 827FSB6BK2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.SensewordAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SensewordApp;
			};
			name = Debug;
		};
		DAFC41D12E005DE2009F56B1 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 827FSB6BK2;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = zhouqiaaha.top.SensewordAppUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = SensewordApp;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DAFC41982E005DE0009F56B1 /* Build configuration list for PBXProject "SensewordApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DAFC41CA2E005DE2009F56B1 /* Debug */,
				DAFC41CB2E005DE2009F56B1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DAFC41C72E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DAFC41C82E005DE2009F56B1 /* Debug */,
				DAFC41C92E005DE2009F56B1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DAFC41CC2E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DAFC41CD2E005DE2009F56B1 /* Debug */,
				DAFC41CE2E005DE2009F56B1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DAFC41CF2E005DE2009F56B1 /* Build configuration list for PBXNativeTarget "SensewordAppUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DAFC41D02E005DE2009F56B1 /* Debug */,
				DAFC41D12E005DE2009F56B1 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		DA29087B2E01D1D9000B7668 /* XCLocalSwiftPackageReference "Packages/UIComponents" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = Packages/UIComponents;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCRemoteSwiftPackageReference section */
		DA73FFC72E0EC5CF00F80FAF /* XCRemoteSwiftPackageReference "SQLite.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stephencelis/SQLite.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.15.4;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		DA2908802E01D25C000B7668 /* UIComponents */ = {
			isa = XCSwiftPackageProductDependency;
			package = DA29087B2E01D1D9000B7668 /* XCLocalSwiftPackageReference "Packages/UIComponents" */;
			productName = UIComponents;
		};
		DA73FFC82E0EC5D000F80FAF /* SQLite */ = {
			isa = XCSwiftPackageProductDependency;
			package = DA73FFC72E0EC5CF00F80FAF /* XCRemoteSwiftPackageReference "SQLite.swift" */;
			productName = SQLite;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = DAFC41952E005DE0009F56B1 /* Project object */;
}
