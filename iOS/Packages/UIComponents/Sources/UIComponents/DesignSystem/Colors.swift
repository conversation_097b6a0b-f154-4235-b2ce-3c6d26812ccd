import SwiftUI
import Foundation

#if canImport(UIKit)
import UIKit
#endif

/// SenseWord 设计系统 - 颜色规范
/// 实现柔光白主题的完整颜色系统，支持明暗模式自适应
@available(iOS 15.0, macOS 10.15, *)
public struct SenseWordColors {
    
    // MARK: - 主背景渐变色系（暗色风格）

    /// 深蓝色 - 用于动态渐变背景的第一层
    public static let healthColor1 = Color(hue: 191/360, saturation: 0.5, brightness: 0.4)

    /// 深紫色 - 用于动态渐变背景的第二层
    public static let healthColor2 = Color(hue: 201/360, saturation: 0.6, brightness: 0.5)

    /// 深粉色 - 用于动态渐变背景的第三层
    public static let healthColor3 = Color(hue: 350/360, saturation: 0.4, brightness: 0.5)

    /// 深珊瑚色 - 用于动态渐变背景的第四层
    public static let healthColor4 = Color(hue: 4/360, saturation: 0.45, brightness: 0.5)

    /// 深桃色 - 用于动态渐变背景的第五层
    public static let healthColor5 = Color(hue: 14/360, saturation: 0.35, brightness: 0.55)
    
    // MARK: - 语义化颜色
    
    /// 主背景色 - 由动态渐变填充，此处为透明
    public static let backgroundMain = Color.clear
    
    /// 卡片背景色 - 半透明白色，营造玻璃态效果
    public static let backgroundCard = Color.white.opacity(0.7)
    
    /// 主要文本色 - 近黑色，确保可读性
    public static let textPrimary = Color(hex: "#1C1C1E")
    
    /// 次要文本色 - 深灰色，用于次要正文
    public static let textSecondary = Color(hex: "#3A3A3C")
    
    /// 三级文本色 - 中灰色，用于音标、注释等
    public static let textTertiary = Color(hex: "#8E8E93")
    
    /// 强调色 - Apple系统蓝色，高饱和度
    public static let accentBlue = Color(hex: "#007AFF")
    
    /// 柔和阴影色 - 带透明度的深灰色
    public static let shadowSoft = Color(hex: "#3C3C43").opacity(0.15)
    
    // MARK: - 基础背景色
    
    /// 基础背景色 - 柔光白主题的底色
    public static let baseBackground = Color(hex: "#F1F1F6")
    
    // MARK: - 状态颜色
    
    /// 成功色 - 绿色系
    public static let success = Color(hex: "#34C759")
    
    /// 警告色 - 橙色系
    public static let warning = Color(hex: "#FF9500")
    
    /// 错误色 - 红色系
    public static let error = Color(hex: "#FF3B30")
    
    /// 信息色 - 蓝色系
    public static let info = Color(hex: "#007AFF")
    
    // MARK: - 难度等级颜色
    
    /// A1-A2 难度颜色 - 绿色
    public static let difficultyBeginner = Color(hex: "#34C759")
    
    /// B1-B2 难度颜色 - 橙色
    public static let difficultyIntermediate = Color(hex: "#FF9500")
    
    /// C1-C2 难度颜色 - 红色
    public static let difficultyAdvanced = Color(hex: "#FF3B30")
    
    /// 未知难度颜色 - 灰色
    public static let difficultyUnknown = Color(hex: "#8E8E93")

    // MARK: - 上下文类型颜色

    /// 情感共鸣颜色 - 粉色系
    public static let emotionalPink = Color(hex: "#FF6B9D")

    /// 生动意象颜色 - 紫色系
    public static let imageryPurple = Color(hex: "#9B59B6")

    /// 词源本质颜色 - 绿色系
    public static let etymologyGreen = Color(hex: "#2ECC71")

    // MARK: - 灰色系统

    /// 极浅灰色 - 用于背景、分割线等
    public static let gray100 = Color(hex: "#F5F5F5")

    /// 浅灰色 - 用于次要背景
    public static let gray200 = Color(hex: "#EEEEEE")

    /// 中浅灰色 - 用于边框、分割线
    public static let gray300 = Color(hex: "#E0E0E0")

    /// 中灰色 - 用于禁用状态、占位符
    public static let gray400 = Color(hex: "#BDBDBD")

    /// 中深灰色 - 用于次要文本、图标
    public static let gray500 = Color(hex: "#9E9E9E")

    /// 深灰色 - 用于弱化按钮、次要元素
    public static let gray600 = Color(hex: "#777777")

    /// 较深灰色 - 用于次要文本
    public static let gray700 = Color(hex: "#616161")

    /// 深灰色 - 用于主要文本
    public static let gray800 = Color(hex: "#424242")

    /// 极深灰色 - 用于标题、重要文本
    public static let gray900 = Color(hex: "#212121")

    // MARK: - 动态颜色适配
    
    /// 根据难度等级返回对应颜色
    /// - Parameter difficulty: 难度等级字符串
    /// - Returns: 对应的颜色
    public static func colorForDifficulty(_ difficulty: String) -> Color {
        switch difficulty.uppercased() {
        case "A1", "A2":
            return difficultyBeginner
        case "B1", "B2":
            return difficultyIntermediate
        case "C1", "C2":
            return difficultyAdvanced
        default:
            return difficultyUnknown
        }
    }
    
    // MARK: - 渐变色组合
    
    /// 主背景渐变色数组 - 用于动态背景渲染
    public static let gradientColors: [Color] = [
        healthColor1,
        healthColor2,
        healthColor3,
        healthColor4,
        healthColor5
    ]

    /// 渐变色对应的中心点位置
    public static let gradientCenters: [UnitPoint] = [
        UnitPoint(x: 0.3, y: 0.4),
        UnitPoint(x: 0.7, y: 0.3),
        UnitPoint(x: 0.2, y: 0.7),
        UnitPoint(x: 0.6, y: 0.8),
        UnitPoint(x: 0.8, y: 0.6)
    ]

    /// 渐变色对应的半径大小
    public static let gradientRadii: [CGFloat] = [
        400,
        400,
        450,
        450,
        400
    ]
}

// MARK: - Color Extension

@available(iOS 15.0, macOS 10.15, *)
extension Color {
    /// 通过十六进制字符串创建颜色
    /// - Parameter hex: 十六进制颜色字符串，支持 #RGB, #RRGGBB, #AARRGGBB 格式
    public init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// 转换为十六进制字符串
    /// - Returns: 十六进制颜色字符串，格式为 #RRGGBB 或 #AARRGGBB
    public func toHex() -> String {
        #if canImport(UIKit)
        let uiColor = UIColor(self)
        let cgColor = uiColor.cgColor
        guard let components = cgColor.components,
              components.count >= 3 else {
            return "#000000"
        }
        
        let r = Int(components[0] * 255)
        let g = Int(components[1] * 255)
        let b = Int(components[2] * 255)
        let a = components.count > 3 ? Int(components[3] * 255) : 255
        
        if a == 255 {
            return String(format: "#%02X%02X%02X", r, g, b)
        } else {
            return String(format: "#%02X%02X%02X%02X", a, r, g, b)
        }
        #else
        // For non-UIKit platforms, return a default
        return "#000000"
        #endif
    }
}

// MARK: - SenseWord Color Extensions

@available(iOS 15.0, macOS 10.15, *)
extension Color {
    // MARK: - 背景色
    public static let senseWordBackgroundMain = SenseWordColors.backgroundMain
    public static let senseWordBackgroundCard = SenseWordColors.backgroundCard
    public static let senseWordBaseBackground = SenseWordColors.baseBackground

    // MARK: - 文本色
    public static let senseWordTextPrimary = SenseWordColors.textPrimary
    public static let senseWordTextSecondary = SenseWordColors.textSecondary
    public static let senseWordTextTertiary = SenseWordColors.textTertiary

    // MARK: - 强调色
    public static let senseWordAccentBlue = SenseWordColors.accentBlue

    // MARK: - 阴影色
    public static let senseWordShadowSoft = SenseWordColors.shadowSoft

    // MARK: - 状态色
    public static let senseWordSuccess = SenseWordColors.success
    public static let senseWordWarning = SenseWordColors.warning
    public static let senseWordError = SenseWordColors.error
    public static let senseWordInfo = SenseWordColors.info

    // MARK: - 上下文类型色
    public static let senseWordEmotionalPink = SenseWordColors.emotionalPink
    public static let senseWordImageryPurple = SenseWordColors.imageryPurple
    public static let senseWordEtymologyGreen = SenseWordColors.etymologyGreen

    // MARK: - 难度等级色
    public static let senseWordDifficultyBeginner = SenseWordColors.difficultyBeginner
    public static let senseWordDifficultyIntermediate = SenseWordColors.difficultyIntermediate
    public static let senseWordDifficultyAdvanced = SenseWordColors.difficultyAdvanced
    public static let senseWordDifficultyUnknown = SenseWordColors.difficultyUnknown

    // MARK: - 灰色系统
    public static let senseWordGray100 = SenseWordColors.gray100
    public static let senseWordGray200 = SenseWordColors.gray200
    public static let senseWordGray300 = SenseWordColors.gray300
    public static let senseWordGray400 = SenseWordColors.gray400
    public static let senseWordGray500 = SenseWordColors.gray500
    public static let senseWordGray600 = SenseWordColors.gray600
    public static let senseWordGray700 = SenseWordColors.gray700
    public static let senseWordGray800 = SenseWordColors.gray800
    public static let senseWordGray900 = SenseWordColors.gray900
}

// MARK: - Preview Support
// Preview代码已移除以避免编译时的平台兼容性问题
// 在Xcode中使用时，SwiftUI Preview将自动工作
