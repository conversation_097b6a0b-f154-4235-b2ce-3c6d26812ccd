import SwiftUI
import Foundation

#if canImport(UIKit)
import UIKit
#endif

// MARK: - Health Style Light Wallpaper

/// 健康风格亮色背景壁纸组件
/// 专为亮色模式设计的多层渐变背景，具有柔和的蓝色、粉色和珊瑚色调
@available(iOS 15.0, macOS 10.15, *)
public struct HealthStyleLightWallpaperView: View {
    
    // MARK: - Properties
    
    /// 是否启用动画效果
    private let enableAnimation: Bool
    
    /// 模糊半径
    private let blurRadius: CGFloat
    
    /// 整体透明度
    private let opacity: Double
    
    /// 动画速度倍数
    private let animationSpeed: Double
    
    /// 是否启用呼吸效果
    private let enableBreathing: Bool
    
    /// 是否启用旋转效果
    private let enableRotation: Bool
    
    // MARK: - Animation States
    
    /// 旋转角度
    @State private var rotationAngle: Double = 0
    
    /// 缩放效果
    @State private var scaleEffect: CGFloat = 1.0
    
    /// 位移动画偏移
    @State private var animationOffset: CGFloat = 0
    
    /// 透明度动画
    @State private var animatedOpacity: Double = 0.0
    
    // MARK: - Initializer
    
    /// 初始化亮色模式健康风格背景壁纸
    /// - Parameters:
    ///   - enableAnimation: 是否启用动画效果，默认为false
    ///   - blurRadius: 模糊半径，默认为100
    ///   - opacity: 整体透明度，默认为0.6
    ///   - animationSpeed: 动画速度倍数，默认为1.0
    ///   - enableBreathing: 是否启用呼吸效果，默认为true
    ///   - enableRotation: 是否启用旋转效果，默认为true
    public init(
        enableAnimation: Bool = false,
        blurRadius: CGFloat = 100,
        opacity: Double = 0.6,
        animationSpeed: Double = 1.0,
        enableBreathing: Bool = true,
        enableRotation: Bool = true
    ) {
        self.enableAnimation = enableAnimation
        self.blurRadius = blurRadius
        self.opacity = opacity
        self.animationSpeed = animationSpeed
        self.enableBreathing = enableBreathing
        self.enableRotation = enableRotation
    }
    
    // MARK: - Body
    
    public var body: some View {
        ZStack {
            // 亮色基础背景
            Color.white
                .edgesIgnoringSafeArea(.all)

            // 亮色模式的渐变组 - 使用原有的美丽渐变设计
            ZStack {
                // 第一层：柔和蓝色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 200/360, saturation: 0.6, brightness: 0.9),
                        Color(hue: 200/360, saturation: 0.3, brightness: 0.95),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.3, y: 0.4),
                    startRadius: 0,
                    endRadius: 400
                )

                // 第二层：减弱的深紫色渐变 (#32147D) - 降低透明度
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0x32/255.0, green: 0x14/255.0, blue: 0x7D/255.0, opacity: 0.4),
                        Color(red: 0x32/255.0, green: 0x14/255.0, blue: 0x7D/255.0, opacity: 0.15),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.7, y: 0.3),
                    startRadius: 0,
                    endRadius: 400
                )

                // 第三层：粉红色渐变 (#FC849D)
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0xFC/255.0, green: 0x84/255.0, blue: 0x9D/255.0, opacity: 0.6),
                        Color(red: 0xFC/255.0, green: 0x84/255.0, blue: 0x9D/255.0, opacity: 0.2),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.2, y: 0.7),
                    startRadius: 0,
                    endRadius: 450
                )

                // 第四层：减弱的霓虹深紫蓝色渐变 (#27219A) - 降低透明度
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0x27/255.0, green: 0x21/255.0, blue: 0x9A/255.0, opacity: 0.4),
                        Color(red: 0x27/255.0, green: 0x21/255.0, blue: 0x9A/255.0, opacity: 0.15),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.6, y: 0.8),
                    startRadius: 0,
                    endRadius: 450
                )

                // 第五层：减弱的霓虹深紫色渐变 (#2A036D) - 降低透明度
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0x2A/255.0, green: 0x03/255.0, blue: 0x6D/255.0, opacity: 0.35),
                        Color(red: 0x2A/255.0, green: 0x03/255.0, blue: 0x6D/255.0, opacity: 0.1),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.8, y: 0.6),
                    startRadius: 0,
                    endRadius: 400
                )

                // 第六层：新增红色渐变 (#DE3919)
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(red: 0xDE/255.0, green: 0x39/255.0, blue: 0x19/255.0, opacity: 0.5),
                        Color(red: 0xDE/255.0, green: 0x39/255.0, blue: 0x19/255.0, opacity: 0.2),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.1, y: 0.2),
                    startRadius: 0,
                    endRadius: 350
                )
            }
            .blur(radius: blurRadius)
            .edgesIgnoringSafeArea(.all)
            .opacity(animatedOpacity)
            .scaleEffect(scaleEffect)
            .rotationEffect(.degrees(rotationAngle))
            .offset(x: animationOffset, y: animationOffset * 0.7)
        }
        .onAppear {
            startAnimations()
        }
        .onDisappear {
            stopAnimations()
        }
    }
    
    // MARK: - Private Methods
    
    /// 启动动画
    private func startAnimations() {
        // 淡入动画
        withAnimation(.easeOut(duration: 1.5)) {
            animatedOpacity = opacity
        }

        if enableAnimation {
            // 旋转动画 - 增强版本
            if enableRotation {
                withAnimation(.linear(duration: 30.0 / animationSpeed).repeatForever(autoreverses: false)) {
                    rotationAngle = 360
                }
            }

            // 呼吸效果 - 增强版本
            if enableBreathing {
                withAnimation(.easeInOut(duration: 2.5 / animationSpeed).repeatForever(autoreverses: true)) {
                    scaleEffect = 1.15  // 增大呼吸幅度
                }
            }

            // 添加位移动画让效果更明显
            withAnimation(.easeInOut(duration: 8.0 / animationSpeed).repeatForever(autoreverses: true)) {
                animationOffset = 20
            }
        }
    }

    /// 停止动画
    private func stopAnimations() {
        rotationAngle = 0
        scaleEffect = 1.0
        animatedOpacity = 0.0
    }
}

@available(iOS 15.0, macOS 10.15, *)
extension HealthStyleLightWallpaperView {

    /// 创建静态版本的亮色模式健康风格背景
    /// - Returns: 静态亮色背景壁纸视图
    public static func staticLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: false,
            blurRadius: 100,
            opacity: 0.6,
            animationSpeed: 1.0,
            enableBreathing: false,
            enableRotation: false
        )
    }

    /// 创建动画版本的亮色模式健康风格背景
    /// - Returns: 动画亮色背景壁纸视图
    public static func animatedLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: true,
            blurRadius: 100,
            opacity: 0.6,
            animationSpeed: 1.0,
            enableBreathing: true,
            enableRotation: true
        )
    }

    /// 创建高透明度版本的亮色模式健康风格背景
    /// - Returns: 高透明度亮色背景壁纸视图
    public static func subtleLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: false,
            blurRadius: 120,
            opacity: 0.4,
            animationSpeed: 1.0,
            enableBreathing: false,
            enableRotation: false
        )
    }

    /// 创建强烈版本的亮色模式健康风格背景
    /// - Returns: 强烈亮色背景壁纸视图
    public static func vibrantLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: true,
            blurRadius: 80,
            opacity: 0.8,
            animationSpeed: 1.2,
            enableBreathing: true,
            enableRotation: true
        )
    }

    /// 创建柔和动画版本的亮色模式健康风格背景
    /// - Returns: 柔和动画亮色背景壁纸视图
    public static func gentleLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: true,
            blurRadius: 110,
            opacity: 0.5,
            animationSpeed: 0.7,
            enableBreathing: true,
            enableRotation: false
        )
    }

    /// 创建快速动画版本的亮色模式健康风格背景
    /// - Returns: 快速动画亮色背景壁纸视图
    public static func energeticLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: true,
            blurRadius: 90,
            opacity: 0.7,
            animationSpeed: 1.5,
            enableBreathing: true,
            enableRotation: true
        )
    }

    /// 创建仅呼吸效果的亮色模式健康风格背景
    /// - Returns: 仅呼吸效果亮色背景壁纸视图
    public static func breathingLightWallpaper() -> HealthStyleLightWallpaperView {
        HealthStyleLightWallpaperView(
            enableAnimation: true,
            blurRadius: 100,
            opacity: 0.6,
            animationSpeed: 0.8,
            enableBreathing: true,
            enableRotation: false
        )
    }
}
