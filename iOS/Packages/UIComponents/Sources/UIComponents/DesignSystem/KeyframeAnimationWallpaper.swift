import SwiftUI
import Foundation

// MARK: - Keyframe Animation System

/// 关键帧动画样式枚举
/// 只保留三种核心动画：强烈、呼吸、活力
@available(iOS 15.0, macOS 10.15, *)
public enum KeyframeAnimationStyle: String, CaseIterable {
    case vibrant = "强烈"

    /// 获取动画显示名称
    public var displayName: String {
        return self.rawValue
    }

    /// 获取动画持续时间（秒）
    public var duration: TimeInterval {
        switch self {
        case .vibrant: return 6.0      // 强烈动画：6秒周期
        }
    }
}

/// 动画关键帧数据结构
@available(iOS 15.0, macOS 10.15, *)
public struct AnimationKeyframe {
    let time: TimeInterval          // 时间点（0-1）
    let rotationAngle: Double       // 旋转角度
    let scaleEffect: CGFloat        // 缩放效果
    let offsetX: CGFloat           // X轴偏移
    let offsetY: CGFloat           // Y轴偏移
    let opacity: Double            // 透明度
    
    /// 静态起始/结束关键帧（确保动画首尾相连）
    static let identity = AnimationKeyframe(
        time: 0.0,
        rotationAngle: 0.0,
        scaleEffect: 1.0,
        offsetX: 0.0,
        offsetY: 0.0,
        opacity: 1.0
    )
}

/// AnimationKeyframe 扩展方法
@available(iOS 15.0, macOS 10.15, *)
extension AnimationKeyframe {
    /// 创建一个新的关键帧，只修改时间
    func with(time: TimeInterval) -> AnimationKeyframe {
        return AnimationKeyframe(
            time: time,
            rotationAngle: self.rotationAngle,
            scaleEffect: self.scaleEffect,
            offsetX: self.offsetX,
            offsetY: self.offsetY,
            opacity: self.opacity
        )
    }
}

/// 预计算的关键帧动画数据
@available(iOS 15.0, macOS 10.15, *)
public struct PrecomputedKeyframes {
    
    /// 强烈动画关键帧（6秒周期）
    static let vibrantKeyframes: [AnimationKeyframe] = {
        var keyframes: [AnimationKeyframe] = []
        let frameCount = 60 // 60帧，每0.1秒一帧
        
        for i in 0..<frameCount {
            let progress = Double(i) / Double(frameCount)
            let time = progress

            // 强烈的旋转和缩放组合 - 移除透明度变化
            let rotationAngle = progress * 360.0 // 完整旋转一圈
            let scaleEffect = 1.0 + 0.3 * sin(progress * 4 * .pi) // 恢复原有缩放幅度
            let offsetX = 30.0 * sin(progress * 6 * .pi) // 恢复原有位移幅度
            let offsetY = 20.0 * cos(progress * 6 * .pi) // 恢复原有位移幅度
            let opacity = 1.0 // 固定透明度，避免颜色变化

            keyframes.append(AnimationKeyframe(
                time: time,
                rotationAngle: rotationAngle,
                scaleEffect: scaleEffect,
                offsetX: offsetX,
                offsetY: offsetY,
                opacity: opacity
            ))
        }

        // 添加最后一帧，确保与第一帧形成自然循环
        let firstFrame = keyframes[0]
        keyframes.append(AnimationKeyframe(
            time: 1.0,
            rotationAngle: 360.0, // 完整的360度旋转
            scaleEffect: firstFrame.scaleEffect,
            offsetX: firstFrame.offsetX,
            offsetY: firstFrame.offsetY,
            opacity: 1.0 // 固定透明度
        ))
        return keyframes
    }()
    

    
    /// 根据动画样式获取对应的关键帧数据
    static func keyframes(for style: KeyframeAnimationStyle) -> [AnimationKeyframe] {
        switch style {
        case .vibrant: return vibrantKeyframes
        }
    }
}

// MARK: - Animation Control Buttons

/// 动画控制按钮组件
/// 提供三个白色磨砂玻璃按钮来控制关键帧动画
@available(iOS 15.0, macOS 10.15, *)
public struct AnimationControlButtons: View {
    
    /// 当前选中的动画样式
    @State private var selectedStyle: KeyframeAnimationStyle? = nil
    
    /// 动画完成回调
    private let onAnimationSelected: (KeyframeAnimationStyle) -> Void
    
    /// 初始化动画控制按钮
    /// - Parameter onAnimationSelected: 动画选择回调
    public init(onAnimationSelected: @escaping (KeyframeAnimationStyle) -> Void) {
        self.onAnimationSelected = onAnimationSelected
    }
    
    public var body: some View {
        HStack(spacing: 20) {
            ForEach(KeyframeAnimationStyle.allCases, id: \.self) { style in
                AnimationButton(
                    title: style.displayName,
                    isSelected: selectedStyle == style,
                    action: {
                        selectedStyle = style
                        onAnimationSelected(style)
                        
                        // 动画完成后重置选中状态
                        DispatchQueue.main.asyncAfter(deadline: .now() + style.duration + 0.5) {
                            selectedStyle = nil
                        }
                    }
                )
            }
        }
        .padding(.horizontal, 20)
    }
}

/// 单个动画按钮
@available(iOS 15.0, macOS 10.15, *)
private struct AnimationButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 16, weight: .medium, design: .rounded))
                .foregroundColor(isSelected ? .blue : .primary)
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.white.opacity(0.15))
                        .background(
                            RoundedRectangle(cornerRadius: 25)
                                .fill(Color.black.opacity(0.1))
                                .blur(radius: 10)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(isSelected ? Color.blue : Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
        }
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }
}

// MARK: - Keyframe Animation Wallpaper

/// 关键帧动画壁纸组件
/// 使用预计算的关键帧数据实现高性能动画
@available(iOS 15.0, macOS 10.15, *)
public struct KeyframeAnimationWallpaperView: View {

    // MARK: - Properties

    /// 当前动画样式
    private let animationStyle: KeyframeAnimationStyle

    /// 是否强制使用暗色模式
    private let forceDarkMode: Bool

    /// 是否正在播放动画
    @State private var isAnimating: Bool = false

    /// 当前动画进度（0-1）
    @State private var animationProgress: Double = 0.0

    /// 动画计时器
    @State private var animationTimer: Timer?

    /// 静态壁纸透明度（当动画开始时逐渐隐藏）
    @State private var staticWallpaperOpacity: Double = 1.0

    /// 环境变量：当前颜色方案
    @Environment(\.colorScheme) private var colorScheme

    /// 环境变量：减少动画
    @Environment(\.accessibilityReduceMotion) private var reduceMotion



    // MARK: - Initializer

    /// 初始化关键帧动画壁纸
    /// - Parameters:
    ///   - animationStyle: 动画样式
    ///   - forceDarkMode: 是否强制使用暗色模式
    public init(
        animationStyle: KeyframeAnimationStyle,
        forceDarkMode: Bool = false
    ) {
        self.animationStyle = animationStyle
        self.forceDarkMode = forceDarkMode
    }

    // MARK: - Body

    public var body: some View {
        ZStack {
            // 基础背景层
            baseWallpaper

            // 关键帧动画层
            if !reduceMotion {
                keyframeAnimationLayer
            }
        }
        .onAppear {
            NSLog("🎨 KeyframeAnimationWallpaperView: 组件已出现，样式: \(animationStyle.displayName)")
            // 移除自动开始动画，改为外部触发
        }
        .onDisappear {
            // 延迟停止动画，给新壁纸时间出现
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                stopAnimation()
            }
            NSLog("🎨 KeyframeAnimationWallpaperView: 组件已消失")
        }
        .onReceive(NotificationCenter.default.publisher(for: Notification.Name("startWallpaperAnimation"))) { _ in
            NSLog("🎨 KeyframeAnimationWallpaperView: 收到动画触发通知")
            startAnimation()
        }
    }

    /// 基础背景层
    private var baseWallpaper: some View {
        Group {
            if forceDarkMode || colorScheme == .dark {
                // 暗色模式：使用健康风格暗色壁纸
                HealthStyleDarkWallpaperView(
                    enableAnimation: false,
                    blurRadius: 100,
                    opacity: 0.4, // 调整为更暗的效果
                    animationSpeed: 1.0,
                    enableBreathing: false,
                    enableRotation: false
                )

            } else {
                // 亮色模式：使用健康风格亮色壁纸
                HealthStyleLightWallpaperView(
                    enableAnimation: false,
                    blurRadius: 100,
                    opacity: 0.7, // 与动画层保持一致的透明度
                    animationSpeed: 1.0,
                    enableBreathing: false,
                    enableRotation: false
                )

            }
        }
        .opacity(staticWallpaperOpacity)
        .animation(.easeInOut(duration: 0.8), value: staticWallpaperOpacity)
    }

    /// 关键帧动画层
    private var keyframeAnimationLayer: some View {
        animatedWallpaper
            .opacity(isAnimating ? 1.0 : 0.0)
            .animation(.easeInOut(duration: 0.5), value: isAnimating)
    }

    /// 动画层 - 只包含渐变部分，不包含背景色
    private var animatedWallpaper: some View {
        let currentKeyframe = getCurrentKeyframe()

        return Group {
            if forceDarkMode || colorScheme == .dark {
                // 暗色模式动画层 - 只渲染渐变部分
                darkGradientLayer
                    .scaleEffect(currentKeyframe.scaleEffect)
                    .rotationEffect(Angle.degrees(currentKeyframe.rotationAngle))
                    .offset(x: currentKeyframe.offsetX, y: currentKeyframe.offsetY)
            } else {
                // 亮色模式动画层 - 只渲染渐变部分
                lightGradientLayer
                    .scaleEffect(currentKeyframe.scaleEffect)
                    .rotationEffect(Angle.degrees(currentKeyframe.rotationAngle))
                    .offset(x: currentKeyframe.offsetX, y: currentKeyframe.offsetY)
            }
        }
    }

    /// 暗色模式渐变层（不包含背景色）
    private var darkGradientLayer: some View {
        ZStack {
            // 深蓝色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 191/360, saturation: 0.5, brightness: 0.4),
                    .clear
                ]),
                center: UnitPoint(x: 0.3, y: 0.4),
                startRadius: 0,
                endRadius: 400
            )

            // 深紫色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 201/360, saturation: 0.6, brightness: 0.5),
                    .clear
                ]),
                center: UnitPoint(x: 0.7, y: 0.3),
                startRadius: 0,
                endRadius: 400
            )

            // 深粉色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 350/360, saturation: 0.4, brightness: 0.5),
                    .clear
                ]),
                center: UnitPoint(x: 0.2, y: 0.7),
                startRadius: 0,
                endRadius: 450
            )

            // 深珊瑚色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 4/360, saturation: 0.45, brightness: 0.5),
                    .clear
                ]),
                center: UnitPoint(x: 0.6, y: 0.8),
                startRadius: 0,
                endRadius: 450
            )

            // 深桃色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 14/360, saturation: 0.35, brightness: 0.55),
                    .clear
                ]),
                center: UnitPoint(x: 0.8, y: 0.6),
                startRadius: 0,
                endRadius: 400
            )
        }
        .blur(radius: 100)
        .opacity(0.4) // 与基础层保持一致的透明度
        .edgesIgnoringSafeArea(.all)
    }

    /// 亮色模式渐变层（不包含背景色）
    private var lightGradientLayer: some View {
        ZStack {
            // 第一层：柔和蓝色渐变
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(hue: 200/360, saturation: 0.6, brightness: 0.9),
                    Color(hue: 200/360, saturation: 0.3, brightness: 0.95),
                    .clear
                ]),
                center: UnitPoint(x: 0.3, y: 0.4),
                startRadius: 0,
                endRadius: 400
            )

            // 第二层：减弱的深紫色渐变 (#32147D) - 降低透明度
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(red: 0x32/255.0, green: 0x14/255.0, blue: 0x7D/255.0, opacity: 0.4),
                    Color(red: 0x32/255.0, green: 0x14/255.0, blue: 0x7D/255.0, opacity: 0.15),
                    .clear
                ]),
                center: UnitPoint(x: 0.7, y: 0.3),
                startRadius: 0,
                endRadius: 400
            )

            // 第三层：粉红色渐变 (#FC849D)
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(red: 0xFC/255.0, green: 0x84/255.0, blue: 0x9D/255.0, opacity: 0.6),
                    Color(red: 0xFC/255.0, green: 0x84/255.0, blue: 0x9D/255.0, opacity: 0.2),
                    .clear
                ]),
                center: UnitPoint(x: 0.2, y: 0.7),
                startRadius: 0,
                endRadius: 450
            )

            // 第四层：减弱的霓虹深紫蓝色渐变 (#27219A) - 降低透明度
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(red: 0x27/255.0, green: 0x21/255.0, blue: 0x9A/255.0, opacity: 0.4),
                    Color(red: 0x27/255.0, green: 0x21/255.0, blue: 0x9A/255.0, opacity: 0.15),
                    .clear
                ]),
                center: UnitPoint(x: 0.6, y: 0.8),
                startRadius: 0,
                endRadius: 450
            )

            // 第五层：减弱的霓虹深紫色渐变 (#2A036D) - 降低透明度
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(red: 0x2A/255.0, green: 0x03/255.0, blue: 0x6D/255.0, opacity: 0.35),
                    Color(red: 0x2A/255.0, green: 0x03/255.0, blue: 0x6D/255.0, opacity: 0.1),
                    .clear
                ]),
                center: UnitPoint(x: 0.8, y: 0.6),
                startRadius: 0,
                endRadius: 400
            )

            // 第六层：新增红色渐变 (#DE3919)
            RadialGradient(
                gradient: Gradient(colors: [
                    Color(red: 0xDE/255.0, green: 0x39/255.0, blue: 0x19/255.0, opacity: 0.5),
                    Color(red: 0xDE/255.0, green: 0x39/255.0, blue: 0x19/255.0, opacity: 0.2),
                    .clear
                ]),
                center: UnitPoint(x: 0.1, y: 0.2),
                startRadius: 0,
                endRadius: 350
            )
        }
        .blur(radius: 100)
        .opacity(0.7)
        .edgesIgnoringSafeArea(.all)
    }

    // MARK: - Animation Control

    /// 开始播放动画
    public func startAnimation() {
        guard !reduceMotion else {
            NSLog("🎨 KeyframeAnimationWallpaperView: 用户设置了减少动画，跳过动画")
            return
        }

        // 检查是否已有动画在运行
        if animationTimer != nil {
            NSLog("⚠️ KeyframeAnimationWallpaperView: 动画已在运行，先停止当前动画")
            stopAnimation()
        }

        NSLog("🎨 KeyframeAnimationWallpaperView: 开始播放 \(animationStyle.displayName) 动画 (30 FPS)")

        isAnimating = true
        animationProgress = 0.0

        // 当动画开始时，逐渐隐藏静态壁纸
        withAnimation(.easeInOut(duration: 0.8)) {
            staticWallpaperOpacity = 0.0
        }

        let duration = animationStyle.duration
        let frameRate: Double = 30.0 // 30 FPS - 优化性能，降低GPU使用率
        let frameInterval = 1.0 / frameRate
        let totalFrames = Int(duration * frameRate)

        var currentFrame = 0

        animationTimer = Timer.scheduledTimer(withTimeInterval: frameInterval, repeats: true) { timer in
            currentFrame += 1
            animationProgress = Double(currentFrame) / Double(totalFrames)

            if currentFrame >= totalFrames {
                // 动画完成，停止计时器
                timer.invalidate()

                // 先开始渐隐动画，但不立即复位进度
                withAnimation(.easeOut(duration: 0.5)) {
                    self.isAnimating = false
                }

                // 等到渐隐完成后再复位进度，避免瞬间回弹
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.animationProgress = 0.0
                    // 动画结束后，恢复静态壁纸
                    withAnimation(.easeInOut(duration: 0.5)) {
                        self.staticWallpaperOpacity = 1.0
                    }
                }

                NSLog("🎨 KeyframeAnimationWallpaperView: 动画已完成并开始平滑结束 (总帧数: \(totalFrames))")
            }
        }
    }

    /// 停止动画
    public func stopAnimation() {
        animationTimer?.invalidate()
        animationTimer = nil
        isAnimating = false
        animationProgress = 0.0

        // 恢复静态壁纸
        withAnimation(.easeInOut(duration: 0.3)) {
            staticWallpaperOpacity = 1.0
        }

        NSLog("🎨 KeyframeAnimationWallpaperView: 动画已停止")
    }



    /// 获取当前关键帧数据
    private func getCurrentKeyframe() -> AnimationKeyframe {
        let keyframes = PrecomputedKeyframes.keyframes(for: animationStyle)

        guard !keyframes.isEmpty else {
            return AnimationKeyframe.identity
        }

        // 如果动画未开始，返回起始状态
        if animationProgress <= 0.0 {
            return AnimationKeyframe.identity
        }

        // 如果动画已结束，返回最后一个关键帧（平滑结束）
        if animationProgress >= 1.0 {
            return keyframes.last ?? AnimationKeyframe.identity
        }

        // 根据进度查找对应的关键帧
        let targetTime = animationProgress

        // 找到最接近的关键帧
        for i in 0..<(keyframes.count - 1) {
            let currentFrame = keyframes[i]
            let nextFrame = keyframes[i + 1]

            if targetTime >= currentFrame.time && targetTime <= nextFrame.time {
                // 在两个关键帧之间进行插值
                let frameProgress = (targetTime - currentFrame.time) / (nextFrame.time - currentFrame.time)
                return interpolateKeyframes(from: currentFrame, to: nextFrame, progress: frameProgress)
            }
        }

        // 如果没有找到合适的关键帧，返回最后一个
        return keyframes.last ?? AnimationKeyframe.identity
    }

    /// 在两个关键帧之间进行插值
    private func interpolateKeyframes(from: AnimationKeyframe, to: AnimationKeyframe, progress: Double) -> AnimationKeyframe {
        let clampedProgress = max(0.0, min(1.0, progress))

        return AnimationKeyframe(
            time: from.time + (to.time - from.time) * clampedProgress,
            rotationAngle: from.rotationAngle + (to.rotationAngle - from.rotationAngle) * clampedProgress,
            scaleEffect: from.scaleEffect + (to.scaleEffect - from.scaleEffect) * CGFloat(clampedProgress),
            offsetX: from.offsetX + (to.offsetX - from.offsetX) * CGFloat(clampedProgress),
            offsetY: from.offsetY + (to.offsetY - from.offsetY) * CGFloat(clampedProgress),
            opacity: from.opacity + (to.opacity - from.opacity) * clampedProgress
        )
    }
}
