import SwiftUI
import Foundation

#if canImport(UIKit)
import UIKit
#endif

/// SenseWord 设计系统 - 动画配置
/// 定义完整的动画系统，包括转场、微交互和反馈动画
@available(iOS 15.0, macOS 10.15, *)
public struct SenseWordAnimations {
    
    // MARK: - 基础动画配置
    
    /// 快速动画 - 用于微交互和即时反馈
    /// 0.2秒，缓入缓出
    public static let fast = Animation.easeInOut(duration: 0.2)
    
    /// 标准动画 - 用于常规UI变化
    /// 0.3秒，缓入缓出
    public static let standard = Animation.easeInOut(duration: 0.3)
    
    /// 慢速动画 - 用于重要转场
    /// 0.5秒，缓入缓出
    public static let slow = Animation.easeInOut(duration: 0.5)
    
    /// 弹簧动画 - 用于有机感的交互
    /// 响应时间0.6秒，阻尼0.8
    public static let spring = Animation.spring(response: 0.6, dampingFraction: 0.8)
    
    /// 轻弹簧动画 - 用于轻微的弹性效果
    /// 响应时间0.4秒，阻尼0.9
    public static let lightSpring = Animation.spring(response: 0.4, dampingFraction: 0.9)
    
    // MARK: - 转场动画
    
    /// 英雄转场动画 - 用于卡片到全屏的转场
    /// 弹簧动画，响应时间0.6秒，阻尼0.8
    public static let heroTransition = Animation.spring(response: 0.6, dampingFraction: 0.8)
    
    /// 幻灯片转场动画 - 用于幻灯片之间的切换
    /// 缓入缓出，0.4秒
    public static let slideTransition = Animation.easeInOut(duration: 0.4)
    
    /// 淡入淡出动画 - 用于内容的显示隐藏
    /// 缓入缓出，0.3秒
    public static let fadeInOut = Animation.easeInOut(duration: 0.3)
    
    /// 滑入动画 - 用于从边缘滑入的效果
    /// 缓出，0.4秒
    public static let slideIn = Animation.easeOut(duration: 0.4)
    
    /// 滑出动画 - 用于滑出到边缘的效果
    /// 缓入，0.3秒
    public static let slideOut = Animation.easeIn(duration: 0.3)
    
    // MARK: - 背景动画
    
    /// 渐变旋转动画 - 用于动态背景的旋转效果
    /// 线性动画，60秒，无限重复
    public static let gradientRotation = Animation.linear(duration: 60).repeatForever(autoreverses: false)
    
    /// 呼吸动画 - 用于微妙的呼吸效果
    /// 缓入缓出，3秒，无限重复，往返
    public static let breathing = Animation.easeInOut(duration: 3).repeatForever(autoreverses: true)
    
    /// 脉冲动画 - 用于提示和强调
    /// 缓入缓出，1秒，无限重复，往返
    public static let pulse = Animation.easeInOut(duration: 1).repeatForever(autoreverses: true)
    
    // MARK: - 手势动画
    
    /// 拖拽响应动画 - 用于手势拖拽时的实时响应
    /// 交互式弹簧动画
    public static let dragResponse = Animation.interactiveSpring(response: 0.3, dampingFraction: 0.8)
    
    /// 手势结束动画 - 用于手势结束后的回弹
    /// 弹簧动画，响应时间0.5秒，阻尼0.7
    public static let gestureEnd = Animation.spring(response: 0.5, dampingFraction: 0.7)
    
    /// 吸附动画 - 用于分页滚动的吸附效果
    /// 缓出，0.4秒
    public static let snap = Animation.easeOut(duration: 0.4)
    
    // MARK: - 反馈动画
    
    /// 成功反馈动画 - 用于成功状态的反馈
    /// 弹簧动画，响应时间0.4秒，阻尼0.6
    public static let successFeedback = Animation.spring(response: 0.4, dampingFraction: 0.6)
    
    /// 错误反馈动画 - 用于错误状态的反馈
    /// 弹簧动画，响应时间0.3秒，阻尼0.5
    public static let errorFeedback = Animation.spring(response: 0.3, dampingFraction: 0.5)
    
    /// 按钮点击动画 - 用于按钮点击反馈
    /// 快速弹簧动画
    public static let buttonTap = Animation.spring(response: 0.2, dampingFraction: 0.8)
    
    // MARK: - 动画配置结构体
    
    /// 动画配置
    public struct AnimationConfig {
        public let animation: Animation
        public let delay: Double
        public let repeatCount: Int?
        
        public init(animation: Animation, delay: Double = 0, repeatCount: Int? = nil) {
            self.animation = animation
            self.delay = delay
            self.repeatCount = repeatCount
        }
    }
    
    // MARK: - 预定义动画配置
    
    /// 幻灯片进入配置
    public static let slideEnterConfig = AnimationConfig(
        animation: slideTransition,
        delay: 0.1
    )
    
    /// 幻灯片退出配置
    public static let slideExitConfig = AnimationConfig(
        animation: slideTransition,
        delay: 0
    )
    
    /// 提示显示配置
    public static let hintShowConfig = AnimationConfig(
        animation: fadeInOut,
        delay: 1.0
    )
    
    /// 提示隐藏配置
    public static let hintHideConfig = AnimationConfig(
        animation: fadeInOut,
        delay: 3.0
    )
    
    // MARK: - 动画时长常量
    
    /// 动画时长枚举
    public enum Duration {
        /// 极快 - 0.1秒
        case veryFast
        /// 快速 - 0.2秒
        case fast
        /// 标准 - 0.3秒
        case standard
        /// 慢速 - 0.5秒
        case slow
        /// 很慢 - 0.8秒
        case verySlow
        /// 自定义时长
        case custom(Double)
        
        public var value: Double {
            switch self {
            case .veryFast:
                return 0.1
            case .fast:
                return 0.2
            case .standard:
                return 0.3
            case .slow:
                return 0.5
            case .verySlow:
                return 0.8
            case .custom(let duration):
                return duration
            }
        }
    }
    
    /// 动画曲线枚举
    public enum Curve {
        case linear
        case easeIn
        case easeOut
        case easeInOut
        case spring(response: Double, dampingFraction: Double)
        
        public func animation(duration: Duration) -> Animation {
            switch self {
            case .linear:
                return .linear(duration: duration.value)
            case .easeIn:
                return .easeIn(duration: duration.value)
            case .easeOut:
                return .easeOut(duration: duration.value)
            case .easeInOut:
                return .easeInOut(duration: duration.value)
            case .spring(let response, let dampingFraction):
                return .spring(response: response, dampingFraction: dampingFraction)
            }
        }
    }
    
    // MARK: - 动画工厂方法
    
    /// 创建自定义动画
    /// - Parameters:
    ///   - curve: 动画曲线
    ///   - duration: 动画时长
    /// - Returns: 动画对象
    public static func custom(curve: Curve, duration: Duration) -> Animation {
        return curve.animation(duration: duration)
    }
    
    /// 创建延迟动画
    /// - Parameters:
    ///   - animation: 基础动画
    ///   - delay: 延迟时间
    /// - Returns: 带延迟的动画
    public static func delayed(_ animation: Animation, by delay: Double) -> Animation {
        return animation.delay(delay)
    }
    
    /// 创建重复动画
    /// - Parameters:
    ///   - animation: 基础动画
    ///   - count: 重复次数，nil表示无限重复
    ///   - autoreverses: 是否往返
    /// - Returns: 重复动画
    public static func repeating(_ animation: Animation, count: Int? = nil, autoreverses: Bool = false) -> Animation {
        if let count = count {
            return animation.repeatCount(count, autoreverses: autoreverses)
        } else {
            return animation.repeatForever(autoreverses: autoreverses)
        }
    }
}

// MARK: - View Extensions

@available(iOS 15.0, macOS 10.15, *)
extension View {
    /// 应用动画配置
    /// - Parameter config: 动画配置
    /// - Returns: 应用了动画配置的视图
    public func animationConfig<V: Equatable>(_ config: SenseWordAnimations.AnimationConfig, value: V) -> some View {
        self.animation(
            config.delay > 0 ? config.animation.delay(config.delay) : config.animation,
            value: value
        )
    }
    
    /// 应用英雄转场动画
    public func heroTransition<V: Equatable>(value: V) -> some View {
        self.animation(SenseWordAnimations.heroTransition, value: value)
    }
    
    /// 应用幻灯片转场动画
    public func slideTransition<V: Equatable>(value: V) -> some View {
        self.animation(SenseWordAnimations.slideTransition, value: value)
    }
    
    /// 应用弹簧动画
    public func springAnimation<V: Equatable>(value: V) -> some View {
        self.animation(SenseWordAnimations.spring, value: value)
    }
}

// MARK: - Preview Support
// Preview代码已移除以避免编译时的平台兼容性问题
// 在Xcode中使用时，SwiftUI Preview将自动工作
