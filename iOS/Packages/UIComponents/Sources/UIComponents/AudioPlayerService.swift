//
//  AudioPlayerService.swift
//  UIComponents
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  音频播放服务 - 支持例句、短语、发音等音频播放
//

import Foundation
import AVFoundation



// MARK: - 数据类型定义

/// 发音数据类型（用于测试兼容性）
public struct Pronunciation {
    public let dialect: String
    public let symbol: String
    public let audioUrl: URL?

    public init(dialect: String, symbol: String, audioUrl: URL?) {
        self.dialect = dialect
        self.symbol = symbol
        self.audioUrl = audioUrl
    }
}

/// 例句数据类型（用于测试兼容性）
public struct ExampleSentence {
    public let english: String
    public let chinese: String
    public let audioUrl: URL?
    public let phraseBreakdown: [PhraseBreakdown]

    public init(english: String, chinese: String, audioUrl: URL?, phraseBreakdown: [PhraseBreakdown]) {
        self.english = english
        self.chinese = chinese
        self.audioUrl = audioUrl
        self.phraseBreakdown = phraseBreakdown
    }
}

/// 短语分解数据类型（用于测试兼容性）
public struct PhraseBreakdown {
    public let phrase: String
    public let translation: String
    public let audioUrl: String?

    public init(phrase: String, translation: String, audioUrl: String?) {
        self.phrase = phrase
        self.translation = translation
        self.audioUrl = audioUrl
    }
}

// MARK: - 音频播放错误类型

/// 音频播放错误枚举
public enum AudioPlayerError: Error, LocalizedError {
    case invalidURL(URL)
    case audioSessionSetupFailed
    case networkError
    case playbackFailed(Error)
    case audioDataCorrupted
    
    public var errorDescription: String? {
        switch self {
        case .invalidURL(let url):
            return "无效的音频 URL: \(url.absoluteString)"
        case .audioSessionSetupFailed:
            return "音频会话设置失败"
        case .networkError:
            return "网络连接错误，无法播放音频"
        case .playbackFailed(let error):
            return "音频播放失败: \(error.localizedDescription)"
        case .audioDataCorrupted:
            return "音频数据损坏"
        }
    }
}

// MARK: - 音频播放状态

/// 音频播放状态枚举
public enum AudioPlayerState: Equatable {
    case idle
    case loading
    case playing
    case paused
    case error(AudioPlayerError)
    
    /// 是否正在播放
    public var isPlaying: Bool {
        switch self {
        case .playing:
            return true
        default:
            return false
        }
    }
    
    /// 是否可以播放
    public var canPlay: Bool {
        switch self {
        case .idle, .paused:
            return true
        case .error:
            return false
        default:
            return false
        }
    }
    
    public static func == (lhs: AudioPlayerState, rhs: AudioPlayerState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.playing, .playing), (.paused, .paused):
            return true
        case (.error, .error):
            return true
        default:
            return false
        }
    }
}

// MARK: - 音频播放服务

/// 音频播放服务
/// 提供统一的音频播放接口，支持例句、短语、发音等各种音频内容
@MainActor
public class AudioPlayerService: NSObject, ObservableObject {
    
    // MARK: - 单例
    
    /// 共享实例
    public static let shared = AudioPlayerService()
    
    // MARK: - 发布属性
    
    /// 是否正在播放
    @Published public var isPlaying: Bool = false
    
    /// 当前播放的音频URL
    @Published public var currentAudioURL: URL?
    
    /// 播放进度 (0.0 - 1.0)
    @Published public var playbackProgress: Double = 0.0
    
    /// 错误消息
    @Published public var errorMessage: String?
    
    /// 播放状态
    @Published public var state: AudioPlayerState = .idle
    
    // MARK: - 私有属性
    
    /// 音频播放器
    private var audioPlayer: AVAudioPlayer?
    
    /// 进度更新定时器
    private var progressTimer: Timer?
    
    // MARK: - 初始化
    
    public override init() {
        super.init()
        setupAudioSession()
    }
    
    deinit {
        Task { @MainActor in
            stopCurrentAudio()
        }
    }
    
    // MARK: - 公共方法
    
    /// 播放音频
    /// - Parameter url: 音频URL
    public func playAudio(from url: URL) async throws {
        NSLog("🎵 AudioPlayerService: 开始播放音频 - \(url.absoluteString)")

        // 停止当前播放
        stopCurrentAudio()

        // 设置状态
        state = .loading
        currentAudioURL = url
        errorMessage = nil
        
        do {
            // 下载音频数据
            let (data, _) = try await URLSession.shared.data(from: url)
            
            // 创建音频播放器
            audioPlayer = try AVAudioPlayer(data: data)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            // 开始播放
            if audioPlayer?.play() == true {
                isPlaying = true
                state = .playing
                startProgressTimer()
                NSLog("✅ AudioPlayerService: 音频播放成功")
            } else {
                throw AudioPlayerError.playbackFailed(NSError(domain: "AudioPlayerService", code: -1, userInfo: [NSLocalizedDescriptionKey: "播放器启动失败"]))
            }
            
        } catch {
            NSLog("❌ AudioPlayerService: 音频播放失败 - \(error)")
            let audioError: AudioPlayerError
            
            if error is AudioPlayerError {
                audioError = error as! AudioPlayerError
            } else {
                audioError = AudioPlayerError.networkError
            }
            
            state = .error(audioError)
            errorMessage = audioError.errorDescription
            isPlaying = false
            currentAudioURL = nil
            
            throw audioError
        }
    }
    
    /// 停止当前音频播放
    public func stopCurrentAudio() {
        NSLog("🛑 AudioPlayerService: 停止音频播放")
        
        audioPlayer?.stop()
        audioPlayer = nil
        stopProgressTimer()
        
        isPlaying = false
        currentAudioURL = nil
        playbackProgress = 0.0
        state = .idle
        errorMessage = nil
    }
    
    /// 暂停音频播放
    public func pauseAudio() {
        guard isPlaying else { return }
        
        audioPlayer?.pause()
        isPlaying = false
        state = .paused
        stopProgressTimer()
        
        NSLog("⏸️ AudioPlayerService: 音频播放已暂停")
    }
    
    /// 恢复音频播放
    public func resumeAudio() {
        guard state == .paused, let player = audioPlayer else { return }
        
        if player.play() {
            isPlaying = true
            state = .playing
            startProgressTimer()
            NSLog("▶️ AudioPlayerService: 音频播放已恢复")
        }
    }
    
    // MARK: - 便捷方法
    
    /// 播放发音音频
    /// - Parameter pronunciation: 发音数据
    public func playPronunciation(_ pronunciation: Pronunciation) async throws {
        guard let audioUrl = pronunciation.audioUrl else {
            throw AudioPlayerError.invalidURL(URL(string: "")!)
        }
        try await playAudio(from: audioUrl)
    }
    
    /// 播放例句音频
    /// - Parameter example: 例句数据
    public func playExample(_ example: ExampleSentence) async throws {
        guard let audioUrl = example.audioUrl else {
            throw AudioPlayerError.invalidURL(URL(string: "")!)
        }
        try await playAudio(from: audioUrl)
    }
    
    /// 播放短语音频
    /// - Parameter phrase: 短语数据
    public func playPhrase(_ phrase: PhraseBreakdown) async throws {
        guard let audioUrlString = phrase.audioUrl,
              let audioUrl = URL(string: audioUrlString) else {
            throw AudioPlayerError.invalidURL(URL(string: "")!)
        }
        try await playAudio(from: audioUrl)
    }
    
    // MARK: - 私有方法
    
    /// 设置音频会话
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            NSLog("❌ AudioPlayerService: 音频会话设置失败 - \(error)")
            state = .error(.audioSessionSetupFailed)
            errorMessage = AudioPlayerError.audioSessionSetupFailed.errorDescription
        }
    }
    
    /// 开始进度更新定时器
    private func startProgressTimer() {
        stopProgressTimer()
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateProgress()
            }
        }
    }
    
    /// 停止进度更新定时器
    private func stopProgressTimer() {
        progressTimer?.invalidate()
        progressTimer = nil
    }
    
    /// 更新播放进度
    private func updateProgress() {
        guard let player = audioPlayer, player.duration > 0 else { return }
        playbackProgress = player.currentTime / player.duration
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioPlayerService: @preconcurrency AVAudioPlayerDelegate {

    nonisolated public func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        NSLog("🎉 AudioPlayerService: 音频播放完成 - 成功: \(flag)")

        Task { @MainActor in
            isPlaying = false
            playbackProgress = 1.0
            state = .idle
            stopProgressTimer()
        }

        // 保持 currentAudioURL，允许重新播放
    }
    
    nonisolated public func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        NSLog("❌ AudioPlayerService: 音频解码错误 - \(error?.localizedDescription ?? "未知错误")")

        Task { @MainActor in
            let audioError = AudioPlayerError.audioDataCorrupted
            state = .error(audioError)
            errorMessage = audioError.errorDescription
            isPlaying = false
            stopProgressTimer()
        }
    }
}
