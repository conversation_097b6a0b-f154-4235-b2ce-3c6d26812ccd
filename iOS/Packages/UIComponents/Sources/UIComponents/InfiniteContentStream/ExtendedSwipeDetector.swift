import Foundation
import SwiftUI

// MARK: - 扩展滑动检测器

/**
 * @description 扩展滑动检测器
 * 实现精确的手势检测逻辑，区分同义词切换和内容流触发
 * 支持右侧边缘区域检测和双阈值判断
 *
 * 遵循TADA架构原则：
 * - UIComponents独立于业务逻辑
 * - 通过参数传递配置，不依赖外部模型
 * - 提供纯粹的手势检测功能
 */
public struct ExtendedSwipeDetector {

    // MARK: - 手势检测结果枚举

    /**
     * @description 手势检测结果
     * 简化的枚举，避免依赖外部模型
     */
    public enum DetectionResult: CaseIterable, Equatable {
        case switchSynonym          // 切换同义词
        case triggerContentStream   // 触发内容流
        case showStreamHint        // 显示内容流提示
        case none                  // 无动作
    }

    // MARK: - 阈值配置结构

    /**
     * @description 手势阈值配置
     * 内嵌的配置结构，避免外部依赖
     */
    public struct Thresholds {
        public let synonymSwitchThreshold: CGFloat
        public let contentStreamThreshold: CGFloat
        public let rightEdgeZone: CGFloat
        public let hintThreshold: CGFloat

        public static let `default` = Thresholds(
            synonymSwitchThreshold: 50.0,
            contentStreamThreshold: 120.0,
            rightEdgeZone: 80.0,
            hintThreshold: 30.0
        )

        public init(
            synonymSwitchThreshold: CGFloat,
            contentStreamThreshold: CGFloat,
            rightEdgeZone: CGFloat,
            hintThreshold: CGFloat
        ) {
            self.synonymSwitchThreshold = synonymSwitchThreshold
            self.contentStreamThreshold = contentStreamThreshold
            self.rightEdgeZone = rightEdgeZone
            self.hintThreshold = hintThreshold
        }
    }

    // MARK: - 静态检测方法

    /**
     * 检测滑动动作类型
     * @param translation 滑动距离（负值表示左滑）
     * @param startLocation 滑动开始位置
     * @param viewWidth 视图宽度
     * @param thresholds 阈值配置
     * @return 检测到的滑动动作
     */
    public static func detectSwipeAction(
        translation: CGFloat,
        startLocation: CGPoint,
        viewWidth: CGFloat,
        thresholds: Thresholds = .default
    ) -> DetectionResult {
        
        // 检查是否在右侧边缘区域
        let isInRightEdge = startLocation.x > (viewWidth - thresholds.rightEdgeZone)
        
        // 只处理左滑手势（负值）
        guard translation < 0 else { return .none }
        
        let absTranslation = abs(translation)
        
        if isInRightEdge {
            // 右侧边缘区域的左滑处理
            if absTranslation >= thresholds.contentStreamThreshold {
                // 强力左滑 → 直接触发内容流
                return .triggerContentStream
            } else if absTranslation >= thresholds.hintThreshold {
                // 中等左滑 → 显示内容流提示
                return .showStreamHint
            }
        } else {
            // 非右侧边缘区域的左滑处理
            if absTranslation >= thresholds.synonymSwitchThreshold {
                // 普通左滑 → 切换同义词
                return .switchSynonym
            }
        }
        
        return .none
    }
    
    /**
     * 检测滑动动作并返回详细结果
     * @param translation 滑动距离
     * @param startLocation 滑动开始位置
     * @param viewWidth 视图宽度
     * @param thresholds 阈值配置
     * @return 详细的检测结果
     */
    public static func detectSwipeActionDetailed(
        translation: CGFloat,
        startLocation: CGPoint,
        viewWidth: CGFloat,
        thresholds: Thresholds = .default
    ) -> (result: DetectionResult, confidence: Double, isInRightEdge: Bool) {
        
        let action = detectSwipeAction(
            translation: translation,
            startLocation: startLocation,
            viewWidth: viewWidth,
            thresholds: thresholds
        )
        
        let isInRightEdge = startLocation.x > (viewWidth - thresholds.rightEdgeZone)
        let confidence = calculateConfidence(
            translation: translation,
            action: action,
            thresholds: thresholds
        )
        
        return (result: action, confidence: confidence, isInRightEdge: isInRightEdge)
    }
    
    // MARK: - 私有辅助方法
    
    /**
     * 计算检测置信度
     * @param translation 滑动距离
     * @param action 检测到的动作
     * @param thresholds 阈值配置
     * @return 置信度 (0.0-1.0)
     */
    private static func calculateConfidence(
        translation: CGFloat,
        action: DetectionResult,
        thresholds: Thresholds
    ) -> Double {
        
        guard action != .none else { return 0.0 }

        let absTranslation = abs(translation)

        // 根据动作类型获取对应的阈值
        let actionThreshold: CGFloat
        switch action {
        case .triggerContentStream:
            actionThreshold = thresholds.contentStreamThreshold
        case .showStreamHint:
            actionThreshold = thresholds.hintThreshold
        case .switchSynonym:
            actionThreshold = thresholds.synonymSwitchThreshold
        case .none:
            return 0.0
        }

        // 基础置信度：超过阈值的程度
        let baseConfidence = min(1.0, Double(absTranslation / actionThreshold))

        // 根据动作类型调整置信度
        switch action {
        case .triggerContentStream:
            // 内容流触发需要更高的置信度
            return min(1.0, baseConfidence * 1.2)

        case .showStreamHint:
            // 提示显示的置信度适中
            return min(1.0, baseConfidence * 1.0)

        case .switchSynonym:
            // 同义词切换的置信度稍低
            return min(1.0, baseConfidence * 0.9)

        case .none:
            return 0.0
        }
    }
    
    // MARK: - 手势验证方法
    
    /**
     * 验证手势是否有效
     * @param translation 滑动距离
     * @param velocity 滑动速度
     * @param startLocation 开始位置
     * @param viewBounds 视图边界
     * @return 是否为有效手势
     */
    public static func isValidSwipeGesture(
        translation: CGFloat,
        velocity: CGFloat,
        startLocation: CGPoint,
        viewBounds: CGRect
    ) -> Bool {
        
        // 检查开始位置是否在视图范围内
        guard viewBounds.contains(startLocation) else { return false }
        
        // 检查滑动距离是否足够
        guard abs(translation) > 10.0 else { return false }
        
        // 检查滑动速度是否合理（避免意外触发）
        guard abs(velocity) > 50.0 && abs(velocity) < 3000.0 else { return false }
        
        return true
    }
    
    /**
     * 检查是否为右侧边缘手势
     * @param startLocation 开始位置
     * @param viewWidth 视图宽度
     * @param thresholds 阈值配置
     * @return 是否在右侧边缘
     */
    public static func isRightEdgeGesture(
        startLocation: CGPoint,
        viewWidth: CGFloat,
        thresholds: Thresholds = .default
    ) -> Bool {
        return startLocation.x > (viewWidth - thresholds.rightEdgeZone)
    }
    
    // MARK: - 调试和分析方法
    
    /**
     * 获取手势分析信息（用于调试）
     * @param translation 滑动距离
     * @param startLocation 开始位置
     * @param viewWidth 视图宽度
     * @param thresholds 阈值配置
     * @return 分析信息字典
     */
    public static func getGestureAnalysis(
        translation: CGFloat,
        startLocation: CGPoint,
        viewWidth: CGFloat,
        thresholds: Thresholds = .default
    ) -> [String: Any] {
        
        let result = detectSwipeActionDetailed(
            translation: translation,
            startLocation: startLocation,
            viewWidth: viewWidth,
            thresholds: thresholds
        )
        
        let rightEdgeDistance = viewWidth - startLocation.x
        let absTranslation = abs(translation)
        
        return [
            "action": String(describing: result.result),
            "confidence": result.confidence,
            "isInRightEdge": result.isInRightEdge,
            "translationDistance": translation,
            "absTranslationDistance": absTranslation,
            "startLocationX": startLocation.x,
            "rightEdgeDistance": rightEdgeDistance,
            "viewWidth": viewWidth,
            "rightEdgeZone": thresholds.rightEdgeZone,
            "contentStreamThreshold": thresholds.contentStreamThreshold,
            "synonymSwitchThreshold": thresholds.synonymSwitchThreshold,
            "hintThreshold": thresholds.hintThreshold,
            "timestamp": Date().timeIntervalSince1970
        ]
    }
    
    /**
     * 打印手势分析信息（用于调试）
     */
    public static func printGestureAnalysis(
        translation: CGFloat,
        startLocation: CGPoint,
        viewWidth: CGFloat,
        thresholds: Thresholds = .default
    ) {
        let analysis = getGestureAnalysis(
            translation: translation,
            startLocation: startLocation,
            viewWidth: viewWidth,
            thresholds: thresholds
        )
        
        print("🔍 [ExtendedSwipeDetector] 手势分析:")
        print("   动作: \(analysis["action"] ?? "未知")")
        print("   置信度: \(String(format: "%.2f", analysis["confidence"] as? Double ?? 0.0))")
        print("   右侧边缘: \(analysis["isInRightEdge"] as? Bool ?? false)")
        print("   滑动距离: \(String(format: "%.1f", analysis["translationDistance"] as? CGFloat ?? 0.0))px")
        print("   开始位置X: \(String(format: "%.1f", analysis["startLocationX"] as? CGFloat ?? 0.0))px")
        print("   距离右边缘: \(String(format: "%.1f", analysis["rightEdgeDistance"] as? CGFloat ?? 0.0))px")
    }
}

// MARK: - 扩展：DetectionResult 便捷方法

extension ExtendedSwipeDetector.DetectionResult {
    
    /**
     * 是否需要触觉反馈
     */
    public var needsHapticFeedback: Bool {
        switch self {
        case .triggerContentStream:
            return true
        case .switchSynonym:
            return true
        case .showStreamHint:
            return false  // 提示不需要震动
        case .none:
            return false
        }
    }
    
    /**
     * 触觉反馈强度
     */
    public var hapticIntensity: UIImpactFeedbackGenerator.FeedbackStyle {
        switch self {
        case .triggerContentStream:
            return .heavy
        case .switchSynonym:
            return .medium
        case .showStreamHint:
            return .light
        case .none:
            return .light
        }
    }
}

// MARK: - 扩展：Thresholds 便捷方法

extension ExtendedSwipeDetector.Thresholds {

    /**
     * 为特定设备类型优化的阈值
     */
    public static func optimizedForDevice() -> ExtendedSwipeDetector.Thresholds {
        let screenWidth = UIScreen.main.bounds.width

        // 根据设备屏幕宽度调整
        if screenWidth <= 375 {
            // iPhone SE, iPhone 12 mini 等小屏设备
            return ExtendedSwipeDetector.Thresholds(
                synonymSwitchThreshold: 40.0,
                contentStreamThreshold: 100.0,
                rightEdgeZone: 60.0,
                hintThreshold: 25.0
            )
        } else if screenWidth >= 428 {
            // iPhone 12 Pro Max, iPhone 13 Pro Max 等大屏设备
            return ExtendedSwipeDetector.Thresholds(
                synonymSwitchThreshold: 60.0,
                contentStreamThreshold: 140.0,
                rightEdgeZone: 100.0,
                hintThreshold: 35.0
            )
        } else {
            // 标准设备
            return .default
        }
    }
}
