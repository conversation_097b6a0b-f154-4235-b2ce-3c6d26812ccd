{"originHash": "a6367d3fe089e09ae89e8de09a4668e1a7764cdb109f58f735ae3af9c8651c26", "pins": [{"identity": "sqlite.swift", "kind": "remoteSourceControl", "location": "https://github.com/stephencelis/SQLite.swift.git", "state": {"revision": "392dd6058624d9f6c5b4c769d165ddd8c7293394", "version": "0.15.4"}}, {"identity": "swift-toolchain-sqlite", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-toolchain-sqlite", "state": {"revision": "b626d3002773b1a1304166643e7f118f724b2132", "version": "1.0.4"}}, {"identity": "viewins<PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/nalexn/ViewInspector", "state": {"revision": "a6fcac8485bc8f57b2d2b55bb6d97138e8659e4b", "version": "0.10.2"}}], "version": 3}