import XCTest
import Combine
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
@MainActor
final class WordCardViewModelTests: XCTestCase {
    
    var viewModel: WordCardViewModel!
    var mockWordService: MockWordService!
    var mockProgressService: MockLearningProgressService!
    var mockAudioService: MockAudioPlayerService!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() async throws {
        try await super.setUp()

        mockWordService = MockWordService()
        mockProgressService = MockLearningProgressService()
        mockAudioService = MockAudioPlayerService()
        cancellables = Set<AnyCancellable>()

        viewModel = WordCardViewModel(
            wordService: mockWordService,
            progressService: mockProgressService,
            audioService: mockAudioService
        )

        // Reset call counts after initialization
        mockWordService.reset()
        mockProgressService.reset()
        mockAudioService.reset()
    }
    
    override func tearDown() async throws {
        viewModel = nil
        mockWordService = nil
        mockProgressService = nil
        mockAudioService = nil
        cancellables = nil
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testWordCardViewModel_Initialization_ShouldSetDefaultValues() {
        // Given & When & Then
        XCTAssertNil(viewModel.currentWord)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.errorMessage)
        XCTAssertEqual(viewModel.masteredCount, 0)
        XCTAssertEqual(viewModel.reviewCount, 0)
        XCTAssertNil(viewModel.wordProgress)
        XCTAssertFalse(viewModel.isPlayingAudio)
        XCTAssertNil(viewModel.currentAudioURL)
    }
    
    func testWordCardViewModel_ConvenienceInit_ShouldCreateWithMockServices() {
        // Given & When
        let testViewModel = WordCardViewModel()
        
        // Then
        XCTAssertNotNil(testViewModel)
        XCTAssertNil(testViewModel.currentWord)
        XCTAssertFalse(testViewModel.isLoading)
    }
    
    // MARK: - Word Loading Tests
    
    func testLoadWord_WithValidID_ShouldUpdateCurrentWord() async {
        // Given
        let expectedWord = WordDTO.mock()
        mockWordService.setMockWord(expectedWord)
        mockProgressService.setMasteredCount(5)
        mockProgressService.setReviewCount(3)
        
        // When
        await viewModel.loadWord(id: "test")
        
        // Then
        XCTAssertEqual(viewModel.currentWord?.word, expectedWord.word)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNil(viewModel.errorMessage)
        XCTAssertEqual(viewModel.masteredCount, 5)
        XCTAssertEqual(viewModel.reviewCount, 3)
        XCTAssertEqual(mockWordService.getWordCallCount, 1)
        XCTAssertEqual(mockWordService.lastRequestedWordId, "test")
        XCTAssertEqual(mockProgressService.getWordProgressCallCount, 1)
        XCTAssertEqual(mockProgressService.getMasteredCountCallCount, 1)
        XCTAssertEqual(mockProgressService.getReviewCountCallCount, 1)
    }
    
    func testLoadWord_WithInvalidID_ShouldSetErrorMessage() async {
        // Given
        mockWordService.setMockError(SenseWordError.wordNotFound(id: "invalid"))
        
        // When
        await viewModel.loadWord(id: "invalid")
        
        // Then
        XCTAssertNil(viewModel.currentWord)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.errorMessage!.contains("未找到单词"))
        XCTAssertEqual(mockWordService.getWordCallCount, 1)
        XCTAssertEqual(mockWordService.lastRequestedWordId, "invalid")
    }
    
    func testLoadWord_WithNetworkError_ShouldSetGenericErrorMessage() async {
        // Given
        mockWordService.setMockError(URLError(.notConnectedToInternet))
        
        // When
        await viewModel.loadWord(id: "test")
        
        // Then
        XCTAssertNil(viewModel.currentWord)
        XCTAssertFalse(viewModel.isLoading)
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertEqual(viewModel.errorMessage, "加载单词时发生未知错误")
    }
    
    func testLoadWord_ShouldSetLoadingStateCorrectly() async {
        // Given
        let expectedWord = WordDTO.mock()
        mockWordService.setMockWord(expectedWord)
        
        var loadingStates: [Bool] = []
        
        viewModel.$isLoading
            .sink { isLoading in
                loadingStates.append(isLoading)
            }
            .store(in: &cancellables)
        
        // When
        await viewModel.loadWord(id: "test")
        
        // Then
        // Should have: [false (initial), true (loading), false (completed)]
        XCTAssertTrue(loadingStates.contains(true))
        XCTAssertFalse(viewModel.isLoading)
    }
    
    // MARK: - Progress Management Tests
    
    func testMarkWordMastered_ShouldIncrementMasteredCount() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord
        let initialCount = viewModel.masteredCount
        
        // When
        await viewModel.markWordMastered()
        
        // Then
        XCTAssertEqual(viewModel.masteredCount, initialCount + 1)
        XCTAssertEqual(mockProgressService.markMasteredCallCount, 1)
        XCTAssertEqual(mockProgressService.lastMasteredWordId, mockWord.id)
        XCTAssertEqual(mockProgressService.getWordProgressCallCount, 1)
    }
    
    func testMarkWordMastered_WithNoCurrentWord_ShouldNotCallService() async {
        // Given
        viewModel.currentWord = nil
        
        // When
        await viewModel.markWordMastered()
        
        // Then
        XCTAssertEqual(mockProgressService.markMasteredCallCount, 0)
    }
    
    func testMarkWordMastered_WithServiceError_ShouldSetErrorMessage() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord
        mockProgressService.markMasteredResult = .failure(SenseWordError.progressUpdateFailed)
        
        // When
        await viewModel.markWordMastered()
        
        // Then
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.errorMessage!.contains("学习进度更新失败"))
    }
    
    func testAddWordToReview_ShouldIncrementReviewCount() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord
        let initialCount = viewModel.reviewCount
        
        // When
        await viewModel.addWordToReview()
        
        // Then
        XCTAssertEqual(viewModel.reviewCount, initialCount + 1)
        XCTAssertEqual(mockProgressService.addToReviewCallCount, 1)
        XCTAssertEqual(mockProgressService.lastReviewWordId, mockWord.id)
        XCTAssertEqual(mockProgressService.getWordProgressCallCount, 1)
    }
    
    func testAddWordToReview_WithNoCurrentWord_ShouldNotCallService() async {
        // Given
        viewModel.currentWord = nil
        
        // When
        await viewModel.addWordToReview()
        
        // Then
        XCTAssertEqual(mockProgressService.addToReviewCallCount, 0)
    }
    
    // MARK: - Swipe Action Tests
    
    func testHandleSwipeAction_Mastered_ShouldCallMarkWordMastered() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord
        
        // When
        await viewModel.handleSwipeAction(.mastered)
        
        // Then
        XCTAssertEqual(mockProgressService.markMasteredCallCount, 1)
        XCTAssertEqual(mockProgressService.lastMasteredWordId, mockWord.id)
    }
    
    func testHandleSwipeAction_AddedToReview_ShouldCallAddWordToReview() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord

        // When
        await viewModel.handleSwipeAction(.addedToReview)

        // Then
        XCTAssertEqual(mockProgressService.addToReviewCallCount, 1)
        XCTAssertEqual(mockProgressService.lastReviewWordId, mockWord.id)
    }

    func testHandleSwipeAction_Skipped_ShouldNotCallAnyService() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord

        // When
        await viewModel.handleSwipeAction(.skipped)

        // Then
        XCTAssertEqual(mockProgressService.markMasteredCallCount, 0)
        XCTAssertEqual(mockProgressService.addToReviewCallCount, 0)
    }

    func testHandleSwipeAction_Viewed_ShouldNotCallAnyService() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord

        // When
        await viewModel.handleSwipeAction(.viewed)

        // Then
        XCTAssertEqual(mockProgressService.markMasteredCallCount, 0)
        XCTAssertEqual(mockProgressService.addToReviewCallCount, 0)
    }
    
    // MARK: - Audio Playback Tests
    
    func testPlayPronunciation_ShouldCallAudioService() async {
        // Given
        let pronunciation = Pronunciation(
            dialect: "NAmE",
            symbol: "/test/",
            audioUrl: URL(string: "https://example.com/test.mp3")!
        )
        
        // When
        await viewModel.playPronunciation(pronunciation)
        
        // Then
        XCTAssertEqual(mockAudioService.playPronunciationCallCount, 1)
        XCTAssertEqual(mockAudioService.lastPlayedURL, pronunciation.audioUrl)
    }
    
    func testPlayExample_ShouldCallAudioService() async {
        // Given
        let example = ExampleSentence(
            english: "Test sentence",
            chinese: "测试句子",
            audioUrl: URL(string: "https://example.com/example.mp3")!,
            phraseBreakdown: []
        )
        
        // When
        await viewModel.playExample(example)
        
        // Then
        XCTAssertEqual(mockAudioService.playExampleCallCount, 1)
        XCTAssertEqual(mockAudioService.lastPlayedURL, example.audioUrl)
    }
    
    func testPlayPhrase_ShouldCallAudioService() async {
        // Given
        let phrase = PhraseBreakdown(
            phrase: "test phrase",
            translation: "测试短语",
            audioUrl: URL(string: "https://example.com/phrase.mp3")!
        )
        
        // When
        await viewModel.playPhrase(phrase)
        
        // Then
        XCTAssertEqual(mockAudioService.playPhraseCallCount, 1)
        XCTAssertEqual(mockAudioService.lastPlayedURL, phrase.audioUrl)
    }
    
    func testPlayAudio_WithValidURL_ShouldCallAudioService() async {
        // Given
        let audioURL = URL(string: "https://example.com/audio.mp3")!
        
        // When
        await viewModel.playAudio(from: audioURL)
        
        // Then
        XCTAssertEqual(mockAudioService.playAudioCallCount, 1)
        XCTAssertEqual(mockAudioService.lastPlayedURL, audioURL)
    }
    
    func testPlayAudio_WithError_ShouldSetErrorMessage() async {
        // Given
        let audioURL = URL(string: "https://example.com/audio.mp3")!
        mockAudioService.setPlaybackError(SenseWordError.audioPlaybackFailed(url: audioURL))
        
        // When
        await viewModel.playAudio(from: audioURL)
        
        // Then
        XCTAssertNotNil(viewModel.errorMessage)
        XCTAssertTrue(viewModel.errorMessage!.contains("音频播放失败"))
    }
    
    func testStopAudio_ShouldCallAudioService() {
        // When
        viewModel.stopAudio()
        
        // Then
        XCTAssertEqual(mockAudioService.stopAudioCallCount, 1)
    }
    
    // MARK: - Utility Methods Tests
    
    func testClearError_ShouldSetErrorMessageToNil() {
        // Given
        viewModel.errorMessage = "Test error"
        
        // When
        viewModel.clearError()
        
        // Then
        XCTAssertNil(viewModel.errorMessage)
    }
    
    func testRefreshCurrentWord_WithCurrentWord_ShouldReloadWord() async {
        // Given
        let mockWord = WordDTO.mock()
        viewModel.currentWord = mockWord
        mockWordService.setMockWord(mockWord)
        
        // When
        await viewModel.refreshCurrentWord()
        
        // Then
        XCTAssertEqual(mockWordService.getWordCallCount, 1)
        XCTAssertEqual(mockWordService.lastRequestedWordId, mockWord.id)
    }
    
    func testRefreshCurrentWord_WithNoCurrentWord_ShouldNotCallService() async {
        // Given
        viewModel.currentWord = nil

        // When
        await viewModel.refreshCurrentWord()

        // Then
        XCTAssertEqual(mockWordService.getWordCallCount, 0)
    }

    // MARK: - Extension Properties Tests

    func testIsCurrentWordMastered_WithMasteredProgress_ShouldReturnTrue() {
        // Given
        viewModel.wordProgress = WordLearningProgress(
            id: "test",
            wordId: "test",
            userId: "user",
            status: .mastered,
            viewCount: 1
        )

        // When & Then
        XCTAssertTrue(viewModel.isCurrentWordMastered)
    }

    func testIsCurrentWordMastered_WithNonMasteredProgress_ShouldReturnFalse() {
        // Given
        viewModel.wordProgress = WordLearningProgress(
            id: "test",
            wordId: "test",
            userId: "user",
            status: .inProgress,
            viewCount: 1
        )

        // When & Then
        XCTAssertFalse(viewModel.isCurrentWordMastered)
    }

    func testIsCurrentWordInReview_WithInProgressStatus_ShouldReturnTrue() {
        // Given
        viewModel.wordProgress = WordLearningProgress(
            id: "test",
            wordId: "test",
            userId: "user",
            status: .inProgress,
            viewCount: 1
        )

        // When & Then
        XCTAssertTrue(viewModel.isCurrentWordInReview)
    }

    func testIsCurrentWordInReview_WithNonInProgressStatus_ShouldReturnFalse() {
        // Given
        viewModel.wordProgress = WordLearningProgress(
            id: "test",
            wordId: "test",
            userId: "user",
            status: .completed,
            viewCount: 1
        )

        // When & Then
        XCTAssertFalse(viewModel.isCurrentWordInReview)
    }

    func testFormattedMasteredCount_ShouldReturnCorrectFormat() {
        // Given
        viewModel.masteredCount = 42

        // When & Then
        XCTAssertEqual(viewModel.formattedMasteredCount, "42 个已掌握")
    }

    func testFormattedReviewCount_ShouldReturnCorrectFormat() {
        // Given
        viewModel.reviewCount = 15

        // When & Then
        XCTAssertEqual(viewModel.formattedReviewCount, "15 个待复习")
    }
}
