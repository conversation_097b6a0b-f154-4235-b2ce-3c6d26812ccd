import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
final class ContextualExplanationViewTests: XCTestCase {
    
    // MARK: - Test Data
    
    private let sampleExplanation = ContextualExplanation(
        nativeSpeakerIntent: "母语者使用这个词时，通常想表达某事物非常坚固、可靠，能够承受压力或挑战。",
        emotionalResonance: "这个词传递出一种积极、强壮、值得信赖的感觉。",
        vividImagery: "想象一个东西非常坚固、耐用，不容易坏，这就是'robust'。",
        etymologicalEssence: "来自拉丁语 'robustus'，意为橡树般强壮。"
    )
    
    private let shortExplanation = ContextualExplanation(
        nativeSpeakerIntent: "简短说明",
        emotionalResonance: "简短情感",
        vividImagery: "简短意象",
        etymologicalEssence: "简短词源"
    )
    
    // MARK: - Initialization Tests
    
    func testContextualExplanationView_Initialization_ShouldCreateWithExplanation() {
        // Given & When
        let view = ContextualExplanationView(explanation: sampleExplanation)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.explanation.nativeSpeakerIntent, sampleExplanation.nativeSpeakerIntent)
        XCTAssertEqual(view.explanation.emotionalResonance, sampleExplanation.emotionalResonance)
        XCTAssertEqual(view.explanation.vividImagery, sampleExplanation.vividImagery)
        XCTAssertEqual(view.explanation.etymologicalEssence, sampleExplanation.etymologicalEssence)
    }
    
    // MARK: - Content Display Tests
    
    func testContextualExplanationView_SectionHeader_ShouldDisplayCorrectTitle() throws {
        // Given
        let view = ContextualExplanationView(explanation: sampleExplanation)
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let titleText = try headerHStack.text(1)
        XCTAssertEqual(try titleText.string(), "情境深度解析")
    }
    
    func testContextualExplanationView_SectionHeader_ShouldHaveLightbulbIcon() throws {
        // Given
        let view = ContextualExplanationView(explanation: sampleExplanation)
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let iconImage = try headerHStack.image(0)
        XCTAssertEqual(try iconImage.actualImage().name(), "lightbulb.fill")
    }
    
    func testContextualExplanationView_ExplanationSections_ShouldHaveFourSections() throws {
        // Given
        let view = ContextualExplanationView(explanation: sampleExplanation)
        
        // When
        let vStack = try view.inspect().vStack()
        let lazyVStack = try vStack.lazyVStack(1)
        
        // Then
        // Should have 4 sections (nativeSpeakerIntent, emotionalResonance, vividImagery, etymologicalEssence)
        XCTAssertEqual(try lazyVStack.forEach(0).count, 4)
    }
    
    // MARK: - Content Validation Tests

    func testContextualExplanationView_ShouldDisplayAllExplanationContent() {
        // Given & When
        let view = ContextualExplanationView(explanation: sampleExplanation)

        // Then
        XCTAssertEqual(view.explanation.nativeSpeakerIntent, sampleExplanation.nativeSpeakerIntent)
        XCTAssertEqual(view.explanation.emotionalResonance, sampleExplanation.emotionalResonance)
        XCTAssertEqual(view.explanation.vividImagery, sampleExplanation.vividImagery)
        XCTAssertEqual(view.explanation.etymologicalEssence, sampleExplanation.etymologicalEssence)
    }
    
    // MARK: - State Management Tests

    func testContextualExplanationView_InitialState_ShouldBeValid() {
        // Given & When
        let view = ContextualExplanationView(explanation: sampleExplanation)

        // Then
        // View should be created successfully
        XCTAssertNotNil(view)
    }
    
    // MARK: - Edge Cases Tests
    
    func testContextualExplanationView_EmptyContent_ShouldHandleGracefully() {
        // Given
        let emptyExplanation = ContextualExplanation(
            nativeSpeakerIntent: "",
            emotionalResonance: "",
            vividImagery: "",
            etymologicalEssence: ""
        )

        // When & Then
        let view = ContextualExplanationView(explanation: emptyExplanation)
        XCTAssertNotNil(view)
        XCTAssertTrue(view.explanation.nativeSpeakerIntent.isEmpty)
        XCTAssertTrue(view.explanation.emotionalResonance.isEmpty)
        XCTAssertTrue(view.explanation.vividImagery.isEmpty)
        XCTAssertTrue(view.explanation.etymologicalEssence.isEmpty)
    }

    func testContextualExplanationView_ShortContent_ShouldDisplayCorrectly() {
        // Given & When
        let view = ContextualExplanationView(explanation: shortExplanation)

        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.explanation.nativeSpeakerIntent, "简短说明")
        XCTAssertEqual(view.explanation.emotionalResonance, "简短情感")
        XCTAssertEqual(view.explanation.vividImagery, "简短意象")
        XCTAssertEqual(view.explanation.etymologicalEssence, "简短词源")
    }
    
    // MARK: - Performance Tests
    
    func testContextualExplanationView_Performance_ShouldCreateQuickly() {
        // Given
        let explanation = sampleExplanation
        
        // When & Then
        measure {
            for _ in 0..<100 {
                let _ = ContextualExplanationView(explanation: explanation)
            }
        }
    }
}


