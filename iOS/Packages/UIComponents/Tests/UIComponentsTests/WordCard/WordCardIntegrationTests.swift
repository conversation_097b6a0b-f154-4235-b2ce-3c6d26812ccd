import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents
@testable import SharedModels

/// Integration tests for WordCardView slideshow mode
@available(iOS 15.0, *)
final class WordCardIntegrationTests: XCTestCase {
    
    var mockWord: WordDTO!
    var audioPlaybackCalled: Bool = false
    var swipeActionCalled: SwipeGestureHandler.SwipeAction?
    
    override func setUp() {
        super.setUp()
        setupMockData()
    }
    
    override func tearDown() {
        mockWord = nil
        audioPlaybackCalled = false
        swipeActionCalled = nil
        super.tearDown()
    }
    
    // MARK: - Setup
    
    private func setupMockData() {
        mockWord = WordDTO(
            word: "robust",
            difficulty: "B2",
            pronunciations: [
                Pronunciation(
                    dialect: "NAmE",
                    symbol: "/roʊˈbʌst/",
                    audioUrl: URL(string: "https://example.com/robust.mp3")!
                ),
                Pronunciation(
                    dialect: "BrE",
                    symbol: "/rəʊˈbʌst/",
                    audioUrl: URL(string: "https://example.com/robust_br.mp3")!
                )
            ],
            coreDefinition: "强健的；可靠的；坚固的",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "母语者使用这个词时，通常想表达某事物非常坚固、可靠，能够承受压力或挑战。",
                emotionalResonance: "这个词传递出一种积极、强壮、值得信赖的感觉。",
                vividImagery: "想象一个东西非常坚固、耐用，不容易坏，就像一座坚固的城堡。",
                etymologicalEssence: "来自拉丁语 robustus，意为橡木般坚硬。"
            ),
            examples: [
                ExampleCategory(
                    category: "技术场景",
                    examples: [
                        ExampleSentence(
                            english: "The robust system can handle millions of users.",
                            chinese: "这个强大的系统可以处理数百万用户。",
                            audioUrl: URL(string: "https://example.com/example1.mp3")!,
                            phraseBreakdown: [
                                PhraseBreakdown(
                                    phrase: "robust system",
                                    translation: "强大的系统",
                                    audioUrl: URL(string: "https://example.com/phrase1.mp3")!
                                ),
                                PhraseBreakdown(
                                    phrase: "handle millions of users",
                                    translation: "处理数百万用户",
                                    audioUrl: URL(string: "https://example.com/phrase2.mp3")!
                                )
                            ]
                        )
                    ]
                )
            ],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: [
                Synonym(
                    word: "strong",
                    explanation: "strong 强调力量，而 robust 更强调在复杂环境下的稳定性和可靠性。",
                    examples: [
                        SynonymExample(
                            sentence: "The strong foundation supports the building.",
                            translation: "坚固的地基支撑着建筑物。"
                        )
                    ]
                ),
                Synonym(
                    word: "sturdy",
                    explanation: "sturdy 强调结构坚固，而 robust 更强调系统的可靠性。",
                    examples: [
                        SynonymExample(
                            sentence: "The sturdy table can hold heavy books.",
                            translation: "这张结实的桌子可以承受重书。"
                        )
                    ]
                ),
                Synonym(
                    word: "solid",
                    explanation: "solid 强调稳固，而 robust 更强调在压力下的表现。",
                    examples: [
                        SynonymExample(
                            sentence: "The solid construction ensures safety.",
                            translation: "坚固的建筑确保安全。"
                        )
                    ]
                )
            ]
        )
    }
    
    // MARK: - Test Cases
    
    func testWordCardViewInitialization() throws {
        // Given
        let wordCardView = createWordCardView()
        
        // When
        let inspectedView = try wordCardView.inspect()
        
        // Then
        XCTAssertNoThrow(try inspectedView.find(ViewType.ZStack.self))
        // 验证初始状态是传统卡片模式
        // 注意：由于SwiftUI的条件渲染，我们需要检查ZStack的内容
    }
    
    @MainActor
    func testSlideContentDataPreparation() {
        // Given
        let viewModel = WordCardViewModel()
        viewModel.currentWord = mockWord

        // When
        viewModel.enterSlideMode()

        // Then
        XCTAssertTrue(viewModel.isInSlideMode)
        XCTAssertEqual(viewModel.currentSlideIndex, 0)
        XCTAssertFalse(viewModel.slideData.isEmpty)
        
        // 验证幻灯片数据包含预期的内容类型
        let slideTypes = viewModel.slideData.map { slide in
            switch slide {
            case .phonetics:
                return "phonetics"
            case .contextual:
                return "contextual"
            case .example:
                return "example"
            }
        }
        
        XCTAssertTrue(slideTypes.contains("phonetics"))
        XCTAssertTrue(slideTypes.contains("contextual"))
        XCTAssertTrue(slideTypes.contains("example"))
    }
    
    @MainActor
    func testSlideDataTransformation() {
        // Given
        let viewModel = WordCardViewModel()
        viewModel.currentWord = mockWord

        // When
        viewModel.enterSlideMode()
        
        // Then
        // 验证音标幻灯片
        let phoneticsSlides = viewModel.slideData.compactMap { slide in
            if case .phonetics(let word, let pronunciations, let coreDefinition) = slide {
                return (word, pronunciations, coreDefinition)
            }
            return nil
        }
        
        XCTAssertEqual(phoneticsSlides.count, 1)
        XCTAssertEqual(phoneticsSlides.first?.0, "robust")
        XCTAssertEqual(phoneticsSlides.first?.1.count, 2)
        XCTAssertEqual(phoneticsSlides.first?.2, "强健的；可靠的；坚固的")
        
        // 验证上下文幻灯片
        let contextualSlides = viewModel.slideData.compactMap { slide in
            if case .contextual(let title, let content, let type) = slide {
                return (title, content, type)
            }
            return nil
        }
        
        XCTAssertEqual(contextualSlides.count, 4) // 母语者视角、情感色彩、想象画面、词源本质
        
        let contextualTitles = contextualSlides.map { $0.0 }
        XCTAssertTrue(contextualTitles.contains("母语者视角"))
        XCTAssertTrue(contextualTitles.contains("情感色彩"))
        XCTAssertTrue(contextualTitles.contains("想象画面"))
        XCTAssertTrue(contextualTitles.contains("词源本质"))
        
        // 验证例句幻灯片
        let exampleSlides = viewModel.slideData.compactMap { slide in
            if case .example(let example) = slide {
                return example
            }
            return nil
        }
        
        XCTAssertEqual(exampleSlides.count, 1)
        XCTAssertEqual(exampleSlides.first?.english, "The robust system can handle millions of users.")
    }
    
    @MainActor
    func testSlideModeToggle() {
        // Given
        let viewModel = WordCardViewModel()
        viewModel.currentWord = mockWord

        // When - Enter slide mode
        viewModel.enterSlideMode()

        // Then
        XCTAssertTrue(viewModel.isInSlideMode)
        XCTAssertEqual(viewModel.currentSlideIndex, 0)
        XCTAssertFalse(viewModel.slideData.isEmpty)

        // When - Exit slide mode
        viewModel.exitSlideMode()

        // Then
        XCTAssertFalse(viewModel.isInSlideMode)
        XCTAssertEqual(viewModel.currentSlideIndex, 0)
        XCTAssertTrue(viewModel.slideData.isEmpty)
    }
    
    @MainActor
    func testSlideTransition() {
        // Given
        let viewModel = WordCardViewModel()
        viewModel.currentWord = mockWord
        viewModel.enterSlideMode()

        let initialIndex = viewModel.currentSlideIndex
        let totalSlides = viewModel.slideData.count

        // When - Valid transition
        let targetIndex = min(initialIndex + 1, totalSlides - 1)
        viewModel.handleSlideTransition(to: targetIndex)

        // Then
        XCTAssertEqual(viewModel.currentSlideIndex, targetIndex)

        // When - Invalid transition (negative index)
        viewModel.handleSlideTransition(to: -1)

        // Then - Should remain unchanged
        XCTAssertEqual(viewModel.currentSlideIndex, targetIndex)

        // When - Invalid transition (beyond bounds)
        viewModel.handleSlideTransition(to: totalSlides + 10)

        // Then - Should remain unchanged
        XCTAssertEqual(viewModel.currentSlideIndex, targetIndex)
    }
    
    // MARK: - Helper Methods
    
    private func createWordCardView() -> WordCardView {
        return WordCardView(
            word: mockWord,
            onPlayAudio: { [weak self] _ in
                self?.audioPlaybackCalled = true
            },
            onSwipeAction: { [weak self] action in
                self?.swipeActionCalled = action
            }
        )
    }
}

// MARK: - Test Extensions

@available(iOS 15.0, *)
extension WordCardIntegrationTests {
    
    /// Test hero transition configuration
    func testHeroTransitionConfiguration() {
        // Given
        let defaultConfig = HeroTransition.Configuration.default
        let quickConfig = HeroTransition.Configuration.quick
        let smoothConfig = HeroTransition.Configuration.smooth
        
        // Then
        XCTAssertEqual(defaultConfig.duration, 0.6)
        XCTAssertEqual(quickConfig.duration, 0.4)
        XCTAssertEqual(smoothConfig.duration, 0.8)
        
        XCTAssertTrue(defaultConfig.enableBackgroundFade)
        XCTAssertEqual(defaultConfig.backgroundFadeDelay, 0.2)
    }
    
    /// Test contextual slide type enumeration
    func testContextualSlideTypes() {
        // Given
        let types: [ContextualType] = [
            .nativeSpeakerPerspective,
            .emotionalResonance,
            .visualImagery,
            .etymologicalEssence
        ]
        
        // Then
        XCTAssertEqual(types.count, 4)
        // 验证所有类型都是不同的
        let uniqueTypes = Set(types.map { "\($0)" })
        XCTAssertEqual(uniqueTypes.count, 4)
    }
}
