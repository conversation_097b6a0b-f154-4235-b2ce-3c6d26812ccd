import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
final class SynonymsViewTests: XCTestCase {
    
    // MARK: - Test Data
    
    private let sampleSynonyms = [
        Synonym(
            word: "strong",
            explanation: "strong 强调力量，而 robust 更强调在复杂环境下的稳定性和可靠性。",
            examples: [
                SynonymExample(
                    sentence: "The strong foundation supports the building.",
                    translation: "坚固的地基支撑着建筑物。"
                ),
                SynonymExample(
                    sentence: "She has a strong personality.",
                    translation: "她性格很强势。"
                )
            ]
        ),
        Synonym(
            word: "sturdy",
            explanation: "sturdy 侧重于结构的坚固和耐用，通常用于描述物理对象。",
            examples: [
                SynonymExample(
                    sentence: "This sturdy table will last for years.",
                    translation: "这张结实的桌子能用很多年。"
                )
            ]
        ),
        Synonym(
            word: "resilient",
            explanation: "resilient 强调从困难中恢复的能力，而 robust 更强调承受困难而不受影响的能力。",
            examples: [
                Synonym<PERSON>xample(
                    sentence: "Children are remarkably resilient.",
                    translation: "孩子们的适应能力非常强。"
                )
            ]
        )
    ]
    
    private let singleSynonym = [
        Synonym(
            word: "powerful",
            explanation: "powerful 强调力量和影响力，适用范围更广。",
            examples: []
        )
    ]
    
    // MARK: - Initialization Tests
    
    func testSynonymsView_Initialization_ShouldCreateWithSynonyms() {
        // Given & When
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.synonyms.count, 3)
        XCTAssertEqual(view.synonyms[0].word, "strong")
        XCTAssertEqual(view.synonyms[1].word, "sturdy")
        XCTAssertEqual(view.synonyms[2].word, "resilient")
    }
    
    func testSynonymsView_EmptySynonyms_ShouldCreateSuccessfully() {
        // Given & When
        let view = SynonymsView(synonyms: [])
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertTrue(view.synonyms.isEmpty)
    }
    
    // MARK: - Content Display Tests
    
    func testSynonymsView_SectionHeader_ShouldDisplayCorrectTitle() throws {
        // Given
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let titleText = try headerHStack.text(1)
        XCTAssertEqual(try titleText.string(), "同义词对比")
    }
    
    func testSynonymsView_SectionHeader_ShouldHaveSwapIcon() throws {
        // Given
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let iconImage = try headerHStack.image(0)
        XCTAssertEqual(try iconImage.actualImage().name(), "arrow.triangle.swap")
    }
    
    func testSynonymsView_WithSynonyms_ShouldShowSynonymCards() {
        // Given
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // When & Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.synonyms.count, 3)
        XCTAssertFalse(view.synonyms.isEmpty)
    }
    
    func testSynonymsView_EmptySynonyms_ShouldShowEmptyState() throws {
        // Given
        let view = SynonymsView(synonyms: [])
        
        // When
        let vStack = try view.inspect().vStack()
        let emptyStateVStack = try vStack.vStack(1)
        
        // Then
        let emptyText = try emptyStateVStack.text(1)
        XCTAssertEqual(try emptyText.string(), "暂无同义词")
    }
    
    // MARK: - Data Validation Tests
    
    func testSynonymsView_SynonymContent_ShouldMatchInputData() {
        // Given
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // When
        let firstSynonym = view.synonyms[0]
        
        // Then
        XCTAssertEqual(firstSynonym.word, "strong")
        XCTAssertEqual(firstSynonym.explanation, "strong 强调力量，而 robust 更强调在复杂环境下的稳定性和可靠性。")
        XCTAssertEqual(firstSynonym.examples.count, 2)
        XCTAssertEqual(firstSynonym.examples[0].sentence, "The strong foundation supports the building.")
        XCTAssertEqual(firstSynonym.examples[0].translation, "坚固的地基支撑着建筑物。")
    }
    
    func testSynonymsView_SynonymWithoutExamples_ShouldHandleGracefully() {
        // Given
        let synonymWithoutExamples = [
            Synonym(
                word: "test",
                explanation: "测试同义词",
                examples: []
            )
        ]
        
        // When
        let view = SynonymsView(synonyms: synonymWithoutExamples)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertTrue(view.synonyms[0].examples.isEmpty)
    }
    
    // MARK: - State Management Tests

    func testSynonymsView_InitialState_ShouldBeValid() {
        // Given & When
        let view = SynonymsView(synonyms: sampleSynonyms)

        // Then
        XCTAssertNotNil(view)
        XCTAssertFalse(view.synonyms.isEmpty)
    }
    
    // MARK: - Edge Cases Tests
    
    func testSynonymsView_LongExplanation_ShouldDisplayCorrectly() {
        // Given
        let longExplanationSynonym = [
            Synonym(
                word: "comprehensive",
                explanation: "这是一个非常长的解释文本，用来测试界面在处理长文本时的表现。它应该能够正确地换行和显示，不会影响整体的布局和用户体验。这个解释包含了多个句子，并且涵盖了同义词之间的细微差别和使用场景。",
                examples: [
                    SynonymExample(
                        sentence: "This is a comprehensive analysis of the situation.",
                        translation: "这是对情况的全面分析。"
                    )
                ]
            )
        ]
        
        // When
        let view = SynonymsView(synonyms: longExplanationSynonym)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertGreaterThan(view.synonyms[0].explanation.count, 50)
    }
    
    func testSynonymsView_MultipleSynonymsWithVaryingExamples_ShouldHandleCorrectly() {
        // Given
        let varyingExamplesSynonyms = [
            Synonym(word: "word1", explanation: "解释1", examples: []),
            Synonym(word: "word2", explanation: "解释2", examples: [
                SynonymExample(sentence: "例句1", translation: "翻译1")
            ]),
            Synonym(word: "word3", explanation: "解释3", examples: [
                SynonymExample(sentence: "例句2", translation: "翻译2"),
                SynonymExample(sentence: "例句3", translation: "翻译3"),
                SynonymExample(sentence: "例句4", translation: "翻译4")
            ])
        ]
        
        // When
        let view = SynonymsView(synonyms: varyingExamplesSynonyms)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.synonyms.count, 3)
        XCTAssertTrue(view.synonyms[0].examples.isEmpty)
        XCTAssertEqual(view.synonyms[1].examples.count, 1)
        XCTAssertEqual(view.synonyms[2].examples.count, 3)
    }
    
    // MARK: - Performance Tests
    
    func testSynonymsView_Performance_ShouldCreateQuickly() {
        // Given
        let synonyms = sampleSynonyms
        
        // When & Then
        measure {
            for _ in 0..<100 {
                let _ = SynonymsView(synonyms: synonyms)
            }
        }
    }
    
    func testSynonymsView_LargeDataSet_ShouldHandleEfficiently() {
        // Given
        let largeSynonyms = (0..<20).map { index in
            Synonym(
                word: "synonym\(index)",
                explanation: "这是同义词 \(index) 的详细解释，包含了与目标词汇的区别和使用场景。",
                examples: [
                    SynonymExample(
                        sentence: "Example sentence for synonym \(index).",
                        translation: "同义词 \(index) 的例句翻译。"
                    )
                ]
            )
        }
        
        // When & Then
        measure {
            let _ = SynonymsView(synonyms: largeSynonyms)
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testSynonymsView_Accessibility_ShouldHaveProperLabels() {
        // Given
        let view = SynonymsView(synonyms: sampleSynonyms)
        
        // When & Then
        XCTAssertNotNil(view)
        // Note: More detailed accessibility testing would require ViewInspector extensions
        // or integration tests with actual UI testing frameworks
    }
    
    // MARK: - Integration Tests
    
    func testSynonymsView_WithRealWorldData_ShouldDisplayCorrectly() {
        // Given
        let realWorldSynonyms = [
            Synonym(
                word: "robust",
                explanation: "robust 在技术语境中更常用，强调系统的稳定性和可靠性。",
                examples: [
                    SynonymExample(
                        sentence: "The robust architecture ensures high availability.",
                        translation: "强大的架构确保了高可用性。"
                    )
                ]
            )
        ]
        
        // When
        let view = SynonymsView(synonyms: realWorldSynonyms)
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.synonyms[0].word, "robust")
        XCTAssertFalse(view.synonyms[0].explanation.isEmpty)
        XCTAssertFalse(view.synonyms[0].examples.isEmpty)
    }
}
