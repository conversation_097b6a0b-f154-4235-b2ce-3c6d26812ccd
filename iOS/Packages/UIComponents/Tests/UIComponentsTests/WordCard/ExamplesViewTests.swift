import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
final class ExamplesViewTests: XCTestCase {
    
    // MARK: - Test Data
    
    private let sampleExamples = [
        ExampleCategory(
            category: "描述技术方案的可靠性",
            examples: [
                ExampleSentence(
                    english: "Our new system features a robust security framework.",
                    chinese: "我们的新系统采用了强大的安全框架。",
                    audioUrl: URL(string: "https://example.com/audio/example1.mp3")!,
                    phraseBreakdown: [
                        PhraseBreakdown(
                            phrase: "robust security framework",
                            translation: "强大的安全框架",
                            audioUrl: URL(string: "https://example.com/audio/phrase1.mp3")!
                        ),
                        PhraseBreakdown(
                            phrase: "features",
                            translation: "具有，采用",
                            audioUrl: URL(string: "https://example.com/audio/phrase2.mp3")!
                        )
                    ]
                )
            ]
        ),
        ExampleCategory(
            category: "描述人的身体状况",
            examples: [
                ExampleSentence(
                    english: "She has a robust constitution.",
                    chinese: "她体格强健。",
                    audioUrl: URL(string: "https://example.com/audio/example2.mp3")!,
                    phraseBreakdown: [
                        PhraseBreakdown(
                            phrase: "robust constitution",
                            translation: "强健的体格",
                            audioUrl: URL(string: "https://example.com/audio/phrase3.mp3")!
                        )
                    ]
                )
            ]
        )
    ]
    
    private let singleExample = [
        ExampleCategory(
            category: "单一分类",
            examples: [
                ExampleSentence(
                    english: "This is a robust design.",
                    chinese: "这是一个坚固的设计。",
                    audioUrl: URL(string: "https://example.com/audio/example3.mp3")!,
                    phraseBreakdown: []
                )
            ]
        )
    ]
    
    // MARK: - Initialization Tests
    
    func testExamplesView_Initialization_ShouldCreateWithExamples() {
        // Given & When
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.examples.count, 2)
        XCTAssertEqual(view.examples[0].category, "描述技术方案的可靠性")
        XCTAssertEqual(view.examples[1].category, "描述人的身体状况")
    }
    
    func testExamplesView_EmptyExamples_ShouldCreateSuccessfully() {
        // Given & When
        let view = ExamplesView(examples: [], onPlayAudio: { _ in })
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertTrue(view.examples.isEmpty)
    }
    
    // MARK: - Content Display Tests
    
    func testExamplesView_SectionHeader_ShouldDisplayCorrectTitle() throws {
        // Given
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let titleText = try headerHStack.text(1)
        XCTAssertEqual(try titleText.string(), "例句解析")
    }
    
    func testExamplesView_SectionHeader_ShouldHaveQuoteBubbleIcon() throws {
        // Given
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })
        
        // When
        let vStack = try view.inspect().vStack()
        let headerHStack = try vStack.hStack(0)
        
        // Then
        let iconImage = try headerHStack.image(0)
        XCTAssertEqual(try iconImage.actualImage().name(), "quote.bubble.fill")
    }
    
    func testExamplesView_MultipleCategories_ShouldShowCategorySelector() {
        // Given
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })

        // When & Then
        // Should create view successfully with multiple categories
        XCTAssertNotNil(view)
        XCTAssertEqual(view.examples.count, 2)
        XCTAssertTrue(view.examples.count > 1)
    }
    
    func testExamplesView_SingleCategory_ShouldNotShowCategorySelector() throws {
        // Given
        let view = ExamplesView(examples: singleExample, onPlayAudio: { _ in })
        
        // When
        let vStack = try view.inspect().vStack()
        
        // Then
        // Should not have category selector for single category
        // The second element should be the examples content, not category selector
        XCTAssertThrowsError(try vStack.scrollView(1))
    }
    
    func testExamplesView_EmptyExamples_ShouldShowEmptyState() throws {
        // Given
        let view = ExamplesView(examples: [], onPlayAudio: { _ in })
        
        // When
        let vStack = try view.inspect().vStack()
        let emptyStateVStack = try vStack.vStack(1)
        
        // Then
        let emptyText = try emptyStateVStack.text(1)
        XCTAssertEqual(try emptyText.string(), "暂无例句")
    }
    
    // MARK: - Audio Playback Tests
    
    func testExamplesView_AudioPlayback_ShouldCallOnPlayAudio() {
        // Given
        var audioPlayedURL: URL?
        let view = ExamplesView(examples: sampleExamples) { url in
            audioPlayedURL = url
        }
        
        // When
        let expectedURL = sampleExamples[0].examples[0].audioUrl
        view.onPlayAudio(expectedURL)
        
        // Then
        XCTAssertEqual(audioPlayedURL, expectedURL)
    }
    
    // MARK: - Category Selection Tests

    func testExamplesView_InitialState_ShouldBeValid() {
        // Given & When
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })

        // Then
        XCTAssertNotNil(view)
        XCTAssertFalse(view.examples.isEmpty)
    }
    
    // MARK: - Data Validation Tests
    
    func testExamplesView_ExampleContent_ShouldMatchInputData() {
        // Given
        let view = ExamplesView(examples: sampleExamples, onPlayAudio: { _ in })
        
        // When
        let firstCategory = view.examples[0]
        let firstExample = firstCategory.examples[0]
        
        // Then
        XCTAssertEqual(firstExample.english, "Our new system features a robust security framework.")
        XCTAssertEqual(firstExample.chinese, "我们的新系统采用了强大的安全框架。")
        XCTAssertEqual(firstExample.phraseBreakdown.count, 2)
        XCTAssertEqual(firstExample.phraseBreakdown[0].phrase, "robust security framework")
        XCTAssertEqual(firstExample.phraseBreakdown[0].translation, "强大的安全框架")
    }
    
    func testExamplesView_ExampleWithoutPhraseBreakdown_ShouldHandleGracefully() {
        // Given
        let exampleWithoutBreakdown = [
            ExampleCategory(
                category: "测试分类",
                examples: [
                    ExampleSentence(
                        english: "Simple example.",
                        chinese: "简单例句。",
                        audioUrl: URL(string: "https://example.com/audio/simple.mp3")!,
                        phraseBreakdown: []
                    )
                ]
            )
        ]
        
        // When
        let view = ExamplesView(examples: exampleWithoutBreakdown, onPlayAudio: { _ in })
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertTrue(view.examples[0].examples[0].phraseBreakdown.isEmpty)
    }
    
    // MARK: - Edge Cases Tests
    
    func testExamplesView_LongCategoryName_ShouldDisplayCorrectly() {
        // Given
        let longCategoryExample = [
            ExampleCategory(
                category: "这是一个非常长的分类名称，用来测试界面在处理长文本时的表现",
                examples: [
                    ExampleSentence(
                        english: "Test sentence.",
                        chinese: "测试句子。",
                        audioUrl: URL(string: "https://example.com/audio/test.mp3")!,
                        phraseBreakdown: []
                    )
                ]
            )
        ]
        
        // When
        let view = ExamplesView(examples: longCategoryExample, onPlayAudio: { _ in })
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertEqual(view.examples[0].category, "这是一个非常长的分类名称，用来测试界面在处理长文本时的表现")
    }
    
    // MARK: - Performance Tests
    
    func testExamplesView_Performance_ShouldCreateQuickly() {
        // Given
        let examples = sampleExamples
        
        // When & Then
        measure {
            for _ in 0..<100 {
                let _ = ExamplesView(examples: examples, onPlayAudio: { _ in })
            }
        }
    }
    
    func testExamplesView_LargeDataSet_ShouldHandleEfficiently() {
        // Given
        let largeExamples = (0..<50).map { index in
            ExampleCategory(
                category: "分类 \(index)",
                examples: [
                    ExampleSentence(
                        english: "Example sentence \(index).",
                        chinese: "例句 \(index)。",
                        audioUrl: URL(string: "https://example.com/audio/example\(index).mp3")!,
                        phraseBreakdown: []
                    )
                ]
            )
        }
        
        // When & Then
        measure {
            let _ = ExamplesView(examples: largeExamples, onPlayAudio: { _ in })
        }
    }
}
