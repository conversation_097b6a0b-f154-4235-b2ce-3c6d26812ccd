import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
final class WordHeaderViewTests: XCTestCase {
    
    // MARK: - Test Data
    
    private let sampleWord = "robust"
    
    private let singlePronunciation = [
        Pronunciation(
            dialect: "NAmE",
            symbol: "/roʊˈbʌst/",
            audioUrl: URL(string: "https://example.com/robust_us.mp3")!
        )
    ]
    
    private let multiplePronunciations = [
        Pronunciation(
            dialect: "BrE",
            symbol: "/ˈʃedjuːl/",
            audioUrl: URL(string: "https://example.com/schedule_uk.mp3")!
        ),
        Pronunciation(
            dialect: "NAmE",
            symbol: "/ˈskedʒuːl/",
            audioUrl: URL(string: "https://example.com/schedule_us.mp3")!
        )
    ]
    
    // MARK: - Initialization Tests
    
    func testWordHeaderViewInitialization() {
        // Given
        var audioPlayedURL: URL?
        let onPlayAudio: (URL) -> Void = { url in
            audioPlayedURL = url
        }
        
        // When
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: onPlayAudio
        )
        
        // Then
        XCTAssertEqual(view.word, sampleWord)
        XCTAssertEqual(view.pronunciations.count, 1)
        XCTAssertEqual(view.pronunciations.first?.dialect, "NAmE")
        XCTAssertNil(audioPlayedURL)
    }
    
    // MARK: - UI Component Tests
    
    func testWordTitleDisplay() throws {
        // Given
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: { _ in }
        )
        
        // When & Then
        let vStack = try view.inspect().vStack()
        let wordTitle = try vStack.hStack(0).text(0)
        
        XCTAssertEqual(try wordTitle.string(), sampleWord)
        XCTAssertEqual(try wordTitle.attributes().font(), .largeTitle)
        XCTAssertEqual(try wordTitle.attributes().fontWeight(), .bold)
    }
    
    func testSinglePronunciationDisplay() throws {
        // Given
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: { _ in }
        )

        // When & Then - Basic structure test
        let vStack = try view.inspect().vStack()
        XCTAssertEqual(vStack.count, 2) // Word title + pronunciation section
    }
    
    func testMultiplePronunciationsDisplay() throws {
        // Given
        let view = WordHeaderView(
            word: "schedule",
            pronunciations: multiplePronunciations,
            onPlayAudio: { _ in }
        )

        // When & Then - Basic structure test
        let vStack = try view.inspect().vStack()
        XCTAssertEqual(vStack.count, 2) // Word title + pronunciation section
    }
    
    func testPlayButtonPresence() throws {
        // Given
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: { _ in }
        )

        // When & Then - Basic structure test
        let vStack = try view.inspect().vStack()
        XCTAssertEqual(vStack.count, 2) // Word title + pronunciation section
    }
    
    // MARK: - Interaction Tests
    
    func testAudioPlayback() {
        // Given
        var audioPlayedURL: URL?
        let expectation = XCTestExpectation(description: "Audio playback triggered")
        
        let onPlayAudio: (URL) -> Void = { url in
            audioPlayedURL = url
            expectation.fulfill()
        }
        
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: onPlayAudio
        )
        
        // When
        do {
            let vStack = try view.inspect().vStack()
            let pronunciationSection = try vStack.vStack(1)
            let pronunciationDisplay = try pronunciationSection.hStack(0)
            let playButton = try pronunciationDisplay.button(1)
            
            try playButton.tap()
            
            // Then
            wait(for: [expectation], timeout: 1.0)
            XCTAssertEqual(audioPlayedURL, singlePronunciation.first?.audioUrl)
        } catch {
            XCTFail("Failed to interact with play button: \(error)")
        }
    }
    
    func testPronunciationSelection() {
        // Given
        var audioPlayedURL: URL?
        let view = WordHeaderView(
            word: "schedule",
            pronunciations: multiplePronunciations,
            onPlayAudio: { url in audioPlayedURL = url }
        )
        
        // When & Then
        do {
            let vStack = try view.inspect().vStack()
            let pronunciationSection = try vStack.vStack(1)
            let pronunciationSelector = try pronunciationSection.hStack(0)
            let secondButton = try pronunciationSelector.button(1) // NAmE button
            
            try secondButton.tap()
            
            // Should update the selected pronunciation
            // Note: This test would need state inspection which might require additional setup
            XCTAssertNoThrow(try secondButton.tap())
        } catch {
            XCTFail("Failed to interact with pronunciation selector: \(error)")
        }
    }
    
    // MARK: - Accessibility Tests
    
    func testAccessibilityLabels() throws {
        // Given
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: singlePronunciation,
            onPlayAudio: { _ in }
        )
        
        // When & Then
        let vStack = try view.inspect().vStack()
        let pronunciationSection = try vStack.vStack(1)
        let pronunciationDisplay = try pronunciationSection.hStack(0)
        let playButton = try pronunciationDisplay.button(1)
        
        XCTAssertEqual(try playButton.accessibilityLabel().string(), "播放发音")
        XCTAssertEqual(try playButton.accessibilityHint().string(), "点击播放 NAmE 发音")
    }
    
    // MARK: - Edge Cases
    
    func testEmptyPronunciations() {
        // Given
        let view = WordHeaderView(
            word: sampleWord,
            pronunciations: [],
            onPlayAudio: { _ in }
        )
        
        // When & Then
        do {
            let vStack = try view.inspect().vStack()
            
            // Should only show word title, no pronunciation section
            XCTAssertEqual(vStack.count, 1)
            
            let wordTitle = try vStack.hStack(0).text(0)
            XCTAssertEqual(try wordTitle.string(), sampleWord)
        } catch {
            XCTFail("Failed to handle empty pronunciations: \(error)")
        }
    }
    
    func testLongWordDisplay() throws {
        // Given
        let longWord = "incomprehensible"
        let view = WordHeaderView(
            word: longWord,
            pronunciations: singlePronunciation,
            onPlayAudio: { _ in }
        )
        
        // When & Then
        let vStack = try view.inspect().vStack()
        let wordTitle = try vStack.hStack(0).text(0)
        
        XCTAssertEqual(try wordTitle.string(), longWord)
        XCTAssertEqual(try wordTitle.attributes().font(), .largeTitle)
    }
}
