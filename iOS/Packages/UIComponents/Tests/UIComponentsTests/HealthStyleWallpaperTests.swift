import XCTest
import SwiftUI
import ViewInspector
@testable import UIComponents

@available(iOS 15.0, macOS 10.15, *)
final class HealthStyleWallpaperTests: XCTestCase {
    
    // MARK: - Basic Initialization Tests
    
    func testHealthStyleWallpaperInitialization() throws {
        // 测试默认初始化
        let wallpaper = HealthStyleWallpaperView()
        XCTAssertNotNil(wallpaper)
    }
    
    func testHealthStyleWallpaperCustomInitialization() throws {
        // 测试自定义参数初始化
        let wallpaper = HealthStyleWallpaperView(
            enableAnimation: true,
            blurRadius: 80,
            opacity: 0.8
        )
        XCTAssertNotNil(wallpaper)
    }
    
    // MARK: - Factory Methods Tests
    
    func testStaticWallpaperFactory() throws {
        let staticWallpaper = HealthStyleWallpaperView.staticWallpaper()
        XCTAssertNotNil(staticWallpaper)
    }
    
    func testAnimatedWallpaperFactory() throws {
        let animatedWallpaper = HealthStyleWallpaperView.animatedWallpaper()
        XCTAssertNotNil(animatedWallpaper)
    }
    
    func testSubtleWallpaperFactory() throws {
        let subtleWallpaper = HealthStyleWallpaperView.subtleWallpaper()
        XCTAssertNotNil(subtleWallpaper)
    }
    
    func testVibrantWallpaperFactory() throws {
        let vibrantWallpaper = HealthStyleWallpaperView.vibrantWallpaper()
        XCTAssertNotNil(vibrantWallpaper)
    }
    
    // MARK: - ViewModifier Tests
    
    func testHealthStyleWallpaperModifier() throws {
        let testView = Text("Test")
            .healthStyleWallpaper()
        
        XCTAssertNotNil(testView)
    }
    
    func testHealthStyleWallpaperModifierWithCustomStyle() throws {
        let customWallpaper = HealthStyleWallpaperView.animatedWallpaper()
        let testView = Text("Test")
            .healthStyleWallpaper(customWallpaper)
        
        XCTAssertNotNil(testView)
    }
    
    // MARK: - Color System Integration Tests
    
    func testGradientColorsAvailability() throws {
        // 测试渐变色数组是否正确配置
        XCTAssertEqual(SenseWordColors.gradientColors.count, 5)
        XCTAssertEqual(SenseWordColors.gradientCenters.count, 5)
        XCTAssertEqual(SenseWordColors.gradientRadii.count, 5)
    }
    
    func testHealthColorsAvailability() throws {
        // 测试健康风格颜色是否可用
        XCTAssertNotNil(SenseWordColors.healthColor1)
        XCTAssertNotNil(SenseWordColors.healthColor2)
        XCTAssertNotNil(SenseWordColors.healthColor3)
        XCTAssertNotNil(SenseWordColors.healthColor4)
        XCTAssertNotNil(SenseWordColors.healthColor5)
    }
    
    // MARK: - View Hierarchy Tests
    
    func testWallpaperViewHierarchy() throws {
        let wallpaper = HealthStyleWallpaperView.staticWallpaper()
        
        // 使用ViewInspector检查视图层次结构
        let zstack = try wallpaper.inspect().zStack()
        XCTAssertNotNil(zstack)
        
        // 验证ZStack包含背景色和渐变层
        XCTAssertGreaterThanOrEqual(try zstack.count(), 2)
    }
    
    // MARK: - Performance Tests
    
    func testWallpaperRenderingPerformance() throws {
        // 测试壁纸渲染性能
        measure {
            let wallpaper = HealthStyleWallpaperView.staticWallpaper()
            _ = wallpaper.body
        }
    }
    
    func testAnimatedWallpaperPerformance() throws {
        // 测试动画壁纸性能
        measure {
            let wallpaper = HealthStyleWallpaperView.animatedWallpaper()
            _ = wallpaper.body
        }
    }
}

// MARK: - Mock Views for Testing

@available(iOS 15.0, macOS 10.15, *)
struct MockContentView: View {
    var body: some View {
        VStack {
            Text("Mock Content")
                .font(.title)
            Text("Testing wallpaper background")
                .font(.body)
        }
        .padding()
    }
}

@available(iOS 15.0, macOS 10.15, *)
struct MockWallpaperTestView: View {
    var body: some View {
        MockContentView()
            .healthStyleWallpaper(.staticWallpaper())
    }
}
