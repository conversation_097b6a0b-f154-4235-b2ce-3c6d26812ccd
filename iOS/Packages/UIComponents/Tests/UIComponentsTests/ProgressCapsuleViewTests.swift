import XCTest
import Swift<PERSON>
@testable import UIComponents

@available(iOS 15.0, macOS 12.0, *)
@MainActor
final class ProgressCapsuleViewTests: XCTestCase {
    
    // MARK: - Test Properties
    
    private var masteredCount: Int = 0
    private var goalCount: Int = 200
    
    override func setUp() async throws {
        try await super.setUp()
        masteredCount = 0
        goalCount = 200
    }
    
    // MARK: - Initialization Tests
    
    func testProgressCapsuleView_Initialization_ShouldSetDefaultValues() {
        // Given
        let binding = Binding<Int>(
            get: { self.masteredCount },
            set: { self.masteredCount = $0 }
        )
        
        // When
        let view = ProgressCapsuleView(
            masteredCount: binding,
            goalCount: goalCount
        )
        
        // Then
        XCTAssertEqual(view.goalCount, goalCount)
        XCTAssertEqual(view.title, "已掌握")
        XCTAssertEqual(view.accentColor, .green)
    }
    
    func testProgressCapsuleView_CustomInitialization_ShouldSetCustomValues() {
        // Given
        let binding = Binding<Int>(
            get: { self.masteredCount },
            set: { self.masteredCount = $0 }
        )
        let customTitle = "本周目标"
        let customColor = Color.blue
        
        // When
        let view = ProgressCapsuleView(
            masteredCount: binding,
            goalCount: goalCount,
            title: customTitle,
            accentColor: customColor
        )
        
        // Then
        XCTAssertEqual(view.goalCount, goalCount)
        XCTAssertEqual(view.title, customTitle)
        XCTAssertEqual(view.accentColor, customColor)
    }
    
    // MARK: - Progress Calculation Tests
    
    func testProgressPercentage_WithZeroMastered_ShouldReturnZero() {
        // Given
        masteredCount = 0
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 0.0, accuracy: 0.001)
    }
    
    func testProgressPercentage_WithHalfMastered_ShouldReturnFiftyPercent() {
        // Given
        masteredCount = 100
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 0.5, accuracy: 0.001)
    }
    
    func testProgressPercentage_WithGoalReached_ShouldReturnOneHundredPercent() {
        // Given
        masteredCount = 200
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 1.0, accuracy: 0.001)
    }
    
    func testProgressPercentage_WithExceededGoal_ShouldReturnOneHundredPercent() {
        // Given
        masteredCount = 250
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 1.0, accuracy: 0.001)
    }
    
    func testProgressPercentage_WithZeroGoal_ShouldReturnZero() {
        // Given
        masteredCount = 50
        goalCount = 0
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 0.0, accuracy: 0.001)
    }
    
    // MARK: - Goal Status Tests
    
    func testIsGoalReached_WithMasteredLessThanGoal_ShouldReturnFalse() {
        // Given
        masteredCount = 150
        goalCount = 200
        
        // When
        let isReached = isGoalReached(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertFalse(isReached)
    }
    
    func testIsGoalReached_WithMasteredEqualToGoal_ShouldReturnTrue() {
        // Given
        masteredCount = 200
        goalCount = 200
        
        // When
        let isReached = isGoalReached(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertTrue(isReached)
    }
    
    func testIsGoalReached_WithMasteredGreaterThanGoal_ShouldReturnTrue() {
        // Given
        masteredCount = 250
        goalCount = 200
        
        // When
        let isReached = isGoalReached(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertTrue(isReached)
    }
    
    // MARK: - Text Formatting Tests
    
    func testProgressText_ShouldFormatCorrectly() {
        // Given
        masteredCount = 75
        goalCount = 200
        
        // When
        let text = formatProgressText(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(text, "75 / 200")
    }
    
    func testPercentageText_ShouldFormatCorrectly() {
        // Given
        masteredCount = 50
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        let text = formatPercentageText(percentage: percentage)
        
        // Then
        XCTAssertEqual(text, "25%")
    }
    
    func testPercentageText_WithDecimal_ShouldRoundDown() {
        // Given
        masteredCount = 33
        goalCount = 100
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        let text = formatPercentageText(percentage: percentage)
        
        // Then
        XCTAssertEqual(text, "33%")
    }
    
    // MARK: - Milestone Detection Tests
    
    func testIsMilestone_WithMultipleOfTen_ShouldReturnTrue() {
        // Given
        let count = 50
        
        // When
        let isMilestone = isMilestoneCount(count)
        
        // Then
        XCTAssertTrue(isMilestone)
    }
    
    func testIsMilestone_WithNonMultipleOfTen_ShouldReturnFalse() {
        // Given
        let count = 47
        
        // When
        let isMilestone = isMilestoneCount(count)
        
        // Then
        XCTAssertFalse(isMilestone)
    }
    
    func testIsMilestone_WithZero_ShouldReturnTrue() {
        // Given
        let count = 0
        
        // When
        let isMilestone = isMilestoneCount(count)
        
        // Then
        XCTAssertTrue(isMilestone)
    }
    
    // MARK: - Edge Cases Tests
    
    func testProgressCalculation_WithNegativeMastered_ShouldReturnZero() {
        // Given
        masteredCount = -10
        goalCount = 200
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 0.0, accuracy: 0.001)
    }
    
    func testProgressCalculation_WithNegativeGoal_ShouldReturnZero() {
        // Given
        masteredCount = 50
        goalCount = -100
        
        // When
        let percentage = calculateProgressPercentage(masteredCount: masteredCount, goalCount: goalCount)
        
        // Then
        XCTAssertEqual(percentage, 0.0, accuracy: 0.001)
    }
    
    // MARK: - Helper Methods
    
    private func calculateProgressPercentage(masteredCount: Int, goalCount: Int) -> Double {
        guard goalCount > 0 else { return 0 }
        return min(max(Double(masteredCount) / Double(goalCount), 0.0), 1.0)
    }
    
    private func isGoalReached(masteredCount: Int, goalCount: Int) -> Bool {
        return masteredCount >= goalCount
    }
    
    private func formatProgressText(masteredCount: Int, goalCount: Int) -> String {
        return "\(masteredCount) / \(goalCount)"
    }
    
    private func formatPercentageText(percentage: Double) -> String {
        let percentageInt = Int(percentage * 100)
        return "\(percentageInt)%"
    }
    
    private func isMilestoneCount(_ count: Int) -> Bool {
        return count % 10 == 0
    }
}
