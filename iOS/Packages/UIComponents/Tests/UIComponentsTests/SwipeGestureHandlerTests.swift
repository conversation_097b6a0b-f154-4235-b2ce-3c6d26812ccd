import XCTest
import Swift<PERSON>
@testable import UIComponents

@available(iOS 15.0, macOS 12.0, *)
final class SwipeGestureHandlerTests: XCTestCase {
    
    // MARK: - SwipeAction Tests
    
    func testSwipeActionDescription() {
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.nextWord.description, "下一个单词")
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.markMastered.description, "已掌握")
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.addToReview.description, "待复习")
    }
    
    func testSwipeActionIconName() {
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.nextWord.iconName, "arrow.up")
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.markMastered.iconName, "checkmark.circle.fill")
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.addToReview.iconName, "bookmark.fill")
    }
    
    func testSwipeActionColor() {
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.nextWord.color, .blue)
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.markMastered.color, .green)
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.addToReview.color, .orange)
    }
    
    func testSwipeActionEquality() {
        XCTAssertEqual(SwipeGestureHandler.SwipeAction.nextWord, SwipeGestureHandler.SwipeAction.nextWord)
        XCTAssertNotEqual(SwipeGestureHandler.SwipeAction.nextWord, SwipeGestureHandler.SwipeAction.markMastered)
    }
    
    func testSwipeActionAllCases() {
        let allCases = SwipeGestureHandler.SwipeAction.allCases
        XCTAssertEqual(allCases.count, 3)
        XCTAssertTrue(allCases.contains(.nextWord))
        XCTAssertTrue(allCases.contains(.markMastered))
        XCTAssertTrue(allCases.contains(.addToReview))
    }
    
    // MARK: - GestureThresholds Tests
    
    func testGestureThresholdsDefault() {
        let thresholds = SwipeGestureHandler.GestureThresholds.default
        XCTAssertEqual(thresholds.minVerticalDistance, -80)
        XCTAssertEqual(thresholds.minHorizontalDistance, 60)
        XCTAssertEqual(thresholds.maxHorizontalForVertical, 30)
    }
    
    func testGestureThresholdsCustom() {
        let thresholds = SwipeGestureHandler.GestureThresholds(
            minVerticalDistance: -100,
            minHorizontalDistance: 80,
            maxHorizontalForVertical: 40
        )
        XCTAssertEqual(thresholds.minVerticalDistance, -100)
        XCTAssertEqual(thresholds.minHorizontalDistance, 80)
        XCTAssertEqual(thresholds.maxHorizontalForVertical, 40)
    }
    
    // MARK: - detectSwipeAction Tests
    
    func testDetectSwipeAction_UpwardSwipe_ShouldReturnNextWord() {
        // Given
        let upwardTranslation = CGSize(width: 0, height: -100)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: upwardTranslation)
        
        // Then
        XCTAssertEqual(action, .nextWord)
    }
    
    func testDetectSwipeAction_UpwardRightSwipe_ShouldReturnMarkMastered() {
        // Given
        let upwardRightTranslation = CGSize(width: 80, height: -100)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: upwardRightTranslation)
        
        // Then
        XCTAssertEqual(action, .markMastered)
    }
    
    func testDetectSwipeAction_UpwardLeftSwipe_ShouldReturnAddToReview() {
        // Given
        let upwardLeftTranslation = CGSize(width: -80, height: -100)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: upwardLeftTranslation)
        
        // Then
        XCTAssertEqual(action, .addToReview)
    }
    
    func testDetectSwipeAction_InsufficientVerticalDistance_ShouldReturnNil() {
        // Given
        let shortTranslation = CGSize(width: 10, height: -20)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: shortTranslation)
        
        // Then
        XCTAssertNil(action)
    }
    
    func testDetectSwipeAction_DownwardSwipe_ShouldReturnNil() {
        // Given
        let downwardTranslation = CGSize(width: 0, height: 100)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: downwardTranslation)
        
        // Then
        XCTAssertNil(action)
    }
    
    func testDetectSwipeAction_InsufficientHorizontalDistance_ShouldReturnNil() {
        // Given
        let weakDiagonalTranslation = CGSize(width: 50, height: -100)

        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: weakDiagonalTranslation)

        // Then
        XCTAssertNil(action)
    }
    
    func testDetectSwipeAction_PureVerticalWithinThreshold_ShouldReturnNextWord() {
        // Given
        let nearVerticalTranslation = CGSize(width: 25, height: -100)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(translation: nearVerticalTranslation)
        
        // Then
        XCTAssertEqual(action, .nextWord)
    }
    
    func testDetectSwipeAction_CustomThresholds() {
        // Given
        let customThresholds = SwipeGestureHandler.GestureThresholds(
            minVerticalDistance: -50,
            minHorizontalDistance: 40,
            maxHorizontalForVertical: 20
        )
        let translation = CGSize(width: 45, height: -60)
        
        // When
        let action = SwipeGestureHandler.detectSwipeAction(
            translation: translation,
            thresholds: customThresholds
        )
        
        // Then
        XCTAssertEqual(action, .markMastered)
    }
    
    // MARK: - Edge Cases
    
    func testDetectSwipeAction_ExactThresholdValues() {
        let thresholds = SwipeGestureHandler.GestureThresholds.default
        
        // Exact minimum vertical distance
        let exactVertical = CGSize(width: 0, height: thresholds.minVerticalDistance)
        XCTAssertEqual(SwipeGestureHandler.detectSwipeAction(translation: exactVertical), .nextWord)
        
        // Exact maximum horizontal for vertical
        let exactHorizontalMax = CGSize(width: thresholds.maxHorizontalForVertical, height: -100)
        XCTAssertEqual(SwipeGestureHandler.detectSwipeAction(translation: exactHorizontalMax), .nextWord)
        
        // Exact minimum horizontal distance
        let exactHorizontalMin = CGSize(width: thresholds.minHorizontalDistance, height: -100)
        XCTAssertEqual(SwipeGestureHandler.detectSwipeAction(translation: exactHorizontalMin), .markMastered)
    }
    
    func testDetectSwipeAction_LargeValues() {
        // Very large upward swipe
        let largeUpward = CGSize(width: 0, height: -1000)
        XCTAssertEqual(SwipeGestureHandler.detectSwipeAction(translation: largeUpward), .nextWord)
        
        // Very large diagonal swipe
        let largeDiagonal = CGSize(width: 500, height: -1000)
        XCTAssertEqual(SwipeGestureHandler.detectSwipeAction(translation: largeDiagonal), .markMastered)
    }
    
    // MARK: - Gesture Creation Tests
    
    func testCreateSwipeGesture_ShouldReturnGesture() {
        // Given
        var actionReceived: SwipeGestureHandler.SwipeAction?
        
        // When
        let gesture = SwipeGestureHandler.createSwipeGesture { action in
            actionReceived = action
        }
        
        // Then
        XCTAssertNotNil(gesture)
        // Note: Testing actual gesture behavior would require UI testing framework
    }
    
    func testCreateSwipeGestureWithFeedback_ShouldReturnGesture() {
        // Given
        var actionReceived: SwipeGestureHandler.SwipeAction?
        var dragFeedback: (CGSize, SwipeGestureHandler.SwipeAction?)?
        
        // When
        let gesture = SwipeGestureHandler.createSwipeGestureWithFeedback(
            onAction: { action in
                actionReceived = action
            },
            onDragChanged: { translation, potentialAction in
                dragFeedback = (translation, potentialAction)
            }
        )
        
        // Then
        XCTAssertNotNil(gesture)
        // Note: Testing actual gesture behavior would require UI testing framework
    }
}

// MARK: - SwipeGestureFeedbackView Tests

@available(iOS 15.0, macOS 12.0, *)
final class SwipeGestureFeedbackViewTests: XCTestCase {
    
    func testSwipeGestureFeedbackView_Initialization() {
        // Given
        let translation = CGSize(width: 50, height: -100)
        let action = SwipeGestureHandler.SwipeAction.markMastered
        
        // When
        let feedbackView = SwipeGestureFeedbackView(
            translation: translation,
            potentialAction: action,
            isVisible: true
        )
        
        // Then
        XCTAssertEqual(feedbackView.translation, translation)
        XCTAssertEqual(feedbackView.potentialAction, action)
        XCTAssertTrue(feedbackView.isVisible)
    }
    
    func testSwipeGestureFeedbackView_DefaultVisibility() {
        // Given
        let translation = CGSize(width: 0, height: -100)
        let action = SwipeGestureHandler.SwipeAction.nextWord
        
        // When
        let feedbackView = SwipeGestureFeedbackView(
            translation: translation,
            potentialAction: action
        )
        
        // Then
        XCTAssertTrue(feedbackView.isVisible)
    }
}
