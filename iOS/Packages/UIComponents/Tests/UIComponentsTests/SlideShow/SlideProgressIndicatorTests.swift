import XCTest
import Swift<PERSON>
@testable import UIComponents

@available(iOS 15.0, macOS 10.15, *)
final class SlideProgressIndicatorTests: XCTestCase {
    
    // MARK: - Initialization Tests
    
    func testProgressIndicatorInitialization() {
        // Given
        let totalSlides = 5
        let currentSlide = 2
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, totalSlides)
        XCTAssertEqual(indicator.currentSlide, currentSlide)
        XCTAssertNotNil(indicator.configuration)
    }
    
    func testProgressIndicatorWithDefaultConfiguration() {
        // Given
        let totalSlides = 3
        let currentSlide = 1
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide,
            configuration: .default
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, totalSlides)
        XCTAssertEqual(indicator.currentSlide, currentSlide)
        XCTAssertEqual(indicator.configuration.width, 2.5)
        XCTAssertEqual(indicator.configuration.height, 120)
    }
    
    func testProgressIndicatorWithCompactConfiguration() {
        // Given
        let totalSlides = 4
        let currentSlide = 0
        
        // When
        let indicator = SlideProgressIndicator.compact(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, totalSlides)
        XCTAssertEqual(indicator.currentSlide, currentSlide)
        XCTAssertEqual(indicator.configuration.width, 2)
        XCTAssertEqual(indicator.configuration.height, 80)
    }
    
    // MARK: - Configuration Tests
    
    func testDefaultConfiguration() {
        // Given & When
        let config = SlideProgressIndicator.Configuration.default
        
        // Then
        XCTAssertEqual(config.width, 2.5)
        XCTAssertEqual(config.height, 120)
        XCTAssertEqual(config.segmentSpacing, 4)
        XCTAssertEqual(config.cornerRadius, 1.25)
        XCTAssertEqual(config.animationDuration, 0.3)
    }
    
    func testCompactConfiguration() {
        // Given & When
        let config = SlideProgressIndicator.Configuration.compact
        
        // Then
        XCTAssertEqual(config.width, 2)
        XCTAssertEqual(config.height, 80)
        XCTAssertEqual(config.segmentSpacing, 3)
        XCTAssertEqual(config.cornerRadius, 1)
        XCTAssertEqual(config.animationDuration, 0.25)
    }
    
    func testCustomConfiguration() {
        // Given
        let customConfig = SlideProgressIndicator.Configuration(
            width: 3.0,
            height: 100,
            segmentSpacing: 5,
            activeColor: .blue,
            inactiveColor: .gray,
            cornerRadius: 1.5,
            animationDuration: 0.4
        )
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: 5,
            currentSlide: 2,
            configuration: customConfig
        )
        
        // Then
        XCTAssertEqual(indicator.configuration.width, 3.0)
        XCTAssertEqual(indicator.configuration.height, 100)
        XCTAssertEqual(indicator.configuration.segmentSpacing, 5)
        XCTAssertEqual(indicator.configuration.cornerRadius, 1.5)
        XCTAssertEqual(indicator.configuration.animationDuration, 0.4)
    }
    
    // MARK: - Factory Methods Tests
    
    func testWithColorsFactoryMethod() {
        // Given
        let totalSlides = 6
        let currentSlide = 3
        let activeColor = Color.red
        let inactiveColor = Color.gray
        
        // When
        let indicator = SlideProgressIndicator.withColors(
            totalSlides: totalSlides,
            currentSlide: currentSlide,
            activeColor: activeColor,
            inactiveColor: inactiveColor
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, totalSlides)
        XCTAssertEqual(indicator.currentSlide, currentSlide)
        XCTAssertEqual(indicator.configuration.activeColor, activeColor)
        XCTAssertEqual(indicator.configuration.inactiveColor, inactiveColor)
    }
    
    // MARK: - Edge Cases Tests
    
    func testProgressIndicatorWithZeroSlides() {
        // Given
        let totalSlides = 0
        let currentSlide = 0
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, 0)
        XCTAssertEqual(indicator.currentSlide, 0)
    }
    
    func testProgressIndicatorWithSingleSlide() {
        // Given
        let totalSlides = 1
        let currentSlide = 0
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, 1)
        XCTAssertEqual(indicator.currentSlide, 0)
    }
    
    func testProgressIndicatorWithManySlides() {
        // Given
        let totalSlides = 20
        let currentSlide = 10
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, 20)
        XCTAssertEqual(indicator.currentSlide, 10)
    }
    
    func testProgressIndicatorWithInvalidCurrentSlide() {
        // Given
        let totalSlides = 5
        let currentSlide = 10 // Invalid: greater than totalSlides
        
        // When
        let indicator = SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
        
        // Then
        XCTAssertEqual(indicator.totalSlides, 5)
        XCTAssertEqual(indicator.currentSlide, 10) // Should accept any value, validation is UI responsibility
    }
    
    // MARK: - Performance Tests
    
    func testProgressIndicatorPerformance() {
        // Given
        let totalSlides = 100
        let currentSlide = 50
        
        // When & Then
        measure {
            let indicator = SlideProgressIndicator(
                totalSlides: totalSlides,
                currentSlide: currentSlide
            )
            XCTAssertNotNil(indicator)
        }
    }
    
    func testConfigurationCreationPerformance() {
        // When & Then
        measure {
            let config = SlideProgressIndicator.Configuration(
                width: 2.5,
                height: 120,
                segmentSpacing: 4,
                activeColor: .blue,
                inactiveColor: .gray,
                cornerRadius: 1.25,
                animationDuration: 0.3
            )
            XCTAssertNotNil(config)
        }
    }
}

// MARK: - Test Utilities

@available(iOS 15.0, macOS 10.15, *)
extension SlideProgressIndicatorTests {
    
    /// Helper method to create a mock progress indicator
    func createMockProgressIndicator(
        totalSlides: Int = 5,
        currentSlide: Int = 2
    ) -> SlideProgressIndicator {
        return SlideProgressIndicator(
            totalSlides: totalSlides,
            currentSlide: currentSlide
        )
    }
    
    /// Helper method to verify progress indicator properties
    func verifyProgressIndicator(
        _ indicator: SlideProgressIndicator,
        expectedTotal: Int,
        expectedCurrent: Int,
        file: StaticString = #file,
        line: UInt = #line
    ) {
        XCTAssertEqual(
            indicator.totalSlides,
            expectedTotal,
            "Total slides should match expected value",
            file: file,
            line: line
        )
        XCTAssertEqual(
            indicator.currentSlide,
            expectedCurrent,
            "Current slide should match expected value",
            file: file,
            line: line
        )
    }
}
