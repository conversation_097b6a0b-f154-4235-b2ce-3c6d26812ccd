import XCTest
import SwiftUI
@testable import UIComponents

/// 简化的间歇性动画壁纸测试
/// 只测试核心功能，避免复杂的UI测试
@available(iOS 15.0, macOS 11.0, *)
final class IntermittentAnimationWallpaperSimpleTests: XCTestCase {
    
    func testBasicInitialization() {
        // 测试基本初始化
        let wallpaper = IntermittentAnimationWallpaperView()
        XCTAssertNotNil(wallpaper, "间歇性动画壁纸应该能够正常初始化")
    }
    
    func testCustomInitialization() {
        // 测试自定义参数初始化
        let wallpaper = IntermittentAnimationWallpaperView(
            forceDarkMode: true,
            staticDuration: 120,
            animationDuration: 10
        )
        XCTAssertNotNil(wallpaper, "间歇性动画壁纸应该能够使用自定义参数初始化")
    }
    
    func testPerformanceOptimizedWallpaper() {
        // 测试性能优化壁纸
        let wallpaper = PerformanceOptimizedHealthStyleWallpaperView(
            performanceMode: .balanced
        )
        XCTAssertNotNil(wallpaper, "性能优化壁纸应该能够正常初始化")
    }
    
    func testWallpaperStyleEnum() {
        // 测试壁纸样式枚举
        let styles = WallpaperStyle.allCases
        XCTAssertGreaterThan(styles.count, 0, "应该有可用的壁纸样式")
        
        for style in styles {
            let lightWallpaper = style.lightWallpaper
            let darkWallpaper = style.darkWallpaper
            
            XCTAssertNotNil(lightWallpaper, "样式 \(style) 的亮色壁纸应该能够正常创建")
            XCTAssertNotNil(darkWallpaper, "样式 \(style) 的暗色壁纸应该能够正常创建")
        }
    }
    
    func testAdaptiveWallpaper() {
        // 测试自适应壁纸
        let adaptiveWallpaper = AdaptiveHealthStyleWallpaperView()
        XCTAssertNotNil(adaptiveWallpaper, "自适应壁纸应该能够正常初始化")
    }
}
