import XCTest
import Swift<PERSON>
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
final class WordCardViewTests: XCTestCase {
    
    // MARK: - Test Data
    
    private var mockWord: WordDTO!
    private var minimalWord: WordDTO!
    private var audioPlaybackCalls: [URL] = []
    private var swipeActionCalls: [SwipeGestureHandler.SwipeAction] = []
    
    override func setUp() {
        super.setUp()
        audioPlaybackCalls = []
        swipeActionCalls = []
        
        // Create mock word with complete data
        mockWord = WordDTO.mock(word: "robust")
        
        // Create minimal word for testing edge cases
        minimalWord = WordDTO(
            word: "test",
            difficulty: "A1",
            pronunciations: [
                Pronunciation(
                    dialect: "NAmE",
                    symbol: "/test/",
                    audioUrl: URL(string: "https://example.com/test.mp3")!
                )
            ],
            coreDefinition: "测试用词",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "测试意图",
                emotionalResonance: "测试共鸣",
                vividImagery: "测试意象",
                etymologicalEssence: "测试词源"
            ),
            examples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: []
        )
    }
    
    // MARK: - Initialization Tests
    
    func testWordCardView_Initialization_ShouldCreateView() {
        // Given & When
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { url in
                self.audioPlaybackCalls.append(url)
            },
            onSwipeAction: { action in
                self.swipeActionCalls.append(action)
            }
        )
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertTrue(audioPlaybackCalls.isEmpty)
        XCTAssertTrue(swipeActionCalls.isEmpty)
    }
    
    func testWordCardView_InitializationWithMinimalData_ShouldCreateView() {
        // Given & When
        let view = WordCardView(
            word: minimalWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // Then
        XCTAssertNotNil(view)
    }
    
    // MARK: - Content Display Tests
    
    func testWordCardView_WordDisplay_ShouldShowCorrectWord() {
        // Given
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        // Note: In a real UI test framework, we would verify the text content
        // For unit tests, we verify the data is correctly passed
        XCTAssertEqual(mockWord.word, "robust")
        XCTAssertEqual(mockWord.difficulty, "B1")
        XCTAssertEqual(mockWord.coreDefinition, "强健的；可靠的；坚固的")
    }
    
    func testWordCardView_PronunciationDisplay_ShouldShowPronunciations() {
        // Given
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        XCTAssertFalse(mockWord.pronunciations.isEmpty)
        XCTAssertEqual(mockWord.pronunciations.first?.dialect, "NAmE")
        XCTAssertEqual(mockWord.pronunciations.first?.symbol, "/roʊˈbʌst/")
    }
    
    func testWordCardView_ExamplesDisplay_ShouldShowExamplesWhenAvailable() {
        // Given
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        XCTAssertFalse(mockWord.examples.isEmpty)
        XCTAssertEqual(mockWord.examples.first?.category, "描述技术方案的可靠性")
    }
    
    func testWordCardView_SynonymsDisplay_ShouldShowSynonymsWhenAvailable() {
        // Given
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        XCTAssertFalse(mockWord.synonyms.isEmpty)
        XCTAssertEqual(mockWord.synonyms.first?.word, "strong")
    }
    
    func testWordCardView_EmptyContent_ShouldHandleGracefully() {
        // Given
        let view = WordCardView(
            word: minimalWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        XCTAssertTrue(minimalWord.examples.isEmpty)
        XCTAssertTrue(minimalWord.synonyms.isEmpty)
        XCTAssertNotNil(view) // Should still create view without crashing
    }
    
    // MARK: - Callback Tests
    
    func testWordCardView_AudioPlayback_ShouldTriggerCallback() {
        // Given
        var audioCallbackTriggered = false
        var capturedURL: URL?
        
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { url in
                audioCallbackTriggered = true
                capturedURL = url
            },
            onSwipeAction: { _ in }
        )
        
        // When
        let testURL = URL(string: "https://example.com/test.mp3")!
        view.onPlayAudio(testURL)
        
        // Then
        XCTAssertTrue(audioCallbackTriggered)
        XCTAssertEqual(capturedURL, testURL)
    }
    
    func testWordCardView_SwipeAction_ShouldTriggerCallback() {
        // Given
        var swipeCallbackTriggered = false
        var capturedAction: SwipeGestureHandler.SwipeAction?
        
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { action in
                swipeCallbackTriggered = true
                capturedAction = action
            }
        )
        
        // When
        let testAction = SwipeGestureHandler.SwipeAction.markMastered
        view.onSwipeAction(testAction)
        
        // Then
        XCTAssertTrue(swipeCallbackTriggered)
        XCTAssertEqual(capturedAction, testAction)
    }
    
    // MARK: - Difficulty Color Tests
    
    func testWordCardView_DifficultyColors_ShouldReturnCorrectColors() {
        // Given
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // When & Then
        // Note: We can't directly test private methods, but we can test the data
        // that would be used by those methods
        let difficulties = ["A1", "A2", "B1", "B2", "C1", "C2", "UNKNOWN"]
        
        for difficulty in difficulties {
            let testWord = WordDTO(
                word: "test",
                difficulty: difficulty,
                pronunciations: mockWord.pronunciations,
                coreDefinition: "test",
                contextualExplanation: mockWord.contextualExplanation,
                examples: [],
                usageScenarios: [],
                collocations: [],
                usageNotes: [],
                synonyms: []
            )
            
            let testView = WordCardView(
                word: testWord,
                onPlayAudio: { _ in },
                onSwipeAction: { _ in }
            )
            
            XCTAssertNotNil(testView)
            XCTAssertEqual(testWord.difficulty, difficulty)
        }
    }
    
    // MARK: - State Management Tests
    
    func testWordCardView_StateInitialization_ShouldHaveCorrectInitialState() {
        // Given & When
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // Then
        // Note: We can't directly access @State variables in unit tests
        // but we can verify the view initializes without errors
        XCTAssertNotNil(view)
    }
    
    // MARK: - Integration Tests
    
    func testWordCardView_CompleteWordData_ShouldDisplayAllSections() {
        // Given
        let completeWord = WordDTO.mock(word: "comprehensive")
        
        // When
        let view = WordCardView(
            word: completeWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        // Then
        XCTAssertNotNil(view)
        XCTAssertFalse(completeWord.pronunciations.isEmpty)
        XCTAssertFalse(completeWord.coreDefinition.isEmpty)
        XCTAssertFalse(completeWord.examples.isEmpty)
        XCTAssertFalse(completeWord.synonyms.isEmpty)
    }
    
    func testWordCardView_MultipleCallbacks_ShouldHandleCorrectly() {
        // Given
        var audioCallCount = 0
        var swipeCallCount = 0
        
        let view = WordCardView(
            word: mockWord,
            onPlayAudio: { _ in
                audioCallCount += 1
            },
            onSwipeAction: { _ in
                swipeCallCount += 1
            }
        )
        
        // When
        view.onPlayAudio(URL(string: "https://example.com/1.mp3")!)
        view.onPlayAudio(URL(string: "https://example.com/2.mp3")!)
        view.onSwipeAction(.nextWord)
        view.onSwipeAction(.markMastered)
        
        // Then
        XCTAssertEqual(audioCallCount, 2)
        XCTAssertEqual(swipeCallCount, 2)
    }
    
    // MARK: - Performance Tests
    
    func testWordCardView_LargeDataSet_ShouldPerformWell() {
        // Given
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // When
        for i in 0..<100 {
            let view = WordCardView(
                word: WordDTO.mock(word: "word\(i)"),
                onPlayAudio: { _ in },
                onSwipeAction: { _ in }
            )
            _ = view.body // Force view creation
        }
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        // Then
        XCTAssertLessThan(timeElapsed, 1.0, "View creation should be fast")
    }
    
    // MARK: - Edge Cases
    
    func testWordCardView_EmptyWord_ShouldHandleGracefully() {
        // Given
        let emptyWord = WordDTO(
            word: "",
            difficulty: "",
            pronunciations: [],
            coreDefinition: "",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "",
                emotionalResonance: "",
                vividImagery: "",
                etymologicalEssence: ""
            ),
            examples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: []
        )
        
        // When & Then
        let view = WordCardView(
            word: emptyWord,
            onPlayAudio: { _ in },
            onSwipeAction: { _ in }
        )
        
        XCTAssertNotNil(view)
    }
}
