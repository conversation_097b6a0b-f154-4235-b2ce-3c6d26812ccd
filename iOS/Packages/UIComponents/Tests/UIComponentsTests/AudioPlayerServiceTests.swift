import XCTest
import AVFoundation
@testable import UIComponents
@testable import SharedModels

@available(iOS 15.0, macOS 12.0, *)
@MainActor
final class AudioPlayerServiceTests: XCTestCase {
    
    var audioService: AudioPlayerService!
    
    override func setUp() async throws {
        try await super.setUp()
        audioService = AudioPlayerService()
    }
    
    override func tearDown() async throws {
        audioService.stopCurrentAudio()
        audioService = nil
        try await super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testAudioPlayerService_Initialization_ShouldSetDefaultValues() {
        // Given & When & Then
        XCTAssertFalse(audioService.isPlaying)
        XCTAssertNil(audioService.currentAudioURL)
        XCTAssertEqual(audioService.playbackProgress, 0.0)
        XCTAssertNil(audioService.errorMessage)
    }
    
    func testAudioPlayerService_Singleton_ShouldReturnSameInstance() {
        // Given & When
        let instance1 = AudioPlayerService.shared
        let instance2 = AudioPlayerService.shared
        
        // Then
        XCTAssertTrue(instance1 === instance2)
    }
    
    // MARK: - Audio Playback Tests
    
    func testPlayAudio_WithValidURL_ShouldStartPlayback() async {
        // Given
        let validURL = createMockAudioURL()
        
        // When
        do {
            try await audioService.playAudio(from: validURL)
            
            // Then
            XCTAssertTrue(audioService.isPlaying)
            XCTAssertEqual(audioService.currentAudioURL, validURL)
            XCTAssertNil(audioService.errorMessage)
        } catch {
            // Note: In a real test environment, this might fail due to no actual audio file
            // This test validates the setup logic rather than actual playback
            XCTAssertTrue(error is AudioPlayerError)
        }
    }
    
    func testPlayAudio_WithInvalidURL_ShouldThrowError() async {
        // Given
        let invalidURL = URL(string: "invalid://not-a-real-url")!
        
        // When & Then
        do {
            try await audioService.playAudio(from: invalidURL)
            XCTFail("Should throw error for invalid URL")
        } catch {
            XCTAssertTrue(error is AudioPlayerError)
            XCTAssertNotNil(audioService.errorMessage)
        }
    }
    
    func testStopCurrentAudio_WhenPlaying_ShouldStopPlayback() {
        // Given
        audioService.isPlaying = true
        audioService.currentAudioURL = createMockAudioURL()
        audioService.playbackProgress = 0.5
        
        // When
        audioService.stopCurrentAudio()
        
        // Then
        XCTAssertFalse(audioService.isPlaying)
        XCTAssertNil(audioService.currentAudioURL)
        XCTAssertEqual(audioService.playbackProgress, 0.0)
    }
    
    func testPauseAudio_WhenPlaying_ShouldPausePlayback() {
        // Given
        audioService.isPlaying = true
        
        // When
        audioService.pauseAudio()
        
        // Then
        XCTAssertFalse(audioService.isPlaying)
    }
    
    func testResumeAudio_WhenPaused_ShouldResumePlayback() {
        // Given
        audioService.isPlaying = false
        
        // When
        audioService.resumeAudio()
        
        // Then
        XCTAssertTrue(audioService.isPlaying)
    }
    
    // MARK: - Convenience Method Tests
    
    func testPlayPronunciation_WithValidPronunciation_ShouldCallPlayAudio() async {
        // Given
        let pronunciation = createMockPronunciation()
        
        // When
        do {
            try await audioService.playPronunciation(pronunciation)
            
            // Then
            XCTAssertEqual(audioService.currentAudioURL, pronunciation.audioUrl)
        } catch {
            // Expected in test environment without actual audio files
            XCTAssertTrue(error is AudioPlayerError)
        }
    }
    
    func testPlayExample_WithValidExample_ShouldCallPlayAudio() async {
        // Given
        let example = createMockExampleSentence()
        
        // When
        do {
            try await audioService.playExample(example)
            
            // Then
            XCTAssertEqual(audioService.currentAudioURL, example.audioUrl)
        } catch {
            // Expected in test environment without actual audio files
            XCTAssertTrue(error is AudioPlayerError)
        }
    }
    
    func testPlayPhrase_WithValidPhrase_ShouldCallPlayAudio() async {
        // Given
        let phrase = createMockPhraseBreakdown()
        
        // When
        do {
            try await audioService.playPhrase(phrase)
            
            // Then
            XCTAssertEqual(audioService.currentAudioURL, phrase.audioUrl)
        } catch {
            // Expected in test environment without actual audio files
            XCTAssertTrue(error is AudioPlayerError)
        }
    }
    
    // MARK: - Error Handling Tests
    
    func testAudioPlayerError_InvalidURL_ShouldHaveCorrectDescription() {
        // Given
        let url = URL(string: "https://invalid.com/audio.mp3")!
        let error = AudioPlayerError.invalidURL(url)
        
        // When & Then
        XCTAssertTrue(error.errorDescription?.contains("无效的音频 URL") == true)
        XCTAssertTrue(error.errorDescription?.contains(url.absoluteString) == true)
    }
    
    func testAudioPlayerError_AudioSessionSetupFailed_ShouldHaveCorrectDescription() {
        // Given
        let error = AudioPlayerError.audioSessionSetupFailed
        
        // When & Then
        XCTAssertEqual(error.errorDescription, "音频会话设置失败")
    }
    
    func testAudioPlayerError_NetworkError_ShouldHaveCorrectDescription() {
        // Given
        let error = AudioPlayerError.networkError
        
        // When & Then
        XCTAssertEqual(error.errorDescription, "网络连接错误，无法播放音频")
    }
    
    // MARK: - Audio Player State Tests
    
    func testAudioPlayerState_Playing_ShouldReturnCorrectProperties() {
        // Given
        let state = AudioPlayerState.playing
        
        // When & Then
        XCTAssertTrue(state.isPlaying)
        XCTAssertFalse(state.canPlay)
    }
    
    func testAudioPlayerState_Idle_ShouldReturnCorrectProperties() {
        // Given
        let state = AudioPlayerState.idle
        
        // When & Then
        XCTAssertFalse(state.isPlaying)
        XCTAssertTrue(state.canPlay)
    }
    
    func testAudioPlayerState_Paused_ShouldReturnCorrectProperties() {
        // Given
        let state = AudioPlayerState.paused
        
        // When & Then
        XCTAssertFalse(state.isPlaying)
        XCTAssertTrue(state.canPlay)
    }
    
    func testAudioPlayerState_Error_ShouldReturnCorrectProperties() {
        // Given
        let error = AudioPlayerError.networkError
        let state = AudioPlayerState.error(error)
        
        // When & Then
        XCTAssertFalse(state.isPlaying)
        XCTAssertFalse(state.canPlay)
    }
    
    // MARK: - Helper Methods
    
    private func createMockAudioURL() -> URL {
        return URL(string: "https://example.com/test-audio.mp3")!
    }
    
    private func createMockPronunciation() -> Pronunciation {
        return Pronunciation(
            dialect: "NAmE",
            symbol: "/test/",
            audioUrl: createMockAudioURL()
        )
    }
    
    private func createMockExampleSentence() -> ExampleSentence {
        return ExampleSentence(
            english: "This is a test sentence.",
            chinese: "这是一个测试句子。",
            audioUrl: createMockAudioURL(),
            phraseBreakdown: []
        )
    }
    
    private func createMockPhraseBreakdown() -> PhraseBreakdown {
        return PhraseBreakdown(
            phrase: "test phrase",
            translation: "测试短语",
            audioUrl: createMockAudioURL()
        )
    }
}
