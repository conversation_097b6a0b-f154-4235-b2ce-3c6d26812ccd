//
//  SearchAPIModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  Generated from KDD-022 Functions Contract Tweening Chain
//

import Foundation

// 导入共享模型以使用 PhoneticSymbol 和 LanguageCode
// 注意：这些类型已在 SharedModels.swift 中统一定义

// MARK: - 响应模型

/// 单词索引响应
struct WordIndexResponse: Codable {
    let success: Bool
    let data: [WordIndexItem]
    let lastSyncId: Int
    let metadata: WordIndexMetadata

    // 确保success字段在成功响应中始终为true
    init(data: [WordIndexItem], lastSyncId: Int, metadata: WordIndexMetadata) {
        self.success = true
        self.data = data
        self.lastSyncId = lastSyncId
        self.metadata = metadata
    }
}

/// 单词索引项
public struct WordIndexItem: Codable {
    public let syncId: Int
    public let page: Int
    public let word: String
    public let learningLanguage: LanguageCode
    public let scaffoldingLanguage: LanguageCode
    public let coreDefinition: String

    public init(syncId: Int, page: Int, word: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, coreDefinition: String) {
        self.syncId = syncId
        self.page = page
        self.word = word
        self.learningLanguage = learningLanguage
        self.scaffoldingLanguage = scaffoldingLanguage
        self.coreDefinition = coreDefinition
    }
}

// PhoneticSymbol 已移至 SharedModels.swift 中统一定义

/// 单词索引元数据
struct WordIndexMetadata: Codable {
    let count: Int
    let requestTime: Int
    let fromSyncId: Int
    let toSyncId: Int
}

// MARK: - 错误响应模型

/// 搜索索引错误响应
struct WordIndexErrorResponse: Codable {
    let error: WordIndexError
}

/// 搜索索引错误
struct WordIndexError: Codable {
    let code: WordIndexErrorCode
    let message: String
}

/// 搜索索引错误代码
enum WordIndexErrorCode: String, Codable {
    case authenticationFailed = "AUTHENTICATION_FAILED"
    case invalidLanguage = "INVALID_LANGUAGE"
    case databaseError = "DATABASE_ERROR"
    case invalidSyncId = "INVALID_SYNC_ID"
    case tooManyResults = "TOO_MANY_RESULTS"
}

/// 搜索索引详细错误响应
struct WordIndexDetailErrorResponse: Codable {
    let success: Bool
    let error: WordIndexDetailErrorCode
    let message: String
    let timestamp: String

    init(error: WordIndexDetailErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

/// 搜索索引详细错误代码
enum WordIndexDetailErrorCode: String, Codable {
    case invalidAPIKey = "INVALID_API_KEY"
    case rateLimitExceeded = "RATE_LIMIT_EXCEEDED"
    case serviceUnavailable = "SERVICE_UNAVAILABLE"
    case invalidParameters = "INVALID_PARAMETERS"
}

// MARK: - 下载范围响应模型

/// 下载范围响应
public struct DownloadRangeResponse: Codable {
    public let success: Bool
    public let startPage: Int
    public let endPage: Int
    public let totalPages: Int
    public let estimatedItems: Int
    public let message: String?

    public init(success: Bool, startPage: Int, endPage: Int, totalPages: Int, estimatedItems: Int, message: String? = nil) {
        self.success = success
        self.startPage = startPage
        self.endPage = endPage
        self.totalPages = totalPages
        self.estimatedItems = estimatedItems
        self.message = message
    }
}