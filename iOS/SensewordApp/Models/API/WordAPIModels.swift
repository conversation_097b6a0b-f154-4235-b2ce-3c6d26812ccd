//
//  WordAPIModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  Generated from KDD-022 Functions Contract Tweening Chain
//

import Foundation

// MARK: - 请求模型



// MARK: - 响应模型

/// 单词内容响应（用于搜索服务）
typealias WordContentResponse = WordDefinitionResponse

/// 单词定义响应 - 更新支持新数据库字段
struct WordDefinitionResponse: Codable {
    let word: String
    let metadata: WordMetadata
    let content: WordContent

    // 新增字段：支持多语言架构
    let learningLanguage: String?      // 学习语言代码，如 "en"
    let scaffoldingLanguage: String?   // 脚手架语言代码，如 "zh", "ja"
    let syncId: Int?                   // 增量同步ID（可选，用于本地缓存）
    let partsOfSpeech: String?         // 词性信息
    let culturalRiskRegions: [String]? // 文化风险区域
}

/// 单词元数据 - 保持向后兼容
struct WordMetadata: Codable {
    let wordFrequency: String
    let relatedConcepts: [String]
}

/// 单词内容
struct WordContent: Codable {
    let difficulty: String
    let phoneticSymbols: [PhoneticSymbol]
    let coreDefinition: String
    let contextualExplanation: ContextualExplanation
    let usageExamples: [UsageExampleCategory]
    let usageScenarios: [UsageScenario]
    let collocations: [Collocation]
    let usageNotes: [UsageNote]
    let synonyms: [Synonym]
}

// PhoneticSymbol 已移至 SharedModels.swift 中统一定义

/// 语境解释
struct ContextualExplanation: Codable {
    let nativeSpeakerIntent: String
    let emotionalResonance: String
    let vividImagery: String
    let etymologicalEssence: String
}

/// 使用示例分类
struct UsageExampleCategory: Codable {
    let category: String
    let examples: [UsageExample]
}

/// 使用示例
struct UsageExample: Codable {
    let english: String
    let translation: String
    /// 音频链接（可选，由后端TTS服务动态注入）
    let audioUrl: String?
    /// 短语分解（可选）
    let phraseBreakdown: [PhraseBreakdown]?
}

/// 短语分解
struct PhraseBreakdown: Codable {
    let phrase: String
    let translation: String
    /// 音频链接（可选，由后端TTS服务动态注入）
    let audioUrl: String?
}

/// 使用场景
struct UsageScenario: Codable {
    let category: String
    let relevance: String
    let context: String
}

/// 搭配词组
struct Collocation: Codable {
    let type: String
    let pattern: String
    let examples: [CollocationExample]
}

/// 搭配词组示例
struct CollocationExample: Codable {
    let collocation: String
    let translation: String
}

/// 使用注释
struct UsageNote: Codable {
    let aspect: String
    let explanation: String
    let examples: [UsageNoteExample]
}

/// 使用注释示例
struct UsageNoteExample: Codable {
    let sentence: String
    let translation: String
}

/// 同义词
struct Synonym: Codable {
    let word: String
    let explanation: String
    let examples: [SynonymExample]
}

/// 同义词示例
struct SynonymExample: Codable {
    let sentence: String
    let translation: String
}


// MARK: - 错误响应模型

/// 单词查询错误响应
struct WordQueryErrorResponse: Codable {
    let error: WordQueryError
}

/// 单词查询错误
struct WordQueryError: Codable {
    let code: WordQueryErrorCode
    let message: String
}

/// 单词查询错误代码
enum WordQueryErrorCode: String, Codable {
    case invalidWord = "INVALID_WORD"
    case databaseError = "DATABASE_ERROR"
}

