//
//  PurchaseAPIModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  Generated from KDD-022 Functions Contract Tweening Chain
//

import Foundation

// MARK: - 请求模型

/// 购买验证请求体
struct VerifyPurchaseRequest: Codable {
    let receiptData: String
    let productId: ProductId
    let transactionId: String
}

/// 产品ID枚举
enum ProductId: String, Codable, CaseIterable {
    case monthlyPremium = "com.senseword.premium.monthly"
    case yearlyPremium = "com.senseword.premium.yearly"
    case lifetimePremium = "com.senseword.premium.lifetime"

    /// 产品显示名称
    var displayName: String {
        switch self {
        case .monthlyPremium:
            return "月度订阅"
        case .yearlyPremium:
            return "年度订阅"
        case .lifetimePremium:
            return "终身买断"
        }
    }

    /// 产品价格（修正后的价格）
    var price: String {
        switch self {
        case .monthlyPremium:
            return "¥58/月"
        case .yearlyPremium:
            return "¥348/年"
        case .lifetimePremium:
            return "¥998"
        }
    }

    /// 原价（用于显示折扣）
    var originalPrice: String? {
        switch self {
        case .yearlyPremium:
            return "¥696/年"  // 7.99*12 = 95.88美金，约等于696人民币
        default:
            return nil
        }
    }

    /// 折扣标签
    var discountLabel: String? {
        switch self {
        case .yearlyPremium:
            return "限时 5折优惠"
        default:
            return nil
        }
    }

    /// 产品描述
    var description: String? {
        switch self {
        case .yearlyPremium:
            return "包含家庭共享\n平均每月仅需 ¥29"
        case .lifetimePremium:
            return "一次购买，终身使用\n包含家庭共享"
        case .monthlyPremium:
            return "随时可取消"
        }
    }

    /// 是否为推荐选项
    var isRecommended: Bool {
        return self == .yearlyPremium
    }
}

/// 恢复购买请求体
struct RestorePurchaseRequest: Codable {
    let receiptData: String
}

// MARK: - 响应模型

/// 购买验证响应
struct VerifyPurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}

/// 恢复购买响应
struct RestorePurchaseResponse: Codable {
    let success: Bool
    let isPro: Bool
    let expiresAt: String?
    let message: String

    // 确保success字段在成功响应中始终为true
    init(isPro: Bool, expiresAt: String?, message: String) {
        self.success = true
        self.isPro = isPro
        self.expiresAt = expiresAt
        self.message = message
    }
}

// MARK: - 错误响应模型

/// 购买验证错误响应
struct VerifyPurchaseErrorResponse: Codable {
    let success: Bool
    let error: VerifyPurchaseErrorCode
    let message: String
    let timestamp: String

    init(error: VerifyPurchaseErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

/// 购买验证错误代码
enum VerifyPurchaseErrorCode: String, Codable {
    case invalidInput = "INVALID_INPUT"
    case verificationFailed = "VERIFICATION_FAILED"
    case invalidReceipt = "INVALID_RECEIPT"
    case expiredReceipt = "EXPIRED_RECEIPT"
    case duplicatePurchase = "DUPLICATE_PURCHASE"
    case environmentMismatch = "ENVIRONMENT_MISMATCH"
    case bundleIdMismatch = "BUNDLE_ID_MISMATCH"
    case networkError = "NETWORK_ERROR"
    case internalError = "INTERNAL_ERROR"
    case unauthorized = "UNAUTHORIZED"
    case invalidAPIKey = "INVALID_API_KEY"
}

/// 恢复购买错误响应
struct RestorePurchaseErrorResponse: Codable {
    let success: Bool
    let error: RestorePurchaseErrorCode
    let message: String
    let timestamp: String

    init(error: RestorePurchaseErrorCode, message: String, timestamp: String) {
        self.success = false
        self.error = error
        self.message = message
        self.timestamp = timestamp
    }
}

/// 恢复购买错误代码
enum RestorePurchaseErrorCode: String, Codable {
    case invalidInput = "INVALID_INPUT"
    case noPurchasesToRestore = "NO_PURCHASES_TO_RESTORE"
    case restoreFailed = "RESTORE_FAILED"
    case invalidReceipt = "INVALID_RECEIPT"
    case expiredReceipt = "EXPIRED_RECEIPT"
    case environmentMismatch = "ENVIRONMENT_MISMATCH"
    case bundleIdMismatch = "BUNDLE_ID_MISMATCH"
    case networkError = "NETWORK_ERROR"
    case internalError = "INTERNAL_ERROR"
    case unauthorized = "UNAUTHORIZED"
    case invalidAPIKey = "INVALID_API_KEY"
} 