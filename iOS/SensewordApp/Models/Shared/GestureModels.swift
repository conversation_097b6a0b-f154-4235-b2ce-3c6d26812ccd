import Foundation
import SwiftUI

// MARK: - 手势检测模型
// 遵循TADA架构原则：通用的手势交互模型，独立于具体业务逻辑

/**
 * @description 滑动动作类型枚举
 * 定义用户可能触发的不同滑动动作
 */
public enum SwipeAction: CaseIterable, Equatable {
    case switchSynonym          // 切换同义词
    case triggerContentStream   // 触发内容流
    case showStreamHint        // 显示内容流提示
    case none                  // 无动作
    
    /// 动作对应的阈值
    public var threshold: CGFloat {
        switch self {
        case .switchSynonym:
            return 50.0
        case .showStreamHint:
            return 50.0
        case .triggerContentStream:
            return 120.0
        case .none:
            return 0.0
        }
    }
    
    /// 动作描述
    public var description: String {
        switch self {
        case .switchSynonym:
            return "切换同义词"
        case .triggerContentStream:
            return "触发内容流"
        case .showStreamHint:
            return "显示内容流提示"
        case .none:
            return "无动作"
        }
    }
    
    /// 动作图标名称
    public var iconName: String {
        switch self {
        case .switchSynonym:
            return "arrow.left.arrow.right"
        case .triggerContentStream:
            return "arrow.right.circle.fill"
        case .showStreamHint:
            return "hand.point.left"
        case .none:
            return "questionmark"
        }
    }
    
    /// 动作颜色
    public var color: Color {
        switch self {
        case .switchSynonym:
            return .blue
        case .triggerContentStream:
            return .green
        case .showStreamHint:
            return .orange
        case .none:
            return .gray
        }
    }
}

// MARK: - 手势阈值配置

/**
 * @description 手势阈值配置
 * 定义各种手势检测的阈值参数
 */
public struct SwipeThresholds {
    /// 同义词切换阈值
    public let synonymSwitchThreshold: CGFloat
    
    /// 内容流触发阈值
    public let contentStreamThreshold: CGFloat
    
    /// 右侧边缘检测区域宽度
    public let rightEdgeZone: CGFloat
    
    /// 提示显示阈值
    public let hintThreshold: CGFloat
    
    /// 默认配置
    public static let `default` = SwipeThresholds(
        synonymSwitchThreshold: 50.0,
        contentStreamThreshold: 120.0,
        rightEdgeZone: 80.0,
        hintThreshold: 30.0
    )
    
    /// 自定义配置
    public init(
        synonymSwitchThreshold: CGFloat,
        contentStreamThreshold: CGFloat,
        rightEdgeZone: CGFloat,
        hintThreshold: CGFloat
    ) {
        self.synonymSwitchThreshold = synonymSwitchThreshold
        self.contentStreamThreshold = contentStreamThreshold
        self.rightEdgeZone = rightEdgeZone
        self.hintThreshold = hintThreshold
    }
}

// MARK: - 手势检测结果

/**
 * @description 手势检测结果
 * 封装手势检测的结果信息
 */
public struct SwipeDetectionResult {
    /// 检测到的动作
    public let action: SwipeAction
    
    /// 检测置信度 (0.0-1.0)
    public let confidence: Double
    
    /// 是否在右侧边缘区域
    public let isInRightEdge: Bool
    
    /// 滑动距离
    public let translationDistance: CGFloat
    
    /// 起始位置
    public let startLocation: CGPoint
    
    /// 检测时间戳
    public let timestamp: Date
    
    /// 初始化
    public init(
        action: SwipeAction,
        confidence: Double = 1.0,
        isInRightEdge: Bool = false,
        translationDistance: CGFloat = 0.0,
        startLocation: CGPoint = .zero,
        timestamp: Date = Date()
    ) {
        self.action = action
        self.confidence = max(0.0, min(1.0, confidence))  // 确保在0-1范围内
        self.isInRightEdge = isInRightEdge
        self.translationDistance = translationDistance
        self.startLocation = startLocation
        self.timestamp = timestamp
    }
    
    /// 是否为有效检测结果
    public var isValid: Bool {
        return action != .none && confidence > 0.5
    }
}
