//
//  SharedModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  全局共享数据模型（语言代码等）
//

import Foundation

// MARK: - 全局共享枚举

/// 支持的语言代码枚举（22种语言）
/// 用于跨模块的语言相关功能：单词查询、生词本管理、音频服务、搜索索引等
public enum LanguageCode: String, Codable, CaseIterable {
    case english = "en"
    case chinese = "zh"
    case japanese = "ja"
    case german = "de"
    case french = "fr"
    case spanish = "es"
    case italian = "it"
    case portuguese = "pt"
    case polish = "pl"
    case swedish = "sv"
    case danish = "da"
    case norwegian = "no"
    case finnish = "fi"
    case korean = "ko"
    case russian = "ru"
    case arabic = "ar"
    case hindi = "hi"
    case thai = "th"
    case vietnamese = "vi"
    case turkish = "tr"
    case dutch = "nl"
    case indonesian = "id"

    /// 提供用户友好的显示名称
    var displayName: String {
        switch self {
        case .english: return "English"
        case .chinese: return "中文"
        case .japanese: return "日本語"
        case .german: return "Deutsch"
        case .french: return "Français"
        case .spanish: return "Español"
        case .italian: return "Italiano"
        case .portuguese: return "Português"
        case .polish: return "Polski"
        case .swedish: return "Svenska"
        case .danish: return "Dansk"
        case .norwegian: return "Norsk"
        case .finnish: return "Suomi"
        case .korean: return "한국어"
        case .russian: return "Русский"
        case .arabic: return "العربية"
        case .hindi: return "हिन्दी"
        case .thai: return "ไทย"
        case .vietnamese: return "Tiếng Việt"
        case .turkish: return "Türkçe"
        case .dutch: return "Nederlands"
        case .indonesian: return "Bahasa Indonesia"
        }
    }
}

// MARK: - 音标相关数据模型

/// 音标类型枚举
/// 定义支持的音标标注系统类型
enum PhoneticType: String, Codable, CaseIterable {
    case britishEnglish = "BrE"     // 英式英语音标
    case americanEnglish = "NAmE"   // 美式英语音标

    /// 提供用户友好的显示名称
    var displayName: String {
        switch self {
        case .britishEnglish:
            return "英式"
        case .americanEnglish:
            return "美式"
        }
    }

    /// 音标系统的完整名称
    var fullName: String {
        switch self {
        case .britishEnglish:
            return "British English"
        case .americanEnglish:
            return "North American English"
        }
    }

    /// 音标系统的优先级（用于排序显示）
    var priority: Int {
        switch self {
        case .britishEnglish:
            return 1
        case .americanEnglish:
            return 2
        }
    }
}

/// 音标符号统一数据模型
/// 用于表示单词的音标信息，支持多种音标系统
struct PhoneticSymbol: Codable, Hashable, Identifiable {
    /// 唯一标识符
    let id = UUID()

    /// 音标类型（英式/美式）
    let type: PhoneticType

    /// 音标符号字符串
    let symbol: String

    /// 音频链接（可选，由后端TTS服务动态注入）
    let audioUrl: String?

    /// 初始化方法
    init(type: PhoneticType, symbol: String, audioUrl: String? = nil) {
        self.type = type
        self.symbol = symbol
        self.audioUrl = audioUrl
    }

    /// 从API字符串创建音标符号
    /// - Parameters:
    ///   - typeString: API返回的类型字符串 ("BrE" 或 "NAmE")
    ///   - symbol: 音标符号
    ///   - audioUrl: 音频链接（可选）
    /// - Returns: PhoneticSymbol实例，如果类型无效则返回nil
    static func fromAPIString(typeString: String, symbol: String, audioUrl: String? = nil) -> PhoneticSymbol? {
        guard let type = PhoneticType(rawValue: typeString) else {
            return nil
        }
        return PhoneticSymbol(type: type, symbol: symbol, audioUrl: audioUrl)
    }

    /// 转换为API字符串格式
    /// - Returns: 包含type和symbol的字典，用于API请求
    func toAPIFormat() -> [String: String] {
        return [
            "type": type.rawValue,
            "symbol": symbol
        ]
    }

    /// 格式化显示文本
    /// - Returns: 用于UI显示的格式化字符串
    var displayText: String {
        return "\(type.displayName): \(symbol)"
    }

    /// 完整显示文本
    /// - Returns: 包含完整音标系统名称的显示字符串
    var fullDisplayText: String {
        return "\(type.fullName): \(symbol)"
    }
}

// MARK: - PhoneticSymbol扩展方法

extension PhoneticSymbol {
    /// 创建英式音标
    static func british(_ symbol: String, audioUrl: String? = nil) -> PhoneticSymbol {
        return PhoneticSymbol(type: .britishEnglish, symbol: symbol, audioUrl: audioUrl)
    }

    /// 创建美式音标
    static func american(_ symbol: String, audioUrl: String? = nil) -> PhoneticSymbol {
        return PhoneticSymbol(type: .americanEnglish, symbol: symbol, audioUrl: audioUrl)
    }

    /// 创建测试用的音标数据
    static func mock(type: PhoneticType = .britishEnglish, symbol: String = "/test/", audioUrl: String? = nil) -> PhoneticSymbol {
        return PhoneticSymbol(type: type, symbol: symbol, audioUrl: audioUrl)
    }
}

// MARK: - PhoneticSymbol数组扩展

extension Array where Element == PhoneticSymbol {
    /// 按优先级排序音标
    /// - Returns: 按音标类型优先级排序的数组
    func sortedByPriority() -> [PhoneticSymbol] {
        return self.sorted { $0.type.priority < $1.type.priority }
    }

    /// 获取英式音标
    /// - Returns: 第一个英式音标，如果不存在则返回nil
    var britishPhonetic: PhoneticSymbol? {
        return self.first { $0.type == .britishEnglish }
    }

    /// 获取美式音标
    /// - Returns: 第一个美式音标，如果不存在则返回nil
    var americanPhonetic: PhoneticSymbol? {
        return self.first { $0.type == .americanEnglish }
    }

    /// 获取主要音标（优先英式，其次美式）
    /// - Returns: 主要音标，如果都不存在则返回第一个
    var primaryPhonetic: PhoneticSymbol? {
        return britishPhonetic ?? americanPhonetic ?? first
    }

    /// 转换为API格式
    /// - Returns: 用于API请求的字典数组
    func toAPIFormat() -> [[String: String]] {
        return self.map { $0.toAPIFormat() }
    }
}

// 其他全局共享的枚举和数据结构可以在此添加
// 例如：通用错误代码、状态枚举等
