//
//  RecommendationModels.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  推荐系统相关数据模型
//

import Foundation

// MARK: - 推荐模式枚举

/**
 * @description 推荐模式类型
 * 定义三种不同的内容推荐流模式
 */
public enum RecommendationMode: String, CaseIterable, Equatable {
    case daily = "daily"           // 每日一词推荐流
    case search = "search"         // 搜索词推荐流
    case bookmark = "bookmark"     // 生词本推荐流
    
    /// 模式显示名称
    public var displayName: String {
        switch self {
        case .daily:
            return "每日发现"
        case .search:
            return "搜索探索"
        case .bookmark:
            return "复习巩固"
        }
    }
    
    /// 模式描述
    public var description: String {
        switch self {
        case .daily:
            return "基于每日一词的探索之旅"
        case .search:
            return "从搜索词扩展的概念网络"
        case .bookmark:
            return "围绕收藏生词的深度学习"
        }
    }
    
    /// 模式图标
    public var iconName: String {
        switch self {
        case .daily:
            return "calendar.circle.fill"
        case .search:
            return "magnifyingglass.circle.fill"
        case .bookmark:
            return "bookmark.circle.fill"
        }
    }
}

// MARK: - 推荐项数据模型

/**
 * @description 推荐项
 * 表示推荐数组中的单个项目
 */
public struct RecommendationItem: Identifiable, Equatable {
    public let id = UUID()
    public let word: String
    public let source: RecommendationSource
    public let priority: Int
    public let timestamp: Date
    
    public init(
        word: String,
        source: RecommendationSource,
        priority: Int = 0,
        timestamp: Date = Date()
    ) {
        self.word = word
        self.source = source
        self.priority = priority
        self.timestamp = timestamp
    }
}

/**
 * @description 推荐来源
 * 标识推荐项的来源类型
 */
public enum RecommendationSource: String, CaseIterable, Equatable {
    case dailyWord = "daily_word"           // 每日一词
    case relatedConcept = "related_concept" // 关联概念
    case searchWord = "search_word"         // 搜索词
    case bookmarkWord = "bookmark_word"     // 收藏生词
    case userHistory = "user_history"       // 用户历史
    
    /// 来源显示名称
    public var displayName: String {
        switch self {
        case .dailyWord:
            return "每日一词"
        case .relatedConcept:
            return "相关概念"
        case .searchWord:
            return "搜索词"
        case .bookmarkWord:
            return "收藏生词"
        case .userHistory:
            return "历史记录"
        }
    }
    
    /// 优先级权重
    public var priorityWeight: Int {
        switch self {
        case .dailyWord:
            return 100
        case .searchWord:
            return 90
        case .bookmarkWord:
            return 80
        case .relatedConcept:
            return 70
        case .userHistory:
            return 60
        }
    }
}

// MARK: - 推荐数组状态

/**
 * @description 推荐数组状态
 * 管理推荐数组的当前状态
 */
public struct RecommendationArrayState: Equatable {
    public let mode: RecommendationMode
    public let items: [RecommendationItem]
    public let currentIndex: Int
    public let isLoading: Bool
    public let lastUpdated: Date
    
    public init(
        mode: RecommendationMode = .daily,
        items: [RecommendationItem] = [],
        currentIndex: Int = 0,
        isLoading: Bool = false,
        lastUpdated: Date = Date()
    ) {
        self.mode = mode
        self.items = items
        self.currentIndex = currentIndex
        self.isLoading = isLoading
        self.lastUpdated = lastUpdated
    }
    
    /// 是否有下一个项目
    public var hasNext: Bool {
        return currentIndex < items.count - 1
    }
    
    /// 是否有上一个项目
    public var hasPrevious: Bool {
        return currentIndex > 0
    }
    
    /// 当前项目
    public var currentItem: RecommendationItem? {
        guard currentIndex >= 0 && currentIndex < items.count else {
            return nil
        }
        return items[currentIndex]
    }
    
    /// 下一个项目
    public var nextItem: RecommendationItem? {
        guard hasNext else { return nil }
        return items[currentIndex + 1]
    }
    
    /// 剩余项目数量
    public var remainingCount: Int {
        return max(0, items.count - currentIndex - 1)
    }
    
    /// 进度百分比
    public var progress: Double {
        guard !items.isEmpty else { return 0.0 }
        return Double(currentIndex + 1) / Double(items.count)
    }
}

// MARK: - 推荐配置

/**
 * @description 推荐配置
 * 控制推荐算法的参数
 */
public struct RecommendationConfig {
    public let maxArraySize: Int
    public let minArraySize: Int
    public let autoExtendThreshold: Int
    public let cacheSize: Int
    public let enableSingleLayerCascade: Bool
    public let enableDoubleLayerCascade: Bool  // 新增：启用二层级联
    public let maxRecommendationWords: Int     // 新增：推荐数组最大单词数

    public static let `default` = RecommendationConfig(
        maxArraySize: 50,           // 最大推荐数组大小
        minArraySize: 10,           // 最小推荐数组大小
        autoExtendThreshold: 5,     // 自动扩展阈值
        cacheSize: 20,              // 缓存大小
        enableSingleLayerCascade: true,   // 启用单层级联（向后兼容）
        enableDoubleLayerCascade: true,   // 启用二层级联
        maxRecommendationWords: 30        // 推荐数组最多30个单词
    )
    
    public init(
        maxArraySize: Int,
        minArraySize: Int,
        autoExtendThreshold: Int,
        cacheSize: Int,
        enableSingleLayerCascade: Bool,
        enableDoubleLayerCascade: Bool = true,
        maxRecommendationWords: Int = 30
    ) {
        self.maxArraySize = maxArraySize
        self.minArraySize = minArraySize
        self.autoExtendThreshold = autoExtendThreshold
        self.cacheSize = cacheSize
        self.enableSingleLayerCascade = enableSingleLayerCascade
        self.enableDoubleLayerCascade = enableDoubleLayerCascade
        self.maxRecommendationWords = maxRecommendationWords
    }
}

// MARK: - 推荐错误类型

/**
 * @description 推荐系统错误类型
 */
public enum RecommendationError: Error, LocalizedError {
    case arrayEmpty
    case indexOutOfBounds
    case networkError(String)
    case dataParsingError(String)
    case configurationError(String)
    
    public var errorDescription: String? {
        switch self {
        case .arrayEmpty:
            return "推荐数组为空"
        case .indexOutOfBounds:
            return "索引超出范围"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .dataParsingError(let message):
            return "数据解析错误: \(message)"
        case .configurationError(let message):
            return "配置错误: \(message)"
        }
    }
}

// MARK: - 推荐统计信息

/**
 * @description 推荐统计信息
 * 用于分析和优化推荐算法
 */
public struct RecommendationStats {
    public let totalRecommendations: Int
    public let sourceDistribution: [RecommendationSource: Int]
    public let averageSessionLength: Double
    public let userEngagementRate: Double
    public let lastSessionDate: Date?
    
    public init(
        totalRecommendations: Int = 0,
        sourceDistribution: [RecommendationSource: Int] = [:],
        averageSessionLength: Double = 0.0,
        userEngagementRate: Double = 0.0,
        lastSessionDate: Date? = nil
    ) {
        self.totalRecommendations = totalRecommendations
        self.sourceDistribution = sourceDistribution
        self.averageSessionLength = averageSessionLength
        self.userEngagementRate = userEngagementRate
        self.lastSessionDate = lastSessionDate
    }
}
