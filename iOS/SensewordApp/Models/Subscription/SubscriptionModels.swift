//
//  SubscriptionModels.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  订阅页面相关数据模型
//

import Foundation

// 导入ProductId枚举
// 注意：ProductId定义在PurchaseAPIModels.swift中

// MARK: - 订阅产品模型

/// 订阅产品信息
struct SubscriptionProduct: Identifiable, Equatable {
    let id: ProductId
    let title: String
    let price: String
    let originalPrice: String?
    let discountLabel: String?
    let description: String?
    let isRecommended: Bool
    let isSelected: Bool
    
    /// 从ProductId创建订阅产品
    static func from(_ productId: ProductId, isSelected: Bool = false) -> SubscriptionProduct {
        return SubscriptionProduct(
            id: productId,
            title: productId.displayName,
            price: productId.price,
            originalPrice: productId.originalPrice,
            discountLabel: productId.discountLabel,
            description: productId.description,
            isRecommended: productId.isRecommended,
            isSelected: isSelected
        )
    }
    
    /// 获取所有可用的订阅产品
    static var allProducts: [SubscriptionProduct] {
        return ProductId.allCases.map { productId in
            SubscriptionProduct.from(productId, isSelected: productId.isRecommended)
        }
    }
}

// MARK: - 订阅页面状态

/// 订阅页面状态枚举
enum SubscriptionPageState {
    case loading
    case ready([SubscriptionProduct])
    case purchasing(ProductId)
    case restoring
    case error(String)
}

// MARK: - 购买结果

/// 购买结果枚举
enum PurchaseResult {
    case success(ProductId)
    case cancelled
    case failed(Error)
    case pending
}



// MARK: - 订阅页面配置

/// 订阅页面配置
struct SubscriptionPageConfig {
    let title: String
    let subtitle: String
    let features: [String]
    let continueButtonTitle: String
    let restoreButtonTitle: String
    let cancelAnytimeText: String
    
    /// 默认配置
    static let `default` = SubscriptionPageConfig(
        title: "深思语境会员",
        subtitle: "不止于词，抵达心语\n解锁全部功能，开启深度学习之旅",
        features: [
            "无限制单词查询与深度解析",
            "每日精选词汇推荐",
            "无限探索关联词",
            "高品质例句语音",
            "智能个性化学习计划与进度追踪",
            "多设备云端同步，学习记录永不丢失",
            "专属例句与场景化应用练习",
            "详细学习统计与成长报告",
            "专属客服支持，学习路上不孤单"
        ],
        continueButtonTitle: "立即开通",
        restoreButtonTitle: "恢复购买",
        cancelAnytimeText: "随时可取消"
    )
}

// MARK: - 订阅错误类型

/// 订阅相关错误
enum SubscriptionError: LocalizedError {
    case productNotFound
    case purchaseNotAllowed
    case networkError
    case verificationFailed
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .productNotFound:
            return "产品未找到"
        case .purchaseNotAllowed:
            return "当前设备不允许购买"
        case .networkError:
            return "网络连接错误"
        case .verificationFailed:
            return "购买验证失败"
        case .unknown(let message):
            return "未知错误: \(message)"
        }
    }
}
