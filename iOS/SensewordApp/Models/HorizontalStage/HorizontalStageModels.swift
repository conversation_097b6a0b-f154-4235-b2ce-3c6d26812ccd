//
//  HorizontalStageModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  水平舞台数据模型 - 支持深度交互的状态管理
//

import Foundation
import SwiftUI

// MARK: - 区块类型枚举

/// 水平舞台区块类型
enum SectionType: String, CaseIterable {
    case deepContext = "deep_context"
    case examples = "examples"
    case scenarios = "scenarios"
    case usageNotes = "usage_notes"
    case synonyms = "synonyms"
}

// MARK: - 深思语境状态

/// 深思语境内容类型
enum DeepContextContentType: String, CaseIterable {
    case definition = "definition"
    case imagery = "imagery"
    case etymology = "etymology"
}

/// 深思语境状态
class DeepContextState: ObservableObject {
    /// 是否可见
    @Published var isVisible: Bool = true
    
    /// 当前内容类型
    @Published var contentType: DeepContextContentType = .definition
    
    /// 是否正在过渡
    @Published var isTransitioning: Bool = false
    
    /// 触发过渡动画
    func triggerTransition() {
        isTransitioning = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.isTransitioning = false
        }
    }
}

// MARK: - 例句舞台状态

/// 例句舞台状态
class ExampleStageState: ObservableObject {
    /// 当前例句分类索引
    @Published var currentCategoryIndex: Int = 0
    
    /// 当前例句索引
    @Published var currentExampleIndex: Int = 0
    
    /// 是否处于短语分解模式
    @Published var isInPhraseMode: Bool = false
    
    /// 当前短语索引
    @Published var currentPhraseIndex: Int = 0
    
    /// 是否显示呼吸提示
    @Published var showBreathingHint: Bool = true
    
    /// 短语分解状态
    @Published var phraseBreakdownState: PhraseBreakdownState?
    
    /// 音频播放状态
    @Published var audioPlaybackState = AudioPlaybackState()
}

/// 短语分解状态
class PhraseBreakdownState: ObservableObject {
    /// 是否激活
    @Published var isActive: Bool = false
    
    /// 当前例句
    var currentExample: UsageExample
    
    /// 短语列表
    var phrases: [PhraseBreakdown]
    
    /// 当前短语索引
    @Published var currentPhraseIndex: Int = 0
    
    /// 高亮的短语
    @Published var highlightedPhrase: String = ""
    
    /// 初始化
    init(isActive: Bool = false, 
         currentExample: UsageExample = UsageExample(english: "", translation: "", audioUrl: nil, phraseBreakdown: nil),
         phrases: [PhraseBreakdown] = [],
         currentPhraseIndex: Int = 0,
         highlightedPhrase: String = "") {
        self.isActive = isActive
        self.currentExample = currentExample
        self.phrases = phrases
        self.currentPhraseIndex = currentPhraseIndex
        self.highlightedPhrase = highlightedPhrase
    }
    
    /// 当前短语
    var currentPhrase: PhraseBreakdown? {
        guard currentPhraseIndex < phrases.count else { return nil }
        return phrases[currentPhraseIndex]
    }
    
    /// 是否是最后一个短语
    var isLastPhrase: Bool {
        return currentPhraseIndex >= phrases.count - 1
    }
}

/// 音频播放状态
class AudioPlaybackState: ObservableObject {
    /// 是否正在播放
    @Published var isPlaying: Bool = false
    
    /// 当前音频类型
    @Published var currentAudioType: AudioType = .fullSentence
    
    /// 当前音频索引
    @Published var currentAudioIndex: Int = 0
}

/// 音频类型
enum AudioType: String {
    case fullSentence = "full_sentence"
    case phraseBreakdown = "phrase_breakdown"
    case word = "word"
}

// MARK: - 场景轮换状态

/// 场景轮换状态
class ScenarioCarouselState: ObservableObject {
    /// 当前场景索引
    @Published var currentIndex: Int = 0
    
    /// 是否激活
    @Published var isActive: Bool = false
    
    /// 过渡动画状态
    @Published var transitionAnimation: CarouselTransition = .none
    
    /// 是否循环
    @Published var isLooping: Bool = true
    
    /// 触发过渡动画
    func triggerTransition() {
        transitionAnimation = .sliding
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.transitionAnimation = .none
        }
    }
}

/// 轮换过渡动画类型
enum CarouselTransition {
    case none
    case sliding
    case fading
}

// MARK: - 用法注释状态

/// 用法注释状态
class UsageNotesState: ObservableObject {
    /// 当前用法注释索引
    @Published var currentIndex: Int = 0
    
    /// 是否激活
    @Published var isActive: Bool = false
    
    /// 过渡动画状态
    @Published var transitionAnimation: CarouselTransition = .none
    
    /// 触发过渡动画
    func triggerTransition() {
        transitionAnimation = .sliding
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.transitionAnimation = .none
        }
    }
}

// MARK: - 同义词状态

/// 同义词状态
class SynonymsState: ObservableObject {
    /// 当前同义词索引
    @Published var currentIndex: Int = 0
    
    /// 是否激活
    @Published var isActive: Bool = false
    
    /// 过渡动画状态
    @Published var transitionAnimation: CarouselTransition = .none
    
    /// 触发过渡动画
    func triggerTransition() {
        transitionAnimation = .sliding
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.transitionAnimation = .none
        }
    }
}

// MARK: - 全局动画状态

/// 全局动画状态
class AnimationState: ObservableObject {
    /// 是否正在过渡
    @Published var isTransitioning: Bool = false
    
    /// 动画强度 (0.0 - 1.0)
    @Published var intensity: Double = 1.0
    
    /// 动画持续时间
    @Published var duration: Double = 0.3
}

// MARK: - 呼吸动画状态

/// 呼吸动画状态
class BreathingHintState: ObservableObject {
    /// 是否应该显示
    @Published var shouldShow: Bool = false
    
    /// 动画阶段
    @Published var animationPhase: BreathingPhase = .pause
    
    /// 动画强度 (0.0 - 1.0)
    @Published var intensity: Double = 0.8
    
    /// 不透明度
    @Published var opacity: Double = 0.6
    
    /// 缩放比例
    @Published var scale: Double = 1.0
    
    /// 发光半径
    @Published var glowRadius: Double = 10.0
    
    /// 动画持续时间
    @Published var animationDuration: Double = 2.0
}

/// 呼吸动画阶段
enum BreathingPhase {
    case inhale
    case exhale
    case pause
}

// MARK: - 高亮文本片段

/// 高亮文本片段
struct HighlightedTextSegment: Identifiable {
    let id = UUID()
    let text: String
    let isHighlighted: Bool
    let opacity: Double
    
    init(text: String, isHighlighted: Bool = false, opacity: Double = 1.0) {
        self.text = text
        self.isHighlighted = isHighlighted
        self.opacity = opacity
    }
}

// MARK: - 触觉反馈类型

/// 触觉反馈类型
enum HapticFeedbackType {
    case light
    case medium
    case heavy
    case selection
    case success
    case warning
    case error
}

// MARK: - 交互事件

/// 交互事件
enum InteractionEvent {
    case sectionSwitch(from: SectionType, to: SectionType)
    case phraseSwitch(phraseIndex: Int)
    case contentSwitch(contentType: DeepContextContentType)
    case carouselSwitch(index: Int)
    case audioPlay(audioType: AudioType)
    case gestureRecognized(gestureType: GestureType)
}

/// 手势类型
enum GestureType {
    case horizontalSwipe(direction: SwipeDirection)
    case verticalSwipe(direction: SwipeDirection)
    case tap
    case longPress
}

/// 滑动方向
enum SwipeDirection {
    case left
    case right
    case up
    case down
}

// MARK: - 视觉反馈状态

/// 视觉反馈状态
struct VisualFeedbackState {
    let showHighlight: Bool
    let highlightColor: Color
    let animationScale: Double
    let animationOpacity: Double
    
    init(showHighlight: Bool = false,
         highlightColor: Color = .blue,
         animationScale: Double = 1.0,
         animationOpacity: Double = 1.0) {
        self.showHighlight = showHighlight
        self.highlightColor = highlightColor
        self.animationScale = animationScale
        self.animationOpacity = animationOpacity
    }
}

// MARK: - 命令模式

/// 音频命令
struct AudioCommand {
    let action: AudioAction
    let audioUrl: String?
    let audioType: AudioType
    
    enum AudioAction {
        case play
        case pause
        case stop
        case preload
    }
}

/// 触觉命令
struct HapticCommand {
    let feedbackType: HapticFeedbackType
    let intensity: Double
    let delay: TimeInterval
    
    init(feedbackType: HapticFeedbackType, intensity: Double = 1.0, delay: TimeInterval = 0.0) {
        self.feedbackType = feedbackType
        self.intensity = intensity
        self.delay = delay
    }
}
