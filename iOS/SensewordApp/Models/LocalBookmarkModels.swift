//
//  LocalBookmarkModels.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-07-27.
//  Generated from SenseWord后端无状态化重构任务
//

import Foundation

// MARK: - 本地收藏数据模型

/// 本地收藏条目
/// 核心职责：表示本地SQLite中存储的收藏记录
struct LocalBookmarkItem: Codable, Identifiable, Equatable {
    let id: Int
    let word: String
    let language: LanguageCode
    let createdAt: Date
    let deviceId: String? // 可选字段，用于向后兼容
    
    /// 从数据库查询结果创建收藏条目
    /// - Parameter dbResult: 数据库查询结果字典
    /// - Returns: 收藏条目实例，如果数据无效则返回nil
    static func from(dbResult: [String: Any]) -> LocalBookmarkItem? {
        guard let id = dbResult["id"] as? Int,
              let word = dbResult["word"] as? String,
              let languageString = dbResult["language"] as? String,
              let language = LanguageCode(rawValue: languageString),
              let createdAtString = dbResult["created_at"] as? String,
              let deviceId = dbResult["device_id"] as? String else {
            return nil
        }
        
        // 解析ISO 8601日期格式
        let dateFormatter = ISO8601DateFormatter()
        let createdAt = dateFormatter.date(from: createdAtString) ?? Date()
        
        return LocalBookmarkItem(
            id: id,
            word: word,
            language: language,
            createdAt: createdAt,
            deviceId: deviceId
        )
    }
    
    /// 转换为数据库插入参数
    /// - Returns: 数据库插入参数数组
    func toDatabaseParameters() -> [Any] {
        let dateFormatter = ISO8601DateFormatter()
        return [word, language.rawValue, dateFormatter.string(from: createdAt), deviceId]
    }
}

// MARK: - 收藏状态枚举

/// 收藏状态
/// 用于UI层表示单词的收藏状态
enum BookmarkStatus: String, CaseIterable, Codable {
    case bookmarked = "bookmarked"
    case notBookmarked = "not_bookmarked"
    
    /// 布尔值表示
    var isBookmarked: Bool {
        return self == .bookmarked
    }
    
    /// 从布尔值创建状态
    /// - Parameter isBookmarked: 是否已收藏
    /// - Returns: 对应的收藏状态
    static func from(isBookmarked: Bool) -> BookmarkStatus {
        return isBookmarked ? .bookmarked : .notBookmarked
    }
    
    /// 切换状态
    /// - Returns: 切换后的状态
    func toggled() -> BookmarkStatus {
        return self == .bookmarked ? .notBookmarked : .bookmarked
    }
}

// MARK: - 收藏操作结果

/// 收藏操作结果
/// 用于表示收藏操作的执行结果
struct BookmarkOperationResult: Codable {
    let success: Bool
    let message: String
    let bookmarkStatus: BookmarkStatus
    
    /// 成功结果
    /// - Parameters:
    ///   - message: 成功消息
    ///   - status: 操作后的收藏状态
    /// - Returns: 成功结果实例
    static func success(message: String, status: BookmarkStatus) -> BookmarkOperationResult {
        return BookmarkOperationResult(success: true, message: message, bookmarkStatus: status)
    }
    
    /// 失败结果
    /// - Parameters:
    ///   - message: 错误消息
    ///   - status: 当前收藏状态
    /// - Returns: 失败结果实例
    static func failure(message: String, status: BookmarkStatus) -> BookmarkOperationResult {
        return BookmarkOperationResult(success: false, message: message, bookmarkStatus: status)
    }
}


