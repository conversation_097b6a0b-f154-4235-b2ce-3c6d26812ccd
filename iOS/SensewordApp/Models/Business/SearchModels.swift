//
//  SearchModels.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  搜索功能专用业务数据模型
//

import Foundation

// MARK: - 搜索建议模型

/// 搜索建议数据模型
/// 用于本地搜索建议显示，包含用户最需要的核心信息
/// 数据来源：基于 WordIndexItem (API层单一真实来源)
struct SearchSuggestion: Codable, Identifiable, Hashable {
    /// 唯一标识符（基于单词生成，确保一致性）
    var id: String { word }

    /// 建议的单词
    /// 数据来源：WordIndexItem.word
    let word: String

    /// 核心释义 - 用户最需要的信息
    /// 数据来源：WordIndexItem.coreDefinition
    let definition: String

    /// 相关性分数 - 用于搜索排序 (0.0-1.0)
    /// 数据来源：本地计算（基于查询匹配度）
    let relevanceScore: Double

    /// 是否有完整内容缓存
    /// 数据来源：CacheService.checkCacheStatus 计算结果
    let hasFullContent: Bool

    /// 初始化方法
    init(word: String, definition: String, relevanceScore: Double, hasFullContent: Bool) {
        self.word = word
        self.definition = definition
        self.relevanceScore = relevanceScore
        self.hasFullContent = hasFullContent
    }

    /// 计算属性：用于UI展示的格式化文本
    var displayText: String {
        return "\(word) - \(definition)"
    }
}

// MARK: - 缓存状态模型

/// 三级缓存状态枚举
/// 支持完整内容缓存、索引基础信息、无缓存的三级降级策略
enum CacheStatus {
    /// 完整内容缓存 - 包含AI生成的详细解析
    case full(WordDefinitionResponse)
    
    /// 索引基础信息 - 包含音标和核心释义
    case index(WordIndexItem)
    
    /// 无缓存 - 需要网络请求
    case none
}

// MARK: - 搜索配置模型

/// 搜索功能配置
struct SearchConfig {
    /// 搜索建议数量限制
    let suggestionLimit: Int = 10
    
    /// 防抖延迟时间（秒）
    let debounceDelay: TimeInterval = 0.3
    
    /// 缓存超时时间（秒）
    let cacheTimeout: TimeInterval = 3600
    
    /// 搜索响应时间阈值（毫秒）
    let responseTimeThreshold: Int = 100
}

// MARK: - 搜索错误类型

/// 搜索功能相关错误类型
enum SearchError: Error, LocalizedError {
    /// 网络不可用
    case networkUnavailable
    
    /// 索引数据损坏
    case indexCorrupted
    
    /// 内容生成失败
    case contentGenerationFailed
    
    /// 无效查询
    case invalidQuery
    
    /// 查询过短
    case queryTooShort
    
    /// 数据库错误
    case databaseError(String)
    
    var errorDescription: String? {
        switch self {
        case .networkUnavailable:
            return "网络连接不可用，正在使用本地缓存"
        case .indexCorrupted:
            return "本地索引数据损坏，正在重建"
        case .contentGenerationFailed:
            return "内容生成失败，请稍后重试"
        case .invalidQuery:
            return "无效的搜索查询"
        case .queryTooShort:
            return "搜索词过短，请输入更多字符"
        case .databaseError(let message):
            return "数据库错误：\(message)"
        }
    }
}

// MARK: - 搜索统计模型

/// 搜索性能统计
struct SearchMetrics {
    /// 搜索查询
    let query: String
    
    /// 响应时间（毫秒）
    let responseTime: Int
    
    /// 结果数量
    let resultCount: Int
    
    /// 是否命中缓存
    let cacheHit: Bool
    
    /// 搜索来源
    let source: SearchSource
    
    /// 时间戳
    let timestamp: Date
}

/// 搜索来源枚举
enum SearchSource {
    /// 本地索引
    case localIndex

    /// 网络API
    case networkAPI

    /// 内存缓存
    case memoryCache

    /// 磁盘缓存
    case diskCache
}

/// 搜索策略枚举
enum SearchStrategy {
    /// 本地优先
    case localFirst

    /// 混合搜索
    case hybrid

    /// 网络优先
    case networkFirst
}

/// 内容加载状态
enum ContentLoadingState: Equatable {
    /// 空闲
    case idle

    /// 加载中
    case loading

    /// 加载成功
    case loaded(WordDefinitionResponse)

    /// 加载失败
    case error(String)

    static func == (lhs: ContentLoadingState, rhs: ContentLoadingState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading):
            return true
        case (.loaded(let lhsData), .loaded(let rhsData)):
            return lhsData.word == rhsData.word
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}

// MARK: - 搜索状态模型

/// 搜索UI状态
enum SearchState {
    /// 空闲状态
    case idle

    /// 未激活
    case inactive
    
    /// 激活中（手势进行中）
    case activating(progress: CGFloat)
    
    /// 已激活
    case active
    
    /// 搜索中
    case searching
    
    /// 显示结果
    case showingResults([SearchSuggestion])
    
    /// 显示错误
    case error(SearchError)
}

// MARK: - 搜索响应模型

/// 搜索建议响应
/// 业务层组装的搜索建议响应，包含元数据和性能统计
struct SearchSuggestionsResponse: Codable {
    let success: Bool
    let data: [SearchSuggestion]
    let metadata: SearchMetadata

    init(data: [SearchSuggestion], metadata: SearchMetadata) {
        self.success = true
        self.data = data
        self.metadata = metadata
    }
}

/// 搜索元数据
/// 包含搜索性能和来源信息，用于调试和优化
struct SearchMetadata: Codable {
    let query: String
    let resultCount: Int
    let responseTime: Int
    let source: String
}

// MARK: - 扩展方法

extension SearchSuggestion {
    /// 创建用于测试的搜索建议
    /// 注意：只使用API层实际提供的字段
    static func mock(word: String, definition: String, score: Double = 0.8, hasContent: Bool = false) -> SearchSuggestion {
        return SearchSuggestion(
            word: word,
            definition: definition,
            relevanceScore: score,
            hasFullContent: hasContent
        )
    }

    /// 从 WordIndexItem 创建 SearchSuggestion
    /// 确保数据来源的单一真实性
    static func from(wordIndexItem: WordIndexItem, relevanceScore: Double, hasFullContent: Bool) -> SearchSuggestion {
        return SearchSuggestion(
            word: wordIndexItem.word,
            definition: wordIndexItem.coreDefinition,
            relevanceScore: relevanceScore,
            hasFullContent: hasFullContent
        )
    }
}

extension CacheStatus {
    /// 是否有可用内容
    var hasContent: Bool {
        switch self {
        case .full, .index:
            return true
        case .none:
            return false
        }
    }
    
    /// 获取显示用的单词
    var displayWord: String? {
        switch self {
        case .full(let response):
            return response.word
        case .index(let item):
            return item.word
        case .none:
            return nil
        }
    }
}

extension SearchError {
    /// 是否为可恢复错误
    var isRecoverable: Bool {
        switch self {
        case .networkUnavailable, .contentGenerationFailed:
            return true
        case .indexCorrupted, .invalidQuery, .queryTooShort, .databaseError:
            return false
        }
    }
}
