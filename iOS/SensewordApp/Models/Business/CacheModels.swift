//
//  CacheModels.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  缓存管理相关数据模型
//

import Foundation

// MARK: - 缓存项模型

/// 泛型缓存项
/// 包含缓存值和元数据信息
struct CacheItem<T: Codable> {
    /// 缓存的值
    let value: T
    
    /// 创建时间
    let createdAt: Date
    
    /// 过期时间（可选）
    let expiresAt: Date?
    
    /// 访问次数
    let accessCount: Int
    
    /// 最后访问时间
    let lastAccessedAt: Date
    
    /// 数据大小（字节）
    let size: Int
}

// MARK: - 缓存配置模型

/// 缓存服务配置
struct CacheConfig {
    /// 内存缓存限制（字节）
    let memoryLimit: Int
    
    /// 磁盘缓存限制（字节）
    let diskLimit: Int
    
    /// 默认过期时间（秒）
    let defaultTTL: TimeInterval
    
    /// LRU清理阈值（0.0-1.0）
    let cleanupThreshold: Double
    
    /// 是否启用磁盘缓存
    let enableDiskCache: Bool
    
    /// 缓存目录名称
    let cacheDirectoryName: String

    /// 清理间隔（秒）
    let cleanupInterval: TimeInterval

    /// 默认配置
    static let `default` = CacheConfig(
        memoryLimit: 50 * 1024 * 1024,      // 50MB
        diskLimit: 200 * 1024 * 1024,       // 200MB
        defaultTTL: 3600,                    // 1小时
        cleanupThreshold: 0.8,               // 80%时触发清理
        enableDiskCache: true,
        cacheDirectoryName: "SenseWordCache",
        cleanupInterval: 300                 // 5分钟
    )
}

// MARK: - 缓存统计模型

/// 缓存性能统计
struct CacheMetrics {
    /// 内存缓存命中次数
    let memoryHits: Int
    
    /// 磁盘缓存命中次数
    let diskHits: Int
    
    /// 缓存未命中次数
    let misses: Int
    
    /// 当前内存使用量（字节）
    let memoryUsage: Int
    
    /// 当前磁盘使用量（字节）
    let diskUsage: Int
    
    /// 缓存项总数
    let totalItems: Int
    
    /// 统计时间
    let timestamp: Date
    
    /// 计算命中率
    var hitRate: Double {
        let totalRequests = memoryHits + diskHits + misses
        guard totalRequests > 0 else { return 0.0 }
        return Double(memoryHits + diskHits) / Double(totalRequests)
    }
    
    /// 计算内存使用率
    var memoryUsageRate: Double {
        guard CacheConfig.default.memoryLimit > 0 else { return 0.0 }
        return Double(memoryUsage) / Double(CacheConfig.default.memoryLimit)
    }
    
    /// 计算磁盘使用率
    var diskUsageRate: Double {
        guard CacheConfig.default.diskLimit > 0 else { return 0.0 }
        return Double(diskUsage) / Double(CacheConfig.default.diskLimit)
    }
}

// MARK: - 缓存操作结果

/// 缓存操作结果
enum CacheResult<T> {
    /// 成功
    case success(T)
    
    /// 未找到
    case notFound
    
    /// 已过期
    case expired
    
    /// 错误
    case error(CacheError)
}

/// 缓存错误类型
enum CacheError: Error, LocalizedError {
    /// 序列化失败
    case serializationFailed
    
    /// 反序列化失败
    case deserializationFailed
    
    /// 磁盘空间不足
    case diskSpaceInsufficient
    
    /// 内存不足
    case memoryInsufficient
    
    /// 文件系统错误
    case fileSystemError(String)
    
    /// 无效的缓存键
    case invalidKey
    
    var errorDescription: String? {
        switch self {
        case .serializationFailed:
            return "数据序列化失败"
        case .deserializationFailed:
            return "数据反序列化失败"
        case .diskSpaceInsufficient:
            return "磁盘空间不足"
        case .memoryInsufficient:
            return "内存不足"
        case .fileSystemError(let message):
            return "文件系统错误：\(message)"
        case .invalidKey:
            return "无效的缓存键"
        }
    }
}

// MARK: - 缓存策略

/// 缓存策略枚举
enum CacheStrategy {
    /// 仅内存缓存
    case memoryOnly
    
    /// 仅磁盘缓存
    case diskOnly
    
    /// 分层缓存（内存+磁盘）
    case tiered
    
    /// 写穿透（同时写入内存和磁盘）
    case writeThrough
    
    /// 写回（延迟写入磁盘）
    case writeBack
}

// MARK: - 缓存键生成器

/// 缓存键生成器
struct CacheKeyGenerator {
    /// 为单词内容生成缓存键
    static func wordContentKey(word: String, language: LanguageCode) -> String {
        return "word_content_\(language.rawValue)_\(word.lowercased())"
    }
    
    /// 为搜索建议生成缓存键
    static func searchSuggestionsKey(query: String, language: LanguageCode) -> String {
        return "search_suggestions_\(language.rawValue)_\(query.lowercased())"
    }
    
    /// 为索引数据生成缓存键
    static func indexDataKey(word: String, language: LanguageCode) -> String {
        return "index_data_\(language.rawValue)_\(word.lowercased())"
    }
    
    /// 为音频文件生成缓存键
    static func audioKey(word: String, language: LanguageCode, type: String) -> String {
        return "audio_\(type)_\(language.rawValue)_\(word.lowercased())"
    }
}

// MARK: - 扩展方法

extension CacheItem {
    /// 是否已过期
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    /// 剩余生存时间（秒）
    var remainingTTL: TimeInterval? {
        guard let expiresAt = expiresAt else { return nil }
        let remaining = expiresAt.timeIntervalSince(Date())
        return remaining > 0 ? remaining : 0
    }
    
    /// 创建新的缓存项（增加访问次数）
    func accessed() -> CacheItem<T> {
        return CacheItem(
            value: value,
            createdAt: createdAt,
            expiresAt: expiresAt,
            accessCount: accessCount + 1,
            lastAccessedAt: Date(),
            size: size
        )
    }
}

extension CacheMetrics {
    /// 创建空的统计数据
    static var empty: CacheMetrics {
        return CacheMetrics(
            memoryHits: 0,
            diskHits: 0,
            misses: 0,
            memoryUsage: 0,
            diskUsage: 0,
            totalItems: 0,
            timestamp: Date()
        )
    }
    
    /// 计算缓存命中率
    var calculatedHitRate: Double {
        let totalRequests = memoryHits + diskHits + misses
        guard totalRequests > 0 else { return 0.0 }
        return Double(memoryHits + diskHits) / Double(totalRequests)
    }
}
