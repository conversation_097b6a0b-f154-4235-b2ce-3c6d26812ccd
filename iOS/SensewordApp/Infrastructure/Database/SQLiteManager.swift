
//
//  SQLiteManager.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  SQLite数据库管理器 - 线程安全的本地数据库操作
//

import Foundation
import SQLite3

// 尝试导入模型模块来解决类型访问问题
// 如果这些 import 失败，说明存在模块依赖问题

// MARK: - 数据库操作模型

/// 数据库操作封装
struct DatabaseOperation {
    let sql: String
    let parameters: [Any]
    let operationType: OperationType
}

/// 操作类型枚举
enum OperationType {
    case insert
    case update
    case delete
    case select
}

/// SQLite错误映射
enum SQLiteError: Error, LocalizedError {
    case connectionFailed
    case queryFailed(String)
    case transactionFailed
    case databaseCorrupted
    case invalidParameters
    case tableCreationFailed
    
    var errorDescription: String? {
        switch self {
        case .connectionFailed:
            return "数据库连接失败"
        case .queryFailed(let message):
            return "查询执行失败：\(message)"
        case .transactionFailed:
            return "事务处理失败"
        case .databaseCorrupted:
            return "数据库文件损坏"
        case .invalidParameters:
            return "无效的查询参数"
        case .tableCreationFailed:
            return "数据表创建失败"
        }
    }
}

// MARK: - SQLite管理器协议

/// SQLite管理器协议定义
protocol SQLiteManagerProtocol {
    func executeQuery(_ sql: String, parameters: [Any]) async throws -> [[String: Any]]
    func executeUpdate(_ sql: String, parameters: [Any]) async throws -> Bool
    func executeBatch(_ operations: [DatabaseOperation]) async throws -> Bool
    func beginTransaction() throws
    func commitTransaction() throws
    func rollbackTransaction() throws

    // 搜索索引相关方法
    func findWordIndex(word: String, scaffoldingLanguage: LanguageCode) async throws -> [String: Any]?
    func batchUpsertWordIndex(_ items: [WordIndexItem]) async throws
    func getMaxSyncId(for language: LanguageCode) async throws -> Int
    func getMaxPage(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Int
    func searchWordSuggestions(query: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, limit: Int) async throws -> [[String: Any]]
    func getIndexStats(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> [String: Any]
    func getWordSamples(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, limit: Int) async throws -> [[String: Any]]
    func checkIntegrity() async throws -> Bool
    func cleanupOldIndexData(olderThanDays: Int) async throws
    func cleanupEmptyRecords() async throws

    // 收藏功能相关方法
    func addBookmark(word: String, language: LanguageCode, deviceId: String) async throws -> Bool
    func removeBookmark(word: String, language: LanguageCode, deviceId: String) async throws -> Bool
    func getBookmarks(deviceId: String) async throws -> [[String: Any]]
    func isBookmarked(word: String, language: LanguageCode, deviceId: String) async throws -> Bool
}

// MARK: - SQLite管理器实现

/// SQLite数据库管理器
/// 核心职责：管理本地SQLite数据库连接和生命周期，提供线程安全的数据库操作接口
/// 技术特点：支持搜索索引表的创建、查询、更新和事务处理
class SQLiteManager: SQLiteManagerProtocol {
    
    // MARK: - 私有属性
    
    /// 数据库连接指针
    private var database: OpaquePointer?
    
    /// 数据库文件路径
    private let databasePath: String
    
    /// 串行队列确保线程安全
    private let databaseQueue = DispatchQueue(label: "com.senseword.database", qos: .userInitiated)
    
    /// 读写锁
    private let readWriteLock = NSLock()
    
    /// 事务状态
    private var isInTransaction = false
    
    // MARK: - 初始化

    /// 私有初始化方法
    /// - Parameter databaseName: 数据库文件名，默认为"search.db"
    private init(databaseName: String = "search.db") throws {
        // 获取Documents目录路径
        let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)[0]
        self.databasePath = "\(documentsPath)/\(databaseName)"

        try openDatabase()
    }

    /// 创建SQLite管理器实例
    /// - Parameter databaseName: 数据库文件名，默认为"search.db"
    /// - Returns: 配置完成的SQLite管理器实例
    static func create(databaseName: String = "search.db") async throws -> SQLiteManager {
        let startTime = Date()
        NSLog("🗄️ SQLiteManager: 开始创建数据库管理器，文件名: \(databaseName)")

        do {
            let initStartTime = Date()
            let manager = try SQLiteManager(databaseName: databaseName)
            let initTime = Date().timeIntervalSince(initStartTime)
            NSLog("🗄️ SQLiteManager: 数据库连接成功，路径: \(manager.databasePath)，耗时: \(String(format: "%.3f", initTime))s")

            let configStartTime = Date()
            NSLog("🗄️ SQLiteManager: 开始配置数据库设置...")
            try await manager.configureDatabaseSettings()
            let configTime = Date().timeIntervalSince(configStartTime)
            NSLog("🗄️ SQLiteManager: 数据库设置配置完成，耗时: \(String(format: "%.3f", configTime))s")

            let tableStartTime = Date()
            NSLog("🗄️ SQLiteManager: 开始创建数据表...")
            try await manager.createTables()
            let tableTime = Date().timeIntervalSince(tableStartTime)
            NSLog("🗄️ SQLiteManager: 数据表创建完成，耗时: \(String(format: "%.3f", tableTime))s")

            let totalTime = Date().timeIntervalSince(startTime)
            NSLog("✅ SQLiteManager: 数据库管理器创建成功，总耗时: \(String(format: "%.3f", totalTime))s")
            return manager
        } catch {
            let totalTime = Date().timeIntervalSince(startTime)
            NSLog("❌ SQLiteManager: 创建失败，耗时: \(String(format: "%.3f", totalTime))s - \(error)")
            throw error
        }
    }
    
    deinit {
        closeDatabase()
    }
    
    // MARK: - 数据库连接管理
    
    /// 打开数据库连接
    private func openDatabase() throws {
        let result = sqlite3_open(databasePath, &database)

        if result != SQLITE_OK {
            _ = String(cString: sqlite3_errmsg(database))
            sqlite3_close(database)
            database = nil
            throw SQLiteError.connectionFailed
        }
    }

    /// 配置数据库设置
    private func configureDatabaseSettings() async throws {
        NSLog("🗄️ SQLiteManager: 配置外键约束...")
        _ = try await executePragma("PRAGMA foreign_keys = ON")

        NSLog("🗄️ SQLiteManager: 设置WAL模式...")
        let walResult = try await executePragma("PRAGMA journal_mode = WAL")
        NSLog("🗄️ SQLiteManager: WAL模式设置结果: \(walResult)")

        NSLog("🗄️ SQLiteManager: 设置同步模式...")
        _ = try await executePragma("PRAGMA synchronous = NORMAL")
    }
    
    /// 关闭数据库连接
    private func closeDatabase() {
        if let db = database {
            sqlite3_close(db)
            database = nil
        }
    }
    
    // MARK: - 表结构创建
    
    /// 创建必要的数据表
    private func createTables() async throws {
        NSLog("🗄️ SQLiteManager: 创建主索引表...")
        // 创建搜索索引表
        let createIndexTableSQL = """
            CREATE TABLE IF NOT EXISTS word_index (
                syncId INTEGER PRIMARY KEY,
                page INTEGER NOT NULL,
                word TEXT NOT NULL,
                learningLanguage TEXT NOT NULL,
                scaffoldingLanguage TEXT NOT NULL,
                coreDefinition TEXT NOT NULL,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """
        _ = try await executeUpdate(createIndexTableSQL, parameters: [])

        NSLog("🗄️ SQLiteManager: 创建索引优化...")



        NSLog("🗄️ SQLiteManager: 创建性能索引...")
        // 创建索引提升查询性能（使用驼峰字段名）
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_word_language_pair ON word_index(word, learningLanguage, scaffoldingLanguage)", parameters: [])
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_scaffolding_language ON word_index(scaffoldingLanguage)", parameters: [])
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_page_language_pair ON word_index(page, learningLanguage, scaffoldingLanguage)", parameters: [])
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_word_prefix ON word_index(word)", parameters: [])

        NSLog("🗄️ SQLiteManager: 创建收藏表...")
        // 创建收藏表
        let createBookmarksTableSQL = """
            CREATE TABLE IF NOT EXISTS bookmarks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                word TEXT NOT NULL,
                language TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                device_id TEXT NOT NULL DEFAULT (hex(randomblob(16))),
                UNIQUE(word, language, device_id)
            )
        """
        _ = try await executeUpdate(createBookmarksTableSQL, parameters: [])

        NSLog("🗄️ SQLiteManager: 创建收藏表索引...")
        // 创建收藏表性能索引
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_bookmarks_word_language ON bookmarks(word, language)", parameters: [])
        _ = try await executeUpdate("CREATE INDEX IF NOT EXISTS idx_bookmarks_device_id ON bookmarks(device_id)", parameters: [])
    }
    
    // MARK: - 公共接口实现
    
    /// 执行查询操作
    /// - Parameters:
    ///   - sql: SQL查询语句
    ///   - parameters: 查询参数
    /// - Returns: 查询结果数组
    func executeQuery(_ sql: String, parameters: [Any] = []) async throws -> [[String: Any]] {
        return try await performDatabaseOperation {
            return try self.performQuery(sql: sql, parameters: parameters)
        }
    }
    
    /// 执行更新操作
    /// - Parameters:
    ///   - sql: SQL更新语句
    ///   - parameters: 更新参数
    /// - Returns: 操作是否成功
    func executeUpdate(_ sql: String, parameters: [Any] = []) async throws -> Bool {
        return try await performDatabaseOperation {
            return try self.performUpdate(sql: sql, parameters: parameters)
        }
    }
    
    /// 执行批量操作
    /// - Parameter operations: 操作数组
    /// - Returns: 批量操作是否成功
    func executeBatch(_ operations: [DatabaseOperation]) async throws -> Bool {
        return try await performDatabaseOperation {
            try self.beginTransaction()
            
            do {
                for operation in operations {
                    switch operation.operationType {
                    case .insert, .update, .delete:
                        _ = try self.performUpdate(sql: operation.sql, parameters: operation.parameters)
                    case .select:
                        _ = try self.performQuery(sql: operation.sql, parameters: operation.parameters)
                    }
                }
                
                try self.commitTransaction()
                return true
            } catch {
                try self.rollbackTransaction()
                throw error
            }
        }
    }
    
    /// 开始事务
    func beginTransaction() throws {
        guard !isInTransaction else { return }
        
        let result = sqlite3_exec(database, "BEGIN TRANSACTION", nil, nil, nil)
        if result != SQLITE_OK {
            throw SQLiteError.transactionFailed
        }
        
        isInTransaction = true
    }
    
    /// 提交事务
    func commitTransaction() throws {
        guard isInTransaction else { return }
        
        let result = sqlite3_exec(database, "COMMIT", nil, nil, nil)
        if result != SQLITE_OK {
            throw SQLiteError.transactionFailed
        }
        
        isInTransaction = false
    }
    
    /// 回滚事务
    func rollbackTransaction() throws {
        guard isInTransaction else { return }
        
        let result = sqlite3_exec(database, "ROLLBACK", nil, nil, nil)
        if result != SQLITE_OK {
            throw SQLiteError.transactionFailed
        }
        
        isInTransaction = false
    }

    // MARK: - 私有实现方法

    /// 线程安全的数据库操作执行器
    /// - Parameter operation: 数据库操作闭包
    /// - Returns: 操作结果
    private func performDatabaseOperation<T>(_ operation: @escaping () throws -> T) async throws -> T {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async {
                self.readWriteLock.lock()
                defer { self.readWriteLock.unlock() }

                do {
                    let result = try operation()
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    /// 执行SQL查询
    /// - Parameters:
    ///   - sql: SQL语句
    ///   - parameters: 参数数组
    /// - Returns: 查询结果
    private func performQuery(sql: String, parameters: [Any]) throws -> [[String: Any]] {
        guard let db = database else {
            throw SQLiteError.connectionFailed
        }

        var statement: OpaquePointer?
        var results: [[String: Any]] = []

        // 准备SQL语句
        let prepareResult = sqlite3_prepare_v2(db, sql, -1, &statement, nil)
        guard prepareResult == SQLITE_OK else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw SQLiteError.queryFailed(errorMessage)
        }

        defer {
            sqlite3_finalize(statement)
        }

        // 绑定参数
        try bindParameters(statement: statement, parameters: parameters)

        // 执行查询并收集结果
        var stepResult: Int32
        repeat {
            stepResult = sqlite3_step(statement)

            if stepResult == SQLITE_ROW {
                let columnCount = sqlite3_column_count(statement)
                var row: [String: Any] = [:]

                for i in 0..<columnCount {
                    let columnName = String(cString: sqlite3_column_name(statement, i))
                    let columnValue = extractColumnValue(statement: statement, index: i)
                    row[columnName] = columnValue
                }

                results.append(row)
            } else if stepResult != SQLITE_DONE {
                // 处理错误情况
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw SQLiteError.queryFailed(errorMessage)
            }
        } while stepResult == SQLITE_ROW

        return results
    }

    /// 执行SQL更新
    /// - Parameters:
    ///   - sql: SQL语句
    ///   - parameters: 参数数组
    /// - Returns: 操作是否成功
    private func performUpdate(sql: String, parameters: [Any]) throws -> Bool {
        guard let db = database else {
            throw SQLiteError.connectionFailed
        }

        var statement: OpaquePointer?

        // 准备SQL语句
        let prepareResult = sqlite3_prepare_v2(db, sql, -1, &statement, nil)
        guard prepareResult == SQLITE_OK else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw SQLiteError.queryFailed(errorMessage)
        }

        defer {
            sqlite3_finalize(statement)
        }

        // 绑定参数
        try bindParameters(statement: statement, parameters: parameters)

        // 执行更新
        let stepResult = sqlite3_step(statement)
        guard stepResult == SQLITE_DONE else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw SQLiteError.queryFailed(errorMessage)
        }

        return true
    }

    /// 执行PRAGMA语句
    /// - Parameter sql: PRAGMA语句
    /// - Returns: 执行结果
    private func executePragma(_ sql: String) async throws -> [[String: Any]] {
        return try await withCheckedThrowingContinuation { continuation in
            databaseQueue.async {
                do {
                    let result = try self.performPragma(sql: sql)
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    /// 执行PRAGMA语句的同步版本
    /// - Parameter sql: PRAGMA语句
    /// - Returns: 查询结果
    private func performPragma(sql: String) throws -> [[String: Any]] {
        guard let db = database else {
            throw SQLiteError.connectionFailed
        }

        var statement: OpaquePointer?
        var results: [[String: Any]] = []

        // 准备SQL语句
        let prepareResult = sqlite3_prepare_v2(db, sql, -1, &statement, nil)
        guard prepareResult == SQLITE_OK else {
            let errorMessage = String(cString: sqlite3_errmsg(db))
            throw SQLiteError.queryFailed(errorMessage)
        }

        defer {
            sqlite3_finalize(statement)
        }

        // 执行PRAGMA并收集结果（如果有的话）
        var stepResult: Int32
        repeat {
            stepResult = sqlite3_step(statement)

            if stepResult == SQLITE_ROW {
                let columnCount = sqlite3_column_count(statement)
                var row: [String: Any] = [:]

                for i in 0..<columnCount {
                    let columnName = String(cString: sqlite3_column_name(statement, i))
                    let columnValue = extractColumnValue(statement: statement, index: i)
                    row[columnName] = columnValue
                }

                results.append(row)
            } else if stepResult != SQLITE_DONE {
                // 处理错误情况
                let errorMessage = String(cString: sqlite3_errmsg(db))
                throw SQLiteError.queryFailed(errorMessage)
            }
        } while stepResult == SQLITE_ROW

        return results
    }

    /// 绑定SQL参数
    /// - Parameters:
    ///   - statement: SQL语句指针
    ///   - parameters: 参数数组
    private func bindParameters(statement: OpaquePointer?, parameters: [Any]) throws {
        for (index, parameter) in parameters.enumerated() {
            let bindIndex = Int32(index + 1)

            switch parameter {
            case let stringValue as String:
                // 使用 SQLITE_TRANSIENT 确保 SQLite 复制字符串数据，避免指针失效
                let SQLITE_TRANSIENT = unsafeBitCast(OpaquePointer(bitPattern: -1), to: sqlite3_destructor_type.self)
                sqlite3_bind_text(statement, bindIndex, stringValue, -1, SQLITE_TRANSIENT)
            case let intValue as Int:
                sqlite3_bind_int64(statement, bindIndex, Int64(intValue))
            case let doubleValue as Double:
                sqlite3_bind_double(statement, bindIndex, doubleValue)
            case let boolValue as Bool:
                sqlite3_bind_int(statement, bindIndex, boolValue ? 1 : 0)
            case is NSNull:
                sqlite3_bind_null(statement, bindIndex)
            default:
                throw SQLiteError.invalidParameters
            }
        }
    }

    /// 提取列值
    /// - Parameters:
    ///   - statement: SQL语句指针
    ///   - index: 列索引
    /// - Returns: 列值
    private func extractColumnValue(statement: OpaquePointer?, index: Int32) -> Any {
        let columnType = sqlite3_column_type(statement, index)

        switch columnType {
        case SQLITE_TEXT:
            return String(cString: sqlite3_column_text(statement, index))
        case SQLITE_INTEGER:
            return Int(sqlite3_column_int64(statement, index))
        case SQLITE_FLOAT:
            return sqlite3_column_double(statement, index)
        case SQLITE_NULL:
            return NSNull()
        default:
            return NSNull()
        }
    }
}

// MARK: - SQLite管理器扩展

extension SQLiteManager {

    /// 获取数据库版本信息
    func getDatabaseVersion() async throws -> Int {
        let results = try await executeQuery("PRAGMA user_version", parameters: [])
        return results.first?["user_version"] as? Int ?? 0
    }

    /// 设置数据库版本
    /// - Parameter version: 版本号
    func setDatabaseVersion(_ version: Int) async throws {
        _ = try await executeUpdate("PRAGMA user_version = \(version)", parameters: [])
    }

    /// 获取数据库文件大小
    func getDatabaseSize() -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: databasePath)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }

    /// 清理数据库（VACUUM操作）
    func vacuum() async throws {
        _ = try await executeUpdate("VACUUM", parameters: [])
    }

    /// 检查数据库完整性
    func checkIntegrity() async throws -> Bool {
        let results = try await executeQuery("PRAGMA integrity_check", parameters: [])
        let firstResult = results.first?["integrity_check"] as? String
        return firstResult == "ok"
    }

    /// 删除数据库文件（用于开发调试）
    /// 注意：这会永久删除所有本地数据
    func deleteDatabaseFile() throws {
        // 先关闭数据库连接
        closeDatabase()

        // 删除数据库文件
        if FileManager.default.fileExists(atPath: databasePath) {
            try FileManager.default.removeItem(atPath: databasePath)
            NSLog("🗑️ SQLiteManager: 数据库文件已删除: \(databasePath)")
        }

        // 删除相关的WAL和SHM文件
        let walPath = databasePath + "-wal"
        let shmPath = databasePath + "-shm"

        if FileManager.default.fileExists(atPath: walPath) {
            try FileManager.default.removeItem(atPath: walPath)
            NSLog("🗑️ SQLiteManager: WAL文件已删除: \(walPath)")
        }

        if FileManager.default.fileExists(atPath: shmPath) {
            try FileManager.default.removeItem(atPath: shmPath)
            NSLog("🗑️ SQLiteManager: SHM文件已删除: \(shmPath)")
        }
    }
}

// MARK: - 搜索索引专用扩展

extension SQLiteManager {

    /// 插入或更新单词索引
    /// - Parameter indexItem: 单词索引项
    func upsertWordIndex(_ indexItem: WordIndexItem) async throws {
        let sql = """
            INSERT OR REPLACE INTO word_index
            (syncId, page, word, learningLanguage, scaffoldingLanguage, coreDefinition, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """

        let parameters: [Any] = [
            indexItem.syncId,
            indexItem.page,
            indexItem.word,
            indexItem.learningLanguage.rawValue,
            indexItem.scaffoldingLanguage.rawValue,
            indexItem.coreDefinition
        ]

        _ = try await executeUpdate(sql, parameters: parameters)
    }

    /// 批量插入或更新单词索引
    /// - Parameter indexItems: 单词索引项数组
    func batchUpsertWordIndex(_ indexItems: [WordIndexItem]) async throws {
        NSLog("🔧 SQLiteManager: batchUpsertWordIndex 开始 - 处理 %d 条记录", indexItems.count)

        // 打印前几个数据项用于调试
        for (index, item) in indexItems.prefix(3).enumerated() {
            NSLog("🔧 SQLiteManager: 输入数据 %d: syncId=%d, page=%d, word='%@', learningLang='%@', scaffoldingLang='%@', definition='%@'",
                  index + 1, item.syncId, item.page, item.word, item.learningLanguage.rawValue, item.scaffoldingLanguage.rawValue, item.coreDefinition)
        }

        let operations = indexItems.map { indexItem -> DatabaseOperation in
            let sql = """
                INSERT OR REPLACE INTO word_index
                (syncId, page, word, learningLanguage, scaffoldingLanguage, coreDefinition, updatedAt)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """

            let parameters: [Any] = [
                indexItem.syncId,
                indexItem.page,
                indexItem.word,
                indexItem.learningLanguage.rawValue,
                indexItem.scaffoldingLanguage.rawValue,
                indexItem.coreDefinition
            ]

            // 调试第一个操作的参数
            if indexItem.syncId == indexItems.first?.syncId {
                NSLog("🔧 SQLiteManager: 第一个操作 SQL: %@", sql)
                NSLog("🔧 SQLiteManager: 第一个操作参数: %@", parameters as NSArray)
            }

            return DatabaseOperation(sql: sql, parameters: parameters, operationType: .insert)
        }

        NSLog("🔧 SQLiteManager: 开始执行批量操作...")
        _ = try await executeBatch(operations)
        NSLog("🔧 SQLiteManager: 批量操作完成")
    }

    /// 直接搜索单词建议（基于word字段）
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - language: 语言代码
    ///   - limit: 结果数量限制
    /// - Returns: 搜索结果
    func searchWordSuggestions(query: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, limit: Int = 10) async throws -> [[String: Any]] {
        // 验证查询字符串
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else {
            NSLog("⚠️ SQLiteManager: 搜索查询为空，返回空结果")
            return []
        }

        // 验证查询长度（避免过短或过长的查询）
        guard trimmedQuery.count >= 1 && trimmedQuery.count <= 50 else {
            NSLog("⚠️ SQLiteManager: 搜索查询长度无效: \(trimmedQuery.count)")
            return []
        }

        let sql = """
            SELECT word, coreDefinition, learningLanguage, scaffoldingLanguage,
                   LENGTH(word) as word_length
            FROM word_index
            WHERE LOWER(word) LIKE LOWER(?) AND learningLanguage = ? AND scaffoldingLanguage = ?
            ORDER BY LENGTH(word) ASC, word ASC
            LIMIT ?
        """

        // 构建LIKE查询字符串，支持前缀匹配
        let percentChar = "%"  // 明确定义百分号字符
        let likePattern = trimmedQuery + percentChar  // 使用字符串连接

        let queryParameters: [Any] = [likePattern, learningLanguage.rawValue, scaffoldingLanguage.rawValue, limit]

        NSLog(" SQLiteManager: 执行单词搜索")
        NSLog("📝 SQLiteManager: trimmedQuery = %@", trimmedQuery)
        NSLog("📝 SQLiteManager: likePattern = %@", likePattern)
        NSLog("📝 SQLiteManager: likePattern.count = %d", likePattern.count)
        NSLog("📝 SQLiteManager: 学习语言: %@, 脚手架语言: %@", learningLanguage.rawValue, scaffoldingLanguage.rawValue)
        NSLog("📋 SQLiteManager: SQL查询: %@", sql)
        NSLog("📝 SQLiteManager: 参数: %@", queryParameters as NSArray)
        NSLog("🔍 SQLiteManager: 第一个参数详情: %@, 类型: %@", queryParameters[0] as! CVarArg, String(describing: type(of: queryParameters[0])))

        // 验证参数是否包含%
        if let firstParam = queryParameters[0] as? String {
            NSLog("🔧 SQLiteManager: 第一个参数包含%%: %@", firstParam.contains("%") ? "true" : "false")
        }

        let results = try await executeQuery(sql, parameters: queryParameters)
        NSLog("📊 SQLiteManager: 搜索结果数量: \(results.count)")

        // 打印前几个结果用于调试
        for (index, result) in results.prefix(3).enumerated() {
            if let word = result["word"] as? String,
               let definition = result["coreDefinition"] as? String {
                NSLog("🎯 结果 \(index + 1): '\(word)' - \(definition)")
            }
        }

        return results
    }

    /// 精确查找单词索引
    /// - Parameters:
    ///   - word: 单词
    ///   - scaffoldingLanguage: 脚手架语言代码
    /// - Returns: 单词索引项（如果存在）
    func findWordIndex(word: String, scaffoldingLanguage: LanguageCode) async throws -> [String: Any]? {
        let sql = """
            SELECT syncId, page, word, learningLanguage, scaffoldingLanguage, coreDefinition
            FROM word_index
            WHERE word = ? AND scaffoldingLanguage = ?
            LIMIT 1
        """

        let parameters: [Any] = [word, scaffoldingLanguage.rawValue]
        let results = try await executeQuery(sql, parameters: parameters)

        return results.first
    }

    /// 获取最大同步ID
    /// - Parameter language: 语言代码
    /// - Returns: 最大同步ID
    func getMaxSyncId(for language: LanguageCode) async throws -> Int {
        let sql = """
            SELECT COALESCE(MAX(sync_id), 0) as max_sync_id
            FROM word_index
            WHERE language = ?
        """

        let parameters: [Any] = [language.rawValue]
        let results = try await executeQuery(sql, parameters: parameters)

        return results.first?["max_sync_id"] as? Int ?? 0
    }

    /// 获取索引统计信息
    /// - Parameters:
    ///   - learningLanguage: 学习语言代码
    ///   - scaffoldingLanguage: 脚手架语言代码
    /// - Returns: 统计信息
    func getIndexStats(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> [String: Any] {
        let sql = """
            SELECT
                COUNT(*) as total_words,
                MIN(syncId) as min_sync_id,
                MAX(syncId) as max_sync_id,
                MAX(updatedAt) as last_updated
            FROM word_index
            WHERE learningLanguage = ? AND scaffoldingLanguage = ?
        """

        let parameters: [Any] = [learningLanguage.rawValue, scaffoldingLanguage.rawValue]
        let results = try await executeQuery(sql, parameters: parameters)

        return results.first ?? [:]
    }

    /// 调试方法：获取数据库中的前几个单词样本
    /// - Parameters:
    ///   - learningLanguage: 学习语言代码
    ///   - scaffoldingLanguage: 脚手架语言代码
    ///   - limit: 样本数量限制
    /// - Returns: 单词样本
    func getWordSamples(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, limit: Int = 10) async throws -> [[String: Any]] {
        let sql = """
            SELECT word, coreDefinition, syncId
            FROM word_index
            WHERE learningLanguage = ? AND scaffoldingLanguage = ? AND word != '' AND coreDefinition != ''
            ORDER BY updatedAt DESC
            LIMIT ?
        """

        let parameters: [Any] = [learningLanguage.rawValue, scaffoldingLanguage.rawValue, limit]
        NSLog("🔍 SQLiteManager: 获取单词样本 - 学习语言: \(learningLanguage.rawValue), 脚手架语言: \(scaffoldingLanguage.rawValue), 限制: \(limit)")

        let results = try await executeQuery(sql, parameters: parameters)

        // 打印样本数据用于调试
        for (index, result) in results.enumerated() {
            if let word = result["word"] as? String,
               let definition = result["coreDefinition"] as? String,
               let syncId = result["syncId"] as? Int {
                NSLog("📝 样本 \(index + 1): '\(word)' - \(definition) (syncId: \(syncId))")
            }
        }

        return results
    }

    /// 清理过期索引数据
    /// - Parameter olderThanDays: 保留天数
    func cleanupOldIndexData(olderThanDays: Int) async throws {
        let sql = """
            DELETE FROM word_index
            WHERE updatedAt < datetime('now', '-\(olderThanDays) days')
        """

        _ = try await executeUpdate(sql, parameters: [])
    }



    /// 清空所有脏数据（空字符串记录）
    func cleanupEmptyRecords() async throws {
        NSLog("🧹 SQLiteManager: 开始清理空记录...")

        // 删除所有空字符串记录
        let sql = """
            DELETE FROM word_index
            WHERE word = '' OR coreDefinition = '' OR word IS NULL OR coreDefinition IS NULL
        """

        _ = try await executeUpdate(sql, parameters: [])
        NSLog("🧹 SQLiteManager: 空记录清理完成")
    }

    // MARK: - 收藏功能实现

    /// 添加收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    ///   - deviceId: 设备ID
    /// - Returns: 操作是否成功
    func addBookmark(word: String, language: LanguageCode, deviceId: String) async throws -> Bool {
        NSLog("🔖 SQLiteManager: 添加收藏 - 单词: \(word), 语言: \(language.rawValue), 设备: \(deviceId)")

        let sql = """
            INSERT OR IGNORE INTO bookmarks (word, language, device_id)
            VALUES (?, ?, ?)
        """

        let parameters: [Any] = [word, language.rawValue, deviceId]
        return try await executeUpdate(sql, parameters: parameters)
    }

    /// 移除收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    ///   - deviceId: 设备ID
    /// - Returns: 操作是否成功
    func removeBookmark(word: String, language: LanguageCode, deviceId: String) async throws -> Bool {
        NSLog("🔖 SQLiteManager: 移除收藏 - 单词: \(word), 语言: \(language.rawValue), 设备: \(deviceId)")

        let sql = """
            DELETE FROM bookmarks
            WHERE word = ? AND language = ? AND device_id = ?
        """

        let parameters: [Any] = [word, language.rawValue, deviceId]
        return try await executeUpdate(sql, parameters: parameters)
    }

    /// 获取收藏列表
    /// - Parameter deviceId: 设备ID
    /// - Returns: 收藏列表
    func getBookmarks(deviceId: String) async throws -> [[String: Any]] {
        NSLog("🔖 SQLiteManager: 获取收藏列表 - 设备: \(deviceId)")

        let sql = """
            SELECT id, word, language, created_at, device_id
            FROM bookmarks
            WHERE device_id = ?
            ORDER BY created_at DESC
        """

        let parameters: [Any] = [deviceId]
        let results = try await executeQuery(sql, parameters: parameters)

        NSLog("🔖 SQLiteManager: 找到 \(results.count) 个收藏")
        return results
    }

    /// 检查是否已收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    ///   - deviceId: 设备ID
    /// - Returns: 是否已收藏
    func isBookmarked(word: String, language: LanguageCode, deviceId: String) async throws -> Bool {
        let sql = """
            SELECT 1 FROM bookmarks
            WHERE word = ? AND language = ? AND device_id = ?
            LIMIT 1
        """

        let parameters: [Any] = [word, language.rawValue, deviceId]
        let results = try await executeQuery(sql, parameters: parameters)

        let isBookmarked = !results.isEmpty
        NSLog("🔖 SQLiteManager: 收藏状态检查 - 单词: \(word), 已收藏: \(isBookmarked)")

        return isBookmarked
    }

    /// 获取指定语言对的最大页码
    /// - Parameters:
    ///   - learningLang: 学习语言代码
    ///   - scaffoldingLang: 脚手架语言代码
    /// - Returns: 最大页码
    func getMaxPage(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Int {
        let sql = """
            SELECT COALESCE(MAX(page), 0) as maxPage
            FROM word_index
            WHERE learningLanguage = ? AND scaffoldingLanguage = ?
        """

        let parameters: [Any] = [learningLang.rawValue, scaffoldingLang.rawValue]
        let results = try await executeQuery(sql, parameters: parameters)

        return results.first?["maxPage"] as? Int ?? 0
    }
}
