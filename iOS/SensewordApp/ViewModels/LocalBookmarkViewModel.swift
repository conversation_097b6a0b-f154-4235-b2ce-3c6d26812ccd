//
//  LocalBookmarkViewModel.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-07-27.
//  Generated from SenseWord后端无状态化重构任务
//

import Foundation
import SwiftUI

/// 本地收藏ViewModel
/// 核心职责：为UI层提供收藏功能的数据绑定和状态管理
@MainActor
class LocalBookmarkViewModel: ObservableObject {
    
    // MARK: - Published属性
    
    /// 当前单词是否已收藏
    @Published var isBookmarked: Bool = false
    
    /// 收藏列表
    @Published var bookmarks: [LocalBookmarkItem] = []
    
    /// 加载状态
    @Published var isLoading: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String?
    
    /// 操作结果消息
    @Published var operationMessage: String?
    
    // MARK: - 私有属性
    
    /// 本地收藏服务
    private let localBookmarkService: LocalBookmarkServiceProtocol
    
    /// 当前单词
    private var currentWord: String?
    
    /// 当前语言
    private var currentLanguage: LanguageCode?
    
    // MARK: - 初始化
    
    /// 初始化ViewModel
    /// - Parameter localBookmarkService: 本地收藏服务
    init(localBookmarkService: LocalBookmarkServiceProtocol) {
        self.localBookmarkService = localBookmarkService
        NSLog("🔖 LocalBookmarkViewModel: 初始化完成")
    }
    
    // MARK: - 公共方法
    
    /// 切换收藏状态
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    func toggleBookmark(word: String, language: LanguageCode) async {
        NSLog("🔖 LocalBookmarkViewModel: 切换收藏状态 - 单词: \(word), 语言: \(language.rawValue)")
        
        isLoading = true
        errorMessage = nil
        operationMessage = nil
        
        do {
            let result = try await localBookmarkService.toggleBookmark(word: word, language: language)
            
            // 更新UI状态
            isBookmarked = result.bookmarkStatus.isBookmarked
            operationMessage = result.message
            
            // 如果当前正在查看这个单词，更新状态
            if currentWord == word && currentLanguage == language {
                // 状态已经更新
            }
            
            // 刷新收藏列表
            await loadBookmarks()
            
            NSLog("🔖 LocalBookmarkViewModel: 收藏状态切换成功 - 新状态: \(isBookmarked)")
            
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 收藏状态切换失败 - \(error)")
            errorMessage = "收藏操作失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 检查收藏状态
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    func checkBookmarkStatus(word: String, language: LanguageCode) async {
        NSLog("🔖 LocalBookmarkViewModel: 检查收藏状态 - 单词: \(word), 语言: \(language.rawValue)")
        
        currentWord = word
        currentLanguage = language
        
        do {
            let bookmarked = try await localBookmarkService.isBookmarked(word: word, language: language)
            isBookmarked = bookmarked
            
            NSLog("🔖 LocalBookmarkViewModel: 收藏状态检查完成 - 状态: \(bookmarked)")
            
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 收藏状态检查失败 - \(error)")
            errorMessage = "检查收藏状态失败: \(error.localizedDescription)"
            isBookmarked = false
        }
    }
    
    /// 加载收藏列表
    func loadBookmarks() async {
        NSLog("🔖 LocalBookmarkViewModel: 加载收藏列表")
        
        isLoading = true
        errorMessage = nil
        
        do {
            let loadedBookmarks = try await localBookmarkService.getBookmarks()
            bookmarks = loadedBookmarks
            
            NSLog("🔖 LocalBookmarkViewModel: 收藏列表加载完成 - \(bookmarks.count) 个收藏")
            
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 收藏列表加载失败 - \(error)")
            errorMessage = "加载收藏列表失败: \(error.localizedDescription)"
            bookmarks = []
        }
        
        isLoading = false
    }
    
    /// 添加收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    func addBookmark(word: String, language: LanguageCode) async {
        NSLog("🔖 LocalBookmarkViewModel: 添加收藏 - 单词: \(word), 语言: \(language.rawValue)")
        
        isLoading = true
        errorMessage = nil
        operationMessage = nil
        
        do {
            let result = try await localBookmarkService.addBookmark(word: word, language: language)
            
            if result.success {
                isBookmarked = true
                operationMessage = result.message
                await loadBookmarks()
                NSLog("🔖 LocalBookmarkViewModel: 收藏添加成功")
            } else {
                errorMessage = result.message
                NSLog("🔖 LocalBookmarkViewModel: 收藏添加失败 - \(result.message)")
            }
            
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 收藏添加异常 - \(error)")
            errorMessage = "添加收藏失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 移除收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    func removeBookmark(word: String, language: LanguageCode) async {
        NSLog("🔖 LocalBookmarkViewModel: 移除收藏 - 单词: \(word), 语言: \(language.rawValue)")
        
        isLoading = true
        errorMessage = nil
        operationMessage = nil
        
        do {
            let result = try await localBookmarkService.removeBookmark(word: word, language: language)
            
            if result.success {
                isBookmarked = false
                operationMessage = result.message
                await loadBookmarks()
                NSLog("🔖 LocalBookmarkViewModel: 收藏移除成功")
            } else {
                errorMessage = result.message
                NSLog("🔖 LocalBookmarkViewModel: 收藏移除失败 - \(result.message)")
            }
            
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 收藏移除异常 - \(error)")
            errorMessage = "移除收藏失败: \(error.localizedDescription)"
        }
        
        isLoading = false
    }
    
    /// 清除错误消息
    func clearErrorMessage() {
        errorMessage = nil
    }
    
    /// 清除操作消息
    func clearOperationMessage() {
        operationMessage = nil
    }
    
    /// 重置状态
    func resetState() {
        isBookmarked = false
        errorMessage = nil
        operationMessage = nil
        currentWord = nil
        currentLanguage = nil
    }
}

// MARK: - 扩展功能

extension LocalBookmarkViewModel {
    
    /// 获取收藏统计信息
    func getBookmarkStats() async -> [String: Any]? {
        do {
            return try await localBookmarkService.getBookmarkStats()
        } catch {
            NSLog("🔖 LocalBookmarkViewModel: 获取收藏统计失败 - \(error)")
            errorMessage = "获取统计信息失败: \(error.localizedDescription)"
            return nil
        }
    }
    
    /// 按语言筛选收藏
    /// - Parameter language: 语言代码
    /// - Returns: 指定语言的收藏列表
    func getBookmarksByLanguage(_ language: LanguageCode) -> [LocalBookmarkItem] {
        return bookmarks.filter { $0.language == language }
    }
    
    /// 搜索收藏
    /// - Parameter query: 搜索关键词
    /// - Returns: 匹配的收藏列表
    func searchBookmarks(query: String) -> [LocalBookmarkItem] {
        guard !query.isEmpty else { return bookmarks }
        
        let lowercaseQuery = query.lowercased()
        return bookmarks.filter { bookmark in
            bookmark.word.lowercased().contains(lowercaseQuery)
        }
    }
    
    /// 检查是否有收藏
    var hasBookmarks: Bool {
        return !bookmarks.isEmpty
    }
    
    /// 收藏总数
    var bookmarkCount: Int {
        return bookmarks.count
    }
}
