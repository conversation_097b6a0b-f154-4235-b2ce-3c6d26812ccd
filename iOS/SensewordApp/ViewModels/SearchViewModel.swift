//
//  SearchViewModel.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  搜索视图模型 - 响应式搜索状态管理和用户交互协调
//

import Foundation
import SwiftUI
import Combine

// MARK: - 搜索状态枚举
// SearchState 已在 SearchModels.swift 中定义

// ContentLoadingState 已在 SearchModels.swift 中定义

// MARK: - 搜索视图模型

/// 搜索视图模型
/// 核心职责：管理搜索界面状态，协调用户交互和业务逻辑，提供响应式数据绑定
/// 技术特点：支持防抖搜索、状态管理、错误处理、性能监控
@MainActor
class SearchViewModel: ObservableObject {
    
    // MARK: - 发布属性

    /// 搜索查询文本
    @Published var searchQuery: String = "" {
        didSet {
            handleSearchQueryChange()
        }
    }

    /// 搜索建议列表
    @Published var suggestions: [SearchSuggestion] = []

    /// 搜索状态
    @Published var searchState: SearchState = .idle

    /// 内容加载状态
    @Published var contentLoadingState: ContentLoadingState = .idle

    /// 是否显示搜索建议
    @Published var showSuggestions: Bool = false

    /// 当前选中的单词
    @Published var selectedWord: String? = nil

    /// 错误消息
    @Published var errorMessage: String? = nil

    /// 是否显示错误提示
    @Published var showError: Bool = false

    // MARK: - 覆盖层相关属性

    /// 是否激活搜索覆盖层
    @Published var isSearchActive: Bool = false

    /// 搜索按钮缩放比例
    @Published var triggerScale: CGFloat = 1.0

    /// 手势进度（0.0 - 1.0）
    @Published var gestureProgress: CGFloat = 0.0
    
    // MARK: - 私有属性
    
    /// 搜索服务
    private let searchService: SearchServiceProtocol
    
    /// 防抖定时器
    private var debounceTimer: Timer?
    
    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()
    
    /// 当前搜索任务
    private var currentSearchTask: Task<Void, Never>?
    
    /// 当前内容加载任务
    private var currentContentTask: Task<Void, Never>?
    
    /// 配置
    private let debounceDelay: TimeInterval = 0.3
    private let maxSuggestions: Int = 10

    /// 智能预热相关属性
    private var isWarmupCompleted = false
    private var warmupTask: Task<Void, Never>?
    
    // MARK: - 初始化
    
    /// 初始化搜索视图模型
    /// - Parameter searchService: 搜索服务
    init(searchService: SearchServiceProtocol) {
        self.searchService = searchService

        setupBindings()
        // 移除 loadInitialData() 调用，避免在初始化时阻塞主线程
        // 预热操作将在搜索真正激活时进行
    }
    
    // MARK: - 公共方法
    
    /// 获取搜索建议（用于自动完成）
    /// - Parameter query: 搜索查询
    func getSuggestions(query: String? = nil) {
        let searchText = query ?? searchQuery.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !searchText.isEmpty else {
            clearSuggestions()
            return
        }
        
        // 取消之前的搜索任务
        currentSearchTask?.cancel()
        
        // 更新状态
        searchState = .searching
        showSuggestions = true
        
        // 执行搜索
        currentSearchTask = Task {
            do {
                let response = await searchService.getSuggestions(query: searchText)
                
                // 检查任务是否被取消
                guard !Task.isCancelled else { return }
                
                // 更新UI
                await MainActor.run {
                    self.suggestions = response.data
                    self.searchState = .showingResults(response.data)

                    // 如果没有结果，显示提示
                    if response.data.isEmpty {
                        self.showEmptyState()
                    }
                }
                
            } catch {
                // 检查任务是否被取消
                guard !Task.isCancelled else { return }
                
                await MainActor.run {
                    self.handleSearchError(error)
                }
            }
        }
    }

    /// 执行单词搜索（用于回车确认搜索）
    /// - Parameter query: 搜索查询
    func performSearch(query: String? = nil) {
        let searchText = query ?? searchQuery.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !searchText.isEmpty else {
            return
        }

        NSLog("🔍 SearchViewModel: 执行直接搜索 - \(searchText)")

        // 隐藏搜索建议，直接加载单词内容
        showSuggestions = false
        selectedWord = searchText

        // 直接加载单词内容
        loadWordContent(word: searchText)
    }

    /// 执行单词搜索并立即关闭搜索面板（用于键盘确认按钮）
    /// - Parameter query: 搜索查询
    func performSearchAndDismiss(query: String? = nil) {
        let searchText = query ?? searchQuery.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !searchText.isEmpty else {
            return
        }

        NSLog("🔍 SearchViewModel: 执行搜索并关闭面板 - \(searchText)")

        // 先执行搜索
        performSearch(query: searchText)

        // 立即关闭搜索面板
        dismissSearch()

        // 通知主界面更新内容
        NotificationCenter.default.post(
            name: .searchResultSelected,
            object: searchText
        )

        // 在后台异步更新索引（搜索后更新）
        triggerBackgroundIndexUpdate()
    }

    /// 选择搜索建议
    /// - Parameter suggestion: 选中的建议
    func selectSuggestion(_ suggestion: SearchSuggestion) {
        selectedWord = suggestion.word
        searchQuery = suggestion.word
        showSuggestions = false
        
        // 加载完整内容
        loadWordContent(word: suggestion.word)
    }
    
    /// 加载单词内容
    /// - Parameter word: 单词
    func loadWordContent(word: String) {
        // 取消之前的加载任务
        currentContentTask?.cancel()

        // 更新状态
        contentLoadingState = .loading

        // 获取用户偏好语言
        let userLanguage = SettingsService.shared.getCurrentPreferredLanguage()

        // 执行加载
        currentContentTask = Task {
            do {
                let response = try await searchService.getWordContent(
                    word: word,
                    language: userLanguage
                )

                // 检查任务是否被取消
                guard !Task.isCancelled else { return }

                await MainActor.run {
                    // response 本身就是 WordDefinitionResponse
                    self.contentLoadingState = .loaded(response)
                }

            } catch {
                // 检查任务是否被取消
                guard !Task.isCancelled else { return }

                await MainActor.run {
                    self.contentLoadingState = .error(error.localizedDescription)
                }
            }
        }
    }
    
    /// 清除搜索
    func clearSearch() {
        // 取消所有任务
        currentSearchTask?.cancel()
        currentContentTask?.cancel()
        
        // 重置状态
        searchQuery = ""
        suggestions = []
        searchState = .idle
        contentLoadingState = .idle
        showSuggestions = false
        selectedWord = nil
        errorMessage = nil
        showError = false
    }

    /// 重试获取搜索建议
    func retrySearch() {
        guard !searchQuery.isEmpty else { return }
        getSuggestions()
    }
    
    /// 预加载内容
    /// - Parameter words: 单词列表
    func preloadContent(words: [String]) {
        Task {
            let userLanguage = SettingsService.shared.getCurrentPreferredLanguage()
            await searchService.preloadContent(words: words, language: userLanguage)
        }
    }

    // MARK: - 覆盖层控制方法

    /// 激活搜索覆盖层
    /// - Parameter gestureProgress: 手势进度
    func activateSearch(gestureProgress: CGFloat) {
        self.gestureProgress = gestureProgress

        // 根据手势进度更新按钮缩放
        let scale = 1.0 + (gestureProgress * 0.2) // 最大缩放到1.2倍
        triggerScale = scale

        // 移除预热操作，避免在手势过程中执行任何耗时操作

        // 当手势进度达到阈值时激活搜索
        if gestureProgress >= 0.6 && !isSearchActive {
            withAnimation(.spring(response: 0.8, dampingFraction: 0.7)) {
                isSearchActive = true
            }
        }
    }

    /// 取消搜索激活
    func cancelSearchActivation() {
        withAnimation(.easeOut(duration: 0.2)) {
            gestureProgress = 0.0
            triggerScale = 1.0
        }
    }

    /// 关闭搜索覆盖层
    func dismissSearch() {
        withAnimation(.easeInOut(duration: 0.6)) {
            isSearchActive = false
            gestureProgress = 0.0
            triggerScale = 1.0
        }

        // 清理搜索状态
        clearSearch()

        // 在后台异步更新索引
        triggerBackgroundIndexUpdate()
    }

    /// 选择搜索结果并关闭覆盖层
    /// - Parameter suggestion: 选中的建议
    func selectSuggestionAndDismiss(_ suggestion: SearchSuggestion) {
        // 先选择建议
        selectSuggestion(suggestion)

        // 然后关闭覆盖层
        dismissSearch()

        // 通知主界面更新内容
        NotificationCenter.default.post(
            name: .searchResultSelected,
            object: suggestion.word
        )

        // 在后台异步更新索引（选择建议后更新）
        triggerBackgroundIndexUpdate()
    }
}

// MARK: - 私有方法

private extension SearchViewModel {

    /// 设置数据绑定
    func setupBindings() {
        // 监听搜索查询变化
        $searchQuery
            .debounce(for: .seconds(debounceDelay), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] query in
                self?.handleDebouncedSearch(query: query)
            }
            .store(in: &cancellables)

        // 监听错误状态
        $errorMessage
            .map { $0 != nil }
            .assign(to: \.showError, on: self)
            .store(in: &cancellables)
    }

    /// 加载初始数据
    func loadInitialData() {
        Task {
            // 预热搜索服务
            await warmupSearchService()
        }
    }

    /// 处理搜索查询变化
    func handleSearchQueryChange() {
        // 如果查询为空，显示历史记录
        if searchQuery.isEmpty {
            clearSuggestions()
        }

        // 清除错误状态
        clearError()
    }

    /// 处理防抖搜索
    /// - Parameter query: 搜索查询
    func handleDebouncedSearch(query: String) {
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)

        if trimmedQuery.isEmpty {
            clearSuggestions()
        } else if trimmedQuery.count >= 2 { // 最小查询长度
            getSuggestions(query: trimmedQuery)
        }
    }

    /// 清除搜索建议
    func clearSuggestions() {
        suggestions = []
        searchState = .idle
        showSuggestions = false
    }

    /// 显示空状态
    func showEmptyState() {
        // 可以在这里添加空状态的处理逻辑
        // 比如显示"没有找到相关结果"的提示
    }

    /// 处理搜索错误
    /// - Parameter error: 错误对象
    func handleSearchError(_ error: Error) {
        searchState = .error(.networkUnavailable)
        errorMessage = "搜索失败：\(error.localizedDescription)"
        suggestions = []
        showSuggestions = false
    }

    /// 清除错误状态
    func clearError() {
        errorMessage = nil
        showError = false

        if case .error = searchState {
            searchState = .idle
        }
    }

    /// 预热搜索服务
    func warmupSearchService() async {
        await searchService.warmupService()
    }

    /// 智能预热搜索服务（避免重复预热）
    func warmupSearchServiceIfNeeded() async {
        // 如果已经预热完成或正在预热，直接返回
        guard !isWarmupCompleted else { return }

        // 如果已有预热任务在进行，等待其完成
        if let existingTask = warmupTask {
            await existingTask.value
            return
        }

        // 创建新的预热任务
        warmupTask = Task {
            NSLog("🔥 SearchViewModel: 开始异步预热搜索服务...")
            await searchService.warmupService()
            await MainActor.run {
                self.isWarmupCompleted = true
                self.warmupTask = nil
            }
            NSLog("✅ SearchViewModel: 搜索服务预热完成")
        }

        await warmupTask?.value
    }
}

// MARK: - 计算属性

extension SearchViewModel {

    /// 是否正在搜索
    var isSearching: Bool {
        if case .searching = searchState {
            return true
        }
        return false
    }

    /// 是否有搜索结果
    var hasResults: Bool {
        return !suggestions.isEmpty
    }

    /// 是否显示空状态
    var shouldShowEmptyState: Bool {
        return !isSearching && !hasResults && !searchQuery.isEmpty && searchQuery.count >= 2
    }

    /// 是否显示加载指示器
    var shouldShowLoadingIndicator: Bool {
        // 只在真正搜索中或内容加载中时显示加载指示器
        // 搜索失败或错误状态时不显示
        let isActivelySearching = isSearching && !searchQuery.isEmpty
        let isLoadingContent = contentLoadingState == .loading

        return isActivelySearching || isLoadingContent
    }

    /// 搜索结果数量文本
    var resultCountText: String {
        let count = suggestions.count
        if count == 0 {
            return "无搜索结果"
        } else if count == 1 {
            return "1个结果"
        } else {
            return "\(count)个结果"
        }
    }

    /// 当前内容
    var currentContent: WordDefinitionResponse? {
        if case .loaded(let content) = contentLoadingState {
            return content
        }
        return nil
    }

    /// 内容加载错误消息
    var contentErrorMessage: String? {
        if case .error(let message) = contentLoadingState {
            return message
        }
        return nil
    }

    /// 搜索占位符文本（动态）
    var searchPlaceholder: String {
        return "使用搜索用深思语境练单词"
    }

    /// 是否应该显示搜索按钮
    var shouldShowSearchButton: Bool {
        return !isSearchActive
    }

    /// 搜索面板的背景模糊强度
    var backgroundBlurRadius: CGFloat {
        return isSearchActive ? 20.0 : 0.0
    }
}

// MARK: - 搜索视图模型扩展

extension SearchViewModel {

    /// 获取性能统计
    /// - Returns: 性能统计信息
    func getPerformanceStats() async -> [String: Any] {
        return await searchService.getPerformanceStats()
    }

    /// 获取搜索服务健康状态
    func getHealthStatus() async -> [String: Any] {
        return await searchService.getHealthStatus()
    }

    /// 运行诊断
    func runDiagnostics() async -> [String: Any] {
        return await searchService.runDiagnostics()
    }

    /// 设置搜索策略
    /// - Parameter strategy: 搜索策略
    func setSearchStrategy(_ strategy: SearchStrategy) {
        searchService.setSearchStrategy(strategy)
    }

    /// 获取当前搜索策略
    /// - Returns: 当前搜索策略
    func getCurrentStrategy() -> SearchStrategy {
        return searchService.getCurrentStrategy()
    }

    /// 清理缓存
    /// - Parameter olderThanHours: 清理多少小时前的缓存
    func clearCache(olderThanHours: Int = 24) async {
        await searchService.clearCache()
    }

    /// 刷新数据
    func refreshData() async {
        // 刷新搜索相关数据
    }

    /// 处理应用进入前台
    func handleAppDidBecomeActive() {
        Task {
            await refreshData()
        }
    }

    /// 处理应用进入后台
    func handleAppDidEnterBackground() {
        // 取消所有进行中的任务
        currentSearchTask?.cancel()
        currentContentTask?.cancel()
    }

    /// 处理内存警告
    func handleMemoryWarning() {
        // 清理不必要的数据
        if !showSuggestions {
            suggestions = []
        }



        // 清理缓存
        Task {
            await clearCache(olderThanHours: 1)
        }
    }

    /// 触发后台索引更新
    /// 在用户关闭搜索面板或执行搜索后调用，确保索引数据保持最新
    private func triggerBackgroundIndexUpdate() {
        Task {
            do {
                NSLog("🔄 SearchViewModel: 触发后台索引更新")
                _ = try await searchService.syncIndexUpdates()
                NSLog("✅ SearchViewModel: 后台索引更新完成")
            } catch {
                NSLog("❌ SearchViewModel: 后台索引更新失败 - \(error)")
                // 索引更新失败不影响用户体验，只记录日志
            }
        }
    }
}

// MARK: - 调试和测试支持

#if DEBUG
extension SearchViewModel {

    /// 模拟搜索建议（用于测试）
    func mockSuggestions() {
        suggestions = [
            SearchSuggestion.mock(word: "apple", definition: "苹果"),
            SearchSuggestion.mock(word: "book", definition: "书籍"),
            SearchSuggestion.mock(word: "computer", definition: "计算机"),
            SearchSuggestion.mock(word: "dog", definition: "狗"),
            SearchSuggestion.mock(word: "elephant", definition: "大象")
        ]
        searchState = .showingResults(suggestions)
        showSuggestions = true
    }

    /// 模拟加载状态（用于测试）
    func mockLoadingState() {
        searchState = .searching
        contentLoadingState = .loading
    }

    /// 模拟错误状态（用于测试）
    func mockErrorState() {
        searchState = .error(.networkUnavailable)
        contentLoadingState = .error("内容加载失败")
        errorMessage = "搜索服务暂时不可用"
        showError = true
    }

    /// 模拟内容加载（用于测试）
    func mockContentLoaded() {
        let mockMetadata = WordMetadata(
            wordFrequency: "1000",
            relatedConcepts: ["example", "sample"]
        )

        let mockContent = WordContent(
            difficulty: "B2",
            phoneticSymbols: [PhoneticSymbol.british("/test/")],
            coreDefinition: "测试单词",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "母语者意图",
                emotionalResonance: "情感共鸣",
                vividImagery: "生动意象",
                etymologicalEssence: "词源精髓"
            ),
            usageExamples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: []
        )

        let mockResponse = WordDefinitionResponse(
            word: "test",
            metadata: mockMetadata,
            content: mockContent,
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )

        contentLoadingState = .loaded(mockResponse)
    }

    /// 重置到初始状态（用于测试）
    func resetToInitialState() {
        clearSearch()
    }

    /// 获取当前状态摘要（用于调试）
    func getStateSummary() -> [String: Any] {
        return [
            "searchQuery": searchQuery,
            "searchState": String(describing: searchState),
            "contentLoadingState": String(describing: contentLoadingState),
            "suggestionsCount": suggestions.count,
            "showSuggestions": showSuggestions,
            "selectedWord": selectedWord ?? "none",
            "hasError": showError
        ]
    }
}
#endif

// MARK: - 通知处理

extension SearchViewModel {

    /// 注册通知监听
    func registerNotifications() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didBecomeActiveNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppDidBecomeActive()
        }

        NotificationCenter.default.addObserver(
            forName: UIApplication.didEnterBackgroundNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleAppDidEnterBackground()
        }

        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleMemoryWarning()
        }
    }

    /// 注销通知监听
    func unregisterNotifications() {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - 通知名称扩展

extension Notification.Name {
    /// 搜索结果被选中的通知
    static let searchResultSelected = Notification.Name("searchResultSelected")
}
