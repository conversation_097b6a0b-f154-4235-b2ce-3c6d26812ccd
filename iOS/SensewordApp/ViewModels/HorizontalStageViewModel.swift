//
//  HorizontalStageViewModel.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  水平舞台状态管理 - 支持深度交互的单词学习体验
//

import Foundation
import SwiftUI
import Combine

// MARK: - 水平舞台状态管理

/// 水平舞台状态管理器
/// 核心职责：管理水平舞台的所有交互状态，协调各个组件的状态变化
@MainActor
class HorizontalStageViewModel: ObservableObject {
    
    // MARK: - 发布属性
    
    /// 当前激活的区块类型
    @Published var currentSection: SectionType = .deepContext
    
    /// 深思语境状态
    @Published var deepContextState = DeepContextState()
    
    /// 例句舞台状态
    @Published var exampleStageState = ExampleStageState()
    
    /// 场景轮换状态
    @Published var scenarioCarouselState = ScenarioCarouselState()
    
    /// 用法注释状态
    @Published var usageNotesState = UsageNotesState()
    
    /// 同义词状态
    @Published var synonymsState = SynonymsState()
    
    /// 全局动画状态
    @Published var globalAnimationState = AnimationState()
    
    // MARK: - 私有属性
    
    /// 单词数据
    private var wordData: WordDefinitionResponse?
    
    /// 取消令牌集合
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    init() {
        setupStateObservers()
    }
    
    // MARK: - 公共方法
    
    /// 初始化水平舞台状态
    /// - Parameter wordData: 单词定义数据
    func initializeStage(with wordData: WordDefinitionResponse) {
        self.wordData = wordData
        
        // 初始化各个区块状态
        initializeDeepContextState(with: wordData)
        initializeExampleStageState(with: wordData)
        initializeScenarioCarouselState(with: wordData)
        initializeUsageNotesState(with: wordData)
        initializeSynonymsState(with: wordData)
        
        // 设置初始区块
        currentSection = .deepContext
        
        NSLog("🎭 HorizontalStageViewModel: 水平舞台状态初始化完成，单词: \(wordData.word)")
    }
    
    /// 激活指定区块
    /// - Parameter section: 要激活的区块类型
    func activateSection(_ section: SectionType) {
        guard currentSection != section else { return }
        
        // 停用当前区块
        deactivateCurrentSection()
        
        // 激活新区块
        currentSection = section
        activateNewSection(section)
        
        NSLog("🎭 HorizontalStageViewModel: 激活区块 \(section.rawValue)")
    }
    
    // MARK: - 深思语境方法
    
    /// 切换深思语境内容类型
    /// - Parameter contentType: 内容类型
    func switchDeepContextContent(to contentType: DeepContextContentType) {
        deepContextState.contentType = contentType
        deepContextState.triggerTransition()
        
        NSLog("🌟 HorizontalStageViewModel: 切换深思语境到 \(contentType.rawValue)")
    }
    
    // MARK: - 例句舞台方法
    
    /// 进入短语分解模式
    /// - Parameters:
    ///   - categoryIndex: 例句分类索引
    ///   - exampleIndex: 例句索引
    func enterPhraseBreakdownMode(categoryIndex: Int, exampleIndex: Int) {
        guard let wordData = wordData,
              categoryIndex < wordData.content.usageExamples.count,
              exampleIndex < wordData.content.usageExamples[categoryIndex].examples.count else {
            NSLog("❌ HorizontalStageViewModel: 无效的例句索引")
            return
        }
        
        let example = wordData.content.usageExamples[categoryIndex].examples[exampleIndex]
        
        exampleStageState.currentCategoryIndex = categoryIndex
        exampleStageState.currentExampleIndex = exampleIndex
        exampleStageState.isInPhraseMode = true
        exampleStageState.currentPhraseIndex = 0
        exampleStageState.showBreathingHint = false
        
        // 初始化短语分解状态
        if let phrases = example.phraseBreakdown, !phrases.isEmpty {
            exampleStageState.phraseBreakdownState = PhraseBreakdownState(
                isActive: true,
                currentExample: example,
                phrases: phrases,
                currentPhraseIndex: 0,
                highlightedPhrase: phrases[0].phrase
            )
        }
        
        NSLog("🎭 HorizontalStageViewModel: 进入短语分解模式，例句: \(example.english)")
    }
    
    /// 切换到下一个短语
    func switchToNextPhrase() {
        guard exampleStageState.isInPhraseMode,
              let breakdownState = exampleStageState.phraseBreakdownState else { return }

        let nextIndex = breakdownState.currentPhraseIndex + 1

        if nextIndex < breakdownState.phrases.count {
            // 切换到下一个短语
            exampleStageState.phraseBreakdownState?.currentPhraseIndex = nextIndex
            exampleStageState.phraseBreakdownState?.highlightedPhrase = breakdownState.phrases[nextIndex].phrase

            NSLog("🧩 HorizontalStageViewModel: 切换到短语 \(nextIndex + 1)/\(breakdownState.phrases.count)")
        } else {
            // 完成短语分解，回到完整例句
            exitPhraseBreakdownMode()
        }
    }

    /// 切换到上一个短语
    func switchToPreviousPhrase() {
        guard exampleStageState.isInPhraseMode,
              let breakdownState = exampleStageState.phraseBreakdownState else { return }

        let previousIndex = breakdownState.currentPhraseIndex - 1

        if previousIndex >= 0 {
            // 切换到上一个短语
            exampleStageState.phraseBreakdownState?.currentPhraseIndex = previousIndex
            exampleStageState.phraseBreakdownState?.highlightedPhrase = breakdownState.phrases[previousIndex].phrase

            NSLog("🧩 HorizontalStageViewModel: 返回到短语 \(previousIndex + 1)/\(breakdownState.phrases.count)")
        }
    }
    
    /// 退出短语分解模式
    func exitPhraseBreakdownMode() {
        exampleStageState.isInPhraseMode = false
        exampleStageState.currentPhraseIndex = 0
        exampleStageState.phraseBreakdownState = nil
        exampleStageState.showBreathingHint = true
        
        NSLog("🎭 HorizontalStageViewModel: 退出短语分解模式")
    }
    
    // MARK: - 场景轮换方法
    
    /// 切换到下一个场景
    func switchToNextScenario() {
        guard let wordData = wordData,
              !wordData.content.usageScenarios.isEmpty else { return }
        
        let nextIndex = (scenarioCarouselState.currentIndex + 1) % wordData.content.usageScenarios.count
        scenarioCarouselState.currentIndex = nextIndex
        scenarioCarouselState.triggerTransition()
        
        NSLog("🎬 HorizontalStageViewModel: 切换到场景 \(nextIndex + 1)/\(wordData.content.usageScenarios.count)")
    }
    
    /// 切换到上一个场景
    func switchToPreviousScenario() {
        guard let wordData = wordData,
              !wordData.content.usageScenarios.isEmpty else { return }
        
        let prevIndex = scenarioCarouselState.currentIndex == 0 
            ? wordData.content.usageScenarios.count - 1 
            : scenarioCarouselState.currentIndex - 1
        scenarioCarouselState.currentIndex = prevIndex
        scenarioCarouselState.triggerTransition()
        
        NSLog("🎬 HorizontalStageViewModel: 切换到场景 \(prevIndex + 1)/\(wordData.content.usageScenarios.count)")
    }
    
    // MARK: - 用法注释方法
    
    /// 切换到下一个用法注释
    func switchToNextUsageNote() {
        guard let wordData = wordData,
              !wordData.content.usageNotes.isEmpty else { return }
        
        let nextIndex = (usageNotesState.currentIndex + 1) % wordData.content.usageNotes.count
        usageNotesState.currentIndex = nextIndex
        usageNotesState.triggerTransition()
        
        NSLog("📝 HorizontalStageViewModel: 切换到用法注释 \(nextIndex + 1)/\(wordData.content.usageNotes.count)")
    }
    
    // MARK: - 同义词方法
    
    /// 切换到下一个同义词
    func switchToNextSynonym() {
        guard let wordData = wordData,
              !wordData.content.synonyms.isEmpty else { return }
        
        let nextIndex = (synonymsState.currentIndex + 1) % wordData.content.synonyms.count
        synonymsState.currentIndex = nextIndex
        synonymsState.triggerTransition()
        
        NSLog("🔗 HorizontalStageViewModel: 切换到同义词 \(nextIndex + 1)/\(wordData.content.synonyms.count)")
    }
    
    // MARK: - 私有方法
    
    /// 设置状态观察者
    private func setupStateObservers() {
        // 监听区块切换
        $currentSection
            .sink { [weak self] section in
                self?.handleSectionChange(section)
            }
            .store(in: &cancellables)
    }
    
    /// 处理区块切换
    private func handleSectionChange(_ section: SectionType) {
        // 更新全局动画状态
        globalAnimationState.isTransitioning = true
        
        // 延迟重置动画状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.globalAnimationState.isTransitioning = false
        }
    }
    
    /// 停用当前区块
    private func deactivateCurrentSection() {
        switch currentSection {
        case .deepContext:
            deepContextState.isVisible = false
        case .examples:
            exampleStageState.showBreathingHint = false
        case .scenarios:
            scenarioCarouselState.isActive = false
        case .usageNotes:
            usageNotesState.isActive = false
        case .synonyms:
            synonymsState.isActive = false
        }
    }
    
    /// 激活新区块
    private func activateNewSection(_ section: SectionType) {
        switch section {
        case .deepContext:
            deepContextState.isVisible = true
        case .examples:
            exampleStageState.showBreathingHint = true
        case .scenarios:
            scenarioCarouselState.isActive = true
        case .usageNotes:
            usageNotesState.isActive = true
        case .synonyms:
            synonymsState.isActive = true
        }
    }

    // MARK: - 状态初始化方法

    /// 初始化深思语境状态
    private func initializeDeepContextState(with wordData: WordDefinitionResponse) {
        deepContextState.isVisible = true
        deepContextState.contentType = .definition
        deepContextState.isTransitioning = false
    }

    /// 初始化例句舞台状态
    private func initializeExampleStageState(with wordData: WordDefinitionResponse) {
        exampleStageState.currentCategoryIndex = 0
        exampleStageState.currentExampleIndex = 0
        exampleStageState.isInPhraseMode = false
        exampleStageState.currentPhraseIndex = 0
        exampleStageState.showBreathingHint = false
        exampleStageState.phraseBreakdownState = nil

        // 重置音频播放状态
        exampleStageState.audioPlaybackState.isPlaying = false
        exampleStageState.audioPlaybackState.currentAudioType = .fullSentence
        exampleStageState.audioPlaybackState.currentAudioIndex = 0
    }

    /// 初始化场景轮换状态
    private func initializeScenarioCarouselState(with wordData: WordDefinitionResponse) {
        scenarioCarouselState.currentIndex = 0
        scenarioCarouselState.isActive = false
        scenarioCarouselState.transitionAnimation = .none
        scenarioCarouselState.isLooping = true
    }

    /// 初始化用法注释状态
    private func initializeUsageNotesState(with wordData: WordDefinitionResponse) {
        usageNotesState.currentIndex = 0
        usageNotesState.isActive = false
        usageNotesState.transitionAnimation = .none
    }

    /// 初始化同义词状态
    private func initializeSynonymsState(with wordData: WordDefinitionResponse) {
        synonymsState.currentIndex = 0
        synonymsState.isActive = false
        synonymsState.transitionAnimation = .none
    }
}

// MARK: - 计算属性扩展

extension HorizontalStageViewModel {

    /// 当前深思语境内容
    var currentDeepContextContent: String {
        guard let wordData = wordData else { return "" }

        switch deepContextState.contentType {
        case .definition:
            return wordData.content.coreDefinition
        case .imagery:
            return wordData.content.contextualExplanation.vividImagery
        case .etymology:
            return wordData.content.contextualExplanation.etymologicalEssence
        }
    }

    /// 当前深思语境标题
    var currentDeepContextTitle: String {
        switch deepContextState.contentType {
        case .definition:
            return "释义"
        case .imagery:
            return "想象"
        case .etymology:
            return "词源"
        }
    }

    /// 当前例句
    var currentExample: UsageExample? {
        guard let wordData = wordData,
              exampleStageState.currentCategoryIndex < wordData.content.usageExamples.count,
              exampleStageState.currentExampleIndex < wordData.content.usageExamples[exampleStageState.currentCategoryIndex].examples.count else {
            return nil
        }

        return wordData.content.usageExamples[exampleStageState.currentCategoryIndex].examples[exampleStageState.currentExampleIndex]
    }

    /// 当前例句分类标题
    var currentExampleCategoryTitle: String {
        guard let wordData = wordData,
              exampleStageState.currentCategoryIndex < wordData.content.usageExamples.count else {
            return ""
        }

        return wordData.content.usageExamples[exampleStageState.currentCategoryIndex].category
    }

    /// 当前场景
    var currentScenario: UsageScenario? {
        guard let wordData = wordData,
              scenarioCarouselState.currentIndex < wordData.content.usageScenarios.count else {
            return nil
        }

        return wordData.content.usageScenarios[scenarioCarouselState.currentIndex]
    }

    /// 当前用法注释
    var currentUsageNote: UsageNote? {
        guard let wordData = wordData,
              usageNotesState.currentIndex < wordData.content.usageNotes.count else {
            return nil
        }

        return wordData.content.usageNotes[usageNotesState.currentIndex]
    }

    /// 当前同义词
    var currentSynonym: Synonym? {
        guard let wordData = wordData,
              synonymsState.currentIndex < wordData.content.synonyms.count else {
            return nil
        }

        return wordData.content.synonyms[synonymsState.currentIndex]
    }

    /// 获取当前单词数据
    var currentWordData: WordDefinitionResponse? {
        return wordData
    }
}
