//
//  SubscriptionViewModel.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  订阅页面视图模型
//

import Foundation
import Combine
import StoreKit

/// 订阅页面视图模型
@MainActor
class SubscriptionViewModel: ObservableObject {
    
    // MARK: - 发布属性
    
    /// 页面状态
    @Published var pageState: SubscriptionPageState = .loading
    
    /// 选中的产品ID
    @Published var selectedProductId: ProductId = .yearlyPremium
    
    /// 是否正在购买
    @Published var isPurchasing: Bool = false
    
    /// 是否正在恢复购买
    @Published var isRestoring: Bool = false
    
    /// 错误消息
    @Published var errorMessage: String? = nil
    
    /// 是否显示错误提示
    @Published var showError: Bool = false
    
    /// 页面配置
    @Published var config: SubscriptionPageConfig = .default
    
    // MARK: - 私有属性
    
    /// 订阅取消器
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化

    init() {
        loadProducts()
    }
    
    // MARK: - 公共方法
    
    /// 加载产品列表
    func loadProducts() {
        pageState = .loading
        
        // 模拟加载延迟，实际应该从App Store获取产品信息
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let products = SubscriptionProduct.allProducts
            self.pageState = .ready(products)
            NSLog("📦 [SubscriptionViewModel] 产品加载完成，共 \(products.count) 个产品")
        }
    }
    
    /// 选择产品
    func selectProduct(_ productId: ProductId) {
        guard case .ready(let products) = pageState else { return }
        
        selectedProductId = productId
        
        // 更新产品选中状态
        let updatedProducts = products.map { product in
            SubscriptionProduct.from(product.id, isSelected: product.id == productId)
        }
        
        pageState = .ready(updatedProducts)
        NSLog("🎯 [SubscriptionViewModel] 选择产品: \(productId.displayName)")
    }
    
    /// 开始购买流程
    func startPurchase() {
        guard case .ready(_) = pageState else { return }
        
        isPurchasing = true
        pageState = .purchasing(selectedProductId)
        
        NSLog("💳 [SubscriptionViewModel] 开始购买流程: \(selectedProductId.displayName)")

        // 真实购买流程
        Task {
            await performRealPurchase(selectedProductId)
        }
    }
    
    /// 恢复购买
    func restorePurchases() {
        guard case .ready(_) = pageState else { return }
        
        isRestoring = true
        NSLog("🔄 [SubscriptionViewModel] 开始恢复购买")

        // 真实恢复购买流程
        Task {
            await performRealRestore()
        }
    }
    
    /// 关闭错误提示
    func dismissError() {
        showError = false
        errorMessage = nil
    }
    
    // MARK: - 私有方法
    
    /// 执行真实购买
    private func performRealPurchase(_ productId: ProductId) async {
        do {
            // 首先获取Product对象
            guard let product = await getProduct(for: productId) else {
                await MainActor.run {
                    self.handlePurchaseResult(.failed(PurchaseError.unknown))
                }
                return
            }

            // 调用AnonymousPurchaseService进行购买
            let entitlementStatus = try await AnonymousPurchaseService.shared.purchaseProduct(product)

            await MainActor.run {
                if entitlementStatus.isPro {
                    self.handlePurchaseResult(.success(productId))
                } else {
                    self.handlePurchaseResult(.failed(PurchaseError.verificationFailed("权益验证失败")))
                }
            }
        } catch {
            await MainActor.run {
                self.handlePurchaseResult(.failed(error))
            }
        }
    }

    /// 获取Product对象
    private func getProduct(for productId: ProductId) async -> Product? {
        do {
            let products = try await Product.products(for: [productId.rawValue])
            return products.first
        } catch {
            NSLog("❌ [SubscriptionViewModel] 获取产品失败: \(error)")
            return nil
        }
    }
    
    /// 执行真实恢复购买
    private func performRealRestore() async {
        do {
            // 调用AnonymousPurchaseService进行恢复购买
            let entitlementStatus = try await AnonymousPurchaseService.shared.restorePurchases()

            await MainActor.run {
                if entitlementStatus.isPro {
                    // 恢复成功，返回相关产品ID
                    let restoredProducts: [ProductId] = [.yearlyPremium] // 可以根据实际恢复的产品调整
                    self.handleRestoreResult(.success(restoredProducts))
                } else {
                    self.handleRestoreResult(.noProductsToRestore) // 没有可恢复的购买
                }
            }
        } catch {
            await MainActor.run {
                self.handleRestoreResult(.failed(error))
            }
        }
    }
    
    /// 处理购买结果
    private func handlePurchaseResult(_ result: PurchaseResult) {
        isPurchasing = false
        
        switch result {
        case .success(let productId):
            NSLog("✅ [SubscriptionViewModel] 购买成功: \(productId.displayName)")
            // 这里应该更新用户的订阅状态
            loadProducts() // 重新加载产品状态
            
        case .cancelled:
            NSLog("⏹️ [SubscriptionViewModel] 购买已取消")
            loadProducts()
            
        case .failed(let error):
            NSLog("❌ [SubscriptionViewModel] 购买失败: \(error.localizedDescription)")
            showErrorMessage("购买失败: \(error.localizedDescription)")
            loadProducts()
            
        case .pending:
            NSLog("⏳ [SubscriptionViewModel] 购买待处理")
            loadProducts()
        }
    }
    
    /// 处理恢复购买结果
    private func handleRestoreResult(_ result: RestoreResult) {
        isRestoring = false
        
        switch result {
        case .success(let productIds):
            NSLog("✅ [SubscriptionViewModel] 恢复购买成功: \(productIds.map { $0.displayName })")
            loadProducts() // 重新加载产品状态
            
        case .noProductsToRestore:
            NSLog("ℹ️ [SubscriptionViewModel] 没有可恢复的购买")
            showErrorMessage("没有找到可恢复的购买记录")
            
        case .failed(let error):
            NSLog("❌ [SubscriptionViewModel] 恢复购买失败: \(error.localizedDescription)")
            showErrorMessage("恢复购买失败: \(error.localizedDescription)")
        }
    }
    
    /// 显示错误消息
    private func showErrorMessage(_ message: String) {
        errorMessage = message
        showError = true
    }
}

// MARK: - 计算属性

extension SubscriptionViewModel {
    
    /// 当前选中的产品
    var selectedProduct: SubscriptionProduct? {
        guard case .ready(let products) = pageState else { return nil }
        return products.first { $0.id == selectedProductId }
    }
    
    /// 是否可以购买
    var canPurchase: Bool {
        guard case .ready(_) = pageState else { return false }
        return !isPurchasing && !isRestoring
    }
    
    /// 继续按钮文本
    var continueButtonText: String {
        if isPurchasing {
            return "处理中..."
        } else if let product = selectedProduct {
            return "\(config.continueButtonTitle) - \(product.price)"
        } else {
            return config.continueButtonTitle
        }
    }
}
