//
//  APIError.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import Foundation

/// 统一的API错误处理枚举
/// 严格按照FC-01输出规范定义的7个错误case
enum APIError: Error {
    case invalidURL
    case invalidResponse
    case invalidAPIKey
    case unauthorized
    case networkError(Error)
    case decodingError(Error)
    case serverError(Int, String)
    case notImplemented
    case invalidRequest(String)  // 新增：无效请求参数
}

extension APIError: LocalizedError {
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response"
        case .invalidAPIKey:
            return "Invalid API key"
        case .unauthorized:
            return "Unauthorized"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .decodingError(let error):
            return "Decoding error: \(error.localizedDescription)"
        case .serverError(let statusCode, let message):
            return "Server error (\(statusCode)): \(message)"
        case .notImplemented:
            return "Feature not implemented"
        case .invalidRequest(let message):
            return "Invalid request: \(message)"
        }
    }
} 