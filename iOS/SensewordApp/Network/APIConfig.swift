//
//  APIConfig.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import Foundation

/// API配置管理
/// 包含静态API密钥和认证头部生成功能
struct APIConfig {
    /// 静态API密钥 - 用于所有API请求的基础认证
    static let staticAPIKey = "sk-senseword-c4d4d8b54e8e7f797901dc11a412758e-2025"
    
    /// 主API服务基础URL
    static let apiBaseURL = "https://api.senseword.app"
    
    /// [FC-02]: 静态头部生成器
    /// 生成仅包含静态API密钥的认证头部，用于公开API访问
    /// 输入: 无输入参数（计算属性）
    /// 输出: 静态认证头部字典
    static var staticHeaders: [String: String] {
        return [
            "X-Static-API-Key": staticAPIKey
        ]
    }
    

} 