//
//  APIClient.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import Foundation

/// HTTP方法枚举（严格按照技术方案）
enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case DELETE = "DELETE"
}

/// API客户端协议
/// 定义统一的HTTP请求接口
protocol APIClientProtocol {
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        headers: [String: String]?,
        body: Data?
    ) async throws -> T
}

/// 扩展协议以提供默认参数
extension APIClientProtocol {
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil
    ) async throws -> T {
        return try await request(
            endpoint: endpoint,
            method: method,
            headers: headers,
            body: body
        )
    }
}

/// HTTP客户端基础设施
/// [FC-01]: 提供统一的泛型HTTP请求接口，整合URL构建、网络请求、JSON解析的完整流程
class APIClient: APIClientProtocol {
    private let baseURL: String
    private let session: URLSession
    
    init(baseURL: String, session: URLSession = .shared) {
        self.baseURL = baseURL
        self.session = session
    }
    
    /// [FC-01]: APIClient统一请求接口
    /// 职责: 提供统一的泛型HTTP请求接口，整合URL构建、网络请求、JSON解析的完整流程
    /// 输入: endpoint: String, method: HTTPMethod = .GET, headers: [String: String]? = nil, body: Data? = nil
    /// 输出: T where T: Codable 或抛出APIError
    func request<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        headers: [String: String]?,
        body: Data?
    ) async throws -> T {
        // 1. 构建完整URL
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidURL
        }
        
        // 2. 创建URLRequest
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        
        // 3. 设置HTTP头部
        if let headers = headers {
            for (key, value) in headers {
                request.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        // 4. 执行网络请求
        do {
            let (data, response) = try await session.data(for: request)
            
            // 5. 验证HTTP响应
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }
            
            // 6. 处理HTTP状态码
            switch httpResponse.statusCode {
            case 200...299:
                // 成功响应，继续JSON解析
                break
            case 401:
                throw APIError.unauthorized
            case 403:
                throw APIError.invalidAPIKey
            default:
                let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown server error"
                throw APIError.serverError(httpResponse.statusCode, errorMessage)
            }
            
            // 7. JSON解析
            do {
                let decoder = JSONDecoder()
                let result = try decoder.decode(T.self, from: data)
                return result
            } catch {
                throw APIError.decodingError(error)
            }
            
        } catch let error as APIError {
            // 重新抛出APIError类型的错误
            throw error
        } catch {
            // 包装其他网络错误
            throw APIError.networkError(error)
        }
    }
} 