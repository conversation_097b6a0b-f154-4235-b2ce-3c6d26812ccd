//
//  DIContainer.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//

import Foundation

/// 统一依赖注入容器
/// 管理基础设施层、Adapter层、业务层和ViewModel层的所有服务
class DIContainer {
    static let shared = DIContainer()
    
    private init() {}
    
    // MARK: - 基础设施层服务
    
    /// SQLite数据库管理器
    /// 使用工厂方法处理async初始化
    lazy var sqliteManager: SQLiteManagerProtocol = {
        // 注意：这里需要在应用启动时异步初始化
        // 暂时返回一个placeholder，实际使用时需要确保已经初始化
        fatalError("SQLiteManager需要通过异步方法初始化，请使用createSQLiteManager()方法")
    }()
    
    /// 创建SQLite管理器的异步方法
    /// - Returns: 配置完成的SQLite管理器
    func createSQLiteManager() async throws -> SQLiteManagerProtocol {
        return try await SQLiteManager.create()
    }
    
    /// 缓存服务
    lazy var cacheService: CacheServiceProtocol = {
        return CacheService()
    }()

    /// 设置服务
    lazy var settingsService: SettingsServiceProtocol = {
        return SettingsService()
    }()
    
    // MARK: - [FC-04]: 网络客户端工厂
    
    /// 主API服务客户端（指向api.senseword.app）
    /// 输入: 无输入参数（使用预定义配置）
    /// 输出: 预配置的API客户端实例
    lazy var mainAPIClient: APIClientProtocol = {
        return APIClient(baseURL: APIConfig.apiBaseURL)
    }()
    
    /// 单词服务API转译层实例
    /// 输入: 无输入参数（使用预配置的主API客户端和语言对配置管理器）
    /// 输出: 单词服务API转译层实例
    lazy var wordAPIAdapter: WordAPIAdapterProtocol = {
        let languagePairConfigManager = createLanguagePairConfigManager(settingsService: settingsService)
        return WordAPIAdapter(apiClient: mainAPIClient, languagePairConfigManager: languagePairConfigManager)
    }()


    
    /// 搜索索引API转译层实例
    /// 输入: 无输入参数（使用预配置的主API客户端）
    /// 输出: 搜索索引API转译层实例
    lazy var searchAPIAdapter: SearchAPIAdapterProtocol = {
        return SearchAPIAdapter(apiClient: mainAPIClient)
    }()
    

    /// 购买验证API转译层实例
    /// 输入: 无输入参数（使用预配置的认证客户端）
    /// 输出: 购买验证API转译层实例
    /// 匿名购买服务
    lazy var anonymousPurchaseService: AnonymousPurchaseServiceProtocol = AnonymousPurchaseService.shared

    // MARK: - 业务层服务

    /// 本地索引服务
    /// 私有属性，通过工厂方法创建
    private var _localIndexService: LocalIndexServiceProtocol?

    /// 搜索服务
    /// 私有属性，通过工厂方法创建
    private var _searchService: SearchServiceProtocol?

    /// 本地收藏服务
    /// 私有属性，通过工厂方法创建
    private var _localBookmarkService: LocalBookmarkServiceProtocol?

    /// 索引下载服务
    /// 私有属性，通过工厂方法创建
    private var _indexDownloadService: IndexDownloadServiceProtocol?

    /// 语言对配置管理器
    /// 私有属性，通过工厂方法创建
    private var _languagePairConfigManager: LanguagePairConfigManagerProtocol?

    /// 索引下载管理器
    /// 私有属性，通过工厂方法创建
    private var _indexDownloadManager: IndexDownloadManagerProtocol?
    
    /// 创建本地索引服务
    /// - Parameter sqliteManager: SQLite管理器实例
    /// - Returns: 本地索引服务实例
    func createLocalIndexService(sqliteManager: SQLiteManagerProtocol) -> LocalIndexServiceProtocol {
        if let existing = _localIndexService {
            return existing
        }
        
        let service = LocalIndexService(
            sqliteManager: sqliteManager,
            cacheService: cacheService,
            searchAPIAdapter: searchAPIAdapter
        )
        _localIndexService = service
        return service
    }
    
    /// 创建搜索服务
    /// - Parameter localIndexService: 本地索引服务实例
    /// - Returns: 搜索服务实例
    func createSearchService(localIndexService: LocalIndexServiceProtocol) -> SearchServiceProtocol {
        if let existing = _searchService {
            return existing
        }
        
        let service = SearchService(
            localIndexService: localIndexService,
            cacheService: cacheService,
            searchAPIAdapter: searchAPIAdapter,
            wordAPIAdapter: wordAPIAdapter
        )
        _searchService = service
        return service
    }

    /// 创建本地收藏服务
    /// - Parameter sqliteManager: SQLite管理器实例
    /// - Returns: 本地收藏服务实例
    func createLocalBookmarkService(sqliteManager: SQLiteManagerProtocol) -> LocalBookmarkServiceProtocol {
        if let existing = _localBookmarkService {
            return existing
        }

        let service = LocalBookmarkService(
            sqliteManager: sqliteManager,
            deviceId: nil // 使用自动生成的设备ID
        )
        _localBookmarkService = service
        return service
    }

    /// 创建索引下载服务
    /// - Parameters:
    ///   - sqliteManager: SQLite管理器实例
    ///   - searchAPIAdapter: 搜索API适配器实例
    ///   - localIndexService: 本地索引服务实例
    /// - Returns: 索引下载服务实例
    func createIndexDownloadService(
        sqliteManager: SQLiteManagerProtocol,
        searchAPIAdapter: SearchAPIAdapterProtocol,
        localIndexService: LocalIndexServiceProtocol
    ) -> IndexDownloadServiceProtocol {
        if let existing = _indexDownloadService {
            return existing
        }

        let service = IndexDownloadService(
            sqliteManager: sqliteManager,
            searchAPIAdapter: searchAPIAdapter,
            localIndexService: localIndexService
        )
        _indexDownloadService = service
        return service
    }

    /// 创建语言对配置管理器
    /// - Parameter settingsService: 设置服务实例
    /// - Returns: 语言对配置管理器实例
    func createLanguagePairConfigManager(settingsService: SettingsServiceProtocol) -> LanguagePairConfigManagerProtocol {
        if let existing = _languagePairConfigManager {
            return existing
        }

        let manager = LanguagePairConfigManager(settingsService: settingsService)
        _languagePairConfigManager = manager
        return manager
    }

    /// 创建索引下载管理器
    /// - Parameters:
    ///   - settingsService: 设置服务实例
    ///   - indexDownloadService: 索引下载服务实例
    ///   - languagePairConfigManager: 语言对配置管理器实例
    /// - Returns: 索引下载管理器实例
    func createIndexDownloadManager(
        settingsService: SettingsServiceProtocol,
        indexDownloadService: IndexDownloadServiceProtocol,
        languagePairConfigManager: LanguagePairConfigManagerProtocol
    ) -> IndexDownloadManagerProtocol {
        if let existing = _indexDownloadManager {
            return existing
        }

        let manager = IndexDownloadManager(
            settingsService: settingsService,
            indexDownloadService: indexDownloadService,
            languagePairConfigManager: languagePairConfigManager
        )
        _indexDownloadManager = manager
        return manager
    }



    // MARK: - ViewModel工厂方法
    
    /// 创建搜索ViewModel
    /// - Parameter searchService: 搜索服务实例
    /// - Returns: 搜索ViewModel实例
    @MainActor
    func makeSearchViewModel(searchService: SearchServiceProtocol) -> SearchViewModel {
        return SearchViewModel(searchService: searchService)
    }
    
    // MARK: - 初始化管理
    
    /// 异步初始化所有搜索相关服务
    /// - Returns: 完全配置的搜索服务实例
    func initializeSearchServices() async throws -> SearchServiceProtocol {
        // 1. 创建SQLite管理器
        let sqliteManager = try await createSQLiteManager()
        
        // 2. 创建本地索引服务
        let localIndexService = createLocalIndexService(sqliteManager: sqliteManager)
        
        // 3. 创建搜索服务
        let searchService = createSearchService(localIndexService: localIndexService)
        
        return searchService
    }
    
    /// 获取已初始化的搜索服务
    /// 注意：必须先调用initializeSearchServices()
    var searchService: SearchServiceProtocol {
        guard let service = _searchService else {
            fatalError("SearchService未初始化，请先调用initializeSearchServices()方法")
        }
        return service
    }
    
    /// 获取已初始化的本地索引服务
    /// 注意：必须先调用initializeSearchServices()
    var localIndexService: LocalIndexServiceProtocol {
        guard let service = _localIndexService else {
            fatalError("LocalIndexService未初始化，请先调用initializeSearchServices()方法")
        }
        return service
    }

    /// 获取已初始化的本地收藏服务
    /// 注意：必须先调用createLocalBookmarkService()
    var localBookmarkService: LocalBookmarkServiceProtocol {
        guard let service = _localBookmarkService else {
            fatalError("LocalBookmarkService未初始化，请先调用createLocalBookmarkService()方法")
        }
        return service
    }

    /// 获取已初始化的索引下载服务
    /// 注意：必须先调用createIndexDownloadService()
    var indexDownloadService: IndexDownloadServiceProtocol {
        guard let service = _indexDownloadService else {
            fatalError("IndexDownloadService未初始化，请先调用createIndexDownloadService()方法")
        }
        return service
    }

    /// 获取已初始化的语言对配置管理器
    /// 注意：必须先调用createLanguagePairConfigManager()
    var languagePairConfigManager: LanguagePairConfigManagerProtocol {
        guard let manager = _languagePairConfigManager else {
            fatalError("LanguagePairConfigManager未初始化，请先调用createLanguagePairConfigManager()方法")
        }
        return manager
    }

    /// 获取已初始化的索引下载管理器
    /// 注意：必须先调用createIndexDownloadManager()
    var indexDownloadManager: IndexDownloadManagerProtocol {
        guard let manager = _indexDownloadManager else {
            fatalError("IndexDownloadManager未初始化，请先调用createIndexDownloadManager()方法")
        }
        return manager
    }
}