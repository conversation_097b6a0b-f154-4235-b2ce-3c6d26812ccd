import Foundation
import SwiftUI

/// 搜索服务状态枚举
/// 遵循Apple推荐的状态机模式，管理搜索服务的完整生命周期
enum SearchServiceState {
    case notInitialized                    // 未初始化
    case initializing                      // 正在初始化
    case ready(service: SearchServiceProtocol)  // 已就绪
    case failed(error: Error)              // 初始化失败
}

/// 搜索协调器
/// 核心职责：管理搜索服务的异步初始化和状态转换
/// 技术特点：遵循Apple官方性能指南，使用私有队列处理数据密集型操作
@MainActor
class SearchCoordinator: ObservableObject {
    
    // MARK: - 状态管理
    
    /// 搜索服务当前状态
    @Published private(set) var state: SearchServiceState = .notInitialized
    
    /// 初始化任务（防止重复初始化）
    private var initializationTask: Task<Void, Never>?
    
    // MARK: - 公共接口
    
    /// 按需初始化搜索服务
    /// 遵循Apple指南：只在真正需要时才初始化重型组件
    func initializeIfNeeded() {
        // 如果已经初始化或正在初始化，直接返回
        guard case .notInitialized = state else { return }
        
        // 防止重复初始化
        guard initializationTask == nil else { return }
        
        NSLog("🚀 SearchCoordinator: 开始按需初始化搜索服务...")
        state = .initializing
        
        // 使用Apple推荐的私有队列模式进行异步初始化
        initializationTask = Task.detached(priority: .userInitiated) {
            do {
                let startTime = Date()
                NSLog("⏱️ SearchCoordinator: 开始性能监控...")

                let service = try await Self.createSearchServiceInBackground()
                let initTime = Date().timeIntervalSince(startTime)

                NSLog("⏱️ SearchCoordinator: 后台初始化完成，准备切换到主线程...")
                let mainThreadStartTime = Date()

                await MainActor.run {
                    let mainThreadTime = Date().timeIntervalSince(mainThreadStartTime)
                    self.state = .ready(service: service)
                    self.initializationTask = nil
                    NSLog("✅ SearchCoordinator: 搜索服务初始化完成")
                    NSLog("⏱️ 性能统计 - 后台初始化: \(String(format: "%.3f", initTime))s, 主线程切换: \(String(format: "%.3f", mainThreadTime))s")
                }
            } catch {
                await MainActor.run {
                    self.state = .failed(error: error)
                    self.initializationTask = nil
                    NSLog("❌ SearchCoordinator: 搜索服务初始化失败 - \(error)")
                }
            }
        }
    }
    
    /// 获取就绪的搜索服务
    /// - Returns: 如果服务已就绪则返回服务实例，否则返回nil
    func getReadyService() -> SearchServiceProtocol? {
        if case .ready(let service) = state {
            return service
        }
        return nil
    }
    
    /// 检查是否正在初始化
    var isInitializing: Bool {
        if case .initializing = state {
            return true
        }
        return false
    }
    
    /// 检查是否初始化失败
    var initializationError: Error? {
        if case .failed(let error) = state {
            return error
        }
        return nil
    }
    
    // MARK: - 私有方法
    
    /// 在后台线程创建搜索服务
    /// 遵循Apple Core Data指南：在私有队列中处理数据密集型操作
    /// - Returns: 完全初始化的搜索服务实例
    private static func createSearchServiceInBackground() async throws -> SearchServiceProtocol {
        NSLog("🔧 SearchCoordinator: 在后台线程创建搜索服务...")
        
        // 1. 创建SQLite管理器（最耗时的操作）
        let sqliteStartTime = Date()
        let sqliteManager = try await SQLiteManager.create()
        let sqliteTime = Date().timeIntervalSince(sqliteStartTime)
        NSLog("🗄️ SearchCoordinator: SQLite管理器创建完成，耗时: \(String(format: "%.3f", sqliteTime))s")
        
        // 2. 创建缓存服务（轻量级）
        let cacheService = CacheService()
        NSLog("💾 SearchCoordinator: 缓存服务创建完成")
        
        // 3. 创建API适配器（轻量级）
        let apiClient = APIClient(baseURL: APIConfig.apiBaseURL)
        let searchAPIAdapter = SearchAPIAdapter(apiClient: apiClient)
        let wordAPIAdapter = WordAPIAdapter(apiClient: apiClient)
        NSLog("🌐 SearchCoordinator: API适配器创建完成")
        
        // 4. 创建本地索引服务（不在初始化时启动同步）
        let localIndexService = LocalIndexService(
            sqliteManager: sqliteManager,
            cacheService: cacheService,
            searchAPIAdapter: searchAPIAdapter
        )
        NSLog("📚 SearchCoordinator: 本地索引服务创建完成")
        
        // 5. 创建搜索服务（组装所有组件）
        let searchService = SearchService(
            localIndexService: localIndexService,
            cacheService: cacheService,
            searchAPIAdapter: searchAPIAdapter,
            wordAPIAdapter: wordAPIAdapter
        )
        NSLog("🔍 SearchCoordinator: 搜索服务组装完成")
        
        return searchService
    }
}

// MARK: - 扩展：便利方法

extension SearchCoordinator {
    
    /// 重置搜索服务状态（用于测试或错误恢复）
    func reset() {
        initializationTask?.cancel()
        initializationTask = nil
        state = .notInitialized
        NSLog("🔄 SearchCoordinator: 搜索服务状态已重置")
    }
}
