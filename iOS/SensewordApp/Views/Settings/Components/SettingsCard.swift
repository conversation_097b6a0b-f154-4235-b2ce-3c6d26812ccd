//
//  SettingsCard.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置卡片组件 - 高斯模糊半透明卡片
//

import SwiftUI

/// 设置卡片组件
/// 提供统一的高斯模糊半透明卡片样式
struct SettingsCard<Content: View>: View {
    
    // MARK: - 属性
    
    /// 卡片内容
    let content: Content
    
    // MARK: - 初始化
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        content
            .padding(20)
            .background(
                // 高斯模糊半透明背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .opacity(0.8)
            )
            .overlay(
                // 边框
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
    }
}

/// 设置分组组件
/// 用于组织相关的设置项
struct SettingsSection<Content: View>: View {
    
    // MARK: - 属性
    
    /// 分组标题
    let title: String
    
    /// 分组内容
    let content: Content
    
    // MARK: - 初始化
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 分组标题
            Text(title)
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(.white.opacity(0.7))
                .textCase(.uppercase)
                .padding(.horizontal, 4)
            
            // 分组内容
            SettingsCard {
                content
            }
        }
    }
}

// MARK: - 预览

struct SettingsCard_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // 深色背景
            Color.black
                .ignoresSafeArea()

            VStack(spacing: 20) {
                // 基础卡片
                SettingsCard {
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Image(systemName: "person.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(.white.opacity(0.8))

                            Text("账户信息")
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(.white)

                            Spacer()
                        }

                        Text("这是一个设置卡片的示例内容")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                    }
                }

                // 分组卡片
                SettingsSection(title: "个性化设置") {
                    VStack(spacing: 12) {
                        Text("设置项 1")
                            .foregroundColor(.white)

                        Text("设置项 2")
                            .foregroundColor(.white)
                    }
                }
            }
            .padding(20)
        }
    }
}
