//
//  SettingsRow.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置项组件 - 各种类型的设置行
//

import SwiftUI

/// 基础设置行组件
struct SettingsRow: View {
    
    // MARK: - 属性
    
    /// 标题
    let title: String
    
    /// 副标题（可选）
    let subtitle: String?
    
    /// 图标名称（可选）
    let iconName: String?
    
    /// 点击回调（可选）
    let action: (() -> Void)?
    
    // MARK: - 初始化
    
    init(
        title: String,
        subtitle: String? = nil,
        iconName: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.action = action
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        Button(action: action ?? {}) {
            HStack(spacing: 12) {
                // 图标
                if let iconName = iconName {
                    Image(systemName: iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .frame(width: 20, height: 20)
                }
                
                // 文本内容
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                
                Spacer()
                
                // 箭头指示器（如果有点击回调）
                if action != nil {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.5))
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(action == nil)
    }
}

/// 导航设置行组件
struct SettingsNavigationRow: View {
    
    // MARK: - 属性
    
    /// 标题
    let title: String
    
    /// 当前值（可选）
    let subtitle: String?
    
    /// 图标名称（可选）
    let iconName: String?
    
    /// 点击回调
    let action: () -> Void
    
    // MARK: - 初始化
    
    init(
        title: String,
        subtitle: String? = nil,
        iconName: String? = nil,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.action = action
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                if let iconName = iconName {
                    Image(systemName: iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))
                        .frame(width: 20, height: 20)
                }
                
                // 标题
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Spacer()
                
                // 当前值
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                // 箭头指示器
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding(.vertical, 12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 开关设置行组件
struct SettingsToggleRow: View {
    
    // MARK: - 属性
    
    /// 标题
    let title: String
    
    /// 副标题（可选）
    let subtitle: String?
    
    /// 图标名称（可选）
    let iconName: String?
    
    /// 开关状态绑定
    @Binding var isOn: Bool
    
    // MARK: - 初始化
    
    init(
        title: String,
        subtitle: String? = nil,
        iconName: String? = nil,
        isOn: Binding<Bool>
    ) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self._isOn = isOn
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        HStack(spacing: 12) {
            // 图标
            if let iconName = iconName {
                Image(systemName: iconName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                    .frame(width: 20, height: 20)
            }
            
            // 文本内容
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.7))
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
            
            Spacer()
            
            // 开关控件
            Toggle("", isOn: $isOn)
                .toggleStyle(SwitchToggleStyle(tint: .white))
                .scaleEffect(0.8)
        }
        .padding(.vertical, 8)
    }
}

/// 按钮设置行组件
struct SettingsButtonRow: View {
    
    // MARK: - 属性
    
    /// 标题
    let title: String
    
    /// 副标题（可选）
    let subtitle: String?
    
    /// 图标名称（可选）
    let iconName: String?
    
    /// 按钮样式
    let style: ButtonStyle
    
    /// 点击回调
    let action: () -> Void
    
    /// 按钮样式枚举
    enum ButtonStyle {
        case normal
        case destructive
        case prominent
        
        var textColor: Color {
            switch self {
            case .normal:
                return .white
            case .destructive:
                return .red
            case .prominent:
                return .white
            }
        }
        
        var backgroundColor: Color {
            switch self {
            case .normal:
                return .clear
            case .destructive:
                return .clear
            case .prominent:
                return .white.opacity(0.1)
            }
        }
    }
    
    // MARK: - 初始化
    
    init(
        title: String,
        subtitle: String? = nil,
        iconName: String? = nil,
        style: ButtonStyle = .normal,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.subtitle = subtitle
        self.iconName = iconName
        self.style = style
        self.action = action
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                // 图标
                if let iconName = iconName {
                    Image(systemName: iconName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(style.textColor.opacity(0.8))
                        .frame(width: 20, height: 20)
                }
                
                // 文本内容
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(style.textColor)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    
                    if let subtitle = subtitle {
                        Text(subtitle)
                            .font(.system(size: 14))
                            .foregroundColor(style.textColor.opacity(0.7))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                
                Spacer()
            }
            .padding(.vertical, 12)
            .padding(.horizontal, 16)
            .background(style.backgroundColor)
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览

struct SettingsRow_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black
                .ignoresSafeArea()
            
            SettingsCard {
                VStack(spacing: 16) {
                    SettingsRow(
                        title: "基础设置项",
                        subtitle: "这是一个基础设置项",
                        iconName: "gear"
                    )
                    
                    SettingsNavigationRow(
                        title: "导航设置项",
                        subtitle: "当前值",
                        iconName: "arrow.right.circle"
                    ) {
                        print("导航点击")
                    }
                    
                    SettingsToggleRow(
                        title: "开关设置项",
                        subtitle: "这是一个开关设置项",
                        iconName: "switch.2",
                        isOn: .constant(true)
                    )
                    
                    SettingsButtonRow(
                        title: "按钮设置项",
                        subtitle: "这是一个按钮设置项",
                        iconName: "button.programmable",
                        style: .prominent
                    ) {
                        print("按钮点击")
                    }
                }
            }
            .padding(20)
        }
    }
}
