//
//  LanguageSelectionView.swift
//  SensewordApp
//
//  Created by KDD-语言设置面板 Implementation on 2025-06-29.
//  语言选择全屏页面
//

import SwiftUI
import UIComponents

/// 语言选择全屏页面
struct LanguageSelectionView: View {

    // MARK: - 属性

    /// 当前选择的语言
    let currentLanguage: LanguageCode

    /// 语言选择回调
    let onLanguageSelected: (LanguageCode) -> Void

    /// 取消回调
    let onCancel: () -> Void

    /// 搜索文本
    @State private var searchText: String = ""

    /// 是否聚焦搜索框
    @FocusState private var isSearchFieldFocused: Bool

    // MARK: - 计算属性

    /// 过滤后的语言列表
    private var filteredLanguages: [LanguageCode] {
        if searchText.isEmpty {
            return LanguageCode.allCases
        } else {
            return LanguageCode.allCases.filter { language in
                language.displayName.localizedCaseInsensitiveContains(searchText) ||
                language.rawValue.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // MARK: - 界面构建

    var body: some View {
        ZStack {
            // 健康风格背景 - 与主界面保持一致
            KeyframeAnimationWallpaperView(
                animationStyle: .vibrant,
                forceDarkMode: true
            )

            // 主要内容
            VStack(spacing: 0) {
                // 顶部标题区域
                headerSection
                    .padding(.horizontal, 20)
                    .padding(.top, 60)

                // 搜索栏
                searchSection
                    .padding(.horizontal, 20)
                    .padding(.top, 16)

                // 语言列表
                languageListSection
                    .padding(.top, 16)
            }

            // 顶部关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: onCancel) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white.opacity(0.8))
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding(.top, 50)
                    .padding(.trailing, 20)
                }
                Spacer()
            }
        }
        .ignoresSafeArea()
    }

    // MARK: - 顶部标题区域

    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("语言设置")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)

            Text("选择您的单词查询语言")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - 搜索区域

    private var searchSection: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16))
                .foregroundColor(.white.opacity(0.6))

            TextField("搜索语言", text: $searchText)
                .font(.system(size: 16))
                .foregroundColor(.white)
                .focused($isSearchFieldFocused)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.6))
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
        )
    }

    // MARK: - 语言列表区域

    private var languageListSection: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(filteredLanguages, id: \.self) { language in
                    LanguageRowView(
                        language: language,
                        isSelected: language == currentLanguage,
                        onSelect: {
                            onLanguageSelected(language)
                        }
                    )
                }
            }
            .padding(.horizontal, 20)

            // 底部安全区域
            Spacer()
                .frame(height: 100)
        }
        .scrollIndicators(.hidden)
    }
}
// MARK: - 语言行视图

/// 语言选择行视图
private struct LanguageRowView: View {

    /// 语言
    let language: LanguageCode

    /// 是否选中
    let isSelected: Bool

    /// 选择回调
    let onSelect: () -> Void

    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // 语言显示名称
                VStack(alignment: .leading, spacing: 4) {
                    Text(language.displayName)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)

                    Text(language.rawValue.uppercased())
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.6))
                }

                Spacer()

                // 选中指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 24))
                        .foregroundColor(.orange)
                } else {
                    Image(systemName: "circle")
                        .font(.system(size: 24))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.white.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览

struct LanguageSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        LanguageSelectionView(
            currentLanguage: .chinese,
            onLanguageSelected: { language in
                print("选择语言: \(language.displayName)")
            },
            onCancel: {
                print("取消选择")
            }
        )
    }
}
