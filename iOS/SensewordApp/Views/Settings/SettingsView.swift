//
//  SettingsView.swift
//  SensewordApp
//
//  Created by KDD-设置页面 Implementation on 2025-06-29.
//  设置页面主视图
//

import SwiftUI
import UIComponents

/// 设置页面主视图
struct SettingsView: View {
    
    // MARK: - 状态管理
    
    /// 设置视图模型
    @StateObject private var viewModel = SettingsViewModel()
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    // MARK: - 初始化
    
    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        ZStack {
            // 健康风格背景 - 与主界面保持一致
            KeyframeAnimationWallpaperView(
                animationStyle: .vibrant,
                forceDarkMode: true
            )
            
            // 设置内容
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部标题区域
                    headerSection
                    
                    // 账户信息卡片
                    accountInfoSection
                    
                    // 订阅状态卡片
                    subscriptionSection
                    
                    // 语言设置
                    languageSection
                    
                    // 个性化设置
                    personalizationSection
                    
                    // 产品信息
                    productInfoSection
                    
                    // 底部安全区域
                    Spacer()
                        .frame(height: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
            }
            .scrollIndicators(.hidden)
            
            // 顶部关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: onDismiss) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white.opacity(0.8))
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding(.top, 50)
                    .padding(.trailing, 20)
                }
                Spacer()
            }
        }
        .ignoresSafeArea()
        .fullScreenCover(isPresented: $viewModel.showSubscription) {
            SubscriptionView(onDismiss: {
                viewModel.showSubscription = false
            })
        }
        .fullScreenCover(isPresented: $viewModel.showLanguageSelection) {
            LanguageSelectionView(
                currentLanguage: viewModel.userSettings.preferredLanguage,
                onLanguageSelected: { language in
                    viewModel.updatePreferredLanguage(language)
                },
                onCancel: {
                    viewModel.cancelLanguageSelection()
                }
            )
        }
    }
    
    // MARK: - 顶部标题区域
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("设置")
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
            
            Text("个性化您的学习体验")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white.opacity(0.7))
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 16)
    }
    
    // MARK: - 账户信息区域
    
    private var accountInfoSection: some View {
        SettingsCard {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: "person.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("我的账户")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                // 离线模式提示
                Text("离线模式")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
        }
    }
    
    // MARK: - 订阅状态区域
    
    private var subscriptionSection: some View {
        SettingsCard {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: viewModel.isPro ? "crown.fill" : "crown")
                        .font(.system(size: 20))
                        .foregroundColor(viewModel.isPro ? .yellow : .white.opacity(0.8))
                    
                    Text("订阅状态")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                // 订阅信息
                VStack(alignment: .leading, spacing: 12) {
                    Text(viewModel.subscriptionStatusText)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    if !viewModel.isPro {
                        Text("升级到 PRO 解锁全部功能")
                            .font(.system(size: 14))
                            .foregroundColor(.white.opacity(0.7))
                    }
                    
                    // 管理订阅按钮
                    Button(action: {
                        viewModel.openSubscriptionManagement()
                    }) {
                        HStack {
                            Text(viewModel.isPro ? "管理订阅" : "升级到 PRO")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white)
                            
                            Spacer()
                            
                            Image(systemName: "arrow.up.right")
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.7))
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .background(Color.white.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
    
    // MARK: - 语言设置区域
    
    private var languageSection: some View {
        SettingsCard {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: "globe")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("语言设置")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                // 语言选择
                SettingsNavigationRow(
                    title: "单词查询语言",
                    subtitle: viewModel.currentLanguageDisplayName,
                    action: {
                        viewModel.showLanguageSelectionView()
                    }
                )
                
                SettingsNavigationRow(
                    title: "音标偏好",
                    subtitle: "美式发音",
                    action: {
                        // TODO: 实现音标偏好选择
                        print("打开音标偏好选择")
                    }
                )
            }
        }
    }
    
    // MARK: - 个性化设置区域
    
    private var personalizationSection: some View {
        SettingsCard {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: "slider.horizontal.3")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("个性化")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                // 设置项
                SettingsToggleRow(
                    title: "自动播放音频",
                    subtitle: "自动播放单词和例句的音频",
                    isOn: Binding(
                        get: { viewModel.userSettings.autoPlayAudio },
                        set: { viewModel.updateAutoPlayAudio($0) }
                    )
                )
                
                SettingsToggleRow(
                    title: "触感反馈",
                    subtitle: "在交互时提供震动反馈",
                    isOn: Binding(
                        get: { viewModel.userSettings.hapticFeedback },
                        set: { viewModel.updateHapticFeedback($0) }
                    )
                )
                
                SettingsToggleRow(
                    title: "每日提醒",
                    subtitle: "每日推送一个新单词",
                    isOn: Binding(
                        get: { viewModel.userSettings.dailyNotification },
                        set: { viewModel.updateDailyNotification($0) }
                    )
                )
            }
        }
    }
    
    // MARK: - 产品信息区域
    
    private var productInfoSection: some View {
        SettingsCard {
            VStack(alignment: .leading, spacing: 16) {
                // 标题
                HStack {
                    Image(systemName: "info.circle")
                        .font(.system(size: 20))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("关于")
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                
                // 产品信息
                VStack(alignment: .leading, spacing: 12) {
                    Text(viewModel.productInfo.appName)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                    
                    Text(viewModel.productInfo.description)
                        .font(.system(size: 14))
                        .foregroundColor(.white.opacity(0.7))
                    
                    Text(viewModel.productInfo.versionText)
                        .font(.system(size: 12))
                        .foregroundColor(.white.opacity(0.5))
                }
                
                // 操作按钮
                SettingsNavigationRow(
                    title: "在 App Store 中评分",
                    subtitle: nil,
                    action: {
                        viewModel.openAppStoreRating()
                    }
                )
            }
        }
    }
}
