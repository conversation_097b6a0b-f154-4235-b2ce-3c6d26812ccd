//
//  PhoneticPreferenceView.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  音标偏好选择界面
//

import SwiftUI

/// 音标偏好选择视图
struct PhoneticPreferenceView: View {
    
    // MARK: - 属性
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    /// 全局设置管理器
    @ObservedObject private var settingsManager = GlobalSettingsManager.shared
    
    /// 当前选择的偏好
    @State private var selectedPreference: PhoneticPreference
    
    // MARK: - 初始化
    
    init(onDismiss: @escaping () -> Void) {
        self.onDismiss = onDismiss
        self._selectedPreference = State(initialValue: GlobalSettingsManager.shared.currentPhoneticPreference)
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        ZStack {
            // 简单暗色背景
            Color.black
                .ignoresSafeArea()
            
            // 主要内容
            VStack(spacing: 32) {
                // 标题区域
                headerSection
                
                // 选择区域
                selectionSection
                
                // 示例区域
                exampleSection
                
                Spacer()
                
                // 确认按钮
                confirmButton
            }
            .padding(.horizontal, 24)
            .padding(.top, 80)
            .padding(.bottom, 40)
            
            // 顶部关闭按钮
            topCloseButton
        }
    }
    
    // MARK: - 子视图
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Text("音标偏好")
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.white)
            
            Text("选择你偏好的音标类型")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white.opacity(0.7))
        }
    }
    
    private var selectionSection: some View {
        VStack(spacing: 16) {
            ForEach(PhoneticPreference.allCases, id: \.self) { preference in
                preferenceOptionView(preference)
            }
        }
    }
    
    private func preferenceOptionView(_ preference: PhoneticPreference) -> some View {
        Button(action: {
            selectedPreference = preference
        }) {
            HStack(spacing: 16) {
                // 选择指示器
                Image(systemName: selectedPreference == preference ? "checkmark.circle.fill" : "circle")
                    .font(.system(size: 24))
                    .foregroundColor(selectedPreference == preference ? .blue : .white.opacity(0.5))
                
                VStack(alignment: .leading, spacing: 4) {
                    // 偏好名称
                    Text(preference.displayName)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                    
                    // 偏好描述
                    Text(preference.phoneticType)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(selectedPreference == preference ? Color.blue.opacity(0.2) : Color.white.opacity(0.1))
                    .stroke(selectedPreference == preference ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var exampleSection: some View {
        VStack(spacing: 16) {
            Text("示例")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(.white)
            
            VStack(spacing: 12) {
                exampleWordView(word: "hello", american: "/həˈloʊ/", british: "/həˈləʊ/")
                exampleWordView(word: "water", american: "/ˈwɔːtər/", british: "/ˈwɔːtə/")
                exampleWordView(word: "dance", american: "/dæns/", british: "/dɑːns/")
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
        )
    }
    
    private func exampleWordView(word: String, american: String, british: String) -> some View {
        HStack {
            Text(word)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 60, alignment: .leading)
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("美式: \(american)")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(selectedPreference == .american ? .blue : .white.opacity(0.7))
                
                Text("英式: \(british)")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(selectedPreference == .british ? .blue : .white.opacity(0.7))
            }
        }
    }
    
    private var confirmButton: some View {
        Button(action: {
            // 保存设置
            settingsManager.updatePhoneticPreference(selectedPreference)
            onDismiss()
        }) {
            Text("确认")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var topCloseButton: some View {
        VStack {
            HStack {
                Spacer()
                Button(action: onDismiss) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 30))
                        .foregroundColor(.white.opacity(0.8))
                        .background(Color.black.opacity(0.3))
                        .clipShape(Circle())
                }
                .padding(.top, 50)
                .padding(.trailing, 20)
            }
            Spacer()
        }
    }
}

// MARK: - 预览

#if DEBUG
struct PhoneticPreferenceView_Previews: PreviewProvider {
    static var previews: some View {
        PhoneticPreferenceView(onDismiss: {})
            .preferredColorScheme(.dark)
    }
}
#endif
