//
//  DeepContextComponent.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  深思语境组件 - 合并释义、想象、词源的统一体验
//

import SwiftUI

/// 深思语境组件
/// 核心功能：将释义、想象描述、词源解释合并为统一的水平滑动体验
struct DeepContextComponent: View {
    
    // MARK: - 属性
    
    /// 水平舞台视图模型
    @ObservedObject var viewModel: HorizontalStageViewModel
    
    /// 当前内容类型
    @State private var currentContentType: DeepContextContentType = .definition
    
    /// 拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(spacing: 0) {
            // 内容指示器
            contentIndicator
            
            // 主要内容区域
            contentArea
                .gesture(horizontalDragGesture)
        }
        .opacity(viewModel.deepContextState.isVisible ? 1.0 : 0.0)
        .animation(.easeInOut(duration: 0.3), value: viewModel.deepContextState.isVisible)
        .onReceive(viewModel.deepContextState.$contentType) { newType in
            withAnimation(.easeInOut(duration: 0.3)) {
                currentContentType = newType
            }
        }
    }
    
    // MARK: - 内容指示器
    
    private var contentIndicator: some View {
        HStack(spacing: 8) {
            ForEach(DeepContextContentType.allCases, id: \.self) { contentType in
                Circle()
                    .fill(currentContentType == contentType ? Color.white : Color.gray.opacity(0.5))
                    .frame(width: 6, height: 6)
                    .scaleEffect(currentContentType == contentType ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: currentContentType)
            }
        }
        .padding(.bottom, 16)
    }
    
    // MARK: - 内容区域
    
    private var contentArea: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 释义内容
                contentView(
                    title: "释义",
                    content: viewModel.currentDeepContextContent,
                    contentType: .definition
                )
                .frame(width: geometry.size.width)
                
                // 想象内容
                contentView(
                    title: "想象",
                    content: getContentForType(.imagery),
                    contentType: .imagery
                )
                .frame(width: geometry.size.width)
                
                // 词源内容
                contentView(
                    title: "词源",
                    content: getContentForType(.etymology),
                    contentType: .etymology
                )
                .frame(width: geometry.size.width)
            }
            .offset(x: contentOffset(for: geometry.size.width))
            .animation(.easeInOut(duration: isDragging ? 0.0 : 0.3), value: currentContentType)
        }
        .frame(height: 120)
        .clipped()
    }
    
    // MARK: - 单个内容视图
    
    @ViewBuilder
    private func contentView(title: String, content: String, contentType: DeepContextContentType) -> some View {
        VStack(alignment: .center, spacing: 12) {
            // 标题
            Text(title)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.senseWordGray400)
            
            // 内容
            Text(content)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .lineLimit(4)
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 20)
        .opacity(currentContentType == contentType ? 1.0 : 0.7)
        .scaleEffect(currentContentType == contentType ? 1.0 : 0.95)
    }
    
    // MARK: - 手势处理
    
    private var horizontalDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                isDragging = true
                dragOffset = value.translation.width
            }
            .onEnded { value in
                isDragging = false
                handleDragEnd(translation: value.translation.width)
                dragOffset = 0
            }
    }
    
    // MARK: - 私有方法
    
    /// 处理拖拽结束
    private func handleDragEnd(translation: CGFloat) {
        let threshold: CGFloat = 50
        
        if translation > threshold {
            // 向右滑动 - 切换到上一个内容
            switchToPreviousContent()
        } else if translation < -threshold {
            // 向左滑动 - 切换到下一个内容
            switchToNextContent()
        }
    }
    
    /// 切换到下一个内容
    private func switchToNextContent() {
        let allCases = DeepContextContentType.allCases
        guard let currentIndex = allCases.firstIndex(of: currentContentType) else { return }
        
        let nextIndex = (currentIndex + 1) % allCases.count
        let nextContentType = allCases[nextIndex]
        
        viewModel.switchDeepContextContent(to: nextContentType)
        
        // 触发触觉反馈
        triggerHapticFeedback()
    }
    
    /// 切换到上一个内容
    private func switchToPreviousContent() {
        let allCases = DeepContextContentType.allCases
        guard let currentIndex = allCases.firstIndex(of: currentContentType) else { return }
        
        let previousIndex = currentIndex == 0 ? allCases.count - 1 : currentIndex - 1
        let previousContentType = allCases[previousIndex]
        
        viewModel.switchDeepContextContent(to: previousContentType)
        
        // 触发触觉反馈
        triggerHapticFeedback()
    }
    
    /// 计算内容偏移量
    private func contentOffset(for width: CGFloat) -> CGFloat {
        let baseOffset: CGFloat
        
        switch currentContentType {
        case .definition:
            baseOffset = 0
        case .imagery:
            baseOffset = -width
        case .etymology:
            baseOffset = -width * 2
        }
        
        return baseOffset + dragOffset
    }
    
    /// 获取指定类型的内容
    private func getContentForType(_ contentType: DeepContextContentType) -> String {
        // 临时保存当前类型
        let originalType = viewModel.deepContextState.contentType
        
        // 临时切换类型以获取内容
        viewModel.deepContextState.contentType = contentType
        let content = viewModel.currentDeepContextContent
        
        // 恢复原始类型
        viewModel.deepContextState.contentType = originalType
        
        return content
    }
    
    /// 触发触觉反馈
    private func triggerHapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 预览

#if DEBUG
struct DeepContextComponent_Previews: PreviewProvider {
    static var previews: some View {
        let viewModel = HorizontalStageViewModel()
        
        // 创建模拟数据
        let mockMetadata = WordMetadata(
            wordFrequency: "medium",
            relatedConcepts: ["discovery", "chance", "luck"]
        )
        
        let mockContextualExplanation = ContextualExplanation(
            nativeSpeakerIntent: "表达意外惊喜的发现",
            emotionalResonance: "带有愉悦和惊喜的情感色彩",
            vividImagery: "想象在尘土飞扬的古董店里意外发现珍贵宝物的那种惊喜",
            etymologicalEssence: "来自波斯童话《锡兰三王子》，指意外发现的智慧"
        )
        
        let mockContent = WordContent(
            difficulty: "intermediate",
            phoneticSymbols: [],
            coreDefinition: "意外发现有价值事物的能力",
            contextualExplanation: mockContextualExplanation,
            usageExamples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: []
        )
        
        let mockWordData = WordDefinitionResponse(
            word: "serendipity",
            metadata: mockMetadata,
            content: mockContent,
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )
        
        // 初始化视图模型
        viewModel.initializeStage(with: mockWordData)
        
        return DeepContextComponent(viewModel: viewModel)
            .preferredColorScheme(.dark)
            .padding()
            .background(Color.black)
    }
}
#endif
