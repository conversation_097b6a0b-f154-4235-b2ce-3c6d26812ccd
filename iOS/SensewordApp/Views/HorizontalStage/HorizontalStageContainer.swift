//
//  HorizontalStageContainer.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  水平舞台容器 - 统一管理所有水平交互组件
//

import SwiftUI

/// 水平舞台容器
/// 核心功能：统一管理深思语境、例句舞台、场景轮换等所有水平交互组件
struct HorizontalStageContainer: View {
    
    // MARK: - 属性
    
    /// 单词定义数据
    let wordData: WordDefinitionResponse
    
    /// 水平舞台视图模型
    @StateObject private var viewModel = HorizontalStageViewModel()
    

    
    // MARK: - 界面构建
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                VStack(spacing: 50) {
                    // 深思语境区域
                    contentSection(id: "deep_context") {
                        DeepContextComponent(viewModel: viewModel)
                    }
                    
                    // 例句水平舞台区域
                    contentSection(id: "examples") {
                        ExampleStageComponent(viewModel: viewModel)
                    }
                    
                    // 使用场景轮换区域
                    contentSection(id: "scenarios") {
                        ScenarioCarouselComponent(viewModel: viewModel)
                    }
                    
                    // 用法注释轮换区域
                    contentSection(id: "usage_notes") {
                        UsageNotesCarouselComponent(viewModel: viewModel)
                    }
                    
                    // 同义词轮换区域
                    contentSection(id: "synonyms") {
                        SynonymsCarouselComponent(viewModel: viewModel)
                    }
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 16)
            }
            .background(Color.clear)
            .scrollIndicators(.hidden)  // 隐藏滚动指示器
            .onAppear {
                initializeStage()
            }
            .onReceive(viewModel.$currentSection) { section in
                handleSectionChange(section)
            }
        }
    }
    
    // MARK: - 内容区块
    
    @ViewBuilder
    private func contentSection<Content: View>(id: String, @ViewBuilder content: @escaping () -> Content) -> some View {
        content()
            .id(id)
            .onAppear {
                updateCurrentSection(for: id)
            }
    }
    
    // MARK: - 私有方法
    
    /// 初始化舞台
    private func initializeStage() {
        viewModel.initializeStage(with: wordData)
        
        NSLog("🎭 HorizontalStageContainer: 水平舞台容器初始化完成")
    }
    
    /// 处理区块切换
    private func handleSectionChange(_ section: SectionType) {
        // 可以在这里添加自动滚动到对应区块的逻辑
        // 但通常我们希望保持用户的滚动控制
        NSLog("🎭 HorizontalStageContainer: 当前激活区块 \(section.rawValue)")
    }
    
    /// 更新当前区块
    private func updateCurrentSection(for id: String) {
        guard let sectionType = SectionType(rawValue: id) else { return }
        
        // 只有当区块真正可见时才激活
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if viewModel.currentSection != sectionType {
                viewModel.activateSection(sectionType)
            }
        }
    }
}

// MARK: - 预览

#if DEBUG
struct HorizontalStageContainer_Previews: PreviewProvider {
    static var previews: some View {
        // 创建完整的模拟数据
        let mockMetadata = WordMetadata(
            wordFrequency: "medium",
            relatedConcepts: ["discovery", "chance", "luck"]
        )
        
        let mockContextualExplanation = ContextualExplanation(
            nativeSpeakerIntent: "表达意外惊喜的发现",
            emotionalResonance: "带有愉悦和惊喜的情感色彩",
            vividImagery: "想象在尘土飞扬的古董店里意外发现珍贵宝物的那种惊喜",
            etymologicalEssence: "来自波斯童话《锡兰三王子》，指意外发现的智慧"
        )
        
        let mockPhraseBreakdown = [
            PhraseBreakdown(
                phrase: "Finding that rare book",
                translation: "找到那本稀有的书",
                audioUrl: "https://example.com/audio/phrase1.mp3"
            ),
            PhraseBreakdown(
                phrase: "in a dusty antique shop",
                translation: "在一家尘土飞扬的古董店里",
                audioUrl: "https://example.com/audio/phrase2.mp3"
            ),
            PhraseBreakdown(
                phrase: "was a moment of pure serendipity",
                translation: "是一次纯粹的serendipity",
                audioUrl: "https://example.com/audio/phrase3.mp3"
            )
        ]
        
        let mockUsageExamples = [
            UsageExampleCategory(
                category: "日常发现",
                examples: [
                    UsageExample(
                        english: "Finding that rare book in a dusty antique shop was a moment of pure serendipity.",
                        translation: "在尘土飞扬的古董店里找到那本稀有的书，真是一次纯粹的serendipity。",
                        audioUrl: "https://example.com/audio/example1.mp3",
                        phraseBreakdown: mockPhraseBreakdown
                    )
                ]
            )
        ]
        
        let mockUsageScenarios = [
            UsageScenario(
                category: "科学发现",
                relevance: "高度相关",
                context: "科学家在研究过程中的意外发现"
            ),
            UsageScenario(
                category: "人际关系",
                relevance: "中度相关",
                context: "意外遇到重要的人或建立有意义的关系"
            )
        ]
        
        let mockUsageNotes = [
            UsageNote(
                aspect: "语气色彩",
                explanation: "通常带有积极正面的含义",
                examples: [
                    UsageNoteExample(
                        sentence: "It was pure serendipity that led to this breakthrough.",
                        translation: "正是这种意外的发现导致了这一突破。"
                    )
                ]
            )
        ]
        
        let mockSynonyms = [
            Synonym(
                word: "coincidence",
                explanation: "更强调巧合性，较少强调价值发现",
                examples: [
                    SynonymExample(
                        sentence: "Meeting you here is quite a coincidence.",
                        translation: "在这里遇到你真是太巧了。"
                    )
                ]
            )
        ]
        
        let mockContent = WordContent(
            difficulty: "intermediate",
            phoneticSymbols: [],
            coreDefinition: "意外发现有价值事物的能力",
            contextualExplanation: mockContextualExplanation,
            usageExamples: mockUsageExamples,
            usageScenarios: mockUsageScenarios,
            collocations: [],
            usageNotes: mockUsageNotes,
            synonyms: mockSynonyms
        )
        
        let mockWordData = WordDefinitionResponse(
            word: "serendipity",
            metadata: mockMetadata,
            content: mockContent,
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )
        
        return HorizontalStageContainer(wordData: mockWordData)
            .preferredColorScheme(.dark)
            .background(Color.black)
    }
}
#endif
