//
//  SynonymsCarouselComponent.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  同义词轮换组件 - 水平切换同义词
//

import SwiftUI

/// 同义词轮换组件
/// 核心功能：支持同义词的水平切换交互
struct SynonymsCarouselComponent: View {
    
    // MARK: - 属性
    
    /// 水平舞台视图模型
    @ObservedObject var viewModel: HorizontalStageViewModel
    
    /// 拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("同义词")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)
            
            // 同义词内容区域
            synonymContentArea
                .gesture(horizontalDragGesture)
            
            // 同义词指示器
            synonymIndicator
        }
        .frame(maxWidth: .infinity)
        .opacity(viewModel.synonymsState.isActive ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.3), value: viewModel.synonymsState.isActive)
    }
    
    // MARK: - 同义词内容区域
    
    private var synonymContentArea: some View {
        VStack(spacing: 16) {
            if let synonym = viewModel.currentSynonym {
                // 同义词单词
                Text(synonym.word)
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.blue)
                
                // 同义词解释
                Text(synonym.explanation)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white)
                    .lineSpacing(4)
                    .multilineTextAlignment(.center)
                    .frame(maxWidth: .infinity)
                
                // 示例（如果有）
                if !synonym.examples.isEmpty {
                    VStack(spacing: 12) {
                        ForEach(0..<min(synonym.examples.count, 2), id: \.self) { index in
                            let example = synonym.examples[index]
                            synonymExampleView(example: example)
                        }
                    }
                    .padding(.top, 12)
                }
            } else {
                // 空状态
                emptySynonymView
            }
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
        .offset(x: dragOffset)
        .scaleEffect(isDragging ? 0.98 : 1.0)
        .animation(.easeOut(duration: 0.2), value: isDragging)
    }
    
    // MARK: - 同义词示例视图
    
    @ViewBuilder
    private func synonymExampleView(example: SynonymExample) -> some View {
        VStack(spacing: 6) {
            // 英文例句
            Text(example.sentence)
                .font(.system(size: 13, weight: .medium))
                .foregroundColor(.white)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 中文翻译
            Text(example.translation)
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(Color.secondary)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.blue.opacity(0.1))
        )
    }
    
    // MARK: - 同义词指示器
    
    private var synonymIndicator: some View {
        HStack(spacing: 8) {
            if let wordData = getWordData() {
                ForEach(0..<wordData.content.synonyms.count, id: \.self) { index in
                    Circle()
                        .fill(index == viewModel.synonymsState.currentIndex ? Color.white : Color.gray.opacity(0.5))
                        .frame(width: 6, height: 6)
                        .scaleEffect(index == viewModel.synonymsState.currentIndex ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: viewModel.synonymsState.currentIndex)
                }
            }
        }
    }
    
    // MARK: - 空状态视图
    
    private var emptySynonymView: some View {
        VStack(spacing: 12) {
            Image(systemName: "link")
                .font(.system(size: 24))
                .foregroundColor(Color.secondary)
            
            Text("暂无同义词")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.secondary)
        }
        .frame(height: 80)
    }
    
    // MARK: - 手势处理
    
    private var horizontalDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                isDragging = true
                dragOffset = value.translation.width
            }
            .onEnded { value in
                isDragging = false
                handleDragEnd(translation: value.translation.width)
                
                withAnimation(.easeOut(duration: 0.3)) {
                    dragOffset = 0
                }
            }
    }
    
    // MARK: - 私有方法
    
    /// 处理拖拽结束
    private func handleDragEnd(translation: CGFloat) {
        let threshold: CGFloat = 50
        
        if translation > threshold || translation < -threshold {
            // 任意方向滑动都切换到下一个同义词
            viewModel.switchToNextSynonym()
            triggerHapticFeedback(.light)
        }
    }
    
    /// 获取单词数据
    private func getWordData() -> WordDefinitionResponse? {
        return viewModel.currentWordData
    }
    
    /// 触发触觉反馈
    private func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 预览

#if DEBUG
struct SynonymsCarouselComponent_Previews: PreviewProvider {
    static var previews: some View {
        let viewModel = HorizontalStageViewModel()
        
        // 创建模拟数据
        let mockSynonyms = [
            Synonym(
                word: "coincidence",
                explanation: "更强调巧合性，较少强调价值发现",
                examples: [
                    SynonymExample(
                        sentence: "Meeting you here is quite a coincidence.",
                        translation: "在这里遇到你真是太巧了。"
                    )
                ]
            ),
            Synonym(
                word: "fortune",
                explanation: "强调运气和命运的安排",
                examples: [
                    SynonymExample(
                        sentence: "It was pure fortune that led to this discovery.",
                        translation: "纯粹是运气导致了这个发现。"
                    )
                ]
            )
        ]
        
        let mockContent = WordContent(
            difficulty: "intermediate",
            phoneticSymbols: [],
            coreDefinition: "意外发现有价值事物的能力",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "",
                emotionalResonance: "",
                vividImagery: "",
                etymologicalEssence: ""
            ),
            usageExamples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: mockSynonyms
        )
        
        let mockWordData = WordDefinitionResponse(
            word: "serendipity",
            metadata: WordMetadata(wordFrequency: "medium", relatedConcepts: []),
            content: mockContent,
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )
        
        // 初始化视图模型
        viewModel.initializeStage(with: mockWordData)
        
        return SynonymsCarouselComponent(viewModel: viewModel)
            .preferredColorScheme(.dark)
            .padding()
            .background(Color.black)
    }
}
#endif
