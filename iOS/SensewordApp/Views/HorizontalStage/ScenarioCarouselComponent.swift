//
//  ScenarioCarouselComponent.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  场景轮换组件 - 水平切换使用场景
//

import SwiftUI

/// 场景轮换组件
/// 核心功能：支持使用场景的水平切换交互
struct ScenarioCarouselComponent: View {
    
    // MARK: - 属性
    
    /// 水平舞台视图模型
    @ObservedObject var viewModel: HorizontalStageViewModel
    
    /// 拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("场景")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)
            
            // 场景内容区域
            scenarioContentArea
                .gesture(horizontalDragGesture)
            
            // 场景指示器
            scenarioIndicator
        }
        .frame(maxWidth: .infinity)
        .opacity(viewModel.scenarioCarouselState.isActive ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.3), value: viewModel.scenarioCarouselState.isActive)
    }
    
    // MARK: - 场景内容区域
    
    private var scenarioContentArea: some View {
        VStack(spacing: 16) {
            if let scenario = viewModel.currentScenario {
                // 场景分类
                Text(scenario.category)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                // 相关性标签
                HStack {
                    Text("相关性")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.secondary)
                    
                    Text(scenario.relevance)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(relevanceColor(for: scenario.relevance))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(relevanceColor(for: scenario.relevance).opacity(0.2))
                        )
                    
                    Spacer()
                }
                
                // 场景描述
                Text(scenario.context)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white)
                    .lineSpacing(4)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
            } else {
                // 空状态
                emptyStateView
            }
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
        .offset(x: dragOffset)
        .scaleEffect(isDragging ? 0.98 : 1.0)
        .animation(.easeOut(duration: 0.2), value: isDragging)
    }
    
    // MARK: - 场景指示器
    
    private var scenarioIndicator: some View {
        HStack(spacing: 8) {
            if let wordData = getWordData() {
                ForEach(0..<wordData.content.usageScenarios.count, id: \.self) { index in
                    Circle()
                        .fill(index == viewModel.scenarioCarouselState.currentIndex ? Color.white : Color.gray.opacity(0.5))
                        .frame(width: 6, height: 6)
                        .scaleEffect(index == viewModel.scenarioCarouselState.currentIndex ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: viewModel.scenarioCarouselState.currentIndex)
                }
            }
        }
    }
    
    // MARK: - 空状态视图
    
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "theatermasks")
                .font(.system(size: 24))
                .foregroundColor(Color.secondary)
            
            Text("暂无使用场景")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.secondary)
        }
        .frame(height: 80)
    }
    
    // MARK: - 手势处理
    
    private var horizontalDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                isDragging = true
                dragOffset = value.translation.width
            }
            .onEnded { value in
                isDragging = false
                handleDragEnd(translation: value.translation.width)
                
                withAnimation(.easeOut(duration: 0.3)) {
                    dragOffset = 0
                }
            }
    }
    
    // MARK: - 私有方法
    
    /// 处理拖拽结束
    private func handleDragEnd(translation: CGFloat) {
        let threshold: CGFloat = 50
        
        if translation > threshold {
            // 向右滑动 - 切换到上一个场景
            viewModel.switchToPreviousScenario()
            triggerHapticFeedback(.light)
        } else if translation < -threshold {
            // 向左滑动 - 切换到下一个场景
            viewModel.switchToNextScenario()
            triggerHapticFeedback(.light)
        }
    }
    
    /// 获取相关性颜色
    private func relevanceColor(for relevance: String) -> Color {
        switch relevance.lowercased() {
        case "高度相关", "high":
            return .green
        case "中度相关", "medium":
            return .orange
        case "低度相关", "low":
            return .red
        default:
            return .gray
        }
    }
    
    /// 获取单词数据
    private func getWordData() -> WordDefinitionResponse? {
        return viewModel.currentWordData
    }
    
    /// 触发触觉反馈
    private func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
}

// MARK: - 用法注释轮换组件

/// 用法注释轮换组件
struct UsageNotesCarouselComponent: View {
    
    // MARK: - 属性
    
    /// 水平舞台视图模型
    @ObservedObject var viewModel: HorizontalStageViewModel
    
    /// 拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("用法")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)
            
            // 用法注释内容区域
            usageNoteContentArea
                .gesture(horizontalDragGesture)
            
            // 用法注释指示器
            usageNoteIndicator
        }
        .frame(maxWidth: .infinity)
        .opacity(viewModel.usageNotesState.isActive ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.3), value: viewModel.usageNotesState.isActive)
    }
    
    // MARK: - 用法注释内容区域
    
    private var usageNoteContentArea: some View {
        VStack(spacing: 16) {
            if let usageNote = viewModel.currentUsageNote {
                // 用法方面
                Text(usageNote.aspect)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.white)
                
                // 用法解释
                Text(usageNote.explanation)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white)
                    .lineSpacing(4)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                // 示例（如果有）
                if !usageNote.examples.isEmpty {
                    VStack(spacing: 8) {
                        ForEach(0..<min(usageNote.examples.count, 2), id: \.self) { index in
                            let example = usageNote.examples[index]
                            VStack(spacing: 4) {
                                Text(example.sentence)
                                    .font(.system(size: 13, weight: .medium))
                                    .foregroundColor(Color.blue)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                
                                Text(example.translation)
                                    .font(.system(size: 12, weight: .regular))
                                    .foregroundColor(Color.secondary)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        }
                    }
                    .padding(.top, 8)
                }
            } else {
                // 空状态
                emptyUsageNoteView
            }
        }
        .padding(.vertical, 20)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
        .offset(x: dragOffset)
        .scaleEffect(isDragging ? 0.98 : 1.0)
        .animation(.easeOut(duration: 0.2), value: isDragging)
    }
    
    // MARK: - 用法注释指示器
    
    private var usageNoteIndicator: some View {
        HStack(spacing: 8) {
            if let wordData = getWordData() {
                ForEach(0..<wordData.content.usageNotes.count, id: \.self) { index in
                    Circle()
                        .fill(index == viewModel.usageNotesState.currentIndex ? Color.white : Color.gray.opacity(0.5))
                        .frame(width: 6, height: 6)
                        .scaleEffect(index == viewModel.usageNotesState.currentIndex ? 1.2 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: viewModel.usageNotesState.currentIndex)
                }
            }
        }
    }
    
    // MARK: - 空状态视图
    
    private var emptyUsageNoteView: some View {
        VStack(spacing: 12) {
            Image(systemName: "note.text")
                .font(.system(size: 24))
                .foregroundColor(Color.secondary)
            
            Text("暂无用法注释")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.secondary)
        }
        .frame(height: 80)
    }
    
    // MARK: - 手势处理
    
    private var horizontalDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                isDragging = true
                dragOffset = value.translation.width
            }
            .onEnded { value in
                isDragging = false
                handleDragEnd(translation: value.translation.width)
                
                withAnimation(.easeOut(duration: 0.3)) {
                    dragOffset = 0
                }
            }
    }
    
    // MARK: - 私有方法
    
    /// 处理拖拽结束
    private func handleDragEnd(translation: CGFloat) {
        let threshold: CGFloat = 50
        
        if translation > threshold || translation < -threshold {
            // 任意方向滑动都切换到下一个用法注释
            viewModel.switchToNextUsageNote()
            triggerHapticFeedback(.light)
        }
    }
    
    /// 获取单词数据
    private func getWordData() -> WordDefinitionResponse? {
        return viewModel.currentWordData
    }
    
    /// 触发触觉反馈
    private func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
}
