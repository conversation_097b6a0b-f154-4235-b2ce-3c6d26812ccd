//
//  ExampleStageComponent.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  例句水平舞台组件 - 支持短语分解的深度交互
//

import SwiftUI

/// 例句水平舞台组件
/// 核心功能：支持例句的水平滑动和短语分解深度交互
struct ExampleStageComponent: View {
    
    // MARK: - 属性
    
    /// 水平舞台视图模型
    @ObservedObject var viewModel: HorizontalStageViewModel
    
    /// 拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    /// 呼吸动画状态
    @StateObject private var breathingState = BreathingHintState()
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("例句")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.senseWordGray400)
            
            // 例句内容区域
            exampleContentArea
                .gesture(horizontalDragGesture)
        }
        .frame(maxWidth: .infinity)
        .onAppear {
            startBreathingAnimation()
        }
        .onDisappear {
            stopBreathingAnimation()
        }
    }
    
    // MARK: - 例句内容区域
    
    private var exampleContentArea: some View {
        VStack(spacing: 16) {
            // 分类标题
            if !viewModel.currentExampleCategoryTitle.isEmpty {
                Text(viewModel.currentExampleCategoryTitle)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.senseWordGray400)
            }
            
            // 例句显示区域
            if viewModel.exampleStageState.isInPhraseMode {
                // 短语分解模式
                phraseBreakdownView
            } else {
                // 完整例句模式
                fullExampleView
            }
        }
        .padding(.horizontal, 20)
    }
    
    // MARK: - 完整例句视图
    
    private var fullExampleView: some View {
        VStack(spacing: 12) {
            if let example = viewModel.currentExample {
                // 英文例句
                HStack {
                    Text(example.english)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    // 呼吸动画提示
                    if viewModel.exampleStageState.showBreathingHint && 
                       example.phraseBreakdown != nil && 
                       !example.phraseBreakdown!.isEmpty {
                        breathingHintView
                    }
                }
                
                // 中文翻译
                Text(example.translation)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.senseWordGray300)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    // MARK: - 短语分解视图
    
    private var phraseBreakdownView: some View {
        VStack(spacing: 16) {
            if let breakdownState = viewModel.exampleStageState.phraseBreakdownState {
                // 高亮文本显示
                highlightedTextView(breakdownState: breakdownState)
                
                // 当前短语信息
                if let currentPhrase = breakdownState.currentPhrase {
                    VStack(spacing: 8) {
                        // 短语文本
                        Text(currentPhrase.phrase)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.blue)
                        
                        // 短语翻译
                        Text(currentPhrase.translation)
                            .font(.system(size: 14, weight: .regular))
                            .foregroundColor(.senseWordGray300)
                    }
                    .padding(.vertical, 12)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.1))
                    )
                }
                
                // 进度指示器
                progressIndicator(breakdownState: breakdownState)
            }
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }
    
    // MARK: - 高亮文本视图
    
    @ViewBuilder
    private func highlightedTextView(breakdownState: PhraseBreakdownState) -> some View {
        let segments = createHighlightedSegments(
            fullText: breakdownState.currentExample.english,
            highlightedPhrase: breakdownState.highlightedPhrase
        )
        
        HStack(spacing: 0) {
            ForEach(segments) { segment in
                Text(segment.text)
                    .font(.system(size: 16, weight: segment.isHighlighted ? .semibold : .medium))
                    .foregroundColor(segment.isHighlighted ? .blue : .white)
                    .opacity(segment.opacity)
            }
        }
        .multilineTextAlignment(.leading)
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
    // MARK: - 进度指示器
    
    private func progressIndicator(breakdownState: PhraseBreakdownState) -> some View {
        HStack(spacing: 4) {
            ForEach(0..<breakdownState.phrases.count, id: \.self) { index in
                Circle()
                    .fill(index == breakdownState.currentPhraseIndex ? Color.blue : Color.gray.opacity(0.3))
                    .frame(width: 6, height: 6)
                    .scaleEffect(index == breakdownState.currentPhraseIndex ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: breakdownState.currentPhraseIndex)
            }
        }
    }
    
    // MARK: - 呼吸动画提示
    
    private var breathingHintView: some View {
        Circle()
            .fill(Color.blue.opacity(0.3))
            .frame(width: 12, height: 12)
            .scaleEffect(breathingState.scale)
            .opacity(breathingState.opacity)
            .shadow(color: .blue, radius: breathingState.glowRadius)
            .animation(
                Animation.easeInOut(duration: breathingState.animationDuration)
                    .repeatForever(autoreverses: true),
                value: breathingState.scale
            )
    }
    
    // MARK: - 手势处理
    
    private var horizontalDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                isDragging = true
                dragOffset = value.translation.width
            }
            .onEnded { value in
                isDragging = false
                handleDragEnd(translation: value.translation.width)
                dragOffset = 0
            }
    }
    
    // MARK: - 私有方法
    
    /// 处理拖拽结束 - 统一的左滑手势逻辑
    private func handleDragEnd(translation: CGFloat) {
        let threshold: CGFloat = 50

        guard let example = viewModel.currentExample else { return }

        if translation < -threshold {
            // 左滑：统一处理短语切换逻辑
            handleLeftSwipeForPhrase(example: example)
            triggerHapticFeedback(.medium)
        } else if translation > threshold {
            // 右滑：返回上一个短语或退出短语模式
            handleRightSwipeForPhrase(example: example)
            triggerHapticFeedback(.light)
        }
    }

    /// 处理左滑短语切换 - 与 ExamplesSubPageContainer 保持一致
    private func handleLeftSwipeForPhrase(example: UsageExample) {
        // 检查是否有短语分解数据
        guard let phrases = example.phraseBreakdown, !phrases.isEmpty else {
            print("[ExampleStageComponent] 该例句没有短语分解数据")
            return
        }

        if !viewModel.exampleStageState.isInPhraseMode {
            // 首次进入短语模式：显示第一个短语
            viewModel.enterPhraseBreakdownMode(
                categoryIndex: viewModel.exampleStageState.currentCategoryIndex,
                exampleIndex: viewModel.exampleStageState.currentExampleIndex
            )
            print("[ExampleStageComponent] 进入短语模式，显示第一个短语")
        } else {
            // 已在短语模式：切换到下一个短语
            let currentPhraseIndex = viewModel.exampleStageState.phraseBreakdownState?.currentPhraseIndex ?? 0
            if currentPhraseIndex < phrases.count - 1 {
                viewModel.switchToNextPhrase()
                print("[ExampleStageComponent] 切换到下一个短语: \(currentPhraseIndex + 1)")
            } else {
                // 已是最后一个短语：回到完整例句
                viewModel.exitPhraseBreakdownMode()
                print("[ExampleStageComponent] 完成所有短语，回到完整例句")
            }
        }
    }

    /// 处理右滑短语切换 - 与 ExamplesSubPageContainer 保持一致
    private func handleRightSwipeForPhrase(example: UsageExample) {
        if viewModel.exampleStageState.isInPhraseMode {
            let currentPhraseIndex = viewModel.exampleStageState.phraseBreakdownState?.currentPhraseIndex ?? 0
            if currentPhraseIndex > 0 {
                // 返回上一个短语
                viewModel.switchToPreviousPhrase()
                print("[ExampleStageComponent] 返回上一个短语: \(currentPhraseIndex - 1)")
            } else {
                // 已是第一个短语：退出短语模式，回到完整例句
                viewModel.exitPhraseBreakdownMode()
                print("[ExampleStageComponent] 退出短语模式，回到完整例句")
            }
        }
        // 如果不在短语模式，右滑不做任何操作
    }
    
    /// 创建高亮文本片段
    private func createHighlightedSegments(fullText: String, highlightedPhrase: String) -> [HighlightedTextSegment] {
        var segments: [HighlightedTextSegment] = []
        
        if let range = fullText.range(of: highlightedPhrase, options: .caseInsensitive) {
            // 高亮短语之前的文本
            let beforeText = String(fullText[..<range.lowerBound])
            if !beforeText.isEmpty {
                segments.append(HighlightedTextSegment(text: beforeText, isHighlighted: false, opacity: 0.6))
            }
            
            // 高亮的短语
            let highlightedText = String(fullText[range])
            segments.append(HighlightedTextSegment(text: highlightedText, isHighlighted: true, opacity: 1.0))
            
            // 高亮短语之后的文本
            let afterText = String(fullText[range.upperBound...])
            if !afterText.isEmpty {
                segments.append(HighlightedTextSegment(text: afterText, isHighlighted: false, opacity: 0.6))
            }
        } else {
            // 如果没有找到匹配的短语，返回完整文本
            segments.append(HighlightedTextSegment(text: fullText, isHighlighted: false, opacity: 1.0))
        }
        
        return segments
    }
    
    /// 开始呼吸动画
    private func startBreathingAnimation() {
        breathingState.shouldShow = true
        breathingState.scale = 1.0
        breathingState.opacity = 0.6
        
        withAnimation(
            Animation.easeInOut(duration: breathingState.animationDuration)
                .repeatForever(autoreverses: true)
        ) {
            breathingState.scale = 1.3
            breathingState.opacity = 0.9
        }
    }
    
    /// 停止呼吸动画
    private func stopBreathingAnimation() {
        breathingState.shouldShow = false
        withAnimation(.easeOut(duration: 0.3)) {
            breathingState.scale = 1.0
            breathingState.opacity = 0.0
        }
    }
    
    /// 触发触觉反馈
    private func triggerHapticFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impactFeedback = UIImpactFeedbackGenerator(style: style)
        impactFeedback.impactOccurred()
    }
}
