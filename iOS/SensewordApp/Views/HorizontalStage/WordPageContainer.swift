//
//  WordPageContainer.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-28.
//  单词页面容器 - 5个独立页面的水平切换架构
//

import SwiftUI
import UIComponents

/// 单词页面容器
/// 核心功能：实现5个独立页面的水平切换：深思语境、例句、场景、用法、同义词
/// 专注于水平切换功能，垂直卡片切换由WordDetailContainer统一管理
struct WordPageContainer: View {

    // MARK: - 属性

    /// 单词定义数据
    @State var wordData: WordDefinitionResponse

    /// 单词数据更新回调
    var onWordDataUpdate: ((WordDefinitionResponse) -> Void)?

    /// 当前页面索引
    @State private var currentPageIndex: Int = 0

    /// 全局音频管理器
    @ObservedObject private var globalAudioManager = GlobalAudioManager.shared

    // 移除垂直上拉无限内容流组件，保留水平切换功能
    // 垂直卡片切换由WordDetailContainer统一管理
    
    /// 页面类型枚举
    enum PageType: Int, CaseIterable {
        case deepContext = 0
        case examples = 1
        case scenarios = 2
        case collocations = 3
        case usageNotes = 4
        case synonyms = 5

        var title: String {
            switch self {
            case .deepContext: return "深思语境"
            case .examples: return "例句"
            case .scenarios: return "场景"
            case .collocations: return "搭配"
            case .usageNotes: return "用法"
            case .synonyms: return "同义词"
            }
        }
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 页面指示器
                pageIndicator

                // 页面内容
                TabView(selection: $currentPageIndex) {
                    // 深思语境页面
                    DeepContextPage(wordData: wordData)
                        .tag(0)

                    // 例句页面
                    ExamplesPage(wordData: wordData)
                        .tag(1)

                    // 场景页面
                    ScenariosPage(wordData: wordData)
                        .tag(2)

                    // 搭配词组页面
                    CollocationsPage(wordData: wordData)
                        .tag(3)

                    // 用法页面
                    UsageNotesPage(wordData: wordData)
                        .tag(4)

                    // 同义词页面
                    SynonymsPage(wordData: wordData)
                        .tag(5)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.3), value: currentPageIndex)
            }
            .background(Color.clear)

        }
    }
    
    // MARK: - 页面指示器
    
    private var pageIndicator: some View {
        HStack(spacing: 8) {
            ForEach(PageType.allCases, id: \.rawValue) { pageType in
                Circle()
                    .fill(currentPageIndex == pageType.rawValue ? Color.white : Color.gray.opacity(0.5))
                    .frame(width: 6, height: 6)
                    .scaleEffect(currentPageIndex == pageType.rawValue ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: currentPageIndex)
            }
        }
        .padding(.bottom, 16)
    }

    // 移除垂直上拉相关视图，保留水平切换功能
    // 垂直卡片切换的UI由WordDetailContainer统一管理

    // 移除垂直上拉无限内容流方法，保留水平切换功能
    // 垂直卡片切换逻辑由WordDetailContainer统一管理

    // 移除垂直上拉相关方法，保留水平切换功能
    // 单词切换逻辑由WordDetailContainer统一管理
}

// MARK: - 深思语境页面

/// 深思语境页面
/// 显示完整的释义、想象、词源内容
struct DeepContextPage: View {
    let wordData: WordDefinitionResponse
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // 页面标题
                Text("深思语境")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .center)
                
                // 核心释义
                VStack(alignment: .leading, spacing: 12) {
                    Text("释义")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.senseWordGray400)

                    Text(wordData.content.coreDefinition)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineSpacing(4)
                }

                // 母语者意图
                VStack(alignment: .leading, spacing: 12) {
                    Text("意图")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.senseWordGray400)

                    Text(wordData.content.contextualExplanation.nativeSpeakerIntent)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineSpacing(4)
                }

                // 想象描述
                VStack(alignment: .leading, spacing: 12) {
                    Text("想象")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.senseWordGray400)

                    Text(wordData.content.contextualExplanation.vividImagery)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineSpacing(4)
                }

                // 词源解释
                VStack(alignment: .leading, spacing: 12) {
                    Text("词源")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.senseWordGray400)

                    Text(wordData.content.contextualExplanation.etymologicalEssence)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white)
                        .lineSpacing(4)
                }
                
                Spacer(minLength: 50)
            }
            .padding(.horizontal, 20)
        }
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }
}

// MARK: - 例句页面

/// 例句页面
/// 支持例句浏览和左滑短语分解
struct ExamplesPage: View {
    let wordData: WordDefinitionResponse

    var body: some View {
        VStack(spacing: 0) {
            // 页面标题
            Text("例句")
                .font(.system(size: 18, weight: .bold))
                .foregroundColor(.white)
                .padding(.bottom, 24)

            // 例句子PageContainer
            if !wordData.content.usageExamples.isEmpty {
                ExamplesSubPageContainer(wordData: wordData)
            } else {
                emptyExamplesView
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .padding(.horizontal, 16)
    }

    private var emptyExamplesView: some View {
        VStack(spacing: 12) {
            Image(systemName: "quote.bubble")
                .font(.system(size: 24))
                .foregroundColor(.senseWordGray400)

            Text("暂无例句")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)
        }
        .frame(height: 120)
    }
}

// MARK: - 例句子PageContainer

/// 例句子PageContainer
/// 支持多个例句之间的水平滑动切换
struct ExamplesSubPageContainer: View {
    let wordData: WordDefinitionResponse

    @State private var currentExampleGlobalIndex: Int = 0
    @State private var isInPhraseMode: Bool = false
    @State private var currentPhraseIndex: Int = 0

    /// 全局音频管理器
    @ObservedObject private var globalAudioManager = GlobalAudioManager.shared

    // 计算所有例句的扁平化数组
    private var allExamples: [(category: String, example: UsageExample)] {
        var result: [(String, UsageExample)] = []
        for category in wordData.content.usageExamples {
            for example in category.examples {
                result.append((category.category, example))
            }
        }
        return result
    }

    var body: some View {
        VStack(spacing: 0) {
            if !allExamples.isEmpty {
                // 例句内容
                TabView(selection: $currentExampleGlobalIndex) {
                    ForEach(0..<allExamples.count, id: \.self) { index in
                        let exampleData = allExamples[index]

                        SingleExampleView(
                            category: exampleData.category,
                            example: exampleData.example,
                            onPlayAudio: playExampleAudioOnAppear,
                            isInPhraseMode: $isInPhraseMode,
                            currentPhraseIndex: $currentPhraseIndex
                        )
                        .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .animation(.easeInOut(duration: 0.3), value: currentExampleGlobalIndex)
                .onChange(of: currentExampleGlobalIndex) { newIndex in
                    // 切换例句时重置短语分解状态
                    isInPhraseMode = false
                    currentPhraseIndex = 0

                    // 自动播放新例句的音频
                    if newIndex < allExamples.count {
                        let newExample = allExamples[newIndex].example
                        playExampleAudioOnAppear(example: newExample)
                    }
                }
                .onAppear {
                    // 进入例句页面时自动播放第一个例句
                    if !allExamples.isEmpty {
                        let firstExample = allExamples[0].example
                        playExampleAudioOnAppear(example: firstExample)
                    }
                }
            }
        }
    }

    private func playExampleAudioOnAppear(example: UsageExample) {
        guard let audioUrlString = example.audioUrl,
              !audioUrlString.isEmpty else {
            print("🎵 ExamplesSubPageContainer: 例句音频URL不可用")
            return
        }

        print("🎵 ExamplesSubPageContainer: 自动播放例句音频: \(example.english)")

        // 使用全局音频管理器播放音频
        Task {
            do {
                try await globalAudioManager.playSingleAudio(description: "例句: \(example.english)", audioUrl: audioUrlString)
                print("✅ ExamplesSubPageContainer: 例句音频播放成功")
            } catch {
                print("❌ ExamplesSubPageContainer: 例句音频播放失败 - \(error)")
            }
        }
    }

}
    
// MARK: - 单个例句视图

/// 单个例句视图
/// 支持短语分解交互
struct SingleExampleView: View {
    let category: String
    let example: UsageExample
    let onPlayAudio: (UsageExample) -> Void

    @Binding var isInPhraseMode: Bool
    @Binding var currentPhraseIndex: Int

    /// 全局音频管理器
    @ObservedObject private var globalAudioManager = GlobalAudioManager.shared

    var body: some View {
        VStack(spacing: 20) {
            // 分类标题
            Text(category)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)

            // 例句内容
            VStack(spacing: 16) {
                // 英文例句（支持短语高亮）
                if isInPhraseMode && example.phraseBreakdown != nil && !example.phraseBreakdown!.isEmpty {
                    // 短语分解模式：高亮当前短语，其他变暗
                    highlightedSentenceView(example: example, currentPhraseIndex: currentPhraseIndex)
                } else {
                    // 完整例句模式
                    Text(example.english)
                        .font(.system(size: 24, weight: .regular))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                        .lineSpacing(6)
                        .onTapGesture {
                            onPlayAudio(example)
                        }
                }

                // 中文翻译（根据模式显示不同内容）
                if isInPhraseMode && example.phraseBreakdown != nil && !example.phraseBreakdown!.isEmpty && currentPhraseIndex < example.phraseBreakdown!.count {
                    // 显示当前短语的翻译
                    let currentPhrase = example.phraseBreakdown![currentPhraseIndex]
                    Text(currentPhrase.translation)
                        .font(.system(size: 24, weight: .regular))
                        .foregroundColor(.senseWordGray300)
                        .multilineTextAlignment(.center)
                        .lineSpacing(6)
                } else {
                    // 显示完整句子翻译
                    Text(example.translation)
                        .font(.system(size: 24, weight: .regular))
                        .foregroundColor(.senseWordGray300)
                        .multilineTextAlignment(.center)
                        .lineSpacing(6)
                }

                // 提示文字
                if !isInPhraseMode && example.phraseBreakdown != nil && !example.phraseBreakdown!.isEmpty {
                    Text("← 左滑学习短语分解")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.senseWordGray500)
                        .padding(.top, 8)
                } else if isInPhraseMode {
                    Text("← 左滑下一个短语，右滑上一个短语")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.senseWordGray500)
                        .multilineTextAlignment(.center)
                        .padding(.top, 8)
                }
            }
            .padding(.vertical, 20)
            .contentShape(Rectangle()) // 确保整个区域都能响应手势
            .gesture(
                DragGesture()
                    .onEnded { value in
                        handleUnifiedExampleSwipe(
                            translation: value.translation.width,
                            example: example
                        )
                    }
            )

            Spacer()
        }
    }

    /// 统一的例句手势处理 - 简化交互逻辑
    private func handleUnifiedExampleSwipe(translation: CGFloat, example: UsageExample) {
        let threshold: CGFloat = 50

        if translation < -threshold {
            // 左滑：统一处理短语切换逻辑
            handleLeftSwipeForPhrase(example: example)
        } else if translation > threshold {
            // 右滑：返回上一个短语或退出短语模式
            handleRightSwipeForPhrase(example: example)
        }
    }

    /// 处理左滑短语切换
    private func handleLeftSwipeForPhrase(example: UsageExample) {
        // 检查是否有短语分解数据
        guard let phrases = example.phraseBreakdown, !phrases.isEmpty else {
            print("[ExamplesSubPageContainer] 该例句没有短语分解数据")
            return
        }

        if !isInPhraseMode {
            // 首次进入短语模式：显示第一个短语
            isInPhraseMode = true
            currentPhraseIndex = 0
            playPhraseAudio(example: example, phraseIndex: 0)
            print("[ExamplesSubPageContainer] 进入短语模式，显示第一个短语")
        } else {
            // 已在短语模式：切换到下一个短语
            if currentPhraseIndex < phrases.count - 1 {
                currentPhraseIndex += 1
                playPhraseAudio(example: example, phraseIndex: currentPhraseIndex)
                print("[ExamplesSubPageContainer] 切换到下一个短语: \(currentPhraseIndex)")
            } else {
                // 已是最后一个短语：回到完整例句
                exitPhraseBreakdownMode()
                onPlayAudio(example)
                print("[ExamplesSubPageContainer] 完成所有短语，回到完整例句")
            }
        }
    }

    /// 处理右滑短语切换
    private func handleRightSwipeForPhrase(example: UsageExample) {
        if isInPhraseMode {
            if currentPhraseIndex > 0 {
                // 返回上一个短语
                currentPhraseIndex -= 1
                playPhraseAudio(example: example, phraseIndex: currentPhraseIndex)
                print("[ExamplesSubPageContainer] 返回上一个短语: \(currentPhraseIndex)")
            } else {
                // 已是第一个短语：退出短语模式，回到完整例句
                exitPhraseBreakdownMode()
                onPlayAudio(example)
                print("[ExamplesSubPageContainer] 退出短语模式，回到完整例句")
            }
        }
        // 如果不在短语模式，右滑不做任何操作
    }



    private func exitPhraseBreakdownMode() {
        isInPhraseMode = false
        currentPhraseIndex = 0
    }

    private func playExampleAudio(example: UsageExample) {
        guard let audioUrlString = example.audioUrl,
              !audioUrlString.isEmpty else {
            print("🎵 SingleExampleView: 例句音频URL不可用")
            return
        }

        print("🎵 SingleExampleView: 播放例句音频: \(example.english)")

        // 使用统一的 GlobalAudioManager 播放音频，确保音频不冲突
        Task {
            do {
                try await globalAudioManager.playSingleAudio(
                    description: "例句: \(example.english)",
                    audioUrl: audioUrlString
                )
                print("✅ SingleExampleView: 例句音频播放成功")
            } catch {
                print("❌ SingleExampleView: 例句音频播放失败 - \(error)")
            }
        }
    }

    private func playPhraseAudio(example: UsageExample, phraseIndex: Int) {
        guard let phrases = example.phraseBreakdown,
              phraseIndex < phrases.count,
              let audioUrlString = phrases[phraseIndex].audioUrl,
              !audioUrlString.isEmpty else {
            print("🎵 SingleExampleView: 短语音频URL不可用")
            return
        }

        print("🎵 SingleExampleView: 播放短语音频: \(phrases[phraseIndex].phrase)")

        // 使用统一的 GlobalAudioManager 播放音频，确保音频不冲突
        Task {
            do {
                try await globalAudioManager.playSingleAudio(
                    description: "短语: \(phrases[phraseIndex].phrase)",
                    audioUrl: audioUrlString
                )
                print("✅ SingleExampleView: 短语音频播放成功")
            } catch {
                print("❌ SingleExampleView: 短语音频播放失败 - \(error)")
            }
        }
    }

    // MARK: - 短语高亮视图

    @ViewBuilder
    private func highlightedSentenceView(example: UsageExample, currentPhraseIndex: Int) -> some View {
        if let phrases = example.phraseBreakdown, !phrases.isEmpty && currentPhraseIndex < phrases.count {
            let currentPhrase = phrases[currentPhraseIndex]
            let sentence = example.english

            // 创建高亮文本
            let highlightedText = createHighlightedText(
                sentence: sentence,
                highlightPhrase: currentPhrase.phrase
            )

            // 高亮的句子（移除进度指示器）
            highlightedText
                .font(.system(size: 24, weight: .regular))
                .multilineTextAlignment(.center)
                .lineSpacing(6)
        } else {
            Text(example.english)
                .font(.system(size: 24, weight: .regular))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineSpacing(6)
        }
    }

    /// 创建高亮文本
    private func createHighlightedText(sentence: String, highlightPhrase: String) -> Text {
        let lowercaseSentence = sentence.lowercased()
        let lowercasePhrase = highlightPhrase.lowercased()

        if let range = lowercaseSentence.range(of: lowercasePhrase) {
            let beforePhrase = String(sentence[..<range.lowerBound])
            let phraseText = String(sentence[range])
            let afterPhrase = String(sentence[range.upperBound...])

            return Text(beforePhrase)
                .foregroundColor(.white.opacity(0.4)) +
            Text(phraseText)
                .foregroundColor(.white)
                .fontWeight(.bold) +
            Text(afterPhrase)
                .foregroundColor(.white.opacity(0.4))
        } else {
            // 如果找不到短语，显示原句
            return Text(sentence)
                .foregroundColor(.white)
        }
    }
}

// MARK: - 场景页面

/// 场景页面
/// 显示使用场景内容
struct ScenariosPage: View {
    let wordData: WordDefinitionResponse

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // 页面标题
                Text("场景")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .center)

                if !wordData.content.usageScenarios.isEmpty {
                    ForEach(0..<wordData.content.usageScenarios.count, id: \.self) { index in
                        let scenario = wordData.content.usageScenarios[index]
                        scenarioCard(scenario: scenario)
                    }
                } else {
                    emptyScenariosView
                }

                Spacer(minLength: 50)
            }
            .padding(.horizontal, 20)
        }
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }

    @ViewBuilder
    private func scenarioCard(scenario: UsageScenario) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            // 场景分类
            Text(scenario.category)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            // 相关性标签
            HStack {
                Text("相关性")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.senseWordGray400)

                Text(scenario.relevance)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(relevanceColor(for: scenario.relevance))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 4)
                            .fill(relevanceColor(for: scenario.relevance).opacity(0.2))
                    )

                Spacer()
            }

            // 场景描述
            Text(scenario.context)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var emptyScenariosView: some View {
        VStack(spacing: 12) {
            Image(systemName: "theatermasks")
                .font(.system(size: 24))
                .foregroundColor(.senseWordGray400)

            Text("暂无使用场景")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
    }

    private func relevanceColor(for relevance: String) -> Color {
        switch relevance.lowercased() {
        case "高度相关", "high":
            return .green
        case "中度相关", "medium":
            return .orange
        case "低度相关", "low":
            return .red
        default:
            return .gray
        }
    }
}

// MARK: - 用法页面

/// 用法页面
/// 显示用法注释内容
struct UsageNotesPage: View {
    let wordData: WordDefinitionResponse

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // 页面标题
                Text("用法")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .center)

                if !wordData.content.usageNotes.isEmpty {
                    ForEach(0..<wordData.content.usageNotes.count, id: \.self) { index in
                        let usageNote = wordData.content.usageNotes[index]
                        usageNoteCard(usageNote: usageNote)
                    }
                } else {
                    emptyUsageNotesView
                }

                Spacer(minLength: 50)
            }
            .padding(.horizontal, 20)
        }
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }

    @ViewBuilder
    private func usageNoteCard(usageNote: UsageNote) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 用法方面
            Text(usageNote.aspect)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            // 用法解释
            Text(usageNote.explanation)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)

            // 示例（如果有）
            if !usageNote.examples.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(0..<usageNote.examples.count, id: \.self) { index in
                        let example = usageNote.examples[index]
                        VStack(alignment: .leading, spacing: 6) {
                            Text(example.sentence)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)

                            Text(example.translation)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.senseWordGray300)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)
                        }
                    }
                }
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var emptyUsageNotesView: some View {
        VStack(spacing: 12) {
            Image(systemName: "note.text")
                .font(.system(size: 24))
                .foregroundColor(.senseWordGray400)

            Text("暂无用法注释")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 搭配词组页面

/// 搭配词组页面
/// 显示搭配词组内容
struct CollocationsPage: View {
    let wordData: WordDefinitionResponse

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // 页面标题
                Text("搭配")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .center)

                if !wordData.content.collocations.isEmpty {
                    ForEach(0..<wordData.content.collocations.count, id: \.self) { index in
                        let collocation = wordData.content.collocations[index]
                        collocationCard(collocation: collocation)
                    }
                } else {
                    emptyCollocationsView
                }

                Spacer(minLength: 50)
            }
            .padding(.horizontal, 20)
        }
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }

    @ViewBuilder
    private func collocationCard(collocation: Collocation) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 搭配类型
            Text(collocation.type)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            // 搭配示例
            if !collocation.examples.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(0..<collocation.examples.count, id: \.self) { index in
                        let example = collocation.examples[index]
                        VStack(alignment: .leading, spacing: 6) {
                            Text(example.collocation)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)

                            Text(example.translation)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.senseWordGray300)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)
                        }
                    }
                }
            }
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 20)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var emptyCollocationsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "link.circle")
                .font(.system(size: 24))
                .foregroundColor(.senseWordGray400)

            Text("暂无搭配词组")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 同义词页面

/// 同义词页面
/// 显示同义词内容
struct SynonymsPage: View {
    let wordData: WordDefinitionResponse

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // 页面标题
                Text("同义词")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity, alignment: .center)

                if !wordData.content.synonyms.isEmpty {
                    ForEach(0..<wordData.content.synonyms.count, id: \.self) { index in
                        let synonym = wordData.content.synonyms[index]
                        synonymCard(synonym: synonym)
                    }
                } else {
                    emptySynonymsView
                }

                Spacer(minLength: 50)
            }
            .padding(.horizontal, 20)
        }
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }

    @ViewBuilder
    private func synonymCard(synonym: Synonym) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 同义词单词
            Text(synonym.word)
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.white)

            // 同义词解释
            Text(synonym.explanation)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)

            // 示例（如果有）
            if !synonym.examples.isEmpty {
                VStack(alignment: .leading, spacing: 12) {
                    ForEach(0..<synonym.examples.count, id: \.self) { index in
                        let example = synonym.examples[index]
                        VStack(alignment: .leading, spacing: 6) {
                            Text(example.sentence)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)

                            Text(example.translation)
                                .font(.system(size: 16, weight: .regular))
                                .foregroundColor(.senseWordGray300)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .lineSpacing(4)
                        }
                    }
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
        )
    }

    private var emptySynonymsView: some View {
        VStack(spacing: 12) {
            Image(systemName: "link")
                .font(.system(size: 24))
                .foregroundColor(.senseWordGray400)

            Text("暂无同义词")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.senseWordGray400)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
    }
}
