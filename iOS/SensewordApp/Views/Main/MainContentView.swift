//
//  MainContentView.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-28.
//  主界面内容视图 - 集成搜索覆盖层的主界面
//

import SwiftUI
import UIComponents

// MARK: - Color 扩展

extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - 主界面内容视图

/// 主界面内容视图
/// 核心职责：提供主要内容展示，集成搜索覆盖层，支持手势触发搜索
/// 技术特点：支持搜索覆盖层、背景模糊、动画效果、响应式布局
/// 架构特点：遵循Apple性能指南，使用状态机管理搜索服务生命周期
struct MainContentView: View {

    // MARK: - 状态管理

    /// 搜索协调器（管理搜索服务的异步初始化）
    @StateObject private var searchCoordinator = SearchCoordinator()

    /// 是否显示搜索覆盖层
    @State private var showSearchOverlay: Bool = false

    /// 当前显示的单词内容
    @State private var currentWordContent: WordDefinitionResponse? = nil

    /// 是否显示单词结果页面
    @State private var showWordResult: Bool = false

    /// 是否正在加载单词内容
    @State private var isLoadingWord: Bool = false

    /// 动画触发器 - 每次改变值都会触发新的动画
    @State private var animationTrigger: Int = 0





    /// 动画重复任务 - 用于取消动画重复
    @State private var animationRepeatTask: Task<Void, Never>? = nil



    // MARK: - 计算属性

    /// 当前显示的单词数据
    private var currentDisplayedWordData: WordDefinitionResponse? {
        if showWordResult, let wordContent = currentWordContent {
            return wordContent
        }
        return nil
    }

    /// 是否正在显示单词卡片状态
    private var isShowingWordCardState: Bool {
        return !isLoadingWord && currentDisplayedWordData != nil
    }

    // MARK: - 初始化

    /// 初始化主界面内容视图
    /// 遵循Apple指南：构造函数中不执行耗时操作
    init() {
        // 完全无副作用的初始化
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        ZStack {
            // 主要内容区域
            mainContent
                .blur(radius: showSearchOverlay ? 10 : 0)
                .animation(.easeInOut(duration: 0.3), value: showSearchOverlay)

            // 搜索覆盖层（状态驱动显示）
            searchOverlayView
        }
        .onAppear {
            setupView()
        }
    }

    /// 搜索覆盖层视图（状态驱动）
    /// 根据搜索协调器的状态显示不同内容
    @ViewBuilder
    private var searchOverlayView: some View {
        switch searchCoordinator.state {
        case .notInitialized:
            // 未初始化时不显示任何内容
            EmptyView()

        case .initializing:
            // 初始化中显示加载指示器（可选）
            if showSearchOverlay {
                searchLoadingView
            }

        case .ready(let service):
            // 服务就绪时始终显示搜索界面（包含浮动按钮）
            SearchView(
                searchService: service,
                localBookmarkService: createLocalBookmarkService(),
                currentWordData: currentDisplayedWordData,
                isShowingWordCard: isShowingWordCardState
            )
                .onReceive(NotificationCenter.default.publisher(for: .searchResultSelected)) { notification in
                    if let word = notification.object as? String {
                        handleSearchResult(word: word)
                    }
                }

        case .failed(let error):
            // 初始化失败时显示错误信息（可选）
            if showSearchOverlay {
                searchErrorView(error: error)
            }
        }
    }

    /// 搜索加载视图
    private var searchLoadingView: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                ProgressView()
                    .scaleEffect(1.2)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))

                Text("正在准备搜索功能...")
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .medium))
            }
            .padding(24)
            .background(.ultraThinMaterial)
            .cornerRadius(16)
        }
    }

    /// 搜索错误视图
    private func searchErrorView(error: Error) -> some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()

            VStack(spacing: 16) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 32))
                    .foregroundColor(.orange)

                Text("搜索功能暂时不可用")
                    .foregroundColor(.white)
                    .font(.system(size: 16, weight: .medium))

                Button("重试") {
                    searchCoordinator.reset()
                    searchCoordinator.initializeIfNeeded()
                }
                .foregroundColor(.blue)
                .font(.system(size: 14, weight: .medium))
            }
            .padding(24)
            .background(.ultraThinMaterial)
            .cornerRadius(16)
        }
    }

    /// 加载内容视图
    /// 在搜索单词时显示空白页面，背景动画会持续播放直到内容加载完成
    private var loadingContentView: some View {
        // 完全空白的视图，只依靠背景动画来提示加载状态
        Color.clear
    }

    // MARK: - 主要内容
    
    private var mainContent: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 健康风格背景
                ZStack {
                    // 使用关键帧动画背景
                    AnimatableWallpaperWrapper(animationTrigger: animationTrigger)

                    // 内容层
                    if showWordResult, let wordContent = currentWordContent {
                        // 搜索结果的单词学习页面 - 使用统一的单词详情容器
                        WordDetailContainer(
                            currentWordData: wordContent,
                            onDismiss: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showWordResult = false
                                    currentWordContent = nil
                                }
                            }
                        )
                        .transition(.asymmetric(
                            insertion: .move(edge: .bottom).combined(with: .opacity),
                            removal: .move(edge: .bottom).combined(with: .opacity)
                        ))
                    } else if isLoadingWord {
                        // 搜索加载状态：显示空白页面，背景动画会自动播放，不显示每日单词
                        loadingContentView


                    } else {
                        // 默认主页内容（错误状态或无内容时）
                        contentLayer
                    }
                }
            }
        }
        .ignoresSafeArea()
        .onAppear {
            // 在视图出现时立即开始初始化，但不阻塞UI
            searchCoordinator.initializeIfNeeded()
        }
    }
    
    // MARK: - 内容层

    private var contentLayer: some View {
        VStack(spacing: 24) {
            Spacer()



            Spacer()
        }
    }
    

}

// MARK: - 私有方法

private extension MainContentView {
    
    /// 设置视图
    func setupView() {
        // 遵循Apple指南：视图设置中不执行耗时操作
        // 搜索服务将在用户手势触发时按需初始化
    }

    /// 创建本地收藏服务
    /// - Returns: 本地收藏服务实例
    private func createLocalBookmarkService() -> LocalBookmarkServiceProtocol {
        // 暂时返回一个Mock实现，避免复杂的异步初始化
        // TODO: 在后续版本中集成到SearchCoordinator的统一初始化流程中
        return MockLocalBookmarkService()
    }


    
    /// 处理搜索结果
    /// - Parameter word: 选中的单词
    func handleSearchResult(word: String) {
        // 关闭搜索覆盖层
        withAnimation(.easeInOut(duration: 0.3)) {
            showSearchOverlay = false
        }

        // 加载单词内容
        loadWordContent(word: word)
    }

    /// 加载单词内容
    /// - Parameter word: 单词
    func loadWordContent(word: String) {
        // 获取搜索服务
        guard case .ready(let searchService) = searchCoordinator.state else {
            print("❌ 搜索服务未就绪，无法加载单词内容")
            return
        }

        print("🔍 开始加载单词内容: \(word)")

        // 1. 立即清空所有内容页面，避免dailyword卡片的退出动画
        currentWordContent = nil
        showWordResult = false
        // 移除每日单词相关代码

        // 2. 进入加载状态，启动背景动画（仅搜索查词触发动画）
        isLoadingWord = true
        startLoadingAnimation()

        // 3. 异步加载单词内容
        Task {
            do {
                let userLanguage = SettingsService.shared.getCurrentPreferredLanguage()
                let wordContent = try await searchService.getWordContent(
                    word: word,
                    language: userLanguage
                )

                await MainActor.run {
                    // 4. 内容加载完成，停止动画并显示结果
                    self.stopLoadingAnimation()
                    self.isLoadingWord = false
                    self.currentWordContent = wordContent

                    withAnimation(.easeInOut(duration: 0.4)) {
                        self.showWordResult = true
                    }
                    print("✅ 单词内容加载成功，显示结果页面")
                }

            } catch {
                await MainActor.run {
                    // 5. 加载失败，停止动画并显示错误
                    self.stopLoadingAnimation()
                    self.isLoadingWord = false
                    print("❌ 加载单词内容失败: \(error.localizedDescription)")
                    // 这里可以显示错误提示
                }
            }
        }
    }

    /// 开始加载动画
    /// 启动背景渐变动画，如果动画结束但内容还未加载完成，则重复播放
    private func startLoadingAnimation() {
        print("🎨 开始加载动画")

        // 取消之前的动画重复任务
        animationRepeatTask?.cancel()

        // 触发动画
        animationTrigger += 1

        // 设置动画重复逻辑
        scheduleAnimationRepeat()
    }

    /// 停止加载动画
    private func stopLoadingAnimation() {
        print("🎨 停止加载动画")

        // 取消动画重复任务
        animationRepeatTask?.cancel()
        animationRepeatTask = nil

        print("🎨 动画重复任务已取消")
    }

    /// 安排动画重复
    /// 如果在动画结束时内容还未加载完成，则重复播放动画
    private func scheduleAnimationRepeat() {
        let animationDuration = KeyframeAnimationStyle.vibrant.duration

        // 创建可取消的异步任务
        animationRepeatTask = Task {
            // 等待动画完成 + 0.5秒缓冲
            try? await Task.sleep(nanoseconds: UInt64((animationDuration + 0.5) * 1_000_000_000))

            // 检查任务是否被取消
            guard !Task.isCancelled else {
                print("🎨 动画重复任务被取消")
                return
            }

            await MainActor.run {
                // 只检查搜索查词的加载状态（每日单词加载不触发动画）
                if self.isLoadingWord {
                    print("🎨 搜索查词仍在加载中，重复播放动画")
                    // 重新触发动画
                    self.animationTrigger += 1
                    self.scheduleAnimationRepeat()
                } else {
                    print("🎨 搜索查词完成，停止动画重复")
                    self.animationRepeatTask = nil
                }
            }
        }
    }
}

// MARK: - 预览

#if DEBUG
struct MainContentView_Previews: PreviewProvider {
    static var previews: some View {
        MainContentView()
            .preferredColorScheme(.dark)
    }
}

// Mock 搜索服务用于预览
class MockSearchService: SearchServiceProtocol {
    func getSuggestions(query: String) async -> SearchSuggestionsResponse {
        return SearchSuggestionsResponse(
            data: [
                SearchSuggestion.mock(word: "apple", definition: "苹果"),
                SearchSuggestion.mock(word: "book", definition: "书籍")
            ],
            metadata: SearchMetadata(
                query: query,
                resultCount: 2,
                responseTime: 100,
                source: "mock"
            )
        )
    }
    
    func getWordContent(word: String, language: LanguageCode) async throws -> WordDefinitionResponse {
        // Mock implementation
        throw NSError(domain: "Mock", code: 0, userInfo: nil)
    }
    
    func preloadContent(words: [String], language: LanguageCode) async {
        // Mock implementation
    }
    
    func warmupService() async {
        // Mock implementation
    }
    
    func getPerformanceStats() async -> [String: Any] {
        return [:]
    }
    
    func getHealthStatus() async -> [String: Any] {
        return [:]
    }
    
    func runDiagnostics() async -> [String: Any] {
        return [:]
    }
    
    func setSearchStrategy(_ strategy: SearchStrategy) {
        // Mock implementation
    }
    
    func getCurrentStrategy() -> SearchStrategy {
        return .hybrid
    }
    
    func clearCache() async {
        // Mock implementation
    }

    func syncIndexUpdates() async throws -> Int {
        // Mock implementation - return a fake sync ID
        return 1
    }
}

// Mock 本地收藏服务用于预览和降级
class MockLocalBookmarkService: LocalBookmarkServiceProtocol {
    func toggleBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        return BookmarkOperationResult.success(message: "Mock bookmark toggled", status: .bookmarked)
    }

    func addBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        return BookmarkOperationResult.success(message: "Mock bookmark added", status: .bookmarked)
    }

    func removeBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        return BookmarkOperationResult.success(message: "Mock bookmark removed", status: .notBookmarked)
    }

    func isBookmarked(word: String, language: LanguageCode) async throws -> Bool {
        return false
    }

    func getBookmarks() async throws -> [LocalBookmarkItem] {
        return []
    }

    func batchCheckBookmarkStatus(words: [String], language: LanguageCode) async throws -> [String: Bool] {
        return words.reduce(into: [:]) { result, word in
            result[word] = false
        }
    }

    func getBookmarkStats() async throws -> [String: Any] {
        return ["total_count": 0]
    }
}

// MARK: - 可动画的壁纸包装器

/// 可动画的壁纸包装器
/// 通过监听 animationTrigger 的变化来触发动画
struct AnimatableWallpaperWrapper: View {
    let animationTrigger: Int

    var body: some View {
        KeyframeAnimationWallpaperView(
            animationStyle: .vibrant,
            forceDarkMode: true
        )
        .onChange(of: animationTrigger) { _ in
            // 当 animationTrigger 改变时，触发动画
            DispatchQueue.main.async {
                // 通过通知触发动画
                NotificationCenter.default.post(name: .startWallpaperAnimation, object: nil)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .startWallpaperAnimation)) { _ in
            // 这里我们需要一个方法来触发动画
            // 由于我们不能直接调用 startAnimation()，我们使用一个技巧
            triggerAnimation()
        }
    }

    private func triggerAnimation() {
        // 使用通知来触发动画
        // 这个方法会在 KeyframeAnimationWallpaperView 中被监听
        print("🎨 AnimatableWallpaperWrapper: 触发动画")
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    static let startWallpaperAnimation = Notification.Name("startWallpaperAnimation")
}

#endif
