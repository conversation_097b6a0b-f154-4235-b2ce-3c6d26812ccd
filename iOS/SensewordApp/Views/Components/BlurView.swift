//
//  BlurView.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  模糊视图组件
//

import SwiftUI
import UIKit

/// UIKit模糊效果的SwiftUI包装
struct BlurView: UIViewRepresentable {
    let style: UIBlurEffect.Style
    
    func makeUIView(context: Context) -> UIVisualEffectView {
        let view = UIVisualEffectView(effect: UIBlurEffect(style: style))
        return view
    }
    
    func updateUIView(_ uiView: UIVisualEffectView, context: Context) {
        uiView.effect = UIBlurEffect(style: style)
    }
}

/// View扩展，添加backdrop修饰符
extension View {
    func backdrop<T: View>(_ content: T) -> some View {
        self.background(content)
    }
}
