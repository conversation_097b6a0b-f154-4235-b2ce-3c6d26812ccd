//
//  FloatingButtonConstants.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  悬浮按钮位置常量 - 确保跨组件位置一致性
//

import Foundation

/**
 * @description 悬浮按钮位置常量
 * 
 * 核心职责：
 * - 定义所有悬浮按钮的统一位置参数
 * - 确保跨文件、跨组件的位置一致性
 * - 便于统一调整和维护
 * 
 * 设计原则：
 * - 基于左上角原点的绝对位置布局
 * - 使用 GeometryReader 获取容器尺寸
 * - 统一的边距和按钮尺寸定义
 */
struct FloatingButtonConstants {
    
    // MARK: - 基础尺寸参数
    
    /// 悬浮按钮距离屏幕边缘的基础边距
    static let baseMargin: CGFloat = 26
    
    /// 悬浮按钮距离屏幕底部的基础边距
    static let bottomMargin: CGFloat = 38
    
    /// 收藏按钮的估算宽度（用于位置计算）
    static let bookmarkButtonWidth: CGFloat = 80
    
    /// 收藏按钮的估算高度（用于位置计算）
    static let bookmarkButtonHeight: CGFloat = 60
    
    /// 搜索悬浮面板的估算宽度（用于位置计算）
    static let searchActionBarWidth: CGFloat = 150
    
    /// 搜索悬浮面板的估算高度（用于位置计算）
    static let searchActionBarHeight: CGFloat = 60
    
    // MARK: - 位置计算方法
    
    /// 计算收藏按钮的绝对位置
    /// - Parameter containerSize: 容器尺寸
    /// - Returns: 收藏按钮的中心点位置
    static func bookmarkButtonPosition(in containerSize: CGSize) -> CGPoint {
        let position = CGPoint(
            x: baseMargin + bookmarkButtonWidth / 2,
            y: containerSize.height - bottomMargin - bookmarkButtonHeight / 2
        )
        print("🔖 [FloatingButtonConstants] 收藏按钮位置计算:")
        print("🔖   容器尺寸: \(containerSize)")
        print("🔖   计算位置: \(position)")
        print("🔖   参数: baseMargin=\(baseMargin), bottomMargin=\(bottomMargin)")
        print("🔖   参数: buttonWidth=\(bookmarkButtonWidth), buttonHeight=\(bookmarkButtonHeight)")
        return position
    }
    
    /// 计算搜索悬浮面板的绝对位置
    /// - Parameter containerSize: 容器尺寸
    /// - Returns: 搜索悬浮面板的中心点位置
    static func searchActionBarPosition(in containerSize: CGSize) -> CGPoint {
        let position = CGPoint(
            x: containerSize.width - baseMargin - searchActionBarWidth / 2,
            y: containerSize.height - bottomMargin - searchActionBarHeight / 2
        )
        print("🔍 [FloatingButtonConstants] 搜索面板位置计算:")
        print("🔍   容器尺寸: \(containerSize)")
        print("🔍   计算位置: \(position)")
        print("🔍   参数: baseMargin=\(baseMargin), bottomMargin=\(bottomMargin)")
        print("🔍   参数: actionBarWidth=\(searchActionBarWidth), actionBarHeight=\(searchActionBarHeight)")
        return position
    }
}
