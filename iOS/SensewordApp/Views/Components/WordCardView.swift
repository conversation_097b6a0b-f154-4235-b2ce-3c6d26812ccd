//
//  WordCardView.swift
//  SensewordApp
//
//  Created by AI Assistant on 2025-01-29.
//  独立的单词卡片View组件 - 每个实例绑定固定数据，不进行数据更新
//

import SwiftUI
import UIComponents
import UIComponents

/// 独立的单词卡片View组件
/// 设计原则：每个实例绑定固定的WordDefinitionResponse数据，不进行数据更新
/// 卡片切换通过View实例替换实现，而不是数据更新
struct WordCardView: View {

    // MARK: - 属性

    /// 固定的单词数据（不会更新）
    let wordData: WordDefinitionResponse

    /// 关闭回调
    let onDismiss: (() -> Void)?

    /// 收藏状态
    @State private var isBookmarked: Bool = false

    // MARK: - 视图

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 健康风格静态壁纸背景
                KeyframeAnimationWallpaperView(
                    animationStyle: .vibrant,
                    forceDarkMode: true
                )

                // 单词内容
                WordResultView(wordData: wordData, onDismiss: onDismiss ?? {})

                // 收藏按钮已移至 SearchView 统一管理
            }
        }
        .cornerRadius(20)
        .clipped()
        .onAppear {
            print("[WordCardView] 卡片实例创建: \(wordData.word)")
        }
        .onDisappear {
            print("[WordCardView] 卡片实例销毁: \(wordData.word)")
        }
    }
}

/// 可控制音频播放的单词卡片View组件
/// 用于无限内容流，可以控制是否自动播放音频
struct ControlledWordCardView: View {

    // MARK: - 属性

    /// 固定的单词数据（不会更新）
    let wordData: WordDefinitionResponse

    /// 关闭回调
    let onDismiss: (() -> Void)?

    /// 是否应该自动播放音频
    let shouldAutoPlayAudio: Bool

    /// 收藏状态
    @State private var isBookmarked: Bool = false

    // MARK: - 视图

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 完全静态的健康风格背景 - 无限内容流专用
                StaticHealthStyleBackground()

                // 单词内容 - 使用可控制的WordResultView
                ControlledWordResultView(
                    wordData: wordData,
                    onDismiss: onDismiss ?? {},
                    shouldAutoPlayAudio: shouldAutoPlayAudio
                )

                // 收藏按钮已移至 SearchView 统一管理
            }
        }
        .cornerRadius(20)
        .clipped()
        .onAppear {
            print("[ControlledWordCardView] 卡片实例创建: \(wordData.word), 自动播放: \(shouldAutoPlayAudio)")
        }
        .onDisappear {
            print("[ControlledWordCardView] 卡片实例销毁: \(wordData.word)")
        }
    }
}

/// 完全静态的健康风格背景组件
/// 专为无限内容流设计，从创建到销毁都不会有任何动画或状态变化
private struct StaticHealthStyleBackground: View {

    var body: some View {
        ZStack {
            // 暗色背景
            Color.black
                .edgesIgnoringSafeArea(.all)

            // 静态渐变组 - 无任何动画效果
            ZStack {
                // 深蓝色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 191/360, saturation: 0.5, brightness: 0.4),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.3, y: 0.4),
                    startRadius: 0,
                    endRadius: 400
                )

                // 深紫色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 201/360, saturation: 0.6, brightness: 0.5),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.7, y: 0.3),
                    startRadius: 0,
                    endRadius: 400
                )

                // 深粉色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 350/360, saturation: 0.4, brightness: 0.5),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.2, y: 0.7),
                    startRadius: 0,
                    endRadius: 450
                )

                // 深珊瑚色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 4/360, saturation: 0.45, brightness: 0.5),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.6, y: 0.8),
                    startRadius: 0,
                    endRadius: 450
                )

                // 深桃色渐变
                RadialGradient(
                    gradient: Gradient(colors: [
                        Color(hue: 14/360, saturation: 0.35, brightness: 0.55),
                        .clear
                    ]),
                    center: UnitPoint(x: 0.8, y: 0.6),
                    startRadius: 0,
                    endRadius: 400
                )
            }
            .blur(radius: 100)
            .edgesIgnoringSafeArea(.all)
            .opacity(0.4) // 固定透明度，无动画
        }
        // 无 onAppear 和 onDisappear，确保完全静态
    }
}

// MARK: - 预览

#Preview {
    // 使用简化的预览数据，避免复杂的数据模型构造
    WordCardView(
        wordData: WordDefinitionResponse(
            word: "example",
            metadata: WordMetadata(
                wordFrequency: "high",
                relatedConcepts: ["demonstration", "illustration"]
            ),
            content: WordContent(
                difficulty: "intermediate",
                phoneticSymbols: [],
                coreDefinition: "A thing characteristic of its kind or illustrating a general rule.",
                contextualExplanation: ContextualExplanation(
                    nativeSpeakerIntent: "To illustrate a point",
                    emotionalResonance: "Neutral",
                    vividImagery: "Clear demonstration",
                    etymologicalEssence: "From Latin exemplum"
                ),
                usageExamples: [],
                usageScenarios: [],
                collocations: [
                    Collocation(
                        type: "动词搭配",
                        pattern: "verb + example",
                        examples: [
                            CollocationExample(collocation: "give an example", translation: "举个例子"),
                            CollocationExample(collocation: "set an example", translation: "树立榜样")
                        ]
                    ),
                    Collocation(
                        type: "形容词搭配",
                        pattern: "adjective + example",
                        examples: [
                            CollocationExample(collocation: "perfect example", translation: "完美的例子"),
                            CollocationExample(collocation: "typical example", translation: "典型的例子")
                        ]
                    )
                ],
                usageNotes: [],
                synonyms: []
            ),
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        ),
        onDismiss: nil
    )
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .background(Color.black)
}
