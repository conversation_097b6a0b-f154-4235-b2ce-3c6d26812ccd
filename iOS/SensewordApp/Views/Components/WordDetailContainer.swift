//
//  WordDetailContainer.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  统一的单词详情容器 - 支持每日单词、搜索结果、生词等所有场景
//

import SwiftUI
import UIComponents

/**
 * @description 统一的单词详情容器
 * 
 * 核心功能：
 * - 统一的单词详情展示
 * - 卡片式无限内容流
 * - 支持所有场景：每日单词、搜索结果、生词等
 * 
 * 设计原则：
 * - 单一职责：只负责单词详情展示和无限流
 * - 数据来源无关：通过初始单词启动，后续使用推荐系统
 * - 一致的用户体验：所有场景使用相同的交互方式
 */
struct WordDetailContainer: View {
    
    // MARK: - 属性
    
    /// 当前单词数据
    @State var currentWordData: WordDefinitionResponse
    
    /// 下一个单词数据
    @State private var nextWordData: WordDefinitionResponse?
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    /// 推荐数组管理器
    @StateObject private var recommendationManager = RecommendationArrayManager()
    
    /// JIT预加载器
    @StateObject private var jitPreloader = JITPreloader()
    
    // MARK: - 手势状态
    
    /// 当前拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    /// 是否正在切换
    @State private var isSwitching: Bool = false

    /// 是否曾经达到过切换阈值（关键修复）
    @State private var hasTriggeredSwitch: Bool = false

    /// 是否正在淡出上层卡片
    @State private var isCardFadingOut: Bool = false

    // 移除加载指示器，使用静默预加载

    /// 是否已初始化
    @State private var isInitialized: Bool = false

    /// 指引动画状态
    @State private var guidanceAnimationOffset: CGFloat = 0

    /// 是否显示引导动画（在卡片加载完成后显示）
    @State private var showGuidanceAnimation: Bool = false

    // MARK: - 配置
    
    /// 切换阈值（屏幕高度的30%）
    private let switchThreshold: CGFloat = UIScreen.main.bounds.height * 0.3
    
    /// 最大拖拽距离（屏幕高度的50%）
    private let maxDragDistance: CGFloat = UIScreen.main.bounds.height * 0.5
    
    /// 触觉反馈生成器
    @State private var impactFeedback = UIImpactFeedbackGenerator(style: .medium)
    @State private var selectionFeedback = UISelectionFeedbackGenerator()
    
    // MARK: - 界面构建
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 当前卡片实例（固定数据，独立View）
                ControlledWordCardView(
                    wordData: currentWordData,
                    onDismiss: onDismiss,
                    shouldAutoPlayAudio: true // 当前卡片允许自动播放音频
                )
                    .id("current-\(currentWordData.word)") // 强制视图实例重新创建
                    .offset(y: isDragging ? min(dragOffset, 0) : 0)
                    .scaleEffect(currentCardScale)
                    .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
                    .animation(.interactiveSpring(response: 0.4, dampingFraction: 0.8), value: dragOffset)
                    .zIndex(0) // 底层

                // 下一张卡片实例（固定数据，独立View）
                if let nextWord = nextWordData {
                    ControlledWordCardView(
                        wordData: nextWord,
                        onDismiss: onDismiss,
                        shouldAutoPlayAudio: false // 下一张卡片禁止自动播放音频
                    )
                        .id("next-\(nextWord.word)") // 强制视图实例重新创建
                        .offset(y: nextCardOffset)
                        .scaleEffect(nextCardScale)
                        .opacity(nextCardOpacity)
                        .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
                        .animation(.interactiveSpring(response: 0.4, dampingFraction: 0.8), value: dragOffset)
                        .zIndex(1) // 上层，覆盖当前卡片
                }

                // 指引动画 - 只在卡片加载完成后显示
                if showGuidanceAnimation {
                    topGuidanceView
                        .zIndex(2) // 最顶层
                }

                // 底部手势检测区域
                bottomGestureArea
                    .zIndex(3) // 最顶层，确保能捕获手势
            }
            // 移除全屏手势，改为底部区域手势检测
            .onAppear {
                setupWordDetailContainer()
            }
        }
        .background(Color.clear) // 确保全屏覆盖
        .ignoresSafeArea() // 忽略安全区域，实现全屏效果
    }
    
    // 移除旧的卡片视图方法，使用独立的WordCardView组件

    // MARK: - 底部手势检测区域

    private var bottomGestureArea: some View {
        GeometryReader { geometry in
            VStack {
                Spacer() // 推到底部

                // 手势检测区域 - 避开 home bar 区域
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: 120) // 手势检测区域高度
                    .frame(maxWidth: .infinity)
                    .contentShape(Rectangle()) // 确保透明区域也能响应手势
                    .gesture(
                        DragGesture(coordinateSpace: .local)
                            .onChanged { value in
                                // 检查手势起始位置是否在安全区域内
                                let safeAreaBottom = geometry.safeAreaInsets.bottom
                                let gestureStartY = value.startLocation.y
                                let gestureAreaHeight: CGFloat = 120

                                // 如果手势起始位置在 home bar 保护区域内（底部安全区域），则忽略
                                if gestureStartY > (gestureAreaHeight - safeAreaBottom - 10) {
                                    print("[WordDetailContainer] 手势在 home bar 保护区域内，忽略")
                                    return
                                }

                                handleDragChanged(value)
                            }
                            .onEnded { value in
                                // 同样检查手势起始位置
                                let safeAreaBottom = geometry.safeAreaInsets.bottom
                                let gestureStartY = value.startLocation.y
                                let gestureAreaHeight: CGFloat = 120

                                // 如果手势起始位置在 home bar 保护区域内，则忽略
                                if gestureStartY > (gestureAreaHeight - safeAreaBottom - 10) {
                                    print("[WordDetailContainer] 手势结束在 home bar 保护区域内，忽略")
                                    return
                                }

                                handleDragEnded(value)
                            }
                    )

                // Home bar 保护区域 - 不响应手势
                Rectangle()
                    .fill(Color.clear)
                    .frame(height: geometry.safeAreaInsets.bottom + 10) // 安全区域 + 10px 缓冲
                    .frame(maxWidth: .infinity)
            }
        }
    }

    // MARK: - 底部指引动画视图

    private var topGuidanceView: some View {
        GeometryReader { geometry in
            HStack(spacing: 8) {
                // 上拉箭头动画
                Image(systemName: "arrow.up")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white.opacity(0.4))

                // 提示文字 - 显示下一个单词名称
                if let nextWord = nextWordData {
                    Text("探索关联词 \(nextWord.word)")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.white.opacity(0.3))
                } else {
                    Text("探索更多")
                        .font(.system(size: 12, weight: .regular))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            .offset(y: guidanceAnimationOffset)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
            .padding(.bottom, geometry.safeAreaInsets.bottom + 50) // Home bar 安全位置上面更多空间
        }
    }
    
    // MARK: - 计算属性
    
    /// 下一张卡片的Y偏移量
    private var nextCardOffset: CGFloat {
        if isSwitching {
            return 0
        } else if isDragging {
            let progress = min(abs(dragOffset) / maxDragDistance, 1.0)
            return UIScreen.main.bounds.height * (1 - progress)
        } else {
            // 将新卡片移动到屏幕外更远的位置，确保完全不可见
            return UIScreen.main.bounds.height * 1.2
        }
    }
    
    /// 下一张卡片的透明度
    private var nextCardOpacity: Double {
        if isSwitching && isCardFadingOut {
            return 0.0 // 切换完成后渐渐淡出
        } else {
            return 1.0 // 正常状态保持不透明
        }
    }

    /// 当前卡片的透明度
    private var currentCardOpacity: Double {
        // 在切换状态时，当前卡片应该保持不透明
        // 因为切换时 currentWordData 已经是新数据，这就是新卡片
        if isDragging {
            let progress = min(abs(dragOffset) / maxDragDistance, 1.0)
            return 1.0 - (progress * 0.3) // 拖拽时逐渐变透明，但不完全消失
        } else {
            return 1.0 // 正常状态和切换状态都保持完全不透明
        }
    }

    /// 当前卡片的缩放比例
    private var currentCardScale: CGFloat {
        if isSwitching {
            return 1.0 // 修复：切换时新卡片应该立即显示为正常大小，避免缩放动画
        } else if isDragging {
            let progress = min(abs(dragOffset) / maxDragDistance, 1.0)
            return 1.0 - (progress * 0.05) // 轻微缩小
        } else {
            return 1.0
        }
    }

    /// 下一张卡片的缩放比例
    private var nextCardScale: CGFloat {
        if isSwitching {
            return 1.0
        } else if isDragging {
            let progress = min(abs(dragOffset) / maxDragDistance, 1.0)
            return 0.9 + (progress * 0.1) // 从0.9缩放到1.0
        } else {
            return 0.9
        }
    }
    
    // 移除加载指示器，使用静默预加载
    
    // MARK: - 手势处理
    
    /// 处理拖拽变化
    private func handleDragChanged(_ value: DragGesture.Value) {
        let verticalTranslation = value.translation.height
        let horizontalTranslation = value.translation.width

        // 如果已经在拖拽中，继续处理垂直拖拽（不再检查手势方向）
        if isDragging {
            // 只处理向上的垂直拖拽
            guard verticalTranslation < 0 else {
                return
            }

            let upwardDistance = abs(verticalTranslation)

            // 限制最大拖拽距离
            dragOffset = -min(upwardDistance, maxDragDistance)

            // 实时监测阈值状态 - 根据当前距离动态更新
            let currentlyOverThreshold = upwardDistance >= switchThreshold

            // 如果当前超过阈值且之前没有触发过触觉反馈
            if currentlyOverThreshold && !hasTriggeredSwitch && !isSwitching {
                hasTriggeredSwitch = true
                selectionFeedback.selectionChanged()
                print("[WordDetailContainer] 🎯 达到切换阈值，触觉反馈")
            }
            // 如果当前低于阈值，重置触发状态（允许重新触发触觉反馈）
            else if !currentlyOverThreshold && hasTriggeredSwitch {
                hasTriggeredSwitch = false
                print("[WordDetailContainer] 📉 低于切换阈值，重置触发状态")
            }

            print("[WordDetailContainer] 垂直拖拽中，距离: \(upwardDistance)px，阈值: \(switchThreshold)px，当前超阈值: \(currentlyOverThreshold)")
            return
        }

        // 拖拽开始时的手势方向判断 - 更严格的验证
        let isVerticalGesture = abs(verticalTranslation) > abs(horizontalTranslation) * 2.0 // 提高垂直手势要求
        let isHorizontalGesture = abs(horizontalTranslation) > abs(verticalTranslation) * 1.2
        let minVerticalDistance: CGFloat = 15 // 最小垂直距离要求

        // 更严格的手势验证：
        // 1. 必须是明确的向上垂直手势
        // 2. 垂直距离必须超过最小阈值
        // 3. 不能是水平手势
        // 4. 垂直分量必须明显大于水平分量
        guard isVerticalGesture &&
              verticalTranslation < 0 &&
              !isHorizontalGesture &&
              abs(verticalTranslation) >= minVerticalDistance else {
            print("[WordDetailContainer] 手势不符合要求: 垂直=\(abs(verticalTranslation))px, 水平=\(abs(horizontalTranslation))px")
            return
        }

        // 开始垂直拖拽
        isDragging = true
        impactFeedback.prepare()

        let upwardDistance = abs(verticalTranslation)
        dragOffset = -min(upwardDistance, maxDragDistance)

        print("[WordDetailContainer] 开始垂直拖拽，距离: \(upwardDistance)px")
    }
    
    /// 处理拖拽结束
    private func handleDragEnded(_ value: DragGesture.Value) {
        let verticalTranslation = value.translation.height
        let horizontalTranslation = value.translation.width
        let upwardDistance = abs(verticalTranslation)

        // 检查是否为水平手势
        let isHorizontalGesture = abs(horizontalTranslation) > abs(verticalTranslation)

        print("[WordDetailContainer] 拖拽结束，垂直距离: \(upwardDistance)px，水平距离: \(abs(horizontalTranslation))px")

        // 如果是水平手势，直接重置状态，不执行卡片切换
        if isHorizontalGesture {
            print("[WordDetailContainer] 检测到水平手势，重置状态")
            bounceBack()
            return
        }

        // 实时阈值判断：基于最终位置决定是否切换
        let finallyOverThreshold = upwardDistance >= switchThreshold

        if finallyOverThreshold && nextWordData != nil {
            // 最终位置超过阈值，执行切换
            print("[WordDetailContainer] ✅ 最终位置超过阈值，执行卡片切换")
            performCardSwitch()
        } else {
            // 最终位置未超过阈值，弹回原位
            print("[WordDetailContainer] ↩️ 最终位置未超过阈值，弹回原位")
            bounceBack()
        }
    }
    
    // MARK: - 卡片切换逻辑
    
    /// 执行卡片切换 - 基于View实例交换
    private func performCardSwitch() {
        guard let nextWord = nextWordData, !isSwitching else { return }

        let oldWord = currentWordData.word
        print("[WordDetailContainer] 执行View实例切换: \(oldWord) -> \(nextWord.word)")

        // 触觉反馈
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)

        // 防止重复触发
        isSwitching = true

        // 使用动画进行平滑切换
        withAnimation(.easeInOut(duration: 0.3)) {
            // 第一步：隐藏引导动画
            showGuidanceAnimation = false
            guidanceAnimationOffset = 0

            // 重置状态，让下一张卡片移动到屏幕中央完全覆盖
            dragOffset = 0
            isDragging = false
            hasTriggeredSwitch = false
        }

        // 延迟数据交换，确保下一张卡片先完全覆盖
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
            // 第一步：更新底层数据（此时被上层遮挡，用户看不到）
            self.currentWordData = nextWord

            // 第二步：等待足够长时间确保底层数据完全稳定，然后开始淡出上层
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // 先让上层卡片渐渐淡出
                withAnimation(.easeOut(duration: 0.5)) {
                    self.isCardFadingOut = true
                }

                // 淡出完成后再销毁上层卡片
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.nextWordData = nil
                    self.isCardFadingOut = false // 重置状态
                }
            }
        }

        // 延迟重置切换状态，确保上层卡片完全淡出并销毁后再开始预加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.8) {
            self.isSwitching = false

            // 在切换状态重置后，开始预加载新的下一张卡片
            Task {
                await self.preloadNextCard()
            }
        }

        print("[WordDetailContainer] View实例切换完成，旧卡片已销毁: \(oldWord)")
    }
    
    /// 弹回原位 - 强制重置所有状态
    private func bounceBack() {
        print("[WordDetailContainer] 未达到阈值，弹回原位")

        // 立即重置拖拽状态，防止状态混乱
        isDragging = false
        hasTriggeredSwitch = false
        isSwitching = false

        // 使用强弹性动画确保卡片完全回到原位
        withAnimation(.interactiveSpring(response: 0.6, dampingFraction: 0.8, blendDuration: 0.3)) {
            dragOffset = 0
        }

        // 确保引导动画状态正确
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if !self.showGuidanceAnimation && self.nextWordData != nil {
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.showGuidanceAnimation = true
                }
            }
        }
    }
    
    // MARK: - 数据管理
    
    /// 设置单词详情容器
    private func setupWordDetailContainer() {
        guard !isInitialized else { return }
        isInitialized = true

        Task {
            do {
                print("[WordDetailContainer] 开始初始化，当前单词: \(currentWordData.word)")

                // 基于当前单词构建个性化推荐流（优化：传递已有数据避免重复API调用）
                try await recommendationManager.addSearchBasedRecommendations(
                    from: currentWordData.word,
                    wordData: currentWordData
                )

                // 预加载下一张卡片
                await preloadNextCard()

                print("[WordDetailContainer] 初始化完成")

                // 打印最终的推荐数组状态
                recommendationManager.printCurrentArrayState()
            } catch {
                print("[WordDetailContainer] 初始化失败: \(error)")
            }
        }
    }
    
    /// 预加载下一张卡片
    private func preloadNextCard() async {
        // 移动到下一个推荐项
        guard let nextWordRecommendation = recommendationManager.moveToNext() else {
            print("[WordDetailContainer] 没有下一个单词可预加载")
            await MainActor.run {
                // 没有下一个单词时隐藏引导动画
                showGuidanceAnimation = false
                guidanceAnimationOffset = 0
            }
            return
        }

        print("[WordDetailContainer] 开始静默预加载下一张卡片: \(nextWordRecommendation.word)")

        // 静默预加载单词数据
        await jitPreloader.preloadWord(nextWordRecommendation.word)

        if let preloadedData = jitPreloader.getCachedWordData(nextWordRecommendation.word) {
            await MainActor.run {
                nextWordData = preloadedData

                // 卡片加载完成后，延迟显示引导动画
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    startGuidanceAnimation()
                }
            }
            print("[WordDetailContainer] 下一张卡片静默预加载完成: \(nextWordRecommendation.word)")
        } else {
            await MainActor.run {
                // 预加载失败时隐藏引导动画
                showGuidanceAnimation = false
                guidanceAnimationOffset = 0
            }
            print("[WordDetailContainer] 下一张卡片静默预加载失败")
        }
    }

    /// 启动引导动画
    private func startGuidanceAnimation() {
        guard !showGuidanceAnimation else { return }

        print("[WordDetailContainer] 卡片加载完成，启动引导动画")

        // 显示引导动画
        withAnimation(.easeIn(duration: 0.5)) {
            showGuidanceAnimation = true
        }

        // 启动往复上下运动动画
        withAnimation(
            Animation.easeInOut(duration: 2.0)
                .repeatForever(autoreverses: true)
        ) {
            guidanceAnimationOffset = -8 // 向上移动8点，更轻微
        }
    }
}

// MARK: - 预览
#if DEBUG
struct WordDetailContainer_Previews: PreviewProvider {
    static var previews: some View {
        WordDetailContainer(
            currentWordData: WordDefinitionResponse.mock(),
            onDismiss: {}
        )
        .preferredColorScheme(.dark)
    }
}
#endif
