//
//  FloatingActionBar.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  悬浮操作栏 - 搜索和个人页面入口
//

import SwiftUI

/**
 * @description 悬浮操作栏组件
 *
 * 核心职责：
 * - 提供搜索功能入口
 * - 提供个人页面入口
 * - 透明模糊磨砂玻璃效果
 * - 圆角矩形设计
 *
 * 设计原则：
 * - 简洁美观：透明磨砂玻璃效果
 * - 功能明确：搜索和个人两个核心功能
 * - 位置固定：右下角悬浮显示
 * - 交互友好：清晰的图标和文字标识
 */
struct FloatingActionBar: View {

    // MARK: - Properties

    /// 搜索按钮点击回调
    let onSearchTap: () -> Void

    /// 个人页面按钮点击回调
    let onProfileTap: () -> Void

    /// 缩放动画状态
    @State private var searchScale: CGFloat = 1.0
    @State private var profileScale: CGFloat = 1.0
    
    // MARK: - Initialization

    init(
        onSearchTap: @escaping () -> Void,
        onProfileTap: @escaping () -> Void
    ) {
        self.onSearchTap = onSearchTap
        self.onProfileTap = onProfileTap
    }
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: 16) {
            // 搜索按钮
            actionButton(
                icon: "magnifyingglass",
                text: "搜索",
                scale: searchScale,
                action: {
                    withAnimation(.easeOut(duration: 0.1)) {
                        searchScale = 0.95
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.easeOut(duration: 0.1)) {
                            searchScale = 1.0
                        }
                    }
                    onSearchTap()
                }
            )

            // 分隔线
            Rectangle()
                .fill(Color.white.opacity(0.2))
                .frame(width: 1, height: 32)

            // 个人页面按钮
            actionButton(
                icon: "person.circle",
                text: "我的",
                scale: profileScale,
                action: {
                    withAnimation(.easeOut(duration: 0.1)) {
                        profileScale = 0.95
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.easeOut(duration: 0.1)) {
                            profileScale = 1.0
                        }
                    }
                    onProfileTap()
                }
            )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            // 透明模糊磨砂玻璃效果
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
    }
    
    // MARK: - Private Views
    
    /// 操作按钮组件
    private func actionButton(
        icon: String,
        text: String,
        scale: CGFloat,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))

                Text(text)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
            .scaleEffect(scale)
            .frame(minWidth: 44, minHeight: 44) // 确保足够的点击区域
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        // 模拟背景
        LinearGradient(
            colors: [
                Color.blue.opacity(0.3),
                Color.purple.opacity(0.3),
                Color.pink.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
        
        VStack {
            Spacer()
            HStack {
                Spacer()
                FloatingActionBar(
                    onSearchTap: {
                        print("搜索按钮被点击")
                    },
                    onProfileTap: {
                        print("个人页面按钮被点击")
                    }
                )
                .padding(.trailing, 26)
                .padding(.bottom, 38)
            }
        }
    }
}
