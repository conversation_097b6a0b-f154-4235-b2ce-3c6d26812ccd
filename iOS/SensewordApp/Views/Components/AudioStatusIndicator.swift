//
//  AudioStatusIndicator.swift
//  SensewordApp
//
//  Created by AI Assistant on 2025-06-28.
//

import SwiftUI

/// 音频状态指示器
/// 显示音频是否已准备好播放的状态
struct AudioStatusIndicator: View {
    
    /// 音频状态枚举
    enum AudioStatus {
        case ready      // 音频已准备好
        case loading    // 音频正在加载
        case unavailable // 音频不可用
    }
    
    /// 当前音频状态
    let status: AudioStatus
    
    /// 指示器大小
    let size: CGFloat
    
    /// 初始化
    /// - Parameters:
    ///   - status: 音频状态
    ///   - size: 指示器大小，默认为 6
    init(status: AudioStatus, size: CGFloat = 6) {
        self.status = status
        self.size = size
    }
    
    var body: some View {
        ZStack {
            switch status {
            case .ready:
                // 绿色双层圆点（带发光效果）
                readyIndicator
            case .loading:
                // 加载中的动画效果
                loadingIndicator
            case .unavailable:
                // 灰色单层圆点
                unavailableIndicator
            }
        }
        .frame(width: size, height: size)
    }
    
    // MARK: - 音频已准备指示器
    
    private var readyIndicator: some View {
        ZStack {
            // 外层发光圆圈
            Circle()
                .fill(Color(hex: "B1FFB9"))
                .frame(width: size, height: size)
                .blur(radius: 2) // 4px 模糊效果缩放到 2
            
            // 内层实心圆圈
            Circle()
                .fill(Color(hex: "83DD8C"))
                .frame(width: size * 2/3, height: size * 2/3) // 4/6 比例
        }
    }
    
    // MARK: - 加载中指示器
    
    private var loadingIndicator: some View {
        ZStack {
            // 外层淡化圆圈
            Circle()
                .fill(Color(hex: "B1FFB9").opacity(0.3))
                .frame(width: size, height: size)
                .blur(radius: 1)
            
            // 内层脉动圆圈
            Circle()
                .fill(Color(hex: "83DD8C").opacity(0.6))
                .frame(width: size * 2/3, height: size * 2/3)
                .scaleEffect(pulseScale)
                .animation(
                    Animation.easeInOut(duration: 1.0)
                        .repeatForever(autoreverses: true),
                    value: pulseScale
                )
        }
        .onAppear {
            startPulseAnimation()
        }
    }
    
    // MARK: - 音频不可用指示器
    
    private var unavailableIndicator: some View {
        Circle()
            .fill(Color(hex: "787878"))
            .frame(width: size * 2/3, height: size * 2/3) // 4/6 比例，对应设计中的 4px
    }
    
    // MARK: - 动画状态
    
    @State private var pulseScale: CGFloat = 1.0
    
    private func startPulseAnimation() {
        pulseScale = 1.2
    }
}



// MARK: - Preview

struct AudioStatusIndicator_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            HStack(spacing: 20) {
                VStack {
                    AudioStatusIndicator(status: .ready)
                    Text("Ready")
                        .font(.caption)
                }
                
                VStack {
                    AudioStatusIndicator(status: .loading)
                    Text("Loading")
                        .font(.caption)
                }
                
                VStack {
                    AudioStatusIndicator(status: .unavailable)
                    Text("Unavailable")
                        .font(.caption)
                }
            }
            
            // 不同大小的示例
            HStack(spacing: 20) {
                AudioStatusIndicator(status: .ready, size: 4)
                AudioStatusIndicator(status: .ready, size: 6)
                AudioStatusIndicator(status: .ready, size: 8)
                AudioStatusIndicator(status: .ready, size: 10)
            }
        }
        .padding()
        .background(Color.black)
        .previewLayout(.sizeThatFits)
    }
}
