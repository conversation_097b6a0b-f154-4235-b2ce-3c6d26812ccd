import SwiftUI

/**
 * @description 悬浮收藏按钮组件
 * 
 * 核心职责：
 * - 提供收藏功能入口
 * - 透明模糊磨砂玻璃效果
 * - 圆角矩形设计
 * - 与悬浮操作栏样式保持一致
 * 
 * 设计原则：
 * - 简洁美观：透明磨砂玻璃效果
 * - 功能明确：专注收藏功能
 * - 位置固定：左下角悬浮显示
 * - 交互友好：清晰的图标和文字标识
 */
struct FloatingBookmarkButton: View {

    // MARK: - Properties

    /// 单词
    let word: String

    /// 语言代码
    let language: LanguageCode

    /// 本地收藏ViewModel
    @ObservedObject var viewModel: LocalBookmarkViewModel

    /// 缩放动画状态
    @State private var bookmarkScale: CGFloat = 1.0

    // MARK: - Initialization

    init(
        word: String,
        language: LanguageCode,
        viewModel: LocalBookmarkViewModel
    ) {
        self.word = word
        self.language = language
        self.viewModel = viewModel
    }
    
    // MARK: - Views
    
    var body: some View {
        Button(action: {
            print("🔖 [FloatingBookmarkButton] 按钮被点击")
            print("🔖 [FloatingBookmarkButton] 点击前状态: \(viewModel.isBookmarked)")

            // 播放点击动画
            withAnimation(.easeOut(duration: 0.1)) {
                bookmarkScale = 0.95
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeOut(duration: 0.1)) {
                    bookmarkScale = 1.0
                }
            }

            // 异步切换收藏状态
            Task {
                await viewModel.toggleBookmark(word: word, language: language)
            }
        }) {
            VStack(spacing: 4) {
                Image(systemName: viewModel.isBookmarked ? "bookmark.fill" : "bookmark")
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(viewModel.isBookmarked ? .red : .white.opacity(0.8))

                Text("收藏")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(.white.opacity(0.7)) // 文字颜色保持一致，不变色
            }
            .scaleEffect(bookmarkScale)
            .frame(minWidth: 44, minHeight: 44) // 确保足够的点击区域
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .environment(\.colorScheme, .dark)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        .onAppear {
            // 初始化时检查收藏状态
            Task {
                await viewModel.checkBookmarkStatus(word: word, language: language)
            }
        }
    }
}

// MARK: - Preview

#Preview {
    // 简化预览，避免复杂依赖
    ZStack {
        Color.black.ignoresSafeArea()
        Text("FloatingBookmarkButton Preview")
            .foregroundColor(.white)
    }
}
