//
//  PullUpGestureDetector.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  通用上拉手势检测器 - 无限内容流核心组件
//

import SwiftUI

/**
 * @description 通用上拉手势检测器
 * 
 * 核心职责：
 * - 检测用户在页面底部的上拉手势
 * - 提供渐进式提示显示（30px提示 → 80px触发）
 * - 支持触觉反馈和视觉反馈
 * - 完全独立的UI组件，可在任何页面使用
 * 
 * 设计原则：
 * - 自然交互：符合用户直觉的上拉加载模式
 * - 渐进提示：给用户明确的操作反馈
 * - 可配置性：支持自定义阈值和样式
 * - 无侵入性：不影响现有页面布局
 */
struct PullUpGestureDetector: View {
    
    // MARK: - Configuration
    
    /// 触发回调
    let onTriggerNext: () -> Void
    
    /// 触发阈值（默认80px）
    let triggerThreshold: CGFloat
    
    /// 提示阈值（默认30px）
    let hintThreshold: CGFloat
    
    /// 检测区域高度（默认100px）
    let detectionHeight: CGFloat
    
    /// 是否启用触觉反馈
    let enableHapticFeedback: Bool
    
    // MARK: - State
    
    /// 当前拖拽偏移量
    @State private var dragOffset: CGFloat = 0
    
    /// 是否显示提示
    @State private var showHint: Bool = false
    
    /// 是否已触发
    @State private var isTriggered: Bool = false
    
    /// 是否正在拖拽
    @State private var isDragging: Bool = false
    
    /// 触觉反馈生成器
    @State private var impactFeedback = UIImpactFeedbackGenerator(style: .medium)
    
    // MARK: - Initialization
    
    init(
        onTriggerNext: @escaping () -> Void,
        triggerThreshold: CGFloat = 80.0,
        hintThreshold: CGFloat = 30.0,
        detectionHeight: CGFloat = 100.0,
        enableHapticFeedback: Bool = true
    ) {
        self.onTriggerNext = onTriggerNext
        self.triggerThreshold = triggerThreshold
        self.hintThreshold = hintThreshold
        self.detectionHeight = detectionHeight
        self.enableHapticFeedback = enableHapticFeedback
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 提示区域
            if showHint {
                hintView
                    .transition(.opacity.combined(with: .move(edge: .bottom)))
            }
            
            // 手势检测区域
            gestureDetectionArea
        }
        .animation(.easeInOut(duration: 0.3), value: showHint)
        .animation(.easeInOut(duration: 0.2), value: dragOffset)
    }
    
    // MARK: - Hint View
    
    private var hintView: some View {
        HStack(spacing: 12) {
            // 上拉图标
            Image(systemName: "arrow.up")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
                .scaleEffect(isDragging ? 1.2 : 1.0)
            
            // 提示文本
            Text(hintText)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
            
            Spacer()
            
            // 进度指示器
            if isDragging {
                progressIndicator
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
    
    // MARK: - Gesture Detection Area
    
    private var gestureDetectionArea: some View {
        Rectangle()
            .fill(Color.clear)
            .frame(height: detectionHeight)
            .contentShape(Rectangle())
            .gesture(
                DragGesture(coordinateSpace: .local)
                    .onChanged(handleDragChanged)
                    .onEnded(handleDragEnded)
            )
    }
    
    // MARK: - Computed Properties
    
    /// 提示文本
    private var hintText: String {
        if isTriggered {
            return "释放以探索下一个"
        } else if dragOffset > hintThreshold {
            return "继续上拉以探索更多"
        } else {
            return "上拉探索更多内容"
        }
    }
    
    /// 进度指示器
    private var progressIndicator: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(Color.secondary.opacity(0.3), lineWidth: 3)
                .frame(width: 24, height: 24)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: min(dragOffset / triggerThreshold, 1.0))
                .stroke(
                    dragOffset >= triggerThreshold ? Color.green : Color.blue,
                    style: StrokeStyle(lineWidth: 3, lineCap: .round)
                )
                .frame(width: 24, height: 24)
                .rotationEffect(.degrees(-90))
            
            // 中心图标
            if dragOffset >= triggerThreshold {
                Image(systemName: "checkmark")
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.green)
            }
        }
    }
    
    // MARK: - Gesture Handlers
    
    /**
     * 处理拖拽变化
     */
    private func handleDragChanged(_ value: DragGesture.Value) {
        let translation = value.translation.height

        print("[PullUpGestureDetector] 检测到拖拽，translation: \(translation)")

        // 只处理向上拖拽（负值）
        guard translation < 0 else {
            resetState()
            return
        }

        let upwardDistance = abs(translation)
        dragOffset = upwardDistance
        isDragging = true

        print("[PullUpGestureDetector] 拖拽中，距离: \(upwardDistance)px")
        
        // 显示/隐藏提示
        let shouldShowHint = upwardDistance >= hintThreshold
        if shouldShowHint != showHint {
            showHint = shouldShowHint
            
            // 首次显示提示时的触觉反馈
            if shouldShowHint && enableHapticFeedback {
                impactFeedback.impactOccurred()
            }
        }
        
        // 检查是否达到触发阈值
        let shouldTrigger = upwardDistance >= triggerThreshold
        if shouldTrigger != isTriggered {
            isTriggered = shouldTrigger
            
            // 达到触发阈值时的触觉反馈
            if shouldTrigger && enableHapticFeedback {
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)
            }
        }
        
        print("[PullUpGestureDetector] 拖拽距离: \(upwardDistance)px, 提示: \(showHint), 触发: \(isTriggered)")
    }
    
    /**
     * 处理拖拽结束
     */
    private func handleDragEnded(_ value: DragGesture.Value) {
        let translation = value.translation.height
        let upwardDistance = abs(translation)
        
        print("[PullUpGestureDetector] 拖拽结束，距离: \(upwardDistance)px")
        
        // 如果达到触发阈值，执行回调
        if upwardDistance >= triggerThreshold {
            print("[PullUpGestureDetector] 触发下一个内容")
            onTriggerNext()
            
            // 触发成功的触觉反馈
            if enableHapticFeedback {
                let successFeedback = UINotificationFeedbackGenerator()
                successFeedback.notificationOccurred(.success)
            }
        }
        
        // 重置状态
        resetState()
    }
    
    /**
     * 重置状态
     */
    private func resetState() {
        withAnimation(.easeOut(duration: 0.3)) {
            dragOffset = 0
            showHint = false
            isTriggered = false
            isDragging = false
        }
    }
}

// MARK: - Preview

#Preview {
    VStack {
        Spacer()
        
        Text("页面内容")
            .font(.title)
            .padding()
        
        Spacer()
        
        PullUpGestureDetector {
            print("触发下一个内容")
        }
    }
    .background(Color(.systemGroupedBackground))
}
