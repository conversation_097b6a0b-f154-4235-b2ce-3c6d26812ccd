//
//  SubscriptionProductCard.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  订阅产品卡片组件
//

import SwiftUI

/// 订阅产品卡片
struct SubscriptionProductCard: View {
    
    // MARK: - 属性
    
    /// 产品信息
    let product: SubscriptionProduct
    
    /// 选择回调
    let onSelect: () -> Void
    
    // MARK: - 界面构建
    
    var body: some View {
        Button(action: onSelect) {
            ZStack {
                // 主要内容容器
                HStack(spacing: 16) {
                    // 选择指示器
                    ZStack {
                        Circle()
                            .stroke(product.isSelected ? Color.orange : Color.white.opacity(0.3), lineWidth: 2)
                            .frame(width: 24, height: 24)

                        if product.isSelected {
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 16, height: 16)

                            Image(systemName: "checkmark")
                                .font(.system(size: 10, weight: .bold))
                                .foregroundColor(.white)
                        }
                    }

                    // 产品信息
                    VStack(alignment: .leading, spacing: 4) {
                        Text(product.title)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)

                        if let description = product.description {
                            Text(description)
                                .font(.system(size: 14))
                                .foregroundColor(.white.opacity(0.7))
                                .multilineTextAlignment(.leading)
                        }
                    }

                    Spacer()

                    // 价格信息
                    VStack(alignment: .trailing, spacing: 2) {
                        Text(product.price)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)

                        if let originalPrice = product.originalPrice {
                            Text(originalPrice)
                                .font(.system(size: 14))
                                .foregroundColor(.white.opacity(0.5))
                                .strikethrough()
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)

                // 折扣标签（如果有）- 使用绝对定位避免影响布局
                if let discountLabel = product.discountLabel {
                    VStack {
                        HStack {
                            Spacer()
                            Text(discountLabel)
                                .font(.system(size: 12, weight: .semibold))
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.orange)
                                )
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, -8)  // 向上偏移，部分覆盖边框
                        Spacer()
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(product.isSelected ? Color.orange.opacity(0.2) : Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(product.isSelected ? Color.orange : Color.clear, lineWidth: 2)
                )
        )
        .scaleEffect(product.isSelected ? 1.02 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: product.isSelected)
    }
}

// MARK: - 预览

struct SubscriptionProductCard_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black
                .ignoresSafeArea()
            
            VStack(spacing: 16) {
                // 年度订阅（推荐）
                SubscriptionProductCard(
                    product: SubscriptionProduct.from(.yearlyPremium, isSelected: true),
                    onSelect: {}
                )
                
                // 月度订阅
                SubscriptionProductCard(
                    product: SubscriptionProduct.from(.monthlyPremium, isSelected: false),
                    onSelect: {}
                )
                
                // 终身订阅
                SubscriptionProductCard(
                    product: SubscriptionProduct.from(.lifetimePremium, isSelected: false),
                    onSelect: {}
                )
            }
            .padding(20)
        }
    }
}
