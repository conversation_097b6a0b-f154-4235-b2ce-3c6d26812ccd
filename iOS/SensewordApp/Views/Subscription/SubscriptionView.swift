//
//  SubscriptionView.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  订阅页面主视图
//

import SwiftUI
import UIComponents

/// 订阅页面主视图
struct SubscriptionView: View {
    
    // MARK: - 属性
    
    /// 视图模型
    @StateObject private var viewModel = SubscriptionViewModel()
    
    /// 关闭回调
    let onDismiss: () -> Void
    
    // MARK: - 界面构建
    
    var body: some View {
        ZStack {
            // 健康风格背景 - 与主界面保持一致
            KeyframeAnimationWallpaperView(
                animationStyle: .vibrant,
                forceDarkMode: true
            )
            
            // 主要内容
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部标题区域
                    headerSection
                    
                    // 产品选择区域
                    productSelectionSection
                    
                    // 底部操作区域
                    actionSection
                    
                    // 底部安全区域
                    Spacer()
                        .frame(height: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 60)
            }
            .scrollIndicators(.hidden)
            
            // 顶部关闭按钮
            VStack {
                HStack {
                    Spacer()
                    Button(action: onDismiss) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white.opacity(0.8))
                            .background(Color.black.opacity(0.3))
                            .clipShape(Circle())
                    }
                    .padding(.top, 50)
                    .padding(.trailing, 20)
                }
                Spacer()
            }
            
            // 加载指示器
            if case .loading = viewModel.pageState {
                loadingView
            }
        }
        .ignoresSafeArea()
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") {
                viewModel.dismissError()
            }
        } message: {
            if let errorMessage = viewModel.errorMessage {
                Text(errorMessage)
            }
        }
    }
    
    // MARK: - 顶部标题区域
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Text(viewModel.config.title)
                .font(.system(size: 32, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text(viewModel.config.subtitle)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
                .lineSpacing(4)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 8)
    }
    
    // MARK: - 产品选择区域
    
    @ViewBuilder
    private var productSelectionSection: some View {
        switch viewModel.pageState {
        case .ready(let products):
            VStack(spacing: 16) {
                ForEach(products) { product in
                    SubscriptionProductCard(
                        product: product,
                        onSelect: {
                            viewModel.selectProduct(product.id)
                        }
                    )
                }
            }
            
        case .purchasing(let productId):
            VStack(spacing: 16) {
                ForEach(SubscriptionProduct.allProducts) { product in
                    SubscriptionProductCard(
                        product: SubscriptionProduct.from(
                            product.id,
                            isSelected: product.id == productId
                        ),
                        onSelect: {}
                    )
                    .disabled(true)
                    .opacity(0.7)
                }
            }
            
        default:
            EmptyView()
        }
    }
    
    // MARK: - 底部操作区域
    
    private var actionSection: some View {
        VStack(spacing: 16) {
            // 价格总结
            if let selectedProduct = viewModel.selectedProduct {
                priceBreakdownView(for: selectedProduct)
            }
            
            // 继续按钮
            Button(action: {
                viewModel.startPurchase()
            }) {
                HStack {
                    if viewModel.isPurchasing {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    
                    Text(viewModel.continueButtonText)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.orange)
                )
            }
            .disabled(!viewModel.canPurchase)
            .opacity(viewModel.canPurchase ? 1.0 : 0.6)
            
            Text(viewModel.config.cancelAnytimeText)
                .font(.system(size: 14))
                .foregroundColor(.white.opacity(0.7))
            
            // 恢复购买按钮
            Button(action: {
                viewModel.restorePurchases()
            }) {
                HStack {
                    if viewModel.isRestoring {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.6)
                    }
                    
                    Text(viewModel.config.restoreButtonTitle)
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            .disabled(!viewModel.canPurchase)
            .opacity(viewModel.canPurchase ? 1.0 : 0.6)

        }
    }
    
    // MARK: - 价格分解视图
    
    private func priceBreakdownView(for product: SubscriptionProduct) -> some View {
        VStack(spacing: 4) {
            if let originalPrice = product.originalPrice {
                HStack {
                    Text(originalPrice)
                        .font(.system(size: 16))
                        .foregroundColor(.white.opacity(0.5))
                        .strikethrough()

                    Text(product.price)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.white)

                    if product.id == .yearlyPremium {
                        Text("(5折优惠)")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.orange)
                    }
                }
            } else {
                Text(product.price)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
            }
        }
        .padding(.vertical, 8)
    }
    
    // MARK: - 加载视图
    
    private var loadingView: some View {
        ZStack {
            Color.black.opacity(0.3)
                .ignoresSafeArea()
            
            VStack(spacing: 16) {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    .scaleEffect(1.2)
                
                Text("正在加载产品信息...")
                    .font(.system(size: 16))
                    .foregroundColor(.white)
            }
            .padding(32)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black.opacity(0.7))
            )
        }
    }
}
