//
//  WordResultView.swift
//  SensewordApp
//
//  Created by Figma MCP Implementation on 2025-06-28.
//  单词结果显示页面 - 基于Figma设计稿实现
//  Updated by KDD Contract Implementation on 2025-06-28.
//  集成水平舞台交互重构
//

import SwiftUI
import AVFoundation

/// 单词结果显示视图
/// 基于Figma设计稿实现的完整单词内容展示页面
struct WordResultView: View {

    // MARK: - 属性

    /// 单词定义响应数据
    @State var wordData: WordDefinitionResponse

    /// 关闭回调
    let onDismiss: () -> Void

    // 收藏功能已移至悬浮操作栏

    /// 音频播放器（保留用于手动播放）

    /// 全局音频管理器
    @ObservedObject private var globalAudioManager = GlobalAudioManager.shared



    /// 是否已经触发过自动播放
    @State private var hasTriggeredAutoPlay = false

    /// 是否正在刷新数据
    @State private var isRefreshing = false

    // MARK: - 初始化

    init(wordData: WordDefinitionResponse, onDismiss: @escaping () -> Void) {
        self._wordData = State(initialValue: wordData)
        self.onDismiss = onDismiss
    }
    
    // MARK: - 界面构建
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部固定区域 - 单词标题和音标
            VStack(spacing: 0) {
                // 单词标题区域
                wordHeaderSection

                // 音标区域
                phoneticSection
            }
            .padding(.horizontal, 16)
            .background(Color.clear)
            .zIndex(1) // 确保顶部区域在最上层

            // 页面级水平切换内容区域 - 恢复原有布局结构
            WordPageContainer(wordData: wordData)
                .id(wordData.word) // 使用单词作为唯一ID，确保单词变化时重新创建
                .zIndex(0)
        }
        .background(Color.clear)
        .ignoresSafeArea()
        .onAppear {
            // 延迟触发自动播放，确保视图完全加载
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                triggerAutoAudioPlayback()
            }
        }
        .onChange(of: wordData.content.phoneticSymbols) { _ in
            // 当音标数据更新时，重置并重新检查是否可以自动播放
            hasTriggeredAutoPlay = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                triggerAutoAudioPlayback()
            }
        }
        .onDisappear {
            // 停止音频播放，避免与其他页面冲突
            globalAudioManager.stopCurrentPlayback()
        }
    }
    
    // MARK: - 单词标题区域

    private var wordHeaderSection: some View {
        VStack(spacing: 16) {
            // 单词标题 - 响应式字体大小，支持长单词
            Text(wordData.word.capitalized)
                .font(.system(size: adaptiveFontSize, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .lineLimit(2) // 允许最多两行显示
                .minimumScaleFactor(0.7) // 允许缩放到70%
        }
        .frame(maxWidth: .infinity)
        .padding(.top, 60)
        .padding(.bottom, 16)
    }

    /// 根据单词长度计算自适应字体大小
    private var adaptiveFontSize: CGFloat {
        let wordLength = wordData.word.count
        if wordLength <= 8 {
            return 36 // 短单词使用大字体
        } else if wordLength <= 12 {
            return 32 // 中等长度单词
        } else if wordLength <= 16 {
            return 28 // 较长单词
        } else {
            return 24 // 很长的单词使用小字体
        }
    }
    
    // MARK: - 音标区域

    @ViewBuilder
    private var phoneticSection: some View {
        // 只显示美式音标，简化界面，用户可在设置中调整偏好
        if let preferredPhonetic = preferredPhonetic {
                HStack(spacing: 8) {
                    // 音标类型标签
                    Text(preferredPhonetic.type.displayName)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(Color.gray)

                    // 音标显示 - 适中字体
                    Text(preferredPhonetic.symbol)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(Color.gray)

                    // 播放按钮 - 始终显示，保持灰色
                    Button(action: {
                        // 点击音标时，播放该音标
                        playPhoneticAudio(preferredPhonetic)
                    }) {
                        Image(systemName: "speaker.wave.1.fill")
                            .font(.system(size: 20, weight: .medium))
                            .foregroundColor(Color.gray)  // 保持灰色
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .frame(maxWidth: .infinity)
                .padding(.bottom, 22)
            } else {
                // 如果没有音标数据，显示空白区域保持布局一致
                Color.clear
                    .frame(height: 22)
            }
    }

    /// 获取首选音标（根据用户设置偏好）
    private var preferredPhonetic: PhoneticSymbol? {
        let phonetics = wordData.content.phoneticSymbols

        // 使用全局设置管理器获取用户偏好的音标
        return GlobalSettingsManager.shared.getPreferredPhonetic(from: phonetics)
    }

    // MARK: - 核心释义区域

    private var coreDefinitionSection: some View {
        VStack(alignment: .center, spacing: 12) {
            // 标签
            Text("释义")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)

            // 核心定义
            Text(wordData.content.coreDefinition)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 32)
    }

    // MARK: - 语境解释区域

    private var contextualExplanationSection: some View {
        VStack(spacing: 24) {
            // 母语者意图
            contextualItem(
                tag: "意图",
                content: wordData.content.contextualExplanation.nativeSpeakerIntent
            )

            // 想象
            contextualItem(
                tag: "想象",
                content: wordData.content.contextualExplanation.vividImagery
            )

            // 词源
            contextualItem(
                tag: "词源",
                content: wordData.content.contextualExplanation.etymologicalEssence
            )
        }
        .padding(.bottom, 32)
    }

    // MARK: - 语境解释项目

    private func contextualItem(tag: String, content: String) -> some View {
        VStack(alignment: .center, spacing: 12) {
            // 标签
            Text(tag)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)

            // 内容
            Text(content)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - 使用示例区域

    private var usageExamplesSection: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("例句")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)

            // 示例列表
            ForEach(0..<wordData.content.usageExamples.count, id: \.self) { index in
                usageExampleCategory(category: wordData.content.usageExamples[index])
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 32)
    }

    // MARK: - 使用示例分类

    private func usageExampleCategory(category: UsageExampleCategory) -> some View {
        VStack(alignment: .center, spacing: 16) {
            // 分类标题
            Text(category.category)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 示例列表
            ForEach(0..<category.examples.count, id: \.self) { index in
                usageExampleItem(example: category.examples[index])
            }
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - 使用示例项目

    private func usageExampleItem(example: UsageExample) -> some View {
        VStack(alignment: .center, spacing: 8) {
            // 英文句子
            Text(example.english)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 中文翻译
            Text(example.translation)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - 使用场景区域

    private var usageScenariosSection: some View {
        VStack(alignment: .center, spacing: 24) {
            // 标签
            Text("场景")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(Color.secondary)

            // 场景列表
            ForEach(0..<wordData.content.usageScenarios.count, id: \.self) { index in
                usageScenarioItem(scenario: wordData.content.usageScenarios[index])
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 32)
    }

    // MARK: - 使用场景项目

    private func usageScenarioItem(scenario: UsageScenario) -> some View {
        VStack(alignment: .center, spacing: 12) {
            // 场景标题
            Text(scenario.category)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 场景描述
            Text(scenario.context)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
    }

    // MARK: - 用法注释区域

    private var usageNotesSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // 标签
            HStack {
                Spacer()
                Text("用法")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(Color.secondary)
                Spacer()
            }

            // 注释列表
            ForEach(0..<wordData.content.usageNotes.count, id: \.self) { index in
                usageNoteItem(note: wordData.content.usageNotes[index])
            }
        }
        .padding(.bottom, 32)
    }

    // MARK: - 用法注释项目

    private func usageNoteItem(note: UsageNote) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 方面标题
            Text(note.aspect)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 解释
            Text(note.explanation)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.leading)

            // 示例
            ForEach(0..<note.examples.count, id: \.self) { index in
                usageNoteExampleItem(example: note.examples[index])
            }
        }
    }

    // MARK: - 用法注释示例项目

    private func usageNoteExampleItem(example: UsageNoteExample) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 英文句子
            Text(example.sentence)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 中文翻译
            Text(example.translation)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)
        }
    }

    // MARK: - 同义词区域

    private var synonymsSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // 标签
            HStack {
                Spacer()
                Text("同义词")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(Color.secondary)
                Spacer()
            }

            // 同义词列表
            ForEach(0..<wordData.content.synonyms.count, id: \.self) { index in
                synonymItem(synonym: wordData.content.synonyms[index])
            }
        }
        .padding(.bottom, 32)
    }

    // MARK: - 同义词项目

    private func synonymItem(synonym: Synonym) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // 同义词
            Text(synonym.word)
                .font(.system(size: 16, weight: .bold))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 解释
            Text(synonym.explanation)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.leading)

            // 示例
            ForEach(0..<synonym.examples.count, id: \.self) { index in
                synonymExampleItem(example: synonym.examples[index])
            }
        }
    }

    // MARK: - 同义词示例项目

    private func synonymExampleItem(example: SynonymExample) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            // 英文句子
            Text(example.sentence)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.white)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)

            // 中文翻译
            Text(example.translation)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(Color.secondary)
                .lineSpacing(4)
                .multilineTextAlignment(.center)
                .frame(maxWidth: .infinity)
        }
    }

    // MARK: - 音频播放方法

    /// 自动触发音频播放
    /// 检查音标是否有音频URL，如果有则自动开始预加载和播放
    private func triggerAutoAudioPlayback() {
        // 避免重复触发
        guard !hasTriggeredAutoPlay else {
            NSLog("🎵 WordResultView: 已经触发过自动播放，跳过")
            return
        }

        // 检查是否有有效的音频URL
        let validPhonetics = wordData.content.phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                return false
            }
            return true
        }

        guard !validPhonetics.isEmpty else {
            NSLog("⚠️ WordResultView: 没有有效的音频URL，跳过自动播放")
            return
        }

        NSLog("🎵 WordResultView: 开始自动音频播放，共 \(validPhonetics.count) 个音标")

        // 标记已触发
        hasTriggeredAutoPlay = true

        // 使用全局音频管理器进行自动播放
        Task {
            await globalAudioManager.playPhoneticSequence(
                validPhonetics,
                autoPlay: true
            ) {
                NSLog("🎉 WordResultView: 自动音频播放完成")
            }
        }
    }

    /// 播放所有音标音频（手动触发）
    private func playAllPhoneticAudio() {
        // 检查是否有有效的音频URL
        let validPhonetics = wordData.content.phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                return false
            }
            return true
        }

        guard !validPhonetics.isEmpty else {
            NSLog("⚠️ WordResultView: 没有有效的音频URL，无法播放")
            return
        }

        NSLog("🎵 WordResultView: 手动播放所有音标音频，共 \(validPhonetics.count) 个音标")

        // 使用全局音频管理器播放
        Task {
            await globalAudioManager.playPhoneticSequence(
                validPhonetics,
                autoPlay: true
            ) {
                NSLog("🎉 WordResultView: 手动音频播放完成")
            }
        }
    }

    /// 播放音标音频（手动点击时使用）
    /// - Parameter phonetic: 音标符号
    private func playPhoneticAudio(_ phonetic: PhoneticSymbol) {
        guard phonetic.audioUrl != nil else {
            NSLog("⚠️ WordResultView: 音标音频URL无效 - \(phonetic.symbol)")
            return
        }

        NSLog("🔊 WordResultView: 开始播放音标音频 - \(phonetic.type.displayName): \(phonetic.symbol)")

        // 使用全局音频管理器播放单个音标
        Task {
            do {
                try await globalAudioManager.playPhonetic(phonetic)
                NSLog("✅ WordResultView: 音频播放成功")
            } catch {
                NSLog("❌ WordResultView: 音频播放失败 - \(error)")
            }
        }
    }

    // MARK: - 动态内容块

    /// 创建内容块
    /// - Parameters:
    ///   - id: 内容块的唯一标识符
    ///   - content: 内容块的视图内容
    /// - Returns: 内容块视图
    @ViewBuilder
    private func contentBlock<Content: View>(id: String, @ViewBuilder content: @escaping () -> Content) -> some View {
        content()
            .id(id)
    }





}

/// 可控制音频播放的单词结果显示视图
/// 用于无限内容流，可以控制是否自动播放音频
struct ControlledWordResultView: View {

    // MARK: - 属性

    /// 单词定义响应数据（固定数据，不更新）
    let wordData: WordDefinitionResponse

    /// 关闭回调
    let onDismiss: () -> Void

    /// 是否应该自动播放音频
    let shouldAutoPlayAudio: Bool

    // 收藏功能已移至悬浮操作栏

    /// 音频播放器（保留用于手动播放）

    /// 全局音频管理器
    @ObservedObject private var globalAudioManager = GlobalAudioManager.shared

    /// 是否已经触发过自动播放
    @State private var hasTriggeredAutoPlay = false

    /// 是否正在刷新数据
    @State private var isRefreshing = false

    // MARK: - 初始化

    init(wordData: WordDefinitionResponse, onDismiss: @escaping () -> Void, shouldAutoPlayAudio: Bool) {
        self.wordData = wordData
        self.onDismiss = onDismiss
        self.shouldAutoPlayAudio = shouldAutoPlayAudio
    }

    // MARK: - 视图

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 0) {
                // 顶部单词标题区域
                topWordSection

                // 水平滑动页面容器
                WordPageContainer(wordData: wordData)
                    .frame(height: UIScreen.main.bounds.height * 0.7)
            }
        }
        .scrollIndicators(.hidden)
        .background(Color.clear)
        .padding(.horizontal, 16)
        .padding(.top, 60)
        .padding(.bottom, 22)
        .onAppear {
            // 只有在允许自动播放时才触发
            if shouldAutoPlayAudio {
                // 延迟触发自动播放，确保视图完全加载
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    triggerAutoAudioPlayback()
                }
            } else {
                print("🔇 ControlledWordResultView: 禁止自动播放音频: \(wordData.word)")
            }
        }
        .onChange(of: wordData.content.phoneticSymbols) { _ in
            // 当音标数据更新时，重置并重新检查是否可以自动播放
            hasTriggeredAutoPlay = false
            if shouldAutoPlayAudio {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    triggerAutoAudioPlayback()
                }
            }
        }
        .onDisappear {
            // 修复：预加载卡片消失时不停止音频，避免中断正在播放的音频
            // 音频应该由用户主动操作或新卡片的音频播放来管理
            print("🎵 ControlledWordResultView: 卡片消失，保持音频播放: \(wordData.word) (shouldAutoPlayAudio: \(shouldAutoPlayAudio))")
        }
    }

    // MARK: - 顶部单词区域

    /// 顶部单词标题区域
    private var topWordSection: some View {
        VStack(spacing: 16) {
            // 单词标题 - 水平居中
            Text(wordData.word.capitalized)
                .font(.system(size: 36, weight: .bold, design: .default))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)

            // 音标区域 - 水平居中
            phoneticSymbolsSection
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, 24)
    }

    /// 音标符号区域（优化：只显示用户偏好的音标）
    private var phoneticSymbolsSection: some View {
        Group {
            // 只显示用户偏好的音标，简化界面
            if let preferredPhonetic = preferredPhonetic {
                HStack(spacing: 8) {
                    // 音标类型标签
                    Text(preferredPhonetic.type.displayName)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.white.opacity(0.7))

                    // 音标符号
                    Text(preferredPhonetic.symbol)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)

                    // 播放按钮
                    Button(action: {
                        playPhoneticAudio(preferredPhonetic)
                    }) {
                        Image(systemName: "speaker.wave.2.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            } else {
                // 如果没有音标数据，显示空白区域保持布局一致
                Color.clear
            }
        }
        .frame(maxWidth: .infinity, minHeight: 22)
    }

    /// 获取首选音标（根据用户设置偏好）
    private var preferredPhonetic: PhoneticSymbol? {
        let phonetics = wordData.content.phoneticSymbols

        // 使用全局设置管理器获取用户偏好的音标
        return GlobalSettingsManager.shared.getPreferredPhonetic(from: phonetics)
    }

    // MARK: - 音频播放方法

    /// 自动触发音频播放
    /// 检查音标是否有音频URL，如果有则自动开始预加载和播放
    private func triggerAutoAudioPlayback() {
        // 避免重复触发
        guard !hasTriggeredAutoPlay else {
            NSLog("🎵 ControlledWordResultView: 已经触发过自动播放，跳过")
            return
        }

        // 检查是否有有效的音频URL
        let validPhonetics = wordData.content.phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                return false
            }
            return true
        }

        guard !validPhonetics.isEmpty else {
            NSLog("⚠️ ControlledWordResultView: 没有有效的音频URL，跳过自动播放")
            return
        }

        NSLog("🎵 ControlledWordResultView: 开始自动音频播放，共 \(validPhonetics.count) 个音标")

        // 标记已触发
        hasTriggeredAutoPlay = true

        // 使用全局音频管理器进行自动播放
        Task {
            await globalAudioManager.playPhoneticSequence(
                validPhonetics,
                autoPlay: true
            ) {
                NSLog("🎉 ControlledWordResultView: 自动音频播放完成")
            }
        }
    }

    /// 播放所有音标音频（手动触发）
    private func playAllPhoneticAudio() {
        // 检查是否有有效的音频URL
        let validPhonetics = wordData.content.phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                return false
            }
            return true
        }

        guard !validPhonetics.isEmpty else {
            NSLog("⚠️ ControlledWordResultView: 没有有效的音频URL，无法播放")
            return
        }

        NSLog("🎵 ControlledWordResultView: 手动播放所有音标音频，共 \(validPhonetics.count) 个音标")

        // 使用全局音频管理器播放
        Task {
            await globalAudioManager.playPhoneticSequence(
                validPhonetics,
                autoPlay: true
            ) {
                NSLog("🎉 ControlledWordResultView: 手动音频播放完成")
            }
        }
    }

    /// 播放音标音频（手动点击时使用）
    /// - Parameter phonetic: 音标符号
    private func playPhoneticAudio(_ phonetic: PhoneticSymbol) {
        guard phonetic.audioUrl != nil else {
            NSLog("⚠️ ControlledWordResultView: 音标音频URL无效 - \(phonetic.symbol)")
            return
        }

        NSLog("🔊 ControlledWordResultView: 开始播放音标音频 - \(phonetic.type.displayName): \(phonetic.symbol)")

        // 使用全局音频管理器播放单个音标
        Task {
            do {
                try await globalAudioManager.playPhonetic(phonetic)
                NSLog("✅ ControlledWordResultView: 音频播放成功")
            } catch {
                NSLog("❌ ControlledWordResultView: 音频播放失败 - \(error)")
            }
        }
    }


}

// MARK: - 预览
#if DEBUG
struct WordResultView_Previews: PreviewProvider {
    static var previews: some View {
        WordResultView(wordData: WordDefinitionResponse.mock(), onDismiss: {})
            .preferredColorScheme(.dark)
    }
}

// MARK: - Mock 数据扩展
extension WordDefinitionResponse {
    static func mock() -> WordDefinitionResponse {
        return WordDefinitionResponse(
            word: "Serendipity",
            metadata: WordMetadata(
                wordFrequency: "medium",
                relatedConcepts: ["luck", "discovery", "chance"]
            ),
            content: WordContent(
                difficulty: "intermediate",
                phoneticSymbols: [
                    PhoneticSymbol.british("/ˌser.ənˈdɪp.ə.ti/"),
                    PhoneticSymbol.american("/ˌser.ənˈdɪp.ə.ti/")
                ],
                coreDefinition: "serendipity 的核心意思是\"意外发现珍宝的运气\"，特指在寻找其他事物时，无意中发现有价值或令人愉快的事物。",
                contextualExplanation: ContextualExplanation(
                    nativeSpeakerIntent: "当母语者使用 'serendipity' 时，他们想要表达的是一种积极的、偶然的发现。这不仅仅是运气好，更强调了发现的\"意外性\"和\"愉快性\"，而且通常是在你并没有刻意去寻找那个特定事物的情况下发生的",
                    emotionalResonance: "这个词带有积极、惊喜和一点点魔法的感觉。它让人联想到出乎意料的好运，一种\"柳暗花明又一村\"的惊喜感。使用 'serendipity' 能够唤起一种轻松愉悦的情绪，因为它指向的是那些不期而遇的美好。",
                    vividImagery: "想象一下，你正在雨中寻找一把伞，结果却在一家旧书店里偶然发现了一本你一直想读的绝版书。那一刻的惊喜和满足感，就是 'serendipity'。或者，你在旅行中迷了路，却意外走进了一个风景如画、充满当地风情的小镇，这种美好的意外就是 'serendipity'。",
                    etymologicalEssence: "\"Serendipity\" 这个词是由英国作家霍勒斯·沃波尔在1754年创造的，灵感来源于一个叫做《serendip 的三个王子》的波斯童话。故事讲述了王子们总是在旅途中意外地发现他们并非刻意寻找的东西。沃波尔用这个词来描述他们那种\"善于意外发现有用或愉快事物\"的能力，这种能力源于一种敏锐的观察力和对机遇的开放态度。"
                ),
                usageExamples: [
                    UsageExampleCategory(
                        category: "描述幸运的意外发现",
                        examples: [
                            UsageExample(
                                english: "Finding that rare book in a dusty antique shop was a moment of pure serendipity.",
                                translation: "在尘土飞扬的古董店里找到那本稀有的书，真是一次纯粹的serendipity。",
                                audioUrl: nil,
                                phraseBreakdown: nil
                            )
                        ]
                    ),
                    UsageExampleCategory(
                        category: "形容科学或学术上的意外发现",
                        examples: [
                            UsageExample(
                                english: "Penicillin was discovered by serendipity when Fleming noticed mold inhibiting bacterial growth.",
                                translation: "盘尼西林是 Fleming 在偶然注意到霉菌抑制了细菌生长时，通过serendipity发现的。",
                                audioUrl: nil,
                                phraseBreakdown: nil
                            )
                        ]
                    )
                ],
                usageScenarios: [
                    UsageScenario(
                        category: "旅行与探索",
                        relevance: "high",
                        context: "在旅行中，偏离计划，偶然发现当地美食、隐藏的景点或有趣的文化习俗，这种体验可以被描述为一种 serendipity。"
                    ),
                    UsageScenario(
                        category: "学术与科研",
                        relevance: "high",
                        context: "科学家在实验中，并非预期地发现了某种新的现象、材料或治疗方法，这常被归功于 serendipity，但也离不开他们的敏锐观察力和科学素养。"
                    ),
                    UsageScenario(
                        category: "日常生活",
                        relevance: "medium",
                        context: "在购物、社交或学习过程中，意外遇到合意的东西、新朋友或有用的信息，这种不期而遇的美好就是 serendipity。"
                    ),
                    UsageScenario(
                        category: "人际关系",
                        relevance: "medium",
                        context: "例如，在一次社交活动中，偶然认识了对你事业或生活有重大帮助的人，这就是一次美好的 serendipity。"
                    )
                ],
                collocations: [],
                usageNotes: [
                    UsageNote(
                        aspect: "与'luck'和'chance'的区别",
                        explanation: "'Luck' (运气) 是更广泛的概念，指任何好运，不一定涉及发现。'Chance' (机会/偶然) 指的是可能性或随机性，可以是中性的。而 'serendipity' 特指在寻找某物时，意外地发现另一件有价值或愉快的事物，它结合了偶然性、积极性以及\"发现\"的元素。",
                        examples: [
                            UsageNoteExample(
                                sentence: "It was pure luck that I found a parking spot.",
                                translation: "我能找到停车位真是运气好。"
                            ),
                            UsageNoteExample(
                                sentence: "By chance, I ran into my old friend at the station.",
                                translation: "偶然间，我在车站遇到了我的老朋友。"
                            ),
                            UsageNoteExample(
                                sentence: "It was serendipity that I found this antique map while looking for old coins.",
                                translation: "我在寻找旧硬币时偶然发现了这幅古董地图，这就是serendipity。"
                            )
                        ]
                    )
                ],
                synonyms: [
                    Synonym(
                        word: "fluke",
                        explanation: "'Fluke' 指的是一个非常罕见或不太可能发生的偶然的好运或成功。它强调事件的极端偶然性，有时也带有一点点意外的惊喜，但通常不如 'serendipity' 那么强调\"在寻找某物时发现另一物\"的方面，也没有那么积极的学术或探索色彩。例如，一个足球比赛中意外进球可能被称为 'fluke'。",
                        examples: [
                            SynonymExample(
                                sentence: "The winning goal was a complete fluke.",
                                translation: "制胜一球完全是侥幸。"
                            )
                        ]
                    ),
                    Synonym(
                        word: "chance discovery",
                        explanation: "这是对 'serendipity' 最接近的描述性短语。它强调了发现的偶然性，但可能不如 'serendipity' 本身那样具有文学色彩或历史故事感。'Serendipity' 暗示了一种更积极的、甚至是巧妙的意外收获。",
                        examples: [
                            SynonymExample(
                                sentence: "The discovery of the new compound was a chance discovery during unrelated experiments.",
                                translation: "这种新化合物的发现是在无关实验中偶然发现的。"
                            )
                        ]
                    )
                ]
            ),
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )
    }
}
#endif


