//
//  SearchView.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  搜索界面 - 提供完整的搜索用户体验
//

import SwiftUI
import Foundation

// MARK: - 搜索界面

/// 搜索覆盖层界面
/// 核心职责：提供手势触发的搜索覆盖层，支持高斯模糊背景和动画效果
/// 技术特点：支持下拉手势触发、覆盖层动画、半透明背景、响应式布局
struct SearchView: View {

    // MARK: - 状态管理

    /// 搜索视图模型
    @StateObject private var viewModel: SearchViewModel

    /// 焦点状态
    @FocusState private var isSearchFieldFocused: Bool

    /// 键盘高度
    @State private var keyboardHeight: CGFloat = 0

    /// 当前单词数据（用于收藏功能）
    let currentWordData: WordDefinitionResponse?

    /// 是否显示单词卡片状态
    let isShowingWordCard: Bool

    /// 收藏状态
    @State private var isBookmarked: Bool = false

    /// 是否显示设置页面
    @State private var showSettings: Bool = false

    /// 本地收藏ViewModel
    @StateObject private var bookmarkViewModel: LocalBookmarkViewModel

    // MARK: - 初始化

    /// 初始化搜索覆盖层
    /// - Parameters:
    ///   - searchService: 搜索服务
    ///   - localBookmarkService: 本地收藏服务
    ///   - currentWordData: 当前单词数据
    ///   - isShowingWordCard: 是否显示单词卡片状态
    init(
        searchService: SearchServiceProtocol,
        localBookmarkService: LocalBookmarkServiceProtocol,
        currentWordData: WordDefinitionResponse? = nil,
        isShowingWordCard: Bool = false
    ) {
        self._viewModel = StateObject(wrappedValue: SearchViewModel(searchService: searchService))
        self._bookmarkViewModel = StateObject(wrappedValue: LocalBookmarkViewModel(localBookmarkService: localBookmarkService))
        self.currentWordData = currentWordData
        self.isShowingWordCard = isShowingWordCard
    }
    
    // MARK: - 界面构建

    var body: some View {
        ZStack {
            // 搜索覆盖层面板 - 只在激活时显示并拦截手势
            if viewModel.isSearchActive {
                searchOverlayPanel
                    .transition(.asymmetric(
                        insertion: .move(edge: .top).combined(with: .opacity),
                        removal: .customSearchPanelRemoval
                    ))
                    .animation(.easeInOut(duration: 0.4), value: viewModel.isSearchActive)
                    .zIndex(2) // 确保搜索面板在最上层
            }

            // 悬浮按钮区域 - 使用独立的容器，不阻挡下层手势
            if keyboardHeight == 0 {
                VStack {
                    Spacer()
                    HStack {
                        // 左下角收藏按钮 - 只在显示单词卡片时显示
                        if isShowingWordCard && currentWordData != nil {
                            FloatingBookmarkButton(
                                word: currentWordData?.word ?? "",
                                language: .chinese,
                                viewModel: bookmarkViewModel
                            )
                            .onAppear {
                                print("🔖 [SearchView] 收藏按钮出现，单词: \(currentWordData?.word ?? "unknown")")
                                Task {
                                    await bookmarkViewModel.checkBookmarkStatus(word: currentWordData?.word ?? "", language: .chinese)
                                }
                            }
                            .padding(.leading, FloatingButtonConstants.baseMargin)
                            .padding(.bottom, FloatingButtonConstants.bottomMargin)
                        }

                        Spacer()

                        // 右下角搜索操作栏 - 只在非搜索状态时显示
                        if viewModel.shouldShowSearchButton {
                            FloatingActionBar(
                                onSearchTap: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        viewModel.isSearchActive = true
                                    }
                                },
                                onProfileTap: {
                                    showSettings = true
                                }
                            )
                            .padding(.trailing, FloatingButtonConstants.baseMargin)
                            .padding(.bottom, FloatingButtonConstants.bottomMargin)
                        }
                    }
                }
            }
        }
        .onAppear {
            setupView()
        }
        .onDisappear {
            cleanupView()
        }
        .fullScreenCover(isPresented: $showSettings) {
            SettingsView(onDismiss: {
                showSettings = false
            })
        }
        .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { notification in
            handleKeyboardShow(notification)
        }
        .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
            handleKeyboardHide()
        }
    }
    
    // MARK: - 悬浮操作栏
    // 悬浮操作栏已移至 FloatingActionBar 组件
    
    // MARK: - 搜索覆盖层面板

    private var searchOverlayPanel: some View {
        ZStack {
            // 透明磨砂玻璃背景，能看到下面的渐变壁纸
            Color.black
                .opacity(0.3)  // 降低透明度，让下面的壁纸可见
                .background(.ultraThinMaterial, in: Rectangle())  // 使用超薄材质保持磨砂效果
                .ignoresSafeArea()
                .onTapGesture {
                    viewModel.dismissSearch()
                }

            // 搜索面板内容
            VStack(spacing: 0) {
                // 搜索栏
                searchInputBar
                    .padding(.horizontal, 20)
                    .padding(.top, 60)

                // 内容区域 - 让它占满剩余空间，不要用padding压缩
                searchContentArea
                    .frame(maxWidth: .infinity, maxHeight: .infinity)  // 占满剩余空间
                    .layoutPriority(1)  // 给内容区域更高的布局优先级
            }
            .frame(maxWidth: .infinity)  // 🔧 关键修复：让VStack占满屏幕宽度
            .ignoresSafeArea(.container, edges: .top)
        }
        .onAppear {
            // 延迟自动聚焦，此时键盘应该已经预热完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isSearchFieldFocused = true
                NSLog("🔍 SearchView: 搜索界面已出现，自动聚焦搜索框（键盘已预热）")
            }
        }
    }

    // MARK: - 搜索输入栏

    private var searchInputBar: some View {
        HStack(spacing: 8) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
                .font(.system(size: 18, weight: .medium))

            TextField("不止于词，抵达心语", text: $viewModel.searchQuery)
                .focused($isSearchFieldFocused)
                .textFieldStyle(PlainTextFieldStyle())
                .font(.system(size: 18))
                .submitLabel(.search)
                .onSubmit {
                    // 回车时直接搜索用户输入的单词并立即关闭搜索面板
                    viewModel.performSearchAndDismiss()
                }

            // 清除按钮
            if !viewModel.searchQuery.isEmpty {
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        viewModel.clearSearch()
                        isSearchFieldFocused = true
                    }
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                }
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, 18)  // 增加内边距
        .padding(.vertical, 14)    // 增加垂直内边距
        .background(
            // 透明磨砂玻璃效果，能看到下面的渐变壁纸
            Color.black.opacity(0.2)
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 14))
        )
        .overlay(
            // 添加微妙的白色边框，增强磨砂效果
            RoundedRectangle(cornerRadius: 14)
                .stroke(Color.white.opacity(0.2), lineWidth: 0.5)
        )
        .clipShape(RoundedRectangle(cornerRadius: 14))
        .padding(.horizontal, 16)  // 减少外边距，增加搜索框宽度
        .animation(.easeInOut(duration: 0.3), value: viewModel.searchQuery.isEmpty)
    }

    // MARK: - 搜索内容区域

    private var searchContentArea: some View {
        ZStack {
            // 只在有实际搜索建议时显示建议列表
            if viewModel.showSuggestions && viewModel.hasResults {
                // 搜索建议列表
                suggestionsList
                    .transition(.opacity)
            } else {
                // 空白状态 - 其他所有情况都显示空白
                defaultStateView
                    .transition(.opacity)
            }


        }
        .animation(.easeInOut(duration: 0.2), value: viewModel.showSuggestions)
        .animation(.easeInOut(duration: 0.2), value: viewModel.hasResults)
        .animation(.easeInOut(duration: 0.3), value: keyboardHeight)
        .padding(.top, 20)
    }
    
    // MARK: - 搜索建议列表

    private var suggestionsList: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                // 结果数量提示
                HStack {
                    Text(viewModel.resultCountText)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)

                // 建议项列表
                ForEach(viewModel.suggestions, id: \.word) { suggestion in
                    SuggestionRow(suggestion: suggestion) {
                        withAnimation(.easeInOut(duration: 0.6)) {
                            viewModel.selectSuggestionAndDismiss(suggestion)
                            isSearchFieldFocused = false
                        }
                    }
                    .padding(.horizontal, 16)

                    if suggestion.word != viewModel.suggestions.last?.word {
                        Divider()
                            .padding(.leading, 16)
                    }
                }

                // 添加键盘高度的底部间距，确保内容不被键盘遮挡
                Spacer()
                    .frame(height: keyboardHeight + 40)  // 键盘高度 + 额外安全间距
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)  // 确保ScrollView占满可用空间
        .scrollDismissesKeyboard(.interactively)
        .scrollBounceBehavior(.basedOnSize, axes: .vertical)
        .scrollIndicators(.hidden)  // 隐藏滚动指示器
    }
    
    // MARK: - 搜索历史视图
    




    // MARK: - 默认状态视图

    private var defaultStateView: some View {
        VStack {
            Spacer()
        }
    }






}

// MARK: - 自定义动画扩展

extension AnyTransition {
    static var customSearchPanelRemoval: AnyTransition {
        .asymmetric(
            insertion: .identity,
            removal: .modifier(
                active: SearchPanelRemovalModifier(progress: 1.0),
                identity: SearchPanelRemovalModifier(progress: 0.0)
            )
        )
    }
}

struct SearchPanelRemovalModifier: ViewModifier {
    let progress: Double

    func body(content: Content) -> some View {
        let screenHeight = UIScreen.main.bounds.height

        // 位移：从0到-screenHeight
        let yOffset = -screenHeight * progress

        // 透明度：在前2/3的移动中保持1.0，在最后1/3中从1.0变为0
        let opacityThreshold = 2.0 / 3.0  // 在移动到2/3时开始透明度变化
        let opacity: Double = {
            if progress <= opacityThreshold {
                return 1.0
            } else {
                // 在最后1/3的移动中，透明度从1.0线性变为0
                let fadeProgress = (progress - opacityThreshold) / (1.0 - opacityThreshold)
                return 1.0 - fadeProgress
            }
        }()

        content
            .offset(y: yOffset)
            .opacity(opacity)
    }
}

// MARK: - 私有方法

private extension SearchView {

    /// 设置视图
    func setupView() {
        viewModel.registerNotifications()
    }

    /// 清理视图
    func cleanupView() {
        viewModel.unregisterNotifications()
    }

    /// 处理键盘显示
    /// - Parameter notification: 通知对象
    func handleKeyboardShow(_ notification: Notification) {
        if let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect {
            keyboardHeight = keyboardFrame.height
        }
    }

    /// 处理键盘隐藏
    func handleKeyboardHide() {
        keyboardHeight = 0
    }
}
