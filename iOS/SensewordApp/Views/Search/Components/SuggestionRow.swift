//
//  SuggestionRow.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  搜索建议行组件
//

import SwiftUI

// MARK: - 搜索建议行

/// 搜索建议行组件
/// 核心职责：展示单个搜索建议的信息，包括单词和释义
/// 技术特点：支持点击交互、视觉反馈、无障碍访问
struct SuggestionRow: View {
    
    // MARK: - 属性
    
    /// 搜索建议
    let suggestion: SearchSuggestion
    
    /// 点击回调
    let onTap: () -> Void
    
    /// 是否按下状态
    @State private var isPressed = false
    
    // MARK: - 界面构建
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 6) {
                // 单词
                Text(suggestion.word.capitalized)
                    .font(.system(size: 17, weight: .medium))
                    .foregroundColor(.primary)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 释义 - 移除行数限制，允许完整显示
                Text(suggestion.definition)
                    .font(.system(size: 15))
                    .foregroundColor(.secondary)
                    .lineLimit(nil)  // 允许多行显示
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .fixedSize(horizontal: false, vertical: true)  // 确保垂直方向自适应
            }
            .padding(.vertical, 16)  // 增加垂直内边距，给内容更多空间
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .opacity(isPressed ? 0.8 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(suggestion.word), \(suggestion.definition)")
        .accessibilityHint("点击查看详细释义")
    }
}

// MARK: - 预览

#if DEBUG
struct SuggestionRow_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 0) {
            SuggestionRow(
                suggestion: SearchSuggestion.mock(
                    word: "apple",
                    definition: "苹果；苹果公司"
                )
            ) {
                print("Tapped apple")
            }

            Divider()

            SuggestionRow(
                suggestion: SearchSuggestion.mock(
                    word: "sophisticated",
                    definition: "复杂的；精密的；老练的",
                    score: 0.8,
                    hasContent: true
                )
            ) {
                print("Tapped sophisticated")
            }

            Divider()

            SuggestionRow(
                suggestion: SearchSuggestion.mock(
                    word: "computer",
                    definition: "计算机；电脑",
                    score: 0.6,
                    hasContent: false
                )
            ) {
                print("Tapped computer")
            }
        }
        .padding()
        .previewLayout(.sizeThatFits)
        .previewDisplayName("搜索建议行")
    }
}
#endif
