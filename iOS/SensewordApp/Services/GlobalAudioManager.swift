//
//  GlobalAudioManager.swift
//  SensewordApp
//
//  Created by AI Assistant on 2025-06-29.
//  全局音频播放管理器 - 统一管理所有音频播放，避免冲突
//

import Foundation
import AVFoundation
import SwiftUI

/// 播放状态
enum GlobalAudioState: Equatable {
    case idle                    // 空闲
    case loading                 // 加载中
    case playing                 // 播放中
    case paused                  // 暂停
    case completed               // 完成
    case error(String)           // 错误

    static func == (lhs: GlobalAudioState, rhs: GlobalAudioState) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle), (.loading, .loading), (.playing, .playing), (.paused, .paused), (.completed, .completed):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

/// 全局音频管理器
/// 统一管理所有音频播放，确保同时只有一个音频在播放
@MainActor
public class GlobalAudioManager: NSObject, ObservableObject {
    
    // MARK: - 单例
    public static let shared = GlobalAudioManager()
    
    // MARK: - 发布属性
    @Published var state: GlobalAudioState = .idle
    @Published var progress: Double = 0.0
    @Published var currentAudioDescription: String = ""
    
    // MARK: - 私有属性
    private var audioPlayer: AVAudioPlayer?
    private var progressTimer: Timer?
    private var audioCache: [String: Data] = [:]
    
    // 顺序播放相关
    private var sequenceQueue: [PhoneticSymbol] = []
    private var currentSequenceIndex = 0
    private var sequenceCompletionHandler: (() -> Void)?
    
    // MARK: - 初始化
    private override init() {
        super.init()
        setupAudioSession()
    }
    
    // MARK: - 公共方法
    
    /// 播放单个音频
    /// - Parameters:
    ///   - description: 音频描述
    ///   - audioUrl: 音频URL
    public func playSingleAudio(description: String, audioUrl: String) async throws {
        NSLog("🎵 [GlobalAudioManager] 播放单个音频: \(description)")

        // 增强的URL验证
        guard !audioUrl.isEmpty else {
            NSLog("❌ [GlobalAudioManager] 音频URL为空")
            throw AudioError.invalidURL
        }

        guard let url = URL(string: audioUrl) else {
            NSLog("❌ [GlobalAudioManager] 无效的URL格式: \(audioUrl)")
            throw AudioError.invalidURL
        }

        // 验证URL协议和域名
        guard url.scheme == "https" else {
            NSLog("❌ [GlobalAudioManager] 音频URL必须使用HTTPS协议: \(audioUrl)")
            throw AudioError.invalidURL
        }

        guard url.host == "audio.senseword.app" else {
            NSLog("❌ [GlobalAudioManager] 音频URL必须来自audio.senseword.app域名: \(audioUrl)")
            throw AudioError.invalidURL
        }

        // 只停止音频播放器，不清理序列状态
        stopAudioPlayer()

        // 设置状态
        currentAudioDescription = description
        state = .loading
        progress = 0.0
        
        do {
            // 下载或获取缓存的音频数据
            let audioData = try await getAudioData(from: url)
            
            // 创建并播放音频
            audioPlayer = try AVAudioPlayer(data: audioData)
            audioPlayer?.delegate = self
            audioPlayer?.prepareToPlay()
            
            if audioPlayer?.play() == true {
                state = .playing
                startProgressTimer()
                NSLog("✅ [GlobalAudioManager] 音频播放成功")
            } else {
                throw AudioError.playbackFailed
            }
            
        } catch {
            state = .error(error.localizedDescription)
            NSLog("❌ [GlobalAudioManager] 音频播放失败: \(error)")
            throw error
        }
    }
    
    /// 播放音标序列
    /// - Parameters:
    ///   - phoneticSymbols: 音标数组
    ///   - autoPlay: 是否自动播放
    ///   - onCompleted: 完成回调
    func playPhoneticSequence(
        _ phoneticSymbols: [PhoneticSymbol],
        autoPlay: Bool = true,
        onCompleted: (() -> Void)? = nil
    ) async {
        NSLog("🎵 [GlobalAudioManager] 播放音标序列，共 \(phoneticSymbols.count) 个音标")

        // 停止当前播放（包括清理旧序列）
        stopCurrentPlayback()
        
        // 过滤有效的音标
        let validPhonetics = phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                NSLog("⚠️ [GlobalAudioManager] 跳过无效音频URL: \(phonetic.symbol)")
                return false
            }
            return true
        }
        
        guard !validPhonetics.isEmpty else {
            NSLog("❌ [GlobalAudioManager] 没有有效的音频URL")
            state = .error("没有有效的音频")
            return
        }
        
        // 设置序列播放
        sequenceQueue = validPhonetics
        currentSequenceIndex = 0
        sequenceCompletionHandler = onCompleted
        
        if autoPlay {
            await playNextInSequence()
        } else {
            state = .idle
        }
    }
    
    /// 开始序列播放（用于手动触发）
    func startSequencePlayback() async {
        guard !sequenceQueue.isEmpty else {
            NSLog("⚠️ [GlobalAudioManager] 序列为空，无法播放")
            return
        }

        currentSequenceIndex = 0
        progress = 0.0
        await playNextInSequence()
    }

    /// 播放单个音标音频（便捷方法）
    /// - Parameter phonetic: 音标符号
    func playPhonetic(_ phonetic: PhoneticSymbol) async throws {
        guard let audioUrl = phonetic.audioUrl else {
            throw AudioError.invalidURL
        }
        try await playSingleAudio(description: "音标: \(phonetic.symbol)", audioUrl: audioUrl)
    }

    /// 预加载音频数据到缓存
    /// - Parameter url: 音频URL
    /// - Returns: 音频数据
    public func preloadAudioData(from url: URL) async throws -> Data {
        NSLog("🔄 [GlobalAudioManager] 预加载音频数据: \(url.absoluteString)")

        do {
            let audioData = try await getAudioData(from: url)
            NSLog("✅ [GlobalAudioManager] 音频预加载成功: \(url.absoluteString)")
            return audioData
        } catch {
            NSLog("❌ [GlobalAudioManager] 音频预加载失败: \(url.absoluteString) - \(error)")
            throw error
        }
    }
    
    /// 停止音频播放器（不清理序列状态）
    private func stopAudioPlayer() {
        audioPlayer?.stop()
        audioPlayer = nil
        stopProgressTimer()
        NSLog("🛑 [GlobalAudioManager] 音频播放器已停止")
    }

    /// 停止当前播放（包括清理序列状态）
    func stopCurrentPlayback() {
        stopAudioPlayer()

        state = .idle
        progress = 0.0
        currentAudioDescription = ""

        // 清理序列播放状态
        sequenceQueue.removeAll()
        currentSequenceIndex = 0
        sequenceCompletionHandler = nil

        NSLog("🛑 [GlobalAudioManager] 播放已完全停止")
    }
    
    /// 暂停播放
    func pausePlayback() {
        guard state == .playing else { return }
        
        audioPlayer?.pause()
        state = .paused
        stopProgressTimer()
        NSLog("⏸️ [GlobalAudioManager] 播放已暂停")
    }
    
    /// 恢复播放
    func resumePlayback() {
        guard state == .paused, let player = audioPlayer else { return }
        
        if player.play() {
            state = .playing
            startProgressTimer()
            NSLog("▶️ [GlobalAudioManager] 播放已恢复")
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置音频会话
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            NSLog("❌ [GlobalAudioManager] 音频会话设置失败: \(error)")
        }
    }
    
    /// 获取音频数据（支持缓存）
    internal func getAudioData(from url: URL) async throws -> Data {
        let urlString = url.absoluteString
        
        // 检查缓存
        if let cachedData = audioCache[urlString] {
            NSLog("📦 [GlobalAudioManager] 使用缓存音频: \(urlString)")
            return cachedData
        }
        
        // 下载音频
        NSLog("⬇️ [GlobalAudioManager] 下载音频: \(urlString)")
        let (data, _) = try await URLSession.shared.data(from: url)
        
        // 缓存音频数据
        audioCache[urlString] = data
        
        return data
    }
    
    /// 播放序列中的下一个音频
    private func playNextInSequence() async {
        guard currentSequenceIndex < sequenceQueue.count else {
            // 序列播放完成
            state = .completed
            progress = 1.0
            NSLog("🎉 [GlobalAudioManager] 序列播放完成")
            sequenceCompletionHandler?()
            return
        }
        
        let phonetic = sequenceQueue[currentSequenceIndex]
        currentAudioDescription = "音标序列: \(phonetic.symbol)"
        
        guard let audioUrl = phonetic.audioUrl else {
            NSLog("❌ [GlobalAudioManager] 音标音频URL无效: \(phonetic.symbol)")
            currentSequenceIndex += 1
            await playNextInSequence()
            return
        }
        
        do {
            try await playSingleAudio(description: "音标序列: \(phonetic.symbol)", audioUrl: audioUrl)
        } catch {
            NSLog("❌ [GlobalAudioManager] 序列播放失败: \(phonetic.symbol) - \(error)")
            currentSequenceIndex += 1
            await playNextInSequence()
        }
    }
    
    /// 开始进度定时器
    private func startProgressTimer() {
        stopProgressTimer()
        progressTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updateProgress()
            }
        }
    }
    
    /// 停止进度定时器
    private func stopProgressTimer() {
        progressTimer?.invalidate()
        progressTimer = nil
    }
    
    /// 更新播放进度
    private func updateProgress() {
        guard let player = audioPlayer, player.isPlaying else { return }
        
        if !sequenceQueue.isEmpty {
            // 序列播放进度计算
            let sequenceProgress = Double(currentSequenceIndex) / Double(sequenceQueue.count)
            let currentAudioProgress = player.currentTime / player.duration / Double(sequenceQueue.count)
            progress = sequenceProgress + currentAudioProgress
        } else {
            // 单个音频进度
            progress = player.currentTime / player.duration
        }
    }
}

// MARK: - AVAudioPlayerDelegate
extension GlobalAudioManager: AVAudioPlayerDelegate {

    nonisolated public func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        NSLog("🎉 [GlobalAudioManager] 音频播放完成 - 成功: \(flag)")

        Task { @MainActor in
            if !sequenceQueue.isEmpty {
                // 序列播放：播放下一个
                currentSequenceIndex += 1
                await playNextInSequence()
            } else {
                // 单个音频播放完成
                state = .completed
                progress = 1.0
                stopProgressTimer()
            }
        }
    }

    nonisolated public func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        NSLog("❌ [GlobalAudioManager] 音频解码错误: \(error?.localizedDescription ?? "未知错误")")

        Task { @MainActor in
            state = .error(error?.localizedDescription ?? "音频解码错误")
            stopProgressTimer()

            if !sequenceQueue.isEmpty {
                // 序列播放：跳过错误的音频，继续下一个
                currentSequenceIndex += 1
                await playNextInSequence()
            }
        }
    }
}

// MARK: - 错误定义
public enum AudioError: Error, LocalizedError {
    case invalidURL
    case playbackFailed
    case downloadFailed

    public var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的音频URL"
        case .playbackFailed:
            return "音频播放失败"
        case .downloadFailed:
            return "音频下载失败"
        }
    }
}
