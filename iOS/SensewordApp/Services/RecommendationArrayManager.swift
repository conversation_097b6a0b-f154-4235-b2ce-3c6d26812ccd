//
//  RecommendationArrayManager.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  推荐数组管理器 - 无限内容流核心组件
//

import Foundation
import Combine

/**
 * @description 推荐数组管理器
 * 
 * 核心职责：
 * - 管理推荐单词数组的生命周期
 * - 提供统一接口给各业务逻辑添加推荐
 * - 控制当前索引和导航逻辑
 * - 支持三种推荐流模式（每日一词、搜索词、生词本）
 * 
 * 设计原则：
 * - 完全解耦：独立于UI和具体业务逻辑
 * - 业务灵活：任何业务逻辑都可添加推荐内容
 * - 性能优化：智能扩展和缓存管理
 */
@MainActor
class RecommendationArrayManager: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前推荐数组状态
    @Published private(set) var state: RecommendationArrayState
    
    /// 推荐配置
    @Published var config: RecommendationConfig
    
    /// 统计信息
    @Published private(set) var stats: RecommendationStats
    
    // MARK: - Private Properties
    
    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    /// API服务（注入依赖）
    private let apiService: APIServiceProtocol
    
    /// 用户偏好服务
    private let userPreferencesService: UserPreferencesServiceProtocol
    
    // MARK: - Initialization
    
    init(
        apiService: APIServiceProtocol? = nil,
        userPreferencesService: UserPreferencesServiceProtocol? = nil,
        config: RecommendationConfig = .default
    ) {
        // 使用真实的API服务而不是Mock
        if let apiService = apiService {
            self.apiService = apiService
        } else {
            // 创建真实的API服务适配器
            let wordAPIAdapter = DIContainer.shared.wordAPIAdapter
            self.apiService = RealAPIService(wordAPIAdapter: wordAPIAdapter)
        }

        self.userPreferencesService = userPreferencesService ?? MockUserPreferencesService()
        self.config = config
        self.state = RecommendationArrayState()
        self.stats = RecommendationStats()

        setupObservers()
    }
    
    // MARK: - Public Interface
    

    
    /**
     * 添加搜索词推荐流
     * 构建：搜索词 + 二层级联概念（最多30个单词）
     */
    func addSearchBasedRecommendations(from searchWord: String, wordData: WordDefinitionResponse? = nil) async throws {
        updateLoadingState(true)

        do {
            // 1. 构建二层级联推荐数组（优化：使用已有数据避免重复API调用）
            let recommendations = try await buildDoubleLayerCascade(
                from: searchWord,
                source: .searchWord,
                preloadedWordData: wordData
            )

            // 2. 更新状态
            updateState(
                mode: .search,
                items: recommendations,
                currentIndex: 0
            )

            print("[RecommendationArrayManager] 搜索词推荐流构建完成: \(recommendations.count)个单词")

            // 打印推荐数组状态
            printCurrentArrayState()

        } catch {
            print("[RecommendationArrayManager] 搜索词推荐流构建失败: \(error)")
            updateLoadingState(false)
            throw error
        }

        updateLoadingState(false)
    }
    
    /**
     * 添加生词本推荐流
     * 构建：生词A + 级联 + 生词B + 级联的混合模式
     */
    func addBookmarkBasedRecommendations() async throws {
        updateLoadingState(true)
        
        do {
            // 1. 获取用户收藏的生词列表
            let bookmarkedWords = try await fetchBookmarkedWords()
            
            var recommendations: [RecommendationItem] = []
            
            // 2. 为每个生词构建：生词 + 其级联概念
            for bookmarkWord in bookmarkedWords.prefix(config.minArraySize / 2) {
                // 添加生词
                recommendations.append(
                    RecommendationItem(
                        word: bookmarkWord,
                        source: .bookmarkWord,
                        priority: RecommendationSource.bookmarkWord.priorityWeight
                    )
                )
                
                // 添加该生词的级联概念
                if config.enableSingleLayerCascade {
                    let relatedConcepts = try await fetchRelatedConcepts(for: bookmarkWord)
                    let conceptItems = relatedConcepts.prefix(2).map { concept in
                        RecommendationItem(
                            word: concept,
                            source: .relatedConcept,
                            priority: RecommendationSource.relatedConcept.priorityWeight
                        )
                    }
                    recommendations.append(contentsOf: conceptItems)
                }
            }
            
            // 3. 更新状态
            updateState(
                mode: .bookmark,
                items: recommendations,
                currentIndex: 0
            )
            
            print("[RecommendationArrayManager] 生词本推荐流构建完成: \(recommendations.count)个单词")

        } catch {
            print("[RecommendationArrayManager] 生词本推荐流构建失败: \(error)")
            updateLoadingState(false)
            throw error
        }

        updateLoadingState(false)
    }
    
    /**
     * 添加关联概念到当前数组
     * 用于动态扩展推荐数组
     */
    func addRelatedConcepts(from word: String) async throws {
        guard config.enableSingleLayerCascade else { return }
        
        do {
            let relatedConcepts = try await fetchRelatedConcepts(for: word)
            let conceptItems = relatedConcepts.map { concept in
                RecommendationItem(
                    word: concept,
                    source: .relatedConcept,
                    priority: RecommendationSource.relatedConcept.priorityWeight
                )
            }
            
            // 添加到当前数组末尾
            let updatedItems = state.items + conceptItems
            updateState(
                mode: state.mode,
                items: updatedItems,
                currentIndex: state.currentIndex
            )
            
            print("[RecommendationArrayManager] 添加关联概念: \(conceptItems.count)个")
            
        } catch {
            print("[RecommendationArrayManager] 添加关联概念失败: \(error)")
            throw error
        }
    }
    
    // MARK: - Navigation Control
    
    /**
     * 移动到下一个推荐项
     */
    func moveToNext() -> RecommendationItem? {
        print("[RecommendationArrayManager] 尝试移动到下一个推荐项")

        guard state.hasNext else {
            print("[RecommendationArrayManager] 没有下一个推荐项，当前索引: \(state.currentIndex), 总数: \(state.items.count)")
            // 如果接近数组末尾，尝试自动扩展
            if state.remainingCount <= config.autoExtendThreshold {
                print("[RecommendationArrayManager] 触发自动扩展，剩余数量: \(state.remainingCount)")
                Task {
                    await autoExtendArray()
                }
            }
            return nil
        }

        let oldIndex = state.currentIndex
        let newIndex = state.currentIndex + 1
        updateState(
            mode: state.mode,
            items: state.items,
            currentIndex: newIndex
        )

        let currentItem = state.currentItem
        print("[RecommendationArrayManager] 成功移动: [\(oldIndex)] -> [\(newIndex)] \(currentItem?.word ?? "nil")")

        // 懒加载二层推荐：当消费一层推荐词时，动态追加其二层推荐
        if config.enableDoubleLayerCascade {
            Task {
                await expandSecondLayerRecommendations(for: newIndex)
            }
        }

        return currentItem
    }
    
    /**
     * 移动到上一个推荐项
     */
    func moveToPrevious() -> RecommendationItem? {
        guard state.hasPrevious else { return nil }
        
        let newIndex = state.currentIndex - 1
        updateState(
            mode: state.mode,
            items: state.items,
            currentIndex: newIndex
        )
        
        return state.currentItem
    }
    
    /**
     * 获取当前推荐项
     */
    func getCurrentItem() -> RecommendationItem? {
        return state.currentItem
    }
    
    /**
     * 预览下一个推荐项（不移动索引）
     */
    func previewNext() -> RecommendationItem? {
        return state.nextItem
    }
    
    /**
     * 重置推荐数组
     */
    func reset() {
        updateState(
            mode: .daily,
            items: [],
            currentIndex: 0
        )

        print("[RecommendationArrayManager] 推荐数组已重置")
    }

    /**
     * 打印当前推荐数组状态（调试用）
     */
    func printCurrentArrayState() {
        print("=== 推荐数组状态 ===")
        print("模式: \(state.mode.displayName)")
        print("当前索引: \(state.currentIndex)")
        print("总数量: \(state.items.count)")
        print("是否加载中: \(state.isLoading)")
        print("剩余数量: \(state.remainingCount)")
        print("进度: \(String(format: "%.1f", state.progress * 100))%")

        if state.items.isEmpty {
            print("推荐数组为空")
        } else {
            print("\n推荐列表:")
            for (index, item) in state.items.enumerated() {
                let marker = index == state.currentIndex ? "👉" : "  "
                let sourceIcon = getSourceIcon(item.source)
                print("\(marker) [\(index)] \(sourceIcon) \(item.word) (\(item.source.displayName))")
            }
        }

        if let currentItem = state.currentItem {
            print("\n当前项: \(currentItem.word)")
        }

        if let nextItem = state.nextItem {
            print("下一项: \(nextItem.word)")
        } else {
            print("下一项: 无")
        }

        print("==================")
    }

    /**
     * 获取来源图标
     */
    private func getSourceIcon(_ source: RecommendationSource) -> String {
        switch source {
        case .dailyWord:
            return "📅"
        case .relatedConcept:
            return "🔗"
        case .searchWord:
            return "🔍"
        case .bookmarkWord:
            return "📖"
        case .userHistory:
            return "📚"
        }
    }

    // MARK: - Double Layer Cascade Implementation

    /**
     * 懒加载扩展二层推荐
     * 当用户消费到一层推荐词时，动态获取其二层推荐并追加
     */
    private func expandSecondLayerRecommendations(for currentIndex: Int) async {
        // 只对一层推荐词进行扩展（跳过主词和已扩展的二层词）
        guard currentIndex > 0 && currentIndex < state.items.count else { return }

        let currentItem = state.items[currentIndex]

        // 检查是否已经为这个词扩展过二层推荐
        if hasExpandedSecondLayer(for: currentItem.word) {
            return
        }

        print("[RecommendationArrayManager] 开始懒加载二层推荐: \(currentItem.word)")

        do {
            // 获取当前词的二层推荐
            let secondLayerWords = try await fetchAllRecommendationWords(for: currentItem.word)

            // 去重处理：排除已存在的词
            let existingWords = Set(state.items.map { $0.word.lowercased() })
            let newWords = secondLayerWords.filter { !existingWords.contains($0.lowercased()) }

            // 限制追加数量，避免数组过长
            let wordsToAdd = Array(newWords.prefix(5))

            if !wordsToAdd.isEmpty {
                // 创建二层推荐项
                let secondLayerItems = wordsToAdd.map { word in
                    RecommendationItem(
                        word: word,
                        source: .relatedConcept,
                        priority: RecommendationSource.relatedConcept.priorityWeight - 10 // 二层推荐优先级稍低
                    )
                }

                // 追加到推荐数组末尾
                let updatedItems = state.items + secondLayerItems

                // 更新状态
                updateState(
                    mode: state.mode,
                    items: updatedItems,
                    currentIndex: state.currentIndex
                )

                // 记录已扩展的词
                markAsExpandedSecondLayer(word: currentItem.word)

                print("[RecommendationArrayManager] 懒加载二层推荐完成: \(currentItem.word) → 追加 \(wordsToAdd.count) 个词，总计 \(updatedItems.count) 个")
            }

        } catch {
            print("[RecommendationArrayManager] 懒加载二层推荐失败: \(currentItem.word) - \(error)")
        }
    }

    /// 已扩展二层推荐的词集合
    private var expandedSecondLayerWords: Set<String> = []

    /// 检查是否已经为某个词扩展过二层推荐
    private func hasExpandedSecondLayer(for word: String) -> Bool {
        return expandedSecondLayerWords.contains(word.lowercased())
    }

    /// 标记某个词已扩展二层推荐
    private func markAsExpandedSecondLayer(word: String) {
        expandedSecondLayerWords.insert(word.lowercased())
    }

    /**
     * 构建懒加载二层级联推荐数组
     * 算法：主词 + 一层推荐（懒加载，消费时动态追加二层推荐）
     */
    private func buildDoubleLayerCascade(
        from mainWord: String,
        source: RecommendationSource,
        preloadedWordData: WordDefinitionResponse? = nil
    ) async throws -> [RecommendationItem] {
        print("[RecommendationArrayManager] 开始构建懒加载二层级联: \(mainWord)")

        var recommendations: [RecommendationItem] = []

        // 1. 添加主词
        recommendations.append(
            RecommendationItem(
                word: mainWord,
                source: source,
                priority: source.priorityWeight
            )
        )

        // 2. 只构建一层推荐（遵循懒加载原则）
        if config.enableDoubleLayerCascade {
            let firstLayerWords: [String]

            // 优化：使用预加载数据避免重复API调用
            if let wordData = preloadedWordData {
                firstLayerWords = extractRecommendationWords(from: wordData)
                print("[RecommendationArrayManager] 使用预加载数据，避免重复API调用: \(mainWord)")
            } else {
                firstLayerWords = try await fetchAllRecommendationWords(for: mainWord)
                print("[RecommendationArrayManager] 发起API调用获取推荐词: \(mainWord)")
            }

            for word in firstLayerWords {
                recommendations.append(
                    RecommendationItem(
                        word: word,
                        source: .relatedConcept,
                        priority: RecommendationSource.relatedConcept.priorityWeight
                    )
                )
            }
        }

        print("[RecommendationArrayManager] 懒加载二层级联初始化完成: \(recommendations.count)个单词（一层推荐）")
        return recommendations
    }

    // MARK: - Private Methods

    /**
     * 设置观察者
     */
    private func setupObservers() {
        // 监听配置变化
        $config
            .dropFirst()
            .sink { [weak self] newConfig in
                self?.handleConfigurationChange(newConfig)
            }
            .store(in: &cancellables)
    }

    /**
     * 更新状态
     */
    private func updateState(
        mode: RecommendationMode,
        items: [RecommendationItem],
        currentIndex: Int
    ) {
        state = RecommendationArrayState(
            mode: mode,
            items: items,
            currentIndex: currentIndex,
            isLoading: state.isLoading,
            lastUpdated: Date()
        )
    }

    /**
     * 更新加载状态
     */
    private func updateLoadingState(_ isLoading: Bool) {
        state = RecommendationArrayState(
            mode: state.mode,
            items: state.items,
            currentIndex: state.currentIndex,
            isLoading: isLoading,
            lastUpdated: state.lastUpdated
        )
    }

    /**
     * 自动扩展数组
     */
    private func autoExtendArray() async {
        guard let currentItem = state.currentItem else { return }

        do {
            // 基于当前单词添加关联概念
            try await addRelatedConcepts(from: currentItem.word)
        } catch {
            print("[RecommendationArrayManager] 自动扩展失败: \(error)")
        }
    }

    /**
     * 处理配置变化
     */
    private func handleConfigurationChange(_ newConfig: RecommendationConfig) {
        print("[RecommendationArrayManager] 配置已更新")

        // 如果数组大小超过新的最大值，进行裁剪
        if state.items.count > newConfig.maxArraySize {
            let trimmedItems = Array(state.items.prefix(newConfig.maxArraySize))
            updateState(
                mode: state.mode,
                items: trimmedItems,
                currentIndex: min(state.currentIndex, trimmedItems.count - 1)
            )
        }
    }

    // MARK: - API Methods



    /**
     * 从 WordDefinitionResponse 中提取推荐词汇（relatedConcepts + synonyms）
     */
    private func extractRecommendationWords(from wordData: WordDefinitionResponse) -> [String] {
        var allWords: [String] = []

        // 1. 添加相关概念
        allWords.append(contentsOf: wordData.metadata.relatedConcepts)

        // 2. 添加同义词
        let synonymWords = wordData.content.synonyms.map { $0.word }
        allWords.append(contentsOf: synonymWords)

        print("[RecommendationArrayManager] 提取推荐词汇: \(wordData.word) → relatedConcepts: \(wordData.metadata.relatedConcepts.count), synonyms: \(synonymWords.count), 总计: \(allWords.count)")

        return allWords
    }

    /**
     * 获取单词的所有推荐词汇（relatedConcepts + synonyms）
     */
    private func fetchAllRecommendationWords(for word: String) async throws -> [String] {
        do {
            // 获取单词的完整定义
            let wordDefinition = try await apiService.fetchWordDefinition(word: word)

            // 使用统一的提取方法
            return extractRecommendationWords(from: wordDefinition)
        } catch {
            print("[RecommendationArrayManager] 获取推荐词汇失败: \(word) - \(error)")
            throw error
        }
    }

    /**
     * 获取关联概念
     * 从WordDefinitionResponse中提取relatedConcepts和synonyms
     */
    private func fetchRelatedConcepts(for word: String) async throws -> [String] {
        do {
            // 获取单词的完整定义
            let wordDefinition = try await apiService.fetchWordDefinition(word: word)

            var concepts: [String] = []

            // 1. 添加relatedConcepts
            concepts.append(contentsOf: wordDefinition.metadata.relatedConcepts)

            // 2. 添加synonyms中的单词
            let synonymWords = wordDefinition.content.synonyms.map { $0.word }
            concepts.append(contentsOf: synonymWords)

            // 3. 去重并限制数量（避免推荐数组过大）
            let uniqueConcepts = Array(Set(concepts)).prefix(5)

            print("[RecommendationArrayManager] 为单词'\(word)'提取到\(uniqueConcepts.count)个关联概念")

            return Array(uniqueConcepts)

        } catch {
            print("[RecommendationArrayManager] 获取关联概念失败，使用备用数据: \(error)")

            // 备用数据，确保系统稳定运行
            switch word {
            case "serendipity":
                return ["chance", "discovery", "fortune", "unexpected"]
            case "progressive":
                return ["reform", "modernization", "liberalism", "dynamic"]
            case "ephemeral":
                return ["transient", "fleeting", "momentary", "temporary"]
            default:
                return ["concept1", "concept2", "concept3"]
            }
        }
    }

    /**
     * 获取收藏的生词
     */
    private func fetchBookmarkedWords() async throws -> [String] {
        do {
            // 使用注入的API服务获取真实的收藏单词
            let bookmarkedWords = try await apiService.fetchBookmarkedWords()
            print("[RecommendationArrayManager] 获取收藏单词成功: \(bookmarkedWords.count)个")
            return bookmarkedWords
        } catch {
            print("[RecommendationArrayManager] 获取收藏单词失败，使用备用数据: \(error)")
            // 备用数据，确保系统稳定运行
            return ["progressive", "ephemeral", "serendipity", "ubiquitous", "paradigm"]
        }
    }
}

// MARK: - Protocol Definitions

/**
 * API服务协议
 */
protocol APIServiceProtocol {
    func fetchWordDefinition(word: String) async throws -> WordDefinitionResponse
    func fetchRelatedConcepts(for word: String) async throws -> [String]
    func fetchBookmarkedWords() async throws -> [String]
}

/**
 * 用户偏好服务协议
 */
protocol UserPreferencesServiceProtocol {
    var preferredLanguage: String { get }
    var recommendationConfig: RecommendationConfig { get set }
    func saveRecommendationStats(_ stats: RecommendationStats)
    func loadRecommendationStats() -> RecommendationStats?
}

// MARK: - Extensions

extension RecommendationArrayManager {

    /**
     * 获取推荐统计信息
     */
    func getRecommendationStats() -> RecommendationStats {
        let sourceDistribution = Dictionary(
            grouping: state.items,
            by: { $0.source }
        ).mapValues { $0.count }

        return RecommendationStats(
            totalRecommendations: state.items.count,
            sourceDistribution: sourceDistribution,
            averageSessionLength: 0.0, // TODO: 实现会话长度统计
            userEngagementRate: 0.0,   // TODO: 实现用户参与度统计
            lastSessionDate: Date()
        )
    }

    /**
     * 导出推荐数组为调试信息
     */
    func exportDebugInfo() -> [String: Any] {
        return [
            "mode": state.mode.rawValue,
            "itemCount": state.items.count,
            "currentIndex": state.currentIndex,
            "hasNext": state.hasNext,
            "remainingCount": state.remainingCount,
            "progress": state.progress,
            "isLoading": state.isLoading,
            "lastUpdated": state.lastUpdated.timeIntervalSince1970,
            "items": state.items.map { item in
                [
                    "word": item.word,
                    "source": item.source.rawValue,
                    "priority": item.priority,
                    "timestamp": item.timestamp.timeIntervalSince1970
                ]
            }
        ]
    }
}

// MARK: - Real API Service Implementation

/**
 * 真实API服务实现
 * 将WordAPIAdapter适配为APIServiceProtocol
 */
class RealAPIService: APIServiceProtocol {
    private let wordAPIAdapter: WordAPIAdapterProtocol

    init(wordAPIAdapter: WordAPIAdapterProtocol) {
        self.wordAPIAdapter = wordAPIAdapter
    }



    func fetchWordDefinition(word: String) async throws -> WordDefinitionResponse {
        // 使用便捷方法，自动获取默认语言对配置
        return try await wordAPIAdapter.getWord(word)
    }

    func fetchRelatedConcepts(for word: String) async throws -> [String] {
        // 这个方法不应该被调用，因为RecommendationArrayManager有自己的fetchRelatedConcepts实现
        throw APIError.notImplemented
    }

    func fetchBookmarkedWords() async throws -> [String] {
        // TODO: 实现真实的书签API调用
        // 暂时返回空数组，避免使用mock数据
        return []
    }
}

// MARK: - Mock Implementations

/**
 * Mock API服务实现
 */
class MockAPIService: APIServiceProtocol {


    func fetchWordDefinition(word: String) async throws -> WordDefinitionResponse {
        // 返回模拟的WordDefinitionResponse
        let metadata = WordMetadata(
            wordFrequency: "common",
            relatedConcepts: getRelatedConceptsForWord(word)
        )

        let synonyms = getSynonymsForWord(word)

        let content = WordContent(
            difficulty: "intermediate",
            phoneticSymbols: [],
            coreDefinition: "Mock definition for \(word)",
            contextualExplanation: ContextualExplanation(
                nativeSpeakerIntent: "Mock intent",
                emotionalResonance: "Mock resonance",
                vividImagery: "Mock imagery",
                etymologicalEssence: "Mock etymology"
            ),
            usageExamples: [],
            usageScenarios: [],
            collocations: [],
            usageNotes: [],
            synonyms: synonyms
        )

        return WordDefinitionResponse(
            word: word,
            metadata: metadata,
            content: content,
            learningLanguage: "en",
            scaffoldingLanguage: "zh",
            syncId: 1,
            partsOfSpeech: "noun",
            culturalRiskRegions: []
        )
    }

    private func getRelatedConceptsForWord(_ word: String) -> [String] {
        switch word {
        case "serendipity":
            return ["chance", "discovery", "fortune"]
        case "progressive":
            return ["reform", "modernization", "liberalism"]
        case "ephemeral":
            return ["transient", "fleeting", "momentary"]
        default:
            return ["concept1", "concept2"]
        }
    }

    private func getSynonymsForWord(_ word: String) -> [Synonym] {
        switch word {
        case "serendipity":
            return [
                Synonym(word: "luck", explanation: "Good fortune", examples: []),
                Synonym(word: "coincidence", explanation: "Chance occurrence", examples: [])
            ]
        case "progressive":
            return [
                Synonym(word: "advanced", explanation: "Forward-thinking", examples: []),
                Synonym(word: "modern", explanation: "Contemporary", examples: [])
            ]
        default:
            return [
                Synonym(word: "synonym1", explanation: "Mock synonym", examples: [])
            ]
        }
    }

    func fetchRelatedConcepts(for word: String) async throws -> [String] {
        // 这个方法不应该在mock中实现，应该让RecommendationArrayManager使用真实的数据提取逻辑
        // 直接抛出错误，让调用者使用真实的fetchRelatedConcepts方法
        throw APIError.notImplemented
    }

    func fetchBookmarkedWords() async throws -> [String] {
        return ["progressive", "ephemeral", "serendipity", "ubiquitous", "paradigm"]
    }
}

/**
 * Mock 用户偏好服务实现
 */
class MockUserPreferencesService: UserPreferencesServiceProtocol {
    var preferredLanguage: String = "en"
    var recommendationConfig: RecommendationConfig = .default

    func saveRecommendationStats(_ stats: RecommendationStats) {
        // Mock implementation
        print("[MockUserPreferencesService] 保存推荐统计: \(stats.totalRecommendations)个推荐")
    }

    func loadRecommendationStats() -> RecommendationStats? {
        return RecommendationStats()
    }
}
