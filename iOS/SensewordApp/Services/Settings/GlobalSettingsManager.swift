//
//  GlobalSettingsManager.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  全局设置管理器 - 提供应用级别的设置访问
//

import Foundation
import Combine

/// 全局设置管理器
class GlobalSettingsManager: ObservableObject {
    
    // MARK: - 单例
    
    static let shared = GlobalSettingsManager()
    
    // MARK: - 属性
    
    /// 设置服务
    private let settingsService: SettingsService
    
    /// 当前用户设置
    @Published var userSettings: UserSettings
    
    /// 设置变化订阅
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    
    private init() {
        self.settingsService = SettingsService()
        self.userSettings = settingsService.currentSettings
        
        // 监听设置变化
        settingsService.settingsPublisher
            .receive(on: DispatchQueue.main)
            .assign(to: \.userSettings, on: self)
            .store(in: &cancellables)
    }
    
    // MARK: - 音标偏好相关
    
    /// 获取当前音标偏好
    var currentPhoneticPreference: PhoneticPreference {
        return userSettings.phoneticPreference
    }
    
    /// 更新音标偏好
    func updatePhoneticPreference(_ preference: PhoneticPreference) {
        settingsService.updatePhoneticPreference(preference)
    }
    
    /// 根据用户偏好获取首选音标
    func getPreferredPhonetic(from phonetics: [PhoneticSymbol]) -> PhoneticSymbol? {
        switch currentPhoneticPreference {
        case .american:
            return phonetics.americanPhonetic ?? phonetics.britishPhonetic ?? phonetics.first
        case .british:
            return phonetics.britishPhonetic ?? phonetics.americanPhonetic ?? phonetics.first
        }
    }
    
    // MARK: - 其他设置相关
    
    /// 获取自动播放音频设置
    var autoPlayAudio: Bool {
        return userSettings.autoPlayAudio
    }
    
    /// 获取触感反馈设置
    var hapticFeedback: Bool {
        return userSettings.hapticFeedback
    }
    
    /// 获取每日提醒设置
    var dailyNotification: Bool {
        return userSettings.dailyNotification
    }
}

// MARK: - PhoneticSymbol 数组扩展已在 SharedModels.swift 中定义
