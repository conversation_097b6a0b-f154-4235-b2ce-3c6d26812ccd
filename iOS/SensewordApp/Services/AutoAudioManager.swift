//
//  AutoAudioManager.swift
//  SensewordApp
//
//  Created by AI Assistant on 2025-06-28.
//  自动音频预加载和播放管理器
//

import Foundation
import AVFoundation
import UIComponents

/// 自动音频管理器
/// 负责音频的自动预加载、缓存和顺序播放
@MainActor
class AutoAudioManager: NSObject, ObservableObject {
    
    /// 播放状态
    enum PlaybackState {
        case idle           // 空闲状态
        case preloading     // 预加载中
        case ready          // 准备就绪
        case playing        // 正在播放
        case completed      // 播放完成（但可以重新播放）
        case error          // 错误状态
    }
    
    /// 当前播放状态
    @Published var state: PlaybackState = .idle
    
    /// 当前播放进度（0.0 - 1.0）
    @Published var progress: Double = 0.0
    
    /// 音频播放器
    private var audioPlayer: AVAudioPlayer?
    
    /// 音频缓存
    private var audioCache: [String: Data] = [:]
    
    /// 播放队列
    private var playbackQueue: [PhoneticSymbol] = []
    
    /// 当前播放索引
    private var currentPlayIndex = 0
    
    /// 播放完成回调
    private var onPlaybackCompleted: (() -> Void)?
    
    /// 初始化
    override init() {
        super.init()
        setupAudioSession()
    }
    
    // MARK: - 公共方法
    
    /// 自动预加载并播放音标音频
    /// - Parameters:
    ///   - phoneticSymbols: 音标数组
    ///   - autoPlay: 是否自动播放（默认true）
    ///   - onCompleted: 播放完成回调
    func preloadAndPlay(
        phoneticSymbols: [PhoneticSymbol],
        autoPlay: Bool = true,
        onCompleted: (() -> Void)? = nil
    ) {
        NSLog("🎵 [AutoAudioManager] 开始预加载音频，共 \(phoneticSymbols.count) 个音标")

        // 停止其他音频播放器，避免冲突
        stopOtherAudioPlayers()

        // 重置状态
        reset()

        // 设置回调
        self.onPlaybackCompleted = onCompleted
        
        // 过滤有效的音频URL
        let validPhonetics = phoneticSymbols.filter { phonetic in
            guard let audioUrl = phonetic.audioUrl, !audioUrl.isEmpty else {
                NSLog("⚠️ [AutoAudioManager] 跳过无效音频URL: \(phonetic.symbol)")
                return false
            }
            return true
        }
        
        guard !validPhonetics.isEmpty else {
            NSLog("❌ [AutoAudioManager] 没有有效的音频URL")
            state = .error
            return
        }
        
        // 设置播放队列
        playbackQueue = validPhonetics
        state = .preloading
        
        // 开始预加载
        Task {
            await preloadAllAudio()
            
            if autoPlay {
                await startSequentialPlayback()
            } else {
                await MainActor.run {
                    state = .ready
                }
            }
        }
    }
    
    /// 手动开始播放
    func startPlayback() {
        guard state == .ready || state == .completed else {
            NSLog("⚠️ [AutoAudioManager] 播放状态不正确: \(state)")
            return
        }

        // 停止其他音频播放器，避免冲突
        stopOtherAudioPlayers()

        // 如果是completed状态，重置播放索引
        if state == .completed {
            currentPlayIndex = 0
            progress = 0.0
        }

        Task {
            await startSequentialPlayback()
        }
    }
    
    /// 停止播放
    func stopPlayback() {
        audioPlayer?.stop()
        audioPlayer = nil
        currentPlayIndex = 0
        progress = 0.0
        state = .idle
        NSLog("🛑 [AutoAudioManager] 播放已停止")
    }
    
    /// 重置管理器
    func reset() {
        stopPlayback()
        audioCache.removeAll()
        playbackQueue.removeAll()
        currentPlayIndex = 0
        progress = 0.0
        state = .idle
        onPlaybackCompleted = nil
    }
    
    // MARK: - 私有方法

    /// 停止其他音频播放器，避免冲突
    private func stopOtherAudioPlayers() {
        // 停止 AudioPlayerService
        Task { @MainActor in
            AudioPlayerService.shared.stopCurrentAudio()
        }
        NSLog("🛑 [AutoAudioManager] 已停止其他音频播放器")
    }

    /// 设置音频会话
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            NSLog("❌ [AutoAudioManager] 音频会话设置失败: \(error)")
        }
    }


    
    /// 预加载所有音频
    private func preloadAllAudio() async {
        NSLog("📥 [AutoAudioManager] 开始预加载 \(playbackQueue.count) 个音频文件")
        
        for (index, phonetic) in playbackQueue.enumerated() {
            guard let audioUrl = phonetic.audioUrl,
                  let url = URL(string: audioUrl) else {
                continue
            }
            
            do {
                NSLog("📥 [AutoAudioManager] 预加载音频 \(index + 1)/\(playbackQueue.count): \(phonetic.type.displayName)")
                
                let (data, _) = try await URLSession.shared.data(from: url)
                audioCache[audioUrl] = data
                
                NSLog("✅ [AutoAudioManager] 音频预加载成功: \(phonetic.type.displayName) (\(data.count) bytes)")
                
            } catch {
                NSLog("❌ [AutoAudioManager] 音频预加载失败: \(phonetic.type.displayName) - \(error)")
            }
        }
        
        NSLog("🎯 [AutoAudioManager] 预加载完成，成功缓存 \(audioCache.count)/\(playbackQueue.count) 个音频")
    }
    
    /// 开始顺序播放
    private func startSequentialPlayback() async {
        await MainActor.run {
            state = .playing
            currentPlayIndex = 0
        }
        
        NSLog("🎵 [AutoAudioManager] 开始顺序播放，共 \(playbackQueue.count) 个音频")
        
        await playNextAudio()
    }
    
    /// 播放下一个音频
    private func playNextAudio() async {
        guard currentPlayIndex < playbackQueue.count else {
            // 播放完成，但保持可重新播放状态
            await MainActor.run {
                state = .completed  // 保持completed状态，允许重新播放
                progress = 1.0
                NSLog("🎉 [AutoAudioManager] 所有音频播放完成，可重新播放")
                onPlaybackCompleted?()
            }
            return
        }
        
        let phonetic = playbackQueue[currentPlayIndex]
        
        guard let audioUrl = phonetic.audioUrl,
              let audioData = audioCache[audioUrl] else {
            NSLog("❌ [AutoAudioManager] 音频数据不存在: \(phonetic.type.displayName)")
            currentPlayIndex += 1
            await playNextAudio()
            return
        }
        
        await MainActor.run {
            do {
                NSLog("🔊 [AutoAudioManager] 播放音频 \(currentPlayIndex + 1)/\(playbackQueue.count): \(phonetic.type.displayName)")
                
                audioPlayer = try AVAudioPlayer(data: audioData)
                audioPlayer?.delegate = self
                audioPlayer?.play()
                
                // 更新进度
                progress = Double(currentPlayIndex) / Double(playbackQueue.count)
                
            } catch {
                NSLog("❌ [AutoAudioManager] 音频播放失败: \(phonetic.type.displayName) - \(error)")
                currentPlayIndex += 1
                Task {
                    await playNextAudio()
                }
            }
        }
    }
}

// MARK: - AVAudioPlayerDelegate

extension AutoAudioManager: AVAudioPlayerDelegate {

    nonisolated func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        NSLog("✅ [AutoAudioManager] 音频播放完成: \(flag ? "成功" : "失败")")

        Task { @MainActor in
            currentPlayIndex += 1
            await playNextAudio()
        }
    }
    
    nonisolated func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        NSLog("❌ [AutoAudioManager] 音频解码错误: \(error?.localizedDescription ?? "未知错误")")

        Task { @MainActor in
            currentPlayIndex += 1
            await playNextAudio()
        }
    }
}
