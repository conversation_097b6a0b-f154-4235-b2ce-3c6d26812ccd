//
//  SearchAPIAdapter.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  Generated from KDD-022 Functions Contract Tweening Chain
//

import Foundation

/// 搜索索引API转译层协议
/// 定义固定分页搜索索引方法的契约接口
protocol SearchAPIAdapterProtocol {
    func getWordIndexPage(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, page: Int) async throws -> WordIndexResponse
    func getDownloadRange(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, localMaxPage: Int) async throws -> DownloadRangeResponse
}

/// 搜索索引API转译层实现
/// [核心职责]: 纯转译功能，负责HTTP请求构建、JSON解析、数据类型转换
/// [设计原则]: 无状态设计，无业务逻辑，完全机械化转换
class SearchAPIAdapter: SearchAPIAdapterProtocol {
    private let apiClient: APIClientProtocol

    init(apiClient: APIClientProtocol) {
        self.apiClient = apiClient
    }

    // MARK: - 固定分页索引转译器

    /// 获取单词索引分页数据
    /// 职责: 获取指定语言对的固定分页索引数据
    /// 输入: learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, page: Int
    /// 输出: WordIndexResponse
    func getWordIndexPage(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, page: Int) async throws -> WordIndexResponse {
        // 情境化：记录方法调用来源
        NSLog("🔄 [SearchAPIAdapter.getWordIndexPage] 开始获取单词索引分页数据")

        // 透明化：打印输入参数和配置
        NSLog("📥 [SearchAPIAdapter.getWordIndexPage] 输入参数 - learningLanguage: \(learningLanguage.rawValue), scaffoldingLanguage: \(scaffoldingLanguage.rawValue), page: \(page)")

        // 1. 构建分页端点
        let endpoint = "/api/v1/word-index/\(learningLanguage.rawValue)/\(scaffoldingLanguage.rawValue)/page/\(page)"

        NSLog("📝 [SearchAPIAdapter.getWordIndexPage] 固定分页模式 - 页码: \(page)")

        // 精确化：打印完整的、可复现的请求信息
        NSLog("🌐 [SearchAPIAdapter.getWordIndexPage] 准备发起HTTP请求")
        NSLog("🔗 [SearchAPIAdapter.getWordIndexPage] 请求端点: \(endpoint)")
        NSLog("📋 [SearchAPIAdapter.getWordIndexPage] 请求方法: GET")
        NSLog("🔑 [SearchAPIAdapter.getWordIndexPage] 请求头: \(APIConfig.staticHeaders)")

        do {
            // 2. 发起HTTP请求（仅静态密钥认证）
            let response: WordIndexResponse = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                headers: APIConfig.staticHeaders,
                body: nil
            )

            // 完整化：记录成功响应
            NSLog("✅ [SearchAPIAdapter.getWordIndexPage] 请求成功完成")
            NSLog("📊 [SearchAPIAdapter.getWordIndexPage] 响应数据 - 成功: \(response.success), 数据条数: \(response.data.count), 最新syncId: \(response.lastSyncId)")
            NSLog("📈 [SearchAPIAdapter.getWordIndexPage] 元数据 - 总数: \(response.metadata.count), 请求时间: \(response.metadata.requestTime)ms")

            return response

        } catch {
            // 链条化：展示错误传递过程
            NSLog("❌ [SearchAPIAdapter.getWordIndexPage] 请求失败")
            NSLog("🔍 [SearchAPIAdapter.getWordIndexPage] 错误详情: \(error.localizedDescription)")
            NSLog("🔄 [SearchAPIAdapter.getWordIndexPage] 错误类型: \(type(of: error))")
            NSLog("📤 [SearchAPIAdapter.getWordIndexPage] 向上层抛出错误: \(error)")

            throw error
        }
    }

    /// 获取下载范围
    /// 职责: 基于本地最大页码计算需要下载的页面范围
    /// 输入: learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, localMaxPage: Int
    /// 输出: DownloadRangeResponse
    func getDownloadRange(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, localMaxPage: Int) async throws -> DownloadRangeResponse {
        // 情境化：记录方法调用来源
        NSLog("🔄 [SearchAPIAdapter.getDownloadRange] 开始获取下载范围")

        // 透明化：打印输入参数
        NSLog("📥 [SearchAPIAdapter.getDownloadRange] 输入参数 - learningLanguage: \(learningLanguage.rawValue), scaffoldingLanguage: \(scaffoldingLanguage.rawValue), localMaxPage: \(localMaxPage)")

        do {
            // 构建端点URL
            let endpoint = "/api/v1/word-index/\(learningLanguage.rawValue)/\(scaffoldingLanguage.rawValue)/download-range?localMaxPage=\(localMaxPage)"
            NSLog("🌐 [SearchAPIAdapter.getDownloadRange] 构建端点: \(endpoint)")

            // 发起API请求
            NSLog("📡 [SearchAPIAdapter.getDownloadRange] 发起API请求")
            let response: DownloadRangeResponse = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                headers: APIConfig.staticHeaders,
                body: nil
            )

            // 透明化：打印响应数据
            NSLog("📤 [SearchAPIAdapter.getDownloadRange] API响应成功")
            NSLog("📊 [SearchAPIAdapter.getDownloadRange] 下载范围 - startPage: \(response.startPage), endPage: \(response.endPage), totalPages: \(response.totalPages)")
            NSLog("📈 [SearchAPIAdapter.getDownloadRange] 预估单词数: \(response.estimatedItems)")

            return response

        } catch {
            // 链条化：展示错误传递过程
            NSLog("❌ [SearchAPIAdapter.getDownloadRange] 请求失败")
            NSLog("🔍 [SearchAPIAdapter.getDownloadRange] 错误详情: \(error.localizedDescription)")
            NSLog("🔄 [SearchAPIAdapter.getDownloadRange] 错误类型: \(type(of: error))")
            NSLog("📤 [SearchAPIAdapter.getDownloadRange] 向上层抛出错误: \(error)")

            throw error
        }
    }
}