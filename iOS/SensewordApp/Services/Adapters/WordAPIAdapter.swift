//
//  WordAPIAdapter.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-06-26.
//  Generated from KDD-022 Functions Contract Tweening Chain
//

import Foundation

/// 单词服务API转译层协议
/// 定义核心单词服务方法的契约接口
protocol WordAPIAdapterProtocol {
    /// 获取单词定义（双语言参数版本）
    func getWord(_ word: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> WordDefinitionResponse

    /// 获取单词定义（便捷方法，使用默认语言对）
    func getWord(_ word: String) async throws -> WordDefinitionResponse
}

/// 单词服务API转译层实现
/// [核心职责]: 纯转译功能，负责HTTP请求构建、JSON解析、数据类型转换
/// [设计原则]: 无状态设计，无业务逻辑，完全机械化转换
class WordAPIAdapter: WordAPIAdapterProtocol {
    private let apiClient: APIClientProtocol
    private let languagePairConfigManager: LanguagePairConfigManagerProtocol

    init(apiClient: APIClientProtocol, languagePairConfigManager: LanguagePairConfigManagerProtocol) {
        self.apiClient = apiClient
        self.languagePairConfigManager = languagePairConfigManager
    }

    // MARK: - 单词查询转译器

    /// 单词定义查询转译（双语言参数版本）
    /// 职责: 处理单词查询请求，支持完整的多语言单词定义获取
    /// 输入: word: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode
    /// 输出: WordDefinitionResponse
    func getWord(_ word: String, learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> WordDefinitionResponse {
        // 情境化：记录方法调用来源
        NSLog("📖 [WordAPIAdapter.getWord] 开始获取单词定义（双语言版本）")

        // 透明化：打印输入参数和配置
        NSLog("📥 [WordAPIAdapter.getWord] 输入参数 - word: '\(word)', learningLanguage: \(learningLanguage.rawValue), scaffoldingLanguage: \(scaffoldingLanguage.rawValue)")

        // 1. 语言代码验证
        guard languagePairConfigManager.isLanguagePairSupported(learningLanguage: learningLanguage, scaffoldingLanguage: scaffoldingLanguage) else {
            let errorMessage = "不支持的语言对: \(learningLanguage.rawValue)/\(scaffoldingLanguage.rawValue)"
            NSLog("❌ [WordAPIAdapter.getWord] 语言验证失败: \(errorMessage)")
            throw APIError.invalidRequest(errorMessage)
        }

        NSLog("✅ [WordAPIAdapter.getWord] 语言对验证通过: \(learningLanguage.rawValue)/\(scaffoldingLanguage.rawValue)")

        // 2. URL编码处理特殊字符
        let encodedWord = word.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? word
        NSLog("🔤 [WordAPIAdapter.getWord] URL编码处理 - 原始: '\(word)' -> 编码后: '\(encodedWord)'")

        // 2. 构建RESTful端点路径
        let endpoint = "/api/v1/words/\(learningLanguage.rawValue)/\(scaffoldingLanguage.rawValue)/\(encodedWord)"

        NSLog("🌍 [WordAPIAdapter.getWord] RESTful路径构建 - 学习语言: \(learningLanguage.rawValue), 脚手架语言: \(scaffoldingLanguage.rawValue)")
        NSLog("🔗 [WordAPIAdapter.getWord] 双语言端点格式: \(endpoint)")

        // 精确化：打印完整的、可复现的请求信息
        NSLog("🌐 [WordAPIAdapter.getWord] 准备发起HTTP请求")
        NSLog("🔗 [WordAPIAdapter.getWord] 请求端点: \(endpoint)")
        NSLog("📋 [WordAPIAdapter.getWord] 请求方法: GET")
        NSLog("🔑 [WordAPIAdapter.getWord] 请求头: \(APIConfig.staticHeaders)")

        do {
            // 3. 发起HTTP请求（仅静态密钥认证）
            let response: WordDefinitionResponse = try await apiClient.request(
                endpoint: endpoint,
                method: .GET,
                headers: APIConfig.staticHeaders,
                body: nil
            )

            // 完整化：记录成功响应
            NSLog("✅ [WordAPIAdapter.getWord] 请求成功完成")
            NSLog("📊 [WordAPIAdapter.getWord] 响应数据 - 单词: '\(response.word)', 难度: \(response.content.difficulty)")
            NSLog("📈 [WordAPIAdapter.getWord] 内容统计 - 核心定义: '\(response.content.coreDefinition)', 用法示例: \(response.content.usageExamples.count)个分类")

            return response

        } catch {
            // 链条化：展示错误传递过程
            NSLog("❌ [WordAPIAdapter.getWord] 请求失败")
            NSLog("🔍 [WordAPIAdapter.getWord] 错误详情: \(error.localizedDescription)")
            NSLog("🔄 [WordAPIAdapter.getWord] 错误类型: \(type(of: error))")
            NSLog("📤 [WordAPIAdapter.getWord] 向上层抛出错误: \(error)")

            throw error
        }
    }
    /// 单词定义查询转译（便捷方法）
    /// 职责: 使用默认语言对配置获取单词定义
    /// 输入: word: String
    /// 输出: WordDefinitionResponse
    func getWord(_ word: String) async throws -> WordDefinitionResponse {
        // 情境化：记录方法调用来源
        NSLog("📖 [WordAPIAdapter.getWord] 开始获取单词定义（便捷方法）")

        // 从语言对配置管理器获取默认语言对
        let defaultLanguagePair = languagePairConfigManager.getDefaultLanguagePair()

        NSLog("🌍 [WordAPIAdapter.getWord] 使用默认语言对 - 学习语言: \(defaultLanguagePair.learningLanguage.rawValue), 脚手架语言: \(defaultLanguagePair.scaffoldingLanguage.rawValue)")

        // 调用主方法
        return try await getWord(
            word,
            learningLanguage: defaultLanguagePair.learningLanguage,
            scaffoldingLanguage: defaultLanguagePair.scaffoldingLanguage
        )
    }








}
