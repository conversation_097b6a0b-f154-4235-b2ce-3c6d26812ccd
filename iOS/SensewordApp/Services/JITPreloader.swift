//
//  JITPreloader.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-06-29.
//  JIT预加载服务 - 无限内容流核心组件
//

import Foundation
import Combine
import UIKit

/**
 * @description JIT预加载服务
 * 
 * 核心职责：
 * - 预加载下一个单词的完整数据（单词定义 + 所有音频文件）
 * - 智能缓存管理，避免内存泄漏
 * - 异步加载，不阻塞UI线程
 * - 提供加载状态和进度反馈
 * 
 * 设计原则：
 * - JIT (Just-In-Time): 只预加载下一个单词，避免资源浪费
 * - 独立服务: 与UI和业务逻辑完全解耦
 * - 错误处理: 完善的降级方案和错误恢复
 * - 性能优化: 智能缓存和内存管理
 */
@MainActor
class JITPreloader: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 当前预加载状态
    @Published private(set) var isPreloading: Bool = false
    
    /// 预加载进度 (0.0 - 1.0)
    @Published private(set) var preloadProgress: Double = 0.0
    
    /// 最后预加载的单词
    @Published private(set) var lastPreloadedWord: String?
    
    /// 预加载统计信息
    @Published private(set) var stats: PreloadStats = PreloadStats()
    
    // MARK: - Private Properties
    
    /// 单词数据缓存
    private var wordDataCache: [String: WordDefinitionResponse] = [:]
    
    /// 音频URL缓存
    private var audioURLCache: [String: [PreloadAudioType: String]] = [:]
    
    /// 正在加载的任务
    private var loadingTasks: [String: Task<Void, Never>] = [:]
    
    /// 取消订阅集合
    private var cancellables = Set<AnyCancellable>()
    
    /// 配置参数
    private let config: JITPreloaderConfig

    /// 单词API适配器
    private let wordAPIAdapter: WordAPIAdapterProtocol



    // MARK: - Initialization

    init(
        config: JITPreloaderConfig = .default,
        wordAPIAdapter: WordAPIAdapterProtocol? = nil
    ) {
        self.config = config
        self.wordAPIAdapter = wordAPIAdapter ?? DIContainer.shared.wordAPIAdapter

        setupMemoryWarningObserver()
    }
    
    // MARK: - Public Interface
    
    /**
     * 预加载指定单词的完整数据
     * 包括单词定义和所有音频文件
     */
    func preloadWord(_ word: String) async {
        // 检查是否已在缓存中
        if isWordFullyCached(word) {
            print("[JITPreloader] 单词'\(word)'已在缓存中，跳过预加载")
            return
        }
        
        // 检查是否正在加载
        if loadingTasks[word] != nil {
            print("[JITPreloader] 单词'\(word)'正在加载中，跳过重复请求")
            return
        }
        
        // 创建预加载任务
        let task = Task {
            await performPreload(for: word)
        }
        
        loadingTasks[word] = task
        await task.value
        loadingTasks.removeValue(forKey: word)
    }
    
    /**
     * 获取缓存的单词数据
     */
    func getCachedWordData(_ word: String) -> WordDefinitionResponse? {
        return wordDataCache[word]
    }
    
    /**
     * 获取缓存的音频URL
     */
    func getCachedAudioURL(_ word: String, type: PreloadAudioType) -> String? {
        return audioURLCache[word]?[type]
    }
    
    /**
     * 检查单词是否完全缓存（包括数据和音频）
     */
    func isWordFullyCached(_ word: String) -> Bool {
        guard let _ = wordDataCache[word] else { return false }
        
        // 检查所有必需的音频类型是否都已缓存
        let requiredAudioTypes: [PreloadAudioType] = [.pronunciation, .example, .usage]
        for audioType in requiredAudioTypes {
            if audioURLCache[word]?[audioType] == nil {
                return false
            }
        }
        
        return true
    }
    
    /**
     * 清理缓存
     */
    func clearCache() {
        wordDataCache.removeAll()
        audioURLCache.removeAll()

        // 取消所有正在进行的任务
        for (_, task) in loadingTasks {
            task.cancel()
        }
        loadingTasks.removeAll()

        updateStats()
        print("[JITPreloader] 缓存已清理")
    }
    
    /**
     * 清理过期缓存
     */
    func clearExpiredCache() {
        let maxCacheSize = config.maxCacheSize
        
        // 如果缓存大小超过限制，清理最旧的条目
        if wordDataCache.count > maxCacheSize {
            let sortedKeys = wordDataCache.keys.sorted()
            let keysToRemove = sortedKeys.prefix(wordDataCache.count - maxCacheSize)
            
            for key in keysToRemove {
                wordDataCache.removeValue(forKey: key)
                audioURLCache.removeValue(forKey: key)
            }
            
            print("[JITPreloader] 清理了\(keysToRemove.count)个过期缓存条目")
        }
        
        updateStats()
    }
    
    // MARK: - Private Methods
    
    /**
     * 执行预加载
     */
    private func performPreload(for word: String) async {
        updatePreloadingState(true, word: word)
        updateProgress(0.0)
        
        do {
            // 步骤1: 加载单词数据 (30%)
            print("[JITPreloader] 开始预加载单词'\(word)'的数据")
            // 使用便捷方法，自动获取默认语言对配置
            let wordData = try await wordAPIAdapter.getWord(word)
            wordDataCache[word] = wordData
            updateProgress(0.3)
            
            // 步骤2: 提取音频URL (70%)
            print("[JITPreloader] 开始提取单词'\(word)'的音频URL")
            await extractAudioURLs(for: word, wordData: wordData)
            updateProgress(1.0)
            
            // 更新统计信息
            stats.totalPreloaded += 1
            stats.lastPreloadTime = Date()
            
            print("[JITPreloader] 单词'\(word)'预加载完成")
            
        } catch {
            print("[JITPreloader] 预加载单词'\(word)'失败: \(error)")
            stats.failedPreloads += 1
            
            // 清理部分加载的数据
            wordDataCache.removeValue(forKey: word)
            audioURLCache.removeValue(forKey: word)
        }
        
        updatePreloadingState(false, word: nil)
        updateProgress(0.0)
        
        // 清理过期缓存
        clearExpiredCache()
    }
    
    /**
     * 提取音频URL
     */
    private func extractAudioURLs(for word: String, wordData: WordDefinitionResponse) async {
        var audioURLs: [PreloadAudioType: String] = [:]

        // 提取发音音频URL（从音标中获取）
        if let firstPhonetic = wordData.content.phoneticSymbols.first,
           let audioUrl = firstPhonetic.audioUrl,
           isValidAudioURL(audioUrl) {
            audioURLs[.pronunciation] = audioUrl
            print("[JITPreloader] 发现发音音频URL: \(audioUrl)")
        }

        // 提取例句音频URL（从使用示例中获取第一个有音频的例句）
        for category in wordData.content.usageExamples {
            for example in category.examples {
                if let audioUrl = example.audioUrl,
                   isValidAudioURL(audioUrl) {
                    audioURLs[.example] = audioUrl
                    print("[JITPreloader] 发现例句音频URL: \(audioUrl)")
                    break
                }
            }
            if audioURLs[.example] != nil { break }
        }

        // 提取短语音频URL（从短语分解中获取第一个有音频的短语）
        for category in wordData.content.usageExamples {
            for example in category.examples {
                if let phraseBreakdowns = example.phraseBreakdown {
                    for phrase in phraseBreakdowns {
                        if let audioUrl = phrase.audioUrl,
                           isValidAudioURL(audioUrl) {
                            audioURLs[.usage] = audioUrl
                            print("[JITPreloader] 发现短语音频URL: \(audioUrl)")
                            break
                        }
                    }
                }
                if audioURLs[.usage] != nil { break }
            }
            if audioURLs[.usage] != nil { break }
        }

        audioURLCache[word] = audioURLs
        updateProgress(1.0)

        print("[JITPreloader] 音频URL提取完成: \(word) - 共\(audioURLs.count)个音频")
    }
    
    /**
     * 更新预加载状态
     */
    private func updatePreloadingState(_ isLoading: Bool, word: String?) {
        isPreloading = isLoading
        lastPreloadedWord = word
    }
    
    /**
     * 验证音频URL是否有效
     */
    private func isValidAudioURL(_ urlString: String) -> Bool {
        // 检查URL字符串是否为空
        guard !urlString.isEmpty else {
            print("[JITPreloader] 音频URL为空")
            return false
        }

        // 检查是否为有效的URL格式
        guard let url = URL(string: urlString) else {
            print("[JITPreloader] 无效的URL格式: \(urlString)")
            return false
        }

        // 检查是否为HTTPS协议
        guard url.scheme == "https" else {
            print("[JITPreloader] 音频URL必须使用HTTPS协议: \(urlString)")
            return false
        }

        // 检查是否为SenseWord音频域名
        guard url.host == "audio.senseword.app" else {
            print("[JITPreloader] 音频URL必须来自audio.senseword.app域名: \(urlString)")
            return false
        }

        // 检查文件扩展名
        let pathExtension = url.pathExtension.lowercased()
        guard pathExtension == "wav" || pathExtension == "mp3" else {
            print("[JITPreloader] 音频URL必须是wav或mp3格式: \(urlString)")
            return false
        }

        return true
    }

    /**
     * 更新预加载进度
     */
    private func updateProgress(_ progress: Double) {
        preloadProgress = max(0.0, min(1.0, progress))
    }
    
    /**
     * 更新统计信息
     */
    private func updateStats() {
        stats.cachedWordsCount = wordDataCache.count
        stats.cachedAudioFilesCount = audioURLCache.values.reduce(0) { $0 + $1.count }
        stats.cacheMemoryUsage = calculateCacheMemoryUsage()
    }
    
    /**
     * 计算缓存内存使用量
     */
    private func calculateCacheMemoryUsage() -> Int {
        var totalSize = 0

        // 计算音频URL缓存大小（URL字符串很小）
        for audioURLs in audioURLCache.values {
            for audioURL in audioURLs.values {
                totalSize += audioURL.utf8.count
            }
        }

        // 估算单词数据大小（简化计算）
        totalSize += wordDataCache.count * 1024 // 假设每个单词数据约1KB

        return totalSize
    }
    
    /**
     * 设置内存警告观察者
     */
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
    }
    
    /**
     * 处理内存警告
     */
    private func handleMemoryWarning() {
        print("[JITPreloader] 收到内存警告，清理缓存")
        
        // 保留最近预加载的单词，清理其他缓存
        if let lastWord = lastPreloadedWord {
            let wordDataToKeep = wordDataCache[lastWord]
            let audioDataToKeep = audioURLCache[lastWord]

            clearCache()

            if let wordData = wordDataToKeep {
                wordDataCache[lastWord] = wordData
            }
            if let audioData = audioDataToKeep {
                audioURLCache[lastWord] = audioData
            }
        } else {
            clearCache()
        }
        
        updateStats()
    }
}

// MARK: - Supporting Types

/**
 * 预加载音频类型枚举
 */
enum PreloadAudioType: String, CaseIterable {
    case pronunciation = "pronunciation"  // 单词发音
    case example = "example"             // 例句音频
    case usage = "usage"                 // 用法音频

    var displayName: String {
        switch self {
        case .pronunciation:
            return "发音"
        case .example:
            return "例句"
        case .usage:
            return "用法"
        }
    }
}

/**
 * JIT预加载器配置
 */
struct JITPreloaderConfig {
    let maxCacheSize: Int           // 最大缓存单词数量
    let maxMemoryUsage: Int         // 最大内存使用量（字节）
    let preloadTimeout: TimeInterval // 预加载超时时间
    let enableAudioPreload: Bool    // 是否启用音频预加载

    static let `default` = JITPreloaderConfig(
        maxCacheSize: 10,
        maxMemoryUsage: 50 * 1024 * 1024, // 50MB
        preloadTimeout: 30.0,
        enableAudioPreload: true
    )
}

/**
 * 预加载统计信息
 */
struct PreloadStats {
    var totalPreloaded: Int = 0
    var failedPreloads: Int = 0
    var cachedWordsCount: Int = 0
    var cachedAudioFilesCount: Int = 0
    var cacheMemoryUsage: Int = 0
    var lastPreloadTime: Date?

    var successRate: Double {
        let total = totalPreloaded + failedPreloads
        return total > 0 ? Double(totalPreloaded) / Double(total) : 0.0
    }

    var memoryUsageString: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(cacheMemoryUsage))
    }
}

// MARK: - Audio Preloading Helper

/**
 * 音频预加载助手
 * 提供与GlobalAudioManager集成的便捷方法
 */
extension JITPreloader {

    /**
     * 预加载音频到GlobalAudioManager缓存
     */
    func preloadAudioToGlobalCache(_ word: String) async {
        guard let audioURLs = audioURLCache[word] else {
            print("[JITPreloader] 没有找到单词'\(word)'的音频URL")
            return
        }

        print("[JITPreloader] 开始预加载音频到GlobalAudioManager缓存: \(word)")

        for (audioType, audioURL) in audioURLs {
            guard let url = URL(string: audioURL) else {
                print("[JITPreloader] 无效的音频URL: \(audioURL)")
                continue
            }

            do {
                // 使用GlobalAudioManager的缓存机制预加载音频
                let globalAudioManager = GlobalAudioManager.shared
                // 这里我们可以调用一个预加载方法，或者直接触发下载
                // 由于GlobalAudioManager的getAudioData是私有的，我们可能需要添加一个公共预加载方法
                print("[JITPreloader] 音频URL已准备: \(audioType.displayName) - \(audioURL)")

            } catch {
                print("[JITPreloader] 预加载音频失败: \(audioType.displayName) - \(error)")
            }
        }
    }
}

// MARK: - Extensions

extension JITPreloader {

    /**
     * 获取调试信息
     */
    func getDebugInfo() -> [String: Any] {
        return [
            "isPreloading": isPreloading,
            "preloadProgress": preloadProgress,
            "lastPreloadedWord": lastPreloadedWord ?? "无",
            "cachedWords": Array(wordDataCache.keys),
            "cachedAudioTypes": audioURLCache.mapValues { Array($0.keys.map { $0.rawValue }) },
            "loadingTasks": Array(loadingTasks.keys),
            "stats": [
                "totalPreloaded": stats.totalPreloaded,
                "failedPreloads": stats.failedPreloads,
                "successRate": String(format: "%.1f%%", stats.successRate * 100),
                "cachedWordsCount": stats.cachedWordsCount,
                "cachedAudioFilesCount": stats.cachedAudioFilesCount,
                "memoryUsage": stats.memoryUsageString
            ]
        ]
    }

    /**
     * 预加载多个单词
     */
    func preloadWords(_ words: [String]) async {
        print("[JITPreloader] 开始批量预加载\(words.count)个单词")

        for word in words {
            await preloadWord(word)
        }

        print("[JITPreloader] 批量预加载完成")
    }

    /**
     * 获取缓存命中率
     */
    func getCacheHitRate() -> Double {
        return stats.successRate
    }
}
