//
//  LocalIndexService.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  本地索引服务 - 管理本地搜索索引，提供毫秒级搜索建议功能
//

import Foundation

// MARK: - 本地索引配置

/// 本地索引配置
struct LocalIndexConfig {
    let maxIndexSize: Int = 10485760           // 最大索引大小（10MB）
    let syncInterval: TimeInterval = 14400     // 同步间隔（4小时，因为现在有基于用户行为的触发更新）
    let searchTimeout: TimeInterval = 0.1      // 搜索超时（100ms）
    let maxSuggestions: Int = 10               // 最大建议数量
    let minQueryLength: Int = 2                // 最小查询长度
}

/// 搜索性能指标
struct SearchPerformanceMetrics {
    let queryTime: TimeInterval
    let resultCount: Int
    let indexSize: Int
    let lastSyncTime: Date
    let cacheHitRate: Double
    let source: SearchSource
    
    /// 是否超过性能阈值
    var exceedsThreshold: Bool {
        return queryTime > 0.1 // 100ms阈值
    }
}

// MARK: - 本地索引服务协议

/// 本地索引服务协议定义
protocol LocalIndexServiceProtocol {
    func searchSuggestions(query: String, limit: Int) async -> [SearchSuggestion]
    func checkCache(word: String, language: LanguageCode) async -> CacheStatus
    func getIndexData(word: String, scaffoldingLanguage: LanguageCode) async -> WordIndexItem?
    func updateLocalIndex(indexItems: [WordIndexItem]) async throws
    func syncIndexUpdates(since: Int?) async throws -> Int
    func getOptimizedSuggestions(query: String, limit: Int) async -> [SearchSuggestion]
    func checkIndexHealth() async -> [String: Any]
    func startPeriodicSync()
}

// MARK: - 本地索引服务实现

/// 本地索引服务实现
/// 核心职责：管理本地搜索索引数据库，提供毫秒级搜索建议功能，处理增量索引同步
/// 技术特点：支持离线搜索，使用SQLite FTS，维护本地搜索数据的实时性和完整性
class LocalIndexService: LocalIndexServiceProtocol {
    
    // MARK: - 私有属性
    
    /// 配置
    private let config = LocalIndexConfig()
    
    /// SQLite数据库管理器
    private let sqliteManager: SQLiteManagerProtocol
    
    /// 缓存服务
    private let cacheService: CacheServiceProtocol
    
    /// 搜索API适配器
    private let searchAPIAdapter: SearchAPIAdapterProtocol
    
    /// 性能指标
    private var performanceMetrics: [SearchPerformanceMetrics] = []
    
    /// 指标访问锁
    private let metricsLock = NSLock()
    
    /// 同步状态
    private var lastSyncTime: Date?
    private var isSyncing = false
    private let syncLock = NSLock()

    /// 定期同步相关属性
    private var syncTimer: Timer?
    private var isSyncStarted = false
    
    // MARK: - 初始化
    
    /// 初始化本地索引服务
    /// - Parameters:
    ///   - sqliteManager: SQLite管理器
    ///   - cacheService: 缓存服务
    ///   - searchAPIAdapter: 搜索API适配器
    init(sqliteManager: SQLiteManagerProtocol,
         cacheService: CacheServiceProtocol,
         searchAPIAdapter: SearchAPIAdapterProtocol) {
        self.sqliteManager = sqliteManager
        self.cacheService = cacheService
        self.searchAPIAdapter = searchAPIAdapter

        // 注意：同步将在应用启动时由 SenseWordApp 触发
        // 不在初始化时启动，避免阻塞主线程
    }
    
    // MARK: - 公共接口实现
    
    /// 搜索建议
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - limit: 结果数量限制
    /// - Returns: 搜索建议列表
    func searchSuggestions(query: String, limit: Int = 10) async -> [SearchSuggestion] {
        let startTime = Date()

        // 注意：同步现在在应用启动时就开始了，不需要在搜索时触发

        // 验证查询长度
        guard query.count >= config.minQueryLength else {
            return []
        }

        // 限制结果数量
        let actualLimit = min(limit, config.maxSuggestions)
        
        do {
            // 1. 检查缓存
            let cacheKey = "suggestions_\(query.lowercased())_\(actualLimit)"
            if let cachedSuggestions: [SearchSuggestion] = cacheService.get(cacheKey, type: [SearchSuggestion].self) {
                recordPerformanceMetrics(
                    queryTime: Date().timeIntervalSince(startTime),
                    resultCount: cachedSuggestions.count,
                    source: .localIndex
                )
                return cachedSuggestions
            }
            
            // 2. 执行本地搜索
            let suggestions = try await performLocalSearch(query: query, limit: actualLimit)
            
            // 3. 缓存结果
            cacheService.set(cacheKey, value: suggestions, expiry: 300) // 5分钟缓存
            
            // 4. 记录性能指标
            recordPerformanceMetrics(
                queryTime: Date().timeIntervalSince(startTime),
                resultCount: suggestions.count,
                source: .localIndex
            )
            
            return suggestions
            
        } catch {
            print("Search suggestions failed: \(error)")
            return []
        }
    }
    
    /// 检查缓存状态
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 缓存状态
    func checkCache(word: String, language: LanguageCode) async -> CacheStatus {
        // 使用缓存服务的三级检查
        return cacheService.checkCacheStatus(word: word, language: language)
    }
    
    /// 获取索引数据
    /// - Parameters:
    ///   - word: 单词
    ///   - scaffoldingLanguage: 脚手架语言代码
    /// - Returns: 单词索引项
    func getIndexData(word: String, scaffoldingLanguage: LanguageCode) async -> WordIndexItem? {
        do {
            // 1. 检查缓存
            let cacheKey = "word_index_\(word)_\(scaffoldingLanguage.rawValue)"
            if let cachedItem: WordIndexItem = cacheService.get(cacheKey, type: WordIndexItem.self) {
                return cachedItem
            }

            // 2. 从数据库查询
            if let result = try await sqliteManager.findWordIndex(word: word, scaffoldingLanguage: scaffoldingLanguage) {
                let indexItem = try parseWordIndexItem(from: result)

                // 3. 缓存结果
                cacheService.set(cacheKey, value: indexItem, expiry: 3600) // 1小时缓存

                return indexItem
            }

            return nil

        } catch {
            print("Get index data failed: \(error)")
            return nil
        }
    }
    
    /// 更新本地索引
    /// - Parameter indexItems: 索引项数组
    func updateLocalIndex(indexItems: [WordIndexItem]) async throws {
        guard !indexItems.isEmpty else {
            NSLog("⚠️ LocalIndexService: updateLocalIndex 收到空数组，跳过更新")
            return
        }

        NSLog("💾 LocalIndexService: updateLocalIndex 开始 - 处理 \(indexItems.count) 条记录")

        // 打印前几个数据项用于调试
        for (index, item) in indexItems.prefix(3).enumerated() {
            NSLog("📝 更新数据 \(index + 1): syncId=\(item.syncId), word='\(item.word)', definition='\(item.coreDefinition)'")
        }

        do {
            // 1. 批量更新数据库
            NSLog("🗄️ LocalIndexService: 开始批量更新数据库")
            try await sqliteManager.batchUpsertWordIndex(indexItems)
            NSLog("✅ LocalIndexService: 数据库批量更新完成")

            // 2. 更新缓存
            NSLog("🔄 LocalIndexService: 开始更新缓存")
            for item in indexItems {
                let cacheKey = "word_index_\(item.word)_\(item.scaffoldingLanguage.rawValue)"
                cacheService.set(cacheKey, value: item, expiry: 3600)
            }
            NSLog("✅ LocalIndexService: 缓存更新完成")

            // 3. 清理相关搜索缓存
            clearSearchCache()

            NSLog("✅ LocalIndexService: updateLocalIndex 完成 - 已更新 \(indexItems.count) 条记录")

        } catch {
            NSLog("❌ LocalIndexService: updateLocalIndex 失败 - \(error)")
            throw error
        }
    }
    
    /// 同步索引更新（简化版本，主要用于兼容性）
    /// 注意：新的按需下载功能请使用 IndexDownloadService
    /// - Parameter since: 上次同步ID
    /// - Returns: 新的同步ID
    func syncIndexUpdates(since: Int? = nil) async throws -> Int {
        // 使用 actor 或其他异步安全的同步机制
        guard !isSyncing else {
            throw SearchError.indexCorrupted // 重用现有错误类型
        }

        isSyncing = true
        defer { isSyncing = false }

        NSLog("⚠️ LocalIndexService: syncIndexUpdates 已简化，建议使用 IndexDownloadService 进行按需下载")

        do {
            // 简化的同步逻辑：只处理默认语言对的单页数据
            let learningLang = LanguageCode.english
            let scaffoldingLang = LanguageCode.chinese

            // 获取本地最大页码
            let localMaxPage = try await sqliteManager.getMaxPage(learningLang: learningLang, scaffoldingLang: scaffoldingLang)

            // 获取下载范围
            let downloadRange = try await searchAPIAdapter.getDownloadRange(
                learningLanguage: learningLang,
                scaffoldingLanguage: scaffoldingLang,
                localMaxPage: localMaxPage
            )

            // 如果有新数据，只下载第一页
            if downloadRange.startPage > 0 && downloadRange.endPage > 0 {
                let response = try await searchAPIAdapter.getWordIndexPage(
                    learningLanguage: learningLang,
                    scaffoldingLanguage: scaffoldingLang,
                    page: downloadRange.startPage
                )

                if !response.data.isEmpty {
                    try await updateLocalIndex(indexItems: response.data)
                    NSLog("✅ LocalIndexService: 简化同步完成 - \(response.data.count) 条记录")
                }

                return response.lastSyncId
            } else {
                NSLog("ℹ️ LocalIndexService: 没有新数据需要同步")
                return try await sqliteManager.getMaxSyncId(for: scaffoldingLang)
            }

        } catch {
            NSLog("❌ LocalIndexService: 简化同步失败 - \(error)")
            throw error
        }
    }

    /// 启动定期同步
    func startPeriodicSync() {
        guard !isSyncStarted else { return }
        isSyncStarted = true

        NSLog("🔄 LocalIndexService: 启动定期同步，间隔: \(config.syncInterval)秒")
        syncTimer = Timer.scheduledTimer(withTimeInterval: config.syncInterval, repeats: true) { [weak self] _ in
            Task {
                do {
                    _ = try await self?.syncIndexUpdates()
                } catch {
                    NSLog("❌ LocalIndexService: 定期同步失败 - \(error)")
                }
            }
        }
    }
}

// MARK: - 私有实现方法

private extension LocalIndexService {

    /// 执行本地搜索
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - limit: 结果数量限制
    /// - Returns: 搜索建议列表
    func performLocalSearch(query: String, limit: Int) async throws -> [SearchSuggestion] {
        // 当前语言设置（实际应该从用户设置获取）
        let learningLanguage = LanguageCode.english
        let scaffoldingLanguage = LanguageCode.chinese

        // 执行双语言搜索
        let results = try await sqliteManager.searchWordSuggestions(
            query: query,
            learningLanguage: learningLanguage,
            scaffoldingLanguage: scaffoldingLanguage,
            limit: limit
        )

        // 转换为SearchSuggestion对象
        return try results.compactMap { result in
            try parseSearchSuggestion(from: result)
        }
    }

    /// 解析搜索建议
    /// - Parameter result: 数据库查询结果
    /// - Returns: 搜索建议对象
    func parseSearchSuggestion(from result: [String: Any]) throws -> SearchSuggestion? {
        guard let word = result["word"] as? String,
              let coreDefinition = result["coreDefinition"] as? String else {
            return nil
        }

        // 基于单词长度计算相关性分数（越短的单词相关性越高）
        let wordLength = result["word_length"] as? Int ?? word.count
        // 确保计算不会产生NaN值
        let lengthScore = Double(wordLength) / 20.0
        let normalizedScore = max(0.1, min(1.0, 1.0 - lengthScore))

        // 验证分数有效性，防止NaN传递给UI
        guard normalizedScore.isFinite && !normalizedScore.isNaN else {
            NSLog("⚠️ LocalIndexService: 检测到无效的相关性分数，使用默认值 0.5")
            return SearchSuggestion(
                word: word,
                definition: coreDefinition,
                relevanceScore: 0.5,
                hasFullContent: false
            )
        }

        return SearchSuggestion(
            word: word,
            definition: coreDefinition,
            relevanceScore: normalizedScore,
            hasFullContent: false // 本地索引只有基础信息
        )
    }

    /// 解析单词索引项
    /// - Parameter result: 数据库查询结果
    /// - Returns: 单词索引项
    func parseWordIndexItem(from result: [String: Any]) throws -> WordIndexItem {
        guard let syncId = result["syncId"] as? Int,
              let page = result["page"] as? Int,
              let word = result["word"] as? String,
              let learningLanguageString = result["learningLanguage"] as? String,
              let scaffoldingLanguageString = result["scaffoldingLanguage"] as? String,
              let coreDefinition = result["coreDefinition"] as? String,
              let learningLanguage = LanguageCode(rawValue: learningLanguageString),
              let scaffoldingLanguage = LanguageCode(rawValue: scaffoldingLanguageString) else {
            throw SearchError.indexCorrupted
        }

        return WordIndexItem(
            syncId: syncId,
            page: page,
            word: word,
            learningLanguage: learningLanguage,
            scaffoldingLanguage: scaffoldingLanguage,
            coreDefinition: coreDefinition
        )
    }



    /// 记录性能指标
    /// - Parameters:
    ///   - queryTime: 查询时间
    ///   - resultCount: 结果数量
    ///   - source: 数据源
    func recordPerformanceMetrics(queryTime: TimeInterval, resultCount: Int, source: SearchSource) {
        metricsLock.lock()
        defer { metricsLock.unlock() }

        let metrics = SearchPerformanceMetrics(
            queryTime: queryTime,
            resultCount: resultCount,
            indexSize: getIndexSize(),
            lastSyncTime: lastSyncTime ?? Date.distantPast,
            cacheHitRate: getCacheHitRate(),
            source: source
        )

        performanceMetrics.append(metrics)

        // 保持最近100条记录
        if performanceMetrics.count > 100 {
            performanceMetrics.removeFirst(performanceMetrics.count - 100)
        }

        // 如果性能超过阈值，记录警告
        if metrics.exceedsThreshold {
            print("Search performance warning: \(queryTime * 1000)ms for \(resultCount) results")
        }
    }

    /// 获取索引大小
    /// - Returns: 索引大小（字节）
    func getIndexSize() -> Int {
        // Note: 这个方法暂时返回0，因为获取统计信息需要async调用
        // 在后续版本中，应该将此方法改为async或者预先缓存统计信息
        return 0
    }

    /// 获取缓存命中率
    /// - Returns: 缓存命中率
    func getCacheHitRate() -> Double {
        let metrics = cacheService.getCacheMetrics()
        return metrics.calculatedHitRate
    }

    /// 清理搜索缓存
    func clearSearchCache() {
        // 获取所有缓存键
        let allKeys = cacheService.getAllCacheKeys()

        // 清理搜索建议缓存
        let searchKeys = allKeys.filter { $0.hasPrefix("suggestions_") }
        for key in searchKeys {
            // 这里需要实现单个键的删除方法
            // 暂时使用过期时间为0来标记删除
            cacheService.set(key, value: "", expiry: 0)
        }
    }



    /// 延迟启动同步（在首次搜索时调用）
    func startPeriodicSyncIfNeeded() {
        if !isSyncStarted {
            // 延迟5秒启动，避免影响首次搜索体验
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                self.startPeriodicSync()
            }
        }
    }


}

// MARK: - 本地索引服务扩展

extension LocalIndexService {

    /// 获取性能统计
    /// - Returns: 性能统计信息
    func getPerformanceStats() -> [String: Any] {
        metricsLock.lock()
        defer { metricsLock.unlock() }

        guard !performanceMetrics.isEmpty else {
            return [:]
        }

        let queryTimes = performanceMetrics.map { $0.queryTime }
        let resultCounts = performanceMetrics.map { $0.resultCount }

        let avgQueryTime = queryTimes.reduce(0, +) / Double(queryTimes.count)
        let maxQueryTime = queryTimes.max() ?? 0
        let avgResultCount = Double(resultCounts.reduce(0, +)) / Double(resultCounts.count)

        let cacheHits = performanceMetrics.filter { $0.source == .localIndex }.count
        let localHits = performanceMetrics.filter { $0.source == .localIndex }.count
        let totalQueries = performanceMetrics.count

        return [
            "avgQueryTime": avgQueryTime * 1000, // 转换为毫秒
            "maxQueryTime": maxQueryTime * 1000,
            "avgResultCount": avgResultCount,
            "totalQueries": totalQueries,
            "cacheHitRate": totalQueries > 0 ? Double(cacheHits) / Double(totalQueries) : 0.0,
            "localHitRate": totalQueries > 0 ? Double(localHits) / Double(totalQueries) : 0.0,
            "indexSize": getIndexSize(),
            "lastSyncTime": lastSyncTime?.timeIntervalSince1970 ?? 0
        ]
    }

    /// 预热索引
    /// - Parameter commonWords: 常用单词列表
    func warmupIndex(commonWords: [String]) async {
        let language = LanguageCode.chinese

        for word in commonWords {
            // 预加载常用单词的索引数据到缓存
            _ = await getIndexData(word: word, scaffoldingLanguage: language)
        }

        print("Warmed up index for \(commonWords.count) common words")
    }

    /// 检查索引健康状态
    /// - Returns: 健康状态报告
    func checkIndexHealth() async -> [String: Any] {
        do {
            let learningLanguage = LanguageCode.english
            let scaffoldingLanguage = LanguageCode.chinese
            let stats = try await sqliteManager.getIndexStats(for: learningLanguage, scaffoldingLanguage: scaffoldingLanguage)
            let integrity = try await sqliteManager.checkIntegrity()
            let cacheMetrics = cacheService.getCacheMetrics()

            return [
                "databaseIntegrity": integrity,
                "totalWords": stats["total_words"] ?? 0,
                "lastUpdated": stats["last_updated"] ?? "",
                "cacheHitRate": cacheMetrics.calculatedHitRate,
                "memoryUsage": cacheMetrics.memoryUsage,
                "diskUsage": cacheMetrics.diskUsage,
                "syncStatus": isSyncing ? "syncing" : "idle",
                "lastSyncTime": lastSyncTime?.timeIntervalSince1970 ?? 0
            ]
        } catch {
            return [
                "error": error.localizedDescription,
                "status": "unhealthy"
            ]
        }
    }

    /// 强制重建索引
    /// - Returns: 重建是否成功
    func rebuildIndex() async throws -> Bool {
        do {
            // 1. 清理现有缓存
            cacheService.clearAllCache()

            // 2. 清理旧数据
            try await sqliteManager.cleanupEmptyRecords()

            // 3. 强制同步
            _ = try await syncIndexUpdates(since: 0)

            // 4. 清理性能指标
            // 使用异步安全的方式清理性能指标
            await MainActor.run {
                performanceMetrics.removeAll()
            }

            print("Index rebuild completed successfully")
            return true

        } catch {
            print("Index rebuild failed: \(error)")
            throw error
        }
    }

    /// 获取搜索建议（带性能优化）
    /// - Parameters:
    ///   - query: 搜索查询
    ///   - limit: 结果数量限制
    ///   - timeout: 超时时间
    /// - Returns: 搜索建议列表
    func getOptimizedSuggestions(query: String, limit: Int) async -> [SearchSuggestion] {
        // 简化实现，直接调用searchSuggestions
        return await searchSuggestions(query: query, limit: limit)
    }

    /// 批量预加载单词
    /// - Parameter words: 单词列表
    func preloadWords(_ words: [String]) async {
        let language = LanguageCode.chinese

        await withTaskGroup(of: Void.self) { group in
            for word in words {
                group.addTask {
                    _ = await self.getIndexData(word: word, scaffoldingLanguage: language)
                }
            }
        }

        print("Preloaded \(words.count) words")
    }

    /// 清理过期数据
    /// - Parameter olderThanDays: 保留天数
    func cleanupExpiredData(olderThanDays: Int = 30) async throws {
        do {
            // 清理数据库中的过期数据
            try await sqliteManager.cleanupOldIndexData(olderThanDays: olderThanDays)

            // 清理缓存中的过期数据
            let olderThanSeconds = TimeInterval(olderThanDays * 24 * 3600)
            cacheService.clearCache(olderThan: olderThanSeconds)

            print("Cleaned up data older than \(olderThanDays) days")

        } catch {
            print("Cleanup failed: \(error)")
            throw error
        }
    }
}
