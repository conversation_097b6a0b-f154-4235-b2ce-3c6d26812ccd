//
//  IndexDownloadManager.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-07-28.
//  索引下载管理器 - 多语言对桥接服务
//

import Foundation

// MARK: - 索引下载管理器协议

/// 索引下载管理器协议
/// 支持多语言对下载管理，连接用户设置和下载服务
protocol IndexDownloadManagerProtocol {
    /// 下载当前用户的默认语言对索引
    func downloadIndexForCurrentUser() async throws -> Bool
    
    /// 下载指定语言对的索引
    func downloadIndexForLanguagePair(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> Bool
    
    /// 获取当前用户默认语言对的下载进度
    func getCurrentDownloadProgress() -> IndexDownloadProgress?
    
    /// 获取指定语言对的下载进度
    func getDownloadProgress(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) -> IndexDownloadProgress?
    
    /// 获取当前用户支持的语言对列表
    func getSupportedLanguagePairs() -> [LanguagePairConfig]
}

// MARK: - 索引下载管理器实现

/// 索引下载管理器
/// 多语言对桥接服务，连接用户设置、语言对配置和索引下载服务
class IndexDownloadManager: IndexDownloadManagerProtocol {
    
    // MARK: - 属性
    
    /// 设置服务
    private let settingsService: SettingsServiceProtocol
    
    /// 索引下载服务
    private let indexDownloadService: IndexDownloadServiceProtocol
    
    /// 语言对配置管理器
    private let languagePairConfigManager: LanguagePairConfigManagerProtocol
    
    // MARK: - 初始化
    
    /// 初始化索引下载管理器
    /// - Parameters:
    ///   - settingsService: 设置服务实例
    ///   - indexDownloadService: 索引下载服务实例
    ///   - languagePairConfigManager: 语言对配置管理器实例
    init(
        settingsService: SettingsServiceProtocol,
        indexDownloadService: IndexDownloadServiceProtocol,
        languagePairConfigManager: LanguagePairConfigManagerProtocol
    ) {
        self.settingsService = settingsService
        self.indexDownloadService = indexDownloadService
        self.languagePairConfigManager = languagePairConfigManager
        
        NSLog("📥 IndexDownloadManager: 初始化完成")
    }
    
    // MARK: - 公共方法
    
    /// 下载当前用户的默认语言对索引
    /// 获取用户的默认语言对配置，调用底层下载服务
    func downloadIndexForCurrentUser() async throws -> Bool {
        NSLog("📥 IndexDownloadManager: 开始下载当前用户默认语言对索引")
        
        // 获取默认语言对配置
        let defaultLanguagePair = languagePairConfigManager.getDefaultLanguagePair()
        
        NSLog("📥 IndexDownloadManager: 默认语言对 - \(defaultLanguagePair.displayName)")
        
        // 调用具体语言对下载方法
        return try await downloadIndexForLanguagePair(
            learningLanguage: defaultLanguagePair.learningLanguage,
            scaffoldingLanguage: defaultLanguagePair.scaffoldingLanguage
        )
    }
    
    /// 下载指定语言对的索引
    /// 支持任意语言对组合，为未来多语言扩展做准备
    func downloadIndexForLanguagePair(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> Bool {
        NSLog("📥 IndexDownloadManager: 开始下载语言对索引 - \(learningLanguage.displayName) → \(scaffoldingLanguage.displayName)")
        
        // 验证语言对是否支持
        guard languagePairConfigManager.isLanguagePairSupported(
            learningLanguage: learningLanguage,
            scaffoldingLanguage: scaffoldingLanguage
        ) else {
            let errorMessage = "不支持的语言对: \(learningLanguage.displayName) → \(scaffoldingLanguage.displayName)"
            NSLog("❌ IndexDownloadManager: \(errorMessage)")
            throw IndexDownloadManagerError.unsupportedLanguagePair(errorMessage)
        }
        
        // 调用底层索引下载服务
        do {
            let result = try await indexDownloadService.downloadIndexForLanguagePair(
                learningLang: learningLanguage,
                scaffoldingLang: scaffoldingLanguage
            )
            
            NSLog("✅ IndexDownloadManager: 语言对索引下载完成 - \(learningLanguage.displayName) → \(scaffoldingLanguage.displayName), 结果: \(result)")
            return result
            
        } catch {
            NSLog("❌ IndexDownloadManager: 语言对索引下载失败 - \(learningLanguage.displayName) → \(scaffoldingLanguage.displayName), 错误: \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 获取当前用户默认语言对的下载进度
    func getCurrentDownloadProgress() -> IndexDownloadProgress? {
        let defaultLanguagePair = languagePairConfigManager.getDefaultLanguagePair()
        return getDownloadProgress(
            for: defaultLanguagePair.learningLanguage,
            scaffoldingLanguage: defaultLanguagePair.scaffoldingLanguage
        )
    }
    
    /// 获取指定语言对的下载进度
    func getDownloadProgress(for learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) -> IndexDownloadProgress? {
        let languagePairKey = LanguagePairConfigManager.getLanguagePairKey(
            learningLanguage: learningLanguage,
            scaffoldingLanguage: scaffoldingLanguage
        )
        return indexDownloadService.getDownloadProgress(for: languagePairKey)
    }
    
    /// 获取当前用户支持的语言对列表
    func getSupportedLanguagePairs() -> [LanguagePairConfig] {
        return languagePairConfigManager.getCurrentLanguagePairs()
    }
}

// MARK: - 错误定义

/// 索引下载管理器错误
enum IndexDownloadManagerError: LocalizedError {
    case unsupportedLanguagePair(String)
    case configurationError(String)
    case downloadServiceUnavailable
    
    var errorDescription: String? {
        switch self {
        case .unsupportedLanguagePair(let message):
            return "不支持的语言对: \(message)"
        case .configurationError(let message):
            return "配置错误: \(message)"
        case .downloadServiceUnavailable:
            return "下载服务不可用"
        }
    }
}

// MARK: - 静态便捷方法

extension IndexDownloadManager {
    
    /// 静态便捷方法：下载当前用户默认语言对索引
    /// 使用DIContainer获取服务实例并执行下载
    static func downloadIndexForCurrentUser() async throws -> Bool {
        NSLog("📥 IndexDownloadManager.static: 开始下载当前用户索引")
        
        do {
            // 从DIContainer获取必要的服务实例
            let container = DIContainer.shared
            
            // 初始化搜索相关服务（如果尚未初始化）
            let searchService = try await container.initializeSearchServices()
            let sqliteManager = try await container.createSQLiteManager()
            let localIndexService = container.localIndexService
            
            // 创建索引下载服务
            let indexDownloadService = container.createIndexDownloadService(
                sqliteManager: sqliteManager,
                searchAPIAdapter: container.searchAPIAdapter,
                localIndexService: localIndexService
            )
            
            // 创建语言对配置管理器
            let languagePairConfigManager = LanguagePairConfigManager(
                settingsService: SettingsService.shared
            )
            
            // 创建索引下载管理器
            let downloadManager = IndexDownloadManager(
                settingsService: SettingsService.shared,
                indexDownloadService: indexDownloadService,
                languagePairConfigManager: languagePairConfigManager
            )
            
            // 执行下载
            return try await downloadManager.downloadIndexForCurrentUser()
            
        } catch {
            NSLog("❌ IndexDownloadManager.static: 下载失败 - \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 静态便捷方法：下载指定语言对索引
    /// 支持任意语言对组合
    static func downloadIndexForLanguagePair(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) async throws -> Bool {
        NSLog("📥 IndexDownloadManager.static: 开始下载语言对索引 - \(learningLanguage.displayName) → \(scaffoldingLanguage.displayName)")
        
        do {
            // 从DIContainer获取必要的服务实例
            let container = DIContainer.shared
            
            // 初始化搜索相关服务（如果尚未初始化）
            let searchService = try await container.initializeSearchServices()
            let sqliteManager = try await container.createSQLiteManager()
            let localIndexService = container.localIndexService
            
            // 创建索引下载服务
            let indexDownloadService = container.createIndexDownloadService(
                sqliteManager: sqliteManager,
                searchAPIAdapter: container.searchAPIAdapter,
                localIndexService: localIndexService
            )
            
            // 创建语言对配置管理器
            let languagePairConfigManager = LanguagePairConfigManager(
                settingsService: SettingsService.shared
            )
            
            // 创建索引下载管理器
            let downloadManager = IndexDownloadManager(
                settingsService: SettingsService.shared,
                indexDownloadService: indexDownloadService,
                languagePairConfigManager: languagePairConfigManager
            )
            
            // 执行下载
            return try await downloadManager.downloadIndexForLanguagePair(
                learningLanguage: learningLanguage,
                scaffoldingLanguage: scaffoldingLanguage
            )
            
        } catch {
            NSLog("❌ IndexDownloadManager.static: 语言对下载失败 - \(error.localizedDescription)")
            throw error
        }
    }
    
    /// 静态便捷方法：获取当前下载进度
    static func getCurrentDownloadProgress() -> IndexDownloadProgress? {
        // 注意：这个方法需要已初始化的服务，实际使用时可能需要异步版本
        // 当前提供简化实现，实际项目中可能需要调整
        return nil
    }
}
