//
//  CacheService.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  三级缓存服务 - 内存缓存、磁盘缓存、网络获取的统一管理
//

import Foundation
import UIKit

// MARK: - 缓存配置模型
// CacheConfig 和 CacheMetrics 已在 CacheModels.swift 中定义

/// 缓存项元数据
struct CacheItemMetadata: Codable {
    let key: String
    let createdAt: Date
    let expiresAt: Date
    let size: Int
    let accessCount: Int
    let lastAccessTime: Date
    
    /// 是否已过期
    var isExpired: Bool {
        return Date() > expiresAt
    }
    
    /// 剩余生存时间
    var remainingTTL: TimeInterval {
        return max(0, expiresAt.timeIntervalSinceNow)
    }
}

// MARK: - 缓存服务协议

/// 缓存服务协议定义
protocol CacheServiceProtocol {
    func get<T: Codable>(_ key: String, type: T.Type) -> T?
    func set<T: Codable>(_ key: String, value: T, expiry: TimeInterval?)
    func checkCacheStatus(word: String, language: LanguageCode) -> CacheStatus
    func clearCache(olderThan: TimeInterval)
    func getCacheMetrics() -> CacheMetrics
    func getAllCacheKeys() -> [String]
    func clearAllCache()
}

// MARK: - 缓存服务实现

/// 三级缓存服务实现
/// 核心职责：实现内存缓存、磁盘缓存、网络获取的统一管理，提供智能降级机制
/// 技术特点：支持LRU策略、过期管理、内存压力响应、性能监控
class CacheService: NSObject, CacheServiceProtocol {
    
    // MARK: - 私有属性
    
    /// 缓存配置
    private let config = CacheConfig.default
    
    /// 内存缓存（NSCache实现LRU）
    private let memoryCache = NSCache<NSString, CacheWrapper>()
    
    /// 磁盘缓存目录
    private let diskCacheDirectory: URL
    
    /// 缓存元数据存储
    private var metadata: [String: CacheItemMetadata] = [:]
    
    /// 元数据访问锁
    private let metadataLock = NSLock()
    
    /// 性能指标
    private var metrics = CacheMetrics.empty
    
    /// 清理定时器
    private var cleanupTimer: Timer?
    
    // MARK: - 初始化
    
    /// 初始化缓存服务
    override init() {
        // 创建磁盘缓存目录
        let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        diskCacheDirectory = cacheDir.appendingPathComponent("SensewordCache")

        super.init()

        // 设置内存缓存配置
        memoryCache.totalCostLimit = config.memoryLimit
        memoryCache.delegate = self

        try? FileManager.default.createDirectory(at: diskCacheDirectory, withIntermediateDirectories: true)

        // 加载元数据
        loadMetadata()

        // 启动清理定时器
        startCleanupTimer()

        // 注册内存警告通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    deinit {
        cleanupTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
        saveMetadata()
    }
    
    // MARK: - 公共接口实现
    
    /// 获取缓存数据
    /// - Parameters:
    ///   - key: 缓存键
    ///   - type: 数据类型
    /// - Returns: 缓存的数据，如果不存在或已过期则返回nil
    func get<T: Codable>(_ key: String, type: T.Type) -> T? {
        updateMetrics(totalRequests: 1)
        
        // 1. 检查内存缓存
        if let wrapper = memoryCache.object(forKey: NSString(string: key)) {
            updateAccessTime(for: key)
            return wrapper.value as? T
        }
        
        // 2. 检查磁盘缓存
        if let diskData = getDiskCache(key: key, type: type) {
            // 将磁盘数据加载到内存缓存
            let wrapper = CacheWrapper(value: diskData)
            memoryCache.setObject(wrapper, forKey: NSString(string: key))
            updateAccessTime(for: key)
            return diskData
        }
        
        // 3. 缓存未命中
        updateMetrics(missCount: 1)
        return nil
    }
    
    /// 设置缓存数据
    /// - Parameters:
    ///   - key: 缓存键
    ///   - value: 要缓存的数据
    ///   - expiry: 过期时间（秒），nil表示使用默认TTL
    func set<T: Codable>(_ key: String, value: T, expiry: TimeInterval? = nil) {
        let ttl = expiry ?? config.defaultTTL
        let expiresAt = Date().addingTimeInterval(ttl)
        
        // 1. 存储到内存缓存
        let wrapper = CacheWrapper(value: value)
        memoryCache.setObject(wrapper, forKey: NSString(string: key))
        
        // 2. 存储到磁盘缓存
        setDiskCache(key: key, value: value, expiresAt: expiresAt)
        
        // 3. 更新元数据
        updateMetadata(key: key, expiresAt: expiresAt, size: estimateSize(value))
    }
    
    /// 检查缓存状态
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 缓存状态（索引信息/无缓存）
    /// 注意：不再缓存单词内容，确保数据一致性
    func checkCacheStatus(word: String, language: LanguageCode) -> CacheStatus {
        // 不再检查完整内容缓存，确保始终获取最新数据
        // Cloudflare CDN 已经提供了足够的性能优化

        // 检查索引信息缓存（用于搜索建议等功能）
        let indexKey = "word_index_\(word)_\(language.rawValue)"
        if let indexItem: WordIndexItem = get(indexKey, type: WordIndexItem.self) {
            return .index(indexItem)
        }

        return .none
    }
    
    /// 清理过期缓存
    /// - Parameter olderThan: 清理早于指定时间的缓存
    func clearCache(olderThan: TimeInterval) {
        let cutoffDate = Date().addingTimeInterval(-olderThan)
        
        metadataLock.lock()
        defer { metadataLock.unlock() }
        
        let expiredKeys = metadata.compactMap { (key, meta) -> String? in
            return meta.createdAt < cutoffDate ? key : nil
        }
        
        for key in expiredKeys {
            removeFromAllCaches(key: key)
        }
        
        updateMetrics(lastCleanupTime: Date())
    }
    
    /// 获取缓存性能指标
    /// - Returns: 缓存指标
    func getCacheMetrics() -> CacheMetrics {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        let memoryUsage = calculateMemoryUsage()
        let diskUsage = calculateDiskUsage()

        return CacheMetrics(
            memoryHits: metrics.memoryHits,
            diskHits: metrics.diskHits,
            misses: metrics.misses,
            memoryUsage: memoryUsage,
            diskUsage: diskUsage,
            totalItems: metadata.count,
            timestamp: Date()
        )
    }
}

// MARK: - 缓存包装器

/// 内存缓存包装器
private class CacheWrapper: NSObject {
    let value: Any
    
    init(value: Any) {
        self.value = value
        super.init()
    }
}

// MARK: - NSCacheDelegate

extension CacheService: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: Any) {
        // 内存缓存即将清理对象时的回调
        // 可以在这里记录统计信息或执行清理逻辑
    }
}

// MARK: - 私有实现方法

private extension CacheService {

    /// 从磁盘缓存获取数据
    /// - Parameters:
    ///   - key: 缓存键
    ///   - type: 数据类型
    /// - Returns: 缓存的数据
    func getDiskCache<T: Codable>(key: String, type: T.Type) -> T? {
        let fileURL = diskCacheDirectory.appendingPathComponent("\(key).cache")

        guard FileManager.default.fileExists(atPath: fileURL.path) else {
            return nil
        }

        do {
            let data = try Data(contentsOf: fileURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode(type, from: data)
        } catch {
            // 磁盘缓存损坏，删除文件
            try? FileManager.default.removeItem(at: fileURL)
            removeMetadata(for: key)
            return nil
        }
    }

    /// 设置磁盘缓存
    /// - Parameters:
    ///   - key: 缓存键
    ///   - value: 要缓存的数据
    ///   - expiresAt: 过期时间
    func setDiskCache<T: Codable>(key: String, value: T, expiresAt: Date) {
        let fileURL = diskCacheDirectory.appendingPathComponent("\(key).cache")

        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(value)
            try data.write(to: fileURL)
        } catch {
            // 磁盘写入失败，记录错误但不抛出异常
            print("Failed to write disk cache for key \(key): \(error)")
        }
    }

    /// 从所有缓存中移除数据
    /// - Parameter key: 缓存键
    func removeFromAllCaches(key: String) {
        // 从内存缓存移除
        memoryCache.removeObject(forKey: NSString(string: key))

        // 从磁盘缓存移除
        let fileURL = diskCacheDirectory.appendingPathComponent("\(key).cache")
        try? FileManager.default.removeItem(at: fileURL)

        // 移除元数据
        removeMetadata(for: key)
    }

    /// 更新元数据
    /// - Parameters:
    ///   - key: 缓存键
    ///   - expiresAt: 过期时间
    ///   - size: 数据大小
    func updateMetadata(key: String, expiresAt: Date, size: Int) {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        let now = Date()
        let existingMeta = metadata[key]

        metadata[key] = CacheItemMetadata(
            key: key,
            createdAt: existingMeta?.createdAt ?? now,
            expiresAt: expiresAt,
            size: size,
            accessCount: (existingMeta?.accessCount ?? 0) + 1,
            lastAccessTime: now
        )
    }

    /// 更新访问时间
    /// - Parameter key: 缓存键
    func updateAccessTime(for key: String) {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        guard let meta = metadata[key] else { return }

        metadata[key] = CacheItemMetadata(
            key: meta.key,
            createdAt: meta.createdAt,
            expiresAt: meta.expiresAt,
            size: meta.size,
            accessCount: meta.accessCount + 1,
            lastAccessTime: Date()
        )
    }

    /// 移除元数据
    /// - Parameter key: 缓存键
    func removeMetadata(for key: String) {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        metadata.removeValue(forKey: key)
    }

    /// 更新性能指标
    /// - Parameters:
    ///   - totalRequests: 总请求数增量
    ///   - missCount: 未命中数增量
    ///   - lastCleanupTime: 最后清理时间
    func updateMetrics(totalRequests: Int = 0, missCount: Int = 0, lastCleanupTime: Date? = nil) {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        let memoryUsage = calculateMemoryUsage()
        let diskUsage = calculateDiskUsage()

        metrics = CacheMetrics(
            memoryHits: metrics.memoryHits,
            diskHits: metrics.diskHits,
            misses: metrics.misses + missCount,
            memoryUsage: memoryUsage,
            diskUsage: diskUsage,
            totalItems: metadata.count,
            timestamp: Date()
        )
    }

    /// 计算内存使用量
    /// - Returns: 内存使用量（字节）
    func calculateMemoryUsage() -> Int {
        // NSCache不提供直接的内存使用量查询
        // 这里使用元数据估算
        return metadata.values.reduce(0) { total, meta in
            return total + meta.size
        }
    }

    /// 计算磁盘使用量
    /// - Returns: 磁盘使用量（字节）
    func calculateDiskUsage() -> Int {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheDirectory, includingPropertiesForKeys: [.fileSizeKey])
            return files.reduce(0) { total, fileURL in
                do {
                    let resourceValues = try fileURL.resourceValues(forKeys: [.fileSizeKey])
                    return total + (resourceValues.fileSize ?? 0)
                } catch {
                    return total
                }
            }
        } catch {
            return 0
        }
    }

    /// 估算数据大小
    /// - Parameter value: 数据对象
    /// - Returns: 估算的大小（字节）
    func estimateSize<T: Codable>(_ value: T) -> Int {
        do {
            let data = try JSONEncoder().encode(value)
            return data.count
        } catch {
            // 如果无法编码，返回默认估算值
            return 1024 // 1KB
        }
    }

    /// 加载元数据
    func loadMetadata() {
        let metadataURL = diskCacheDirectory.appendingPathComponent("metadata.json")

        guard FileManager.default.fileExists(atPath: metadataURL.path) else {
            return
        }

        do {
            let data = try Data(contentsOf: metadataURL)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            metadata = try decoder.decode([String: CacheItemMetadata].self, from: data)
        } catch {
            // 元数据损坏，重新开始
            metadata = [:]
        }
    }

    /// 保存元数据
    func saveMetadata() {
        let metadataURL = diskCacheDirectory.appendingPathComponent("metadata.json")

        do {
            metadataLock.lock()
            let currentMetadata = metadata
            metadataLock.unlock()

            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(currentMetadata)
            try data.write(to: metadataURL)
        } catch {
            print("Failed to save cache metadata: \(error)")
        }
    }

    /// 启动清理定时器
    func startCleanupTimer() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: config.cleanupInterval, repeats: true) { [weak self] _ in
            self?.performScheduledCleanup()
        }
    }

    /// 执行定期清理
    func performScheduledCleanup() {
        // 清理过期项
        cleanupExpiredItems()

        // 检查磁盘使用量
        let diskUsage = calculateDiskUsage()
        if diskUsage > config.diskLimit {
            cleanupLRUItems(targetSize: config.diskLimit * 8 / 10) // 清理到80%
        }

        // 保存元数据
        saveMetadata()

        updateMetrics(lastCleanupTime: Date())
    }

    /// 清理过期项
    func cleanupExpiredItems() {
        metadataLock.lock()
        let expiredKeys = metadata.compactMap { (key, meta) -> String? in
            return meta.isExpired ? key : nil
        }
        metadataLock.unlock()

        for key in expiredKeys {
            removeFromAllCaches(key: key)
        }
    }

    /// 清理LRU项目
    /// - Parameter targetSize: 目标大小
    func cleanupLRUItems(targetSize: Int) {
        metadataLock.lock()
        let sortedItems = metadata.values.sorted { $0.lastAccessTime < $1.lastAccessTime }
        metadataLock.unlock()

        var currentSize = calculateDiskUsage()

        for item in sortedItems {
            if currentSize <= targetSize {
                break
            }

            removeFromAllCaches(key: item.key)
            currentSize -= item.size
        }
    }

    /// 处理内存警告
    @objc func handleMemoryWarning() {
        // 清空内存缓存
        memoryCache.removeAllObjects()

        // 清理一些磁盘缓存
        let targetSize = config.diskLimit / 2 // 清理到50%
        cleanupLRUItems(targetSize: targetSize)
    }
}

// MARK: - 缓存服务扩展

extension CacheService {

    /// 预热缓存
    /// - Parameter keys: 要预热的缓存键列表
    func warmupCache(keys: [String]) {
        for key in keys {
            // 从磁盘加载到内存
            if let wrapper = getDiskCache(key: key, type: Data.self) {
                let cacheWrapper = CacheWrapper(value: wrapper)
                memoryCache.setObject(cacheWrapper, forKey: NSString(string: key))
            }
        }
    }

    /// 获取缓存键列表
    /// - Returns: 所有缓存键
    func getAllCacheKeys() -> [String] {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        return Array(metadata.keys)
    }

    /// 检查缓存是否存在
    /// - Parameter key: 缓存键
    /// - Returns: 是否存在
    func exists(_ key: String) -> Bool {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        guard let meta = metadata[key] else { return false }
        return !meta.isExpired
    }

    /// 获取缓存项信息
    /// - Parameter key: 缓存键
    /// - Returns: 缓存项元数据
    func getCacheItemInfo(_ key: String) -> CacheItemMetadata? {
        metadataLock.lock()
        defer { metadataLock.unlock() }

        return metadata[key]
    }

    /// 清空所有缓存
    func clearAllCache() {
        // 清空内存缓存
        memoryCache.removeAllObjects()

        // 清空磁盘缓存
        do {
            let files = try FileManager.default.contentsOfDirectory(at: diskCacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
        } catch {
            print("Failed to clear disk cache: \(error)")
        }

        // 清空元数据
        metadataLock.lock()
        metadata.removeAll()
        metadataLock.unlock()

        // 重置指标
        metrics = CacheMetrics.empty
    }
}
