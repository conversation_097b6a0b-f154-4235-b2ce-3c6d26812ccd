//
//  SearchService.swift
//  SensewordApp
//
//  Created by KDD-023 Implementation on 2025-06-27.
//  搜索业务协调服务 - 实现LPLC原则的统一搜索接口
//

import Foundation

// 导入必要的模型和协议
// 这些类型在其他文件中定义，需要导入才能使用

// MARK: - 并发安全的数据管理器

/// 性能指标管理器 - 使用 Actor 确保并发安全
actor PerformanceMetricsManager {
    private var metrics: [String: Any] = [:]

    func recordMetrics(operation: String, responseTime: TimeInterval, success: Bool) {
        let timestamp = Date().timeIntervalSince1970
        let key = "\(operation)_\(Int(timestamp))"

        metrics[key] = [
            "operation": operation,
            "responseTime": responseTime,
            "success": success,
            "timestamp": timestamp
        ]

        // 保持最近100条记录
        if metrics.count > 100 {
            let sortedKeys = metrics.keys.sorted()
            for oldKey in sortedKeys.prefix(metrics.count - 100) {
                metrics.removeValue(forKey: oldKey)
            }
        }
    }

    func getMetrics() -> [String: Any] {
        return metrics
    }
}

// MARK: - 搜索配置

/// 搜索服务配置
struct SearchServiceConfig {
    let suggestionTimeout: TimeInterval = 0.15    // 建议超时（150ms）
    let contentTimeout: TimeInterval = 5.0        // 内容超时（5秒）
    let maxRetries: Int = 3                       // 最大重试次数
    let retryDelay: TimeInterval = 1.0            // 重试延迟
    let debounceDelay: TimeInterval = 0.3         // 防抖延迟
}

// SearchStrategy 已在 SearchModels.swift 中定义

/// 搜索结果来源
enum SearchResultSource {
    case localCache      // 本地缓存
    case localIndex      // 本地索引
    case networkAPI      // 网络API
    case hybrid          // 混合来源
}

// MARK: - 搜索服务协议

/// 搜索服务协议定义
protocol SearchServiceProtocol {
    func getSuggestions(query: String) async -> SearchSuggestionsResponse
    func getWordContent(word: String, language: LanguageCode) async throws -> WordDefinitionResponse
    func preloadContent(words: [String], language: LanguageCode) async

    func warmupService() async
    func getPerformanceStats() async -> [String: Any]
    func getHealthStatus() async -> [String: Any]
    func runDiagnostics() async -> [String: Any]
    func setSearchStrategy(_ strategy: SearchStrategy)
    func getCurrentStrategy() -> SearchStrategy
    func clearCache() async
    func syncIndexUpdates() async throws -> Int
}

// MARK: - 搜索服务实现

/// 搜索业务协调服务实现
/// 核心职责：协调本地索引、缓存、网络API，实现LPLC原则的统一搜索接口
/// 技术特点：支持防抖、重试、降级策略，提供最佳用户体验的搜索功能
class SearchService: SearchServiceProtocol {
    
    // MARK: - 私有属性
    
    /// 配置
    private let config = SearchServiceConfig()
    
    /// 本地索引服务
    private let localIndexService: LocalIndexServiceProtocol
    
    /// 缓存服务
    private let cacheService: CacheServiceProtocol
    
    /// 搜索API适配器
    private let searchAPIAdapter: SearchAPIAdapterProtocol
    
    /// 单词API适配器
    private let wordAPIAdapter: WordAPIAdapterProtocol
    
    /// 搜索策略
    private var searchStrategy: SearchStrategy = .hybrid
    
    /// 防抖任务
    private var debounceTask: Task<Void, Never>?

    /// 性能指标管理器
    private let metricsManager = PerformanceMetricsManager()
    
    // MARK: - 初始化
    
    /// 初始化搜索服务
    /// - Parameters:
    ///   - localIndexService: 本地索引服务
    ///   - cacheService: 缓存服务
    ///   - searchAPIAdapter: 搜索API适配器
    ///   - wordAPIAdapter: 单词API适配器
    init(localIndexService: LocalIndexServiceProtocol,
         cacheService: CacheServiceProtocol,
         searchAPIAdapter: SearchAPIAdapterProtocol,
         wordAPIAdapter: WordAPIAdapterProtocol) {
        self.localIndexService = localIndexService
        self.cacheService = cacheService
        self.searchAPIAdapter = searchAPIAdapter
        self.wordAPIAdapter = wordAPIAdapter
    }
    
    // MARK: - 公共接口实现
    
    /// 获取搜索建议
    /// - Parameter query: 搜索查询
    /// - Returns: 搜索建议响应
    func getSuggestions(query: String) async -> SearchSuggestionsResponse {
        let startTime = Date()
        
        // 取消之前的防抖任务
        debounceTask?.cancel()
        
        // 验证查询
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return SearchSuggestionsResponse(
                data: [],
                metadata: SearchMetadata(
                    query: query,
                    resultCount: 0,
                    responseTime: 0,
                    source: "localCache"
                )
            )
        }
        
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)

        do {
            // 执行LPLC策略搜索
            let suggestions = try await performLPLCSearch(query: trimmedQuery)
            
            let responseTime = Date().timeIntervalSince(startTime)
            recordPerformanceMetrics(operation: "getSuggestions", responseTime: responseTime, success: true)
            
            return SearchSuggestionsResponse(
                data: suggestions,
                metadata: SearchMetadata(
                    query: trimmedQuery,
                    resultCount: suggestions.count,
                    responseTime: Int(responseTime * 1000),
                    source: "hybrid"
                )
            )
            
        } catch {
            let responseTime = Date().timeIntervalSince(startTime)
            recordPerformanceMetrics(operation: "getSuggestions", responseTime: responseTime, success: false)
            
            print("Get suggestions failed: \(error)")
            
            // 降级到空结果
            return SearchSuggestionsResponse(
                data: [],
                metadata: SearchMetadata(
                    query: trimmedQuery,
                    resultCount: 0,
                    responseTime: Int(responseTime * 1000),
                    source: "error"
                )
            )
        }
    }
    
    /// 获取单词内容
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 单词内容响应
    func getWordContent(word: String, language: LanguageCode) async throws -> WordContentResponse {
        let startTime = Date()

        do {
            // 直接从网络获取最新内容，确保数据一致性
            let content = try await fetchFullContent(word: word, language: language, startTime: startTime)

            let responseTime = Date().timeIntervalSince(startTime)
            recordPerformanceMetrics(operation: "getWordContent", responseTime: responseTime, success: true)

            return content

        } catch {
            let responseTime = Date().timeIntervalSince(startTime)
            recordPerformanceMetrics(operation: "getWordContent", responseTime: responseTime, success: false)

            print("Get word content failed: \(error)")

            // 抛出错误，让调用方处理
            throw error
        }
    }
    
    /// 预加载内容
    /// - Parameters:
    ///   - words: 单词列表
    ///   - language: 语言代码
    func preloadContent(words: [String], language: LanguageCode) async {
        await withTaskGroup(of: Void.self) { group in
            for word in words {
                group.addTask {
                    do {
                        _ = try await self.getWordContent(word: word, language: language)
                    } catch {
                        print("Preload failed for word \(word): \(error)")
                    }
                }
            }
        }
        
        print("Preloaded content for \(words.count) words")
    }
}

// MARK: - 私有实现方法

private extension SearchService {

    /// 执行LPLC策略搜索
    /// - Parameter query: 搜索查询
    /// - Returns: 搜索建议列表
    func performLPLCSearch(query: String) async throws -> [SearchSuggestion] {
        // LPLC策略：Local Priority, Live Cache
        // 1. 本地索引优先（毫秒级响应）
        // 2. 网络API补充（秒级响应）
        // 3. 实时缓存更新

        // 简化为纯本地搜索，因为SearchAPIAdapter不提供搜索建议功能
        // 网络层只负责索引数据同步，搜索建议完全依赖本地索引
        return await localIndexService.getOptimizedSuggestions(query: query, limit: 10)
    }

    /// 获取完整内容
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码（脚手架语言）
    ///   - startTime: 开始时间
    /// - Returns: 单词内容响应
    func fetchFullContent(word: String, language: LanguageCode, startTime: Date) async throws -> WordContentResponse {
        // 使用重试机制获取网络内容
        let content = try await performWithRetry(maxRetries: config.maxRetries) {
            // 使用便捷方法，自动获取默认语言对配置
            try await self.wordAPIAdapter.getWord(word)
        }

        // 不再缓存单词内容，确保始终获取最新数据
        // Cloudflare CDN 已经提供了足够的性能优化

        let responseTime = Date().timeIntervalSince(startTime)
        recordPerformanceMetrics(operation: "fetchFullContent", responseTime: responseTime, success: true)

        // content已经是WordDefinitionResponse类型，直接返回
        return content
    }

    /// 合并搜索结果
    /// - Parameters:
    ///   - local: 本地结果
    ///   - network: 网络结果
    /// - Returns: 合并后的结果
    func mergeSearchResults(local: [SearchSuggestion], network: [SearchSuggestion]) -> [SearchSuggestion] {
        var mergedResults: [SearchSuggestion] = []
        var seenWords: Set<String> = []

        // 1. 优先添加本地结果（响应快，用户体验好）
        for suggestion in local {
            if !seenWords.contains(suggestion.word) {
                mergedResults.append(suggestion)
                seenWords.insert(suggestion.word)
            }
        }

        // 2. 补充网络结果（内容更全面）
        for suggestion in network {
            if !seenWords.contains(suggestion.word) && mergedResults.count < 10 {
                // 标记为有完整内容（来自网络）
                let enhancedSuggestion = SearchSuggestion(
                    word: suggestion.word,
                    definition: suggestion.definition,
                    relevanceScore: suggestion.relevanceScore * 0.9, // 略降权重
                    hasFullContent: true
                )
                mergedResults.append(enhancedSuggestion)
                seenWords.insert(suggestion.word)
            }
        }

        // 3. 按相关性排序
        return mergedResults.sorted { $0.relevanceScore > $1.relevanceScore }
    }

    /// 带重试的操作执行
    /// - Parameters:
    ///   - maxRetries: 最大重试次数
    ///   - operation: 操作闭包
    /// - Returns: 操作结果
    func performWithRetry<T>(maxRetries: Int, operation: @escaping () async throws -> T) async throws -> T {
        var lastError: Error?

        for attempt in 0...maxRetries {
            do {
                return try await operation()
            } catch {
                lastError = error

                if attempt < maxRetries {
                    // 指数退避延迟
                    let delay = config.retryDelay * pow(2.0, Double(attempt))
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        throw lastError ?? SearchError.networkUnavailable
    }

    /// 记录性能指标
    /// - Parameters:
    ///   - operation: 操作名称
    ///   - responseTime: 响应时间
    ///   - success: 是否成功
    func recordPerformanceMetrics(operation: String, responseTime: TimeInterval, success: Bool) {
        Task {
            await metricsManager.recordMetrics(operation: operation, responseTime: responseTime, success: success)
        }
    }
}

// MARK: - 搜索服务扩展

extension SearchService {

    /// 获取性能统计
    /// - Returns: 性能统计信息
    func getPerformanceStats() async -> [String: Any] {
        return await metricsManager.getMetrics()
    }

    /// 设置搜索策略
    /// - Parameter strategy: 搜索策略
    func setSearchStrategy(_ strategy: SearchStrategy) {
        self.searchStrategy = strategy
    }

    /// 获取当前搜索策略
    /// - Returns: 当前搜索策略
    func getCurrentStrategy() -> SearchStrategy {
        return searchStrategy
    }

    /// 预热搜索服务
    func warmupService() async {
        let startTime = Date()
        // 减少预热查询数量，只使用最常见的2个查询进行预热
        let commonQueries = ["hello", "test"]
        NSLog("🔥 SearchService: 开始预热搜索服务，查询数量: \(commonQueries.count)")

        await withTaskGroup(of: Void.self) { group in
            for query in commonQueries {
                group.addTask {
                    let queryStartTime = Date()
                    _ = await self.getSuggestions(query: query)
                    let queryTime = Date().timeIntervalSince(queryStartTime)
                    NSLog("🔥 SearchService: 预热查询 '\(query)' 完成，耗时: \(String(format: "%.3f", queryTime))s")
                }
            }
        }

        let totalTime = Date().timeIntervalSince(startTime)
        NSLog("✅ SearchService: 搜索服务预热完成，总耗时: \(String(format: "%.3f", totalTime))s")
    }

    /// 清理缓存
    func clearCache() async {
        let olderThanHours = 24
        let olderThanSeconds = TimeInterval(olderThanHours * 3600)
        cacheService.clearCache(olderThan: olderThanSeconds)

        print("Cleared cache older than \(olderThanHours) hours")
    }

    /// 同步索引更新
    /// 在用户关闭搜索面板或执行搜索后调用，确保索引数据保持最新
    /// - Returns: 新的同步ID
    func syncIndexUpdates() async throws -> Int {
        return try await localIndexService.syncIndexUpdates(since: nil)
    }

    /// 获取搜索服务健康状态
    /// - Returns: 健康状态报告
    func getHealthStatus() async -> [String: Any] {
        let indexHealth = await localIndexService.checkIndexHealth()
        let cacheMetrics = cacheService.getCacheMetrics()

        let currentMetrics = await metricsManager.getMetrics()

        return [
            "indexHealth": indexHealth,
            "cacheMetrics": [
                "hitRate": cacheMetrics.calculatedHitRate,
                "memoryUsage": cacheMetrics.memoryUsage,
                "diskUsage": cacheMetrics.diskUsage
            ],
            "performanceMetrics": currentMetrics,
            "currentStrategy": searchStrategy,
            "timestamp": Date().timeIntervalSince1970
        ]
    }

    /// 执行搜索服务诊断
    /// - Returns: 诊断报告
    func runDiagnostics() async -> [String: Any] {
        var diagnostics: [String: Any] = [:]

        // 1. 测试本地搜索性能
        let testQuery = "test"
        let startTime = Date()
        let suggestions = await localIndexService.searchSuggestions(query: testQuery, limit: 5)
        let localSearchTime = Date().timeIntervalSince(startTime)

        diagnostics["localSearchPerformance"] = [
            "queryTime": localSearchTime * 1000, // 毫秒
            "resultCount": suggestions.count,
            "status": localSearchTime < 0.1 ? "good" : "slow"
        ]

        // 2. 测试缓存性能
        let cacheTestKey = "diagnostic_test"
        let cacheTestValue = "test_value"

        let cacheStartTime = Date()
        cacheService.set(cacheTestKey, value: cacheTestValue, expiry: 60)
        let cachedValue: String? = cacheService.get(cacheTestKey, type: String.self)
        let cacheTime = Date().timeIntervalSince(cacheStartTime)

        diagnostics["cachePerformance"] = [
            "writeReadTime": cacheTime * 1000, // 毫秒
            "success": cachedValue == cacheTestValue,
            "status": cacheTime < 0.01 ? "good" : "slow"
        ]

        // 3. 获取整体健康状态
        let healthStatus = await getHealthStatus()
        diagnostics["healthStatus"] = healthStatus

        // 4. 系统资源使用情况
        diagnostics["systemResources"] = [
            "memoryPressure": ProcessInfo.processInfo.thermalState.rawValue,
            "availableMemory": ProcessInfo.processInfo.physicalMemory
        ]

        return diagnostics
    }
}
