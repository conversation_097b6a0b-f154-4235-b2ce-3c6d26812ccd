//
//  IndexDownloadService.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-07-28.
//  专门处理按需索引下载的服务
//

import Foundation

// MARK: - 下载进度模型

/// 索引下载进度
public struct IndexDownloadProgress: Codable {
    /// 当前页码
    public let currentPage: Int
    
    /// 总页数
    public let totalPages: Int
    
    /// 已下载单词数量
    public let downloadedWords: Int
    
    /// 总单词数量
    public let totalWords: Int
    
    /// 下载状态
    public let status: IndexDownloadStatus
    
    /// 错误信息
    public let error: String?
    
    /// 下载进度百分比 (0.0 - 1.0)
    public var progress: Double {
        guard totalPages > 0 else { return 0.0 }
        return Double(currentPage) / Double(totalPages)
    }
    
    /// 初始化下载进度
    public init(currentPage: Int, totalPages: Int, downloadedWords: Int, totalWords: Int, status: IndexDownloadStatus, error: String? = nil) {
        self.currentPage = currentPage
        self.totalPages = totalPages
        self.downloadedWords = downloadedWords
        self.totalWords = totalWords
        self.status = status
        self.error = error
    }
}

// MARK: - 索引下载服务协议

/// 索引下载服务协议
protocol IndexDownloadServiceProtocol {
    /// 下载指定语言对的索引
    func downloadIndexForLanguagePair(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Bool
    
    /// 获取下载进度
    func getDownloadProgress(for languagePair: String) -> IndexDownloadProgress?
    
    /// 暂停下载
    func pauseDownload(for languagePair: String) async
    
    /// 恢复下载
    func resumeDownload(for languagePair: String) async throws
    
    /// 取消下载
    func cancelDownload(for languagePair: String) async
    
    /// 获取所有语言对的下载状态
    func getAllDownloadStatuses() -> [String: IndexDownloadProgress]
}

// MARK: - 索引下载服务实现

/// 索引下载服务
/// 专门处理按需索引下载，支持断点续传、进度跟踪、错误恢复
class IndexDownloadService: IndexDownloadServiceProtocol {
    
    // MARK: - 属性
    
    /// SQLite管理器
    private let sqliteManager: SQLiteManagerProtocol
    
    /// 搜索API适配器
    private let searchAPIAdapter: SearchAPIAdapterProtocol
    
    /// 本地索引服务
    private let localIndexService: LocalIndexServiceProtocol
    
    /// 下载进度存储
    private var downloadProgresses: [String: IndexDownloadProgress] = [:]
    
    /// 下载任务存储
    private var downloadTasks: [String: Task<Bool, Error>] = [:]
    
    /// 操作队列
    private let operationQueue = DispatchQueue(label: "com.senseword.indexdownload", qos: .userInitiated)
    
    // MARK: - 初始化
    
    /// 初始化索引下载服务
    init(sqliteManager: SQLiteManagerProtocol, searchAPIAdapter: SearchAPIAdapterProtocol, localIndexService: LocalIndexServiceProtocol) {
        self.sqliteManager = sqliteManager
        self.searchAPIAdapter = searchAPIAdapter
        self.localIndexService = localIndexService
        
        // 从持久化存储恢复下载进度
        loadPersistedDownloadProgresses()
        
        NSLog("📥 IndexDownloadService: 初始化完成")
    }
    
    // MARK: - 公共方法
    
    /// 下载指定语言对的索引
    func downloadIndexForLanguagePair(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Bool {
        let languagePairKey = "\(learningLang.rawValue)-\(scaffoldingLang.rawValue)"
        
        NSLog("📥 IndexDownloadService: 开始下载语言对索引 - \(languagePairKey)")
        
        // 检查是否已有下载任务在进行
        if let existingTask = downloadTasks[languagePairKey] {
            NSLog("⚠️ IndexDownloadService: 语言对 \(languagePairKey) 已有下载任务在进行")
            return try await existingTask.value
        }
        
        // 创建下载任务
        let downloadTask = Task<Bool, Error> {
            return try await performDownload(learningLang: learningLang, scaffoldingLang: scaffoldingLang)
        }
        
        downloadTasks[languagePairKey] = downloadTask
        
        do {
            let result = try await downloadTask.value
            downloadTasks.removeValue(forKey: languagePairKey)
            return result
        } catch {
            downloadTasks.removeValue(forKey: languagePairKey)
            throw error
        }
    }
    
    /// 获取下载进度
    func getDownloadProgress(for languagePair: String) -> IndexDownloadProgress? {
        return downloadProgresses[languagePair]
    }
    
    /// 暂停下载
    func pauseDownload(for languagePair: String) async {
        NSLog("⏸️ IndexDownloadService: 暂停下载 - \(languagePair)")
        
        // 取消当前任务
        downloadTasks[languagePair]?.cancel()
        downloadTasks.removeValue(forKey: languagePair)
        
        // 更新状态为暂停
        if var progress = downloadProgresses[languagePair] {
            downloadProgresses[languagePair] = IndexDownloadProgress(
                currentPage: progress.currentPage,
                totalPages: progress.totalPages,
                downloadedWords: progress.downloadedWords,
                totalWords: progress.totalWords,
                status: .paused,
                error: nil
            )
            persistDownloadProgress(for: languagePair)
        }
    }
    
    /// 恢复下载
    func resumeDownload(for languagePair: String) async throws {
        NSLog("▶️ IndexDownloadService: 恢复下载 - \(languagePair)")
        
        // 解析语言对
        let components = languagePair.split(separator: "-")
        guard components.count == 2,
              let learningLang = LanguageCode(rawValue: String(components[0])),
              let scaffoldingLang = LanguageCode(rawValue: String(components[1])) else {
            throw IndexDownloadError.invalidLanguagePair
        }
        
        // 重新开始下载
        _ = try await downloadIndexForLanguagePair(learningLang: learningLang, scaffoldingLang: scaffoldingLang)
    }
    
    /// 取消下载
    func cancelDownload(for languagePair: String) async {
        NSLog("❌ IndexDownloadService: 取消下载 - \(languagePair)")
        
        // 取消当前任务
        downloadTasks[languagePair]?.cancel()
        downloadTasks.removeValue(forKey: languagePair)
        
        // 移除下载进度
        downloadProgresses.removeValue(forKey: languagePair)
        removePersistedDownloadProgress(for: languagePair)
    }
    
    /// 获取所有语言对的下载状态
    func getAllDownloadStatuses() -> [String: IndexDownloadProgress] {
        return downloadProgresses
    }
    
    // MARK: - 私有方法
    
    /// 执行实际的下载操作
    private func performDownload(learningLang: LanguageCode, scaffoldingLang: LanguageCode) async throws -> Bool {
        let languagePairKey = "\(learningLang.rawValue)-\(scaffoldingLang.rawValue)"
        
        do {
            // 1. 获取本地最大页码
            let localMaxPage = try await sqliteManager.getMaxPage(learningLang: learningLang, scaffoldingLang: scaffoldingLang)
            NSLog("📊 IndexDownloadService: 本地最大页码 - \(localMaxPage)")
            
            // 2. 获取下载范围
            let downloadRange = try await searchAPIAdapter.getDownloadRange(
                learningLanguage: learningLang,
                scaffoldingLanguage: scaffoldingLang,
                localMaxPage: localMaxPage
            )
            
            NSLog("📊 IndexDownloadService: 下载范围 - startPage: \(downloadRange.startPage), endPage: \(downloadRange.endPage)")
            
            // 3. 检查是否需要下载
            if downloadRange.startPage == 0 && downloadRange.endPage == 0 {
                NSLog("✅ IndexDownloadService: 索引已是最新，无需下载")
                updateDownloadProgress(
                    for: languagePairKey,
                    currentPage: 0,
                    totalPages: 0,
                    downloadedWords: 0,
                    totalWords: 0,
                    status: .completed
                )
                return true
            }
            
            // 4. 初始化下载进度
            updateDownloadProgress(
                for: languagePairKey,
                currentPage: downloadRange.startPage - 1,
                totalPages: downloadRange.totalPages,
                downloadedWords: 0,
                totalWords: downloadRange.estimatedItems,
                status: .downloading
            )
            
            // 5. 循环下载页面
            for page in downloadRange.startPage...downloadRange.endPage {
                // 检查任务是否被取消
                try Task.checkCancellation()
                
                NSLog("📥 IndexDownloadService: 下载第 \(page) 页")
                
                // 下载单页数据
                let pageResponse = try await searchAPIAdapter.getWordIndexPage(
                    learningLanguage: learningLang,
                    scaffoldingLanguage: scaffoldingLang,
                    page: page
                )
                
                // 更新本地索引
                try await localIndexService.updateLocalIndex(indexItems: pageResponse.data)
                
                // 更新下载进度
                let downloadedWords = (page - downloadRange.startPage + 1) * 1000
                updateDownloadProgress(
                    for: languagePairKey,
                    currentPage: page,
                    totalPages: downloadRange.totalPages,
                    downloadedWords: downloadedWords,
                    totalWords: downloadRange.estimatedItems,
                    status: .downloading
                )
                
                NSLog("✅ IndexDownloadService: 第 \(page) 页下载完成，已下载 \(downloadedWords) 个单词")
            }
            
            // 6. 下载完成
            updateDownloadProgress(
                for: languagePairKey,
                currentPage: downloadRange.endPage,
                totalPages: downloadRange.totalPages,
                downloadedWords: downloadRange.estimatedItems,
                totalWords: downloadRange.estimatedItems,
                status: .completed
            )
            
            NSLog("🎉 IndexDownloadService: 语言对 \(languagePairKey) 下载完成")
            return true
            
        } catch {
            NSLog("❌ IndexDownloadService: 下载失败 - \(error)")
            
            // 更新错误状态
            if let currentProgress = downloadProgresses[languagePairKey] {
                updateDownloadProgress(
                    for: languagePairKey,
                    currentPage: currentProgress.currentPage,
                    totalPages: currentProgress.totalPages,
                    downloadedWords: currentProgress.downloadedWords,
                    totalWords: currentProgress.totalWords,
                    status: .failed,
                    error: error.localizedDescription
                )
            }
            
            throw error
        }
    }

    /// 更新下载进度
    private func updateDownloadProgress(
        for languagePair: String,
        currentPage: Int,
        totalPages: Int,
        downloadedWords: Int,
        totalWords: Int,
        status: IndexDownloadStatus,
        error: String? = nil
    ) {
        let progress = IndexDownloadProgress(
            currentPage: currentPage,
            totalPages: totalPages,
            downloadedWords: downloadedWords,
            totalWords: totalWords,
            status: status,
            error: error
        )

        downloadProgresses[languagePair] = progress
        persistDownloadProgress(for: languagePair)
    }

    /// 持久化下载进度
    private func persistDownloadProgress(for languagePair: String) {
        guard let progress = downloadProgresses[languagePair] else { return }

        do {
            let data = try JSONEncoder().encode(progress)
            UserDefaults.standard.set(data, forKey: "download_progress_\(languagePair)")
        } catch {
            NSLog("❌ IndexDownloadService: 持久化下载进度失败 - \(error)")
        }
    }

    /// 移除持久化的下载进度
    private func removePersistedDownloadProgress(for languagePair: String) {
        UserDefaults.standard.removeObject(forKey: "download_progress_\(languagePair)")
    }

    /// 加载持久化的下载进度
    private func loadPersistedDownloadProgresses() {
        let userDefaults = UserDefaults.standard
        let keys = userDefaults.dictionaryRepresentation().keys

        for key in keys {
            if key.hasPrefix("download_progress_") {
                let languagePair = String(key.dropFirst("download_progress_".count))

                if let data = userDefaults.data(forKey: key) {
                    do {
                        let progress = try JSONDecoder().decode(IndexDownloadProgress.self, from: data)
                        downloadProgresses[languagePair] = progress
                        NSLog("📥 IndexDownloadService: 恢复下载进度 - \(languagePair): \(progress.status.displayName)")
                    } catch {
                        NSLog("❌ IndexDownloadService: 恢复下载进度失败 - \(languagePair): \(error)")
                        // 移除损坏的数据
                        userDefaults.removeObject(forKey: key)
                    }
                }
            }
        }
    }
}

// MARK: - 错误定义

/// 索引下载错误
enum IndexDownloadError: Error, LocalizedError {
    case invalidLanguagePair
    case downloadInProgress
    case networkError(String)
    case databaseError(String)

    var errorDescription: String? {
        switch self {
        case .invalidLanguagePair:
            return "无效的语言对"
        case .downloadInProgress:
            return "下载正在进行中"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .databaseError(let message):
            return "数据库错误: \(message)"
        }
    }
}
