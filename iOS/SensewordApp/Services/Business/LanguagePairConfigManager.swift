//
//  LanguagePairConfigManager.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-07-28.
//  多语言对配置管理器 - 支持多教学语言、多本地语言架构
//

import Foundation

// MARK: - 语言对配置管理器协议

/// 语言对配置管理器协议
/// 支持多语言对配置管理，为未来扩展做准备
protocol LanguagePairConfigManagerProtocol {
    /// 获取当前用户的活跃语言对列表
    func getCurrentLanguagePairs() -> [LanguagePairConfig]
    
    /// 获取默认语言对
    func getDefaultLanguagePair() -> LanguagePairConfig
    
    /// 检查语言对是否支持
    func isLanguagePairSupported(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) -> Bool
    
    /// 获取支持的学习语言列表
    func getSupportedLearningLanguages() -> [LanguageCode]
    
    /// 获取支持的脚手架语言列表
    func getSupportedScaffoldingLanguages() -> [LanguageCode]
}

// MARK: - 语言对配置数据模型

/// 语言对配置
struct LanguagePairConfig: Codable, Identifiable, Hashable {
    /// 唯一标识符
    let id: String
    
    /// 学习语言（目标语言）
    let learningLanguage: LanguageCode
    
    /// 脚手架语言（母语/解释语言）
    let scaffoldingLanguage: LanguageCode
    
    /// 是否为默认语言对
    let isDefault: Bool
    
    /// 是否启用
    let isEnabled: Bool
    
    /// 初始化语言对配置
    init(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode, isDefault: Bool = false, isEnabled: Bool = true) {
        self.id = "\(learningLanguage.rawValue)-\(scaffoldingLanguage.rawValue)"
        self.learningLanguage = learningLanguage
        self.scaffoldingLanguage = scaffoldingLanguage
        self.isDefault = isDefault
        self.isEnabled = isEnabled
    }
    
    /// 显示名称
    var displayName: String {
        return "\(learningLanguage.displayName) → \(scaffoldingLanguage.displayName)"
    }
}

// MARK: - 语言对配置管理器实现

/// 语言对配置管理器
/// 管理多语言对配置，支持当前默认策略和未来扩展
class LanguagePairConfigManager: LanguagePairConfigManagerProtocol {
    
    // MARK: - 属性
    
    /// 设置服务
    private let settingsService: SettingsServiceProtocol
    
    // MARK: - 初始化
    
    /// 初始化语言对配置管理器
    /// - Parameter settingsService: 设置服务实例
    init(settingsService: SettingsServiceProtocol) {
        self.settingsService = settingsService
        NSLog("🌍 LanguagePairConfigManager: 初始化完成")
    }
    
    // MARK: - 公共方法
    
    /// 获取当前用户的活跃语言对列表
    /// 当前实现：返回基于用户偏好语言的默认语言对
    /// 未来扩展：支持用户配置的多个语言对
    func getCurrentLanguagePairs() -> [LanguagePairConfig] {
        let defaultPair = getDefaultLanguagePair()
        NSLog("🌍 LanguagePairConfigManager: 获取当前语言对 - \(defaultPair.displayName)")
        return [defaultPair]
    }
    
    /// 获取默认语言对
    /// 基于用户设置返回默认语言对：学习英语，用用户偏好语言作为脚手架
    func getDefaultLanguagePair() -> LanguagePairConfig {
        let userPreferredLanguage = settingsService.getCurrentPreferredLanguage()
        let defaultPair = LanguagePairConfig(
            learningLanguage: .english,
            scaffoldingLanguage: userPreferredLanguage,
            isDefault: true,
            isEnabled: true
        )
        
        NSLog("🌍 LanguagePairConfigManager: 默认语言对 - 学习语言: \(defaultPair.learningLanguage.displayName), 脚手架语言: \(defaultPair.scaffoldingLanguage.displayName)")
        return defaultPair
    }
    
    /// 检查语言对是否支持
    /// 当前实现：支持英语作为学习语言的所有组合
    /// 未来扩展：支持更多学习语言
    func isLanguagePairSupported(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) -> Bool {
        let supportedLearningLanguages = getSupportedLearningLanguages()
        let supportedScaffoldingLanguages = getSupportedScaffoldingLanguages()
        
        let isSupported = supportedLearningLanguages.contains(learningLanguage) && 
                         supportedScaffoldingLanguages.contains(scaffoldingLanguage)
        
        NSLog("🌍 LanguagePairConfigManager: 语言对支持检查 - \(learningLanguage.rawValue)-\(scaffoldingLanguage.rawValue): \(isSupported)")
        return isSupported
    }
    
    /// 获取支持的学习语言列表
    /// 当前实现：仅支持英语
    /// 未来扩展：支持西班牙语、法语、德语等
    func getSupportedLearningLanguages() -> [LanguageCode] {
        // Phase 1: 仅支持英语学习
        let supportedLanguages: [LanguageCode] = [.english]
        
        // Phase 2+ 扩展计划:
        // let supportedLanguages: [LanguageCode] = [.english, .spanish, .french, .german, .italian]
        
        return supportedLanguages
    }
    
    /// 获取支持的脚手架语言列表
    /// 当前实现：支持所有可用语言作为脚手架语言
    /// 未来扩展：可根据内容可用性进行筛选
    func getSupportedScaffoldingLanguages() -> [LanguageCode] {
        // 支持所有可用语言作为脚手架语言
        return LanguageCode.allCases
    }
}

// MARK: - 扩展方法

extension LanguagePairConfigManager {
    
    /// 获取语言对的键值
    /// - Parameters:
    ///   - learningLanguage: 学习语言
    ///   - scaffoldingLanguage: 脚手架语言
    /// - Returns: 语言对键值
    static func getLanguagePairKey(learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode) -> String {
        return "\(learningLanguage.rawValue)-\(scaffoldingLanguage.rawValue)"
    }
    
    /// 从键值解析语言对
    /// - Parameter key: 语言对键值
    /// - Returns: 语言对元组，解析失败返回nil
    static func parseLanguagePairKey(_ key: String) -> (learningLanguage: LanguageCode, scaffoldingLanguage: LanguageCode)? {
        let components = key.split(separator: "-")
        guard components.count == 2,
              let learningLang = LanguageCode(rawValue: String(components[0])),
              let scaffoldingLang = LanguageCode(rawValue: String(components[1])) else {
            return nil
        }
        return (learningLanguage: learningLang, scaffoldingLanguage: scaffoldingLang)
    }
}
