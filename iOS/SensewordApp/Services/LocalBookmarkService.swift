//
//  LocalBookmarkService.swift
//  SensewordApp
//
//  Created by KDD Contract Implementation on 2025-07-27.
//  Generated from SenseWord后端无状态化重构任务
//

import Foundation

// MARK: - 本地收藏服务协议

/// 本地收藏服务协议
/// 定义本地收藏功能的核心接口
protocol LocalBookmarkServiceProtocol {
    /// 切换收藏状态
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 操作结果
    func toggleBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult
    
    /// 检查是否已收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 是否已收藏
    func isBookmarked(word: String, language: LanguageCode) async throws -> Bool
    
    /// 获取收藏列表
    /// - Returns: 收藏列表
    func getBookmarks() async throws -> [LocalBookmarkItem]
    
    /// 添加收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 操作结果
    func addBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult
    
    /// 移除收藏
    /// - Parameters:
    ///   - word: 单词
    ///   - language: 语言代码
    /// - Returns: 操作结果
    func removeBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult
    
    /// 批量获取收藏状态
    /// - Parameters:
    ///   - words: 单词列表
    ///   - language: 语言代码
    /// - Returns: 单词到收藏状态的映射
    func batchCheckBookmarkStatus(words: [String], language: LanguageCode) async throws -> [String: Bool]

    /// 获取收藏统计信息
    /// - Returns: 收藏统计信息
    func getBookmarkStats() async throws -> [String: Any]
}

// MARK: - 本地收藏服务实现

/// 本地收藏服务
/// 核心职责：封装SQLite收藏操作，提供线程安全的异步接口
class LocalBookmarkService: LocalBookmarkServiceProtocol {
    
    // MARK: - 私有属性
    
    /// SQLite管理器
    private let sqliteManager: SQLiteManagerProtocol
    
    /// 设备ID（可选，用于向后兼容）
    private let deviceId: String?
    
    /// 操作队列，确保线程安全
    private let operationQueue = DispatchQueue(label: "com.senseword.bookmark.operations", qos: .userInitiated)
    
    // MARK: - 初始化
    
    /// 初始化本地收藏服务
    /// - Parameters:
    ///   - sqliteManager: SQLite管理器
    ///   - deviceId: 设备ID（可选，用于向后兼容）
    init(sqliteManager: SQLiteManagerProtocol, deviceId: String? = nil) {
        self.sqliteManager = sqliteManager
        self.deviceId = deviceId

        if let deviceId = self.deviceId {
            NSLog("🔖 LocalBookmarkService: 初始化完成，设备ID: \(deviceId)")
        } else {
            NSLog("🔖 LocalBookmarkService: 初始化完成，无设备ID（纯本地模式）")
        }
    }
    
    // MARK: - 公共接口实现
    
    /// 切换收藏状态
    func toggleBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        NSLog("🔖 LocalBookmarkService: 切换收藏状态 - 单词: \(word), 语言: \(language.rawValue)")
        
        let isCurrentlyBookmarked = try await isBookmarked(word: word, language: language)
        
        if isCurrentlyBookmarked {
            return try await removeBookmark(word: word, language: language)
        } else {
            return try await addBookmark(word: word, language: language)
        }
    }
    
    /// 检查是否已收藏
    func isBookmarked(word: String, language: LanguageCode) async throws -> Bool {
        let effectiveDeviceId = deviceId ?? "local_device"
        return try await sqliteManager.isBookmarked(word: word, language: language, deviceId: effectiveDeviceId)
    }
    
    /// 获取收藏列表
    func getBookmarks() async throws -> [LocalBookmarkItem] {
        NSLog("🔖 LocalBookmarkService: 获取收藏列表")

        let effectiveDeviceId = deviceId ?? "local_device"
        let dbResults = try await sqliteManager.getBookmarks(deviceId: effectiveDeviceId)
        
        let bookmarks = dbResults.compactMap { LocalBookmarkItem.from(dbResult: $0) }
        
        NSLog("🔖 LocalBookmarkService: 成功获取 \(bookmarks.count) 个收藏")
        return bookmarks
    }
    
    /// 添加收藏
    func addBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        NSLog("🔖 LocalBookmarkService: 添加收藏 - 单词: \(word), 语言: \(language.rawValue)")
        
        do {
            let effectiveDeviceId = deviceId ?? "local_device"
            let success = try await sqliteManager.addBookmark(word: word, language: language, deviceId: effectiveDeviceId)
            
            if success {
                NSLog("🔖 LocalBookmarkService: 收藏添加成功")
                return BookmarkOperationResult.success(
                    message: "收藏添加成功",
                    status: .bookmarked
                )
            } else {
                NSLog("🔖 LocalBookmarkService: 收藏添加失败")
                return BookmarkOperationResult.failure(
                    message: "收藏添加失败",
                    status: .notBookmarked
                )
            }
        } catch {
            NSLog("🔖 LocalBookmarkService: 收藏添加异常 - \(error)")
            return BookmarkOperationResult.failure(
                message: "收藏添加异常: \(error.localizedDescription)",
                status: .notBookmarked
            )
        }
    }
    
    /// 移除收藏
    func removeBookmark(word: String, language: LanguageCode) async throws -> BookmarkOperationResult {
        NSLog("🔖 LocalBookmarkService: 移除收藏 - 单词: \(word), 语言: \(language.rawValue)")
        
        do {
            let effectiveDeviceId = deviceId ?? "local_device"
            let success = try await sqliteManager.removeBookmark(word: word, language: language, deviceId: effectiveDeviceId)
            
            if success {
                NSLog("🔖 LocalBookmarkService: 收藏移除成功")
                return BookmarkOperationResult.success(
                    message: "收藏移除成功",
                    status: .notBookmarked
                )
            } else {
                NSLog("🔖 LocalBookmarkService: 收藏移除失败")
                return BookmarkOperationResult.failure(
                    message: "收藏移除失败",
                    status: .bookmarked
                )
            }
        } catch {
            NSLog("🔖 LocalBookmarkService: 收藏移除异常 - \(error)")
            return BookmarkOperationResult.failure(
                message: "收藏移除异常: \(error.localizedDescription)",
                status: .bookmarked
            )
        }
    }
    
    /// 批量获取收藏状态
    func batchCheckBookmarkStatus(words: [String], language: LanguageCode) async throws -> [String: Bool] {
        NSLog("🔖 LocalBookmarkService: 批量检查收藏状态 - \(words.count) 个单词")
        
        var result: [String: Bool] = [:]
        
        // 并发检查所有单词的收藏状态
        await withTaskGroup(of: (String, Bool).self) { group in
            for word in words {
                group.addTask {
                    do {
                        let isBookmarked = try await self.isBookmarked(word: word, language: language)
                        return (word, isBookmarked)
                    } catch {
                        NSLog("🔖 LocalBookmarkService: 检查单词 \(word) 收藏状态失败 - \(error)")
                        return (word, false)
                    }
                }
            }
            
            for await (word, isBookmarked) in group {
                result[word] = isBookmarked
            }
        }
        
        NSLog("🔖 LocalBookmarkService: 批量检查完成，\(result.count) 个结果")
        return result
    }
}

// MARK: - 扩展功能

extension LocalBookmarkService {
    
    /// 获取收藏统计信息
    /// - Returns: 收藏统计信息
    func getBookmarkStats() async throws -> [String: Any] {
        let bookmarks = try await getBookmarks()
        
        let languageStats = Dictionary(grouping: bookmarks, by: { $0.language })
            .mapValues { $0.count }
        
        return [
            "total_count": bookmarks.count,
            "language_breakdown": languageStats,
            "device_id": deviceId ?? "local_device",
            "last_updated": Date()
        ]
    }
    
    /// 清理设备收藏数据（用于重置或清理）
    /// - Returns: 清理的记录数
    func clearAllBookmarks() async throws -> Int {
        NSLog("🔖 LocalBookmarkService: 清理所有收藏数据")
        
        let bookmarks = try await getBookmarks()
        let count = bookmarks.count
        
        for bookmark in bookmarks {
            _ = try await removeBookmark(word: bookmark.word, language: bookmark.language)
        }
        
        NSLog("🔖 LocalBookmarkService: 清理完成，共清理 \(count) 个收藏")
        return count
    }
}
