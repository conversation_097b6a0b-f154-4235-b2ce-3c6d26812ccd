//
//  AnonymousPurchaseService.swift
//  SensewordApp
//
//  Created by KDD Implementation on 2025-07-27.
//  匿名用户购买和权益管理服务
//

import Foundation
import StoreKit
import Security

/// 匿名用户购买服务协议
protocol AnonymousPurchaseServiceProtocol {
    /// 检查当前权益状态
    func checkEntitlements() async throws -> EntitlementStatus

    /// 恢复购买
    func restorePurchases() async throws -> EntitlementStatus

    /// 获取本地权益缓存
    func getLocalEntitlements() -> [LocalEntitlementRecord]

    /// 清理过期权益
    func cleanupExpiredEntitlements() async
}

/// 匿名用户购买服务实现
class AnonymousPurchaseService: AnonymousPurchaseServiceProtocol {
    
    // MARK: - 单例
    static let shared = AnonymousPurchaseService()
    
    // MARK: - 私有属性
    private let keychainService = "com.senseword.entitlements"
    private let keychainAccount = "local_entitlements"
    
    // MARK: - 初始化
    private init() {
        startTransactionListener()
    }
    
    // MARK: - 公共方法实现
    
    /// 检查当前权益状态
    /// 调用Transaction.currentEntitlements异步查询当前权益，将结果存储到Keychain
    func checkEntitlements() async throws -> EntitlementStatus {
        NSLog("💳 AnonymousPurchaseService: 开始检查权益状态")

        // 时间校验（失败时仅记录日志不返回错误）
        let timeValid = await validateSystemTime()
        if !timeValid {
            NSLog("⚠️ AnonymousPurchaseService: 时间校验失败，但继续执行权益检查")
        }

        var hasProEntitlement = false
        var latestExpiryDate: Date?
        
        // 查询StoreKit 2的当前权益
        for await transaction in Transaction.currentEntitlements {
            let verificationResult = checkVerified(transaction)
            
            switch verificationResult {
            case .unverified(_, let error):
                NSLog("⚠️ AnonymousPurchaseService: 权益验证失败 - \(error)")
                continue
            case .verified(let transaction):
                // 检查交易是否被撤销
                if transaction.revocationDate != nil {
                    NSLog("⚠️ AnonymousPurchaseService: 交易已被撤销 - \(transaction.productID)")
                    continue
                }

                // 检查是否为Pro产品
                if transaction.productID.contains("premium") || transaction.productID.contains("pro") {
                    hasProEntitlement = true
                    
                    // 获取过期时间
                    if let expirationDate = transaction.expirationDate {
                        if latestExpiryDate == nil || expirationDate > latestExpiryDate! {
                            latestExpiryDate = expirationDate
                        }
                    }
                    
                    // 保存到Keychain
                    let record = LocalEntitlementRecord(
                        productId: transaction.productID,
                        purchaseDate: transaction.purchaseDate,
                        expiryDate: transaction.expirationDate
                    )
                    saveEntitlementToKeychain(record)
                    
                    NSLog("✅ AnonymousPurchaseService: 发现Pro权益 - 产品ID: \(transaction.productID)")
                }
            }
        }
        
        let status: EntitlementStatus = hasProEntitlement ? .pro(expiryDate: latestExpiryDate) : .free
        NSLog("📊 AnonymousPurchaseService: 权益检查完成 - 状态: \(status)")
        
        return status
    }
    
    /// 恢复购买
    /// 调用AppStore.sync()同步购买记录，遍历Transaction.currentEntitlements更新本地Keychain缓存
    func restorePurchases() async throws -> EntitlementStatus {
        NSLog("🔄 AnonymousPurchaseService: 开始恢复购买")

        do {
            // 同步App Store购买记录
            try await AppStore.sync()
            NSLog("✅ AnonymousPurchaseService: App Store同步完成")

            // 重新检查权益
            let entitlementStatus = try await checkEntitlements()

            // 统计恢复的权益数量
            let localEntitlements = getLocalEntitlements()
            let restoredCount = localEntitlements.count

            NSLog("✅ AnonymousPurchaseService: 购买恢复完成 - 恢复\(restoredCount)个权益")
            return entitlementStatus

        } catch {
            NSLog("❌ AnonymousPurchaseService: 购买恢复失败 - \(error)")
            throw error
        }
    }
    
    /// 获取本地权益缓存
    func getLocalEntitlements() -> [LocalEntitlementRecord] {
        guard let data = loadEntitlementsFromKeychain() else {
            return []
        }
        
        do {
            let entitlements = try JSONDecoder().decode([LocalEntitlementRecord].self, from: data)
            return entitlements.filter { record in
                // 过滤未过期的权益
                if let expiryDate = record.expiryDate {
                    return expiryDate > Date()
                }
                return true // 永久权益
            }
        } catch {
            NSLog("❌ AnonymousPurchaseService: 解析本地权益失败 - \(error)")
            return []
        }
    }
    
    /// 清理过期权益
    func cleanupExpiredEntitlements() async {
        let currentEntitlements = getLocalEntitlements()
        let validEntitlements = currentEntitlements.filter { record in
            if let expiryDate = record.expiryDate {
                return expiryDate > Date()
            }
            return true
        }
        
        // 如果有权益被清理，更新Keychain
        if validEntitlements.count != currentEntitlements.count {
            do {
                let data = try JSONEncoder().encode(validEntitlements)
                saveDataToKeychain(data)
                NSLog("🧹 AnonymousPurchaseService: 清理了\(currentEntitlements.count - validEntitlements.count)个过期权益")
            } catch {
                NSLog("❌ AnonymousPurchaseService: 清理过期权益失败 - \(error)")
            }
        }
    }
}

// MARK: - 私有方法

extension AnonymousPurchaseService {
    
    /// 验证交易
    private func checkVerified<T>(_ result: VerificationResult<T>) -> VerificationResult<T> {
        return result
    }
    
    /// 保存权益到Keychain
    private func saveEntitlementToKeychain(_ record: LocalEntitlementRecord) {
        var currentEntitlements = getLocalEntitlements()
        
        // 检查是否已存在相同产品ID的权益
        if let existingIndex = currentEntitlements.firstIndex(where: { $0.productId == record.productId }) {
            currentEntitlements[existingIndex] = record
        } else {
            currentEntitlements.append(record)
        }
        
        do {
            let data = try JSONEncoder().encode(currentEntitlements)
            saveDataToKeychain(data)
        } catch {
            NSLog("❌ AnonymousPurchaseService: 保存权益到Keychain失败 - \(error)")
        }
    }
    
    /// 保存数据到Keychain
    private func saveDataToKeychain(_ data: Data) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: keychainAccount,
            kSecValueData as String: data
        ]
        
        // 删除现有项目
        SecItemDelete(query as CFDictionary)
        
        // 添加新项目
        let status = SecItemAdd(query as CFDictionary, nil)
        if status != errSecSuccess {
            NSLog("❌ AnonymousPurchaseService: Keychain保存失败 - 状态码: \(status)")
        }
    }
    
    /// 从Keychain加载权益数据
    private func loadEntitlementsFromKeychain() -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: keychainAccount,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        if status == errSecSuccess {
            return result as? Data
        } else {
            return nil
        }
    }

    /// 启动Transaction.updates监听器
    private func startTransactionListener() {
        Task.detached(priority: .background) {
            for await result in Transaction.updates {
                switch result {
                case .verified(let transaction):
                    await self.updateEntitlementsFromTransaction(transaction)
                case .unverified(_, let error):
                    NSLog("⚠️ AnonymousPurchaseService: Transaction更新验证失败 - \(error)")
                }
            }
        }
    }

    /// 从Transaction更新权益状态
    private func updateEntitlementsFromTransaction(_ transaction: Transaction) async {
        NSLog("🔄 AnonymousPurchaseService: 处理Transaction更新 - 产品ID: \(transaction.productID)")

        // 检查是否为Pro产品
        if transaction.productID.contains("premium") || transaction.productID.contains("pro") {
            let record = LocalEntitlementRecord(
                productId: transaction.productID,
                purchaseDate: transaction.purchaseDate,
                expiryDate: transaction.expirationDate
            )
            saveEntitlementToKeychain(record)
            NSLog("✅ AnonymousPurchaseService: Transaction更新完成 - 产品ID: \(transaction.productID)")
        }
    }

    /// 验证系统时间
    /// 通过CDN时间戳验证防止时钟篡改，网络异常时不阻塞功能
    private func validateSystemTime() async -> Bool {
        do {
            let url = URL(string: "https://cdn.senseword.app/api/v1/config/version.json")!
            let (_, response) = try await URLSession.shared.data(from: url)

            if let httpResponse = response as? HTTPURLResponse,
               let dateString = httpResponse.value(forHTTPHeaderField: "Date") {

                let formatter = DateFormatter()
                formatter.dateFormat = "EEE, dd MMM yyyy HH:mm:ss zzz"
                formatter.locale = Locale(identifier: "en_US_POSIX")

                if let serverTime = formatter.date(from: dateString) {
                    let timeDifference = abs(serverTime.timeIntervalSince(Date()))
                    let isValid = timeDifference < 300 // 允许5分钟误差

                    if !isValid {
                        NSLog("⚠️ AnonymousPurchaseService: 系统时间异常，差异: \(timeDifference)秒")
                    }

                    return isValid
                }
            }
        } catch {
            NSLog("⚠️ AnonymousPurchaseService: 时间校验网络异常，继续执行 - \(error)")
        }

        // 网络异常时不阻塞功能
        return true
    }

    /// 检查订阅状态
    /// 处理Product.SubscriptionInfo.Status的各种订阅状态
    func checkSubscriptionStatus(for productId: String) async throws -> EntitlementStatus {
        NSLog("🔍 AnonymousPurchaseService: 检查订阅状态 - 产品ID: \(productId)")

        do {
            let statuses = try await Product.SubscriptionInfo.status(for: productId)

            for status in statuses {
                guard case .verified(let transaction) = status.transaction,
                      case .verified(_) = status.renewalInfo else {
                    continue
                }

                switch status.state {
                case .subscribed:
                    NSLog("✅ AnonymousPurchaseService: 订阅状态 - 正常订阅")
                    return .pro(expiryDate: transaction.expirationDate)

                case .inGracePeriod:
                    NSLog("⚠️ AnonymousPurchaseService: 订阅状态 - 宽限期")
                    return .pro(expiryDate: transaction.expirationDate)

                case .inBillingRetryPeriod:
                    NSLog("⚠️ AnonymousPurchaseService: 订阅状态 - 账单重试期")
                    return .pro(expiryDate: transaction.expirationDate)

                case .expired:
                    NSLog("❌ AnonymousPurchaseService: 订阅状态 - 已过期")
                    return .free

                case .revoked:
                    NSLog("❌ AnonymousPurchaseService: 订阅状态 - 已撤销")
                    return .free

                default:
                    NSLog("⚠️ AnonymousPurchaseService: 订阅状态 - 未知状态")
                    return .free
                }
            }

            return .free
        } catch {
            NSLog("❌ AnonymousPurchaseService: 订阅状态检查失败 - \(error)")
            throw error
        }
    }

    /// 购买产品
    /// 处理StoreKit 2购买流程的各种结果状态
    func purchaseProduct(_ product: Product) async throws -> EntitlementStatus {
        NSLog("💳 AnonymousPurchaseService: 开始购买产品 - \(product.id)")

        do {
            let result = try await product.purchase()

            switch result {
            case .success(let verificationResult):
                switch verificationResult {
                case .verified(let transaction):
                    NSLog("✅ AnonymousPurchaseService: 购买成功并验证通过 - \(transaction.productID)")

                    // 完成交易
                    await transaction.finish()

                    // 更新本地权益记录
                    let record = LocalEntitlementRecord(
                        productId: transaction.productID,
                        purchaseDate: transaction.purchaseDate,
                        expiryDate: transaction.expirationDate
                    )
                    saveEntitlementToKeychain(record)

                    return .pro(expiryDate: transaction.expirationDate)

                case .unverified(let transaction, let error):
                    NSLog("❌ AnonymousPurchaseService: 购买验证失败 - \(error)")
                    throw PurchaseError.verificationFailed(error.localizedDescription)
                }

            case .pending:
                NSLog("⏳ AnonymousPurchaseService: 购买待处理 (Ask to Buy)")
                throw PurchaseError.pending

            case .userCancelled:
                NSLog("🚫 AnonymousPurchaseService: 用户取消购买")
                throw PurchaseError.userCancelled

            @unknown default:
                NSLog("❓ AnonymousPurchaseService: 未知购买结果")
                throw PurchaseError.unknown
            }
        } catch {
            NSLog("❌ AnonymousPurchaseService: 购买失败 - \(error)")
            throw error
        }
    }

    /// 显示管理订阅界面
    /// Apple审核合规要求：提供订阅管理入口
    func showManageSubscriptions() async {
        NSLog("🔧 AnonymousPurchaseService: 显示管理订阅界面")

        do {
            // 获取当前窗口场景
            guard let windowScene = await MainActor.run(body: {
                UIApplication.shared.connectedScenes
                    .compactMap { $0 as? UIWindowScene }
                    .first
            }) else {
                NSLog("❌ AnonymousPurchaseService: 无法获取窗口场景")
                return
            }

            // 显示Apple官方订阅管理界面
            try await AppStore.showManageSubscriptions(in: windowScene)
            NSLog("✅ AnonymousPurchaseService: 订阅管理界面已显示")
        } catch {
            NSLog("❌ AnonymousPurchaseService: 显示订阅管理界面失败 - \(error)")
        }
    }

    /// 处理未完成的交易
    /// 确保所有交易都正确完成并从队列中移除
    func processUnfinishedTransactions() async {
        NSLog("🔄 AnonymousPurchaseService: 开始处理未完成交易")

        for await result in Transaction.unfinished {
            switch result {
            case .verified(let transaction):
                NSLog("✅ AnonymousPurchaseService: 处理未完成交易 - \(transaction.productID)")

                // 更新本地权益记录
                if transaction.productID.contains("premium") || transaction.productID.contains("pro") {
                    let record = LocalEntitlementRecord(
                        productId: transaction.productID,
                        purchaseDate: transaction.purchaseDate,
                        expiryDate: transaction.expirationDate
                    )
                    saveEntitlementToKeychain(record)
                }

                // 完成交易
                await transaction.finish()
                NSLog("✅ AnonymousPurchaseService: 未完成交易已处理完成 - \(transaction.productID)")

            case .unverified(let transaction, let error):
                NSLog("❌ AnonymousPurchaseService: 未完成交易验证失败 - \(transaction.productID), 错误: \(error)")
                // 对于验证失败的交易，也需要完成以避免重复处理
                await transaction.finish()
            }
        }

        NSLog("✅ AnonymousPurchaseService: 未完成交易处理完成")
    }
}

/// 购买错误类型
enum PurchaseError: Error, LocalizedError {
    case verificationFailed(String)
    case pending
    case userCancelled
    case unknown

    var errorDescription: String? {
        switch self {
        case .verificationFailed(let message):
            return "购买验证失败: \(message)"
        case .pending:
            return "购买正在等待批准"
        case .userCancelled:
            return "用户取消了购买"
        case .unknown:
            return "未知购买错误"
        }
    }
}


