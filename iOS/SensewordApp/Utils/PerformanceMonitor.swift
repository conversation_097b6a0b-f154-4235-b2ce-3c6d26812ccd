import Foundation
import UIKit
import os.log

/// 性能监控工具
/// 用于实时监控应用的CPU、内存、定时器等性能指标
class PerformanceMonitor {
    static let shared = PerformanceMonitor()
    
    private var isMonitoring = false
    private var monitoringTimer: Timer?
    private var startTime: Date?
    
    // 性能指标
    private var cpuUsage: Double = 0
    private var memoryUsage: UInt64 = 0
    private var activeTimers: Int = 0
    private var activeAnimations: Int = 0
    
    // 日志
    private let logger = OSLog(subsystem: "com.senseword.performance", category: "monitor")
    
    private init() {}
    
    // MARK: - 公共接口
    
    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        startTime = Date()
        
        NSLog("🔍 PerformanceMonitor: 开始性能监控")
        
        // 每5秒收集一次性能数据
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.collectPerformanceData()
        }
    }
    
    /// 停止性能监控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        NSLog("🔍 PerformanceMonitor: 停止性能监控")
        
        if let startTime = startTime {
            let duration = Date().timeIntervalSince(startTime)
            NSLog("📊 PerformanceMonitor: 监控时长: %.2f秒", duration)
        }
    }
    
    /// 记录定时器创建
    func recordTimerCreated(name: String) {
        activeTimers += 1
        NSLog("⏰ PerformanceMonitor: 定时器创建 - %@, 当前活跃: %d", name, activeTimers)
    }
    
    /// 记录定时器销毁
    func recordTimerDestroyed(name: String) {
        activeTimers = max(0, activeTimers - 1)
        NSLog("⏰ PerformanceMonitor: 定时器销毁 - %@, 当前活跃: %d", name, activeTimers)
    }
    
    /// 记录动画开始
    func recordAnimationStarted(name: String) {
        activeAnimations += 1
        NSLog("🎨 PerformanceMonitor: 动画开始 - %@, 当前活跃: %d", name, activeAnimations)
    }
    
    /// 记录动画结束
    func recordAnimationEnded(name: String) {
        activeAnimations = max(0, activeAnimations - 1)
        NSLog("🎨 PerformanceMonitor: 动画结束 - %@, 当前活跃: %d", name, activeAnimations)
    }
    
    // MARK: - 私有方法
    
    /// 收集性能数据
    private func collectPerformanceData() {
        cpuUsage = getCurrentCPUUsage()
        memoryUsage = getCurrentMemoryUsage()
        
        NSLog("📊 PerformanceMonitor: CPU: %.1f%%, 内存: %.1fMB, 定时器: %d, 动画: %d", 
              cpuUsage, Double(memoryUsage) / 1024 / 1024, activeTimers, activeAnimations)
        
        // 检查性能警告
        checkPerformanceWarnings()
    }
    
    /// 获取当前CPU使用率
    private func getCurrentCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024 / 1024 // 简化的CPU使用率估算
        }
        return 0
    }
    
    /// 获取当前内存使用量
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return UInt64(info.resident_size)
        }
        return 0
    }
    
    /// 检查性能警告
    private func checkPerformanceWarnings() {
        // CPU使用率警告
        if cpuUsage > 25.0 {
            NSLog("⚠️ PerformanceMonitor: CPU使用率过高: %.1f%%", cpuUsage)
        }
        
        // 内存使用警告
        let memoryMB = Double(memoryUsage) / 1024 / 1024
        if memoryMB > 100.0 {
            NSLog("⚠️ PerformanceMonitor: 内存使用过高: %.1fMB", memoryMB)
        }
        
        // 定时器数量警告
        if activeTimers > 5 {
            NSLog("⚠️ PerformanceMonitor: 活跃定时器过多: %d", activeTimers)
        }
        
        // 动画数量警告
        if activeAnimations > 3 {
            NSLog("⚠️ PerformanceMonitor: 同时进行的动画过多: %d", activeAnimations)
        }
    }
}

// MARK: - 扩展方法

extension Timer {
    /// 创建带性能监控的定时器
    static func scheduledTimerWithMonitoring(
        withTimeInterval interval: TimeInterval,
        repeats: Bool,
        name: String,
        block: @escaping (Timer) -> Void
    ) -> Timer {
        PerformanceMonitor.shared.recordTimerCreated(name: name)
        
        return Timer.scheduledTimer(withTimeInterval: interval, repeats: repeats) { timer in
            block(timer)
            
            if !repeats {
                PerformanceMonitor.shared.recordTimerDestroyed(name: name)
            }
        }
    }
}

extension UIView {
    /// 执行带性能监控的动画
    static func animateWithMonitoring(
        withDuration duration: TimeInterval,
        name: String,
        animations: @escaping () -> Void,
        completion: ((Bool) -> Void)? = nil
    ) {
        PerformanceMonitor.shared.recordAnimationStarted(name: name)
        
        UIView.animate(withDuration: duration, animations: animations) { finished in
            PerformanceMonitor.shared.recordAnimationEnded(name: name)
            completion?(finished)
        }
    }
}
