import SwiftUI
import UIKit

/// 键盘预热组件
/// 
/// 用于在应用启动时预热iOS系统键盘，避免首次激活时的4秒卡顿
/// 基于Apple Developer Forum和Stack Overflow的最佳实践
struct KeyboardPreloader: UIViewRepresentable {
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField(frame: .zero)
        
        // 配置文本框属性，减少键盘负担
        textField.autocorrectionType = .no
        textField.spellCheckingType = .no
        textField.smartQuotesType = .no
        textField.smartDashesType = .no
        textField.smartInsertDeleteType = .no
        textField.keyboardType = .default
        
        // 确保文本框不可见且不可交互
        textField.isHidden = true
        textField.alpha = 0
        textField.isUserInteractionEnabled = false
        
        // 延迟执行键盘预热，避免阻塞应用启动
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            NSLog("⌨️ KeyboardPreloader: 开始键盘预热...")
            let startTime = Date()
            
            // 激活键盘（这会触发系统键盘的初始化）
            textField.becomeFirstResponder()
            
            // 立即取消激活，释放键盘
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                textField.resignFirstResponder()
                let preloadTime = Date().timeIntervalSince(startTime)
                NSLog("✅ KeyboardPreloader: 键盘预热完成，耗时: \(String(format: "%.3f", preloadTime))s")
            }
        }
        
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        // 不需要更新
    }
}

/// 键盘预热管理器
/// 
/// 提供更精细的键盘预热控制
class KeyboardPreloadManager {
    static let shared = KeyboardPreloadManager()
    private var isPreloaded = false
    
    private init() {}
    
    /// 执行键盘预热
    /// - Parameter delay: 延迟时间（秒）
    func preloadKeyboard(delay: TimeInterval = 1.0) {
        guard !isPreloaded else {
            NSLog("⌨️ KeyboardPreloadManager: 键盘已预热，跳过")
            return
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            self.performPreload()
        }
    }
    
    private func performPreload() {
        NSLog("⌨️ KeyboardPreloadManager: 开始键盘预热...")
        let startTime = Date()
        
        // 创建隐藏的文本框
        let textField = UITextField(frame: .zero)
        textField.autocorrectionType = .no
        textField.spellCheckingType = .no
        textField.smartQuotesType = .no
        textField.smartDashesType = .no
        textField.smartInsertDeleteType = .no
        textField.isHidden = true
        textField.alpha = 0
        
        // 添加到当前窗口
        if let window = UIApplication.shared.windows.first {
            window.addSubview(textField)
            
            // 激活键盘
            textField.becomeFirstResponder()
            
            // 短暂延迟后取消激活
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                textField.resignFirstResponder()
                textField.removeFromSuperview()
                
                let preloadTime = Date().timeIntervalSince(startTime)
                NSLog("✅ KeyboardPreloadManager: 键盘预热完成，耗时: \(String(format: "%.3f", preloadTime))s")
                self.isPreloaded = true
            }
        }
    }
}
