//
//  RecommendationArrayManagerTests.swift
//  SensewordAppTests
//
//  Created by KDD Implementation on 2025-06-29.
//  推荐数组管理器测试
//

import XCTest
@testable import SensewordApp

@MainActor
class RecommendationArrayManagerTests: XCTestCase {
    
    var manager: RecommendationArrayManager!
    var mockAPIService: MockAPIService!
    var mockUserPreferencesService: MockUserPreferencesService!
    
    override func setUp() {
        super.setUp()
        
        mockAPIService = MockAPIService()
        mockUserPreferencesService = MockUserPreferencesService()
        
        manager = RecommendationArrayManager(
            apiService: mockAPIService,
            userPreferencesService: mockUserPreferencesService,
            config: .default
        )
    }
    
    override func tearDown() {
        manager = nil
        mockAPIService = nil
        mockUserPreferencesService = nil
        super.tearDown()
    }
    
    // MARK: - 初始化测试
    
    func testInitialization() {
        XCTAssertEqual(manager.state.mode, .daily)
        XCTAssertTrue(manager.state.items.isEmpty)
        XCTAssertEqual(manager.state.currentIndex, 0)
        XCTAssertFalse(manager.state.isLoading)
    }
    
    // MARK: - 每日一词推荐流测试
    
    func testAddDailyWordRecommendations() async throws {
        // Given
        mockAPIService.mockDailyWord = "serendipity"
        mockAPIService.mockRelatedConcepts = ["chance", "discovery", "fortune"]
        
        // When
        try await manager.addDailyWordRecommendations()
        
        // Then
        XCTAssertEqual(manager.state.mode, .daily)
        XCTAssertEqual(manager.state.items.count, 4) // 1个每日一词 + 3个关联概念
        XCTAssertEqual(manager.state.items[0].word, "serendipity")
        XCTAssertEqual(manager.state.items[0].source, .dailyWord)
        XCTAssertEqual(manager.state.items[1].source, .relatedConcept)
        XCTAssertFalse(manager.state.isLoading)
    }
    
    // MARK: - 搜索词推荐流测试
    
    func testAddSearchBasedRecommendations() async throws {
        // Given
        let searchWord = "progressive"
        mockAPIService.mockRelatedConcepts = ["reform", "modernization", "dynamic"]
        
        // When
        try await manager.addSearchBasedRecommendations(from: searchWord)
        
        // Then
        XCTAssertEqual(manager.state.mode, .search)
        XCTAssertEqual(manager.state.items.count, 4) // 1个搜索词 + 3个关联概念
        XCTAssertEqual(manager.state.items[0].word, "progressive")
        XCTAssertEqual(manager.state.items[0].source, .searchWord)
    }
    
    // MARK: - 生词本推荐流测试
    
    func testAddBookmarkBasedRecommendations() async throws {
        // Given
        mockAPIService.mockBookmarkedWords = ["progressive", "ephemeral"]
        mockAPIService.mockRelatedConcepts = ["concept1", "concept2"]
        
        // When
        try await manager.addBookmarkBasedRecommendations()
        
        // Then
        XCTAssertEqual(manager.state.mode, .bookmark)
        XCTAssertGreaterThan(manager.state.items.count, 2) // 至少有生词 + 概念
        
        // 验证生词和概念的交替模式
        let bookmarkItems = manager.state.items.filter { $0.source == .bookmarkWord }
        let conceptItems = manager.state.items.filter { $0.source == .relatedConcept }
        XCTAssertGreaterThan(bookmarkItems.count, 0)
        XCTAssertGreaterThan(conceptItems.count, 0)
    }
    
    // MARK: - 导航控制测试
    
    func testNavigationControl() async throws {
        // Given - 设置推荐数组
        try await manager.addDailyWordRecommendations()
        let initialIndex = manager.state.currentIndex
        
        // When - 移动到下一个
        let nextItem = manager.moveToNext()
        
        // Then
        XCTAssertNotNil(nextItem)
        XCTAssertEqual(manager.state.currentIndex, initialIndex + 1)
        XCTAssertEqual(nextItem?.word, manager.state.currentItem?.word)
        
        // When - 移动到上一个
        let previousItem = manager.moveToPrevious()
        
        // Then
        XCTAssertNotNil(previousItem)
        XCTAssertEqual(manager.state.currentIndex, initialIndex)
    }
    
    func testNavigationBoundaries() async throws {
        // Given - 空数组
        XCTAssertTrue(manager.state.items.isEmpty)
        
        // When - 尝试导航
        let nextItem = manager.moveToNext()
        let previousItem = manager.moveToPrevious()
        
        // Then
        XCTAssertNil(nextItem)
        XCTAssertNil(previousItem)
        XCTAssertEqual(manager.state.currentIndex, 0)
    }
    
    // MARK: - 状态管理测试
    
    func testStateProperties() async throws {
        // Given
        try await manager.addDailyWordRecommendations()
        
        // Then
        XCTAssertTrue(manager.state.hasNext)
        XCTAssertFalse(manager.state.hasPrevious)
        XCTAssertNotNil(manager.state.currentItem)
        XCTAssertNotNil(manager.state.nextItem)
        XCTAssertGreaterThan(manager.state.remainingCount, 0)
        XCTAssertGreaterThan(manager.state.progress, 0.0)
    }
    
    func testReset() async throws {
        // Given - 有推荐数组
        try await manager.addDailyWordRecommendations()
        XCTAssertFalse(manager.state.items.isEmpty)
        
        // When
        manager.reset()
        
        // Then
        XCTAssertEqual(manager.state.mode, .daily)
        XCTAssertTrue(manager.state.items.isEmpty)
        XCTAssertEqual(manager.state.currentIndex, 0)
    }
    
    // MARK: - 配置测试
    
    func testConfigurationChange() {
        // Given
        let newConfig = RecommendationConfig(
            maxArraySize: 20,
            minArraySize: 5,
            autoExtendThreshold: 3,
            cacheSize: 10,
            enableSingleLayerCascade: false
        )
        
        // When
        manager.config = newConfig
        
        // Then
        XCTAssertEqual(manager.config.maxArraySize, 20)
        XCTAssertFalse(manager.config.enableSingleLayerCascade)
    }
    
    // MARK: - 统计信息测试
    
    func testRecommendationStats() async throws {
        // Given
        try await manager.addDailyWordRecommendations()
        
        // When
        let stats = manager.getRecommendationStats()
        
        // Then
        XCTAssertGreaterThan(stats.totalRecommendations, 0)
        XCTAssertTrue(stats.sourceDistribution.keys.contains(.dailyWord))
        XCTAssertTrue(stats.sourceDistribution.keys.contains(.relatedConcept))
    }
    
    // MARK: - 调试信息测试
    
    func testExportDebugInfo() async throws {
        // Given
        try await manager.addDailyWordRecommendations()
        
        // When
        let debugInfo = manager.exportDebugInfo()
        
        // Then
        XCTAssertNotNil(debugInfo["mode"])
        XCTAssertNotNil(debugInfo["itemCount"])
        XCTAssertNotNil(debugInfo["items"])
        
        if let items = debugInfo["items"] as? [[String: Any]] {
            XCTAssertGreaterThan(items.count, 0)
            XCTAssertNotNil(items[0]["word"])
            XCTAssertNotNil(items[0]["source"])
        }
    }
}

// MARK: - Mock Services

class MockAPIService: APIServiceProtocol {
    var mockDailyWord: String = "test"
    var mockRelatedConcepts: [String] = []
    var mockBookmarkedWords: [String] = []
    
    func fetchDailyWord() async throws -> String {
        return mockDailyWord
    }
    
    func fetchWordDefinition(word: String) async throws -> WordDefinitionResponse {
        // 返回模拟的WordDefinitionResponse
        fatalError("Not implemented in mock")
    }
    
    func fetchRelatedConcepts(for word: String) async throws -> [String] {
        return mockRelatedConcepts
    }
    
    func fetchBookmarkedWords() async throws -> [String] {
        return mockBookmarkedWords
    }
}

class MockUserPreferencesService: UserPreferencesServiceProtocol {
    var preferredLanguage: String = "en"
    var recommendationConfig: RecommendationConfig = .default
    
    func saveRecommendationStats(_ stats: RecommendationStats) {
        // Mock implementation
    }
    
    func loadRecommendationStats() -> RecommendationStats? {
        return nil
    }
}
