//
//  JITPreloaderTests.swift
//  SensewordAppTests
//
//  Created by KDD Implementation on 2025-06-29.
//  JIT预加载服务测试
//

import XCTest
@testable import SensewordApp

@MainActor
class JITPreloaderTests: XCTestCase {
    
    var preloader: JITPreloader!

    override func setUp() {
        super.setUp()

        preloader = JITPreloader(
            config: JITPreloaderConfig(
                maxCacheSize: 5,
                maxMemoryUsage: 10 * 1024 * 1024, // 10MB
                preloadTimeout: 10.0,
                enableAudioPreload: true
            )
        )
    }
    
    override func tearDown() {
        preloader.clearCache()
        preloader = nil
        super.tearDown()
    }
    
    // MARK: - 基础功能测试
    
    func testInitialization() {
        XCTAssertFalse(preloader.isPreloading)
        XCTAssertEqual(preloader.preloadProgress, 0.0)
        XCTAssertNil(preloader.lastPreloadedWord)
        XCTAssertEqual(preloader.stats.totalPreloaded, 0)
    }
    
    func testPreloadSingleWord() async {
        // Given
        let word = "serendipity"
        
        // When
        await preloader.preloadWord(word)
        
        // Then
        XCTAssertNotNil(preloader.getCachedWordData(word))
        XCTAssertTrue(preloader.isWordFullyCached(word))
        XCTAssertEqual(preloader.lastPreloadedWord, word)
        XCTAssertEqual(preloader.stats.totalPreloaded, 1)
        XCTAssertFalse(preloader.isPreloading)
    }
    
    func testPreloadMultipleWords() async {
        // Given
        let words = ["progressive", "ephemeral", "ubiquitous"]
        
        // When
        await preloader.preloadWords(words)
        
        // Then
        for word in words {
            XCTAssertNotNil(preloader.getCachedWordData(word))
            XCTAssertTrue(preloader.isWordFullyCached(word))
        }
        XCTAssertEqual(preloader.stats.totalPreloaded, words.count)
    }
    
    // MARK: - 缓存管理测试
    
    func testCacheHitForAlreadyLoadedWord() async {
        // Given
        let word = "serendipity"
        await preloader.preloadWord(word)
        let initialPreloadCount = preloader.stats.totalPreloaded
        
        // When - 再次预加载同一个单词
        await preloader.preloadWord(word)
        
        // Then - 应该直接从缓存返回，不增加预加载计数
        XCTAssertEqual(preloader.stats.totalPreloaded, initialPreloadCount)
        XCTAssertTrue(preloader.isWordFullyCached(word))
    }
    
    func testCacheSizeLimit() async {
        // Given - 配置最大缓存为5个单词
        let words = ["word1", "word2", "word3", "word4", "word5", "word6", "word7"]
        
        // When - 预加载超过限制的单词数量
        await preloader.preloadWords(words)
        
        // Then - 缓存应该被限制在最大大小
        XCTAssertLessThanOrEqual(preloader.stats.cachedWordsCount, 5)
    }
    
    func testClearCache() async {
        // Given
        await preloader.preloadWord("test")
        XCTAssertTrue(preloader.isWordFullyCached("test"))
        
        // When
        preloader.clearCache()
        
        // Then
        XCTAssertFalse(preloader.isWordFullyCached("test"))
        XCTAssertNil(preloader.getCachedWordData("test"))
        XCTAssertEqual(preloader.stats.cachedWordsCount, 0)
    }
    
    // MARK: - 音频缓存测试
    
    func testAudioURLCaching() async {
        // Given
        let word = "serendipity"

        // When
        await preloader.preloadWord(word)

        // Then
        for audioType in PreloadAudioType.allCases {
            let audioURL = preloader.getCachedAudioURL(word, type: audioType)
            // 注意：由于使用真实API，可能不是所有音频类型都有URL
            // 这里我们只检查如果有URL，它应该是有效的
            if let url = audioURL {
                XCTAssertFalse(url.isEmpty, "音频URL不应为空")
                XCTAssertTrue(url.hasPrefix("http"), "音频URL应该是有效的HTTP URL")
            }
        }
    }
    
    func testIsWordFullyCached() async {
        // Given
        let word = "progressive"
        
        // When - 预加载前
        XCTAssertFalse(preloader.isWordFullyCached(word))
        
        // When - 预加载后
        await preloader.preloadWord(word)
        
        // Then
        XCTAssertTrue(preloader.isWordFullyCached(word))
    }
    
    // MARK: - 状态管理测试
    
    func testPreloadingStateChanges() async {
        // Given
        let word = "ephemeral"
        
        // When - 开始预加载
        let preloadTask = Task {
            await preloader.preloadWord(word)
        }
        
        // 等待一小段时间让预加载开始
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        // Then - 预加载过程中状态应该正确
        // 注意：由于异步操作的时序问题，这个测试可能不稳定
        // 在实际项目中可能需要更复杂的同步机制
        
        await preloadTask.value
        
        // 预加载完成后状态应该重置
        XCTAssertFalse(preloader.isPreloading)
        XCTAssertEqual(preloader.preloadProgress, 0.0)
    }
    
    func testPreloadProgress() async {
        // Given
        let word = "ubiquitous"
        
        // When
        await preloader.preloadWord(word)
        
        // Then - 预加载完成后进度应该重置
        XCTAssertEqual(preloader.preloadProgress, 0.0)
        XCTAssertFalse(preloader.isPreloading)
    }
    
    // MARK: - 统计信息测试
    
    func testStatsTracking() async {
        // Given
        let words = ["word1", "word2"]
        
        // When
        await preloader.preloadWords(words)
        
        // Then
        XCTAssertEqual(preloader.stats.totalPreloaded, words.count)
        XCTAssertGreaterThan(preloader.stats.cachedAudioFilesCount, 0)
        XCTAssertGreaterThan(preloader.stats.cacheMemoryUsage, 0)
        XCTAssertNotNil(preloader.stats.lastPreloadTime)
    }
    
    func testSuccessRate() async {
        // Given
        await preloader.preloadWords(["word1", "word2", "word3"])
        
        // When
        let successRate = preloader.getCacheHitRate()
        
        // Then
        XCTAssertEqual(successRate, 1.0) // 100% 成功率（使用Mock服务）
    }
    
    // MARK: - 调试信息测试
    
    func testDebugInfo() async {
        // Given
        await preloader.preloadWord("test")
        
        // When
        let debugInfo = preloader.getDebugInfo()
        
        // Then
        XCTAssertNotNil(debugInfo["isPreloading"])
        XCTAssertNotNil(debugInfo["preloadProgress"])
        XCTAssertNotNil(debugInfo["cachedWords"])
        XCTAssertNotNil(debugInfo["stats"])
        
        if let cachedWords = debugInfo["cachedWords"] as? [String] {
            XCTAssertTrue(cachedWords.contains("test"))
        }
    }
    
    // MARK: - 错误处理测试
    
    func testConcurrentPreloadRequests() async {
        // Given
        let word = "concurrent"
        
        // When - 同时发起多个相同单词的预加载请求
        async let task1 = preloader.preloadWord(word)
        async let task2 = preloader.preloadWord(word)
        async let task3 = preloader.preloadWord(word)
        
        await task1
        await task2
        await task3
        
        // Then - 应该只预加载一次
        XCTAssertEqual(preloader.stats.totalPreloaded, 1)
        XCTAssertTrue(preloader.isWordFullyCached(word))
    }
    
    // MARK: - 配置测试
    
    func testCustomConfiguration() {
        // Given
        let customConfig = JITPreloaderConfig(
            maxCacheSize: 3,
            maxMemoryUsage: 5 * 1024 * 1024,
            preloadTimeout: 5.0,
            enableAudioPreload: false
        )
        
        // When
        let customPreloader = JITPreloader(
            config: customConfig,
            apiService: mockAPIService,
            audioService: mockAudioService
        )
        
        // Then
        XCTAssertNotNil(customPreloader)
        // 配置验证需要通过实际使用来测试
    }
}
