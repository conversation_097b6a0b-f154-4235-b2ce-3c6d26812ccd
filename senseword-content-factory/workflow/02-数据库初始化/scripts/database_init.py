#!/usr/bin/env python3
"""
SenseWord Content Factory - 数据库初始化脚本 v4.0
完整的数据库结构初始化，包含所有表、索引和新增的处理队列表

功能：
1. 创建完整的 v4.0 数据库结构
2. 创建 words_for_publish 表（主要内容表）
3. 创建 tts_assets 表（TTS资产管理表）
4. 创建 word_processing_queue 表（新增的看板式状态管理表）
5. 创建所有必要的索引
6. 验证数据库结构完整性

使用方法：
    python database_init.py --db-path /path/to/senseword_content_v4.db [--force]

参数：
    --db-path: 数据库文件路径
    --force: 强制重新创建（会删除现有数据库）
    --verify-only: 仅验证现有数据库结构，不创建新表
"""

import sqlite3
import argparse
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class DatabaseInitializer:
    """SenseWord v4.0 数据库初始化器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        
    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
            print(f"✅ 成功连接到数据库: {self.db_path}")
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")
    
    def create_words_for_publish_table(self) -> bool:
        """创建 words_for_publish 主表"""
        try:
            cursor = self.connection.cursor()
            
            # 创建主表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS "words_for_publish" (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT NOT NULL,
                    learningLanguage TEXT NOT NULL DEFAULT 'en',
                    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
                    contentJson TEXT NOT NULL,
                    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
                    contentVersion TEXT DEFAULT 'v1.0',
                    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    partsOfSpeech TEXT,
                    ttsStatus TEXT DEFAULT 'pending',
                    aiAuditScore REAL,
                    aiAuditShouldRegenerate INTEGER DEFAULT 0,
                    aiAuditComment TEXT,
                    frequency TEXT,
                    auditStatus TEXT DEFAULT 'pending_review',
                    aiAuditShouldRemove INTEGER DEFAULT 0,
                    culturalRiskRegions TEXT DEFAULT '[]',
                    ttsHashList TEXT,
                    audioGenerated INTEGER DEFAULT 0,
                    
                    -- 唯一约束：确保同一语言对下的单词不重复
                    UNIQUE(word, learningLanguage, scaffoldingLanguage)
                )
            """)
            
            print("✅ words_for_publish 表创建成功")
            return True
            
        except Exception as e:
            print(f"❌ words_for_publish 表创建失败: {e}")
            return False
    
    def create_tts_assets_table(self) -> bool:
        """创建 tts_assets TTS资产管理表"""
        try:
            cursor = self.connection.cursor()
            
            # 创建TTS资产表（与现有结构保持一致）
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tts_assets (
                    ttsId TEXT PRIMARY KEY,                 -- 24位哈希作为主键

                    -- 文本信息（支持调试和复用）
                    originalText TEXT NOT NULL,             -- 原始文本
                    normalizedText TEXT NOT NULL,           -- 标准化文本
                    textHash TEXT NOT NULL,                 -- 文本哈希（与ttsId相同）

                    -- TTS处理信息
                    textToSpeak TEXT NOT NULL,              -- 实际用于TTS的文本
                    learningLanguage TEXT NOT NULL,         -- 学习语言
                    ttsType TEXT NOT NULL,                  -- TTS类型：phonetic_bre, phonetic_name, example_sentence, phrase_breakdown

                    -- 音频资产信息
                    status TEXT DEFAULT 'pending',          -- pending, processing, completed, failed
                    audioUrl TEXT,                          -- 音频文件URL
                    audioDuration REAL,                     -- 音频时长（秒）

                    -- 时间戳
                    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            print("✅ tts_assets 表创建成功")
            return True
            
        except Exception as e:
            print(f"❌ tts_assets 表创建失败: {e}")
            return False
    
    def create_word_processing_queue_table(self) -> bool:
        """创建 word_processing_queue 看板式状态管理表"""
        try:
            cursor = self.connection.cursor()
            
            # 创建处理队列表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS word_processing_queue (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT NOT NULL,
                    learningLanguage TEXT NOT NULL DEFAULT 'en',
                    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',

                    -- 核心处理流程状态 (看板列)
                    contentGenerated BOOLEAN NOT NULL DEFAULT FALSE,
                    contentAiReviewed BOOLEAN NOT NULL DEFAULT FALSE,
                    ttsIdGenerated BOOLEAN NOT NULL DEFAULT FALSE,
                    audioGenerated BOOLEAN NOT NULL DEFAULT FALSE,
                    readyForPublish BOOLEAN NOT NULL DEFAULT FALSE,

                    -- 基本信息
                    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                    -- 唯一约束：确保同一语言对下的单词不重复
                    UNIQUE(word, learningLanguage, scaffoldingLanguage)
                )
            """)
            
            print("✅ word_processing_queue 表创建成功")
            return True
            
        except Exception as e:
            print(f"❌ word_processing_queue 表创建失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """创建所有必要的索引"""
        try:
            cursor = self.connection.cursor()
            
            # words_for_publish 表索引
            indexes_words = [
                "CREATE INDEX IF NOT EXISTS idx_words_audioGenerated ON words_for_publish(audioGenerated)",
                "CREATE INDEX IF NOT EXISTS idx_words_ttsHashList ON words_for_publish(ttsHashList)",
                "CREATE INDEX IF NOT EXISTS idx_words_language_pair ON words_for_publish(learningLanguage, scaffoldingLanguage)",
                "CREATE INDEX IF NOT EXISTS idx_words_publish_status ON words_for_publish(publishStatus)",
                "CREATE INDEX IF NOT EXISTS idx_words_audit_status ON words_for_publish(auditStatus)",
                "CREATE INDEX IF NOT EXISTS idx_words_tts_status ON words_for_publish(ttsStatus)"
            ]
            
            # tts_assets 表索引
            indexes_tts = [
                "CREATE INDEX IF NOT EXISTS idx_tts_textHash ON tts_assets(textHash)",
                "CREATE INDEX IF NOT EXISTS idx_tts_languageHash ON tts_assets(learningLanguage, textHash)",
                "CREATE INDEX IF NOT EXISTS idx_tts_status ON tts_assets(status)",
                "CREATE INDEX IF NOT EXISTS idx_tts_type ON tts_assets(ttsType)"
            ]
            
            # word_processing_queue 表索引
            indexes_queue = [
                "CREATE INDEX IF NOT EXISTS idx_queue_language_pair ON word_processing_queue(learningLanguage, scaffoldingLanguage)",
                "CREATE INDEX IF NOT EXISTS idx_queue_content_generated ON word_processing_queue(contentGenerated)",
                "CREATE INDEX IF NOT EXISTS idx_queue_content_reviewed ON word_processing_queue(contentAiReviewed)",
                "CREATE INDEX IF NOT EXISTS idx_queue_tts_generated ON word_processing_queue(ttsIdGenerated)",
                "CREATE INDEX IF NOT EXISTS idx_queue_audio_generated ON word_processing_queue(audioGenerated)",
                "CREATE INDEX IF NOT EXISTS idx_queue_ready_publish ON word_processing_queue(readyForPublish)",
                "CREATE INDEX IF NOT EXISTS idx_queue_updated_at ON word_processing_queue(updatedAt)"
            ]
            
            # 执行所有索引创建
            all_indexes = indexes_words + indexes_tts + indexes_queue
            
            for index_sql in all_indexes:
                cursor.execute(index_sql)
            
            print(f"✅ 成功创建 {len(all_indexes)} 个索引")
            return True
            
        except Exception as e:
            print(f"❌ 索引创建失败: {e}")
            return False
    
    def verify_database_structure(self) -> Dict[str, bool]:
        """验证数据库结构完整性"""
        verification_results = {}
        
        try:
            cursor = self.connection.cursor()
            
            # 检查表是否存在
            tables_to_check = ['words_for_publish', 'tts_assets', 'word_processing_queue']
            
            for table_name in tables_to_check:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                
                result = cursor.fetchone()
                verification_results[f"table_{table_name}"] = result is not None
                
                if result:
                    print(f"✅ 表 {table_name} 存在")
                else:
                    print(f"❌ 表 {table_name} 不存在")
            
            # 检查关键索引是否存在
            key_indexes = [
                'idx_words_audioGenerated',
                'idx_tts_status',
                'idx_queue_language_pair'
            ]
            
            for index_name in key_indexes:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name=?
                """, (index_name,))
                
                result = cursor.fetchone()
                verification_results[f"index_{index_name}"] = result is not None
                
                if result:
                    print(f"✅ 索引 {index_name} 存在")
                else:
                    print(f"❌ 索引 {index_name} 不存在")
            
            return verification_results
            
        except Exception as e:
            print(f"❌ 数据库结构验证失败: {e}")
            return verification_results

    def get_database_statistics(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        stats = {}

        try:
            cursor = self.connection.cursor()

            # 统计各表的记录数
            tables = ['words_for_publish', 'tts_assets', 'word_processing_queue']

            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    stats[f"{table}_count"] = count
                    print(f"📊 {table}: {count:,} 条记录")
                except:
                    stats[f"{table}_count"] = 0
                    print(f"📊 {table}: 表不存在或为空")

            # 统计处理队列的状态分布
            try:
                cursor.execute("""
                    SELECT
                        SUM(CASE WHEN contentGenerated = 1 THEN 1 ELSE 0 END) as content_generated,
                        SUM(CASE WHEN contentAiReviewed = 1 THEN 1 ELSE 0 END) as content_reviewed,
                        SUM(CASE WHEN ttsIdGenerated = 1 THEN 1 ELSE 0 END) as tts_generated,
                        SUM(CASE WHEN audioGenerated = 1 THEN 1 ELSE 0 END) as audio_generated,
                        SUM(CASE WHEN readyForPublish = 1 THEN 1 ELSE 0 END) as ready_publish
                    FROM word_processing_queue
                """)

                result = cursor.fetchone()
                if result:
                    stats.update({
                        'content_generated': result[0] or 0,
                        'content_reviewed': result[1] or 0,
                        'tts_generated': result[2] or 0,
                        'audio_generated': result[3] or 0,
                        'ready_publish': result[4] or 0
                    })

                    print("📊 处理队列状态分布:")
                    print(f"   - 内容已生成: {stats['content_generated']:,}")
                    print(f"   - 内容已审核: {stats['content_reviewed']:,}")
                    print(f"   - TTS已生成: {stats['tts_generated']:,}")
                    print(f"   - 音频已生成: {stats['audio_generated']:,}")
                    print(f"   - 准备发布: {stats['ready_publish']:,}")
            except:
                print("📊 处理队列状态分布: 表不存在或为空")

            return stats

        except Exception as e:
            print(f"❌ 获取数据库统计信息失败: {e}")
            return stats

    def initialize_database(self, force: bool = False) -> bool:
        """完整的数据库初始化流程"""
        print("🚀 开始数据库初始化...")
        print(f"📍 数据库路径: {self.db_path}")

        # 检查是否需要强制重新创建
        if force and os.path.exists(self.db_path):
            print("⚠️  强制模式：删除现有数据库")
            os.remove(self.db_path)

        # 建立连接
        self.connect()

        try:
            # 创建所有表
            success_flags = []

            print("\n📋 创建数据库表...")
            success_flags.append(self.create_words_for_publish_table())
            success_flags.append(self.create_tts_assets_table())
            success_flags.append(self.create_word_processing_queue_table())

            print("\n🔍 创建索引...")
            success_flags.append(self.create_indexes())

            # 提交事务
            self.connection.commit()

            print("\n✅ 验证数据库结构...")
            verification_results = self.verify_database_structure()

            print("\n📊 数据库统计信息...")
            self.get_database_statistics()

            # 检查是否所有操作都成功
            all_success = all(success_flags)
            all_verified = all(verification_results.values())

            if all_success and all_verified:
                print("\n🎉 数据库初始化完成！")
                print("✅ 所有表和索引创建成功")
                print("✅ 数据库结构验证通过")
                return True
            else:
                print("\n⚠️  数据库初始化部分成功")
                print("❌ 部分表或索引创建失败，请检查错误信息")
                return False

        except Exception as e:
            print(f"\n❌ 数据库初始化失败: {e}")
            self.connection.rollback()
            return False
        finally:
            self.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord Content Factory - 数据库初始化脚本 v4.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 初始化新数据库
  python database_init.py --db-path ./senseword_content_v4.db

  # 强制重新创建数据库（会删除现有数据）
  python database_init.py --db-path ./senseword_content_v4.db --force

  # 仅验证现有数据库结构
  python database_init.py --db-path ./senseword_content_v4.db --verify-only
        """
    )

    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )

    parser.add_argument(
        '--force',
        action='store_true',
        help='强制重新创建数据库（会删除现有数据）'
    )

    parser.add_argument(
        '--verify-only',
        action='store_true',
        help='仅验证现有数据库结构，不创建新表'
    )

    args = parser.parse_args()

    # 验证数据库路径
    db_dir = os.path.dirname(args.db_path)
    if db_dir and not os.path.exists(db_dir):
        print(f"❌ 数据库目录不存在: {db_dir}")
        sys.exit(1)

    # 创建初始化器
    initializer = DatabaseInitializer(args.db_path)

    try:
        if args.verify_only:
            # 仅验证模式
            print("🔍 验证模式：检查现有数据库结构...")
            if not os.path.exists(args.db_path):
                print(f"❌ 数据库文件不存在: {args.db_path}")
                sys.exit(1)

            initializer.connect()
            verification_results = initializer.verify_database_structure()
            initializer.get_database_statistics()
            initializer.close()

            if all(verification_results.values()):
                print("\n✅ 数据库结构验证通过")
                sys.exit(0)
            else:
                print("\n❌ 数据库结构验证失败")
                sys.exit(1)
        else:
            # 初始化模式
            success = initializer.initialize_database(force=args.force)

            if success:
                print(f"\n🎉 数据库初始化成功: {args.db_path}")
                sys.exit(0)
            else:
                print(f"\n❌ 数据库初始化失败: {args.db_path}")
                sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
