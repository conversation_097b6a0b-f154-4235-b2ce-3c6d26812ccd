# SenseWord 数据库初始化脚本 v4.0

## 概述

这是 SenseWord Content Factory 的完整数据库初始化脚本，用于创建和管理 v4.0 版本的数据库结构。

## 功能特性

### 🗄️ 完整的数据库结构
- **words_for_publish**: 主要内容表，存储单词和相关内容
- **tts_assets**: TTS资产管理表，管理音频文件和哈希
- **word_processing_queue**: 看板式状态管理表（新增）

### 🔍 索引优化
- 为所有关键字段创建性能优化索引
- 支持语言对查询、状态筛选等高频操作
- 复合索引优化多条件查询性能

### ✅ 完整性验证
- 自动验证表结构完整性
- 检查索引是否正确创建
- 提供详细的统计信息

## 使用方法

### 基本用法

```bash
# 初始化新数据库
python database_init.py --db-path /path/to/senseword_content_v4.db

# 强制重新创建数据库（会删除现有数据）
python database_init.py --db-path /path/to/senseword_content_v4.db --force

# 仅验证现有数据库结构
python database_init.py --db-path /path/to/senseword_content_v4.db --verify-only
```

### 实际使用示例

```bash
# 为英语-中文语言对创建数据库
python database_init.py --db-path ../../content-assets/01_en/01_zh/sqlite/senseword_content_v4.db

# 验证现有数据库结构
python database_init.py --db-path ../../content-assets/01_en/01_zh/sqlite/senseword_content_v4.db --verify-only
```

## 数据库结构详解

### words_for_publish 表
主要内容表，存储单词的完整信息：

```sql
CREATE TABLE words_for_publish (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    contentJson TEXT NOT NULL,
    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
    contentVersion TEXT DEFAULT 'v1.0',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    partsOfSpeech TEXT,
    ttsStatus TEXT DEFAULT 'pending',
    aiAuditScore REAL,
    aiAuditShouldRegenerate INTEGER DEFAULT 0,
    aiAuditComment TEXT,
    frequency TEXT,
    auditStatus TEXT DEFAULT 'pending_review',
    aiAuditShouldRemove INTEGER DEFAULT 0,
    culturalRiskRegions TEXT DEFAULT '[]',
    ttsHashList TEXT,
    audioGenerated INTEGER DEFAULT 0,
    
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
);
```

### tts_assets 表
TTS资产管理表，管理音频文件：

```sql
CREATE TABLE tts_assets (
    ttsId TEXT PRIMARY KEY,                 -- 24位哈希作为主键
    originalText TEXT NOT NULL,             -- 原始文本
    normalizedText TEXT NOT NULL,           -- 标准化文本
    textHash TEXT NOT NULL,                 -- 文本哈希
    textToSpeak TEXT NOT NULL,              -- 实际用于TTS的文本
    learningLanguage TEXT NOT NULL,         -- 学习语言
    ttsType TEXT NOT NULL,                  -- TTS类型
    status TEXT DEFAULT 'pending',          -- 状态
    audioUrl TEXT,                          -- 音频文件URL
    audioDuration REAL,                     -- 音频时长
    sourceWord TEXT,                        -- 来源单词
    scaffoldingLanguage TEXT,               -- 脚手架语言
    createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### word_processing_queue 表（新增）
看板式状态管理表，用于精细化的单词处理流程控制：

```sql
CREATE TABLE word_processing_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    
    -- 核心处理流程状态 (看板列)
    contentGenerated BOOLEAN NOT NULL DEFAULT FALSE,
    contentAiReviewed BOOLEAN NOT NULL DEFAULT FALSE,
    ttsIdGenerated BOOLEAN NOT NULL DEFAULT FALSE,
    audioGenerated BOOLEAN NOT NULL DEFAULT FALSE,
    readyForPublish BOOLEAN NOT NULL DEFAULT FALSE,
    
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
);
```

## 输出示例

### 成功初始化
```
🚀 开始数据库初始化...
📍 数据库路径: ./senseword_content_v4.db

📋 创建数据库表...
✅ words_for_publish 表创建成功
✅ tts_assets 表创建成功
✅ word_processing_queue 表创建成功

🔍 创建索引...
✅ 成功创建 13 个索引

✅ 验证数据库结构...
✅ 表 words_for_publish 存在
✅ 表 tts_assets 存在
✅ 表 word_processing_queue 存在
✅ 索引 idx_words_audioGenerated 存在
✅ 索引 idx_tts_status 存在
✅ 索引 idx_queue_language_pair 存在

📊 数据库统计信息...
📊 words_for_publish: 0 条记录
📊 tts_assets: 0 条记录
📊 word_processing_queue: 0 条记录

🎉 数据库初始化完成！
✅ 所有表和索引创建成功
✅ 数据库结构验证通过
```

### 验证现有数据库
```
🔍 验证模式：检查现有数据库结构...
✅ 成功连接到数据库: ./senseword_content_v4.db
✅ 表 words_for_publish 存在
✅ 表 tts_assets 存在
✅ 表 word_processing_queue 存在
✅ 索引 idx_words_audioGenerated 存在
✅ 索引 idx_tts_status 存在
✅ 索引 idx_queue_language_pair 存在

📊 数据库统计信息...
📊 words_for_publish: 15,234 条记录
📊 tts_assets: 1,306,098 条记录
📊 word_processing_queue: 0 条记录
📊 处理队列状态分布:
   - 内容已生成: 0
   - 内容已审核: 0
   - TTS已生成: 0
   - 音频已生成: 0
   - 准备发布: 0

✅ 数据库结构验证通过
```

## 注意事项

### ⚠️ 重要提醒
- 使用 `--force` 参数会**完全删除**现有数据库，请谨慎使用
- 建议在生产环境使用前先在测试环境验证
- 新增的 `word_processing_queue` 表与现有表完全兼容

### 🔧 故障排除
1. **权限错误**: 确保对数据库文件和目录有读写权限
2. **路径错误**: 确保数据库目录存在
3. **SQLite版本**: 确保使用 SQLite 3.8+ 版本

### 📈 性能优化
- 所有关键字段都已创建索引
- 支持高效的语言对查询
- 优化了状态筛选查询性能

## 与现有系统的兼容性

这个初始化脚本完全兼容现有的 SenseWord v4.0 系统：
- 保留所有现有表结构
- 新增的处理队列表不影响现有功能
- 所有索引都是向后兼容的

## 下一步

数据库初始化完成后，您可以：
1. 使用 `01-单词提取与过滤` 工作流导入单词
2. 使用 `03-AI批处理筛选` 进行内容生成
3. 使用新的看板式状态管理系统跟踪处理进度
