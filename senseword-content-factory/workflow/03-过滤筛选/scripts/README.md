# 阶段03: AI批处理筛选

## 📋 概述
使用Google Vertex AI批处理服务对来自阶段01的TSV格式单词列表进行智能筛选，采用同心圆筛选系统，确保选出最适合语言学习的高质量单词。

## 🎯 目标
- 从TSV文件读取过滤后的单词列表
- 使用AI进行智能单词筛选
- 应用同心圆筛选系统
- 保持混合架构优势
- 生成高质量的单词候选列表

## 🔄 数据管道设计

### 输入数据
- **主要输入**: 来自阶段01的TSV格式单词列表（一行一词）
- **提示词**: 同心圆筛选提示词 (`/workflow/Prompts/01_单词筛选同心圆模型提示词/01_EN.md`)
- **配置**: Vertex AI批处理配置

### 输出数据
- **JSONL结果**: AI筛选后的单词列表（包含评分、词性、保留标记）
- **筛选报告**: 筛选统计和质量分析
- **批处理日志**: 任务执行状态和错误信息

### 处理流程
```
TSV单词列表 → 01_tsv_to_jsonl.py → 02_generate_vertex_ai_batch.py → GCP批处理系统 → 03_process_results.py → 04_update_database.py → 筛选结果
     ↓              ↓                      ↓                         ↓              ↓                    ↓                ↓
  一行一词格式      批量JSONL生成         生成批处理任务文件           AI处理          结果处理             数据库更新        高质量词汇
  来自阶段01       100词/批次            上传脚本+配置文件           Vertex AI       解析筛选结果         状态同步          优先级排序
```

## 🛠️ 脚本说明

### `01_tsv_to_jsonl.py` - TSV转JSONL批处理文件生成器

#### 功能
- 从TSV文件读取单词列表（一行一词格式）
- 生成Vertex AI批处理所需的JSONL文件
- 自动分批处理（默认100词/批次）
- 集成同心圆筛选提示词

#### 用法
```bash
# 基础用法
python scripts/01_tsv_to_jsonl.py \
  --input /path/to/filtered_words.tsv \
  --output /path/to/output_dir/ \
  --batch-size 100

# 指定提示词文件
python scripts/01_tsv_to_jsonl.py \
  --input /path/to/filtered_words.tsv \
  --output /path/to/output_dir/ \
  --prompt-file /path/to/custom_prompt.md \
  --batch-size 50
```

### `02_generate_vertex_ai_batch.py` - Vertex AI批处理任务文件生成器

#### 功能
- 生成Vertex AI批处理任务所需的完整文件集
- 创建任务配置信息和上传脚本
- 生成标准化的文件命名和目录结构
- 与Google Cloud Platform批处理系统集成

#### 用法
```bash
python scripts/02_generate_vertex_ai_batch.py \
  --input /path/to/batch_job.jsonl \
  --output /path/to/batch_files/ \
  --job-name word-filtering-20250713
```

#### 输出文件
- `task_info_*.json`: 任务配置信息
- `*.jsonl`: 标准化的批处理输入文件
- `upload_*.sh`: GCS上传脚本
- `README_*.md`: 使用说明文档

### `03_process_results.py` - 批处理结果处理器

#### 功能
- 下载和解析Vertex AI批处理结果
- 生成筛选统计报告
- 输出高质量单词列表（保留/移除分类）

#### 用法
```bash
python scripts/03_process_results.py \
  --job-id <vertex_ai_job_id> \
  --project-id your-project-id \
  --output /path/to/results_dir/
```

### `04_update_database_with_ai_results.py` - 数据库状态更新器

#### 功能
- 将AI筛选结果更新到数据库
- 同步单词处理状态
- 生成更新统计报告

#### 用法
```bash
python scripts/04_update_database_with_ai_results.py \
  --db-path /path/to/database.db \
  --results-file /path/to/ai_results.jsonl
```

### `05_cleanup_anomalous_words.py` - 异常单词清理器

#### 功能
- 基于AI结果和模式检测清理异常单词
- 支持试运行模式预览
- 生成详细的清理报告

#### 用法
```bash
# 试运行预览
python scripts/05_cleanup_anomalous_words.py \
  --db-path /path/to/database.db \
  --results-file /path/to/ai_results.jsonl \
  --dry-run

# 确认执行清理
python scripts/05_cleanup_anomalous_words.py \
  --db-path /path/to/database.db \
  --results-file /path/to/ai_results.jsonl \
  --confirm
```

### `process_word_filtering.sh` - 一键处理脚本

#### 功能
- 完整的TSV到AI筛选结果的端到端流程
- 支持分步执行和结果处理
- 自动任务监控和状态跟踪

#### 用法
```bash
# 完整流程（推荐）
./scripts/process_word_filtering.sh \
  -i /path/to/filtered_words.tsv \
  -o ./output \
  -p your-project-id

# 仅提交任务（适合长时间处理）
./scripts/process_word_filtering.sh \
  -i /path/to/filtered_words.tsv \
  -o ./output \
  -p your-project-id \
  --submit-only

# 仅处理结果（任务完成后）
./scripts/process_word_filtering.sh \
  --process-only job_123456 \
  -o ./output \
  -p your-project-id
```

## 🔄 典型工作流程

### 方式一：完整自动化流程
```bash
# 1. 从阶段01获取TSV文件
INPUT_TSV="/path/to/stage01_output/filtered_words.tsv"

# 2. 执行完整AI筛选流程
./scripts/process_word_filtering.sh \
  -i "$INPUT_TSV" \
  -o ./ai_filtering_output \
  -p your-project-id \
  -j word-filtering-$(date +%Y%m%d)

# 3. 查看结果
ls ./ai_filtering_output/
```

### 方式二：分步执行流程（推荐用于生产环境）
```bash
# 1. 生成JSONL并提交任务
./scripts/process_word_filtering.sh \
  -i filtered_words.tsv \
  -o ./output \
  -p your-project-id \
  --submit-only

# 2. 记录任务ID（从输出或job_id.txt文件）
JOB_ID=$(cat ./output/job_id.txt)

# 3. 等待30-60分钟后处理结果
./scripts/process_word_filtering.sh \
  --process-only "$JOB_ID" \
  -o ./output \
  -p your-project-id
```

### 方式三：手动分步执行
```bash
# 1. TSV转JSONL
python scripts/01_tsv_to_jsonl.py \
  --input filtered_words.tsv \
  --output ./batch_input \
  --batch-size 100

# 2. 提交到Vertex AI
python scripts/02_submit_vertex_ai.py \
  --input ./batch_input/ai_word_filtering_*.jsonl \
  --job-name word-filtering-test \
  --project-id your-project-id

# 3. 处理结果（任务完成后）
python scripts/03_process_results.py \
  --job-id <returned_job_id> \
  --project-id your-project-id \
  --output ./results
```

## 配置文件

### vertex_ai_config.json
```json
{
  "project_settings": {
    "project_id": "senseword-ai-platform",
    "location": "us-central1",
    "service_account_key": "path/to/service-account.json"
  },
  "batch_job_settings": {
    "model_name": "gemini-1.5-pro",
    "max_requests_per_minute": 1000,
    "timeout_seconds": 3600,
    "retry_policy": {
      "max_retries": 3,
      "backoff_multiplier": 2.0
    }
  },
  "storage_settings": {
    "input_bucket": "senseword-batch-input",
    "output_bucket": "senseword-batch-output",
    "temp_bucket": "senseword-batch-temp"
  },
  "hybrid_architecture": {
    "use_python_validation": true,
    "use_gsutil_upload": true,
    "use_curl_api": true,
    "enable_progress_monitoring": true
  }
}
```

## 同心圆筛选系统

### 筛选层级

#### 核心圈 (Core Circle) - 评分 9-10
- 最基础、最重要的单词
- 日常生活必需词汇
- 高频使用单词
- 学习优先级最高

#### 重要圈 (Important Circle) - 评分 7-8
- 重要但非核心的单词
- 学术和职场常用词汇
- 中等频率单词
- 学习优先级较高

#### 扩展圈 (Extended Circle) - 评分 5-6
- 扩展词汇和专业术语
- 低频但有用的单词
- 特定场景使用
- 学习优先级中等

#### 边缘圈 (Peripheral Circle) - 评分 3-4
- 生僻或专业性很强的单词
- 极低频使用
- 特殊领域专用
- 学习优先级较低

#### 排除圈 (Excluded Circle) - 评分 1-2
- 不适合学习的单词
- 过时或废弃词汇
- 方言或俚语
- 不推荐学习

## 提示词系统

### 主提示词 (concentric_circle_prompt.md)
```markdown
# SenseWord 单词筛选同心圆系统

你是一个专业的英语词汇筛选专家，需要根据同心圆系统对单词进行评分和分类。

## 评分标准

### 核心圈 (9-10分)
- 日常生活必需词汇
- 高频使用单词 (前1000词)
- 基础语法功能词
- 学习者必须掌握

### 重要圈 (7-8分)
- 学术和职场常用词汇
- 中等频率单词 (1000-5000词)
- 重要概念表达
- 提升表达能力必需

### 扩展圈 (5-6分)
- 扩展词汇和一般专业术语
- 低频但实用单词 (5000-15000词)
- 特定场景使用
- 丰富词汇量有用

### 边缘圈 (3-4分)
- 生僻或高度专业化单词
- 极低频使用 (15000词以后)
- 特殊领域专用
- 一般学习者不必掌握

### 排除圈 (1-2分)
- 过时、废弃或方言词汇
- 不当或冒犯性词汇
- 拼写错误或无效词汇
- 不适合学习

## 输出格式
对每个单词，请提供：
1. 评分 (1-10)
2. 分类圈层
3. 筛选理由 (简洁明确)
4. 学习建议 (可选)
```

## 数据目录结构

```
data/
├── batch_input/     # 批处理输入文件
│   ├── batch_job.jsonl
│   ├── job_config.json
│   └── upload_manifest.json
├── batch_output/    # 批处理输出文件
│   ├── results.jsonl
│   ├── job_report.json
│   └── error_log.json
└── processed/       # 处理后的最终结果
    ├── filtered_words.json
    ├── scoring_report.json
    └── quality_metrics.json
```

## 混合架构实现

### Python验证层
- 输入数据格式验证
- JSONL文件生成和验证
- 结果数据解析和验证
- 错误处理和重试逻辑

### gsutil上传层
```bash
# 上传输入文件到GCS
gsutil -m cp data/batch_input/batch_job.jsonl gs://senseword-batch-input/
gsutil -m cp data/batch_input/job_config.json gs://senseword-batch-input/
```

### curl API层
```bash
# 提交批处理任务
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -H "Content-Type: application/json" \
  -d @job_request.json \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/us-central1/batchPredictionJobs"

# 监控任务状态
curl -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/PROJECT_ID/locations/us-central1/batchPredictionJobs/JOB_ID"
```

## 批处理任务格式

### 输入JSONL格式
```jsonl
{"custom_id": "word_001", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gemini-1.5-pro", "messages": [{"role": "system", "content": "你是专业的英语词汇筛选专家..."}, {"role": "user", "content": "请对单词 'example' 进行同心圆筛选评分"}]}}
{"custom_id": "word_002", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gemini-1.5-pro", "messages": [{"role": "system", "content": "你是专业的英语词汇筛选专家..."}, {"role": "user", "content": "请对单词 'serendipity' 进行同心圆筛选评分"}]}}
```

### 输出结果格式
```json
{
  "metadata": {
    "job_id": "batch_job_001",
    "processing_date": "2024-01-01T00:00:00Z",
    "total_words": 25000,
    "processed_words": 24950,
    "failed_words": 50,
    "model_version": "gemini-1.5-pro"
  },
  "results": [
    {
      "word_id": "word_001",
      "word": "example",
      "ai_score": 9,
      "circle_category": "core",
      "reasoning": "高频日常词汇，基础概念表达必需",
      "learning_priority": "high",
      "confidence": 0.95
    }
  ]
}
```

## 质量控制

### 批处理监控
- 实时任务状态监控
- 进度百分比跟踪
- 错误率统计
- 性能指标收集

### 结果验证
- AI评分合理性检查
- 筛选理由质量评估
- 一致性验证
- 异常结果标记

### 错误处理
- 失败请求重试机制
- 部分结果保存
- 错误日志记录
- 人工审核标记

## 性能优化

### 批处理优化
- 合理的批次大小设置
- 并发请求控制
- 超时和重试策略
- 资源使用监控

### 成本控制
- 请求频率限制
- 模型选择优化
- 批处理任务合并
- 成本监控和预警

## 监控和日志

### 关键指标
- 批处理任务成功率
- 平均处理时间
- AI评分分布
- 成本消耗统计

### 日志示例
```
[2024-01-01 10:00:00] INFO: Preparing batch job with 25000 words
[2024-01-01 10:05:00] INFO: Uploading input file to GCS: gs://senseword-batch-input/batch_job.jsonl
[2024-01-01 10:10:00] INFO: Submitting batch job to Vertex AI
[2024-01-01 10:10:30] INFO: Batch job submitted successfully, job_id: batch_job_001
[2024-01-01 10:15:00] INFO: Job status: RUNNING, progress: 15%
[2024-01-01 12:30:00] INFO: Job completed successfully, processing results
[2024-01-01 12:35:00] INFO: Results processed: 24950 success, 50 failed
```

## 下一阶段
AI筛选完成后，高质量的单词列表将传递给 `04-数据库初始化` 阶段进行数据库导入和架构设置。
