#!/usr/bin/env python3
"""
TSV单词列表转Vertex AI批处理JSONL文件生成器
从阶段01的TSV输出文件读取单词，生成AI批处理任务文件
"""

import json
import os
import argparse
from datetime import datetime
from typing import List, Dict, Any
import math
from pathlib import Path

class TSVToJSONLConverter:
    """TSV单词列表转JSONL批处理文件转换器"""
    
    def __init__(self, tsv_path: str, output_dir: str, batch_size: int = 100, prompt_file: str = None):
        """
        初始化转换器
        
        Args:
            tsv_path: TSV输入文件路径
            output_dir: 输出目录路径
            batch_size: 每批处理的单词数量
            prompt_file: 自定义提示词文件路径
        """
        self.tsv_path = tsv_path
        self.output_dir = output_dir
        self.batch_size = batch_size
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载提示词文件
        self.prompt_content = self._load_prompt_file(prompt_file)
        
        print(f"🎯 TSV到JSONL转换器已初始化")
        print(f"  - TSV文件: {tsv_path}")
        print(f"  - 输出目录: {output_dir}")
        print(f"  - 批处理大小: {batch_size}")
    
    def _load_prompt_file(self, custom_prompt_file: str = None) -> str:
        """
        加载提示词文件
        
        Args:
            custom_prompt_file: 自定义提示词文件路径
            
        Returns:
            提示词文本内容
        """
        if custom_prompt_file and os.path.exists(custom_prompt_file):
            prompt_file_path = custom_prompt_file
        else:
            # 使用默认提示词路径
            prompt_file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "..", "Prompts", "01_单词筛选同心圆模型提示词", "01_EN.md"
            )
        
        try:
            if not os.path.exists(prompt_file_path):
                raise FileNotFoundError(f"提示词文件不存在: {prompt_file_path}")
            
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            
            print(f"✅ 成功读取提示词文件: {os.path.basename(prompt_file_path)}")
            return prompt
            
        except Exception as e:
            print(f"❌ 提示词文件读取失败: {e}")
            raise
    
    def _load_words_from_tsv(self) -> List[str]:
        """
        从TSV文件加载单词列表
        
        Returns:
            单词列表
        """
        try:
            words = []
            with open(self.tsv_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    word = line.strip()
                    if word:  # 跳过空行
                        words.append({
                            "id": line_num,
                            "word": word
                        })
            
            print(f"✅ 成功从TSV文件加载 {len(words)} 个单词")
            return words
            
        except Exception as e:
            print(f"❌ TSV文件读取失败: {e}")
            raise
    
    def _create_vertex_ai_request(self, words_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        为一批单词创建Vertex AI批处理请求
        
        Args:
            words_batch: 当前批次的单词列表
            
        Returns:
            符合Vertex AI格式的请求字典
        """
        # 构建单词数据文本
        words_data = "\n".join([
            json.dumps({"id": w["id"], "word": w["word"]}, ensure_ascii=False) 
            for w in words_batch
        ])
        
        # 组合完整的请求文本
        full_prompt = f"{self.prompt_content}\n\n请分析以下单词数据：\n{words_data}"
        
        # 构建Vertex AI请求格式
        request = {
            "request": {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": full_prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.7,  # 审核任务保持适中温度
                    "thinkingConfig": {
                        "thinkingBudget": 0  # 关闭思考功能，避免不必要的token消耗
                    },
                    "maxOutputTokens": 20000,  # 增加输出限制确保完整审核结果和编辑指令
                    "topK": 40,
                    "topP": 0.95,
                    "responseMimeType": "application/json"
                }
            }
        }
        
        return request
    
    def convert_to_jsonl(self) -> str:
        """
        将TSV文件转换为JSONL批处理文件
        
        Returns:
            生成的JSONL文件路径
        """
        print(f"🚀 开始TSV到JSONL转换...")
        
        try:
            # 从TSV文件加载单词
            words = self._load_words_from_tsv()
            total_words = len(words)
            total_batches = math.ceil(total_words / self.batch_size)
            
            # 生成JSONL文件名
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            jsonl_filename = f"ai_word_filtering_{timestamp_str}.jsonl"
            jsonl_filepath = os.path.join(self.output_dir, jsonl_filename)
            
            print(f"  - 单词总数: {total_words}")
            print(f"  - 批次数: {total_batches}")
            print(f"  - 输出文件: {jsonl_filename}")
            
            # 生成JSONL文件
            with open(jsonl_filepath, 'w', encoding='utf-8') as f:
                for i in range(total_batches):
                    # 计算当前批次的范围
                    start_idx = i * self.batch_size
                    end_idx = min(start_idx + self.batch_size, total_words)
                    
                    # 获取当前批次的单词
                    words_batch = words[start_idx:end_idx]
                    
                    if not words_batch:
                        break
                    
                    # 创建Vertex AI请求
                    vertex_request = self._create_vertex_ai_request(words_batch)
                    
                    # 写入一行JSON
                    f.write(json.dumps(vertex_request, ensure_ascii=False) + '\n')
                    
                    # 显示进度
                    progress = (i + 1) / total_batches * 100
                    print(f"  📈 进度: {i+1}/{total_batches} ({progress:.1f}%) - 批次大小: {len(words_batch)}")
            
            print(f"🎉 JSONL文件生成完成！")
            print(f"  - 文件: {jsonl_filename}")
            print(f"  - 总行数: {total_batches}")
            print(f"  - 单词总数: {total_words}")
            
            return jsonl_filepath
            
        except Exception as e:
            print(f"❌ JSONL转换失败: {e}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TSV单词列表转Vertex AI批处理JSONL文件')
    parser.add_argument('--input', required=True, help='输入TSV文件路径')
    parser.add_argument('--output', required=True, help='输出目录路径')
    parser.add_argument('--batch-size', type=int, default=100, help='每批处理的单词数量 (默认: 100)')
    parser.add_argument('--prompt-file', help='自定义提示词文件路径')
    
    args = parser.parse_args()
    
    print(f"🎯 TSV到JSONL转换器")
    print(f"=" * 60)
    
    try:
        # 检查输入文件是否存在
        if not os.path.exists(args.input):
            print(f"❌ 输入TSV文件不存在: {args.input}")
            exit(1)
        
        # 创建转换器并执行
        converter = TSVToJSONLConverter(
            tsv_path=args.input,
            output_dir=args.output,
            batch_size=args.batch_size,
            prompt_file=args.prompt_file
        )
        
        # 执行转换
        jsonl_file = converter.convert_to_jsonl()
        
        print(f"\n✅ TSV到JSONL转换完成!")
        print(f"📄 生成文件: {os.path.basename(jsonl_file)}")
        print(f"🚀 可直接提交给Vertex AI批处理服务")
        
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        exit(1)

if __name__ == "__main__":
    main()
