# 工具类库 (Utils)

## 概述
提供统一的公共工具类和函数库，支持整个工作流程的数据库操作、文件处理、日志记录、监控和错误恢复功能。

## 目录结构

```
utils/
├── common/              # 基础公共工具
│   ├── database_utils.py    # 数据库连接和操作
│   ├── file_utils.py        # 文件操作工具
│   ├── logging_utils.py     # 日志记录工具
│   └── validation_utils.py  # 数据验证工具
├── monitoring/          # 监控和性能跟踪
│   ├── pipeline_monitor.py  # 流水线监控
│   ├── error_detector.py    # 错误检测
│   └── performance_tracker.py # 性能跟踪
├── batch_processing/    # 批处理相关工具
│   ├── vertex_ai_client.py  # Vertex AI客户端
│   ├── batch_manager.py     # 批处理管理器
│   └── result_processor.py  # 结果处理器
└── recovery/           # 错误恢复和数据修复
    ├── error_recovery.py    # 错误恢复
    ├── data_repair.py       # 数据修复
    └── checkpoint_manager.py # 检查点管理
```

## 核心工具类

### common/database_utils.py
**功能**: 统一的数据库连接和操作接口
**主要方法**:
- `get_connection(db_path)`: 获取数据库连接
- `execute_query(connection, query, params)`: 执行查询
- `execute_batch(connection, queries)`: 批量执行
- `create_backup(db_path, backup_path)`: 创建备份
- `validate_schema(connection, expected_tables)`: 验证架构

### common/file_utils.py
**功能**: 文件和目录操作工具
**主要方法**:
- `ensure_directory(path)`: 确保目录存在
- `safe_write_json(data, filepath)`: 安全写入JSON
- `safe_read_json(filepath)`: 安全读取JSON
- `get_file_hash(filepath)`: 获取文件哈希
- `compress_file(source, target)`: 文件压缩

### common/logging_utils.py
**功能**: 统一的日志记录系统
**主要方法**:
- `setup_logger(name, level, log_file)`: 设置日志器
- `log_performance(func)`: 性能日志装饰器
- `log_error(error, context)`: 错误日志记录
- `create_progress_bar(total)`: 创建进度条

### common/validation_utils.py
**功能**: 数据验证和格式检查
**主要方法**:
- `validate_json_schema(data, schema)`: JSON架构验证
- `validate_word_format(word)`: 单词格式验证
- `validate_file_integrity(filepath)`: 文件完整性验证
- `sanitize_input(input_data)`: 输入数据清理

## 监控工具

### monitoring/pipeline_monitor.py
**功能**: 流水线执行监控
**特性**:
- 实时状态监控
- 性能指标收集
- 异常检测和报警
- 可视化仪表板

### monitoring/error_detector.py
**功能**: 错误检测和分析
**特性**:
- 异常模式识别
- 错误分类和统计
- 预警机制
- 根因分析

### monitoring/performance_tracker.py
**功能**: 性能跟踪和优化建议
**特性**:
- 执行时间统计
- 内存使用监控
- 吞吐量分析
- 性能瓶颈识别

## 批处理工具

### batch_processing/vertex_ai_client.py
**功能**: Vertex AI批处理客户端
**特性**:
- 混合架构支持 (Python + gsutil + curl)
- 任务提交和监控
- 结果下载和处理
- 错误重试机制

### batch_processing/batch_manager.py
**功能**: 批处理任务管理
**特性**:
- 任务队列管理
- 并发控制
- 进度跟踪
- 资源调度

### batch_processing/result_processor.py
**功能**: 批处理结果处理
**特性**:
- 结果解析和验证
- 数据格式转换
- 质量检查
- 统计报告生成

## 恢复工具

### recovery/error_recovery.py
**功能**: 自动错误恢复
**特性**:
- 失败任务重试
- 状态回滚
- 数据一致性检查
- 恢复策略选择

### recovery/data_repair.py
**功能**: 数据修复和清理
**特性**:
- 损坏数据检测
- 自动修复算法
- 数据完整性恢复
- 修复报告生成

### recovery/checkpoint_manager.py
**功能**: 检查点管理
**特性**:
- 自动检查点创建
- 断点续传支持
- 状态快照管理
- 回滚点选择

## 使用示例

### 数据库操作示例
```python
from utils.common.database_utils import get_connection, execute_query

# 获取数据库连接
conn = get_connection("data/senseword.db")

# 执行查询
results = execute_query(conn, "SELECT * FROM words WHERE status = ?", ("active",))

# 批量操作
queries = [
    ("UPDATE words SET status = ? WHERE id = ?", ("processed", 1)),
    ("UPDATE words SET status = ? WHERE id = ?", ("processed", 2))
]
execute_batch(conn, queries)
```

### 日志记录示例
```python
from utils.common.logging_utils import setup_logger, log_performance

# 设置日志器
logger = setup_logger("word_processor", "INFO", "logs/processor.log")

# 使用性能日志装饰器
@log_performance
def process_words(word_list):
    logger.info(f"Processing {len(word_list)} words")
    # 处理逻辑
    return processed_words
```

### 监控示例
```python
from utils.monitoring.pipeline_monitor import PipelineMonitor

# 创建监控器
monitor = PipelineMonitor("word_processing_pipeline")

# 开始监控
monitor.start_stage("word_extraction")
# 执行阶段任务
monitor.end_stage("word_extraction", success=True, metrics={"processed": 1000})

# 生成报告
report = monitor.generate_report()
```

### 批处理示例
```python
from utils.batch_processing.vertex_ai_client import VertexAIClient

# 创建客户端
client = VertexAIClient(
    project_id="senseword-ai",
    location="us-central1",
    credentials_path="service-account.json"
)

# 提交批处理任务
job_result = client.submit_batch_job(
    input_file="data/batch_input.jsonl",
    job_name="word-filtering-001",
    model_name="gemini-1.5-pro"
)

# 监控任务状态
status = client.monitor_job_status(job_result.job_id)

# 下载结果
results = client.download_results(job_result.job_id, "data/output/")
```

## 配置管理

### 全局配置
所有工具类都支持通过配置文件进行参数设置：

```json
{
  "database": {
    "connection_timeout": 30,
    "max_retries": 3,
    "backup_interval": 3600
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "max_file_size": "10MB",
    "backup_count": 5
  },
  "monitoring": {
    "check_interval": 60,
    "alert_threshold": 0.8,
    "metrics_retention": 7
  },
  "batch_processing": {
    "max_concurrent_jobs": 5,
    "timeout_seconds": 3600,
    "retry_attempts": 3
  }
}
```

## 错误处理

### 统一错误处理
所有工具类都遵循统一的错误处理模式：

```python
from utils.common.logging_utils import log_error

try:
    # 执行操作
    result = risky_operation()
except SpecificException as e:
    log_error(e, {"operation": "risky_operation", "context": context})
    # 尝试恢复或返回默认值
    result = handle_specific_error(e)
except Exception as e:
    log_error(e, {"operation": "risky_operation", "context": context})
    # 通用错误处理
    raise ProcessingError(f"Operation failed: {str(e)}")
```

## 测试和质量保证

### 单元测试
每个工具类都包含完整的单元测试：

```bash
# 运行所有工具类测试
python -m pytest utils/tests/

# 运行特定模块测试
python -m pytest utils/tests/test_database_utils.py

# 生成覆盖率报告
python -m pytest --cov=utils utils/tests/
```

### 集成测试
提供端到端的集成测试：

```bash
# 运行集成测试
python -m pytest utils/integration_tests/

# 性能测试
python utils/performance_tests/benchmark.py
```

## 维护和扩展

### 添加新工具类
1. 在相应目录下创建新的Python文件
2. 实现标准的接口和错误处理
3. 添加完整的文档字符串
4. 编写单元测试和集成测试
5. 更新配置文件和README

### 版本管理
- 遵循语义化版本控制
- 维护变更日志
- 向后兼容性保证
- 废弃功能的迁移指南

这个工具类库为整个SenseWord内容生产工作流程提供了坚实的基础设施支持，确保各个阶段能够高效、可靠地协同工作。
