# SenseWord 批处理生成器完整流程分析

## 📋 概述

SenseWord 批处理生成器 (`01_batch_processing_generator.py`) 是内容生成工作流的核心组件，负责从数据库中读取需要生成内容的单词，并创建符合 Vertex AI 批处理要求的 JSONL 文件。

## 🔄 完整流程图

```mermaid
flowchart TD
    Start([🚀 启动批处理生成器]) --> ParseArgs[📝 解析命令行参数]
    
    ParseArgs --> ValidateDB{🔍 验证数据库文件}
    ValidateDB -->|❌ 不存在| ErrorExit1[❌ 退出: 数据库文件不存在]
    ValidateDB -->|✅ 存在| SetPaths[📁 设置默认路径]
    
    SetPaths --> ConnectDB[🔌 连接数据库]
    ConnectDB --> CheckTables{🔍 检查必需表}
    CheckTables -->|❌ 缺失| ErrorExit2[❌ 退出: 缺少必需表]
    CheckTables -->|✅ 存在| GetStats[📊 获取统计信息]
    
    GetStats --> CheckWords{📈 检查待处理单词}
    CheckWords -->|❌ 无单词| InfoExit[ℹ️ 退出: 无需处理单词]
    CheckWords -->|✅ 有单词| FetchWords[📥 获取单词数据]
    
    FetchWords --> ReadPrompt[📖 读取提示词模板]
    ReadPrompt --> ValidatePrompt{🔍 验证提示词}
    ValidatePrompt -->|❌ 失败| ErrorExit3[❌ 退出: 提示词读取失败]
    ValidatePrompt -->|✅ 成功| GenerateBatch[⚙️ 生成批处理文件]
    
    GenerateBatch --> SaveFile[💾 保存 JSONL 文件]
    SaveFile --> ShowStats[📊 显示统计信息]
    ShowStats --> Success[🎉 完成]
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#ffebee,stroke:#000000,stroke-width:2px,color:#000000
    classDef success fill:#e8f5e8,stroke:#000000,stroke-width:3px,color:#000000
    
    class Start,Success startEnd
    class ParseArgs,SetPaths,ConnectDB,GetStats,FetchWords,ReadPrompt,GenerateBatch,SaveFile,ShowStats process
    class ValidateDB,CheckTables,CheckWords,ValidatePrompt decision
    class ErrorExit1,ErrorExit2,ErrorExit3,InfoExit error
```

## ⏱️ 时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant Script as 🐍 批处理脚本
    participant DB as 🗄️ SQLite数据库
    participant FS as 📁 文件系统
    participant Vertex as ☁️ Vertex AI

    User->>Script: 执行脚本 + 参数
    Script->>Script: 解析命令行参数
    Script->>DB: 连接数据库
    DB-->>Script: 连接成功
    
    Script->>DB: 检查 word_processing_queue 表
    DB-->>Script: 表存在确认
    
    Script->>DB: 查询统计信息
    DB-->>Script: 返回统计数据
    Note over Script: 总数: 15,234<br/>已生成: 8,567<br/>待生成: 6,667
    
    Script->>DB: 查询待处理单词
    DB-->>Script: 返回单词列表
    Note over DB,Script: [(apple, en, zh),<br/>(beautiful, en, zh),<br/>(challenge, en, zh)]
    
    Script->>FS: 读取提示词模板
    FS-->>Script: 返回模板内容
    
    loop 为每个单词生成请求
        Script->>Script: 创建批处理请求
        Note over Script: 替换模板变量<br/>构建JSON请求
    end
    
    Script->>FS: 保存 JSONL 文件
    FS-->>Script: 保存成功
    
    Script->>User: 显示完成信息
    Note over User,Script: 文件路径<br/>任务数量<br/>语言对分布
    
    User->>Vertex: 手动上传到 Vertex AI
    Note over Vertex: 后续批处理执行
```

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "🎯 SenseWord Content Factory"
        subgraph "📊 数据层"
            DB[(🗄️ SQLite数据库<br/>senseword_content_v4.db)]
            WPQ[📋 word_processing_queue<br/>• contentGenerated = FALSE<br/>• 单词 + 语言对]
            WFP[📄 words_for_publish<br/>• 最终内容存储]
        end
        
        subgraph "🔄 处理层"
            BPG[⚙️ 批处理生成器<br/>01_batch_processing_generator.py]
            PT[📖 提示词模板<br/>02_senseword-内容生产-v10-英语版本.md]
        end
        
        subgraph "📁 输出层"
            JSONL[📄 JSONL文件<br/>content_generation_batch_*.jsonl]
            GCS[☁️ Google Cloud Storage]
        end
    end
    
    subgraph "🤖 AI处理"
        VAI[🧠 Vertex AI<br/>批处理服务]
        Gemini[💎 Gemini模型<br/>内容生成]
    end
    
    %% 数据流
    WPQ --> BPG
    PT --> BPG
    BPG --> JSONL
    JSONL --> GCS
    GCS --> VAI
    VAI --> Gemini
    Gemini --> VAI
    VAI --> GCS
    
    %% 样式
    classDef database fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    classDef ai fill:#fff3e0,stroke:#000000,stroke-width:3px,color:#000000
    
    class DB,WPQ,WFP database
    class BPG,PT process
    class JSONL,GCS output
    class VAI,Gemini ai
```

## 📊 关键数据结构转化过程

```mermaid
graph LR
    subgraph "🗄️ 数据库输入"
        DBData["📋 数据库记录<br/>{<br/>  word: 'apple',<br/>  learningLanguage: 'en',<br/>  scaffoldingLanguage: 'zh',<br/>  contentGenerated: FALSE<br/>}"]
    end
    
    subgraph "🔄 脚本处理"
        ProcessData["⚙️ 处理后数据<br/>('apple', 'en', 'zh')"]
        PromptData["📝 提示词数据<br/>{<br/>  word: 'apple',<br/>  learningLanguage: 'en',<br/>  scaffoldingLanguage: 'zh'<br/>}"]
    end
    
    subgraph "☁️ Vertex AI 请求"
        VertexReq["🤖 Vertex AI 请求<br/>{<br/>  'request': {<br/>    'contents': [{<br/>      'role': 'user',<br/>      'parts': [{<br/>        'text': '提示词 + 数据'<br/>      }]<br/>    }],<br/>    'generationConfig': {<br/>      'temperature': 0.7,<br/>      'maxOutputTokens': 60000<br/>    }<br/>  }<br/>}"]
    end
    
    DBData --> ProcessData
    ProcessData --> PromptData
    PromptData --> VertexReq
    
    %% 样式
    classDef input fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    
    class DBData input
    class ProcessData,PromptData process
    class VertexReq output

## 🔍 详细执行步骤说明

### 1. 初始化阶段 🚀
- **参数解析**: 脚本接收数据库路径、限制数量、输出目录等参数
- **路径设置**: 自动设置默认的提示词文件和输出目录路径
- **连接验证**: 建立数据库连接并验证必需表的存在

### 2. 数据获取阶段 📊
- **统计查询**: 从 `word_processing_queue` 表获取处理状态统计
- **单词筛选**: 查询 `contentGenerated = FALSE` 的单词记录
- **数据格式**: 返回 `(word, learningLanguage, scaffoldingLanguage)` 元组列表

### 3. 模板处理阶段 📖
- **模板读取**: 加载英文提示词模板文件
- **变量替换**: 将 `{{scaffoldingLanguage}}` 替换为具体语言名称
- **内容验证**: 确保模板内容完整且格式正确

### 4. 批处理生成阶段 ⚙️
- **请求构建**: 为每个单词创建符合 Vertex AI 格式的请求
- **JSON封装**: 包装为标准的批处理请求格式
- **文件输出**: 生成带时间戳的 JSONL 文件

### 5. 结果展示阶段 📈
- **统计信息**: 显示处理的单词数量和语言对分布
- **文件路径**: 提供生成文件的完整路径
- **后续指导**: 说明如何将文件提交到 Vertex AI

## 💡 关键技术要点

### 数据库查询优化
```sql
SELECT word, learningLanguage, scaffoldingLanguage
FROM word_processing_queue
WHERE contentGenerated = FALSE
ORDER BY createdAt ASC
```

### 语言映射机制
```python
SCAFFOLDING_LANGUAGE_MAPPING = {
    'zh': 'Chinese',
    'en': 'English',
    'es': 'Spanish'
}
```

### Vertex AI 请求格式
```json
{
  "request": {
    "contents": [{"role": "user", "parts": [{"text": "..."}]}],
    "generationConfig": {
      "temperature": 0.7,
      "maxOutputTokens": 60000,
      "responseMimeType": "application/json"
    }
  }
}
```

## 🎯 使用示例

### 基本使用
```bash
python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db
```

### 限制数量测试
```bash
python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db --limit 100
```

### 自定义输出目录
```bash
python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db --output-dir ./custom_output
```

## 📋 输出文件示例

生成的 JSONL 文件每行包含一个完整的 Vertex AI 批处理请求：

```json
{"request": {"contents": [{"role": "user", "parts": [{"text": "SenseWord AI Engine Final Prompt...\n\nPlease analyze the following word data:\n{\"word\": \"apple\", \"learningLanguage\": \"en\", \"scaffoldingLanguage\": \"zh\"}"}]}], "generationConfig": {"temperature": 0.7, "maxOutputTokens": 60000, "topK": 40, "topP": 0.95, "responseMimeType": "application/json"}}}
```

## 🔄 与其他组件的集成

1. **前置依赖**: 需要先运行数据库初始化和状态同步脚本
2. **后续流程**: 生成的文件将提交到 Vertex AI 进行批处理
3. **结果处理**: 批处理完成后需要运行结果提取脚本更新数据库
```
