# SenseWord 批处理生成器 - 系统架构与组件交互

## 🏗️ 整体系统架构

```mermaid
graph TB
    subgraph "🎯 SenseWord Content Factory 生态系统"
        subgraph "📊 数据存储层"
            SQLite[(🗄️ SQLite数据库<br/>senseword_content_v4.db)]
            
            subgraph "核心数据表"
                WPQ[📋 word_processing_queue<br/>• 看板式状态管理<br/>• contentGenerated标记<br/>• 语言对配置]
                WFP[📄 words_for_publish<br/>• 最终内容存储<br/>• AI审核状态<br/>• TTS资产链接]
                TTS[🎵 tts_assets<br/>• 音频资产管理<br/>• 哈希ID映射<br/>• 生成状态追踪]
            end
        end
        
        subgraph "⚙️ 核心处理层"
            BPG[🤖 批处理生成器<br/>01_batch_processing_generator.py<br/>• 数据库查询<br/>• 模板处理<br/>• JSONL生成]
            
            subgraph "依赖组件"
                DBInit[🔧 数据库初始化<br/>database_init.py]
                Sync[🔄 状态同步<br/>01-sync_words_to_queue.py]
                PT[📖 提示词模板<br/>02_senseword-内容生产-v10-英语版本.md]
            end
        end
        
        subgraph "📁 输出与传输层"
            Local[💾 本地文件<br/>content_generation_batch_*.jsonl]
            GCS[☁️ Google Cloud Storage<br/>批处理文件存储]
        end
        
        subgraph "🤖 AI处理层"
            VAI[🧠 Vertex AI<br/>批处理服务<br/>• 任务调度<br/>• 并行处理<br/>• 结果收集]
            Gemini[💎 Gemini模型<br/>• 深度语言分析<br/>• JSON结构化输出<br/>• 多语言支持]
        end
        
        subgraph "🔄 后处理层"
            Extract[📥 结果提取器<br/>• 批处理结果解析<br/>• 数据库更新<br/>• 状态同步]
        end
    end
    
    %% 数据流和依赖关系
    DBInit --> SQLite
    SQLite --> WPQ
    SQLite --> WFP
    SQLite --> TTS
    Sync --> WPQ
    
    WPQ --> BPG
    PT --> BPG
    BPG --> Local
    Local --> GCS
    GCS --> VAI
    VAI --> Gemini
    Gemini --> VAI
    VAI --> GCS
    GCS --> Extract
    Extract --> WFP
    Extract --> WPQ
    
    %% 样式定义
    classDef database fill:#e3f2fd,stroke:#000000,stroke-width:3px,color:#000000
    classDef table fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    classDef dependency fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#ffebee,stroke:#000000,stroke-width:2px,color:#000000
    classDef ai fill:#fce4ec,stroke:#000000,stroke-width:3px,color:#000000
    
    class SQLite database
    class WPQ,WFP,TTS table
    class BPG process
    class DBInit,Sync,PT dependency
    class Local,GCS output
    class VAI,Gemini ai
    class Extract process
```

## 🔄 组件交互时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant BPG as 🤖 批处理生成器
    participant DB as 🗄️ 数据库
    participant FS as 📁 文件系统
    participant GCS as ☁️ Google Cloud
    participant VAI as 🧠 Vertex AI
    participant Gemini as 💎 Gemini模型

    Note over User,Gemini: 🚀 批处理内容生成完整流程
    
    User->>BPG: 启动脚本 + 参数
    BPG->>BPG: 解析参数 & 初始化
    
    BPG->>DB: 连接数据库
    DB-->>BPG: 连接确认
    
    BPG->>DB: 检查必需表存在
    DB-->>BPG: 表结构验证通过
    
    BPG->>DB: 查询处理统计
    DB-->>BPG: 返回状态分布
    Note over BPG: 📊 总数: 15,234<br/>已生成: 8,567<br/>待生成: 6,667
    
    BPG->>DB: 查询待处理单词
    DB-->>BPG: 返回单词列表
    Note over DB,BPG: 🔍 contentGenerated = FALSE<br/>按创建时间排序
    
    BPG->>FS: 读取提示词模板
    FS-->>BPG: 返回模板内容
    
    loop 🔄 为每个单词生成请求
        BPG->>BPG: 构建输入数据对象
        BPG->>BPG: 替换模板变量
        BPG->>BPG: 封装Vertex AI请求
    end
    
    BPG->>FS: 保存JSONL文件
    FS-->>BPG: 文件保存成功
    
    BPG->>User: 显示完成信息
    Note over User: 📄 文件路径<br/>📊 任务统计<br/>🌐 语言对分布
    
    Note over User,VAI: 🚀 手动提交到云端处理
    User->>GCS: 上传JSONL文件
    User->>VAI: 创建批处理任务
    
    VAI->>GCS: 读取批处理文件
    
    loop 🔄 并行处理每个请求
        VAI->>Gemini: 发送单词分析请求
        Gemini->>Gemini: 深度语言分析
        Gemini-->>VAI: 返回JSON结构化结果
    end
    
    VAI->>GCS: 保存批处理结果
    VAI-->>User: 批处理完成通知
    
    Note over User: 🎯 后续使用结果提取器<br/>更新数据库内容
```

## 🧩 核心组件详解

### 1. ContentGenerationBatchProcessor 类 🤖

```mermaid
classDiagram
    class ContentGenerationBatchProcessor {
        -db_path: str
        -connection: sqlite3.Connection
        +__init__(db_path: str)
        +connect() sqlite3.Connection
        +close() void
        +check_required_tables() bool
        +get_words_for_content_generation(limit) List[Tuple]
        +get_content_generation_statistics() Dict[str, int]
    }
    
    class DatabaseConnection {
        +sqlite3.connect()
        +cursor.execute()
        +fetchall()
        +fetchone()
    }
    
    class FileSystem {
        +read_prompt_template()
        +generate_batch_jsonl()
        +os.makedirs()
    }
    
    ContentGenerationBatchProcessor --> DatabaseConnection
    ContentGenerationBatchProcessor --> FileSystem
    
    %% 样式
    classDef processor fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    
    class ContentGenerationBatchProcessor processor
    class DatabaseConnection,FileSystem external
```

### 2. 数据处理流水线 🔄

```mermaid
graph LR
    subgraph "📥 输入阶段"
        Input1[🗄️ 数据库查询<br/>word_processing_queue]
        Input2[📖 提示词模板<br/>Markdown文件]
    end
    
    subgraph "⚙️ 处理阶段"
        Process1[🔄 数据转换<br/>元组 → 对象]
        Process2[📝 模板替换<br/>变量 → 具体值]
        Process3[📦 请求封装<br/>Vertex AI格式]
    end
    
    subgraph "📤 输出阶段"
        Output1[💾 JSONL文件<br/>本地存储]
        Output2[📊 统计信息<br/>控制台输出]
    end
    
    Input1 --> Process1
    Input2 --> Process2
    Process1 --> Process3
    Process2 --> Process3
    Process3 --> Output1
    Process3 --> Output2
    
    %% 样式
    classDef input fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    
    class Input1,Input2 input
    class Process1,Process2,Process3 process
    class Output1,Output2 output
```
