# SenseWord 批处理生成器 - 数据流转与转化过程

## 🔄 完整数据流转图

```mermaid
graph TD
    subgraph "🗄️ 数据库层"
        DB[(SQLite数据库)]
        WPQ[📋 word_processing_queue表<br/>contentGenerated = FALSE]
    end
    
    subgraph "📊 原始数据示例"
        RawData["🔍 数据库记录<br/>┌─────────────────────┐<br/>│ word: 'apple' 🍎    │<br/>│ learningLanguage: 'en'│<br/>│ scaffoldingLanguage: 'zh'│<br/>│ contentGenerated: FALSE│<br/>│ createdAt: 2024-01-15│<br/>└─────────────────────┘"]
    end
    
    subgraph "⚙️ 脚本处理层"
        Fetch[📥 获取数据<br/>SQL查询结果]
        Transform[🔄 数据转换<br/>元组格式化]
        Template[📖 模板处理<br/>变量替换]
    end
    
    subgraph "📝 处理后数据"
        TupleData["📦 元组数据<br/>┌─────────────────┐<br/>│ ('apple', 'en', 'zh')│<br/>│ ('beautiful', 'en', 'zh')│<br/>│ ('challenge', 'en', 'zh')│<br/>└─────────────────┘"]
        
        InputData["🎯 输入数据对象<br/>┌─────────────────────┐<br/>│ {                   │<br/>│   'word': 'apple',  │<br/>│   'learningLanguage': 'en',│<br/>│   'scaffoldingLanguage': 'zh'│<br/>│ }                   │<br/>└─────────────────────┘"]
    end
    
    subgraph "☁️ Vertex AI 请求"
        VertexReq["🤖 最终请求格式<br/>┌─────────────────────┐<br/>│ {                   │<br/>│   'request': {      │<br/>│     'contents': [   │<br/>│       {             │<br/>│         'role': 'user',│<br/>│         'parts': [  │<br/>│           {         │<br/>│             'text': '提示词+数据'│<br/>│           }         │<br/>│         ]           │<br/>│       }             │<br/>│     ],              │<br/>│     'generationConfig': {│<br/>│       'temperature': 0.7,│<br/>│       'maxOutputTokens': 60000│<br/>│     }               │<br/>│   }                 │<br/>│ }                   │<br/>└─────────────────────┘"]
    end
    
    subgraph "💾 文件输出"
        JSONL["📄 JSONL文件<br/>content_generation_batch_20240115_143022.jsonl"]
    end
    
    %% 数据流
    DB --> WPQ
    WPQ --> RawData
    RawData --> Fetch
    Fetch --> Transform
    Transform --> TupleData
    TupleData --> Template
    Template --> InputData
    InputData --> VertexReq
    VertexReq --> JSONL
    
    %% 样式
    classDef database fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef rawdata fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    classDef processed fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#ffebee,stroke:#000000,stroke-width:2px,color:#000000
    
    class DB,WPQ database
    class RawData rawdata
    class Fetch,Transform,Template process
    class TupleData,InputData,VertexReq processed
    class JSONL output
```

## 🔍 详细数据转化步骤

### 步骤1: 数据库查询 📊

```mermaid
graph LR
    subgraph "SQL查询"
        Query["🔍 SELECT word, learningLanguage, scaffoldingLanguage<br/>FROM word_processing_queue<br/>WHERE contentGenerated = FALSE<br/>ORDER BY createdAt ASC"]
    end
    
    subgraph "查询结果"
        Result["📋 查询结果<br/>┌─────────────────────┐<br/>│ [                   │<br/>│   ('apple', 'en', 'zh'),│<br/>│   ('beautiful', 'en', 'zh'),│<br/>│   ('challenge', 'en', 'zh'),│<br/>│   ('democracy', 'en', 'zh'),│<br/>│   ('environment', 'en', 'zh')│<br/>│ ]                   │<br/>└─────────────────────┘"]
    end
    
    Query --> Result
    
    classDef query fill:#e3f2fd,stroke:#000000,stroke-width:2px,color:#000000
    classDef result fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    
    class Query query
    class Result result
```

### 步骤2: 提示词模板处理 📖

```mermaid
graph TD
    subgraph "模板变量替换"
        Template["📝 原始模板<br/>{{scaffoldingLanguage}} 解释<br/>{{learningLanguage}} 单词"]
        
        Replace1["🔄 替换 scaffoldingLanguage<br/>Chinese 解释<br/>{{learningLanguage}} 单词"]
        
        Replace2["✅ 最终模板<br/>Chinese 解释<br/>en 单词"]
    end
    
    subgraph "语言映射"
        Mapping["🗺️ 语言代码映射<br/>┌─────────────────┐<br/>│ 'zh' → 'Chinese'│<br/>│ 'en' → 'English'│<br/>│ 'es' → 'Spanish'│<br/>└─────────────────┘"]
    end
    
    Template --> Replace1
    Mapping --> Replace1
    Replace1 --> Replace2
    
    classDef template fill:#f3e5f5,stroke:#000000,stroke-width:2px,color:#000000
    classDef mapping fill:#fff3e0,stroke:#000000,stroke-width:2px,color:#000000
    
    class Template,Replace1,Replace2 template
    class Mapping mapping
```

### 步骤3: 请求对象构建 🔧

```mermaid
graph TD
    subgraph "输入数据构建"
        WordData["📦 单词数据<br/>('apple', 'en', 'zh')"]
        
        InputObj["🎯 输入对象<br/>┌─────────────────────┐<br/>│ {                   │<br/>│   'word': 'apple',  │<br/>│   'learningLanguage': 'en',│<br/>│   'scaffoldingLanguage': 'zh'│<br/>│ }                   │<br/>└─────────────────────┘"]
    end
    
    subgraph "请求封装"
        PromptText["📝 完整提示词文本<br/>模板 + JSON数据"]
        
        VertexRequest["🤖 Vertex AI 请求<br/>┌─────────────────────┐<br/>│ {                   │<br/>│   'request': {      │<br/>│     'contents': [   │<br/>│       {             │<br/>│         'role': 'user',│<br/>│         'parts': [  │<br/>│           {         │<br/>│             'text': 'SenseWord AI Engine...'│<br/>│           }         │<br/>│         ]           │<br/>│       }             │<br/>│     ],              │<br/>│     'generationConfig': {│<br/>│       'temperature': 0.7,│<br/>│       'maxOutputTokens': 60000,│<br/>│       'topK': 40,   │<br/>│       'topP': 0.95, │<br/>│       'responseMimeType': 'application/json'│<br/>│     }               │<br/>│   }                 │<br/>│ }                   │<br/>└─────────────────────┘"]
    end
    
    WordData --> InputObj
    InputObj --> PromptText
    PromptText --> VertexRequest
    
    classDef input fill:#e8f5e8,stroke:#000000,stroke-width:2px,color:#000000
    classDef request fill:#ffebee,stroke:#000000,stroke-width:2px,color:#000000
    
    class WordData,InputObj,PromptText input
    class VertexRequest request

## 📄 真实数据示例

### 数据库原始记录
```json
{
  "id": 1001,
  "word": "apple",
  "learningLanguage": "en",
  "scaffoldingLanguage": "zh",
  "contentGenerated": false,
  "contentAiReviewed": false,
  "ttsIdGenerated": false,
  "audioGenerated": false,
  "readyForPublish": false,
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

### 脚本处理后的元组
```python
word_data = [
    ("apple", "en", "zh"),
    ("beautiful", "en", "zh"),
    ("challenge", "en", "zh"),
    ("democracy", "en", "zh"),
    ("environment", "en", "zh")
]
```

### 生成的输入数据对象
```json
{
  "word": "apple",
  "learningLanguage": "en",
  "scaffoldingLanguage": "zh"
}
```

### 最终的 Vertex AI 请求
```json
{
  "request": {
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "### SenseWord AI Engine Final Prompt: Deep Word Analysis\n\n#### 1. Overall Mission\nYou are a seasoned linguist and an empathetic multilingual educator...\n\nPlease analyze the following word data:\n{\"word\": \"apple\", \"learningLanguage\": \"en\", \"scaffoldingLanguage\": \"zh\"}"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.7,
      "maxOutputTokens": 60000,
      "topK": 40,
      "topP": 0.95,
      "responseMimeType": "application/json"
    }
  }
}
```

### 输出的 JSONL 文件内容
```
{"request": {"contents": [{"role": "user", "parts": [{"text": "### SenseWord AI Engine Final Prompt...\n\nPlease analyze the following word data:\n{\"word\": \"apple\", \"learningLanguage\": \"en\", \"scaffoldingLanguage\": \"zh\"}"}]}], "generationConfig": {"temperature": 0.7, "maxOutputTokens": 60000, "topK": 40, "topP": 0.95, "responseMimeType": "application/json"}}}
{"request": {"contents": [{"role": "user", "parts": [{"text": "### SenseWord AI Engine Final Prompt...\n\nPlease analyze the following word data:\n{\"word\": \"beautiful\", \"learningLanguage\": \"en\", \"scaffoldingLanguage\": \"zh\"}"}]}], "generationConfig": {"temperature": 0.7, "maxOutputTokens": 60000, "topK": 40, "topP": 0.95, "responseMimeType": "application/json"}}}
```

## 🔄 数据转化关键点

### 1. 数据库字段映射
- `word` → 直接使用
- `learningLanguage` → 直接使用
- `scaffoldingLanguage` → 映射为语言名称用于模板替换

### 2. 模板变量处理
- `{{scaffoldingLanguage}}` → 替换为 "Chinese"
- `{{learningLanguage}}` → 保持为 "en"

### 3. JSON 结构封装
- 输入数据 → Vertex AI 标准请求格式
- 添加生成配置参数
- 设置响应格式为 JSON

### 4. 文件输出格式
- 每行一个完整的 JSON 请求对象
- 文件名包含时间戳便于识别
- 符合 Vertex AI 批处理要求

## 📊 处理统计信息

### 典型处理量级
- **小批量测试**: 100-500 个单词
- **中等批量**: 1,000-5,000 个单词
- **大批量生产**: 10,000+ 个单词

### 文件大小估算
- 每个请求约 15-20KB
- 1,000 个单词 ≈ 15-20MB
- 10,000 个单词 ≈ 150-200MB

### 处理时间
- 数据库查询: < 1秒
- 模板处理: < 5秒
- 文件生成: 1-10秒（取决于数量）
```
