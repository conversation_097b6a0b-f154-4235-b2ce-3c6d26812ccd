# SenseWord 内容生成工作流程文档

## 📋 概述

SenseWord 内容生成模块是整个内容工厂的核心组件，负责将筛选后的单词通过 AI 生成深度语言分析内容。本模块采用 Vertex AI 批处理服务，实现大规模、高效率的内容生成。

## 🎯 核心功能

- **📊 数据库驱动**: 从 `word_processing_queue` 表读取待处理单词
- **🤖 AI 内容生成**: 使用 Gemini 模型进行深度单词分析
- **☁️ 批处理优化**: 支持 Vertex AI 批处理服务，提高处理效率
- **🌐 多语言支持**: 支持 learningLanguage + scaffoldingLanguage 配置
- **📄 标准化输出**: 生成符合 SenseWord 规范的 JSON 结构化内容

## 🏗️ 系统架构

```
📊 数据库层 (SQLite)
    ↓
⚙️ 批处理生成器 (Python)
    ↓
📄 JSONL 文件 (本地)
    ↓
☁️ Google Cloud Storage
    ↓
🧠 Vertex AI 批处理服务
    ↓
💎 Gemini 模型处理
    ↓
📥 结果提取与数据库更新
```

## 📁 文件结构

```
05-内容生成/
├── scripts/
│   ├── batch_task_queue/
│   │   └── 01_batch_processing_generator.py  # 🤖 核心批处理生成器
│   └── result_extraction/
│       └── (结果提取脚本)
├── batch_output/                             # 📄 生成的 JSONL 文件
├── docs/                                     # 📚 文档目录
│   ├── README.md                            # 📖 本文档
│   ├── 01_批处理生成器完整流程图.md           # 🔄 完整流程分析
│   ├── 02_数据流转与转化过程.md               # 📊 数据转化详解
│   └── 03_系统架构与组件交互.md               # 🏗️ 架构设计文档
└── config/                                   # ⚙️ 配置文件
```

## 🚀 快速开始

### 前置条件

1. **数据库准备**: 确保已运行数据库初始化脚本
2. **状态同步**: 运行状态同步脚本，确保 `word_processing_queue` 表有数据
3. **提示词文件**: 确保英文提示词模板文件存在

### 基本使用

```bash
# 进入脚本目录
cd senseword-content-factory/workflow/05-内容生成/scripts/batch_task_queue

# 基本使用 - 生成所有待处理单词的批处理任务
python 01_batch_processing_generator.py --db-path ../../../../01-EN/SQLite/senseword_content_v4.db

# 限制数量测试 - 仅处理前100个单词
python 01_batch_processing_generator.py --db-path ../../../../01-EN/SQLite/senseword_content_v4.db --limit 100

# 自定义输出目录
python 01_batch_processing_generator.py --db-path ../../../../01-EN/SQLite/senseword_content_v4.db --output-dir ./custom_output

# 使用自定义提示词文件
python 01_batch_processing_generator.py --db-path ../../../../01-EN/SQLite/senseword_content_v4.db --prompt-file ./custom_prompt.md
```

### 参数说明

| 参数 | 必需 | 说明 | 示例 |
|------|------|------|------|
| `--db-path` | ✅ | 数据库文件路径 | `./senseword_content_v4.db` |
| `--limit` | ❌ | 限制处理的单词数量 | `100` |
| `--output-dir` | ❌ | 输出目录路径 | `./batch_output` |
| `--prompt-file` | ❌ | 提示词文件路径 | `./custom_prompt.md` |

## 📊 数据流程

### 1. 数据库查询阶段
```sql
SELECT word, learningLanguage, scaffoldingLanguage
FROM word_processing_queue 
WHERE contentGenerated = FALSE
ORDER BY createdAt ASC
```

### 2. 数据转化阶段
```python
# 数据库记录 → 元组
("apple", "en", "zh")

# 元组 → 输入对象
{
  "word": "apple",
  "learningLanguage": "en", 
  "scaffoldingLanguage": "zh"
}
```

### 3. 请求生成阶段
```json
{
  "request": {
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "提示词模板 + 单词数据"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.7,
      "maxOutputTokens": 60000,
      "responseMimeType": "application/json"
    }
  }
}
```

## 🔧 核心组件

### ContentGenerationBatchProcessor 类

主要功能：
- **数据库连接管理**: 安全的连接建立和关闭
- **表结构验证**: 检查必需的数据库表是否存在
- **统计信息获取**: 提供处理状态的详细统计
- **单词数据查询**: 高效获取待处理的单词列表

### 关键函数

- `get_words_for_content_generation()`: 获取需要生成内容的单词
- `get_content_generation_statistics()`: 获取处理统计信息
- `create_batch_request()`: 创建 Vertex AI 批处理请求
- `generate_batch_jsonl()`: 生成 JSONL 批处理文件

## 📈 性能指标

### 处理能力
- **小批量测试**: 100-500 个单词
- **中等批量**: 1,000-5,000 个单词
- **大批量生产**: 10,000+ 个单词

### 文件大小
- 每个请求约 15-20KB
- 1,000 个单词 ≈ 15-20MB
- 10,000 个单词 ≈ 150-200MB

### 处理时间
- 数据库查询: < 1秒
- 模板处理: < 5秒  
- 文件生成: 1-10秒（取决于数量）

## 🔄 完整工作流程

1. **📊 数据准备**: 运行数据库初始化和状态同步
2. **🤖 批处理生成**: 使用本脚本生成 JSONL 文件
3. **☁️ 云端上传**: 将 JSONL 文件上传到 Google Cloud Storage
4. **🧠 AI 处理**: 提交到 Vertex AI 批处理服务
5. **📥 结果提取**: 使用结果提取脚本更新数据库
6. **✅ 状态更新**: 更新 `contentGenerated` 状态

## 🛠️ 故障排除

### 常见问题

**Q: 数据库连接失败**
```
❌ 数据库连接失败: no such file or directory
```
**A**: 检查数据库文件路径是否正确，确保文件存在

**Q: 没有找到待处理单词**
```
⚠️ 没有找到需要生成内容的单词
```
**A**: 运行状态同步脚本，确保 `word_processing_queue` 表有 `contentGenerated = FALSE` 的记录

**Q: 提示词文件读取失败**
```
❌ 读取提示词文件失败: FileNotFoundError
```
**A**: 检查提示词文件路径，确保英文版本提示词文件存在

### 调试模式

```bash
# 使用小数量测试
python 01_batch_processing_generator.py --db-path ./test.db --limit 10

# 检查数据库状态
sqlite3 senseword_content_v4.db "SELECT COUNT(*) FROM word_processing_queue WHERE contentGenerated = 0;"
```

## 📚 相关文档

- [完整流程图](./01_批处理生成器完整流程图.md) - 详细的流程分析和可视化
- [数据流转过程](./02_数据流转与转化过程.md) - 数据在系统中的转化过程
- [系统架构设计](./03_系统架构与组件交互.md) - 架构设计和组件交互

## 🎯 下一步

生成批处理文件后，您需要：

1. **上传文件**: 将生成的 JSONL 文件上传到 Google Cloud Storage
2. **创建批处理任务**: 在 Vertex AI 控制台创建批处理预测任务
3. **监控进度**: 等待批处理完成（通常需要几小时到几天）
4. **提取结果**: 使用结果提取脚本处理 AI 生成的内容
5. **更新数据库**: 将生成的内容保存到 `words_for_publish` 表

---

📝 **文档版本**: v2.0  
🕒 **最后更新**: 2024-01-15  
👤 **维护者**: SenseWord Content Factory Team
