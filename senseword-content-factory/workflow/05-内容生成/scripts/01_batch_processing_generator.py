#!/usr/bin/env python3
"""
SenseWord Content Factory - Vertex AI 批处理内容生成器 v2.0
基于 word_processing_queue 表生成符合 Google Cloud Vertex AI 批处理格式的 JSONL 文件

核心功能：
1. 从 word_processing_queue 表读取需要生成内容的单词
2. 使用最新的英文提示词模板
3. 生成符合 Vertex AI 批处理要求的 JSONL 文件
4. 支持多语言对配置（learningLanguage + scaffoldingLanguage）

更新内容：
- 使用数据库驱动的单词获取方式
- 支持英文提示词模板
- 优化批处理任务生成逻辑
- 增强错误处理和日志记录
"""

import json
import os
import sqlite3
import sys
from typing import List, Dict, Tuple, Optional
from datetime import datetime

# 语言代码到语言名称映射（用于scaffoldingLanguage）
SCAFFOLDING_LANGUAGE_MAPPING = {
    'zh': 'Chinese',
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean'
}

class ContentGenerationBatchProcessor:
    """内容生成批处理器"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None

    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            print(f"✅ 成功连接到数据库: {self.db_path}")
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")

    def check_required_tables(self) -> bool:
        """检查必需的数据库表是否存在"""
        try:
            cursor = self.connection.cursor()

            # 检查 word_processing_queue 表
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='word_processing_queue'
            """)
            if not cursor.fetchone():
                print("❌ word_processing_queue 表不存在")
                print("💡 请先运行数据库初始化脚本和状态同步脚本")
                return False

            print("✅ 所有必需的表都存在")
            return True

        except Exception as e:
            print(f"❌ 检查数据库表失败: {e}")
            return False

    def get_words_for_content_generation(self, limit: Optional[int] = None) -> List[Tuple[str, str, str]]:
        """从 word_processing_queue 表获取需要生成内容的单词"""
        try:
            cursor = self.connection.cursor()

            # 查询需要生成内容的单词（contentGenerated = FALSE）
            query = """
                SELECT word, learningLanguage, scaffoldingLanguage
                FROM word_processing_queue
                WHERE contentGenerated = FALSE
                ORDER BY createdAt ASC
            """

            if limit:
                query += f" LIMIT {limit}"

            cursor.execute(query)
            results = cursor.fetchall()

            print(f"📊 找到 {len(results)} 个需要生成内容的单词")
            return results

        except Exception as e:
            print(f"❌ 获取单词列表失败: {e}")
            return []

    def get_content_generation_statistics(self) -> Dict[str, int]:
        """获取内容生成统计信息"""
        try:
            cursor = self.connection.cursor()

            # 统计各种状态的单词数量
            cursor.execute("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN contentGenerated = 1 THEN 1 ELSE 0 END) as content_generated,
                    SUM(CASE WHEN contentGenerated = 0 THEN 1 ELSE 0 END) as need_content_generation
                FROM word_processing_queue
            """)

            result = cursor.fetchone()
            if result:
                stats = {
                    'total': result[0],
                    'content_generated': result[1] or 0,
                    'need_content_generation': result[2] or 0
                }

                print("📊 内容生成统计:")
                print(f"   - 总单词数: {stats['total']:,}")
                print(f"   - 已生成内容: {stats['content_generated']:,}")
                print(f"   - 需要生成内容: {stats['need_content_generation']:,}")

                return stats
            else:
                return {'total': 0, 'content_generated': 0, 'need_content_generation': 0}

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
            return {'total': 0, 'content_generated': 0, 'need_content_generation': 0}

def read_prompt_template(prompt_file_path: str) -> Optional[str]:
    """读取提示词模板文件"""
    try:
        with open(prompt_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"✅ 成功读取提示词模板: {prompt_file_path}")
            return content
    except Exception as e:
        print(f"❌ 读取提示词文件失败: {e}")
        return None


def create_batch_request(word: str, learning_language: str, scaffolding_language: str, prompt_template: str) -> Dict:
    """创建单个批处理请求"""
    # 将语言代码映射到语言名称
    scaffolding_language_name = SCAFFOLDING_LANGUAGE_MAPPING.get(scaffolding_language, scaffolding_language)

    # 构建输入数据 - 根据英文提示词的要求
    input_data = {
        "word": word,
        "learningLanguage": learning_language,
        "scaffoldingLanguage": scaffolding_language  # 使用scaffoldingLanguage保持与数据库一致
    }

    # 替换提示词中的变量
    prompt_content = prompt_template.replace('{{scaffoldingLanguage}}', scaffolding_language_name)
    prompt_content = prompt_content.replace('{{learningLanguage}}', learning_language)

    # 构建 Vertex AI 批处理请求格式
    request = {
        "request": {
            "contents": [
                {
                    "role": "user",
                    "parts": [
                        {
                            "text": f"{prompt_content}\n\nPlease analyze the following word data:\n{json.dumps(input_data, ensure_ascii=False)}"
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 60000,
                "topK": 40,
                "topP": 0.95,
                "responseMimeType": "application/json"
            }
        }
    }

    return request

def generate_batch_jsonl(word_data: List[Tuple[str, str, str]], prompt_content: str, output_file: str) -> bool:
    """生成批处理 JSONL 文件"""
    try:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            for word, learning_language, scaffolding_language in word_data:
                request = create_batch_request(word, learning_language, scaffolding_language, prompt_content)
                f.write(json.dumps(request, ensure_ascii=False) + '\n')

        print(f"✅ 生成批处理文件: {output_file}")
        print(f"   - 包含 {len(word_data)} 个任务")
        print(f"   - 使用 Vertex AI 官方要求的 request 包装格式")
        print(f"   - 温度设置: 0.7, 最大输出: 60000 tokens")
        print(f"   - 支持多语言对配置 (learningLanguage + scaffoldingLanguage)")

        return True

    except Exception as e:
        print(f"❌ 生成批处理文件失败: {e}")
        return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="SenseWord Content Factory - Vertex AI 批处理内容生成器 v2.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 生成所有需要内容的单词的批处理任务
  python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db

  # 限制生成数量（用于测试）
  python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db --limit 100

  # 指定输出目录
  python 01_batch_processing_generator.py --db-path ./senseword_content_v4.db --output-dir ./custom_output
        """
    )

    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )

    parser.add_argument(
        '--limit',
        type=int,
        help='限制处理的单词数量（用于测试）'
    )

    parser.add_argument(
        '--output-dir',
        help='输出目录路径（默认：./batch_output）'
    )

    parser.add_argument(
        '--prompt-file',
        help='提示词文件路径（默认使用英文生产提示词）'
    )

    args = parser.parse_args()

    # 验证数据库文件
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)

    # 设置默认路径
    base_path = "/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS"

    # 默认提示词文件路径
    default_prompt_file = os.path.join(
        base_path,
        "senseword-content-factory/workflow/Prompts/英文生产/02_senseword-内容生产-v10-英语版本.md"
    )
    prompt_file = args.prompt_file or default_prompt_file

    # 默认输出目录
    default_output_dir = os.path.join(
        base_path,
        "senseword-content-factory/workflow/05-内容生成/batch_output"
    )
    output_dir = args.output_dir or default_output_dir

    print("🚀 SenseWord Content Factory - 批处理内容生成器 v2.0")
    print(f"📍 数据库路径: {args.db_path}")
    print(f"📍 提示词文件: {prompt_file}")
    print(f"📍 输出目录: {output_dir}")

    # 创建批处理器
    processor = ContentGenerationBatchProcessor(args.db_path)

    try:
        # 连接数据库
        processor.connect()

        # 检查必需的表
        if not processor.check_required_tables():
            print("❌ 数据库表检查失败")
            sys.exit(1)

        # 获取统计信息
        stats = processor.get_content_generation_statistics()

        if stats['need_content_generation'] == 0:
            print("⚠️  没有找到需要生成内容的单词")
            print("💡 提示：请确保已运行状态同步脚本，并且有单词的 contentGenerated = FALSE")
            return

        # 获取需要生成内容的单词
        print(f"\n📊 正在获取需要生成内容的单词...")
        word_data = processor.get_words_for_content_generation(limit=args.limit)

        if not word_data:
            print("❌ 无法获取单词数据")
            return

        # 读取提示词模板
        print(f"\n📖 正在读取提示词模板...")
        prompt_content = read_prompt_template(prompt_file)
        if not prompt_content:
            print("❌ 无法读取提示词模板")
            return

        # 生成批处理文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"content_generation_batch_{timestamp}.jsonl")

        print(f"\n🔄 正在生成批处理 JSONL 文件...")
        success = generate_batch_jsonl(word_data, prompt_content, output_file)

        if success:
            print(f"\n🎉 批处理文件生成成功！")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 任务数量: {len(word_data)}")
            print(f"🔧 配置: 温度=0.7, 最大输出=60000 tokens")

            # 显示语言对分布
            language_pairs = {}
            for _, learning_lang, scaffolding_lang in word_data:
                pair = f"{learning_lang}->{scaffolding_lang}"
                language_pairs[pair] = language_pairs.get(pair, 0) + 1

            print(f"\n📈 语言对分布:")
            for pair, count in language_pairs.items():
                print(f"   - {pair}: {count} 个单词")

            print(f"\n🎯 接下来的步骤:")
            print(f"1. 将生成的 JSONL 文件上传到 Google Cloud Storage")
            print(f"2. 提交到 Vertex AI 批处理服务")
            print(f"3. 等待批处理完成后，使用结果提取脚本处理输出")
            print(f"4. 更新数据库中的内容和状态")
        else:
            print(f"\n❌ 批处理文件生成失败")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        processor.close()

if __name__ == "__main__":
    main()