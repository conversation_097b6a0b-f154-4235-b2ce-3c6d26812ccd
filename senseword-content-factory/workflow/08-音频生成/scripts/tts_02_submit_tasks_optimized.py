#!/usr/bin/env python3
"""
优化版 TTS 任务提交脚本

基于重构后的 tts_assets 表结构，实现高性能的单表查询
性能提升：从 7.2秒/单词 提升到 毫秒级批量处理

新特性：
1. 直接查询 tts_assets 表，避免双表关联
2. 基于 frequency 的智能优先级处理
3. 批量提交，支持大规模单词处理
4. 高性能索引查询

作者: AI Assistant  
日期: 2025-07-16
"""

import sqlite3
import requests
import json
import time
import argparse
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime

class OptimizedTTSSubmitter:
    def __init__(self, db_path: str, config_path: str):
        self.db_path = db_path
        self.config_path = config_path
        self.config = self.load_config()
        self.conn = None
        
    def load_config(self) -> Dict:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def connect_db(self):
        """连接数据库"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row
        
    def disconnect_db(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
    
    def get_pending_words_by_priority(self, word_limit: int = 100) -> List[Dict]:
        """
        按优先级获取待处理的单词列表

        优先级顺序：High > Medium > Medium-Low > Low > Rare
        """
        print(f"🔍 查询待处理单词 (限制: {word_limit})")

        query = """
        SELECT DISTINCT word, frequency
        FROM tts_assets
        WHERE status IN ('pending', 'processing', 'failed', 'submitted')
          AND audioUrl IS NULL
          AND word IS NOT NULL
        ORDER BY
          CASE frequency
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'medium_low' THEN 3
            WHEN 'low' THEN 4
            WHEN 'rare' THEN 5
            ELSE 6
          END,
          word
        LIMIT ?
        """

        start_time = time.time()
        cursor = self.conn.execute(query, (word_limit,))
        words = [dict(row) for row in cursor.fetchall()]
        query_time = time.time() - start_time

        print(f"✅ 查询完成: {len(words)} 个单词，耗时: {query_time:.3f}秒")

        # 统计优先级分布
        freq_stats = {}
        for word in words:
            freq = word['frequency']
            freq_stats[freq] = freq_stats.get(freq, 0) + 1

        print("📊 优先级分布:")
        for freq, count in sorted(freq_stats.items()):
            print(f"   {freq}: {count} 个单词")

        return words

    def get_word_tasks(self, word: str) -> List[Dict]:
        """
        获取指定单词的所有需要重新处理的TTS任务
        包括: pending, processing, failed, submitted (实现最终一致性)
        """
        query = """
        SELECT
            ttsId, word, originalText, ttsType, textToSpeak,
            learningLanguage, frequency, status
        FROM tts_assets
        WHERE word = ?
          AND status IN ('pending', 'processing', 'failed', 'submitted')
          AND audioUrl IS NULL
        ORDER BY
          CASE status
            WHEN 'failed' THEN 1
            WHEN 'processing' THEN 2
            WHEN 'pending' THEN 3
            WHEN 'submitted' THEN 4
            ELSE 5
          END,
          ttsType
        """

        cursor = self.conn.execute(query, (word,))
        tasks = [dict(row) for row in cursor.fetchall()]

        return tasks
    
    def get_tasks_by_words(self, words: List[str]) -> List[Dict]:
        """
        根据指定单词列表获取TTS任务
        """
        if not words:
            return []
        
        print(f"🔍 查询指定单词的TTS任务: {len(words)} 个单词")
        
        # 创建占位符
        placeholders = ','.join(['?' for _ in words])
        
        query = f"""
        SELECT
            ttsId, word, originalText, ttsType, textToSpeak,
            learningLanguage, frequency, status
        FROM tts_assets
        WHERE word IN ({placeholders})
          AND status IN ('pending', 'processing', 'failed', 'submitted')
          AND audioUrl IS NULL
        ORDER BY
          CASE status
            WHEN 'failed' THEN 1
            WHEN 'processing' THEN 2
            WHEN 'pending' THEN 3
            WHEN 'submitted' THEN 4
            ELSE 5
          END,
          CASE frequency
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'medium_low' THEN 3
            WHEN 'low' THEN 4
            WHEN 'rare' THEN 5
            ELSE 6
          END,
          word,
          ttsType
        """
        
        start_time = time.time()
        cursor = self.conn.execute(query, words)
        tasks = [dict(row) for row in cursor.fetchall()]
        query_time = time.time() - start_time
        
        print(f"✅ 查询完成: {len(tasks)} 个任务，耗时: {query_time:.3f}秒")
        
        return tasks
    
    def submit_word_tasks(self, word: str, tasks: List[Dict]) -> Dict:
        """
        提交单个单词的所有TTS任务到Worker
        """
        if not tasks:
            return {"success": True, "submitted": 0, "failed": 0}

        print(f"📤 提交单词: {word} ({len(tasks)} 个任务)")

        try:
            # 准备提交数据
            submit_data = {
                "word": word,
                "tasks": [
                    {
                        "ttsId": task["ttsId"],
                        "text": task["textToSpeak"],
                        "type": task["ttsType"]
                    }
                    for task in tasks
                ]
            }

            # 提交到Worker
            worker_url = f"{self.config['worker']['baseUrl']}{self.config['worker']['submitEndpoint']}"
            response = requests.post(
                worker_url,
                json=submit_data,
                headers={"Content-Type": "application/json"},
                timeout=self.config['batch']['timeout']
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print(f"✅ {word}: {len(tasks)} 个任务提交成功")

                    # 本地计算并显示计费信息
                    local_characters = sum(len(task["textToSpeak"]) for task in tasks)
                    local_cost = (local_characters / 1_000_000) * 15  # $15/百万字符
                    print(f"   💰 本次提交: {local_characters}字符 (${local_cost:.4f})")

                    # 显示API Key累计统计（来自Worker）
                    billing = result.get("billing")
                    if billing:
                        print(f"   📊 API Key累计: {billing['totalCharacters']}字符 (${billing['totalCostUSD']:.4f})")

                    # 显示实时系统统计
                    system_stats = result.get("system_stats")
                    if system_stats:
                        backlog = system_stats.get('backlog', system_stats['total'] - system_stats['completed'])
                        print(f"   📈 系统状态: {system_stats['total']}总任务, {system_stats['completed']}已完成, {backlog}积压 ({system_stats['completion_rate']})")

                    return {
                        "success": True,
                        "submitted": len(tasks),
                        "failed": 0,
                        "inserted": result.get("inserted", 0),
                        "failed_tasks": result.get("failed_tasks", []),
                        "billing": billing,
                        "system_stats": system_stats,
                        "local_characters": local_characters,
                        "local_cost": local_cost
                    }
                else:
                    print(f"❌ {word}: 提交失败 - {result}")
                    return {
                        "success": False,
                        "submitted": 0,
                        "failed": len(tasks),
                        "failed_tasks": [task["ttsId"] for task in tasks]
                    }
            else:
                print(f"❌ {word}: HTTP {response.status_code}")
                return {
                    "success": False,
                    "submitted": 0,
                    "failed": len(tasks),
                    "failed_tasks": [task["ttsId"] for task in tasks]
                }

        except Exception as e:
            print(f"❌ {word}: 提交异常 - {e}")
            return {
                "success": False,
                "submitted": 0,
                "failed": len(tasks),
                "failed_tasks": [task["ttsId"] for task in tasks]
            }
    
    def update_word_tasks_status(self, word: str, status: str):
        """
        更新指定单词的所有需要重新处理的任务状态
        """
        query = """
        UPDATE tts_assets
        SET status = ?
        WHERE word = ?
          AND status IN ('pending', 'processing', 'failed', 'submitted')
          AND audioUrl IS NULL
        """

        cursor = self.conn.execute(query, (status, word))
        updated_count = cursor.rowcount
        self.conn.commit()

        if updated_count > 0:
            print(f"✅ 更新单词 {word} 的 {updated_count} 个任务状态为: {status}")

        return updated_count
    
    def run_priority_submission(self, word_limit: int):
        """
        按优先级运行单词级别任务提交
        """
        try:
            self.connect_db()

            print("🚀 开始优化版TTS任务提交 (按单词完整提交)")
            print("=" * 60)

            start_time = time.time()

            # 1. 获取待处理单词
            words = self.get_pending_words_by_priority(word_limit)

            if not words:
                print("ℹ️ 没有待处理的单词")
                return

            # 2. 按单词逐个提交
            words_processed = 0
            words_submitted = 0
            words_failed = 0
            total_tasks = 0
            total_inserted = 0
            failed_tasks = []

            for i, word_data in enumerate(words, 1):
                word = word_data['word']
                frequency = word_data['frequency']

                print(f"\n📝 [{i}/{len(words)}] 处理单词: {word} (频率: {frequency})")
                words_processed += 1

                # 获取单词的所有任务
                tasks = self.get_word_tasks(word)
                if not tasks:
                    print(f"⚠️ 单词 {word} 没有待处理任务")
                    continue

                total_tasks += len(tasks)
                print(f"   📋 找到 {len(tasks)} 个TTS任务")

                # 提交单词任务
                result = self.submit_word_tasks(word, tasks)

                if result["success"]:
                    # 更新任务状态
                    self.update_word_tasks_status(word, "submitted")
                    words_submitted += 1
                    total_inserted += result.get("inserted", 0)

                    # 记录失败的任务
                    if result.get("failed_tasks"):
                        failed_tasks.extend(result["failed_tasks"])
                        print(f"   ⚠️ 有 {len(result['failed_tasks'])} 个任务失败")
                else:
                    words_failed += 1
                    failed_tasks.extend(result.get("failed_tasks", []))
                    print(f"   ❌ 单词 {word} 提交失败")

            # 3. 统计结果
            total_time = time.time() - start_time

            print("\n" + "=" * 60)
            print("📊 提交完成统计:")
            print(f"   处理单词数: {words_processed}")
            print(f"   提交成功单词数: {words_submitted}")
            print(f"   提交失败单词数: {words_failed}")
            print(f"   总任务数: {total_tasks}")
            print(f"   入库任务数: {total_inserted}")
            print(f"   失败任务数: {len(failed_tasks)}")
            print(f"   单词提交成功率: {words_submitted/words_processed*100:.1f}%" if words_processed > 0 else "   单词提交成功率: 0%")
            print(f"   任务入库成功率: {total_inserted/total_tasks*100:.1f}%" if total_tasks > 0 else "   任务入库成功率: 0%")
            print(f"   总耗时: {total_time:.2f}秒")
            print(f"   平均每单词: {total_time/words_processed:.2f}秒" if words_processed > 0 else "   平均每单词: 0秒")

        except Exception as e:
            print(f"❌ 提交失败: {e}")
            raise
        finally:
            self.disconnect_db()
    
    def run_word_submission(self, words: List[str]):
        """
        按指定单词运行任务提交
        """
        try:
            self.connect_db()

            print(f"🚀 开始指定单词TTS任务提交: {len(words)} 个单词")
            print("=" * 60)

            start_time = time.time()

            # 统计变量
            words_processed = 0
            words_submitted = 0
            words_failed = 0
            total_tasks = 0
            total_inserted = 0
            failed_tasks = []

            # 按单词逐个处理
            for word in words:
                print(f"\n📝 处理指定单词: {word}")
                words_processed += 1

                # 获取单词的所有任务
                tasks = self.get_word_tasks(word)
                if not tasks:
                    print(f"⚠️ 单词 {word} 没有待处理任务")
                    continue

                total_tasks += len(tasks)

                # 统计任务状态分布
                status_stats = {}
                for task in tasks:
                    status = task['status']
                    status_stats[status] = status_stats.get(status, 0) + 1

                print(f"   📋 找到 {len(tasks)} 个TTS任务")
                print("   📊 状态分布:", end=" ")
                for status, count in status_stats.items():
                    print(f"{status}:{count}", end=" ")
                print()

                # 提交单词任务
                result = self.submit_word_tasks(word, tasks)

                if result["success"]:
                    # 更新任务状态
                    self.update_word_tasks_status(word, "submitted")
                    words_submitted += 1
                    total_inserted += result.get("inserted", 0)

                    # 记录失败的任务
                    if result.get("failed_tasks"):
                        failed_tasks.extend(result["failed_tasks"])
                        print(f"   ⚠️ 有 {len(result['failed_tasks'])} 个任务失败")
                else:
                    words_failed += 1
                    failed_tasks.extend(result.get("failed_tasks", []))
                    print(f"   ❌ 单词 {word} 提交失败")

            # 统计结果
            total_time = time.time() - start_time

            print("\n" + "=" * 60)
            print("📊 提交完成统计:")
            print(f"   指定单词数: {len(words)}")
            print(f"   处理单词数: {words_processed}")
            print(f"   提交成功单词数: {words_submitted}")
            print(f"   提交失败单词数: {words_failed}")
            print(f"   总任务数: {total_tasks}")
            print(f"   入库任务数: {total_inserted}")
            print(f"   失败任务数: {len(failed_tasks)}")
            print(f"   单词提交成功率: {words_submitted/words_processed*100:.1f}%" if words_processed > 0 else "   单词提交成功率: 0%")
            print(f"   任务入库成功率: {total_inserted/total_tasks*100:.1f}%" if total_tasks > 0 else "   任务入库成功率: 0%")
            print(f"   总耗时: {total_time:.2f}秒")
            print(f"   平均每单词: {total_time/words_processed:.2f}秒" if words_processed > 0 else "   平均每单词: 0秒")

        except Exception as e:
            print(f"❌ 提交失败: {e}")
            raise
        finally:
            self.disconnect_db()

    def get_billing_status(self):
        """
        获取当前API Key计费状态
        """
        try:
            worker_url = f"{self.config['worker']['baseUrl']}/billing/status"
            response = requests.get(
                worker_url,
                headers={"Content-Type": "application/json"},
                timeout=self.config['batch']['timeout']
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    billing = result.get("billing", {})
                    print("\n💰 API Key计费状态:")
                    print(f"   总字符数: {billing.get('totalCharacters', 0):,}")
                    print(f"   总成本: ${billing.get('totalCostUSD', 0):.4f}")
                    print(f"   价格: ${billing.get('pricePerMillionChars', 15)}/百万字符")
                    print(f"   最后更新: {billing.get('lastUpdated', 'N/A')}")
                    return billing
                else:
                    print("❌ 获取计费状态失败")
                    return None
            else:
                print(f"❌ 获取计费状态失败: HTTP {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取计费状态异常: {e}")
            return None

    def get_dashboard_status(self):
        """查看TTS系统综合状态"""
        try:
            print("📊 查询TTS系统综合状态...")

            response = requests.get(f"{self.config['worker']['baseUrl']}/dashboard", timeout=30)

            if response.status_code == 200:
                data = response.json()

                if data.get("success"):
                    print("\n🎯 TTS系统综合状态:")
                    print("=" * 50)

                    # 任务统计
                    tasks = data.get("tasks", {})
                    print(f"📋 任务统计:")
                    print(f"   总任务数: {tasks.get('total', 0):,}")
                    print(f"   待处理: {tasks.get('pending', 0):,}")
                    print(f"   处理中: {tasks.get('processing', 0):,}")
                    print(f"   已完成: {tasks.get('completed', 0):,}")
                    print(f"   失败: {tasks.get('failed', 0):,}")
                    print(f"   完成率: {tasks.get('completion_rate', '0%')}")

                    # 计费信息
                    billing = data.get("billing", {})
                    if "error" not in billing:
                        print(f"\n💰 API Key计费:")
                        print(f"   总字符数: {billing.get('total_characters', 0):,}")
                        print(f"   总成本: ${billing.get('total_cost_usd', 0):.4f}")
                        print(f"   价格: ${billing.get('price_per_million_chars', 15):.2f}/百万字符")
                        print(f"   使用率: {billing.get('estimated_api_key_usage', 'N/A')}")
                        print(f"   最后更新: {billing.get('last_updated', 'N/A')}")
                    else:
                        print(f"\n💰 API Key计费: {billing.get('error', '获取失败')}")

                    # 队列状态 (替代轮询窗口)
                    queue = data.get("queue", {})
                    if queue:
                        print(f"\n⚡ 队列状态:")
                        print(f"   架构: {queue.get('architecture', 'N/A')}")
                        print(f"   队列名称: {queue.get('queue_name', 'N/A')}")
                        print(f"   最大并发: {queue.get('max_concurrency', 'N/A')}")
                        print(f"   批次大小: {queue.get('max_batch_size', 'N/A')}")
                        print(f"   状态: {queue.get('status', 'N/A')}")

                    # 系统状态
                    system = data.get("system", {})
                    print(f"\n🔧 系统状态:")
                    print(f"   健康状态: {'健康' if system.get('healthy') else '异常'}")
                    print(f"   数据库连接: {'正常' if system.get('database_connected') else '异常'}")
                    print(f"   Worker状态: {system.get('worker_status', 'unknown')}")
                    print(f"   检查时间: {data.get('timestamp', 'N/A')}")

                else:
                    print(f"❌ 查询失败: {data.get('message', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
        except Exception as e:
            print(f"❌ 获取系统状态异常: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="优化版TTS任务提交脚本 - 按单词完整提交")
    parser.add_argument("--limit", type=int, default=10, help="按优先级提交的单词数量限制")
    parser.add_argument("--words", nargs="+", help="指定要处理的单词列表")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--billing", action="store_true", help="查看API Key计费状态")
    parser.add_argument("--dashboard", action="store_true", help="查看TTS系统综合状态")

    args = parser.parse_args()

    # 数据库和配置路径
    db_path = "../../../content-assets/01_en/01_zh/sqlite/senseword_content_v4.db"
    config_path = args.config

    if not Path(db_path).exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return

    if not Path(config_path).exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return

    # 创建提交器
    submitter = OptimizedTTSSubmitter(db_path, config_path)

    # 根据参数选择运行模式
    if args.billing:
        # 查看计费状态
        submitter.get_billing_status()
    elif args.dashboard:
        # 查看系统综合状态
        submitter.get_dashboard_status()
    elif args.words:
        submitter.run_word_submission(args.words)
    else:
        submitter.run_priority_submission(args.limit)

if __name__ == "__main__":
    main()
