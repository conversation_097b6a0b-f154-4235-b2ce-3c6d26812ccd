#!/usr/bin/env python3
"""
TTS Assets 表结构迁移脚本

目标：为 tts_assets 表添加 word 和 frequency 字段，实现单表查询优化
性能提升：从双表关联查询改为单表索引查询，预期提升性能1000倍以上

数据规模：
- TTS资产: 1,306,098 条记录
- 单词: 55,703 个
- 频率类型: High, Medium, Medium-Low, Low, Rare

作者: AI Assistant
日期: 2025-07-16
"""

import sqlite3
import json
import time
from pathlib import Path
from typing import Dict, List, Tuple

class TTSAssetsMigrator:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None
        
    def connect(self):
        """连接数据库"""
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row
        print(f"✅ 已连接数据库: {self.db_path}")
        
    def disconnect(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            print("✅ 数据库连接已关闭")
    
    def backup_database(self):
        """备份整个数据库文件"""
        print("🔄 创建数据库备份...")
        try:
            import shutil
            from datetime import datetime

            # 创建备份目录
            backup_dir = Path(self.db_path).parent / "backup"
            backup_dir.mkdir(exist_ok=True)

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"senseword_content_v4_before_migration_{timestamp}.db"

            # 复制数据库文件
            shutil.copy2(self.db_path, backup_file)

            print(f"✅ 数据库备份完成: {backup_file}")
            return backup_file
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            raise
    
    def add_new_columns(self):
        """为 tts_assets 表添加新字段"""
        print("🔄 添加新字段...")
        try:
            # 添加 word 字段
            self.conn.execute("""
                ALTER TABLE tts_assets 
                ADD COLUMN word TEXT
            """)
            
            # 添加 frequency 字段，默认值为 'medium'
            self.conn.execute("""
                ALTER TABLE tts_assets 
                ADD COLUMN frequency TEXT DEFAULT 'medium'
            """)
            
            self.conn.commit()
            print("✅ 新字段添加完成: word, frequency")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("ℹ️ 字段已存在，跳过添加")
            else:
                raise
    
    def get_word_tts_mapping(self) -> Dict[str, Tuple[str, List[str]]]:
        """获取单词到TTS ID列表的映射"""
        print("🔄 创建单词-TTS映射...")

        cursor = self.conn.execute("""
            SELECT word, frequency, ttsHashList
            FROM words_for_publish
            WHERE ttsHashList IS NOT NULL AND ttsHashList != ''
        """)

        word_tts_map = {}
        total_tts_ids = 0

        for row in cursor:
            word = row['word']
            frequency = row['frequency'].lower()
            if frequency == 'medium-low':
                frequency = 'medium_low'

            # 解析 ttsHashList JSON
            try:
                tts_hash_list = json.loads(row['ttsHashList'])
                if isinstance(tts_hash_list, list) and tts_hash_list:
                    word_tts_map[word] = (frequency, tts_hash_list)
                    total_tts_ids += len(tts_hash_list)
            except (json.JSONDecodeError, TypeError):
                print(f"⚠️ 跳过无效的ttsHashList: {word}")
                continue

        print(f"✅ 映射创建完成: {len(word_tts_map)} 个单词, {total_tts_ids} 个TTS ID")
        return word_tts_map
    
    def migrate_data_by_word(self, word_tts_map: Dict[str, Tuple[str, List[str]]]):
        """按单词批量迁移数据"""
        print("🔄 开始按单词批量数据迁移...")

        total_words = len(word_tts_map)
        total_tts_ids = sum(len(tts_ids) for _, tts_ids in word_tts_map.values())

        print(f"📊 待处理: {total_words:,} 个单词, {total_tts_ids:,} 个TTS ID")

        processed_words = 0
        updated_tts_ids = 0
        start_time = time.time()

        # 按单词处理
        for word, (frequency, tts_id_list) in word_tts_map.items():
            try:
                # 准备批量更新数据
                updates = [(word, frequency, tts_id) for tts_id in tts_id_list]

                # 批量更新
                self.conn.executemany("""
                    UPDATE tts_assets
                    SET word = ?, frequency = ?
                    WHERE ttsId = ?
                """, updates)

                updated_tts_ids += len(updates)
                processed_words += 1

                # 每100个单词提交一次
                if processed_words % 100 == 0:
                    self.conn.commit()

                    # 显示进度
                    elapsed = time.time() - start_time
                    if elapsed > 0:
                        word_rate = processed_words / elapsed
                        tts_rate = updated_tts_ids / elapsed
                        eta = (total_words - processed_words) / word_rate if word_rate > 0 else 0

                        print(f"📈 进度: {processed_words:,}/{total_words:,} 单词 ({processed_words/total_words*100:.1f}%) "
                              f"更新TTS: {updated_tts_ids:,} "
                              f"速度: {word_rate:.0f}单词/秒, {tts_rate:.0f}TTS/秒 "
                              f"预计剩余: {eta:.0f}秒")

            except Exception as e:
                print(f"❌ 处理单词失败 {word}: {e}")
                continue

        # 最终提交
        self.conn.commit()

        elapsed = time.time() - start_time
        print(f"✅ 数据迁移完成:")
        print(f"   处理单词: {processed_words:,}/{total_words:,}")
        print(f"   更新TTS ID: {updated_tts_ids:,}/{total_tts_ids:,}")
        print(f"   总耗时: {elapsed:.1f}秒")
        print(f"   平均速度: {processed_words/elapsed:.1f}单词/秒, {updated_tts_ids/elapsed:.0f}TTS/秒")
    
    def create_indexes(self):
        """创建高性能索引"""
        print("🔄 创建索引...")
        
        indexes = [
            ("idx_tts_priority", "tts_assets", "frequency, status, word"),
            ("idx_tts_word_status", "tts_assets", "word, status"),
            ("idx_tts_frequency", "tts_assets", "frequency"),
            ("idx_tts_word", "tts_assets", "word")
        ]
        
        for idx_name, table, columns in indexes:
            try:
                self.conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS {idx_name} 
                    ON {table}({columns})
                """)
                print(f"✅ 索引创建: {idx_name}")
            except Exception as e:
                print(f"❌ 索引创建失败 {idx_name}: {e}")
        
        self.conn.commit()
        print("✅ 所有索引创建完成")
    
    def verify_migration(self):
        """验证迁移结果"""
        print("🔄 验证迁移结果...")
        
        # 检查字段是否存在
        cursor = self.conn.execute("PRAGMA table_info(tts_assets)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'word' not in columns or 'frequency' not in columns:
            print("❌ 新字段未正确添加")
            return False
        
        # 检查数据完整性
        cursor = self.conn.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(word) as with_word,
                COUNT(frequency) as with_frequency
            FROM tts_assets
        """)
        
        stats = cursor.fetchone()
        print(f"📊 数据统计:")
        print(f"   总记录数: {stats['total']:,}")
        print(f"   有word字段: {stats['with_word']:,}")
        print(f"   有frequency字段: {stats['with_frequency']:,}")
        
        # 检查频率分布
        cursor = self.conn.execute("""
            SELECT frequency, COUNT(*) as count
            FROM tts_assets
            WHERE frequency IS NOT NULL
            GROUP BY frequency
            ORDER BY count DESC
        """)

        print(f"📊 频率分布:")
        for row in cursor.fetchall():
            print(f"   {row['frequency']}: {row['count']:,}")

        # 检查单词分布
        cursor = self.conn.execute("""
            SELECT
                COUNT(DISTINCT word) as unique_words,
                COUNT(CASE WHEN word IS NOT NULL THEN 1 END) as with_word
            FROM tts_assets
        """)

        word_stats = cursor.fetchone()
        print(f"📊 单词统计:")
        print(f"   唯一单词数: {word_stats['unique_words']:,}")
        print(f"   有单词字段: {word_stats['with_word']:,}")

        # 验证成功条件
        success = (
            stats['with_word'] > 0 and
            stats['with_frequency'] > 0 and
            word_stats['unique_words'] > 0
        )

        if success:
            print("✅ 迁移验证通过")
        else:
            print("❌ 迁移验证失败")

        return success
    
    def run_migration(self):
        """执行完整迁移流程"""
        backup_file = None
        try:
            self.connect()

            print("🚀 开始 TTS Assets 表结构迁移")
            print("=" * 50)

            # 1. 备份整个数据库
            backup_file = self.backup_database()

            # 2. 添加字段
            self.add_new_columns()

            # 3. 获取单词-TTS映射
            word_tts_map = self.get_word_tts_mapping()

            # 4. 按单词迁移数据
            self.migrate_data_by_word(word_tts_map)

            # 5. 创建索引
            self.create_indexes()

            # 6. 验证结果
            if self.verify_migration():
                print("🎉 迁移成功完成！")
                print(f"💾 备份文件: {backup_file}")
            else:
                print("❌ 迁移验证失败")
                print(f"🔄 可使用备份文件恢复: {backup_file}")

        except Exception as e:
            print(f"❌ 迁移失败: {e}")
            if backup_file:
                print(f"🔄 可使用备份文件恢复: {backup_file}")
            raise
        finally:
            self.disconnect()

def main():
    """主函数"""
    # 数据库路径
    db_path = "../../../content-assets/01_en/01_zh/sqlite/senseword_content_v4.db"
    
    if not Path(db_path).exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    # 执行迁移
    migrator = TTSAssetsMigrator(db_path)
    migrator.run_migration()

if __name__ == "__main__":
    main()
