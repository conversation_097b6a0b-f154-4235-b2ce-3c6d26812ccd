#!/usr/bin/env python3
"""
大规模TTS任务智能协调器
基于02脚本的解耦设计，纯协调器模式

核心理念：
1. 充分利用02脚本的完善功能
2. 基于健康值进行智能调度
3. 解耦本地提交与云端处理
4. 自动延迟和重试机制
"""

import subprocess
import json
import time
import argparse
import logging
import os
import requests
from typing import List, Dict, Optional
from dataclasses import dataclass
from datetime import datetime
import sqlite3

@dataclass
class HealthConfig:
    """系统健康配置"""
    cost_threshold: float = 95.0          # API Key成本健康阈值(%)

    # 基于积压任务绝对数量的流量控制
    target_backlog_tasks: int = 200       # 目标积压任务数 - 系统最佳运行状态
    max_backlog_tasks: int = 500          # 最大积压任务数 - 超过此数量需要减速
    critical_backlog_tasks: int = 600    # 临界积压任务数 - 超过此数量需要暂停

    batch_size: int = 100000000           # 处理单词数量限制 (默认: 无限制)

    # 流量控制间隔配置
    turbo_interval: float = 0.5           # 涡轮模式间隔 - 积压不足时快速提交
    normal_interval: float = 2.0          # 正常间隔 - 积压适中时
    slow_interval: float = 5.0            # 减速间隔 - 积压过多时
    pause_interval: int = 60              # 暂停间隔 - 积压临界时等待1分钟

@dataclass
class SystemStatus:
    """系统状态"""
    current_cost: float
    pending_tasks: int
    total_tasks: int
    completion_rate: float
    can_submit: bool
    stop_reason: Optional[str] = None

@dataclass
class SystemHealth:
    """系统健康状态"""
    cost_usage: float           # 成本使用率(%)
    total_tasks: int           # 总任务数
    backlog_tasks: int         # 积压任务数（所有非completed任务）

    # 流量控制状态
    flow_mode: str             # 流量模式: 'turbo', 'normal', 'slow', 'pause', 'stop'
    interval: float            # 建议的提交间隔(秒)
    action_required: str       # 需要的操作: 'continue', 'wait_1min', 'stop_and_wait'
    reason: str                # 状态说明

class TTSCoordinator:
    def __init__(self, config_path: str = "config.json"):
        """初始化TTS智能协调器"""
        with open(config_path, 'r') as f:
            self.config = json.load(f)

        self.conn = None
        self.health_config = HealthConfig()
        self.script_02_path = "tts_02_submit_tasks_optimized.py"
        self.logger = None
        self.setup_logging()

    def setup_logging(self):
        """设置日志配置"""
        # 确保日志目录存在
        log_dir = "/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/workflow/08-音频生成/logs"
        os.makedirs(log_dir, exist_ok=True)

        # 生成日志文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"coordinator_{timestamp}.log")

        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(message)s'

        # 创建logger
        self.logger = logging.getLogger('TTSCoordinator')
        self.logger.setLevel(logging.INFO)

        # 清除已有的handlers
        self.logger.handlers.clear()

        # 文件handler - 详细日志
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter(log_format)
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # 控制台handler - 简洁输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # 只显示警告和错误
        console_formatter = logging.Formatter('%(levelname)s: %(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 记录启动信息
        self.logger.info("="*60)
        self.logger.info("TTS智能协调器启动")
        self.logger.info(f"日志文件: {log_file}")
        self.logger.info(f"配置文件: {self.config}")
        self.logger.info("="*60)

        # 终端显示简洁启动信息
        print(f"🚀 TTS智能协调器启动")
        print(f"📝 日志文件: {log_file}")
        print(f"🔧 配置: {len(self.config)} 项配置加载完成")

    def connect_db(self):
        """连接数据库"""
        db_path = self.config['database']['sqlitePath']
        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = sqlite3.Row
        self.logger.info(f"连接数据库: {db_path}")

    def get_word_tasks(self, word: str) -> List[Dict]:
        """获取单词的所有待处理TTS任务"""
        query = """
        SELECT ttsId, word, originalText, ttsType, textToSpeak
        FROM tts_assets
        WHERE word = ?
          AND (status IN ('pending', 'processing', 'failed') OR audioUrl IS NULL)
          AND status NOT IN ('completed', 'submitted')
        ORDER BY ttsType, originalText
        """

        cursor = self.conn.execute(query, (word,))
        tasks = []
        for row in cursor.fetchall():
            tasks.append({
                "ttsId": row[0],
                "word": row[1],
                "originalText": row[2],
                "ttsType": row[3],
                "textToSpeak": row[4]
            })

        return tasks

    def update_local_task_status(self, tasks: List[Dict], status: str) -> None:
        """更新本地数据库中任务的状态，实现最终一致性"""
        try:
            update_sql = """
            UPDATE tts_assets
            SET status = ?
            WHERE ttsId = ?
            """

            updated_count = 0
            for task in tasks:
                try:
                    cursor = self.conn.execute(update_sql, (status, task["ttsId"]))
                    if cursor.rowcount > 0:
                        updated_count += 1
                except Exception as e:
                    self.logger.error(f"更新任务状态失败 (ttsId: {task['ttsId']}): {e}")

            # 提交事务
            self.conn.commit()

            if updated_count > 0:
                self.logger.info(f"📝 本地状态同步: {updated_count} 个任务状态更新为 '{status}'")

        except Exception as e:
            self.logger.error(f"批量更新任务状态失败: {e}")
            # 回滚事务
            self.conn.rollback()



    def submit_word_tasks(self, word: str) -> Dict:
        """直接提交单个单词的所有TTS任务到Worker"""
        try:
            # 获取单词的待处理任务
            tasks = self.get_word_tasks(word)

            if not tasks:
                self.logger.info(f"单词 {word} 没有待处理任务")
                return {
                    "success": True,
                    "submitted": 0,
                    "billing": {"total_cost_usd": 0.0},
                    "system_stats": {"total": 0, "completed": 0, "backlog": 0},
                    "submitted_words": 0
                }

            self.logger.info(f"提交单词: {word} ({len(tasks)} 个任务)")

            # 准备提交数据
            submit_data = {
                "word": word,
                "tasks": [
                    {
                        "ttsId": task["ttsId"],
                        "text": task["textToSpeak"],
                        "type": task["ttsType"]
                    }
                    for task in tasks
                ]
            }

            # 提交到Worker
            worker_url = f"{self.config['worker']['baseUrl']}{self.config['worker']['submitEndpoint']}"
            response = requests.post(
                worker_url,
                json=submit_data,
                headers={"Content-Type": "application/json"},
                timeout=300  # 5分钟超时
            )

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result and result.get("success"):
                        self.logger.info(f"✅ {word}: {len(tasks)} 个任务提交成功")

                        # 更新本地数据库状态 - 实现最终一致性
                        self.update_local_task_status(tasks, "submitted")

                        # 提取计费和系统状态信息
                        billing = result.get("billing", {}) or {}
                        queue_info = result.get("queue_info", {}) or {}

                        # 本地计算成本（更准确）
                        total_characters = sum(len(task["textToSpeak"]) for task in tasks)
                        estimated_cost = (total_characters / 1_000_000) * 15.0  # $15/百万字符

                        # 转换为协调器期望的格式
                        billing_info = {
                            "total_cost_usd": billing.get("totalCostUSD", estimated_cost)
                        }

                        # 从队列信息推断系统状态
                        queued_tasks = queue_info.get("queued_tasks", 0)
                        stats_info = {
                            "total": result.get("received", 0),
                            "completed": result.get("inserted", 0),
                            "backlog": queued_tasks  # 积压 = 队列中的任务数
                        }

                        self.logger.info(f"计费信息: {billing_info}")
                        self.logger.info(f"系统状态: {stats_info}")

                        return {
                            "success": True,
                            "submitted": len(tasks),
                            "billing": billing_info,
                            "system_stats": stats_info,
                            "submitted_words": 1
                        }
                    else:
                        self.logger.error(f"❌ {word}: 提交失败 - {result}")
                        return {
                            "success": False,
                            "error": f"Worker返回失败: {result}",
                            "submitted_words": 0
                        }
                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ {word}: JSON解析失败 - {e}")
                    self.logger.error(f"响应内容: {response.text[:500]}")
                    return {
                        "success": False,
                        "error": f"JSON解析失败: {e}",
                        "submitted_words": 0
                    }
            else:
                self.logger.error(f"❌ {word}: HTTP {response.status_code}")
                self.logger.error(f"响应内容: {response.text[:500]}")
                return {
                    "success": False,
                    "error": f"HTTP错误: {response.status_code}",
                    "submitted_words": 0
                }

        except Exception as e:
            self.logger.error(f"提交单词任务失败: {e}")
            return {"success": False, "error": str(e), "submitted_words": 0}

    def evaluate_system_health(self, billing_info: Dict, system_stats: Dict) -> SystemHealth:
        """基于积压任务绝对数量评估系统健康状态并确定流量控制策略"""
        if not billing_info or not system_stats:
            return SystemHealth(
                cost_usage=0.0,
                total_tasks=0,
                backlog_tasks=0,
                flow_mode="stop",
                interval=0.0,
                action_required="stop_and_wait",
                reason="无法获取系统状态信息"
            )

        # 获取基础数据
        current_cost = billing_info.get('total_cost_usd', 0.0)
        # 从环境变量或配置中获取成本限额，默认200美元
        cost_limit = float(os.getenv('API_KEY_COST_LIMIT_USD', '200'))
        cost_usage = (current_cost / cost_limit) * 100
        total_tasks = system_stats.get('total', 0)
        backlog_tasks = system_stats.get('backlog', 0)  # 积压任务数（所有非completed任务）

        # 优先级1: 成本问题 - 立即停止
        if cost_usage >= self.health_config.cost_threshold:
            return SystemHealth(
                cost_usage=cost_usage,
                total_tasks=total_tasks,
                backlog_tasks=backlog_tasks,
                flow_mode="stop",
                interval=0.0,
                action_required="stop_and_wait",
                reason=f"🚨 API Key成本达到上限: {cost_usage:.1f}% >= {self.health_config.cost_threshold}%"
            )

        # 优先级2: 基于积压任务绝对数量的流量控制
        if backlog_tasks >= self.health_config.critical_backlog_tasks:
            # 临界积压 - 暂停提交
            flow_mode = "pause"
            interval = self.health_config.pause_interval
            action_required = "wait_1min"
            reason = f"⏸️ 积压任务临界: {backlog_tasks} >= {self.health_config.critical_backlog_tasks}，暂停提交"

        elif backlog_tasks >= self.health_config.max_backlog_tasks:
            # 积压过多 - 减速提交
            flow_mode = "slow"
            interval = self.health_config.slow_interval
            action_required = "continue"
            reason = f"🐌 积压任务过多: {backlog_tasks} >= {self.health_config.max_backlog_tasks}，减速提交"

        elif backlog_tasks >= self.health_config.target_backlog_tasks:
            # 积压适中 - 正常提交
            flow_mode = "normal"
            interval = self.health_config.normal_interval
            action_required = "continue"
            reason = f"✅ 积压任务适中: {backlog_tasks} >= {self.health_config.target_backlog_tasks}，正常提交"

        else:
            # 积压不足 - 涡轮提交
            flow_mode = "turbo"
            interval = self.health_config.turbo_interval
            action_required = "continue"
            reason = f"🚀 积压任务不足: {backlog_tasks} < {self.health_config.target_backlog_tasks}，涡轮提交"

        return SystemHealth(
            cost_usage=cost_usage,
            total_tasks=total_tasks,
            backlog_tasks=backlog_tasks,
            flow_mode=flow_mode,
            interval=interval,
            action_required=action_required,
            reason=reason
        )



    def get_priority_words(self) -> List[Dict]:
        """获取按频率排序的所有待处理单词列表"""
        self.logger.info("查询所有待处理的优先单词...")

        query = """
        SELECT DISTINCT word, frequency, COUNT(*) as task_count
        FROM tts_assets
        WHERE status IN ('failed', 'processing', 'pending')
          AND audioUrl IS NULL
          AND word IS NOT NULL
        GROUP BY word, frequency
        ORDER BY
          CASE frequency
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'medium_low' THEN 3
            WHEN 'low' THEN 4
            WHEN 'rare' THEN 5
            ELSE 6
          END,
          word
        """

        cursor = self.conn.execute(query)
        words = [dict(row) for row in cursor.fetchall()]
        
        # 统计信息
        total_tasks = sum(word['task_count'] for word in words)
        freq_stats = {}
        for word in words:
            freq = word['frequency']
            freq_stats[freq] = freq_stats.get(freq, 0) + 1
        
        self.logger.info(f"找到 {len(words)} 个优先单词，预估 {total_tasks} 个任务")
        self.logger.info("频率分布:")
        for freq, count in sorted(freq_stats.items()):
            self.logger.info(f"   {freq}: {count} 个单词")
        
        return words



    def run_intelligent_coordination(self):
        """运行智能协调提交"""
        try:
            self.connect_db()

            self.logger.info("开始TTS智能协调提交")
            self.logger.info("="*60)
            self.logger.info(f"成本健康阈值: {self.health_config.cost_threshold}%")
            self.logger.info(f"目标积压任务数: {self.health_config.target_backlog_tasks}")
            self.logger.info(f"最大积压任务数: {self.health_config.max_backlog_tasks}")
            self.logger.info(f"临界积压任务数: {self.health_config.critical_backlog_tasks}")
            self.logger.info(f"处理单词数量限制: {self.health_config.batch_size}")
            self.logger.info("="*60)

            # 终端简洁显示
            print(f"📊 开始处理所有待处理的优先单词")

            # 获取优先单词列表
            priority_words = self.get_priority_words()

            if not priority_words:
                self.logger.warning("没有待处理的优先单词")
                print("⚠️ 没有待处理的优先单词")
                return

            # 单词级智能协调循环 (支持batch_size限制)
            total_words = min(len(priority_words), self.health_config.batch_size)
            actual_words = priority_words[:total_words]  # 只处理指定数量的单词
            total_processed = 0
            total_submitted = 0
            total_failed = 0

            self.logger.info(f"计划处理单词数: {total_words} (数据库总数: {len(priority_words)})")
            print(f"📊 计划处理 {total_words} 个单词 (数据库总数: {len(priority_words)})")

            i = 0
            while i < len(actual_words):
                word_info = actual_words[i]
                word = word_info['word']
                word_num = i + 1

                self.logger.info(f"处理单词 {word_num}/{total_words}: {word}")

                # 直接提交单个单词任务
                result = self.submit_word_tasks(word)

                if result["success"]:
                    # 简化处理逻辑 - 移除复杂的健康评估和延迟
                    total_processed += result.get("submitted_words", 0)
                    total_submitted += result.get("submitted", 0)

                    # 获取基本状态信息
                    billing = result.get("billing", {})
                    system_stats = result.get("system_stats", {})
                    current_cost = billing.get("total_cost_usd", 0.0)
                    current_backlog = system_stats.get("backlog", 0)

                    self.logger.info(f"✅ 单词 {word} 提交成功: {result.get('submitted', 0)} 个任务")

                    # 终端简洁显示进度
                    progress = (word_num / total_words) * 100
                    print(f"📈 进度: {word_num}/{total_words} ({progress:.1f}%) | 积压: {current_backlog} | 已提交: {total_submitted} | 成本: ${current_cost:.2f}")

                    # 简单的成本检查 - 如果超过预算则停止
                    if current_cost >= 190.0:  # 接近 $200 限制时停止
                        self.logger.warning(f"成本接近限制 (${current_cost:.2f}/$200)，停止提交")
                        print(f"💰 成本接近限制，已停止提交。请更换 API Key 后继续。")
                        break

                else:
                    # 提交失败
                    total_failed += 1
                    self.logger.error(f"❌ 单词 {word} 提交失败: {result.get('error', '未知错误')}")
                    print(f"❌ 单词 {word} 失败")

                # 处理下一个单词
                i += 1

            # 最终统计
            success_rate = (total_submitted / (total_submitted + total_failed) * 100) if (total_submitted + total_failed) > 0 else 0

            self.logger.info("="*60)
            self.logger.info("智能协调完成统计:")
            self.logger.info(f"   处理单词数: {total_processed}")
            self.logger.info(f"   提交成功: {total_submitted}")
            self.logger.info(f"   提交失败: {total_failed}")
            self.logger.info(f"   成功率: {success_rate:.1f}%")
            self.logger.info("="*60)

            # 终端简洁显示最终结果
            print(f"\n✅ 协调完成: {total_submitted} 成功, {total_failed} 失败 ({success_rate:.1f}%)")

        except Exception as e:
            self.logger.error(f"智能协调失败: {e}")
            print(f"❌ 协调器异常: {e}")
        finally:
            if self.conn:
                self.conn.close()
                self.logger.info("数据库连接已关闭")
            self.logger.info("TTS智能协调器结束")

def main():
    parser = argparse.ArgumentParser(description="TTS智能协调器 - 快速批量提交模式")
    parser.add_argument("--batch-size", type=int, default=100, help="处理单词数量 (默认: 100)")
    parser.add_argument("--config", default="config.json", help="配置文件路径")

    args = parser.parse_args()

    coordinator = TTSCoordinator(args.config)
    coordinator.health_config.batch_size = args.batch_size
    coordinator.run_intelligent_coordination()

if __name__ == "__main__":
    main()
