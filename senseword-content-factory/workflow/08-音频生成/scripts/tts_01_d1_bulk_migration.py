#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TTS D1 批量迁移脚本
====================

功能：
1. 扫描本地 SQLite 数据库中的所有 TTS 任务
2. 生成批量 SQL 文件（支持重复检查）
3. 安全执行迁移到 Cloudflare D1
4. 用户确认机制，防止误操作

作者：TTS 系统
日期：2025-07-17
"""

import os
import sys
import json
import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional
import subprocess
from pathlib import Path

class TTSD1BulkMigration:
    def __init__(self, config_path: str = "config.json"):
        """初始化迁移器"""
        self.setup_logging()
        self.load_config(config_path)
        self.migration_dir = Path("/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/cloudflare/d1/tts-db/migrations/local_sync")
        self.migration_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("🚀 TTS D1 批量迁移器初始化完成")
        self.logger.info(f"📁 迁移文件目录: {self.migration_dir}")

    def setup_logging(self):
        """设置日志"""
        log_dir = Path("../logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"d1_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            self.logger.info(f"✅ 配置文件加载成功: {config_path}")
        except Exception as e:
            self.logger.error(f"❌ 配置文件加载失败: {e}")
            sys.exit(1)

    def connect_database(self):
        """连接本地数据库"""
        try:
            db_path = self.config['database']['sqlitePath']
            self.conn = sqlite3.connect(db_path)
            self.conn.row_factory = sqlite3.Row
            self.logger.info(f"✅ 数据库连接成功: {db_path}")
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            sys.exit(1)

    def scan_all_tts_tasks(self) -> List[Dict]:
        """扫描所有 TTS 任务"""
        self.logger.info("🔍 开始扫描本地 TTS 任务...")

        # 只提取 D1 迁移需要的字段
        query = """
        SELECT ttsId, ttsType, textToSpeak
        FROM tts_assets
        ORDER BY ttsId
        """

        cursor = self.conn.execute(query)
        tasks = []

        for row in cursor.fetchall():
            tasks.append({
                "ttsId": row["ttsId"],
                "ttsType": row["ttsType"],
                "textToSpeak": row["textToSpeak"]
            })

        self.logger.info(f"📊 扫描完成，找到 {len(tasks)} 个 TTS 任务")
        return tasks



    def generate_sql_batches(self, tasks: List[Dict], batch_size: int = 50000) -> List[str]:
        """生成批量 SQL 文件"""
        self.logger.info(f"📝 开始生成 SQL 文件，批次大小: {batch_size}")
        
        sql_files = []
        total_batches = (len(tasks) + batch_size - 1) // batch_size
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(tasks))
            batch_tasks = tasks[start_idx:end_idx]
            
            # 生成文件名（简洁格式：只有序号）
            filename = f"migration_batch_{batch_num + 1:03d}_of_{total_batches:03d}.sql"
            filepath = self.migration_dir / filename
            
            # 生成 SQL 内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"-- TTS 任务批量迁移 - 批次 {batch_num + 1}/{total_batches}\n")
                f.write(f"-- 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"-- 任务数量: {len(batch_tasks)}\n\n")
                
                for task in batch_tasks:
                    # 映射到 D1 表结构：只保留 D1 需要的字段
                    sql = f"""INSERT INTO tts_tasks (ttsId, text, type, status)
SELECT '{self.escape_sql(task['ttsId'])}',
       '{self.escape_sql(task['textToSpeak'])}',
       '{self.escape_sql(task['ttsType'])}',
       'pending'
WHERE NOT EXISTS (SELECT 1 FROM tts_tasks WHERE ttsId = '{self.escape_sql(task['ttsId'])}');

"""
                    f.write(sql)
            
            sql_files.append(str(filepath))
            self.logger.info(f"✅ 生成批次 {batch_num + 1}/{total_batches}: {filename} ({len(batch_tasks)} 个任务)")
        
        self.logger.info(f"🎉 所有 SQL 文件生成完成，共 {len(sql_files)} 个文件")
        return sql_files

    def escape_sql(self, value: str) -> str:
        """SQL 字符串转义"""
        if value is None:
            return ""
        return str(value).replace("'", "''")

    def preview_migration(self, sql_files: List[str]):
        """预览迁移信息"""
        print("\n" + "="*60)
        print("🔍 迁移预览")
        print("="*60)
        print(f"📁 迁移文件目录: {self.migration_dir}")
        print(f"📊 SQL 文件数量: {len(sql_files)}")
        print(f"📝 文件列表:")
        
        for i, filepath in enumerate(sql_files, 1):
            filename = Path(filepath).name
            file_size = Path(filepath).stat().st_size
            print(f"   {i:2d}. {filename} ({file_size:,} bytes)")
        
        total_size = sum(Path(f).stat().st_size for f in sql_files)
        print(f"💾 总文件大小: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        print("="*60)

    def confirm_migration(self, auto_confirm: bool = False) -> bool:
        """用户确认迁移"""
        print("\n⚠️  重要提醒:")
        print("   1. 此操作将向 Cloudflare D1 数据库写入大量数据")
        print("   2. 已存在的任务会被跳过（基于 ttsId）")
        print("   3. 新任务状态将设置为 'pending'")
        print("   4. 迁移过程可能需要较长时间")
        print("   5. 请确保网络连接稳定")

        if auto_confirm:
            print("\n✅ 自动确认模式，开始执行迁移...")
            return True

        while True:
            response = input("\n🤔 确认执行迁移吗？(yes/no): ").strip().lower()
            if response in ['yes', 'y']:
                return True
            elif response in ['no', 'n']:
                return False
            else:
                print("❌ 请输入 'yes' 或 'no'")

    def execute_migration(self, sql_files: List[str], database_name: str = "tts-database"):
        """执行迁移"""
        self.logger.info(f"🚀 开始执行迁移到数据库: {database_name}")
        
        success_count = 0
        failed_files = []
        
        for i, sql_file in enumerate(sql_files, 1):
            filename = Path(sql_file).name
            self.logger.info(f"📤 执行批次 {i}/{len(sql_files)}: {filename}")
            
            try:
                # 执行 wrangler d1 execute 命令
                cmd = [
                    "wrangler", "d1", "execute", database_name,
                    "--file", sql_file
                ]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5分钟超时
                )
                
                if result.returncode == 0:
                    success_count += 1
                    self.logger.info(f"✅ 批次 {i} 执行成功")
                else:
                    failed_files.append((sql_file, result.stderr))
                    self.logger.error(f"❌ 批次 {i} 执行失败: {result.stderr}")
                
            except subprocess.TimeoutExpired:
                failed_files.append((sql_file, "执行超时"))
                self.logger.error(f"❌ 批次 {i} 执行超时")
            except Exception as e:
                failed_files.append((sql_file, str(e)))
                self.logger.error(f"❌ 批次 {i} 执行异常: {e}")
        
        # 输出执行结果
        print(f"\n🎉 迁移执行完成!")
        print(f"✅ 成功: {success_count}/{len(sql_files)} 个批次")
        print(f"❌ 失败: {len(failed_files)} 个批次")
        
        if failed_files:
            print("\n❌ 失败的批次:")
            for sql_file, error in failed_files:
                filename = Path(sql_file).name
                print(f"   - {filename}: {error}")

    def run_migration(self, batch_size: int = 50000, database_name: str = "tts-database", auto_confirm: bool = False):
        """运行完整迁移流程"""
        try:
            # 1. 连接数据库
            self.connect_database()

            # 2. 扫描本地所有任务（全表迁移）
            all_tasks = self.scan_all_tts_tasks()
            if not all_tasks:
                self.logger.warning("⚠️ 没有找到本地任务")
                return

            # 3. 生成 SQL 文件（包含重复检查逻辑）
            sql_files = self.generate_sql_batches(all_tasks, batch_size)

            # 4. 预览迁移
            self.preview_migration(sql_files)

            # 5. 用户确认
            if not self.confirm_migration(auto_confirm):
                self.logger.info("❌ 用户取消迁移")
                return

            # 6. 执行迁移
            self.execute_migration(sql_files, database_name)
            
        except Exception as e:
            self.logger.error(f"❌ 迁移过程发生错误: {e}")
        finally:
            if hasattr(self, 'conn'):
                self.conn.close()
                self.logger.info("📝 数据库连接已关闭")

def main():
    import argparse

    parser = argparse.ArgumentParser(description="TTS D1 批量迁移工具")
    parser.add_argument("--batch-size", type=int, default=50000, help="批次大小 (默认: 50000)")
    parser.add_argument("--database", default="tts-database", help="D1 数据库名称 (默认: tts-database)")
    parser.add_argument("--config", default="config.json", help="配置文件路径")
    parser.add_argument("--auto-confirm", action="store_true", help="自动确认，跳过用户输入")

    args = parser.parse_args()

    migrator = TTSD1BulkMigration(args.config)
    migrator.run_migration(args.batch_size, args.database, args.auto_confirm)

if __name__ == "__main__":
    main()
