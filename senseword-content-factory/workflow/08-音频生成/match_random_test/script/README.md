# TTS音频质量抽样检查脚本

该脚本用于从TTS Worker获取已完成任务的样本数据，并将其保存为CSV文件以便进行人工审核。

## 功能特性

- 支持按小时获取任务样本
- 可筛选特定类型的TTS任务
- 可限制返回的样本数量
- 支持按类型分别获取样本（每种类型指定数量）
- 自动保存为CSV格式文件

## 使用方法

### 基本用法

```bash
python sample_tts_quality_check.py
```

默认将获取最近1小时内完成的30个任务样本。

### 按类型分别获取样本

```bash
python sample_tts_quality_check.py --by-type
```

将获取最近1小时内每种类型各5个任务样本，总共20个样本（4种类型×5个样本）。

### 命令行参数

| 参数 | 默认值 | 描述 |
|------|--------|------|
| `--output-dir` | `../csv/` | 输出目录路径 |
| `--hour-offset` | -1 | 小时偏移 (0=不限制, -1=最近1小时[默认], -2=最近2小时) |
| `--type` | None | 类型筛选 (example_sentence, phrase_breakdown, phonetic_name, phonetic_bre) |
| `--limit` | 30 | 返回数量 (最大100) |
| `--by-type` | False | 按类型分别获取样本 |
| `--limit-per-type` | 5 | 每种类型的样本数量 |
| `--verbose` 或 `-v` | False | 详细输出 |

### 示例

1. 获取最近2小时内完成的10个任务样本：
```bash
python sample_tts_quality_check.py --hour-offset -2 --limit 10
```

2. 按类型分别获取最近1小时内每种类型的样本（每种类型5个）：
```bash
python sample_tts_quality_check.py --by-type --hour-offset -1
```

3. 按类型分别获取最近2小时内每种类型的样本（每种类型10个）：
```bash
python sample_tts_quality_check.py --by-type --hour-offset -2 --limit-per-type 10
```

4. 获取最近1小时内完成的example_sentence类型任务样本（最多50个）：
```bash
python sample_tts_quality_check.py --type example_sentence --limit 50
```

## 注意事项

1. Worker端点和基础URL已硬编码在脚本中
2. 生成的CSV文件将保存在指定的输出目录中，文件名包含时间戳以避免重复
3. 使用`--by-type`参数时，`--type`参数将被忽略

## 输出格式

生成的CSV文件包含以下字段：
- `ttsId`: TTS任务ID
- `text`: 音频对应的文本
- `type`: 任务类型
- `audioUrl`: 音频文件URL
- `completedAt`: 任务完成时间