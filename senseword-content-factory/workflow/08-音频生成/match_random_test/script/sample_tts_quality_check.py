#!/usr/bin/env python3
"""
TTS音频质量抽样测试脚本

功能：
1. 从TTS Worker获取今天完成的任务样本
2. 将样本数据转换为CSV格式
3. 保存到指定目录便于人工审核

使用方法：
python sample_tts_quality_check.py [--config-path CONFIG] [--output-dir OUTPUT] [--days-offset DAYS]

默认查询昨天的数据（--days-offset -1），因为数据库使用UTC时间
"""

import requests
import csv
import json
import datetime
import argparse
from pathlib import Path
from typing import List, Dict, Optional


class TTSSampleChecker:
    """TTS音频质量抽样检查器"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化检查器
        
        Args:
            output_dir: 输出目录，默认使用../csv/
        """
        self.output_dir = Path(output_dir or "../csv/")
        
        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 硬编码配置
        self.base_url = "https://senseword-tts-worker.zhouqi-aaha.workers.dev"
        self.sample_endpoint = "/sample-completed-tasks"
    
    def _load_config(self):
        """加载配置文件"""
        # 配置已硬编码，此方法已废弃
        pass
    
    def get_samples_by_type(self, hour_offset: int = -1, limit_per_type: int = 5) -> List[Dict]:
        """
        按类型分别获取样本任务数据

        Args:
            hour_offset: 小时偏移，-1=最近1小时，-2=最近2小时
            limit_per_type: 每种类型的样本数量，默认5

        Returns:
            所有类型的样本任务列表
        """
        # 定义需要获取样本的类型列表
        types = ['example_sentence', 'phrase_breakdown', 'phonetic_name', 'phonetic_bre']
        
        all_samples = []
        
        # 分别获取每种类型的样本
        for tts_type in types:
            try:
                samples = self.fetch_sample_tasks(hour_offset, tts_type, limit_per_type)
                all_samples.extend(samples)
                print(f"✅ 获取到 {len(samples)} 个 {tts_type} 类型的样本")
            except Exception as e:
                print(f"❌ 获取 {tts_type} 类型样本失败: {e}")
        
        return all_samples
    
    def fetch_sample_tasks(self, hour_offset: int = -1, type_filter: str = None, limit: int = 10) -> List[Dict]:
        """
        从Worker获取样本任务数据

        Args:
            hour_offset: 小时偏移，-1=最近1小时，-2=最近2小时
            type_filter: 类型筛选，如'example_sentence'
            limit: 返回数量，默认10，最大100

        Returns:
            样本任务列表
        """
        try:
            url = f"{self.base_url}{self.sample_endpoint}"
            params = []

            # 添加hour_offset参数
            if hour_offset != 0:
                params.append(f"hour_offset={hour_offset}")
                
            if type_filter:
                params.append(f"type={type_filter}")
            if limit != 10:
                params.append(f"limit={limit}")

            if params:
                url += "?" + "&".join(params)

            print(f"🔍 正在获取样本数据: {url}")
            
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if not data.get('success'):
                error_msg = data.get('error', 'Unknown error')
                print(f"❌ API返回错误: {error_msg}")
                return []
            
            samples = data.get('samples', [])
            count = data.get('count', 0)
            query_date = data.get('queryDate', 'Unknown')
            
            print(f"✅ 获取成功: {count} 个样本任务 (日期: {query_date})")
            return samples
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return []
        except Exception as e:
            print(f"❌ 获取样本数据失败: {e}")
            return []
    
    def get_samples_by_type(self, hour_offset: int = -1, limit_per_type: int = 5) -> List[Dict]:
        """
        按类型分别获取样本任务数据

        Args:
            hour_offset: 小时偏移，-1=最近1小时，-2=最近2小时
            limit_per_type: 每种类型的样本数量，默认5

        Returns:
            所有类型的样本任务列表
        """
        # 定义需要获取样本的类型列表
        types = ['example_sentence', 'phrase_breakdown', 'phonetic_name', 'phonetic_bre']
        
        all_samples = []
        
        # 分别获取每种类型的样本
        for tts_type in types:
            try:
                samples = self.fetch_sample_tasks(hour_offset, tts_type, limit_per_type)
                all_samples.extend(samples)
                print(f"✅ 获取到 {len(samples)} 个 {tts_type} 类型的样本")
            except Exception as e:
                print(f"❌ 获取 {tts_type} 类型样本失败: {e}")
        
        return all_samples
    
    def save_to_csv(self, samples: List[Dict]) -> Optional[str]:
        """
        将样本数据保存为CSV文件
        
        Args:
            samples: 样本任务列表
            
        Returns:
            保存的文件路径，失败时返回None
        """
        if not samples:
            print("⚠️ 没有样本数据需要保存")
            return None
        
        try:
            # 生成文件名（包含时间戳）
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tts_quality_samples_{timestamp}.csv"
            filepath = self.output_dir / filename
            
            # CSV列定义
            fieldnames = ['ttsId', 'text', 'type', 'audioUrl', 'completedAt']
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # 写入表头
                writer.writeheader()
                
                # 写入数据行
                for sample in samples:
                    # 确保所有字段都存在
                    row = {field: sample.get(field, '') for field in fieldnames}
                    writer.writerow(row)
            
            print(f"✅ CSV文件保存成功: {filepath}")
            print(f"📊 共保存 {len(samples)} 条记录")
            
            return str(filepath)
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return None
    
    def run_quality_check(self, hour_offset: int = -1, type_filter: str = None, limit: int = 10, by_type: bool = False, limit_per_type: int = 5) -> Dict:
        """
        执行完整的质量检查流程

        Args:
            hour_offset: 小时偏移，-1=最近1小时，-2=最近2小时
            type_filter: 类型筛选，如'example_sentence'
            limit: 返回数量，默认10，最大100
            by_type: 是否按类型分别获取样本
            limit_per_type: 每种类型的样本数量，默认5

        Returns:
            执行结果统计
        """
        date_desc = f"最近{abs(hour_offset)}小时"
        type_desc = f" (类型: {type_filter})" if type_filter else ""
        print(f"🚀 开始TTS音频质量抽样检查 ({date_desc}{type_desc}, 数量: {limit})")
        print("=" * 60)

        start_time = datetime.datetime.now()

        # 1. 获取样本数据
        if by_type:
            # 按类型分别获取样本
            print(f"🔄 按类型分别获取样本，每种类型{limit_per_type}个")
            samples = self.get_samples_by_type(hour_offset, limit_per_type)
        elif type_filter:
            # 获取特定类型的样本
            samples = self.fetch_sample_tasks(hour_offset, type_filter, limit)
        else:
            # 获取所有类型的样本
            samples = self.fetch_sample_tasks(hour_offset, None, limit)
        
        # 2. 保存为CSV
        csv_file = None
        if samples:
            csv_file = self.save_to_csv(samples)
        
        # 3. 生成统计报告
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        result = {
            'success': len(samples) > 0,
            'sample_count': len(samples),
            'csv_file': csv_file,
            'duration_seconds': duration,
            'timestamp': start_time.isoformat()
        }
        
        print("\n📋 执行结果统计:")
        print(f"   样本数量: {result['sample_count']}")
        print(f"   CSV文件: {result['csv_file'] or '未生成'}")
        print(f"   执行时间: {result['duration_seconds']:.2f}秒")
        print(f"   执行状态: {'成功' if result['success'] else '失败'}")
        
        return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TTS音频质量抽样测试')
    parser.add_argument('--output-dir', help='输出目录路径')
    parser.add_argument('--hour-offset', type=int, default=-1, help='小时偏移 (0=不限制, -1=最近1小时[默认], -2=最近2小时)')
    parser.add_argument('--type', help='类型筛选 (example_sentence, phrase_breakdown, phonetic_name, phonetic_bre)')
    parser.add_argument('--limit', type=int, default=30, help='返回数量 (默认30，最大100)')
    parser.add_argument('--by-type', action='store_true', help='按类型分别获取样本，每种类型默认5个')
    parser.add_argument('--limit-per-type', type=int, default=5, help='每种类型的样本数量 (默认5)')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    try:
        # 创建检查器实例
        checker = TTSSampleChecker(
            output_dir=args.output_dir
        )

        # 执行质量检查
        result = checker.run_quality_check(
            hour_offset=args.hour_offset,
            type_filter=args.type,
            limit=args.limit,
            by_type=args.by_type,
            limit_per_type=args.limit_per_type
        )
        
        # 根据结果设置退出码
        exit_code = 0 if result['success'] else 1
        
        if args.verbose:
            print(f"\n🔍 详细结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        exit(130)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
