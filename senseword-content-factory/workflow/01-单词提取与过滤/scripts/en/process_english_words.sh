#!/bin/bash
"""
英语单词提取与过滤一键处理脚本
整合提取和过滤两个步骤，简化使用流程
"""

# 检查参数
if [ $# -lt 2 ]; then
    echo "用法: $0 <输入路径> <输出文件名> [选项]"
    echo ""
    echo "参数说明:"
    echo "  输入路径    - 词典文件或文件夹路径"
    echo "  输出文件名  - 输出的TSV文件名（不含扩展名）"
    echo ""
    echo "选项:"
    echo "  --recursive  - 递归处理文件夹"
    echo "  --verbose    - 详细输出模式"
    echo ""
    echo "示例:"
    echo "  $0 cambridge_dict.txt cambridge_words"
    echo "  $0 /path/to/dicts/ all_words --recursive --verbose"
    exit 1
fi

INPUT_PATH="$1"
OUTPUT_NAME="$2"
shift 2

# 解析选项
RECURSIVE=""
VERBOSE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --recursive)
            RECURSIVE="--recursive"
            shift
            ;;
        --verbose)
            VERBOSE="--verbose"
            shift
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 临时文件名
TEMP_RAW="temp_${OUTPUT_NAME}_raw.txt"
FINAL_OUTPUT="${OUTPUT_NAME}.tsv"

echo "英语单词提取与过滤处理"
echo "=================================="
echo "输入路径: $INPUT_PATH"
echo "输出文件: $FINAL_OUTPUT"
echo "递归处理: $([ -n "$RECURSIVE" ] && echo "是" || echo "否")"
echo "详细输出: $([ -n "$VERBOSE" ] && echo "是" || echo "否")"
echo ""

# 检查输入路径是否存在
if [ ! -e "$INPUT_PATH" ]; then
    echo "错误: 输入路径不存在: $INPUT_PATH"
    exit 1
fi

# 步骤1: 提取单词
echo "步骤1: 提取单词..."
echo "=================================="
python3 "$SCRIPT_DIR/01_extract_words.py" \
    --input "$INPUT_PATH" \
    --output "$TEMP_RAW" \
    $RECURSIVE $VERBOSE

# 检查提取是否成功
if [ $? -ne 0 ]; then
    echo "错误: 单词提取失败"
    exit 1
fi

if [ ! -f "$TEMP_RAW" ]; then
    echo "错误: 提取结果文件未生成"
    exit 1
fi

echo ""
echo "步骤2: 过滤单词..."
echo "=================================="

# 步骤2: 过滤单词
python3 "$SCRIPT_DIR/02_filter_words.py" \
    --input "$TEMP_RAW" \
    --output "$FINAL_OUTPUT" \
    $VERBOSE

# 检查过滤是否成功
if [ $? -ne 0 ]; then
    echo "错误: 单词过滤失败"
    # 清理临时文件
    rm -f "$TEMP_RAW"
    exit 1
fi

# 清理临时文件
rm -f "$TEMP_RAW"

echo ""
echo "处理完成!"
echo "=================================="
echo "输出文件: $FINAL_OUTPUT"

# 显示结果统计
if [ -f "$FINAL_OUTPUT" ]; then
    WORD_COUNT=$(wc -l < "$FINAL_OUTPUT")
    echo "最终单词数: $WORD_COUNT"
    
    # 显示前10个单词作为预览
    echo ""
    echo "预览前10个单词:"
    head -10 "$FINAL_OUTPUT" | sed 's/^/  /'
    
    if [ $WORD_COUNT -gt 10 ]; then
        echo "  ..."
        echo "  (共 $WORD_COUNT 个单词)"
    fi
fi

# 检查是否有统计报告
REPORT_FILE="${OUTPUT_NAME}.tsv.report.txt"
if [ -f "$REPORT_FILE" ]; then
    echo ""
    echo "统计报告: $REPORT_FILE"
fi

echo ""
echo "使用方法:"
echo "  查看完整结果: cat $FINAL_OUTPUT"
echo "  查看统计报告: cat $REPORT_FILE"
