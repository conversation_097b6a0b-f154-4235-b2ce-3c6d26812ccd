#!/usr/bin/env python3
"""
英语单词提取器
从词典文件或文本文件中提取英文单词，支持文件夹递归处理
"""

import re
import argparse
from pathlib import Path
from typing import Set, Tuple


class EnglishWordExtractor:
    """英语单词提取器"""
    
    def __init__(self):
        # 英文单词的正则表达式模式
        self.word_patterns = [
            # 基本单词模式 (字母、连字符、撇号)
            r'\b[a-zA-Z][a-zA-Z\'\-]*[a-zA-Z]\b',
            # 单个字母单词 (如 a, I)
            r'\b[a-zA-Z]\b',
            # 缩写词 (如 A.B.C., Ph.D.)
            r'\b[A-Z]+(?:\.[A-Z]+)*\.?\b',
            # 数字-字母组合 (如 24-7, 911, A1)
            r'\b\d+[a-zA-Z]+\b',
            r'\b[a-zA-Z]+\d+\b',
            # 带斜杠的组合 (如 9/11)
            r'\b\w+/\w+\b'
        ]
        
        # HTML标签清理模式
        self.html_patterns = [
            r'<[^>]+>',  # HTML标签
            r'&[a-zA-Z]+;',  # HTML实体
            r'&\#\d+;'  # 数字HTML实体
        ]
        
        # 需要过滤的内容模式
        self.filter_patterns = [
            r'^\d+$',  # 纯数字
            r'^[^\w]+$',  # 纯符号
        ]
        
        # 允许的单字符单词
        self.allowed_single_chars = {'a', 'A', 'I'}
        
    def clean_html(self, text: str) -> str:
        """清理HTML标签和实体"""
        for pattern in self.html_patterns:
            text = re.sub(pattern, ' ', text)
        return text
    
    def extract_words_from_line(self, line: str) -> Set[str]:
        """从单行文本中提取单词"""
        # 清理HTML
        cleaned_line = self.clean_html(line)
        
        words = set()
        
        # 使用多个模式提取单词
        for pattern in self.word_patterns:
            matches = re.findall(pattern, cleaned_line, re.IGNORECASE)
            for match in matches:
                # 基本清理
                word = match.strip().lower()
                if self.is_valid_word(word):
                    words.add(word)
        
        return words
    
    def is_valid_word(self, word: str) -> bool:
        """验证单词是否有效"""
        if not word:
            return False
            
        # 检查是否为单字符但不在允许列表中
        if len(word) == 1 and word not in self.allowed_single_chars:
            return False
            
        # 检查过滤模式
        for pattern in self.filter_patterns:
            if re.match(pattern, word):
                return False
        
        # 检查是否包含至少一个字母
        if not re.search(r'[a-zA-Z]', word):
            return False
            
        # 过滤过长的"单词"（可能是误提取的内容）
        if len(word) > 50:
            return False
            
        return True
    
    def extract_words_from_file(self, file_path: Path) -> Tuple[Set[str], int]:
        """从文件中提取所有单词"""
        words = set()
        lines_processed = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line_words = self.extract_words_from_line(line)
                    words.update(line_words)
                    lines_processed += 1
                    
                    # 每处理1000行显示进度
                    if line_num % 1000 == 0:
                        print(f"  处理第 {line_num} 行，已提取 {len(words)} 个单词...")
                        
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            
        return words, lines_processed
    
    def process_input(self, input_path: str, recursive: bool = False) -> Set[str]:
        """处理输入路径（文件或文件夹）"""
        input_path = Path(input_path)
        all_words = set()
        
        if input_path.is_file():
            # 处理单个文件
            print(f"处理文件: {input_path.name}")
            words, lines = self.extract_words_from_file(input_path)
            all_words.update(words)
            print(f"  提取了 {len(words)} 个单词，处理了 {lines} 行")
            
        elif input_path.is_dir():
            # 处理文件夹
            if recursive:
                # 递归处理所有文本文件
                text_files = list(input_path.rglob("*.txt")) + list(input_path.rglob("*.json")) + list(input_path.rglob("*.xml"))
            else:
                # 只处理当前目录的文本文件
                text_files = list(input_path.glob("*.txt")) + list(input_path.glob("*.json")) + list(input_path.glob("*.xml"))
            
            if not text_files:
                print(f"在目录 {input_path} 中未找到支持的文件")
                return all_words
            
            print(f"找到 {len(text_files)} 个文件")
            
            for file_path in text_files:
                print(f"\n处理文件: {file_path.name}")
                words, lines = self.extract_words_from_file(file_path)
                all_words.update(words)
                print(f"  提取了 {len(words)} 个单词，处理了 {lines} 行")
        
        else:
            print(f"错误: 路径 {input_path} 不存在")
            
        return all_words
    
    def save_words(self, words: Set[str], output_path: str):
        """保存单词到文件"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 排序单词列表
        sorted_words = sorted(words, key=str.lower)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            for word in sorted_words:
                f.write(f"{word}\n")
        
        print(f"\n提取结果已保存到: {output_path}")
        print(f"总计提取 {len(words)} 个唯一单词")


def main():
    parser = argparse.ArgumentParser(description='英语单词提取器')
    parser.add_argument('--input', required=True, help='输入文件或文件夹路径')
    parser.add_argument('--output', required=True, help='输出文件路径')
    parser.add_argument('--recursive', action='store_true', help='递归处理文件夹')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print("英语单词提取器")
    print("=" * 50)
    print(f"输入路径: {args.input}")
    print(f"输出路径: {args.output}")
    print(f"递归处理: {'是' if args.recursive else '否'}")
    print()
    
    # 创建提取器
    extractor = EnglishWordExtractor()
    
    # 处理输入
    words = extractor.process_input(args.input, args.recursive)
    
    if words:
        # 保存结果
        extractor.save_words(words, args.output)
    else:
        print("未提取到任何单词")


if __name__ == "__main__":
    main()
