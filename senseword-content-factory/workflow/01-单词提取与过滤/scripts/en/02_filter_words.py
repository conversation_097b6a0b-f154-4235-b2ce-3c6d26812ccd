#!/usr/bin/env python3
"""
英语单词过滤器 - 超精细过滤
整合所有过滤级别，使用最强的过滤标准，确保输出高质量的英语单词列表
"""

import re
import argparse
from pathlib import Path
from typing import List, Set, Dict, Tuple


class EnglishWordFilter:
    """英语单词超精细过滤器"""
    
    def __init__(self):
        # 允许的极短单词白名单（真正的英语功能词）
        self.allowed_short_words = {
            'a', 'i', 'am', 'an', 'as', 'at', 'be', 'by', 'do', 'go', 'he', 
            'if', 'in', 'is', 'it', 'me', 'my', 'no', 'of', 'on', 'or', 'so', 
            'to', 'up', 'us', 'we', 'ox', 'pi'
        }
        
        # 允许的三字母真实单词白名单
        self.allowed_three_letters = {
            'ace', 'act', 'add', 'age', 'aid', 'aim', 'air', 'all', 'and', 'ant',
            'any', 'ape', 'app', 'apt', 'arc', 'are', 'ark', 'arm', 'art', 'ask',
            'ate', 'awe', 'axe', 'bad', 'bag', 'ban', 'bar', 'bat', 'bay', 'bed',
            'bee', 'bet', 'bid', 'big', 'bin', 'bit', 'boy', 'bug', 'bus', 'but',
            'buy', 'cab', 'can', 'cap', 'car', 'cat', 'cow', 'cry', 'cup', 'cut',
            'dad', 'day', 'den', 'dew', 'did', 'die', 'dig', 'dog', 'dot', 'dry',
            'due', 'ear', 'eat', 'egg', 'end', 'era', 'eve', 'eye', 'fan', 'far',
            'fat', 'few', 'fig', 'fin', 'fit', 'fix', 'fly', 'fog', 'for', 'fox',
            'fun', 'fur', 'gap', 'gas', 'get', 'got', 'gun', 'guy', 'gym', 'had',
            'ham', 'hat', 'her', 'hey', 'hid', 'him', 'hip', 'his', 'hit', 'hop',
            'hot', 'how', 'hub', 'hug', 'hut', 'ice', 'ill', 'ink', 'inn', 'ion',
            'its', 'ivy', 'jam', 'jar', 'jaw', 'jet', 'job', 'joy', 'key', 'kid',
            'kit', 'lab', 'lap', 'law', 'lay', 'led', 'leg', 'let', 'lid', 'lie',
            'lip', 'log', 'lot', 'low', 'mad', 'man', 'map', 'mat', 'max', 'may',
            'men', 'met', 'mix', 'mob', 'mom', 'mud', 'mug', 'net', 'new', 'nod',
            'nor', 'not', 'now', 'nut', 'oak', 'odd', 'off', 'oil', 'old', 'one',
            'opt', 'our', 'out', 'owl', 'own', 'pad', 'pan', 'pat', 'paw', 'pay',
            'pen', 'pet', 'pie', 'pig', 'pin', 'pit', 'pop', 'pot', 'pub', 'pup',
            'put', 'ram', 'ran', 'rat', 'raw', 'ray', 'red', 'rid', 'rim', 'rip',
            'rob', 'rod', 'row', 'rub', 'rug', 'rum', 'run', 'sad', 'sat', 'saw',
            'say', 'sea', 'see', 'set', 'sew', 'she', 'shy', 'sin', 'sip', 'sir',
            'sit', 'six', 'ski', 'sky', 'sly', 'sob', 'son', 'sow', 'spy', 'sum',
            'sun', 'tab', 'tag', 'tan', 'tap', 'tar', 'tax', 'tea', 'ten', 'the',
            'tie', 'tin', 'tip', 'toe', 'ton', 'too', 'top', 'toy', 'try', 'tub',
            'two', 'use', 'van', 'war', 'was', 'way', 'web', 'wet', 'who', 'why',
            'win', 'wit', 'won', 'wow', 'yes', 'yet', 'you', 'zip', 'zoo'
        }
        
        # 移除了技术术语过滤，因为这一步的目的是字符层面的验证，不是判断是否应该学习
        
        # 特殊无元音单词
        self.special_no_vowel = {'by', 'my', 'gym', 'fly', 'try', 'cry', 'dry', 'sky', 'why', 'spy', 'shy', 'sly'}
        
        # 统计信息
        self.stats = {
            'input_total': 0,
            'too_short': 0,
            'too_long': 0,
            'invalid_chars': 0,
            'no_vowels': 0,
            'three_letter_abbrev': 0,
            'combined_words': 0,
            'repetitive': 0,
            'meaningless': 0,
            'final_kept': 0
        }
    
    def is_valid_word(self, word: str) -> Tuple[bool, str]:
        """检查单词是否有效，返回(是否有效, 原因)"""
        word = word.lower().strip()
        
        if not word:
            return False, "empty"
        
        # 1. 长度检查
        if len(word) < 2:
            if word in self.allowed_short_words:
                return True, "valid_short"
            return False, "too_short"
        
        if len(word) > 20:
            return False, "too_long"
        
        # 2. 三字母单词特殊处理
        if len(word) == 3:
            if word in self.allowed_three_letters:
                return True, "valid_three_letter"
            elif re.match(r'^[a-z]{3}$', word):
                # 没有元音的三字母组合很可能是缩写
                if not re.search(r'[aeiou]', word):
                    return False, "three_letter_abbreviation"
                return True, "three_letter_with_vowel"
        
        # 3. 检查组合词模式
        combined_patterns = [
            r'[a-z]+about[a-z]+', r'[a-z]+above[a-z]+', r'[a-z]+after[a-z]+',
            r'[a-z]+around[a-z]+', r'[a-z]+under[a-z]+', r'[a-z]+over[a-z]+',
            r'[a-z]+with[a-z]+', r'[a-z]+without[a-z]+', r'[a-z]+before[a-z]+',
            r'[a-z]+behind[a-z]+', r'[a-z]+between[a-z]+', r'[a-z]+through[a-z]+',
            r'[a-z]+against[a-z]+', r'[a-z]+during[a-z]+'
        ]
        
        for pattern in combined_patterns:
            if re.search(pattern, word):
                return False, "combined_words"
        
        # 4. 检查明显的非单词模式（保留，这些确实不是正常单词）
        # 只保留明显异常的模式，移除正常学术词汇的过滤
        
        # 5. 重复和异常模式检查
        if len(word) >= 4:
            # 重复检查
            if re.search(r'(.{2,})\1', word):
                return False, "repetitive"
            
            # 异常的元音/辅音聚集
            if re.search(r'[aeiou]{4,}', word):
                return False, "too_many_vowels"
            
            if re.search(r'[bcdfghjklmnpqrstvwxyz]{5,}', word):
                return False, "too_many_consonants"
        
        # 6. 无意义字母序列
        meaningless_patterns = [
            r'[a-z]*aaa[a-z]*', r'[a-z]*bbb[a-z]*', r'[a-z]*ccc[a-z]*',
            r'[a-z]*xxx[a-z]*', r'[a-z]*zzz[a-z]*'
        ]
        
        for pattern in meaningless_patterns:
            if re.search(pattern, word):
                return False, "meaningless"
        
        # 7. 元音检查
        if not re.search(r'[aeiou]', word) and word not in self.special_no_vowel:
            return False, "no_vowels"
        
        # 8. 字符检查
        if not re.match(r'^[a-z]+$', word):
            return False, "invalid_chars"
        
        return True, "valid"
    
    def filter_words(self, words: List[str]) -> Dict[str, any]:
        """执行超精细过滤"""
        self.stats['input_total'] = len(words)
        
        valid_words = []
        filtered_categories = {}
        
        for word in words:
            is_valid, reason = self.is_valid_word(word)
            
            if is_valid:
                valid_words.append(word)
                self.stats['final_kept'] += 1
            else:
                # 统计过滤原因
                if reason not in filtered_categories:
                    filtered_categories[reason] = []
                filtered_categories[reason].append(word)
                
                # 更新统计
                if reason in ['too_short', 'too_long', 'invalid_chars', 'no_vowels',
                             'three_letter_abbreviation', 'combined_words',
                             'repetitive', 'meaningless']:
                    if reason == 'three_letter_abbreviation':
                        self.stats['three_letter_abbrev'] += 1
                    else:
                        self.stats[reason] += 1
        
        return {
            'valid_words': sorted(set(valid_words)),
            'filtered_categories': filtered_categories,
            'stats': self.stats
        }
    
    def load_words(self, input_path: str) -> List[str]:
        """从文件加载单词列表"""
        words = []
        with open(input_path, 'r', encoding='utf-8') as f:
            for line in f:
                word = line.strip()
                if word:
                    words.append(word)
        return words
    
    def save_results(self, results: Dict, output_path: str):
        """保存过滤结果"""
        output_path = Path(output_path)
        
        # 保存有效单词到TSV文件
        with open(output_path, 'w', encoding='utf-8') as f:
            for word in results['valid_words']:
                f.write(f"{word}\n")
        
        # 保存统计报告
        report_path = output_path.with_suffix('.report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("英语单词超精细过滤报告\n")
            f.write("=" * 50 + "\n\n")
            
            stats = results['stats']
            f.write(f"输入单词总数: {stats['input_total']:,}\n")
            f.write(f"输出有效单词: {stats['final_kept']:,}\n")
            f.write(f"过滤率: {((stats['input_total'] - stats['final_kept']) / stats['input_total'] * 100):.1f}%\n\n")
            
            f.write("过滤原因统计:\n")
            f.write("-" * 30 + "\n")
            for key, value in stats.items():
                if key not in ['input_total', 'final_kept'] and value > 0:
                    f.write(f"{key}: {value:,}\n")
        
        print(f"\n过滤结果已保存到: {output_path}")
        print(f"统计报告: {report_path}")


def main():
    parser = argparse.ArgumentParser(description='英语单词超精细过滤器')
    parser.add_argument('--input', required=True, help='输入单词文件路径')
    parser.add_argument('--output', required=True, help='输出TSV文件路径')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print("英语单词超精细过滤器")
    print("=" * 50)
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print()
    
    # 创建过滤器
    filter_engine = EnglishWordFilter()
    
    # 加载单词
    print("正在加载单词列表...")
    words = filter_engine.load_words(args.input)
    print(f"加载了 {len(words):,} 个单词")
    
    # 执行过滤
    print("\n正在执行超精细过滤...")
    results = filter_engine.filter_words(words)
    
    # 保存结果
    filter_engine.save_results(results, args.output)
    
    # 显示摘要
    stats = results['stats']
    print(f"\n过滤完成!")
    print(f"输入: {stats['input_total']:,} 个单词")
    print(f"输出: {stats['final_kept']:,} 个有效单词")
    print(f"过滤: {stats['input_total'] - stats['final_kept']:,} 个单词")
    print(f"保留率: {(stats['final_kept'] / stats['input_total'] * 100):.1f}%")


if __name__ == "__main__":
    main()
