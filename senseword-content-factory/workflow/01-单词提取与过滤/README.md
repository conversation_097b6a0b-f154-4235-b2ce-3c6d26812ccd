# 阶段01: 单词提取与过滤 (整合工作流)

## 📋 概述

这个阶段整合了原来的"单词提取"和"文本过滤"两个步骤，形成一个统一的工作流程。它能够从各种格式的词典文件或文本文件中提取单词，并进行超精细过滤，最终输出高质量的单词列表。

## 🎯 核心功能

### 1. 多格式输入支持
- **词典文件**: TXT, JSON, XML 等格式的词典
- **文本文件**: 包含大量单词的文章、文档等
- **文件夹处理**: 递归处理文件夹中的所有文件
- **语言特定**: 当前专注于英语处理

### 2. 智能单词提取
- **正则表达式模式**: 多种英语单词模式识别
- **HTML清理**: 自动清理HTML标签和实体
- **格式标准化**: 统一单词格式和大小写
- **去重处理**: 自动去除重复单词

### 3. 超精细过滤系统
- **基础验证**: 长度、字符、格式检查
- **语言特定**: 英语单词结构验证
- **质量控制**: 技术术语、组合词、缩写过滤
- **最强标准**: 使用ultra_fine级别的过滤规则

## 🏗️ 数据管道设计

### 输入端
```
文件/文件夹 → 格式检测 → 单词提取 → 超精细过滤 → TSV输出
     ↓           ↓          ↓          ↓         ↓
  .txt/.json   自动识别   正则匹配   质量验证   一行一词
  .xml/...     编码处理   HTML清理   结构检查   排序输出
```

### 处理流程
```
原始文件 → 01_extract_words.py → 02_filter_words.py → 高质量单词列表
    ↓              ↓                    ↓              ↓
  多格式文件      提取所有单词        超精细过滤        TSV格式
  递归处理        HTML清理           质量控制          统计报告
```

## 🛠️ 脚本说明

### `01_extract_words.py` - 英语单词提取器

#### 功能
- 从词典文件或文本文件中提取英文单词
- 支持文件夹递归处理
- 自动HTML清理和格式标准化
- 基础单词验证

#### 使用方法
```bash
python scripts/en/01_extract_words.py \
  --input /path/to/input \
  --output extracted_words.txt \
  --recursive
```

#### 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--input` | ✅ | - | 输入文件或文件夹路径 |
| `--output` | ✅ | - | 输出文件路径 |
| `--recursive` | ❌ | False | 是否递归处理文件夹 |
| `--verbose` | ❌ | False | 详细输出模式 |

#### 支持的文件格式
- `.txt` - 纯文本文件
- `.json` - JSON格式文件
- `.xml` - XML格式文件

### `02_filter_words.py` - 英语单词超精细过滤器

#### 功能
- 使用最强的过滤标准（ultra_fine级别）
- 多维度质量检查和验证
- 详细的过滤统计报告
- 输出高质量的单词列表

#### 使用方法
```bash
python scripts/en/02_filter_words.py \
  --input extracted_words.txt \
  --output filtered_words.tsv \
  --verbose
```

#### 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| `--input` | ✅ | - | 输入单词文件路径 |
| `--output` | ✅ | - | 输出TSV文件路径 |
| `--verbose` | ❌ | False | 详细输出模式 |

#### 过滤标准
1. **长度检查**: 2-20字符，特殊短词白名单
2. **字符验证**: 只允许英文字母
3. **结构检查**: 元音辅音分布验证
4. **模式过滤**: 组合词、明显缩写
5. **质量控制**: 重复模式、无意义序列

注：移除了技术术语过滤，因为这一步的目的是字符层面验证，不判断单词是否应该学习

## 🚀 完整工作流示例

### 1. 处理单个词典文件
```bash
# 步骤1: 提取单词
python scripts/en/01_extract_words.py \
  --input cambridge_dict.txt \
  --output raw_words.txt

# 步骤2: 过滤单词
python scripts/en/02_filter_words.py \
  --input raw_words.txt \
  --output cambridge_words.tsv
```

### 2. 递归处理词典文件夹
```bash
# 步骤1: 递归提取所有词典文件
python scripts/en/01_extract_words.py \
  --input /path/to/dictionaries/ \
  --output all_raw_words.txt \
  --recursive \
  --verbose

# 步骤2: 超精细过滤
python scripts/en/02_filter_words.py \
  --input all_raw_words.txt \
  --output all_filtered_words.tsv \
  --verbose
```

### 3. 一键处理脚本
```bash
#!/bin/bash
# process_english_words.sh

INPUT_PATH="$1"
OUTPUT_NAME="$2"

echo "开始处理英语单词提取与过滤..."

# 步骤1: 提取
python scripts/en/01_extract_words.py \
  --input "$INPUT_PATH" \
  --output "temp_${OUTPUT_NAME}_raw.txt" \
  --recursive

# 步骤2: 过滤
python scripts/en/02_filter_words.py \
  --input "temp_${OUTPUT_NAME}_raw.txt" \
  --output "${OUTPUT_NAME}.tsv"

# 清理临时文件
rm "temp_${OUTPUT_NAME}_raw.txt"

echo "处理完成: ${OUTPUT_NAME}.tsv"
```

## 📊 输出格式

### 主要输出文件 (TSV格式)
```
word1
word2
word3
example
sample
```

### 统计报告文件 (.report.txt)
```
英语单词超精细过滤报告
==================================================

输入单词总数: 150,000
输出有效单词: 85,000
过滤率: 43.3%

过滤原因统计:
------------------------------
too_short: 5,000
combined_words: 8,000
three_letter_abbrev: 12,000
repetitive: 3,000
meaningless: 2,000
no_vowels: 1,500
```

## 🔍 质量控制

### 过滤级别说明
这个整合工作流使用**超精细过滤**标准，包括：

1. **基础验证**
   - 长度检查 (2-20字符)
   - 字符验证 (只允许英文字母)
   - 元音检查 (必须包含元音，除特殊情况)

2. **结构验证**
   - 三字母单词白名单验证
   - 元音辅音分布检查
   - 重复模式识别

3. **模式过滤**
   - 组合词过滤 (错误拼接的词)
   - 缩写词过滤 (明显的非标准缩写)

4. **质量保证**
   - 无意义序列过滤
   - 异常字母聚集检查
   - 最终结构验证

### 质量指标
- **过滤效率**: 通常过滤掉40-50%的低质量单词
- **准确率**: 保留的单词95%以上为有效英语单词
- **覆盖率**: 涵盖常用英语词汇的90%以上

## 🔧 故障排除

### 常见问题
1. **编码错误**: 确保输入文件使用UTF-8编码
2. **内存不足**: 大文件处理时可能需要更多内存
3. **路径问题**: 使用绝对路径避免路径错误
4. **权限问题**: 确保对输入输出目录有读写权限

### 调试模式
```bash
# 启用详细输出查看处理过程
python scripts/en/01_extract_words.py \
  --input test.txt \
  --output test_raw.txt \
  --verbose

python scripts/en/02_filter_words.py \
  --input test_raw.txt \
  --output test_filtered.tsv \
  --verbose
```

## 📈 性能优化

### 处理大文件
- 使用流式处理避免内存溢出
- 分批处理大型词典文件
- 定期显示处理进度

### 提升效率
- 预先清理明显的垃圾数据
- 使用SSD存储提升I/O性能
- 合理设置批处理大小

这个整合的工作流程将原来分散的单词提取和文本过滤功能统一到一个高效、专业的英语单词处理管道中，确保输出高质量的英语单词列表。
