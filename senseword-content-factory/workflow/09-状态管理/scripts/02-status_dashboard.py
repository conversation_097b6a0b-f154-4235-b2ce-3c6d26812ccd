#!/usr/bin/env python3
"""
SenseWord 状态看板 - 显示当前处理进度和状态分布
纯监控工具，不修改任何数据，只展示状态信息

功能：
1. 显示总体统计信息
2. 展示各阶段处理进度
3. 分析处理瓶颈
4. 提供下一步操作建议

使用方法：
    python 02-status_dashboard.py --db-path /path/to/senseword_content_v4.db [选项]

参数：
    --db-path: 数据库文件路径（必需）
    --detailed: 显示详细的状态分布
    --export-csv: 导出状态统计到CSV文件
"""

import sqlite3
import argparse
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json


class StatusDashboard:
    """SenseWord 状态看板"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
    
    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
    
    def get_overall_stats(self) -> Dict[str, int]:
        """获取总体统计信息"""
        try:
            cursor = self.connection.cursor()
            
            # 获取基本统计
            cursor.execute("SELECT COUNT(*) FROM word_processing_queue")
            total = cursor.fetchone()[0] or 0
            
            # 获取完全完成的数量（所有状态都为True）
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE contentGenerated = 1 
                AND contentAiReviewed = 1 
                AND ttsIdGenerated = 1 
                AND audioGenerated = 1 
                AND readyForPublish = 1
            """)
            fully_complete = cursor.fetchone()[0] or 0
            
            # 获取完全未开始的数量（所有状态都为False）
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE contentGenerated = 0 
                AND contentAiReviewed = 0 
                AND ttsIdGenerated = 0 
                AND audioGenerated = 0 
                AND readyForPublish = 0
            """)
            not_started = cursor.fetchone()[0] or 0
            
            # 进行中的数量
            in_progress = total - fully_complete - not_started
            
            return {
                'total': total,
                'fully_complete': fully_complete,
                'in_progress': in_progress,
                'not_started': not_started
            }
            
        except Exception as e:
            print(f"❌ 获取总体统计失败: {e}")
            return {}
    
    def get_stage_stats(self) -> Dict[str, int]:
        """获取各阶段统计信息"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute("""
                SELECT 
                    SUM(CASE WHEN contentGenerated = 1 THEN 1 ELSE 0 END) as content_generated,
                    SUM(CASE WHEN contentAiReviewed = 1 THEN 1 ELSE 0 END) as content_reviewed,
                    SUM(CASE WHEN ttsIdGenerated = 1 THEN 1 ELSE 0 END) as tts_generated,
                    SUM(CASE WHEN audioGenerated = 1 THEN 1 ELSE 0 END) as audio_generated,
                    SUM(CASE WHEN readyForPublish = 1 THEN 1 ELSE 0 END) as ready_publish
                FROM word_processing_queue
            """)
            
            result = cursor.fetchone()
            if result:
                return {
                    'content_generated': result[0] or 0,
                    'content_reviewed': result[1] or 0,
                    'tts_generated': result[2] or 0,
                    'audio_generated': result[3] or 0,
                    'ready_publish': result[4] or 0
                }
            else:
                return {
                    'content_generated': 0,
                    'content_reviewed': 0,
                    'tts_generated': 0,
                    'audio_generated': 0,
                    'ready_publish': 0
                }
                
        except Exception as e:
            print(f"❌ 获取阶段统计失败: {e}")
            return {}
    
    def get_pending_stats(self) -> Dict[str, int]:
        """获取各阶段待处理数量"""
        try:
            cursor = self.connection.cursor()
            
            # 内容生成待处理
            cursor.execute("SELECT COUNT(*) FROM word_processing_queue WHERE contentGenerated = 0")
            content_pending = cursor.fetchone()[0] or 0
            
            # 内容审核待处理
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE contentGenerated = 1 AND contentAiReviewed = 0
            """)
            review_pending = cursor.fetchone()[0] or 0
            
            # TTS生成待处理
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE contentAiReviewed = 1 AND ttsIdGenerated = 0
            """)
            tts_pending = cursor.fetchone()[0] or 0
            
            # 音频生成待处理
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE ttsIdGenerated = 1 AND audioGenerated = 0
            """)
            audio_pending = cursor.fetchone()[0] or 0
            
            # 准备发布待处理
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE audioGenerated = 1 AND readyForPublish = 0
            """)
            publish_pending = cursor.fetchone()[0] or 0
            
            return {
                'content_pending': content_pending,
                'review_pending': review_pending,
                'tts_pending': tts_pending,
                'audio_pending': audio_pending,
                'publish_pending': publish_pending
            }
            
        except Exception as e:
            print(f"❌ 获取待处理统计失败: {e}")
            return {}
    
    def analyze_bottlenecks(self) -> List[Dict]:
        """分析处理瓶颈"""
        pending_stats = self.get_pending_stats()
        bottlenecks = []
        
        # 定义瓶颈阈值
        threshold = 100
        
        if pending_stats.get('content_pending', 0) > threshold:
            bottlenecks.append({
                'stage': '内容生成',
                'count': pending_stats['content_pending'],
                'priority': 'high',
                'description': '大量单词等待内容生成'
            })
        
        if pending_stats.get('review_pending', 0) > threshold:
            bottlenecks.append({
                'stage': '内容审核',
                'count': pending_stats['review_pending'],
                'priority': 'medium',
                'description': '内容等待AI审核'
            })
        
        if pending_stats.get('tts_pending', 0) > threshold:
            bottlenecks.append({
                'stage': 'TTS生成',
                'count': pending_stats['tts_pending'],
                'priority': 'medium',
                'description': 'TTS哈希等待生成'
            })
        
        if pending_stats.get('audio_pending', 0) > threshold:
            bottlenecks.append({
                'stage': '音频生成',
                'count': pending_stats['audio_pending'],
                'priority': 'high',
                'description': '音频文件等待生成'
            })
        
        if pending_stats.get('publish_pending', 0) > threshold:
            bottlenecks.append({
                'stage': '发布准备',
                'count': pending_stats['publish_pending'],
                'priority': 'low',
                'description': '内容等待发布准备'
            })
        
        return bottlenecks
    
    def get_language_distribution(self) -> Dict[str, int]:
        """获取语言对分布"""
        try:
            cursor = self.connection.cursor()
            
            cursor.execute("""
                SELECT learningLanguage, scaffoldingLanguage, COUNT(*) as count
                FROM word_processing_queue
                GROUP BY learningLanguage, scaffoldingLanguage
                ORDER BY count DESC
            """)
            
            results = cursor.fetchall()
            distribution = {}
            
            for learning_lang, scaffolding_lang, count in results:
                key = f"{learning_lang}-{scaffolding_lang}"
                distribution[key] = count
            
            return distribution
            
        except Exception as e:
            print(f"❌ 获取语言分布失败: {e}")
            return {}
    
    def display_dashboard(self, detailed: bool = False):
        """显示状态看板"""
        print("📊 SenseWord 处理状态看板")
        print("=" * 60)
        print(f"🕒 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 总体统计
        overall_stats = self.get_overall_stats()
        if overall_stats:
            print(f"\n🗄️  总体状态:")
            print(f"   - 总单词数: {overall_stats['total']:,}")
            print(f"   - 完全完成: {overall_stats['fully_complete']:,}")
            print(f"   - 进行中: {overall_stats['in_progress']:,}")
            print(f"   - 未开始: {overall_stats['not_started']:,}")
            
            if overall_stats['total'] > 0:
                completion_rate = (overall_stats['fully_complete'] / overall_stats['total']) * 100
                print(f"   - 完成率: {completion_rate:.1f}%")
        
        # 各阶段进度
        stage_stats = self.get_stage_stats()
        if stage_stats:
            print(f"\n📈 各阶段进度:")
            print(f"   - 内容已生成: {stage_stats['content_generated']:,}")
            print(f"   - 内容已审核: {stage_stats['content_reviewed']:,}")
            print(f"   - TTS已生成: {stage_stats['tts_generated']:,}")
            print(f"   - 音频已生成: {stage_stats['audio_generated']:,}")
            print(f"   - 准备发布: {stage_stats['ready_publish']:,}")
        
        # 待处理统计
        pending_stats = self.get_pending_stats()
        if pending_stats:
            print(f"\n⏳ 待处理统计:")
            print(f"   - 内容生成待处理: {pending_stats['content_pending']:,}")
            print(f"   - 内容审核待处理: {pending_stats['review_pending']:,}")
            print(f"   - TTS生成待处理: {pending_stats['tts_pending']:,}")
            print(f"   - 音频生成待处理: {pending_stats['audio_pending']:,}")
            print(f"   - 发布准备待处理: {pending_stats['publish_pending']:,}")
        
        # 瓶颈分析
        bottlenecks = self.analyze_bottlenecks()
        if bottlenecks:
            print(f"\n🚨 处理瓶颈:")
            for bottleneck in bottlenecks:
                priority_icon = "🔴" if bottleneck['priority'] == 'high' else "🟡" if bottleneck['priority'] == 'medium' else "🟢"
                print(f"   {priority_icon} {bottleneck['stage']}: {bottleneck['count']:,} ({bottleneck['description']})")
        else:
            print(f"\n✅ 当前无明显处理瓶颈")
        
        # 详细信息
        if detailed:
            lang_dist = self.get_language_distribution()
            if lang_dist:
                print(f"\n🌍 语言对分布:")
                for lang_pair, count in lang_dist.items():
                    print(f"   - {lang_pair}: {count:,}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord 状态看板 - 显示处理进度和状态分布",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 显示基本状态看板
  python 02-status_dashboard.py --db-path ./senseword_content_v4.db
  
  # 显示详细状态信息
  python 02-status_dashboard.py --db-path ./senseword_content_v4.db --detailed
        """
    )
    
    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )
    
    parser.add_argument(
        '--detailed',
        action='store_true',
        help='显示详细的状态分布'
    )
    
    args = parser.parse_args()
    
    # 验证数据库文件
    import os
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)
    
    # 创建状态看板
    dashboard = StatusDashboard(args.db_path)
    
    try:
        # 连接数据库
        dashboard.connect()
        
        # 显示看板
        dashboard.display_dashboard(detailed=args.detailed)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        dashboard.close()


if __name__ == "__main__":
    main()
