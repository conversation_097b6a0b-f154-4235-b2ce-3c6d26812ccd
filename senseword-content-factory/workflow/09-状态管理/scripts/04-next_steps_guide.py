#!/usr/bin/env python3
"""
SenseWord 下一步操作指引 - 分析当前状态并建议具体的操作命令
智能分析工具，根据当前处理状态提供操作建议

功能：
1. 分析当前处理瓶颈
2. 提供具体的操作命令
3. 估算处理时间和资源需求
4. 优先级排序和建议

使用方法：
    python 04-next_steps_guide.py --db-path /path/to/senseword_content_v4.db [选项]

参数：
    --db-path: 数据库文件路径（必需）
    --priority: 只显示指定优先级的建议（high/medium/low）
    --format: 输出格式（text/json/markdown）
    --export-commands: 导出命令到shell脚本文件
"""

import sqlite3
import argparse
import sys
import json
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class NextStepsGuide:
    """下一步操作指引"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        
        # 定义处理阶段配置
        self.stage_configs = {
            'content_generation': {
                'name': '内容生成',
                'description': '为单词生成完整的学习内容',
                'filter': {'contentGenerated': False},
                'priority_threshold': 500,
                'estimated_time_per_batch': 45,  # 分钟
                'batch_size': 500,
                'workflow_path': '03-AI批处理筛选',
                'script_name': 'content_generation.py',
                'next_stage': 'content_review'
            },
            'content_review': {
                'name': '内容审核',
                'description': 'AI审核生成的内容质量',
                'filter': {'contentGenerated': True, 'contentAiReviewed': False},
                'priority_threshold': 300,
                'estimated_time_per_batch': 30,
                'batch_size': 1000,
                'workflow_path': '03-AI批处理筛选',
                'script_name': 'content_review.py',
                'next_stage': 'tts_generation'
            },
            'tts_generation': {
                'name': 'TTS哈希生成',
                'description': '为内容生成TTS音频哈希',
                'filter': {'contentAiReviewed': True, 'ttsIdGenerated': False},
                'priority_threshold': 200,
                'estimated_time_per_batch': 20,
                'batch_size': 1000,
                'workflow_path': '05-TTS处理',
                'script_name': 'tts_hash_generation.py',
                'next_stage': 'audio_generation'
            },
            'audio_generation': {
                'name': '音频生成',
                'description': '生成实际的音频文件',
                'filter': {'ttsIdGenerated': True, 'audioGenerated': False},
                'priority_threshold': 100,
                'estimated_time_per_batch': 60,
                'batch_size': 200,
                'workflow_path': '05-TTS处理',
                'script_name': 'audio_generation.py',
                'next_stage': 'publish_preparation'
            },
            'publish_preparation': {
                'name': '发布准备',
                'description': '准备内容发布到生产环境',
                'filter': {'audioGenerated': True, 'readyForPublish': False},
                'priority_threshold': 50,
                'estimated_time_per_batch': 15,
                'batch_size': 1000,
                'workflow_path': '07-发布准备',
                'script_name': 'publish_preparation.py',
                'next_stage': None
            }
        }
    
    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
    
    def get_stage_counts(self) -> Dict[str, int]:
        """获取各阶段的待处理数量"""
        try:
            cursor = self.connection.cursor()
            stage_counts = {}
            
            for stage_key, config in self.stage_configs.items():
                filter_conditions = config['filter']
                
                # 构建WHERE子句
                conditions = []
                params = []
                
                for field, value in filter_conditions.items():
                    conditions.append(f"{field} = ?")
                    params.append(1 if value else 0)
                
                where_clause = " AND ".join(conditions)
                query = f"SELECT COUNT(*) FROM word_processing_queue WHERE {where_clause}"
                
                cursor.execute(query, params)
                count = cursor.fetchone()[0] or 0
                stage_counts[stage_key] = count
            
            return stage_counts
            
        except Exception as e:
            print(f"❌ 获取阶段统计失败: {e}")
            return {}
    
    def analyze_priorities(self, stage_counts: Dict[str, int]) -> List[Dict]:
        """分析优先级和生成建议"""
        recommendations = []
        
        for stage_key, count in stage_counts.items():
            if count == 0:
                continue
                
            config = self.stage_configs[stage_key]
            
            # 确定优先级
            if count >= config['priority_threshold']:
                priority = 'high'
                priority_icon = '🔴'
            elif count >= config['priority_threshold'] // 2:
                priority = 'medium'
                priority_icon = '🟡'
            else:
                priority = 'low'
                priority_icon = '🟢'
            
            # 计算批次数和预估时间
            batch_count = (count + config['batch_size'] - 1) // config['batch_size']
            estimated_total_time = batch_count * config['estimated_time_per_batch']
            
            # 生成命令
            commands = self.generate_commands(stage_key, config, count)
            
            recommendation = {
                'stage': stage_key,
                'name': config['name'],
                'description': config['description'],
                'count': count,
                'priority': priority,
                'priority_icon': priority_icon,
                'batch_count': batch_count,
                'batch_size': config['batch_size'],
                'estimated_time_minutes': estimated_total_time,
                'estimated_time_hours': estimated_total_time / 60,
                'commands': commands,
                'next_stage': config.get('next_stage'),
                'workflow_path': config['workflow_path']
            }
            
            recommendations.append(recommendation)
        
        # 按优先级和数量排序
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        recommendations.sort(key=lambda x: (priority_order[x['priority']], -x['count']))
        
        return recommendations
    
    def generate_commands(self, stage_key: str, config: Dict, count: int) -> List[str]:
        """生成具体的操作命令"""
        commands = []
        
        # 基础路径
        workflow_path = config['workflow_path']
        script_name = config['script_name']
        
        # 查询命令 - 映射到正确的stage名称
        stage_mapping = {
            'content_generation': 'content_pending',
            'content_review': 'review_pending',
            'tts_generation': 'tts_pending',
            'audio_generation': 'audio_pending',
            'publish_preparation': 'publish_pending'
        }

        query_stage = stage_mapping.get(stage_key, stage_key)
        query_cmd = f"python 09-状态管理/scripts/03-query_by_status.py " \
                   f"--db-path ./senseword_content_v4.db " \
                   f"--stage {query_stage} " \
                   f"--limit {config['batch_size']}"
        
        # 处理命令
        process_cmd = f"cd {workflow_path}/scripts && " \
                     f"python {script_name} " \
                     f"--db-path ../../senseword_content_v4.db " \
                     f"--batch-size {config['batch_size']}"
        
        # 状态更新命令
        sync_cmd = "python 09-状态管理/scripts/01-sync_words_to_queue.py " \
                  "--db-path ./senseword_content_v4.db --force"
        
        commands.extend([
            f"# 1. 查询待处理的{config['name']}单词",
            query_cmd,
            "",
            f"# 2. 执行{config['name']}处理",
            process_cmd,
            "",
            f"# 3. 更新状态（处理完成后）",
            sync_cmd
        ])
        
        return commands
    
    def get_overall_progress(self) -> Dict:
        """获取总体进度信息"""
        try:
            cursor = self.connection.cursor()
            
            # 总数
            cursor.execute("SELECT COUNT(*) FROM word_processing_queue")
            total = cursor.fetchone()[0] or 0
            
            # 完全完成数
            cursor.execute("""
                SELECT COUNT(*) FROM word_processing_queue 
                WHERE contentGenerated = 1 AND contentAiReviewed = 1 
                AND ttsIdGenerated = 1 AND audioGenerated = 1 AND readyForPublish = 1
            """)
            completed = cursor.fetchone()[0] or 0
            
            # 计算进度
            progress_percentage = (completed / total * 100) if total > 0 else 0
            
            return {
                'total': total,
                'completed': completed,
                'remaining': total - completed,
                'progress_percentage': progress_percentage
            }
            
        except Exception as e:
            print(f"❌ 获取总体进度失败: {e}")
            return {}
    
    def display_guide(self, priority_filter: str = None, format_type: str = 'text'):
        """显示操作指引"""
        if format_type == 'text':
            self.display_text_guide(priority_filter)
        elif format_type == 'json':
            self.display_json_guide(priority_filter)
        elif format_type == 'markdown':
            self.display_markdown_guide(priority_filter)
    
    def display_text_guide(self, priority_filter: str = None):
        """显示文本格式的指引"""
        print("🎯 SenseWord 下一步操作指引")
        print("=" * 60)
        print(f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 总体进度
        progress = self.get_overall_progress()
        if progress:
            print(f"\n📊 总体进度:")
            print(f"   - 总单词数: {progress['total']:,}")
            print(f"   - 已完成: {progress['completed']:,}")
            print(f"   - 剩余: {progress['remaining']:,}")
            print(f"   - 完成率: {progress['progress_percentage']:.1f}%")
        
        # 获取阶段统计
        stage_counts = self.get_stage_counts()
        recommendations = self.analyze_priorities(stage_counts)
        
        # 过滤优先级
        if priority_filter:
            recommendations = [r for r in recommendations if r['priority'] == priority_filter]
        
        if not recommendations:
            print(f"\n✅ 当前没有需要处理的任务")
            if priority_filter:
                print(f"   （优先级筛选: {priority_filter}）")
            return
        
        print(f"\n💡 推荐的操作 ({len(recommendations)} 个任务):")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{rec['priority_icon']} 任务 {i}: {rec['name']}")
            print(f"   📝 描述: {rec['description']}")
            print(f"   📊 待处理数量: {rec['count']:,}")
            print(f"   🔢 建议批次: {rec['batch_count']} 批次 (每批 {rec['batch_size']})")
            print(f"   ⏱️  预估时间: {rec['estimated_time_hours']:.1f} 小时")
            print(f"   📁 工作流路径: {rec['workflow_path']}")
            
            if rec['next_stage']:
                next_stage_name = self.stage_configs[rec['next_stage']]['name']
                print(f"   ➡️  完成后进入: {next_stage_name}")
            
            print(f"\n   🔧 操作命令:")
            for cmd in rec['commands']:
                if cmd.startswith('#'):
                    print(f"   {cmd}")
                elif cmd.strip():
                    print(f"   $ {cmd}")
                else:
                    print()
    
    def display_json_guide(self, priority_filter: str = None):
        """显示JSON格式的指引"""
        stage_counts = self.get_stage_counts()
        recommendations = self.analyze_priorities(stage_counts)
        progress = self.get_overall_progress()
        
        if priority_filter:
            recommendations = [r for r in recommendations if r['priority'] == priority_filter]
        
        output = {
            'generated_at': datetime.now().isoformat(),
            'overall_progress': progress,
            'recommendations': recommendations,
            'total_tasks': len(recommendations)
        }
        
        print(json.dumps(output, indent=2, ensure_ascii=False))
    
    def display_markdown_guide(self, priority_filter: str = None):
        """显示Markdown格式的指引"""
        print("# SenseWord 下一步操作指引\n")
        print(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # 总体进度
        progress = self.get_overall_progress()
        if progress:
            print("## 📊 总体进度\n")
            print(f"- **总单词数**: {progress['total']:,}")
            print(f"- **已完成**: {progress['completed']:,}")
            print(f"- **剩余**: {progress['remaining']:,}")
            print(f"- **完成率**: {progress['progress_percentage']:.1f}%\n")
        
        # 推荐操作
        stage_counts = self.get_stage_counts()
        recommendations = self.analyze_priorities(stage_counts)
        
        if priority_filter:
            recommendations = [r for r in recommendations if r['priority'] == priority_filter]
        
        if not recommendations:
            print("## ✅ 当前状态\n")
            print("当前没有需要处理的任务。\n")
            return
        
        print(f"## 💡 推荐操作 ({len(recommendations)} 个任务)\n")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"### {rec['priority_icon']} 任务 {i}: {rec['name']}\n")
            print(f"**描述**: {rec['description']}\n")
            print(f"- **待处理数量**: {rec['count']:,}")
            print(f"- **建议批次**: {rec['batch_count']} 批次 (每批 {rec['batch_size']})")
            print(f"- **预估时间**: {rec['estimated_time_hours']:.1f} 小时")
            print(f"- **工作流路径**: `{rec['workflow_path']}`")
            
            if rec['next_stage']:
                next_stage_name = self.stage_configs[rec['next_stage']]['name']
                print(f"- **完成后进入**: {next_stage_name}")
            
            print(f"\n**操作命令**:\n")
            print("```bash")
            for cmd in rec['commands']:
                print(cmd)
            print("```\n")
    
    def export_commands(self, output_file: str, priority_filter: str = None):
        """导出命令到shell脚本"""
        try:
            stage_counts = self.get_stage_counts()
            recommendations = self.analyze_priorities(stage_counts)
            
            if priority_filter:
                recommendations = [r for r in recommendations if r['priority'] == priority_filter]
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("#!/bin/bash\n")
                f.write("# SenseWord 自动化处理脚本\n")
                f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"# 任务 {i}: {rec['name']}\n")
                    f.write(f"# 待处理数量: {rec['count']:,}\n")
                    f.write(f"# 预估时间: {rec['estimated_time_hours']:.1f} 小时\n\n")
                    
                    for cmd in rec['commands']:
                        if cmd.startswith('#'):
                            f.write(f"{cmd}\n")
                        elif cmd.strip():
                            f.write(f"{cmd}\n")
                        else:
                            f.write("\n")
                    
                    f.write("\n" + "="*50 + "\n\n")
            
            print(f"✅ 命令已导出到 {output_file}")
            
        except Exception as e:
            print(f"❌ 导出命令失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord 下一步操作指引",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 显示所有建议
  python 04-next_steps_guide.py --db-path ./senseword_content_v4.db
  
  # 只显示高优先级建议
  python 04-next_steps_guide.py --db-path ./senseword_content_v4.db --priority high
  
  # 导出为JSON格式
  python 04-next_steps_guide.py --db-path ./senseword_content_v4.db --format json
  
  # 导出命令到脚本文件
  python 04-next_steps_guide.py --db-path ./senseword_content_v4.db --export-commands ./next_steps.sh
        """
    )
    
    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )
    
    parser.add_argument(
        '--priority',
        choices=['high', 'medium', 'low'],
        help='只显示指定优先级的建议'
    )
    
    parser.add_argument(
        '--format',
        choices=['text', 'json', 'markdown'],
        default='text',
        help='输出格式（默认text）'
    )
    
    parser.add_argument(
        '--export-commands',
        help='导出命令到shell脚本文件'
    )
    
    args = parser.parse_args()
    
    # 验证数据库文件
    import os
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)
    
    # 创建指引工具
    guide = NextStepsGuide(args.db_path)
    
    try:
        # 连接数据库
        guide.connect()
        
        # 导出命令
        if args.export_commands:
            guide.export_commands(args.export_commands, args.priority)
        else:
            # 显示指引
            guide.display_guide(args.priority, args.format)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        guide.close()


if __name__ == "__main__":
    main()
