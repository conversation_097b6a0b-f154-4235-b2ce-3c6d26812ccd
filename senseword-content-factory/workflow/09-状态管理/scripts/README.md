# SenseWord 状态管理脚本

## 概述

这个目录包含 SenseWord Content Factory 的看板式状态管理工具，专注于**纯监控和查询**，不参与具体的业务处理。

## 🎯 核心理念

### 极简状态管理
- **只看不改**: 纯粹的监控和查询工具
- **单一真实来源**: 所有业务环节直接操作数据库
- **手动控制**: 您决定何时执行哪个脚本
- **零耦合**: 通过数据库状态通信，无函数调用依赖

### 看板式状态跟踪
将复杂的单词处理流程分解为5个清晰的状态：
1. **contentGenerated** - 内容已生成
2. **contentAiReviewed** - 内容已审核
3. **ttsIdGenerated** - TTS ID已生成
4. **audioGenerated** - 音频已生成
5. **readyForPublish** - 准备发布

## 📋 脚本列表

### 01-sync_words_to_queue.py
**状态同步脚本** - 唯一的数据修改工具

- **功能**: 将 words_for_publish 表的数据同步到 word_processing_queue 状态管理表
- **使用时机**: 批处理任务完成后，全量更新状态
- **特点**: 幂等操作，支持重复执行

```bash
# 批处理完成后更新状态
python 01-sync_words_to_queue.py --db-path ./db.db --force
```

### 02-status_dashboard.py
**状态看板** - 总体进度监控

- **功能**: 显示当前处理进度和状态分布
- **输出**: 总体统计、各阶段进度、处理瓶颈分析
- **特点**: 纯查询，不修改任何数据

```bash
# 查看状态看板
python 02-status_dashboard.py --db-path ./db.db

# 查看详细信息
python 02-status_dashboard.py --db-path ./db.db --detailed
```

### 03-query_by_status.py
**状态查询工具** - 精确筛选单词

- **功能**: 按状态筛选和查询单词
- **支持**: 预定义阶段、自定义筛选、多种输出格式
- **特点**: 灵活的查询条件，支持分页和导出

```bash
# 查询等待音频生成的单词
python 03-query_by_status.py --db-path ./db.db --stage audio_pending --limit 10

# 自定义查询条件
python 03-query_by_status.py --db-path ./db.db --custom-filter '{"learningLanguage": "en"}'

# 导出为CSV
python 03-query_by_status.py --db-path ./db.db --stage audio_pending --format csv --output-file audio_pending.csv
```

### 04-next_steps_guide.py
**下一步指引** - 智能操作建议

- **功能**: 分析当前状态并建议具体的操作命令
- **输出**: 优先级排序的任务列表、具体命令、时间估算
- **特点**: 智能分析瓶颈，提供可执行的命令

```bash
# 获取操作建议
python 04-next_steps_guide.py --db-path ./db.db

# 只显示高优先级任务
python 04-next_steps_guide.py --db-path ./db.db --priority high

# 导出命令到脚本
python 04-next_steps_guide.py --db-path ./db.db --export-commands ./next_steps.sh
```

## 🔧 预定义查询阶段

| 阶段名称 | 描述 | 筛选条件 |
|----------|------|----------|
| `content_pending` | 等待内容生成 | `contentGenerated = false` |
| `review_pending` | 等待内容审核 | `contentGenerated = true, contentAiReviewed = false` |
| `tts_pending` | 等待TTS生成 | `contentAiReviewed = true, ttsIdGenerated = false` |
| `audio_pending` | 等待音频生成 | `ttsIdGenerated = true, audioGenerated = false` |
| `publish_pending` | 等待发布准备 | `audioGenerated = true, readyForPublish = false` |
| `publish_ready` | 准备发布 | `readyForPublish = true` |
| `fully_complete` | 完全完成 | 所有状态为 true |
| `not_started` | 完全未开始 | 所有状态为 false |

## 📊 典型使用流程

### 日常监控流程
```bash
# 1. 查看总体状态
python 02-status_dashboard.py --db-path ./db.db

# 2. 获取操作建议
python 04-next_steps_guide.py --db-path ./db.db

# 3. 查询具体待处理单词
python 03-query_by_status.py --db-path ./db.db --stage audio_pending --limit 5

# 4. 执行业务脚本（在相应的工作流目录中）
cd ../05-TTS处理/scripts
python audio_generation.py --db-path ../../db.db --batch-size 200

# 5. 更新状态
cd ../../09-状态管理/scripts
python 01-sync_words_to_queue.py --db-path ./db.db --force
```

### 批处理完成后的标准流程
```bash
# 批处理任务完成后，更新状态
python 01-sync_words_to_queue.py --db-path ./db.db --force

# 查看更新后的状态
python 02-status_dashboard.py --db-path ./db.db

# 获取下一步建议
python 04-next_steps_guide.py --db-path ./db.db --priority high
```

## 📈 输出示例

### 状态看板输出
```
📊 SenseWord 处理状态看板
============================================================
🕒 更新时间: 2025-07-13 17:46:30

🗄️  总体状态:
   - 总单词数: 55,703
   - 完全完成: 0
   - 进行中: 55,703
   - 未开始: 0
   - 完成率: 0.0%

📈 各阶段进度:
   - 内容已生成: 55,703
   - 内容已审核: 55,700
   - TTS已生成: 55,703
   - 音频已生成: 0
   - 准备发布: 0

⏳ 待处理统计:
   - 内容生成待处理: 0
   - 内容审核待处理: 3
   - TTS生成待处理: 0
   - 音频生成待处理: 55,703
   - 发布准备待处理: 0

🚨 处理瓶颈:
   🔴 音频生成: 55,703 (音频文件等待生成)
```

### 查询结果输出
```
🔍 查询阶段: audio_pending
📝 描述: 等待音频生成的单词
📊 找到 5 个单词:
--------------------------------------------------------------------------------
ID     单词              语言对      内容   审核   TTS  音频   发布
--------------------------------------------------------------------------------
54261  <USER>     <GROUP>    ✅    ✅    ✅    ❌    ❌
54262  waterresistant  en-zh    ✅    ✅    ✅    ❌    ❌
54263  waters          en-zh    ✅    ✅    ✅    ❌    ❌
54264  watershed       en-zh    ✅    ✅    ✅    ❌    ❌
54265  waterside       en-zh    ✅    ✅    ✅    ❌    ❌
```

### 操作指引输出
```
🎯 SenseWord 下一步操作指引
============================================================

💡 推荐的操作 (2 个任务):

🔴 任务 1: 音频生成
   📝 描述: 生成实际的音频文件
   📊 待处理数量: 55,703
   🔢 建议批次: 279 批次 (每批 200)
   ⏱️  预估时间: 279.0 小时
   📁 工作流路径: 05-TTS处理

   🔧 操作命令:
   # 1. 查询待处理的音频生成单词
   $ python 09-状态管理/scripts/03-query_by_status.py --db-path ./db.db --stage audio_pending --limit 200

   # 2. 执行音频生成处理
   $ cd 05-TTS处理/scripts && python audio_generation.py --db-path ../../db.db --batch-size 200

   # 3. 更新状态（处理完成后）
   $ python 09-状态管理/scripts/01-sync_words_to_queue.py --db-path ./db.db --force
```

## 🌟 设计优势

### ✅ 完全解耦
- 状态管理只负责监控，不参与业务处理
- 各业务环节独立运行，直接操作数据库
- 通过数据库状态通信，无代码依赖

### ✅ 手动控制
- 您完全控制每个步骤的执行时机
- 批处理任务的不可预测性得到完美处理
- 可以在任何环节暂停、检查、重试

### ✅ 智能分析
- 自动识别处理瓶颈和优先级
- 提供具体可执行的操作命令
- 估算处理时间和资源需求

### ✅ 灵活查询
- 多种预定义查询阶段
- 支持自定义筛选条件
- 多种输出格式（表格、JSON、CSV）

## 🔧 与业务环节的协作

状态管理系统与各业务环节的协作模式：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   状态管理      │    │   业务环节      │    │   数据库        │
│   (监控查询)    │    │   (具体处理)    │    │   (单一真实来源) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. 查询状态           │                       │
         ├──────────────────────────────────────────────▶│
         │                       │                       │
         │ 2. 显示建议           │                       │
         │◀──────────────────────│                       │
         │                       │                       │
         │                       │ 3. 执行处理           │
         │                       ├──────────────────────▶│
         │                       │                       │
         │ 4. 更新状态           │                       │
         ├──────────────────────────────────────────────▶│
```

## 📝 最佳实践

### 🔄 定期监控
- 每天开始工作前查看状态看板
- 批处理完成后立即更新状态
- 定期检查处理瓶颈和优先级

### ⚠️ 注意事项
- 状态管理脚本不处理具体业务逻辑
- 批处理任务在各自的工作流目录中执行
- 使用 `--force` 参数会清空现有队列数据

### 🎯 工作流程
1. **监控**: 使用状态看板了解当前进度
2. **分析**: 使用操作指引获取建议
3. **查询**: 使用状态查询获取具体数据
4. **执行**: 在业务环节中执行具体处理
5. **更新**: 使用同步脚本更新状态
6. **重复**: 回到第1步继续监控

这个状态管理系统为 SenseWord 提供了清晰的处理进度可视化和智能的操作指引，让您能够高效地管理复杂的单词处理流程。
