#!/usr/bin/env python3
"""
SenseWord 状态查询工具 - 按状态筛选和查询单词
纯查询工具，不修改任何数据，只展示符合条件的单词

功能：
1. 按预定义阶段查询单词
2. 自定义状态筛选条件
3. 多种输出格式支持
4. 分页和限制功能

使用方法：
    python 03-query_by_status.py --db-path /path/to/db.db --stage content_pending [选项]

参数：
    --db-path: 数据库文件路径（必需）
    --stage: 预定义的处理阶段
    --custom-filter: 自定义JSON格式的筛选条件
    --limit: 限制返回数量
    --offset: 偏移量（用于分页）
    --format: 输出格式（table/json/csv/count）
    --output-file: 输出到文件
"""

import sqlite3
import argparse
import sys
import json
import csv
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class StatusQuery:
    """状态查询工具"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        
        # 预定义的阶段筛选条件
        self.predefined_stages = {
            'content_pending': {
                'description': '等待内容生成的单词',
                'filter': {'contentGenerated': False}
            },
            'review_pending': {
                'description': '等待内容审核的单词',
                'filter': {'contentGenerated': True, 'contentAiReviewed': False}
            },
            'tts_pending': {
                'description': '等待TTS生成的单词',
                'filter': {'contentAiReviewed': True, 'ttsIdGenerated': False}
            },
            'audio_pending': {
                'description': '等待音频生成的单词',
                'filter': {'ttsIdGenerated': True, 'audioGenerated': False}
            },
            'publish_pending': {
                'description': '等待发布准备的单词',
                'filter': {'audioGenerated': True, 'readyForPublish': False}
            },
            'publish_ready': {
                'description': '准备发布的单词',
                'filter': {'readyForPublish': True}
            },
            'fully_complete': {
                'description': '完全完成的单词',
                'filter': {
                    'contentGenerated': True,
                    'contentAiReviewed': True,
                    'ttsIdGenerated': True,
                    'audioGenerated': True,
                    'readyForPublish': True
                }
            },
            'not_started': {
                'description': '完全未开始的单词',
                'filter': {
                    'contentGenerated': False,
                    'contentAiReviewed': False,
                    'ttsIdGenerated': False,
                    'audioGenerated': False,
                    'readyForPublish': False
                }
            }
        }
    
    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
    
    def build_where_clause(self, filter_conditions: Dict) -> Tuple[str, List]:
        """构建WHERE子句"""
        conditions = []
        params = []
        
        for field, value in filter_conditions.items():
            if field in ['contentGenerated', 'contentAiReviewed', 'ttsIdGenerated', 
                        'audioGenerated', 'readyForPublish']:
                conditions.append(f"{field} = ?")
                params.append(1 if value else 0)
            elif field in ['learningLanguage', 'scaffoldingLanguage']:
                conditions.append(f"{field} = ?")
                params.append(value)
            elif field == 'word':
                if isinstance(value, str):
                    conditions.append("word LIKE ?")
                    params.append(f"%{value}%")
                elif isinstance(value, list):
                    placeholders = ','.join(['?' for _ in value])
                    conditions.append(f"word IN ({placeholders})")
                    params.extend(value)
        
        where_clause = " AND ".join(conditions) if conditions else "1=1"
        return where_clause, params
    
    def query_words(self, 
                   filter_conditions: Dict,
                   limit: int = None,
                   offset: int = 0,
                   include_content: bool = False) -> List[Dict]:
        """查询符合条件的单词"""
        try:
            cursor = self.connection.cursor()
            
            # 构建查询字段
            base_fields = "id, word, learningLanguage, scaffoldingLanguage, " \
                         "contentGenerated, contentAiReviewed, ttsIdGenerated, " \
                         "audioGenerated, readyForPublish, createdAt, updatedAt"
            
            if include_content:
                # 如果需要内容，从主表查询
                query = f"""
                    SELECT q.{base_fields}, w.contentJson
                    FROM word_processing_queue q
                    LEFT JOIN words_for_publish w ON q.word = w.word 
                        AND q.learningLanguage = w.learningLanguage 
                        AND q.scaffoldingLanguage = w.scaffoldingLanguage
                """
            else:
                query = f"SELECT {base_fields} FROM word_processing_queue q"
            
            # 构建WHERE子句
            where_clause, params = self.build_where_clause(filter_conditions)
            query += f" WHERE {where_clause}"
            
            # 添加排序
            query += " ORDER BY q.updatedAt DESC, q.id"
            
            # 添加分页
            if limit:
                query += f" LIMIT {limit}"
            if offset > 0:
                query += f" OFFSET {offset}"
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # 转换为字典列表
            if include_content:
                columns = ['id', 'word', 'learningLanguage', 'scaffoldingLanguage',
                          'contentGenerated', 'contentAiReviewed', 'ttsIdGenerated',
                          'audioGenerated', 'readyForPublish', 'createdAt', 'updatedAt',
                          'contentJson']
            else:
                columns = ['id', 'word', 'learningLanguage', 'scaffoldingLanguage',
                          'contentGenerated', 'contentAiReviewed', 'ttsIdGenerated',
                          'audioGenerated', 'readyForPublish', 'createdAt', 'updatedAt']
            
            words = []
            for row in results:
                word_dict = dict(zip(columns, row))
                # 转换布尔值
                for bool_field in ['contentGenerated', 'contentAiReviewed', 'ttsIdGenerated',
                                  'audioGenerated', 'readyForPublish']:
                    word_dict[bool_field] = bool(word_dict[bool_field])
                words.append(word_dict)
            
            return words
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return []
    
    def count_words(self, filter_conditions: Dict) -> int:
        """统计符合条件的单词数量"""
        try:
            cursor = self.connection.cursor()
            
            where_clause, params = self.build_where_clause(filter_conditions)
            query = f"SELECT COUNT(*) FROM word_processing_queue WHERE {where_clause}"
            
            cursor.execute(query, params)
            return cursor.fetchone()[0] or 0
            
        except Exception as e:
            print(f"❌ 统计失败: {e}")
            return 0
    
    def display_table(self, words: List[Dict], show_status: bool = True):
        """以表格形式显示单词"""
        if not words:
            print("📭 没有找到符合条件的单词")
            return
        
        print(f"📊 找到 {len(words)} 个单词:")
        print("-" * 80)
        
        # 表头
        if show_status:
            print(f"{'ID':<6} {'单词':<15} {'语言对':<8} {'内容':<4} {'审核':<4} {'TTS':<4} {'音频':<4} {'发布':<4}")
            print("-" * 80)
        else:
            print(f"{'ID':<6} {'单词':<20} {'语言对':<10} {'更新时间':<20}")
            print("-" * 80)
        
        # 数据行
        for word in words:
            lang_pair = f"{word['learningLanguage']}-{word['scaffoldingLanguage']}"
            
            if show_status:
                status_icons = [
                    "✅" if word['contentGenerated'] else "❌",
                    "✅" if word['contentAiReviewed'] else "❌",
                    "✅" if word['ttsIdGenerated'] else "❌",
                    "✅" if word['audioGenerated'] else "❌",
                    "✅" if word['readyForPublish'] else "❌"
                ]
                print(f"{word['id']:<6} {word['word']:<15} {lang_pair:<8} "
                      f"{status_icons[0]:<4} {status_icons[1]:<4} {status_icons[2]:<4} "
                      f"{status_icons[3]:<4} {status_icons[4]:<4}")
            else:
                updated_time = word.get('updatedAt', '')[:19] if word.get('updatedAt') else ''
                print(f"{word['id']:<6} {word['word']:<20} {lang_pair:<10} {updated_time:<20}")
    
    def export_csv(self, words: List[Dict], output_file: str):
        """导出为CSV文件"""
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                if not words:
                    return
                
                fieldnames = words[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for word in words:
                    writer.writerow(word)
            
            print(f"✅ 已导出到 {output_file}")
            
        except Exception as e:
            print(f"❌ 导出CSV失败: {e}")
    
    def export_json(self, words: List[Dict], output_file: str):
        """导出为JSON文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as jsonfile:
                json.dump(words, jsonfile, indent=2, ensure_ascii=False, default=str)
            
            print(f"✅ 已导出到 {output_file}")
            
        except Exception as e:
            print(f"❌ 导出JSON失败: {e}")
    
    def list_stages(self):
        """列出所有预定义阶段"""
        print("📋 可用的预定义阶段:")
        print("-" * 50)
        
        for stage_name, stage_info in self.predefined_stages.items():
            print(f"🔹 {stage_name}")
            print(f"   描述: {stage_info['description']}")
            print(f"   筛选条件: {stage_info['filter']}")
            print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord 状态查询工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
预定义阶段:
  content_pending   - 等待内容生成的单词
  review_pending    - 等待内容审核的单词
  tts_pending       - 等待TTS生成的单词
  audio_pending     - 等待音频生成的单词
  publish_pending   - 等待发布准备的单词
  publish_ready     - 准备发布的单词
  fully_complete    - 完全完成的单词
  not_started       - 完全未开始的单词

使用示例:
  # 查询等待内容生成的单词
  python 03-query_by_status.py --db-path ./db.db --stage content_pending --limit 10
  
  # 自定义查询条件
  python 03-query_by_status.py --db-path ./db.db --custom-filter '{"learningLanguage": "en"}'
  
  # 导出为CSV
  python 03-query_by_status.py --db-path ./db.db --stage audio_pending --format csv --output-file audio_pending.csv
  
  # 仅统计数量
  python 03-query_by_status.py --db-path ./db.db --stage publish_ready --format count
        """
    )
    
    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )
    
    parser.add_argument(
        '--stage',
        help='预定义的处理阶段'
    )
    
    parser.add_argument(
        '--custom-filter',
        help='自定义JSON格式的筛选条件'
    )
    
    parser.add_argument(
        '--limit',
        type=int,
        default=20,
        help='限制返回数量（默认20）'
    )
    
    parser.add_argument(
        '--offset',
        type=int,
        default=0,
        help='偏移量，用于分页（默认0）'
    )
    
    parser.add_argument(
        '--format',
        choices=['table', 'json', 'csv', 'count'],
        default='table',
        help='输出格式（默认table）'
    )
    
    parser.add_argument(
        '--output-file',
        help='输出到文件'
    )
    
    parser.add_argument(
        '--include-content',
        action='store_true',
        help='包含内容JSON（仅用于json/csv格式）'
    )
    
    parser.add_argument(
        '--list-stages',
        action='store_true',
        help='列出所有预定义阶段'
    )
    
    args = parser.parse_args()
    
    # 验证数据库文件
    import os
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)
    
    # 创建查询工具
    query_tool = StatusQuery(args.db_path)
    
    try:
        # 连接数据库
        query_tool.connect()
        
        # 列出阶段
        if args.list_stages:
            query_tool.list_stages()
            return
        
        # 确定筛选条件
        filter_conditions = {}
        
        if args.stage:
            if args.stage not in query_tool.predefined_stages:
                print(f"❌ 未知的阶段: {args.stage}")
                print("使用 --list-stages 查看可用阶段")
                sys.exit(1)
            filter_conditions = query_tool.predefined_stages[args.stage]['filter']
            print(f"🔍 查询阶段: {args.stage}")
            print(f"📝 描述: {query_tool.predefined_stages[args.stage]['description']}")
        
        if args.custom_filter:
            try:
                custom_filter = json.loads(args.custom_filter)
                filter_conditions.update(custom_filter)
                print(f"🔍 自定义筛选条件: {custom_filter}")
            except json.JSONDecodeError as e:
                print(f"❌ 自定义筛选条件JSON格式错误: {e}")
                sys.exit(1)
        
        if not filter_conditions:
            print("❌ 请指定 --stage 或 --custom-filter")
            sys.exit(1)
        
        # 仅统计数量
        if args.format == 'count':
            count = query_tool.count_words(filter_conditions)
            print(count)
            return
        
        # 查询单词
        words = query_tool.query_words(
            filter_conditions,
            limit=args.limit,
            offset=args.offset,
            include_content=args.include_content
        )
        
        # 输出结果
        if args.format == 'table':
            query_tool.display_table(words)
        elif args.format == 'json':
            if args.output_file:
                query_tool.export_json(words, args.output_file)
            else:
                print(json.dumps(words, indent=2, ensure_ascii=False, default=str))
        elif args.format == 'csv':
            if args.output_file:
                query_tool.export_csv(words, args.output_file)
            else:
                print("❌ CSV格式需要指定 --output-file")
                sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        query_tool.close()


if __name__ == "__main__":
    main()
