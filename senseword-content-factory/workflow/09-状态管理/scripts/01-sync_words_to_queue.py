#!/usr/bin/env python3
"""
SenseWord Content Factory - 单词状态同步脚本 v1.0
将 words_for_publish 表中的单词同步到 word_processing_queue 看板式状态管理表

核心功能：
1. 智能状态分析：根据现有数据自动判断处理进度
2. 幂等操作：支持重复执行，确保数据一致性
3. 批量处理：高效处理大规模数据
4. 安全模式：支持模拟运行和强制重建

使用场景：
- 初始化看板状态管理系统
- 批处理任务完成后更新状态
- 数据一致性检查和修复
- 状态统计和分析

使用方法：
    python 01-sync_words_to_queue.py --db-path /path/to/senseword_content_v4.db [选项]

参数：
    --db-path: 数据库文件路径（必需）
    --dry-run: 仅模拟运行，不实际插入数据
    --batch-size: 批量处理大小（默认2000，推荐范围500-5000）
    --force: 强制重新同步（会清空现有队列数据）
    --stats-only: 仅显示统计信息，不执行同步
"""

import sqlite3
import argparse
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json


class WordQueueSynchronizer:
    """单词队列同步器 - 看板式状态管理的核心工具"""
    
    def __init__(self, db_path: str, batch_size: int = 2000):
        self.db_path = db_path
        self.batch_size = batch_size
        self.connection: Optional[sqlite3.Connection] = None
        self.sync_stats = {
            'total_words': 0,
            'synced_words': 0,
            'skipped_words': 0,
            'error_words': 0,
            'content_generated': 0,
            'content_reviewed': 0,
            'tts_generated': 0,
            'audio_generated': 0,
            'ready_for_publish': 0
        }
    
    def connect(self) -> sqlite3.Connection:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.execute("PRAGMA foreign_keys = ON")
            print(f"✅ 成功连接到数据库: {self.db_path}")
            return self.connection
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("✅ 数据库连接已关闭")
    
    def check_tables_exist(self) -> bool:
        """检查必要的表是否存在"""
        try:
            cursor = self.connection.cursor()
            
            # 检查源表
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='words_for_publish'
            """)
            if not cursor.fetchone():
                print("❌ words_for_publish 表不存在")
                return False
            
            # 检查目标表
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='word_processing_queue'
            """)
            if not cursor.fetchone():
                print("❌ word_processing_queue 表不存在，请先运行数据库初始化脚本")
                return False
            
            print("✅ 所有必要的表都存在")
            return True
            
        except Exception as e:
            print(f"❌ 检查表存在性失败: {e}")
            return False
    
    def get_existing_words_count(self) -> int:
        """获取现有队列中的单词数量"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM word_processing_queue")
            count = cursor.fetchone()[0]
            print(f"📊 当前队列中已有 {count:,} 个单词")
            return count
        except Exception as e:
            print(f"❌ 获取现有单词数量失败: {e}")
            return 0
    
    def clear_existing_queue(self) -> bool:
        """清空现有队列数据"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("DELETE FROM word_processing_queue")
            deleted_count = cursor.rowcount
            print(f"🗑️  已清空队列中的 {deleted_count:,} 个单词")
            return True
        except Exception as e:
            print(f"❌ 清空队列失败: {e}")
            return False
    
    def analyze_word_status(self, word_data: Tuple) -> Dict[str, bool]:
        """分析单词的当前状态，智能设置初始状态值"""
        (word_id, word, learning_lang, scaffolding_lang, content_json, 
         publish_status, tts_status, ai_audit_score, ai_audit_should_regenerate,
         ai_audit_comment, audit_status, ai_audit_should_remove, 
         tts_hash_list, audio_generated) = word_data
        
        # 初始化所有状态为False
        status = {
            'contentGenerated': False,
            'contentAiReviewed': False,
            'ttsIdGenerated': False,
            'audioGenerated': False,
            'readyForPublish': False
        }
        
        # 1. 判断内容是否已生成 - 检查contentJson是否有实际内容
        if (content_json and 
            content_json.strip() and 
            content_json != '{}' and 
            len(content_json.strip()) > 10):  # 确保不是空的JSON
            status['contentGenerated'] = True
            self.sync_stats['content_generated'] += 1
        
        # 2. 判断内容是否已审核 - 检查auditStatus是否为completed
        if (audit_status and 
            audit_status.lower() in ['completed', 'approved', 'passed'] and 
            ai_audit_score is not None and ai_audit_score > 0):
            status['contentAiReviewed'] = True
            self.sync_stats['content_reviewed'] += 1
        
        # 3. 判断TTS ID是否已生成 - 检查ttsHashList是否有内容
        if (tts_hash_list and 
            tts_hash_list.strip() and 
            tts_hash_list != '[]' and 
            len(tts_hash_list.strip()) > 5):  # 确保不是空数组
            status['ttsIdGenerated'] = True
            self.sync_stats['tts_generated'] += 1
        
        # 4. 判断音频是否已生成 - 检查audioGenerated字段
        if audio_generated == 1:
            status['audioGenerated'] = True
            self.sync_stats['audio_generated'] += 1
        
        # 5. 判断是否准备发布 - 综合判断
        if (status['audioGenerated'] and 
            publish_status and publish_status != 'pending_upload' and
            (not ai_audit_should_remove or ai_audit_should_remove == 0)):
            status['readyForPublish'] = True
            self.sync_stats['ready_for_publish'] += 1
        
        return status
    
    def fetch_words_batch(self, offset: int, limit: int) -> List[Tuple]:
        """批量获取单词数据"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT id, word, learningLanguage, scaffoldingLanguage, contentJson,
                       publishStatus, ttsStatus, aiAuditScore, aiAuditShouldRegenerate,
                       aiAuditComment, auditStatus, aiAuditShouldRemove,
                       ttsHashList, audioGenerated
                FROM words_for_publish
                ORDER BY id
                LIMIT ? OFFSET ?
            """, (limit, offset))
            
            return cursor.fetchall()
            
        except Exception as e:
            print(f"❌ 获取单词批次失败: {e}")
            return []
    
    def insert_words_batch(self, word_batch: List[Tuple], dry_run: bool = False) -> int:
        """批量插入单词到队列表"""
        if dry_run:
            # 在模拟模式下也要分析状态以更新统计信息
            for word_data in word_batch:
                self.analyze_word_status(word_data)
            print(f"🔍 [模拟] 准备插入 {len(word_batch)} 个单词")
            return len(word_batch)
        
        try:
            cursor = self.connection.cursor()
            insert_data = []
            
            for word_data in word_batch:
                _, word, learning_lang, scaffolding_lang = word_data[:4]
                status = self.analyze_word_status(word_data)
                
                insert_data.append((
                    word,
                    learning_lang,
                    scaffolding_lang,
                    status['contentGenerated'],
                    status['contentAiReviewed'],
                    status['ttsIdGenerated'],
                    status['audioGenerated'],
                    status['readyForPublish']
                ))
            
            # 批量插入
            cursor.executemany("""
                INSERT OR IGNORE INTO word_processing_queue 
                (word, learningLanguage, scaffoldingLanguage, 
                 contentGenerated, contentAiReviewed, ttsIdGenerated, 
                 audioGenerated, readyForPublish)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, insert_data)
            
            inserted_count = cursor.rowcount
            return inserted_count
            
        except Exception as e:
            print(f"❌ 批量插入失败: {e}")
            return 0
    
    def get_total_words_count(self) -> int:
        """获取总单词数量"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM words_for_publish")
            return cursor.fetchone()[0]
        except Exception as e:
            print(f"❌ 获取总单词数量失败: {e}")
            return 0
    
    def get_detailed_statistics(self) -> Dict:
        """获取详细的数据库统计信息"""
        stats = {}
        
        try:
            cursor = self.connection.cursor()
            
            # 获取words_for_publish表的状态分布
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN contentJson IS NOT NULL AND LENGTH(contentJson) > 10 THEN 1 ELSE 0 END) as has_content,
                    SUM(CASE WHEN auditStatus = 'completed' AND aiAuditScore > 0 THEN 1 ELSE 0 END) as reviewed,
                    SUM(CASE WHEN ttsHashList IS NOT NULL AND LENGTH(ttsHashList) > 5 THEN 1 ELSE 0 END) as has_tts,
                    SUM(CASE WHEN audioGenerated = 1 THEN 1 ELSE 0 END) as has_audio,
                    SUM(CASE WHEN publishStatus != 'pending_upload' THEN 1 ELSE 0 END) as published
                FROM words_for_publish
            """)
            
            result = cursor.fetchone()
            if result:
                stats['words_for_publish'] = {
                    'total': result[0],
                    'has_content': result[1],
                    'reviewed': result[2],
                    'has_tts': result[3],
                    'has_audio': result[4],
                    'published': result[5]
                }
            
            # 获取word_processing_queue表的状态分布
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN contentGenerated = 1 THEN 1 ELSE 0 END) as content_generated,
                    SUM(CASE WHEN contentAiReviewed = 1 THEN 1 ELSE 0 END) as content_reviewed,
                    SUM(CASE WHEN ttsIdGenerated = 1 THEN 1 ELSE 0 END) as tts_generated,
                    SUM(CASE WHEN audioGenerated = 1 THEN 1 ELSE 0 END) as audio_generated,
                    SUM(CASE WHEN readyForPublish = 1 THEN 1 ELSE 0 END) as ready_publish
                FROM word_processing_queue
            """)
            
            result = cursor.fetchone()
            if result:
                stats['word_processing_queue'] = {
                    'total': result[0] or 0,
                    'content_generated': result[1] or 0,
                    'content_reviewed': result[2] or 0,
                    'tts_generated': result[3] or 0,
                    'audio_generated': result[4] or 0,
                    'ready_publish': result[5] or 0
                }
            else:
                stats['word_processing_queue'] = {
                    'total': 0,
                    'content_generated': 0,
                    'content_reviewed': 0,
                    'tts_generated': 0,
                    'audio_generated': 0,
                    'ready_publish': 0
                }
            
            return stats
            
        except Exception as e:
            print(f"❌ 获取详细统计信息失败: {e}")
            return stats

    def print_detailed_statistics(self, stats: Dict):
        """打印详细的统计信息"""
        print("\n📊 详细数据库统计信息:")

        if 'words_for_publish' in stats:
            wp_stats = stats['words_for_publish']
            print(f"\n🗄️  words_for_publish 表:")
            print(f"   - 总单词数: {wp_stats.get('total', 0):,}")
            print(f"   - 有内容: {wp_stats.get('has_content', 0):,}")
            print(f"   - 已审核: {wp_stats.get('reviewed', 0):,}")
            print(f"   - 有TTS: {wp_stats.get('has_tts', 0):,}")
            print(f"   - 有音频: {wp_stats.get('has_audio', 0):,}")
            print(f"   - 已发布: {wp_stats.get('published', 0):,}")

        if 'word_processing_queue' in stats:
            wq_stats = stats['word_processing_queue']
            print(f"\n📋 word_processing_queue 表:")
            print(f"   - 总单词数: {wq_stats.get('total', 0):,}")
            print(f"   - 内容已生成: {wq_stats.get('content_generated', 0):,}")
            print(f"   - 内容已审核: {wq_stats.get('content_reviewed', 0):,}")
            print(f"   - TTS已生成: {wq_stats.get('tts_generated', 0):,}")
            print(f"   - 音频已生成: {wq_stats.get('audio_generated', 0):,}")
            print(f"   - 准备发布: {wq_stats.get('ready_publish', 0):,}")
        else:
            print(f"\n📋 word_processing_queue 表:")
            print(f"   - 总单词数: 0")
            print(f"   - 内容已生成: 0")
            print(f"   - 内容已审核: 0")
            print(f"   - TTS已生成: 0")
            print(f"   - 音频已生成: 0")
            print(f"   - 准备发布: 0")

    def synchronize_words(self, dry_run: bool = False, force: bool = False) -> bool:
        """执行单词同步"""
        print("🚀 开始单词数据同步...")

        # 检查表存在性
        if not self.check_tables_exist():
            return False

        # 获取总数量
        total_count = self.get_total_words_count()
        self.sync_stats['total_words'] = total_count
        print(f"📊 待同步单词总数: {total_count:,}")

        if total_count == 0:
            print("⚠️  没有找到待同步的单词")
            return True

        # 检查现有队列
        existing_count = self.get_existing_words_count()
        if existing_count > 0:
            if force:
                print("🔄 强制模式：清空现有队列数据")
                if not dry_run:
                    self.clear_existing_queue()
            else:
                print("⚠️  队列中已有数据，使用 --force 参数强制重新同步")
                return False

        # 批量处理
        processed = 0
        offset = 0

        try:
            while offset < total_count:
                # 获取当前批次
                batch = self.fetch_words_batch(offset, self.batch_size)
                if not batch:
                    break

                # 插入当前批次
                inserted = self.insert_words_batch(batch, dry_run)
                self.sync_stats['synced_words'] += inserted

                processed += len(batch)
                offset += self.batch_size

                # 显示进度
                progress = (processed / total_count) * 100
                print(f"📈 进度: {processed:,}/{total_count:,} ({progress:.1f}%) - 已插入: {inserted}")

                # 提交事务（每批次）
                if not dry_run:
                    self.connection.commit()

            print(f"\n✅ 同步完成！")
            return True

        except Exception as e:
            print(f"\n❌ 同步过程中发生错误: {e}")
            if not dry_run:
                self.connection.rollback()
            return False

    def print_sync_statistics(self):
        """打印同步统计信息"""
        print("\n📊 同步统计信息:")
        print(f"   - 总单词数: {self.sync_stats['total_words']:,}")
        print(f"   - 已同步: {self.sync_stats['synced_words']:,}")
        print(f"   - 跳过: {self.sync_stats['skipped_words']:,}")
        print(f"   - 错误: {self.sync_stats['error_words']:,}")

        print("\n📈 状态分布:")
        print(f"   - 内容已生成: {self.sync_stats['content_generated']:,}")
        print(f"   - 内容已审核: {self.sync_stats['content_reviewed']:,}")
        print(f"   - TTS已生成: {self.sync_stats['tts_generated']:,}")
        print(f"   - 音频已生成: {self.sync_stats['audio_generated']:,}")
        print(f"   - 准备发布: {self.sync_stats['ready_for_publish']:,}")

        if self.sync_stats['total_words'] > 0:
            completion_rate = (self.sync_stats['synced_words'] / self.sync_stats['total_words']) * 100
            print(f"\n🎯 同步完成率: {completion_rate:.1f}%")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord 单词状态同步脚本 v1.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 查看详细统计信息
  python 01-sync_words_to_queue.py --db-path ./senseword_content_v4.db --stats-only

  # 模拟同步（不实际插入数据）
  python 01-sync_words_to_queue.py --db-path ./senseword_content_v4.db --dry-run

  # 实际同步（推荐批次大小2000）
  python 01-sync_words_to_queue.py --db-path ./senseword_content_v4.db --batch-size 2000

  # 强制重新同步（清空现有队列数据）
  python 01-sync_words_to_queue.py --db-path ./senseword_content_v4.db --force

  # 高性能同步（大批次）
  python 01-sync_words_to_queue.py --db-path ./senseword_content_v4.db --batch-size 5000

批次大小建议：
  - 小数据集（<10K）: 500-1000
  - 中等数据集（10K-100K）: 1000-2000（推荐）
  - 大数据集（>100K）: 2000-5000
  - 内存充足时最大可用: 10000
        """
    )

    parser.add_argument(
        '--db-path',
        required=True,
        help='数据库文件路径'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅模拟运行，不实际插入数据'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=2000,
        help='批量处理大小（默认2000，推荐范围500-5000）'
    )

    parser.add_argument(
        '--force',
        action='store_true',
        help='强制重新同步（会清空现有队列数据）'
    )

    parser.add_argument(
        '--stats-only',
        action='store_true',
        help='仅显示统计信息，不执行同步'
    )

    args = parser.parse_args()

    # 验证数据库文件
    import os
    if not os.path.exists(args.db_path):
        print(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)

    # 验证批次大小
    if args.batch_size < 100 or args.batch_size > 10000:
        print(f"⚠️  批次大小 {args.batch_size} 超出推荐范围 (100-10000)")
        print("   建议使用 500-5000 之间的值以获得最佳性能")

    # 创建同步器
    synchronizer = WordQueueSynchronizer(args.db_path, args.batch_size)

    try:
        # 连接数据库
        synchronizer.connect()

        if args.stats_only:
            # 仅显示统计信息模式
            print("📊 统计信息模式：显示详细数据库状态")
            stats = synchronizer.get_detailed_statistics()
            synchronizer.print_detailed_statistics(stats)
            sys.exit(0)

        # 执行同步
        if args.dry_run:
            print("🔍 模拟模式：不会实际修改数据库")

        success = synchronizer.synchronize_words(
            dry_run=args.dry_run,
            force=args.force
        )

        # 打印统计信息
        synchronizer.print_sync_statistics()

        if success:
            if args.dry_run:
                print("\n🎉 模拟同步完成！")
                print("💡 确认无误后，移除 --dry-run 参数执行实际同步")
            else:
                print("\n🎉 单词数据同步成功！")
                print("✅ 看板式状态管理系统已就绪")
            sys.exit(0)
        else:
            print("\n❌ 单词数据同步失败！")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
    finally:
        synchronizer.close()


if __name__ == "__main__":
    main()
