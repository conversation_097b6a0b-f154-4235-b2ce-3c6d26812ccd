#!/usr/bin/env python3
"""
SenseWord AI审核结果统一处理器 v4.0
基于v4数据库架构和看板式状态管理的统一审核结果处理脚本

功能：
- 统一处理AI审核结果JSONL文件
- 支持更新审核字段、应用编辑指令、移除问题单词
- 基于看板式状态管理自动更新处理状态
- 完整的数据库备份和事务管理
- 智能操作分类和优先级处理

使用方法：
    python3 02_process_audit_results.py --results audit_results.jsonl [--db-path PATH] [--dry-run]

参数：
    --results: AI审核结果JSONL文件路径
    --db-path: 数据库文件路径（默认：./senseword_content_v4.db）
    --dry-run: 预览模式，不实际修改数据库
    --backup-dir: 备份目录路径（默认：./backups）
    --report-dir: 报告输出目录（默认：./reports）

作者：AI Assistant
日期：2025-07-13
版本：v4.0 - 统一审核结果处理器
"""

import json
import sqlite3
import logging
import argparse
import shutil
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_DB_PATH = "./senseword_content_v4.db"
DEFAULT_BACKUP_DIR = "./backups"
DEFAULT_REPORT_DIR = "./reports"


class OperationType(Enum):
    """操作类型枚举"""
    REMOVE = "remove"      # 删除操作（最高优先级）
    EDIT = "edit"          # 编辑操作（中等优先级）
    UPDATE = "update"      # 更新操作（最低优先级）


@dataclass
class EditInstruction:
    """编辑指令数据类"""
    operation: str
    path: str
    newValue: Any
    reason: str
    
    def validate(self) -> bool:
        """验证编辑指令的有效性"""
        if not all([self.operation, self.path]):
            return False
        
        # 检查路径格式
        if not self.path.startswith('contentJson.'):
            return False
            
        # 检查操作类型
        if self.operation not in ['update']:
            return False
            
        return True


@dataclass
class AuditResult:
    """审核结果数据类"""
    id: int
    word: str
    aiAuditScore: Optional[int]
    aiAuditShouldRegenerate: int
    aiAuditShouldRemove: int
    aiAuditComment: str
    aiAuditEditInstructions: List[EditInstruction]
    
    def get_operation_type(self) -> OperationType:
        """根据审核结果确定操作类型"""
        # 优先级：删除 > 编辑 > 更新
        if self.requires_removal():
            return OperationType.REMOVE
        elif self.requires_editing():
            return OperationType.EDIT
        else:
            return OperationType.UPDATE
    
    def requires_removal(self) -> bool:
        """是否需要删除"""
        return (self.aiAuditShouldRemove == 1 or 
                self.aiAuditScore == 0)
    
    def requires_editing(self) -> bool:
        """是否需要编辑"""
        return (self.aiAuditEditInstructions and 
                len(self.aiAuditEditInstructions) > 0)
    
    def requires_update_only(self) -> bool:
        """是否仅需要更新审核字段"""
        return (not self.requires_removal() and 
                not self.requires_editing())


@dataclass
class ProcessingResult:
    """处理结果数据类"""
    total_processed: int = 0
    successful_updates: int = 0
    successful_edits: int = 0
    successful_removals: int = 0
    failed_operations: int = 0
    errors: List[str] = None
    warnings: List[str] = None
    processing_time: float = 0.0
    backup_file: Optional[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class ResultsLoader:
    """审核结果加载器"""
    
    @staticmethod
    def load_jsonl_file(file_path: Path) -> List[Dict]:
        """加载JSONL文件"""
        try:
            results = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        result = json.loads(line)
                        results.append(result)
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ 跳过第{line_num}行，JSON解析失败: {e}")
                        continue
            
            logger.info(f"📖 成功加载 {len(results)} 条审核结果")
            return results
            
        except Exception as e:
            logger.error(f"❌ 加载JSONL文件失败: {e}")
            return []
    
    @staticmethod
    def parse_audit_result(result_data: Dict) -> Optional[AuditResult]:
        """解析单个审核结果"""
        try:
            # 解析编辑指令
            edit_instructions = []
            if 'aiAuditEditInstructions' in result_data:
                for instr_data in result_data['aiAuditEditInstructions']:
                    instruction = EditInstruction(
                        operation=instr_data.get('operation', ''),
                        path=instr_data.get('path', ''),
                        newValue=instr_data.get('newValue'),
                        reason=instr_data.get('reason', '')
                    )
                    if instruction.validate():
                        edit_instructions.append(instruction)
            
            # 创建审核结果对象
            audit_result = AuditResult(
                id=result_data.get('id'),
                word=result_data.get('word', ''),
                aiAuditScore=result_data.get('aiAuditScore'),
                aiAuditShouldRegenerate=result_data.get('aiAuditShouldRegenerate', 0),
                aiAuditShouldRemove=result_data.get('aiAuditShouldRemove', 0),
                aiAuditComment=result_data.get('aiAuditComment', ''),
                aiAuditEditInstructions=edit_instructions
            )
            
            return audit_result
            
        except Exception as e:
            logger.warning(f"⚠️ 解析审核结果失败: {e}")
            return None


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
    
    def connect(self) -> bool:
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.execute("PRAGMA foreign_keys = ON")
            logger.info(f"✅ 成功连接到数据库: {self.db_path}")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("✅ 数据库连接已关闭")
    
    def begin_transaction(self):
        """开始事务"""
        if self.connection:
            self.connection.execute("BEGIN TRANSACTION")
            logger.info("🔄 开始数据库事务")
    
    def commit_transaction(self):
        """提交事务"""
        if self.connection:
            self.connection.commit()
            logger.info("✅ 事务提交成功")
    
    def rollback_transaction(self):
        """回滚事务"""
        if self.connection:
            self.connection.rollback()
            logger.info("🔄 事务已回滚")
    
    def check_required_tables(self) -> bool:
        """检查必需的数据库表是否存在"""
        try:
            cursor = self.connection.cursor()
            required_tables = ['words_for_publish', 'word_processing_queue']
            
            for table in required_tables:
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table,))
                
                if not cursor.fetchone():
                    logger.error(f"❌ 缺少必需的表: {table}")
                    return False
            
            logger.info(f"✅ 所有必需的表都存在")
            return True
            
        except Exception as e:
            logger.error(f"❌ 检查数据库表失败: {e}")
            return False

    def get_word_data(self, word_id: int) -> Optional[Dict]:
        """获取单词数据"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT id, word, contentJson, learningLanguage, scaffoldingLanguage
                FROM words_for_publish
                WHERE id = ?
            """, (word_id,))

            result = cursor.fetchone()
            if result:
                return {
                    'id': result[0],
                    'word': result[1],
                    'contentJson': json.loads(result[2]) if result[2] else {},
                    'learningLanguage': result[3],
                    'scaffoldingLanguage': result[4]
                }
            return None

        except Exception as e:
            logger.error(f"❌ 获取单词数据失败 (ID: {word_id}): {e}")
            return None

    def update_audit_fields(self, word_id: int, audit_result: AuditResult) -> bool:
        """更新审核字段"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                UPDATE words_for_publish
                SET aiAuditScore = ?,
                    aiAuditShouldRegenerate = ?,
                    aiAuditShouldRemove = ?,
                    aiAuditComment = ?,
                    updatedAt = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                audit_result.aiAuditScore,
                audit_result.aiAuditShouldRegenerate,
                audit_result.aiAuditShouldRemove,
                audit_result.aiAuditComment,
                word_id
            ))

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ 更新审核字段失败 (ID: {word_id}): {e}")
            return False

    def update_content_json(self, word_id: int, content_json: Dict) -> bool:
        """更新contentJson字段"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                UPDATE words_for_publish
                SET contentJson = ?,
                    updatedAt = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (json.dumps(content_json, ensure_ascii=False), word_id))

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ 更新contentJson失败 (ID: {word_id}): {e}")
            return False

    def remove_word(self, word_id: int) -> bool:
        """删除单词"""
        try:
            cursor = self.connection.cursor()

            # 先删除word_processing_queue中的记录
            cursor.execute("""
                DELETE FROM word_processing_queue
                WHERE word = (SELECT word FROM words_for_publish WHERE id = ?)
                AND learningLanguage = (SELECT learningLanguage FROM words_for_publish WHERE id = ?)
                AND scaffoldingLanguage = (SELECT scaffoldingLanguage FROM words_for_publish WHERE id = ?)
            """, (word_id, word_id, word_id))

            # 再删除words_for_publish中的记录
            cursor.execute("DELETE FROM words_for_publish WHERE id = ?", (word_id,))

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ 删除单词失败 (ID: {word_id}): {e}")
            return False

    def update_kanban_status(self, word_id: int, reviewed: bool = True) -> bool:
        """更新看板状态"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                UPDATE word_processing_queue
                SET contentAiReviewed = ?,
                    updatedAt = CURRENT_TIMESTAMP
                WHERE word = (SELECT word FROM words_for_publish WHERE id = ?)
                AND learningLanguage = (SELECT learningLanguage FROM words_for_publish WHERE id = ?)
                AND scaffoldingLanguage = (SELECT scaffoldingLanguage FROM words_for_publish WHERE id = ?)
            """, (reviewed, word_id, word_id, word_id))

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"❌ 更新看板状态失败 (ID: {word_id}): {e}")
            return False


class OperationHandler:
    """操作处理器基类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager

    def execute(self, audit_result: AuditResult) -> Tuple[bool, str]:
        """执行操作（子类实现）"""
        raise NotImplementedError

    def create_backup_entry(self, audit_result: AuditResult) -> Optional[Dict]:
        """创建备份条目"""
        word_data = self.db_manager.get_word_data(audit_result.id)
        if word_data:
            return {
                'id': audit_result.id,
                'word': audit_result.word,
                'operation_type': audit_result.get_operation_type().value,
                'backup_data': word_data,
                'timestamp': datetime.now().isoformat()
            }
        return None


class UpdateOperation(OperationHandler):
    """更新操作处理器"""

    def execute(self, audit_result: AuditResult) -> Tuple[bool, str]:
        """执行更新操作"""
        try:
            # 更新审核字段
            if not self.db_manager.update_audit_fields(audit_result.id, audit_result):
                return False, f"更新审核字段失败 (ID: {audit_result.id})"

            # 更新看板状态
            if not self.db_manager.update_kanban_status(audit_result.id, True):
                return False, f"更新看板状态失败 (ID: {audit_result.id})"

            return True, f"成功更新单词 {audit_result.word} (ID: {audit_result.id})"

        except Exception as e:
            return False, f"更新操作异常 (ID: {audit_result.id}): {e}"


class RemoveOperation(OperationHandler):
    """删除操作处理器"""

    def execute(self, audit_result: AuditResult) -> Tuple[bool, str]:
        """执行删除操作"""
        try:
            # 删除单词
            if not self.db_manager.remove_word(audit_result.id):
                return False, f"删除单词失败 (ID: {audit_result.id})"

            return True, f"成功删除单词 {audit_result.word} (ID: {audit_result.id})"

        except Exception as e:
            return False, f"删除操作异常 (ID: {audit_result.id}): {e}"


class EditOperation(OperationHandler):
    """编辑操作处理器"""

    def execute(self, audit_result: AuditResult) -> Tuple[bool, str]:
        """执行编辑操作"""
        try:
            # 获取当前内容
            word_data = self.db_manager.get_word_data(audit_result.id)
            if not word_data:
                return False, f"获取单词数据失败 (ID: {audit_result.id})"

            content_json = word_data['contentJson']

            # 应用编辑指令
            for instruction in audit_result.aiAuditEditInstructions:
                if not self.apply_edit_instruction(content_json, instruction):
                    return False, f"应用编辑指令失败 (ID: {audit_result.id}, Path: {instruction.path})"

            # 验证内容结构
            if not self.validate_content_structure(content_json):
                return False, f"内容结构验证失败 (ID: {audit_result.id})"

            # 更新数据库
            if not self.db_manager.update_content_json(audit_result.id, content_json):
                return False, f"更新contentJson失败 (ID: {audit_result.id})"

            # 更新审核字段
            if not self.db_manager.update_audit_fields(audit_result.id, audit_result):
                return False, f"更新审核字段失败 (ID: {audit_result.id})"

            # 更新看板状态
            if not self.db_manager.update_kanban_status(audit_result.id, True):
                return False, f"更新看板状态失败 (ID: {audit_result.id})"

            return True, f"成功编辑单词 {audit_result.word} (ID: {audit_result.id})"

        except Exception as e:
            return False, f"编辑操作异常 (ID: {audit_result.id}): {e}"

    def apply_edit_instruction(self, content_json: Dict, instruction: EditInstruction) -> bool:
        """应用单个编辑指令"""
        try:
            # 解析路径
            path_parts = instruction.path.split('.')
            if path_parts[0] != 'contentJson':
                return False

            # 导航到目标位置
            current = content_json
            for part in path_parts[1:-1]:
                if part not in current:
                    current[part] = {}
                current = current[part]

            # 应用更新
            final_key = path_parts[-1]
            current[final_key] = instruction.newValue

            logger.info(f"✅ 应用编辑指令: {instruction.path} = {instruction.newValue}")
            return True

        except Exception as e:
            logger.error(f"❌ 应用编辑指令失败: {e}")
            return False

    def validate_content_structure(self, content_json: Dict) -> bool:
        """验证内容结构完整性"""
        try:
            # 检查必需的顶级字段
            required_fields = ['word', 'metadata', 'content']
            for field in required_fields:
                if field not in content_json:
                    logger.error(f"❌ 缺少必需字段: {field}")
                    return False

            # 检查content字段的子字段
            if 'content' in content_json:
                content = content_json['content']
                required_content_fields = [
                    'difficulty', 'coreDefinition', 'contextualExplanation',
                    'usageScenarios', 'collocations', 'usageNotes',
                    'synonyms', 'phoneticSymbols', 'usageExamples'
                ]

                for field in required_content_fields:
                    if field not in content:
                        logger.warning(f"⚠️ content字段缺少: {field}")

            return True

        except Exception as e:
            logger.error(f"❌ 内容结构验证失败: {e}")
            return False


class AuditResultsProcessor:
    """AI审核结果统一处理器"""

    def __init__(self, db_path: Path, results_file: Path, dry_run: bool = False,
                 backup_dir: Path = None, report_dir: Path = None):
        self.db_path = db_path
        self.results_file = results_file
        self.dry_run = dry_run
        self.backup_dir = backup_dir or Path(DEFAULT_BACKUP_DIR)
        self.report_dir = report_dir or Path(DEFAULT_REPORT_DIR)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 确保目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.report_dir.mkdir(parents=True, exist_ok=True)

        # 初始化组件
        self.db_manager = DatabaseManager(self.db_path)
        self.results_loader = ResultsLoader()

        logger.info(f"🚀 AI审核结果处理器初始化完成")
        logger.info(f"📁 数据库路径: {self.db_path}")
        logger.info(f"📄 结果文件: {self.results_file}")
        logger.info(f"🔧 运行模式: {'预览模式' if self.dry_run else '执行模式'}")

    def validate_inputs(self) -> bool:
        """验证输入文件和数据库"""
        try:
            # 检查数据库文件
            if not self.db_path.exists():
                logger.error(f"❌ 数据库文件不存在: {self.db_path}")
                return False

            # 检查结果文件
            if not self.results_file.exists():
                logger.error(f"❌ 审核结果文件不存在: {self.results_file}")
                return False

            # 连接数据库并检查表
            if not self.db_manager.connect():
                return False

            if not self.db_manager.check_required_tables():
                return False

            logger.info(f"✅ 输入验证通过")
            return True

        except Exception as e:
            logger.error(f"❌ 输入验证失败: {e}")
            return False

    def create_database_backup(self) -> Optional[str]:
        """创建数据库完整备份"""
        try:
            backup_filename = f"senseword_content_backup_{self.timestamp}.db"
            backup_path = self.backup_dir / backup_filename

            logger.info(f"💾 开始创建数据库备份...")
            shutil.copy2(self.db_path, backup_path)

            logger.info(f"✅ 数据库备份完成: {backup_path}")
            return str(backup_path)

        except Exception as e:
            logger.error(f"❌ 创建数据库备份失败: {e}")
            return None

    def load_and_parse_results(self) -> List[AuditResult]:
        """加载并解析审核结果"""
        try:
            # 加载JSONL文件
            raw_results = self.results_loader.load_jsonl_file(self.results_file)
            if not raw_results:
                return []

            # 解析审核结果
            audit_results = []
            for raw_result in raw_results:
                audit_result = self.results_loader.parse_audit_result(raw_result)
                if audit_result:
                    audit_results.append(audit_result)

            logger.info(f"📊 成功解析 {len(audit_results)} 条有效审核结果")
            return audit_results

        except Exception as e:
            logger.error(f"❌ 加载解析审核结果失败: {e}")
            return []

    def classify_operations(self, audit_results: List[AuditResult]) -> Dict[OperationType, List[AuditResult]]:
        """按操作类型分类审核结果"""
        classified = {
            OperationType.REMOVE: [],
            OperationType.EDIT: [],
            OperationType.UPDATE: []
        }

        for result in audit_results:
            operation_type = result.get_operation_type()
            classified[operation_type].append(result)

        # 输出分类统计
        logger.info(f"📊 操作分类统计:")
        logger.info(f"   - 删除操作: {len(classified[OperationType.REMOVE])} 个")
        logger.info(f"   - 编辑操作: {len(classified[OperationType.EDIT])} 个")
        logger.info(f"   - 更新操作: {len(classified[OperationType.UPDATE])} 个")

        return classified

    def process_operations(self, classified_operations: Dict[OperationType, List[AuditResult]]) -> ProcessingResult:
        """处理所有操作"""
        result = ProcessingResult()
        backup_entries = []

        try:
            # 按优先级处理：删除 > 编辑 > 更新
            operation_order = [OperationType.REMOVE, OperationType.EDIT, OperationType.UPDATE]

            for operation_type in operation_order:
                operations = classified_operations[operation_type]
                if not operations:
                    continue

                logger.info(f"🔄 开始处理 {operation_type.value} 操作 ({len(operations)} 个)")

                # 创建操作处理器
                if operation_type == OperationType.REMOVE:
                    handler = RemoveOperation(self.db_manager)
                elif operation_type == OperationType.EDIT:
                    handler = EditOperation(self.db_manager)
                else:  # UPDATE
                    handler = UpdateOperation(self.db_manager)

                # 处理每个操作
                for audit_result in operations:
                    result.total_processed += 1

                    # 创建备份条目
                    backup_entry = handler.create_backup_entry(audit_result)
                    if backup_entry:
                        backup_entries.append(backup_entry)

                    # 执行操作（预览模式下跳过实际执行）
                    if self.dry_run:
                        logger.info(f"🔍 [预览] {operation_type.value}: {audit_result.word} (ID: {audit_result.id})")
                        if operation_type == OperationType.REMOVE:
                            result.successful_removals += 1
                        elif operation_type == OperationType.EDIT:
                            result.successful_edits += 1
                        else:
                            result.successful_updates += 1
                    else:
                        success, message = handler.execute(audit_result)
                        if success:
                            logger.info(f"✅ {message}")
                            if operation_type == OperationType.REMOVE:
                                result.successful_removals += 1
                            elif operation_type == OperationType.EDIT:
                                result.successful_edits += 1
                            else:
                                result.successful_updates += 1
                        else:
                            logger.error(f"❌ {message}")
                            result.failed_operations += 1
                            result.errors.append(message)

            # 保存备份清单
            if backup_entries:
                self.save_backup_manifest(backup_entries)

            return result

        except Exception as e:
            logger.error(f"❌ 处理操作失败: {e}")
            result.errors.append(f"处理操作异常: {e}")
            return result

    def save_backup_manifest(self, backup_entries: List[Dict]) -> Optional[str]:
        """保存备份清单"""
        try:
            manifest_filename = f"backup_manifest_{self.timestamp}.json"
            manifest_path = self.backup_dir / manifest_filename

            manifest_data = {
                'timestamp': self.timestamp,
                'total_entries': len(backup_entries),
                'backup_entries': backup_entries
            }

            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest_data, f, ensure_ascii=False, indent=2)

            logger.info(f"💾 备份清单已保存: {manifest_path}")
            return str(manifest_path)

        except Exception as e:
            logger.error(f"❌ 保存备份清单失败: {e}")
            return None

    def generate_report(self, result: ProcessingResult) -> str:
        """生成处理报告"""
        try:
            report_filename = f"audit_processing_report_{self.timestamp}.md"
            report_path = self.report_dir / report_filename

            # 生成报告内容
            report_content = f"""# AI审核结果处理报告

## 📊 处理摘要

- **处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **运行模式**: {'预览模式' if self.dry_run else '执行模式'}
- **结果文件**: {self.results_file}
- **数据库**: {self.db_path}
- **备份文件**: {result.backup_file or '无'}

## 📈 处理统计

- **总处理数**: {result.total_processed}
- **成功更新**: {result.successful_updates}
- **成功编辑**: {result.successful_edits}
- **成功删除**: {result.successful_removals}
- **失败操作**: {result.failed_operations}
- **处理时长**: {result.processing_time:.2f} 秒

## 📋 操作详情

### ✅ 成功操作
- 更新操作: {result.successful_updates} 个
- 编辑操作: {result.successful_edits} 个
- 删除操作: {result.successful_removals} 个

### ❌ 失败操作
{chr(10).join(f'- {error}' for error in result.errors) if result.errors else '无失败操作'}

### ⚠️ 警告信息
{chr(10).join(f'- {warning}' for warning in result.warnings) if result.warnings else '无警告信息'}

## 🎯 处理结果

{'✅ 所有操作预览完成' if self.dry_run else '✅ 所有操作执行完成' if result.failed_operations == 0 else f'⚠️ 部分操作失败 ({result.failed_operations}/{result.total_processed})'}

---
*报告生成时间: {datetime.now().isoformat()}*
"""

            # 保存报告
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"📊 处理报告已生成: {report_path}")
            return str(report_path)

        except Exception as e:
            logger.error(f"❌ 生成报告失败: {e}")
            return ""

    def process_audit_results(self) -> ProcessingResult:
        """处理审核结果的主方法"""
        start_time = datetime.now()
        result = ProcessingResult()

        try:
            logger.info(f"🚀 开始处理AI审核结果")

            # 1. 验证输入
            if not self.validate_inputs():
                result.errors.append("输入验证失败")
                return result

            # 2. 创建数据库备份（非预览模式）
            if not self.dry_run:
                backup_file = self.create_database_backup()
                if not backup_file:
                    result.errors.append("数据库备份失败")
                    return result
                result.backup_file = backup_file

            # 3. 加载并解析审核结果
            audit_results = self.load_and_parse_results()
            if not audit_results:
                result.errors.append("没有有效的审核结果")
                return result

            # 4. 分类操作
            classified_operations = self.classify_operations(audit_results)

            # 5. 开始事务（非预览模式）
            if not self.dry_run:
                self.db_manager.begin_transaction()

            try:
                # 6. 处理操作
                result = self.process_operations(classified_operations)

                # 7. 提交事务（非预览模式）
                if not self.dry_run:
                    if result.failed_operations == 0:
                        self.db_manager.commit_transaction()
                        logger.info("✅ 所有操作成功，事务已提交")
                    else:
                        self.db_manager.rollback_transaction()
                        logger.error("❌ 存在失败操作，事务已回滚")
                        result.errors.append("事务回滚：存在失败操作")

            except Exception as e:
                if not self.dry_run:
                    self.db_manager.rollback_transaction()
                    logger.error(f"❌ 处理异常，事务已回滚: {e}")
                result.errors.append(f"处理异常: {e}")

            # 8. 计算处理时间
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()

            # 9. 生成报告
            report_path = self.generate_report(result)

            logger.info(f"🎉 处理完成，耗时 {result.processing_time:.2f} 秒")
            return result

        except Exception as e:
            logger.error(f"❌ 处理审核结果失败: {e}")
            result.errors.append(f"处理失败: {e}")
            return result

        finally:
            # 关闭数据库连接
            self.db_manager.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord AI审核结果统一处理器 v4.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法：处理审核结果
  python 02_process_audit_results.py --results audit_results.jsonl

  # 预览模式：查看将要执行的操作
  python 02_process_audit_results.py --results audit_results.jsonl --dry-run

  # 指定数据库路径
  python 02_process_audit_results.py --results audit_results.jsonl --db-path ./senseword_content_v4.db

  # 自定义备份和报告目录
  python 02_process_audit_results.py --results audit_results.jsonl --backup-dir ./backups --report-dir ./reports

注意事项:
  - 处理前会自动创建完整的数据库备份
  - 所有操作在单一事务中执行，确保数据一致性
  - 失败时会自动回滚，不会造成数据损坏
  - 建议先使用 --dry-run 预览操作结果
        """
    )

    parser.add_argument("--results", required=True,
                       help="AI审核结果JSONL文件路径")
    parser.add_argument("--db-path", default=DEFAULT_DB_PATH,
                       help=f"数据库文件路径 (默认: {DEFAULT_DB_PATH})")
    parser.add_argument("--dry-run", action="store_true",
                       help="预览模式，不实际修改数据库")
    parser.add_argument("--backup-dir", default=DEFAULT_BACKUP_DIR,
                       help=f"备份目录路径 (默认: {DEFAULT_BACKUP_DIR})")
    parser.add_argument("--report-dir", default=DEFAULT_REPORT_DIR,
                       help=f"报告输出目录 (默认: {DEFAULT_REPORT_DIR})")

    args = parser.parse_args()

    try:
        # 解析路径
        current_dir = Path(__file__).parent
        results_file = Path(args.results)
        db_path = Path(args.db_path)
        backup_dir = Path(args.backup_dir)
        report_dir = Path(args.report_dir)

        # 转换为绝对路径
        if not results_file.is_absolute():
            results_file = current_dir / results_file
        if not db_path.is_absolute():
            db_path = current_dir / db_path
        if not backup_dir.is_absolute():
            backup_dir = current_dir / backup_dir
        if not report_dir.is_absolute():
            report_dir = current_dir / report_dir

        # 创建处理器
        processor = AuditResultsProcessor(
            db_path=db_path,
            results_file=results_file,
            dry_run=args.dry_run,
            backup_dir=backup_dir,
            report_dir=report_dir
        )

        # 处理审核结果
        result = processor.process_audit_results()

        # 输出结果摘要
        if result.total_processed > 0:
            print(f"\n🎉 {'预览' if args.dry_run else '处理'}完成!")
            print(f"📊 处理摘要:")
            print(f"   - 总处理数: {result.total_processed}")
            print(f"   - 成功更新: {result.successful_updates}")
            print(f"   - 成功编辑: {result.successful_edits}")
            print(f"   - 成功删除: {result.successful_removals}")
            print(f"   - 失败操作: {result.failed_operations}")
            print(f"   - 处理时长: {result.processing_time:.2f} 秒")

            if result.backup_file:
                print(f"   - 备份文件: {result.backup_file}")

            if args.dry_run:
                print(f"\n📝 这是预览模式，没有实际修改数据库")
                print(f"   如需执行操作，请移除 --dry-run 参数")

            if result.failed_operations == 0:
                print(f"\n✅ 所有操作{'预览' if args.dry_run else '执行'}成功!")
                return 0
            else:
                print(f"\n⚠️ 存在 {result.failed_operations} 个失败操作")
                print(f"   详细错误信息请查看日志和报告")
                return 1
        else:
            print(f"❌ 没有找到有效的审核结果进行处理")
            return 1

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
