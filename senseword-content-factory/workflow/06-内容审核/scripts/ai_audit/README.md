# SenseWord AI内容审核工作流程 v4.0

## 📋 概述

基于v4数据库架构和看板式状态管理的完整AI内容审核工作流程，采用现代化的统一脚本架构，提供安全、高效的审核结果处理能力。

## 🏗️ 架构特性

### v4.0 核心升级
- **看板式状态管理**: 基于word_processing_queue表的精细化流程控制
- **v4数据库架构**: 完全兼容最新的数据库结构
- **统一脚本架构**: 整合多个功能到单一脚本，提升安全性和易用性
- **原子事务管理**: 确保数据一致性，支持自动回滚
- **智能操作分类**: 自动识别删除、编辑、更新操作

### 数据库表依赖
- `words_for_publish`: 主要内容表，包含单词内容和审核结果
- `word_processing_queue`: 看板式状态管理表，跟踪处理进度
- `tts_assets`: TTS资产管理表（间接依赖）

## 🔄 简化的工作流程

### 阶段1: 批处理任务生成
```
01_generate_v4_audit_batch.py → 生成JSONL批处理文件
```

### 阶段2: AI批处理执行
```
Google Cloud Platform批处理系统 → AI审核执行 → 结果下载
```

### 阶段3: 统一结果处理（新架构）
```
02_process_audit_results.py → 统一处理所有审核结果
├── 自动分类操作类型
├── 原子事务处理
├── 数据库备份
├── 看板状态更新
└── 详细报告生成
```

**流程简化优势：**
- 🔄 **3个阶段** 替代原来的5个阶段
- 🚀 **3个核心脚本** 替代原来的8个脚本
- 🔒 **统一事务** 确保数据安全
- 📊 **智能分析** 提供深度质量洞察
- 📈 **一键处理** 简化操作流程

## 📁 核心脚本说明

### 🚀 01_generate_v4_audit_batch.py - AI审核批处理生成器
**功能**: 基于看板式状态管理生成AI审核批处理任务

**使用方法**:
```bash
# 生成未审核单词的批处理任务（推荐）
python3 01_generate_v4_audit_batch.py

# 生成需要重新生成的单词批处理任务
python3 01_generate_v4_audit_batch.py --mode regenerate

# 指定数据库路径和限制数量
python3 01_generate_v4_audit_batch.py --db-path ./senseword_content_v4.db --limit 500
```

**核心特性**:
- ✅ 基于看板式状态管理的智能筛选
- ✅ 自动检测数据库表完整性
- ✅ 详细的统计信息展示
- ✅ 支持相对路径和绝对路径

---

### 🚀 02_process_audit_results.py - 统一审核结果处理器 (推荐使用)
**功能**: 统一的AI审核结果处理器，整合了原有多个脚本的功能

**使用方法**:
```bash
# 基本用法：处理审核结果
python3 02_process_audit_results.py --results audit_results.jsonl

# 预览模式：查看将要执行的操作（推荐先预览）
python3 02_process_audit_results.py --results audit_results.jsonl --dry-run

# 指定数据库路径
python3 02_process_audit_results.py --results audit_results.jsonl --db-path ./senseword_content_v4.db
```

**核心特性**:
- ✅ **智能操作分类**: 自动识别删除、编辑、更新操作
- ✅ **原子事务**: 全成功或全失败，确保数据一致性
- ✅ **完整备份**: 处理前自动备份整个数据库
- ✅ **安全预览**: 内置dry-run模式查看操作效果
- ✅ **看板集成**: 自动更新contentAiReviewed状态
- ✅ **详细报告**: 生成Markdown格式的处理报告

**安全保障**:
- 🔒 **数据库备份**: 处理前自动创建完整备份
- 🔒 **事务管理**: 单一事务确保原子性操作
- 🔒 **回滚机制**: 失败时自动回滚所有更改
- 🔒 **预览模式**: 支持dry-run模式安全预览

---

### 📊 03_extract_words.py - AI审核单词数据提取器
**功能**: 提取和分析AI审核过的单词数据，生成详细的质量分析报告
- 智能评分阈值处理：支持提取指定分数范围或所有已审核单词
- 多维度数据分析：评分分布、频率分布、词性分布、问题类型分析
- coreDefinition提取：从contentJson中提取核心定义便于分析
- 详细报告生成：CSV数据文件 + Markdown分析报告

**使用方法**:
```bash
# 提取0-6分的低质量单词（推荐）
python3 03_extract_words.py --max-score 6

# 提取所有已审核单词进行全面分析
python3 03_extract_words.py --max-score 10

# 指定输出目录
python3 03_extract_words.py --max-score 6 --output-dir ./reports
```

**参数说明**:
- `--max-score`: 最大评分阈值（默认：6分）
  - 设为6：提取0-6分的低质量单词
  - 设为10或更高：提取所有已审核单词
- `--output-dir`: 输出目录路径（默认：./reports）

**核心特性**:
- ✅ **智能阈值处理**: max_score>=10时提取所有已审核单词
- ✅ **安全JSON解析**: 完整的异常处理和默认值机制
- ✅ **多维度分析**: 5个维度的详细统计分析
- ✅ **问题类型检测**: 8种常见问题类型的自动识别
- ✅ **质量特征分析**: 按分数段的质量特征和样本展示
- ✅ **v4数据库兼容**: 与最新数据库架构完全兼容

**输出文件**:
- `all_audited_words_{timestamp}.csv` - 包含coreDefinition的完整数据
- `audited_words_analysis_report_{timestamp}.md` - 详细分析报告

**使用场景**:
- 🔍 **质量评估**: 了解整体内容质量分布和问题类型
- 🗑️ **数据清理**: 识别需要移除的低质量单词
- 📈 **改进分析**: 为内容重新生成提供数据支持
- 📊 **趋势跟踪**: 跟踪质量改进效果

## 🔄 完整工作流程示例

### 标准AI审核流程
```bash
# 步骤1: 生成AI审核批处理任务
python3 01_generate_v4_audit_batch.py --mode unaudited --limit 1000

# 步骤2: 提交到Google Cloud Platform批处理系统
# (手动操作：上传JSONL文件到Vertex AI批处理服务)

# 步骤3: 下载审核结果并统一处理
# 先预览操作效果
python3 02_process_audit_results.py --results audit_results.jsonl --dry-run

# 确认无误后执行处理
python3 02_process_audit_results.py --results audit_results.jsonl
```

### 质量分析和数据清理流程
```bash
# 步骤1: 提取低质量单词进行分析
python3 03_extract_words.py --max-score 6

# 步骤2: 查看分析报告，了解质量问题分布
# 查看生成的 audited_words_analysis_report_{timestamp}.md

# 步骤3: 基于分析结果，生成重新审核的批处理任务
python3 01_generate_v4_audit_batch.py --mode regenerate

# 步骤4: 处理重新审核的结果
python3 02_process_audit_results.py --results regenerate_audit_results.jsonl
```

### 全面质量评估流程
```bash
# 步骤1: 提取所有已审核单词进行全面分析
python3 03_extract_words.py --max-score 10

# 步骤2: 分析整体质量分布和趋势
# 查看生成的详细分析报告

# 步骤3: 根据分析结果制定改进策略
# 基于报告中的问题类型分布和质量特征
```

## 📊 架构优势对比

### 新架构 vs 旧架构

| 功能特性 | 旧架构（多个脚本） | 新架构（统一脚本） |
|---------|------------------|-------------------|
| 脚本数量 | 8个独立脚本 | 3个核心脚本 |
| 工作流阶段 | 5个复杂阶段 | 3个简化阶段 |
| 数据库连接 | 多次独立连接 | 单次连接复用 |
| 事务管理 | 无统一事务 | 完整原子事务 |
| 备份策略 | 分散的备份 | 统一完整备份 |
| 错误处理 | 各自处理 | 统一错误处理 |
| 预览功能 | 部分支持 | 完整预览模式 |
| 状态更新 | 手动更新 | 自动更新看板状态 |
| 数据分析 | 基础统计 | 多维度深度分析 |
| 用户体验 | 复杂操作 | 一键处理 |
| 数据安全 | 基础保障 | 多重安全机制 |

## 🎯 使用建议

### 新用户推荐流程
1. **首次使用**: 先用`--dry-run`预览模式熟悉操作
2. **小批量测试**: 使用`--limit`参数限制处理数量
3. **逐步扩大**: 确认流程无误后处理更大批量
4. **定期备份**: 利用自动备份功能保障数据安全

### 最佳实践
- ✅ **总是先预览**: 使用`--dry-run`查看操作效果
- ✅ **保留备份**: 自动生成的备份文件请妥善保存
- ✅ **查看报告**: 处理完成后检查详细报告
- ✅ **分批处理**: 大量数据建议分批处理，降低风险

## 🚨 已移除的旧脚本

以下脚本已被统一的`02_process_audit_results.py`替代，不再维护：
- ❌ `03_update_audit_results_to_db.py` - 功能已整合到统一脚本
- ❌ `06_remove_problematic_words.py` - 功能已整合到统一脚本
- ❌ `08_apply_edit_instructions.py` - 功能已整合到统一脚本

**当前活跃脚本**:
- ✅ `01_generate_v4_audit_batch.py` - AI审核批处理生成器
- ✅ `02_process_audit_results.py` - 统一审核结果处理器
- ✅ `03_extract_words.py` - AI审核单词数据提取器

**迁移建议**: 请使用新的统一脚本架构，它提供了更安全、更高效的处理能力和深度数据分析功能。

## 📞 技术支持

### 可视化文档
详细的工作原理和技术实现请参考：
- `../mermaid/01_整体工作流程图.md` - 完整流程图
- `../mermaid/02_process_audit_results_详细工作原理.md` - 统一脚本详细工作原理
- `../mermaid/03_extract_words_详细工作原理.md` - 数据提取器详细工作原理
- `../mermaid/03_extract_words_v4兼容性分析.md` - v4数据库兼容性分析
- `../mermaid/03_数据结构转化图.md` - 数据转换过程
- `../mermaid/04_系统架构图.md` - 系统架构设计
- `../mermaid/05_看板状态管理图.md` - 看板状态管理

### 故障排除
1. **数据库连接问题**: 检查数据库文件路径和权限
2. **JSONL格式错误**: 验证审核结果文件格式
3. **事务回滚**: 查看详细日志了解失败原因
4. **备份恢复**: 使用自动生成的备份文件恢复数据

### 最佳实践
- ✅ 总是先使用`--dry-run`预览操作
- ✅ 定期检查自动生成的备份文件
- ✅ 查看处理报告了解详细结果
- ✅ 大批量数据建议分批处理

---
*最后更新: 2025-07-13*
*版本: v4.0 - 统一审核结果处理器架构*
