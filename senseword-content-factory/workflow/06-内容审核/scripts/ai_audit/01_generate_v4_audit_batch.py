#!/usr/bin/env python3
"""
SenseWord AI内容审核批处理生成器 v4.0
基于v4数据库架构和看板式状态管理的AI审核批处理任务生成器

功能：
- 使用最新的SenseWord AI内容审核官v4提示词
- 基于word_processing_queue表的看板式状态管理
- 生成包含编辑指令的AI审核批处理JSONL文件
- 支持Vertex AI批处理API格式
- 智能筛选已生成内容但未审核的单词

使用方法：
    python3 01_generate_v4_audit_batch.py [--db-path PATH] [--mode MODE] [--limit N] [--output-dir PATH]

参数：
    --db-path: 数据库文件路径（默认：当前目录下的senseword_content_v4.db）
    --mode: 提取模式
        - unaudited: 已生成内容但未AI审核的单词 (contentGenerated=TRUE, contentAiReviewed=FALSE)
        - regenerate: 需要重新生成的单词 (aiAuditShouldRegenerate = 1)
        - all: 所有已生成内容的单词
    --limit: 限制处理的单词数量（默认：1000）
    --output-dir: 输出目录路径（默认：./batch_output）

作者：AI Assistant
日期：2025-07-13
版本：v4.0 - 基于v4数据库架构和看板式状态管理
"""

import json
import sqlite3
import logging
import argparse
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 默认配置
DEFAULT_DB_PATH = "./senseword_content_v4.db"
DEFAULT_PROMPT_PATH = "../../Prompts/英文生产/03_senseword-内容审核-v4-英语版本.md"
DEFAULT_OUTPUT_DIR = "./batch_output"
DEFAULT_LIMIT = 1000

class ContentAuditBatchProcessor:
    """SenseWord AI内容审核批处理生成器 v4.0"""

    def __init__(self, db_path: str, prompt_path: str, mode: str = 'unaudited',
                 limit: int = DEFAULT_LIMIT, output_dir: str = DEFAULT_OUTPUT_DIR):
        """
        初始化批处理生成器

        参数:
            db_path: 数据库文件路径
            prompt_path: 提示词文件路径
            mode: 提取模式 (unaudited|regenerate|all)
            limit: 限制处理的单词数量
            output_dir: 输出目录路径
        """
        self.db_path = Path(db_path)
        self.prompt_path = Path(prompt_path)
        self.mode = mode
        self.limit = limit
        self.output_dir = Path(output_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 验证文件存在性
        self._validate_files()

        logger.info(f"🚀 AI内容审核批处理生成器初始化完成")
        logger.info(f"📊 提取模式: {mode}, 限制数量: {limit}")
        logger.info(f"📁 输出目录: {self.output_dir}")

    def _validate_files(self):
        """验证必要文件的存在性"""
        if not self.db_path.exists():
            logger.error(f"❌ 数据库文件不存在: {self.db_path}")
            sys.exit(1)

        if not self.prompt_path.exists():
            logger.error(f"❌ 提示词文件不存在: {self.prompt_path}")
            sys.exit(1)

        logger.info(f"✅ 文件验证通过")

    def load_prompt_template(self) -> str:
        """
        加载v4提示词模板

        返回:
            str: 提示词内容
        """
        try:
            logger.info(f"📖 加载提示词文件: {self.prompt_path}")

            with open(self.prompt_path, 'r', encoding='utf-8') as f:
                prompt_content = f.read()

            logger.info(f"✅ 提示词加载成功，长度: {len(prompt_content)} 字符")
            return prompt_content

        except Exception as e:
            logger.error(f"❌ 加载提示词失败: {e}")
            sys.exit(1)
    
    def check_required_tables(self) -> bool:
        """检查必需的数据库表是否存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 检查必需的表
                required_tables = ['words_for_publish', 'word_processing_queue']
                for table in required_tables:
                    cursor.execute("""
                        SELECT name FROM sqlite_master
                        WHERE type='table' AND name=?
                    """, (table,))

                    if not cursor.fetchone():
                        logger.error(f"❌ 缺少必需的表: {table}")
                        return False

                logger.info(f"✅ 所有必需的表都存在")
                return True

        except Exception as e:
            logger.error(f"❌ 检查数据库表失败: {e}")
            return False

    def get_audit_statistics(self) -> Dict[str, int]:
        """获取审核统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取基础统计
                cursor.execute("""
                    SELECT
                        COUNT(*) as total_words,
                        COUNT(CASE WHEN q.contentGenerated = 1 THEN 1 END) as content_generated,
                        COUNT(CASE WHEN q.contentAiReviewed = 1 THEN 1 END) as content_reviewed,
                        COUNT(CASE WHEN w.aiAuditScore IS NOT NULL THEN 1 END) as audited_words,
                        COUNT(CASE WHEN w.aiAuditShouldRegenerate = 1 THEN 1 END) as need_regenerate
                    FROM word_processing_queue q
                    LEFT JOIN words_for_publish w ON q.word = w.word
                        AND q.learningLanguage = w.learningLanguage
                        AND q.scaffoldingLanguage = w.scaffoldingLanguage
                """)

                result = cursor.fetchone()
                return {
                    'total_words': result[0],
                    'content_generated': result[1],
                    'content_reviewed': result[2],
                    'audited_words': result[3],
                    'need_regenerate': result[4]
                }

        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {e}")
            return {}

    def get_words_for_audit(self) -> List[Dict]:
        """
        基于看板式状态管理获取需要审核的单词

        返回:
            list: 单词列表
        """
        try:
            mode_desc = {
                'unaudited': '已生成内容但未AI审核的单词',
                'regenerate': '需要重新生成的单词',
                'all': '所有已生成内容的单词'
            }

            logger.info(f"📊 获取{mode_desc.get(self.mode, '单词')} (模式: {self.mode})")

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 根据模式构建不同的查询
                if self.mode == 'unaudited':
                    # 获取已生成内容但未AI审核的单词
                    query = """
                    SELECT w.id, w.word, w.contentJson, w.learningLanguage, w.scaffoldingLanguage
                    FROM words_for_publish w
                    INNER JOIN word_processing_queue q ON w.word = q.word
                        AND w.learningLanguage = q.learningLanguage
                        AND w.scaffoldingLanguage = q.scaffoldingLanguage
                    WHERE q.contentGenerated = 1
                        AND q.contentAiReviewed = 0
                        AND w.contentJson IS NOT NULL
                        AND w.contentJson != ''
                    ORDER BY w.id
                    LIMIT ?
                    """
                elif self.mode == 'regenerate':
                    # 获取需要重新生成的单词
                    query = """
                    SELECT w.id, w.word, w.contentJson, w.learningLanguage, w.scaffoldingLanguage
                    FROM words_for_publish w
                    WHERE w.aiAuditShouldRegenerate = 1
                        AND w.contentJson IS NOT NULL
                        AND w.contentJson != ''
                    ORDER BY w.aiAuditScore ASC, w.id
                    LIMIT ?
                    """
                elif self.mode == 'all':
                    # 获取所有已生成内容的单词
                    query = """
                    SELECT w.id, w.word, w.contentJson, w.learningLanguage, w.scaffoldingLanguage
                    FROM words_for_publish w
                    INNER JOIN word_processing_queue q ON w.word = q.word
                        AND w.learningLanguage = q.learningLanguage
                        AND w.scaffoldingLanguage = q.scaffoldingLanguage
                    WHERE q.contentGenerated = 1
                        AND w.contentJson IS NOT NULL
                        AND w.contentJson != ''
                    ORDER BY w.id
                    LIMIT ?
                    """
                else:
                    logger.error(f"❌ 不支持的模式: {self.mode}")
                    return []

                cursor.execute(query, (self.limit,))
                results = cursor.fetchall()

                words = []
                for row in results:
                    try:
                        word_data = {
                            'id': row[0],
                            'word': row[1],
                            'contentJson': json.loads(row[2]) if row[2] else {},
                            'learningLanguage': row[3],
                            'scaffoldingLanguage': row[4]
                        }
                        words.append(word_data)
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ 跳过JSON解析失败的单词 ID {row[0]}: {e}")
                        continue

                logger.info(f"📊 获取到 {len(words)} 个{mode_desc.get(self.mode, '单词')}")
                return words

        except Exception as e:
            logger.error(f"❌ 获取单词失败: {e}")
            return []

    def create_batch_request(self, word_data: Dict, prompt_content: str) -> Dict:
        """
        创建单个Vertex AI批处理请求

        参数:
            word_data: 单词数据
            prompt_content: 提示词内容

        返回:
            dict: Vertex AI批处理请求格式

        期望输出格式 (v4.0):
        {
            "id": 1111,
            "word": "example",
            "aiAuditScore": 8,
            "aiAuditShouldRegenerate": 0,
            "aiAuditShouldRemove": 0,
            "aiAuditComment": "审核评论",
            "aiAuditEditInstructions": [...]
        }
        """
        try:
            # 构建输入数据 - 只包含id, word, contentJson三个字段
            input_data = {
                "id": word_data["id"],
                "word": word_data["word"],
                "contentJson": word_data["contentJson"]
            }

            # 构建用户消息内容
            user_message = f"{prompt_content}\n\n请审核以下单词内容：\n{json.dumps(input_data, ensure_ascii=False, indent=2)}"

            # 构建 Vertex AI 批处理请求格式
            request = {
                "request": {
                    "contents": [
                        {
                            "role": "user",
                            "parts": [
                                {
                                    "text": user_message
                                }
                            ]
                        }
                    ],
                    "generationConfig": {
                        "temperature": 0.3,  # 审核任务使用较低温度确保一致性
                        "maxOutputTokens": 20000,  # 确保完整审核结果和编辑指令
                        "topK": 40,
                        "topP": 0.95,
                        "responseMimeType": "application/json"
                    }
                }
            }

            return request

        except Exception as e:
            logger.error(f"❌ 创建批处理请求失败: {e}")
            return None

    def generate_batch_file(self) -> Dict:
        """
        生成AI审核批处理文件

        返回:
            dict: 生成结果
        """
        try:
            logger.info("🚀 开始生成AI审核批处理文件")

            # 检查数据库表
            if not self.check_required_tables():
                return {'success': False, 'error': '数据库表检查失败'}

            # 显示统计信息
            stats = self.get_audit_statistics()
            if stats:
                logger.info(f"📊 数据库统计信息:")
                logger.info(f"   - 总单词数: {stats.get('total_words', 0):,}")
                logger.info(f"   - 已生成内容: {stats.get('content_generated', 0):,}")
                logger.info(f"   - 已AI审核: {stats.get('content_reviewed', 0):,}")
                logger.info(f"   - 需要重新生成: {stats.get('need_regenerate', 0):,}")

            # 加载提示词
            prompt_content = self.load_prompt_template()

            # 获取需要审核的单词
            words = self.get_words_for_audit()
            if not words:
                return {'success': False, 'error': f'没有找到符合条件的单词 (模式: {self.mode})'}

            # 生成批处理文件
            batch_filename = f"content_audit_batch_{self.mode}_{self.timestamp}.jsonl"
            batch_file = self.output_dir / batch_filename

            successful_requests = 0
            with open(batch_file, 'w', encoding='utf-8') as f:
                for word_data in words:
                    request = self.create_batch_request(word_data, prompt_content)
                    if request:
                        f.write(json.dumps(request, ensure_ascii=False) + '\n')
                        successful_requests += 1

            logger.info(f"✅ 批处理文件已生成: {batch_file}")
            logger.info(f"📊 成功生成 {successful_requests} 个请求")

            return {
                'success': True,
                'batch_file': str(batch_file),
                'word_count': len(words),
                'successful_requests': successful_requests,
                'mode': self.mode,
                'prompt_version': 'v4.0'
            }

        except Exception as e:
            logger.error(f"❌ 生成批处理文件失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="SenseWord AI内容审核批处理生成器 v4.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 生成未审核单词的批处理任务（默认）
  python 01_generate_v4_audit_batch.py

  # 生成需要重新生成的单词批处理任务
  python 01_generate_v4_audit_batch.py --mode regenerate

  # 指定数据库路径和输出目录
  python 01_generate_v4_audit_batch.py --db-path ./senseword_content_v4.db --output-dir ./output

  # 限制处理数量
  python 01_generate_v4_audit_batch.py --limit 500
        """
    )

    parser.add_argument("--db-path", default=DEFAULT_DB_PATH,
                       help=f"数据库文件路径 (默认: {DEFAULT_DB_PATH})")
    parser.add_argument("--mode", choices=['unaudited', 'regenerate', 'all'], default='unaudited',
                       help="提取模式: unaudited(未审核), regenerate(需要重新生成), all(所有)")
    parser.add_argument("--limit", type=int, default=DEFAULT_LIMIT,
                       help=f"限制处理的单词数量 (默认: {DEFAULT_LIMIT})")
    parser.add_argument("--output-dir", default=DEFAULT_OUTPUT_DIR,
                       help=f"输出目录路径 (默认: {DEFAULT_OUTPUT_DIR})")
    parser.add_argument("--prompt-path", default=DEFAULT_PROMPT_PATH,
                       help=f"提示词文件路径 (默认: {DEFAULT_PROMPT_PATH})")

    args = parser.parse_args()

    try:
        # 解析相对路径
        current_dir = Path(__file__).parent
        db_path = current_dir / args.db_path if not Path(args.db_path).is_absolute() else args.db_path
        prompt_path = current_dir / args.prompt_path if not Path(args.prompt_path).is_absolute() else args.prompt_path
        output_dir = current_dir / args.output_dir if not Path(args.output_dir).is_absolute() else args.output_dir

        processor = ContentAuditBatchProcessor(
            db_path=str(db_path),
            prompt_path=str(prompt_path),
            mode=args.mode,
            limit=args.limit,
            output_dir=str(output_dir)
        )

        result = processor.generate_batch_file()

        if result['success']:
            print(f"\n🎉 批处理文件生成成功!")
            print(f"📊 生成摘要:")
            print(f"   - 提示词版本: {result['prompt_version']}")
            print(f"   - 提取模式: {result['mode']}")
            print(f"   - 处理单词数: {result['word_count']:,}")
            print(f"   - 成功请求数: {result['successful_requests']:,}")
            print(f"   - 批处理文件: {result['batch_file']}")
            print(f"\n📝 下一步:")
            print(f"   1. 使用Google Cloud Platform批处理系统提交任务")
            print(f"   2. 等待AI审核完成")
            print(f"   3. 使用03_update_audit_results_to_db.py更新结果到数据库")
            return 0
        else:
            print(f"❌ 生成失败: {result.get('error', '未知错误')}")
            return 1

    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
