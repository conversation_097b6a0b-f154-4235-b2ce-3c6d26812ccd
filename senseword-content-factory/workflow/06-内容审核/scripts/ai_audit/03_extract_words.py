#!/usr/bin/env python3
"""
AI审核单词数据提取脚本

功能：
- 从数据库中提取所有AI审核过的单词数据
- 包含id、word、coreDefinition和完整的AI审核结果
- 按评分分组分析，支持质量评估和改进决策
- 生成详细的审核单词分析报告

使用方法：
    python3 05_extract_words_to_regenerate.py [--max-score 10]

参数：
    --max-score: 最大评分阈值，默认为6分，设为10可提取所有审核单词

作者：AI Assistant
日期：2025-07-09
版本：v3.0 - AI审核单词分析版
"""

import json
import csv
import sqlite3
import logging
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 文件路径配置
DB_PATH = "/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content.db"
REPORTS_DIR = "/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/workflows/04-创建 AI 自动审核批处理任务/reports"

class AuditedWordsExtractor:
    """AI审核单词提取器"""

    def __init__(self, max_score=6):
        """
        初始化提取器

        参数:
            max_score: 最大评分阈值
        """
        self.db_path = Path(DB_PATH)
        self.reports_dir = Path(REPORTS_DIR)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.max_score = max_score

        # 确保报告目录存在
        self.reports_dir.mkdir(parents=True, exist_ok=True)

        logger.info("INFO - [LowScoreWordsExtractor][__init__001] 低分单词提取器初始化完成")
        logger.info(f"INFO - [LowScoreWordsExtractor][__init__002] 数据库路径: {self.db_path}")
        logger.info(f"INFO - [LowScoreWordsExtractor][__init__003] 报告目录: {self.reports_dir}")
        logger.info(f"INFO - [LowScoreWordsExtractor][__init__004] 评分阈值: 0-{self.max_score}分")
    
    def validate_database(self):
        """
        验证数据库是否存在
        
        返回:
            bool: 验证是否成功
        """
        try:
            logger.info("INFO - [LowScoreWordsExtractor][validate_database_001] 验证数据库")

            if not self.db_path.exists():
                logger.error(f"ERROR - [LowScoreWordsExtractor][validate_database_002] 数据库文件不存在: {self.db_path}")
                return False

            logger.info("INFO - [LowScoreWordsExtractor][validate_database_003] ✅ 数据库验证通过")
            return True

        except Exception as e:
            logger.error(f"ERROR - [LowScoreWordsExtractor][validate_database_004] 数据库验证失败: {e}")
            return False
    
    def extract_low_score_words(self):
        """
        从数据库中提取低分单词（0-max_score分）

        返回:
            list: 低分单词列表
        """
        try:
            logger.info(f"INFO - [LowScoreWordsExtractor][extract_low_score_words_001] 开始提取0-{self.max_score}分的单词")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询所有有评分的单词（包含contentJson字段以提取coreDefinition）
                if self.max_score >= 10:
                    # 如果max_score >= 10，提取所有有评分的单词
                    query = """
                    SELECT
                        id,
                        word,
                        contentJson,
                        aiAuditScore,
                        aiAuditShouldRegenerate,
                        aiAuditShouldRemove,
                        aiAuditComment,
                        frequency,
                        partsOfSpeech,
                        learningLanguage,
                        scaffoldingLanguage,
                        publishStatus,
                        contentVersion,
                        updatedAt
                    FROM words_for_publish
                    WHERE aiAuditScore IS NOT NULL
                    ORDER BY aiAuditScore ASC, id
                    """
                    cursor.execute(query)
                else:
                    # 否则按指定分数范围提取
                    query = """
                    SELECT
                        id,
                        word,
                        contentJson,
                        aiAuditScore,
                        aiAuditShouldRegenerate,
                        aiAuditShouldRemove,
                        aiAuditComment,
                        frequency,
                        partsOfSpeech,
                        learningLanguage,
                        scaffoldingLanguage,
                        publishStatus,
                        contentVersion,
                        updatedAt
                    FROM words_for_publish
                    WHERE aiAuditScore IS NOT NULL AND aiAuditScore <= ?
                    ORDER BY aiAuditScore ASC, id
                    """
                    cursor.execute(query, (self.max_score,))
                
                results = cursor.fetchall()

                # 转换为字典列表
                low_score_words = []
                for row in results:
                    # 提取coreDefinition
                    core_definition = ""
                    try:
                        if row[2]:  # contentJson字段
                            content_json = json.loads(row[2])
                            if 'content' in content_json and 'coreDefinition' in content_json['content']:
                                core_definition = content_json['content']['coreDefinition']
                    except (json.JSONDecodeError, KeyError, TypeError) as e:
                        logger.warning(f"WARN - [LowScoreWordsExtractor][extract_low_score_words] 解析contentJson失败 (ID: {row[0]}): {e}")
                        core_definition = "解析失败"

                    word_data = {
                        'id': row[0],
                        'word': row[1],
                        'coreDefinition': core_definition,
                        'aiAuditScore': row[3],
                        'aiAuditShouldRegenerate': row[4],
                        'aiAuditShouldRemove': row[5],
                        'aiAuditComment': row[6],
                        'frequency': row[7],
                        'partsOfSpeech': row[8],
                        'learningLanguage': row[9],
                        'scaffoldingLanguage': row[10],
                        'publishStatus': row[11],
                        'contentVersion': row[12],
                        'updatedAt': row[13]
                    }
                    low_score_words.append(word_data)

                logger.info(f"INFO - [LowScoreWordsExtractor][extract_low_score_words_002] ✅ 成功提取 {len(low_score_words)} 个低分单词")
                return low_score_words

        except Exception as e:
            logger.error(f"ERROR - [LowScoreWordsExtractor][extract_low_score_words_003] 提取失败: {e}")
            return []
    
    def analyze_low_score_words(self, low_score_words):
        """
        分析低分单词的分布和问题

        参数:
            low_score_words: 低分单词列表

        返回:
            dict: 分析结果
        """
        try:
            logger.info("INFO - [LowScoreWordsExtractor][analyze_low_score_words_001] 分析低分单词分布")
            
            analysis = {
                'total_count': len(low_score_words),
                'score_distribution': {},
                'frequency_distribution': {},
                'parts_of_speech_distribution': {},
                'common_issues': {},
                'word_length_distribution': {},
                'content_version_distribution': {},
                'removal_candidates': {},  # 按分数统计可能需要移除的单词
                'score_quality_analysis': {}  # 每个分数段的质量分析
            }

            for word_data in low_score_words:
                # 评分分布
                score = word_data.get('aiAuditScore', 0)
                score_range = f"{int(score)}分" if score > 0 else "0分"
                analysis['score_distribution'][score_range] = analysis['score_distribution'].get(score_range, 0) + 1
                
                # 频率分布
                freq = word_data.get('frequency', 'Unknown')
                analysis['frequency_distribution'][freq] = analysis['frequency_distribution'].get(freq, 0) + 1
                
                # 词性分布
                pos = word_data.get('partsOfSpeech', 'Unknown')
                analysis['parts_of_speech_distribution'][pos] = analysis['parts_of_speech_distribution'].get(pos, 0) + 1
                
                # 单词长度分布
                word_len = len(word_data.get('word', ''))
                len_range = f"{word_len}字符" if word_len <= 10 else "10+字符"
                analysis['word_length_distribution'][len_range] = analysis['word_length_distribution'].get(len_range, 0) + 1
                
                # 内容版本分布
                version = word_data.get('contentVersion', 'Unknown')
                analysis['content_version_distribution'][version] = analysis['content_version_distribution'].get(version, 0) + 1
                
                # 重新生成原因分析（从评论中提取关键词）
                comment = word_data.get('aiAuditComment', '').lower()
                if '心语' in comment or 'xinyu' in comment:
                    analysis['common_issues']['心语部分需要改进'] = analysis['common_issues'].get('心语部分需要改进', 0) + 1
                elif '例句' in comment or 'example' in comment:
                    analysis['common_issues']['例句需要改进'] = analysis['common_issues'].get('例句需要改进', 0) + 1
                elif '定义' in comment or 'definition' in comment:
                    analysis['common_issues']['定义需要改进'] = analysis['common_issues'].get('定义需要改进', 0) + 1
                elif '搭配' in comment or 'collocation' in comment:
                    analysis['common_issues']['搭配需要改进'] = analysis['common_issues'].get('搭配需要改进', 0) + 1
                elif '深度' in comment or 'depth' in comment:
                    analysis['common_issues']['内容深度不足'] = analysis['common_issues'].get('内容深度不足', 0) + 1
                elif '生动' in comment or 'vivid' in comment:
                    analysis['common_issues']['内容不够生动'] = analysis['common_issues'].get('内容不够生动', 0) + 1
                elif '准确' in comment or 'accuracy' in comment:
                    analysis['common_issues']['准确性需要提升'] = analysis['common_issues'].get('准确性需要提升', 0) + 1
                else:
                    analysis['common_issues']['其他质量问题'] = analysis['common_issues'].get('其他质量问题', 0) + 1

            # 分析每个分数段的质量特征
            score_groups = {}
            for word_data in low_score_words:
                score = word_data.get('aiAuditScore', 0)
                if score not in score_groups:
                    score_groups[score] = []
                score_groups[score].append(word_data)

            # 为每个分数段生成质量分析
            for score, words in score_groups.items():
                analysis['score_quality_analysis'][f"{score}分"] = {
                    'count': len(words),
                    'percentage': len(words) / len(low_score_words) * 100,
                    'sample_words': [w['word'] for w in words[:10]],  # 前10个样本
                    'common_problems': self._analyze_score_problems(words)
                }

            # 移除候选分析
            for score in range(0, self.max_score + 1):
                count = analysis['score_distribution'].get(f"{score}分", 0)
                if count > 0:
                    analysis['removal_candidates'][f"{score}分及以下"] = sum(
                        analysis['score_distribution'].get(f"{s}分", 0) for s in range(0, score + 1)
                    )

            logger.info("INFO - [LowScoreWordsExtractor][analyze_low_score_words_002] ✅ 低分单词分析完成")
            return analysis

        except Exception as e:
            logger.error(f"ERROR - [LowScoreWordsExtractor][analyze_low_score_words_003] 分析失败: {e}")
            return {}

    def _analyze_score_problems(self, words):
        """
        分析特定分数段的常见问题

        参数:
            words: 该分数段的单词列表

        返回:
            list: 常见问题列表
        """
        problems = []

        # 分析评论中的关键词
        all_comments = ' '.join([w.get('aiAuditComment', '') for w in words]).lower()

        if '拼写错误' in all_comments or 'spelling' in all_comments:
            problems.append('拼写错误')
        if '非标准' in all_comments or 'non-standard' in all_comments:
            problems.append('非标准单词')
        if '过时' in all_comments or 'obsolete' in all_comments:
            problems.append('过时用法')
        if '敏感' in all_comments or 'sensitive' in all_comments:
            problems.append('敏感内容')
        if '专业' in all_comments or 'technical' in all_comments:
            problems.append('专业术语')
        if '心语' in all_comments:
            problems.append('心语部分问题')
        if '例句' in all_comments:
            problems.append('例句质量问题')
        if '定义' in all_comments:
            problems.append('定义准确性问题')

        return problems if problems else ['质量问题待分析']
    

    
    def save_csv_report(self, low_score_words):
        """
        保存CSV格式报告

        参数:
            low_score_words: 低分单词列表

        返回:
            str: 保存的文件路径
        """
        try:
            logger.info("INFO - [LowScoreWordsExtractor][save_csv_report_001] 保存CSV报告")

            csv_file = self.reports_dir / f"all_audited_words_{self.timestamp}.csv"

            if not low_score_words:
                logger.warning("WARN - [LowScoreWordsExtractor][save_csv_report_002] 没有低分单词")
                return None
            
            # CSV字段（包含coreDefinition）
            fieldnames = ['id', 'word', 'coreDefinition', 'aiAuditScore', 'aiAuditShouldRegenerate', 'aiAuditShouldRemove',
                         'aiAuditComment', 'frequency', 'partsOfSpeech', 'learningLanguage',
                         'scaffoldingLanguage', 'publishStatus', 'contentVersion', 'updatedAt']
            
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(low_score_words)

            logger.info(f"INFO - [LowScoreWordsExtractor][save_csv_report_002] ✅ CSV报告已保存: {csv_file}")
            return str(csv_file)

        except Exception as e:
            logger.error(f"ERROR - [LowScoreWordsExtractor][save_csv_report_003] 保存CSV报告失败: {e}")
            return None
    
    def generate_markdown_report(self, low_score_words, analysis):
        """
        生成Markdown格式报告

        参数:
            low_score_words: 低分单词列表
            analysis: 分析结果

        返回:
            str: 保存的文件路径
        """
        try:
            logger.info("INFO - [LowScoreWordsExtractor][generate_markdown_report_001] 生成Markdown报告")
            
            md_file = self.reports_dir / f"audited_words_analysis_report_{self.timestamp}.md"
            
            # 生成报告内容
            report_content = f"""# AI审核单词分析报告 (0-{self.max_score}分)

## 📊 基本统计

- **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **分析范围**: 0-{self.max_score}分的单词
- **审核单词总数**: {analysis.get('total_count', 0):,}
- **数据库路径**: `{self.db_path}`
- **说明**: 本报告包含coreDefinition字段，便于结合comment进行针对性分析

## 🎯 移除阈值分析

### 各分数段移除影响
"""

            # 添加移除候选分析
            for threshold, count in sorted(analysis.get('removal_candidates', {}).items()):
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **移除{threshold}**: {count:,} 个单词 ({percentage:.1f}%)\n"

            report_content += "\n## 📈 详细分析\n\n### 📊 评分分布\n"
            
            for score, count in sorted(analysis.get('score_distribution', {}).items()):
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **{score}**: {count} 个单词 ({percentage:.1f}%)\n"
            
            report_content += "\n### 🔤 频率分布\n"
            for freq, count in sorted(analysis.get('frequency_distribution', {}).items()):
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **{freq}**: {count} 个单词 ({percentage:.1f}%)\n"
            
            report_content += "\n### 📚 词性分布\n"
            for pos, count in sorted(analysis.get('parts_of_speech_distribution', {}).items(), key=lambda x: x[1], reverse=True)[:10]:
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **{pos}**: {count} 个单词 ({percentage:.1f}%)\n"
            
            report_content += "\n### 🔍 需要改进的问题\n"
            for issue, count in sorted(analysis.get('common_issues', {}).items(), key=lambda x: x[1], reverse=True):
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **{issue}**: {count} 个单词 ({percentage:.1f}%)\n"
            
            report_content += "\n### 📏 单词长度分布\n"
            for length, count in sorted(analysis.get('word_length_distribution', {}).items()):
                percentage = (count / analysis['total_count'] * 100) if analysis['total_count'] > 0 else 0
                report_content += f"- **{length}**: {count} 个单词 ({percentage:.1f}%)\n"

            # 添加分数段质量分析
            report_content += "\n### 🔍 各分数段质量分析\n"
            for score_range, quality_info in sorted(analysis.get('score_quality_analysis', {}).items()):
                report_content += f"\n#### {score_range}\n"
                report_content += f"- **数量**: {quality_info['count']:,} 个单词 ({quality_info['percentage']:.1f}%)\n"
                report_content += f"- **主要问题**: {', '.join(quality_info['common_problems'])}\n"
                report_content += f"- **样本单词**: {', '.join(quality_info['sample_words'][:5])}\n"

            # 添加详细列表（前30个，按评分排序）
            report_content += "\n## 📝 详细列表 (前30个，按评分排序)\n\n"
            report_content += "| ID | 单词 | 核心定义 | 评分 | 频率 | 词性 | 改进建议 |\n"
            report_content += "|----|----|----|----|----|----|---------|\n"

            for word_data in low_score_words[:30]:
                comment = word_data.get('aiAuditComment', '')[:50] + '...' if len(word_data.get('aiAuditComment', '')) > 50 else word_data.get('aiAuditComment', '')
                pos_short = word_data.get('partsOfSpeech', '')[:15] + '...' if len(word_data.get('partsOfSpeech', '')) > 15 else word_data.get('partsOfSpeech', '')
                core_def = word_data.get('coreDefinition', '')[:40] + '...' if len(word_data.get('coreDefinition', '')) > 40 else word_data.get('coreDefinition', '')
                report_content += f"| {word_data.get('id', '')} | {word_data.get('word', '')} | {core_def} | {word_data.get('aiAuditScore', '')} | {word_data.get('frequency', '')} | {pos_short} | {comment} |\n"

            if len(low_score_words) > 30:
                report_content += f"\n*... 还有 {len(low_score_words) - 30} 个单词，详见完整的CSV报告*\n"
            
            report_content += f"""
## 📁 相关文件

- **CSV报告**: `all_audited_words_{self.timestamp}.csv`
- **Markdown报告**: `audited_words_analysis_report_{self.timestamp}.md`

## 🎯 改进建议

### 优先级排序
1. **低分单词优先**: 评分0-5分的单词需要优先重新生成
2. **高频单词优先**: High和Medium频率的单词影响更大
3. **常见词性优先**: 名词和动词类单词使用频率更高

### 改进重点
1. **心语部分**: 增加深度和启发性
2. **例句质量**: 提供更生动、实用的例句
3. **定义准确性**: 确保核心定义的准确性
4. **内容丰富度**: 增加搭配、用法等内容

## 🚀 下一步操作

1. **按优先级处理**: 先处理低分和高频单词
2. **批量重新生成**: 可以按批次重新生成内容
3. **质量验证**: 重新生成后进行质量检查
4. **更新数据库**: 完成后更新相关字段

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"INFO - [WordsToRegenerateExtractor][generate_markdown_report_002] ✅ Markdown报告已保存: {md_file}")
            return str(md_file)
            
        except Exception as e:
            logger.error(f"ERROR - [WordsToRegenerateExtractor][generate_markdown_report_003] 生成Markdown报告失败: {e}")
            return None
    
    def run_extraction(self):
        """
        执行完整的提取流程
        
        返回:
            dict: 提取结果
        """
        try:
            logger.info("=" * 60)
            logger.info("INFO - [WordsToRegenerateExtractor][run_extraction_001] 🚀 开始提取需要重新生成的单词")
            logger.info("=" * 60)
            
            # 步骤1: 验证数据库
            logger.info("INFO - [WordsToRegenerateExtractor][run_extraction_002] 步骤1: 验证数据库")
            if not self.validate_database():
                raise RuntimeError("数据库验证失败")
            
            # 步骤2: 提取低分单词
            logger.info("INFO - [LowScoreWordsExtractor][run_extraction_003] 步骤2: 提取低分单词")
            low_score_words = self.extract_low_score_words()
            if not low_score_words:
                logger.info("INFO - [LowScoreWordsExtractor][run_extraction_004] ✅ 没有低分单词")
                return {'success': True, 'count': 0, 'files': []}

            # 步骤3: 分析低分单词
            logger.info("INFO - [LowScoreWordsExtractor][run_extraction_005] 步骤3: 分析低分单词")
            analysis = self.analyze_low_score_words(low_score_words)
            
            # 步骤4: 保存报告
            logger.info("INFO - [LowScoreWordsExtractor][run_extraction_006] 步骤4: 保存报告")

            saved_files = []

            # 保存CSV报告
            csv_file = self.save_csv_report(low_score_words)
            if csv_file:
                saved_files.append(csv_file)

            # 生成Markdown报告
            md_file = self.generate_markdown_report(low_score_words, analysis)
            if md_file:
                saved_files.append(md_file)

            logger.info("=" * 60)
            logger.info("INFO - [LowScoreWordsExtractor][run_extraction_007] ✅ 低分单词提取完成")
            logger.info("=" * 60)

            # 输出摘要
            print(f"\n📊 提取摘要:")
            print(f"  0-{self.max_score}分单词: {len(low_score_words):,} 个")
            print(f"  生成的报告文件: {len(saved_files)} 个 (CSV + Markdown)")
            print(f"  报告保存目录: {self.reports_dir}")
            print(f"  注意: 包含coreDefinition字段，便于结合comment进行针对性分析")

            return {
                'success': True,
                'count': len(low_score_words),
                'files': saved_files,
                'analysis': analysis
            }
            
        except Exception as e:
            logger.error(f"ERROR - [WordsToRegenerateExtractor][run_extraction_008] 提取失败: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="提取低分单词审核结果")
    parser.add_argument("--max-score", type=int, default=6, help="最大评分阈值，默认为6分")

    args = parser.parse_args()

    try:
        extractor = AuditedWordsExtractor(max_score=args.max_score)
        result = extractor.run_extraction()
        
        if result['success']:
            print("✅ 提取完成")
            return 0
        else:
            print(f"❌ 提取失败: {result.get('error', '未知错误')}")
            return 1
        
    except Exception as e:
        logger.error(f"ERROR - [main] 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
