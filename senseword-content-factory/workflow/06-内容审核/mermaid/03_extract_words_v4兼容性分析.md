# 03_extract_words.py v4数据库兼容性分析

## 🔍 兼容性问题分析

### 📊 字段对比表

| 脚本中查询的字段 | v4数据库实际字段 | 兼容性状态 | 说明 |
|-----------------|-----------------|-----------|------|
| `id` | ✅ `id` | ✅ 完全兼容 | 主键字段 |
| `word` | ✅ `word` | ✅ 完全兼容 | 单词字段 |
| `contentJson` | ✅ `contentJson` | ✅ 完全兼容 | 内容JSON字段 |
| `aiAuditScore` | ✅ `aiAuditScore` | ✅ 完全兼容 | AI审核评分 |
| `aiAuditShouldRegenerate` | ✅ `aiAuditShouldRegenerate` | ✅ 完全兼容 | 是否需要重新生成 |
| `aiAuditShouldRemove` | ✅ `aiAuditShouldRemove` | ✅ 完全兼容 | 是否需要移除 |
| `aiAuditComment` | ✅ `aiAuditComment` | ✅ 完全兼容 | AI审核评论 |
| `frequency` | ✅ `frequency` | ✅ 完全兼容 | 单词频率 |
| `partsOfSpeech` | ✅ `partsOfSpeech` | ✅ 完全兼容 | 词性信息 |
| `learningLanguage` | ✅ `learningLanguage` | ✅ 完全兼容 | 学习语言 |
| `scaffoldingLanguage` | ✅ `scaffoldingLanguage` | ✅ 完全兼容 | 脚手架语言 |
| `publishStatus` | ✅ `publishStatus` | ✅ 完全兼容 | 发布状态 |
| `contentVersion` | ✅ `contentVersion` | ✅ 完全兼容 | 内容版本 |
| `updatedAt` | ✅ `updatedAt` | ✅ 完全兼容 | 更新时间 |

## ✅ 兼容性结论

```mermaid
flowchart TD
    ANALYSIS[🔍 兼容性分析] --> FIELD_CHECK[📊 字段检查]
    
    FIELD_CHECK --> ALL_MATCH{✅ 所有字段匹配?}
    ALL_MATCH -->|是| COMPATIBLE[✅ 完全兼容]
    ALL_MATCH -->|否| INCOMPATIBLE[❌ 存在不兼容]
    
    COMPATIBLE --> CONCLUSION[📋 结论：无需修改]
    INCOMPATIBLE --> FIX_NEEDED[🔧 需要修复]
    
    CONCLUSION --> READY[🚀 可直接使用]
    FIX_NEEDED --> MODIFY[✏️ 修改脚本]

    %% 样式定义
    classDef analysis fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef success fill:#E8F5E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef error fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef action fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000

    class ANALYSIS,FIELD_CHECK analysis
    class ALL_MATCH decision
    class COMPATIBLE,CONCLUSION,READY success
    class INCOMPATIBLE error
    class FIX_NEEDED,MODIFY action
```

**🎉 好消息：03_extract_words.py脚本与v4数据库完全兼容！**

所有查询的字段都在v4数据库的words_for_publish表中存在，无需任何修改即可正常运行。

## 📊 数据库架构验证

### v4数据库words_for_publish表结构
```sql
CREATE TABLE IF NOT EXISTS "words_for_publish" (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    learningLanguage TEXT NOT NULL DEFAULT 'en',
    scaffoldingLanguage TEXT NOT NULL DEFAULT 'zh',
    contentJson TEXT NOT NULL,
    publishStatus TEXT NOT NULL DEFAULT 'pending_upload',
    contentVersion TEXT DEFAULT 'v1.0',
    updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    partsOfSpeech TEXT,
    ttsStatus TEXT DEFAULT 'pending',
    aiAuditScore REAL,                          -- ✅ 脚本使用
    aiAuditShouldRegenerate INTEGER DEFAULT 0,  -- ✅ 脚本使用
    aiAuditComment TEXT,                        -- ✅ 脚本使用
    frequency TEXT,                             -- ✅ 脚本使用
    auditStatus TEXT DEFAULT 'pending_review',
    aiAuditShouldRemove INTEGER DEFAULT 0,      -- ✅ 脚本使用
    culturalRiskRegions TEXT DEFAULT '[]',
    ttsHashList TEXT,
    audioGenerated INTEGER DEFAULT 0,
    
    UNIQUE(word, learningLanguage, scaffoldingLanguage)
)
```

### 脚本查询的字段验证
```sql
-- 脚本中的查询语句
SELECT
    id,                        -- ✅ 存在
    word,                      -- ✅ 存在
    contentJson,               -- ✅ 存在
    aiAuditScore,              -- ✅ 存在
    aiAuditShouldRegenerate,   -- ✅ 存在
    aiAuditShouldRemove,       -- ✅ 存在
    aiAuditComment,            -- ✅ 存在
    frequency,                 -- ✅ 存在
    partsOfSpeech,             -- ✅ 存在
    learningLanguage,          -- ✅ 存在
    scaffoldingLanguage,       -- ✅ 存在
    publishStatus,             -- ✅ 存在
    contentVersion,            -- ✅ 存在
    updatedAt                  -- ✅ 存在
FROM words_for_publish
WHERE aiAuditScore IS NOT NULL AND aiAuditScore <= ?
ORDER BY aiAuditScore ASC, id
```

## 🚀 使用建议

### 1. 直接使用
脚本可以直接在v4数据库上运行，无需任何修改：

```bash
# 提取0-6分的低质量单词
python3 03_extract_words.py --max-score 6

# 提取所有已审核单词
python3 03_extract_words.py --max-score 10

# 指定输出目录
python3 03_extract_words.py --max-score 6 --output-dir ./reports
```

### 2. 数据库路径配置
确保脚本能找到正确的v4数据库文件：

```python
# 脚本中的数据库路径配置
DB_PATH = "/path/to/senseword_content_v4.db"
```

### 3. 输出文件格式
脚本会生成两个文件：
- `all_audited_words_{timestamp}.csv` - 包含所有提取字段的CSV数据
- `audited_words_analysis_report_{timestamp}.md` - 详细的分析报告

## 📈 性能优化建议

### 1. 索引优化
为了提高查询性能，建议在v4数据库上创建以下索引：

```sql
-- 为AI审核相关字段创建索引
CREATE INDEX IF NOT EXISTS idx_words_audit_score 
ON words_for_publish(aiAuditScore);

CREATE INDEX IF NOT EXISTS idx_words_audit_status 
ON words_for_publish(aiAuditScore, aiAuditShouldRegenerate, aiAuditShouldRemove);

-- 为排序字段创建复合索引
CREATE INDEX IF NOT EXISTS idx_words_score_id 
ON words_for_publish(aiAuditScore ASC, id ASC);
```

### 2. 查询优化
脚本中的查询已经很好地利用了WHERE条件和ORDER BY，性能表现良好：

```sql
-- 高效的范围查询
WHERE aiAuditScore IS NOT NULL AND aiAuditScore <= ?
ORDER BY aiAuditScore ASC, id
```

### 3. 内存使用
脚本使用流式处理，适合处理大量数据：
- 逐行处理查询结果
- 不会一次性加载所有数据到内存
- 支持处理数万个单词的分析

## 🎯 集成建议

### 与其他工作流程的集成
1. **与01_generate_v4_audit_batch.py集成**: 
   - 使用提取的低分单词生成重新审核的批处理任务

2. **与02_process_audit_results.py集成**:
   - 分析处理结果，了解改进效果

3. **与看板式状态管理集成**:
   - 基于word_processing_queue表的状态进行更精确的筛选

### 工作流程建议
```bash
# 1. 提取需要改进的单词
python3 03_extract_words.py --max-score 6

# 2. 分析报告，确定改进策略
# 查看生成的Markdown报告

# 3. 生成重新审核的批处理任务
python3 01_generate_v4_audit_batch.py --mode regenerate

# 4. 处理审核结果
python3 02_process_audit_results.py --results new_audit_results.jsonl
```

---
*分析完成时间: 2025-07-13*
*结论: 完全兼容，可直接使用*
