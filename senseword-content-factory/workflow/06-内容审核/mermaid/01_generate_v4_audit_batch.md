# 01_generate_v4_audit_batch.py 整体工作流程图

## 🔄 完整流程图

```mermaid
flowchart TD
    START([🚀 脚本启动]) --> PARSE[📝 解析命令行参数]
    
    PARSE --> INIT[🏗️ 初始化ContentAuditBatchProcessor]
    
    INIT --> VALIDATE{🔍 验证文件存在性}
    VALIDATE -->|❌ 失败| ERROR1[❌ 输出错误信息并退出]
    VALIDATE -->|✅ 成功| CHECK_TABLES[🗄️ 检查数据库表]
    
    CHECK_TABLES --> TABLE_CHECK{📋 表是否存在?}
    TABLE_CHECK -->|❌ 缺少表| ERROR2[❌ 数据库表检查失败]
    TABLE_CHECK -->|✅ 完整| GET_STATS[📊 获取审核统计信息]
    
    GET_STATS --> LOAD_PROMPT[📖 加载v4提示词模板]
    LOAD_PROMPT --> PROMPT_CHECK{📄 提示词加载成功?}
    PROMPT_CHECK -->|❌ 失败| ERROR3[❌ 提示词加载失败]
    PROMPT_CHECK -->|✅ 成功| GET_WORDS[🔍 获取需要审核的单词]
    
    GET_WORDS --> MODE_CHECK{🎯 根据模式筛选}
    MODE_CHECK -->|unaudited| QUERY1[📊 contentGenerated=1 AND contentAiReviewed=0]
    MODE_CHECK -->|regenerate| QUERY2[📊 aiAuditShouldRegenerate=1]
    MODE_CHECK -->|all| QUERY3[📊 所有已生成内容的单词]
    
    QUERY1 --> WORD_CHECK{📝 是否找到单词?}
    QUERY2 --> WORD_CHECK
    QUERY3 --> WORD_CHECK
    
    WORD_CHECK -->|❌ 无单词| ERROR4[❌ 没有找到符合条件的单词]
    WORD_CHECK -->|✅ 有单词| CREATE_FILE[📁 创建批处理文件]
    
    CREATE_FILE --> PROCESS_LOOP[🔄 处理每个单词]
    
    PROCESS_LOOP --> CREATE_REQUEST[🛠️ 创建批处理请求]
    CREATE_REQUEST --> REQUEST_CHECK{✅ 请求创建成功?}
    REQUEST_CHECK -->|❌ 失败| SKIP[⏭️ 跳过该单词]
    REQUEST_CHECK -->|✅ 成功| WRITE_JSONL[📝 写入JSONL文件]
    
    WRITE_JSONL --> MORE_WORDS{🔄 还有更多单词?}
    MORE_WORDS -->|✅ 是| PROCESS_LOOP
    MORE_WORDS -->|❌ 否| SUCCESS[🎉 生成成功]
    
    SKIP --> MORE_WORDS
    
    SUCCESS --> OUTPUT[📊 输出生成摘要]
    OUTPUT --> END([✅ 脚本完成])
    
    ERROR1 --> END
    ERROR2 --> END
    ERROR3 --> END
    ERROR4 --> END

    %% 样式定义
    classDef startEnd fill:#E8F5E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef success fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef data fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class START,END startEnd
    class PARSE,INIT,CHECK_TABLES,GET_STATS,LOAD_PROMPT,GET_WORDS,CREATE_FILE,PROCESS_LOOP,CREATE_REQUEST,WRITE_JSONL process
    class VALIDATE,TABLE_CHECK,PROMPT_CHECK,MODE_CHECK,WORD_CHECK,REQUEST_CHECK,MORE_WORDS decision
    class ERROR1,ERROR2,ERROR3,ERROR4,SKIP error
    class SUCCESS success
    class QUERY1,QUERY2,QUERY3 data
    class OUTPUT output
```

## 📊 关键决策节点说明

### 🔍 文件验证阶段
- **数据库文件**: 检查senseword_content_v4.db是否存在
- **提示词文件**: 验证03_senseword-内容审核-v4-英语版本.md是否可访问
- **输出目录**: 确保输出目录存在或可创建

### 🗄️ 数据库表检查
- **words_for_publish**: 主要内容表，包含单词和审核结果
- **word_processing_queue**: 看板式状态管理表，跟踪处理进度

### 🎯 模式筛选逻辑
- **unaudited模式**: `contentGenerated=1 AND contentAiReviewed=0`
- **regenerate模式**: `aiAuditShouldRegenerate=1`
- **all模式**: 所有已生成内容的单词

## 📈 性能特征

### 内存使用
- **流式处理**: 逐个处理单词，避免一次性加载所有数据
- **JSON解析**: 仅在需要时解析contentJson字段
- **文件写入**: 实时写入JSONL文件，不缓存在内存中

### 错误恢复
- **跳过机制**: 单个单词处理失败不影响整体流程
- **详细日志**: 记录每个步骤的执行状态
- **统计信息**: 提供成功和失败的详细计数

---
# 01_generate_v4_audit_batch.py 详细时序图

## ⏱️ 组件交互时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant Main as 🚀 main()
    participant Parser as 📝 ArgumentParser
    participant Processor as 🏗️ ContentAuditBatchProcessor
    participant DB as 🗄️ SQLite数据库
    participant FS as 📁 文件系统
    participant Logger as 📋 日志系统

    User->>Main: 执行脚本
    Main->>Parser: 解析命令行参数
    Parser-->>Main: 返回参数对象

    Main->>Processor: 初始化处理器
    Note over Processor: 设置路径、模式、限制等参数
    
    Processor->>FS: 验证数据库文件存在性
    FS-->>Processor: 文件状态
    
    Processor->>FS: 验证提示词文件存在性
    FS-->>Processor: 文件状态
    
    alt 文件验证失败
        Processor->>Logger: 记录错误
        Processor-->>Main: 返回错误
        Main-->>User: 退出程序
    end

    Processor->>Main: 调用generate_batch_file()
    
    Processor->>DB: 检查必需表存在性
    DB-->>Processor: 表检查结果
    
    alt 表不存在
        Processor->>Logger: 记录错误
        Processor-->>Main: 返回失败结果
    end

    Processor->>DB: 获取审核统计信息
    Note over DB: SELECT COUNT(*) FROM word_processing_queue...
    DB-->>Processor: 统计数据
    Processor->>Logger: 输出统计信息

    Processor->>FS: 加载提示词模板
    FS-->>Processor: 提示词内容
    Processor->>Logger: 记录加载成功

    Processor->>DB: 根据模式查询单词
    Note over DB: 不同模式使用不同SQL查询
    
    alt unaudited模式
        Note over DB: contentGenerated=1 AND contentAiReviewed=0
    else regenerate模式
        Note over DB: aiAuditShouldRegenerate=1
    else all模式
        Note over DB: 所有已生成内容的单词
    end
    
    DB-->>Processor: 单词数据列表
    Processor->>Logger: 记录获取的单词数量

    alt 没有找到单词
        Processor->>Logger: 记录警告
        Processor-->>Main: 返回失败结果
    end

    Processor->>FS: 创建输出文件
    FS-->>Processor: 文件句柄

    loop 处理每个单词
        Processor->>Processor: 创建批处理请求
        Note over Processor: 构建Vertex AI请求格式
        
        alt 请求创建成功
            Processor->>FS: 写入JSONL行
            FS-->>Processor: 写入确认
        else 请求创建失败
            Processor->>Logger: 记录警告
            Note over Processor: 跳过该单词，继续处理
        end
    end

    Processor->>FS: 关闭输出文件
    Processor->>Logger: 记录生成完成
    
    Processor-->>Main: 返回成功结果
    Main->>User: 输出生成摘要
    
    Note over User: 🎉 批处理文件生成完成
```

## 🔄 关键时间节点

### 初始化阶段 (0-100ms)
- 参数解析和验证
- 文件路径解析
- 处理器实例化

### 验证阶段 (100-500ms)
- 数据库文件存在性检查
- 提示词文件访问验证
- 数据库表结构检查

### 数据获取阶段 (500ms-2s)
- 统计信息查询
- 提示词文件读取
- 单词数据查询和解析

### 文件生成阶段 (2s-30s)
- 批处理文件创建
- 逐个单词处理
- JSONL格式写入

## 📊 性能特征

### 数据库操作
- **连接复用**: 使用with语句自动管理连接
- **查询优化**: 基于索引的高效查询
- **批量获取**: 一次性获取所有符合条件的单词

### 内存管理
- **流式处理**: 不在内存中缓存所有数据
- **即时写入**: 生成的请求立即写入文件
- **JSON解析**: 仅在需要时解析contentJson

---
# 数据结构转化过程详解

## 🔄 完整数据流转图

```mermaid
flowchart LR
    subgraph DB ["🗄️ 数据库查询结果"]
        SQL[📊 SQL查询]
        RAW[📋 原始元组数据]
    end
    
    subgraph PYTHON ["🐍 Python对象转换"]
        TUPLE[📝 元组解析]
        DICT[📦 字典构建]
        JSON_PARSE[🔧 JSON解析]
    end
    
    subgraph REQUEST ["🛠️ 批处理请求构建"]
        INPUT[📥 输入数据结构]
        PROMPT[📖 提示词模板]
        VERTEX[🚀 Vertex AI格式]
    end
    
    subgraph OUTPUT ["📤 最终输出"]
        JSONL[📄 JSONL文件]
        STATS[📊 统计信息]
    end

    SQL --> RAW
    RAW --> TUPLE
    TUPLE --> DICT
    DICT --> JSON_PARSE
    JSON_PARSE --> INPUT
    INPUT --> REQUEST
    PROMPT --> REQUEST
    REQUEST --> VERTEX
    VERTEX --> JSONL
    VERTEX --> STATS

    %% 样式定义
    classDef database fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef python fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef request fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class SQL,RAW database
    class TUPLE,DICT,JSON_PARSE python
    class INPUT,PROMPT,VERTEX request
    class JSONL,STATS output
```

## 📊 真实数据转换示例

### 阶段1: 数据库查询结果
```sql
-- unaudited模式的SQL查询
SELECT w.id, w.word, w.contentJson, w.learningLanguage, w.scaffoldingLanguage
FROM words_for_publish w
INNER JOIN word_processing_queue q ON w.word = q.word 
WHERE q.contentGenerated = 1 AND q.contentAiReviewed = 0
LIMIT 1000
```

**原始元组数据**:
```python
(
    12345,                    # id
    "serendipity",           # word
    '{"word":"serendipity",...}',  # contentJson (JSON字符串)
    "en",                    # learningLanguage
    "zh"                     # scaffoldingLanguage
)
```

### 阶段2: Python对象转换
```python
# 元组解析为字典
word_data = {
    'id': 12345,
    'word': 'serendipity',
    'contentJson': {
        "word": "serendipity",
        "metadata": {
            "wordFrequency": "low",
            "relatedConcepts": ["chance", "discovery", "fortune"],
            "culturalRiskRegions": []
        },
        "content": {
            "difficulty": "advanced",
            "coreDefinition": "The occurrence of events by chance in a happy way",
            "contextualExplanation": {
                "nativeSpeakerIntent": "Express pleasant surprise at unexpected discoveries",
                "emotionalResonance": "Wonder, delight, gratitude",
                "vividImagery": "Like finding a hidden treasure while walking",
                "etymologicalEssence": "From Persian fairy tale 'Three Princes of Serendip'"
            },
            "usageScenarios": [
                {
                    "context": "Academic research",
                    "situation": "Discovering unexpected connections in data"
                }
            ],
            "phoneticSymbols": [
                {
                    "type": "bre",
                    "symbol": "/ˌserənˈdɪpɪti/",
                    "ttsId": "abc123def456"
                }
            ],
            "usageExamples": [
                {
                    "learningLanguage": "It was pure serendipity that led to the discovery.",
                    "ttsId": "def789ghi012"
                }
            ]
        }
    },
    'learningLanguage': 'en',
    'scaffoldingLanguage': 'zh'
}
```

### 阶段3: 批处理请求构建
```python
# 输入数据结构（发送给AI的数据）
input_data = {
    "id": 12345,
    "word": "serendipity",
    "contentJson": {
        # 完整的contentJson内容
        "word": "serendipity",
        "metadata": {...},
        "content": {...}
    }
}

# 用户消息内容
user_message = f"""
{prompt_content}

请审核以下单词内容：
{json.dumps(input_data, ensure_ascii=False, indent=2)}
"""
```

### 阶段4: Vertex AI批处理格式
```python
# 最终的Vertex AI请求格式
vertex_request = {
    "request": {
        "contents": [
            {
                "role": "user",
                "parts": [
                    {
                        "text": user_message  # 包含提示词+单词数据
                    }
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.3,
            "maxOutputTokens": 20000,
            "topK": 40,
            "topP": 0.95,
            "responseMimeType": "application/json"
        }
    }
}
```

### 阶段5: JSONL文件输出
```jsonl
{"request":{"contents":[{"role":"user","parts":[{"text":"# SenseWord AI Content Auditor v4\n\n## Role Definition...\n\n请审核以下单词内容：\n{\n  \"id\": 12345,\n  \"word\": \"serendipity\",\n  \"contentJson\": {...}\n}"}]}],"generationConfig":{"temperature":0.3,"maxOutputTokens":20000,"topK":40,"topP":0.95,"responseMimeType":"application/json"}}}
```

## 📈 数据处理统计

### 文件大小估算
- **单个请求**: ~12-18KB（包含完整提示词和内容）
- **1000个请求**: ~12-18MB JSONL文件
- **提示词大小**: ~25KB（v4版本）
- **单词内容**: ~3-8KB（根据内容复杂度）

### 处理性能
- **JSON解析**: ~0.1ms/单词
- **请求构建**: ~0.5ms/单词
- **文件写入**: ~0.2ms/单词
- **总体处理**: ~1000单词/秒
---
# 系统架构图

## 🏗️ 整体系统架构

```mermaid
graph TB
    subgraph USER ["👤 用户层"]
        CLI[🖥️ 命令行界面]
        PARAMS[📝 参数配置]
    end
    
    subgraph SCRIPT ["🚀 脚本层"]
        MAIN[🎯 main函数]
        PROCESSOR[🏗️ ContentAuditBatchProcessor]
    end
    
    subgraph CORE ["⚙️ 核心组件"]
        VALIDATOR[🔍 文件验证器]
        DB_CHECKER[🗄️ 数据库检查器]
        PROMPT_LOADER[📖 提示词加载器]
        WORD_FETCHER[📊 单词获取器]
        REQUEST_BUILDER[🛠️ 请求构建器]
        FILE_WRITER[📝 文件写入器]
    end
    
    subgraph EXTERNAL ["🌐 外部系统"]
        DATABASE[(🗄️ SQLite数据库)]
        PROMPT_FILE[📄 提示词文件]
        OUTPUT_DIR[📁 输出目录]
        VERTEX_AI[🤖 Vertex AI批处理]
    end
    
    subgraph TABLES ["📋 数据库表"]
        WORDS_TABLE[(📚 words_for_publish)]
        QUEUE_TABLE[(📋 word_processing_queue)]
        TTS_TABLE[(🔊 tts_assets)]
    end

    %% 用户交互
    CLI --> MAIN
    PARAMS --> PROCESSOR
    
    %% 脚本内部流程
    MAIN --> PROCESSOR
    PROCESSOR --> VALIDATOR
    PROCESSOR --> DB_CHECKER
    PROCESSOR --> PROMPT_LOADER
    PROCESSOR --> WORD_FETCHER
    PROCESSOR --> REQUEST_BUILDER
    PROCESSOR --> FILE_WRITER
    
    %% 外部系统交互
    VALIDATOR --> PROMPT_FILE
    VALIDATOR --> DATABASE
    DB_CHECKER --> DATABASE
    PROMPT_LOADER --> PROMPT_FILE
    WORD_FETCHER --> DATABASE
    FILE_WRITER --> OUTPUT_DIR
    
    %% 数据库表关系
    DATABASE --> WORDS_TABLE
    DATABASE --> QUEUE_TABLE
    DATABASE --> TTS_TABLE
    
    %% 输出到外部系统
    OUTPUT_DIR --> VERTEX_AI

    %% 样式定义
    classDef user fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef script fill:#FFF2CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef core fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef tables fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class CLI,PARAMS user
    class MAIN,PROCESSOR script
    class VALIDATOR,DB_CHECKER,PROMPT_LOADER,WORD_FETCHER,REQUEST_BUILDER,FILE_WRITER core
    class DATABASE,PROMPT_FILE,OUTPUT_DIR,VERTEX_AI external
    class WORDS_TABLE,QUEUE_TABLE,TTS_TABLE tables
```

## 🔧 核心组件详解

### ContentAuditBatchProcessor类
```mermaid
classDiagram
    class ContentAuditBatchProcessor {
        -db_path: Path
        -prompt_path: Path
        -mode: str
        -limit: int
        -output_dir: Path
        -timestamp: str
        
        +__init__(db_path, prompt_path, mode, limit, output_dir)
        +_validate_files() bool
        +check_required_tables() bool
        +get_audit_statistics() Dict
        +load_prompt_template() str
        +get_words_for_audit() List[Dict]
        +create_batch_request(word_data, prompt_content) Dict
        +generate_batch_file() Dict
    }
    
    ContentAuditBatchProcessor --> SQLiteDatabase : 查询数据
    ContentAuditBatchProcessor --> FileSystem : 读写文件
    ContentAuditBatchProcessor --> Logger : 记录日志

    %% 样式定义
    classDef processor fill:#FFF2CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef external fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000

    class ContentAuditBatchProcessor processor
    class SQLiteDatabase,FileSystem,Logger external
```

## 🗄️ 数据库架构关系

```mermaid
erDiagram
    words_for_publish ||--o{ word_processing_queue : "word + language pair"
    words_for_publish ||--o{ tts_assets : "ttsHashList"
    
    words_for_publish {
        int id PK
        string word
        string learningLanguage
        string scaffoldingLanguage
        text contentJson
        real aiAuditScore
        int aiAuditShouldRegenerate
        int aiAuditShouldRemove
        text aiAuditComment
        string publishStatus
        string auditStatus
    }
    
    word_processing_queue {
        int id PK
        string word
        string learningLanguage
        string scaffoldingLanguage
        boolean contentGenerated
        boolean contentAiReviewed
        boolean ttsIdGenerated
        boolean audioGenerated
        boolean readyForPublish
        timestamp createdAt
        timestamp updatedAt
    }
    
    tts_assets {
        string ttsId PK
        text originalText
        text normalizedText
        text textToSpeak
        string learningLanguage
        string ttsType
        string status
        string audioUrl
        real audioDuration
        timestamp createdAt
    }
```

## 📊 数据流向图

```mermaid
flowchart LR
    subgraph INPUT ["📥 输入源"]
        CMD[🖥️ 命令行参数]
        DB[(🗄️ 数据库)]
        PROMPT[📄 提示词文件]
    end
    
    subgraph PROCESSING ["⚙️ 处理层"]
        PARSE[📝 参数解析]
        VALIDATE[🔍 验证]
        QUERY[📊 数据查询]
        BUILD[🛠️ 请求构建]
    end
    
    subgraph OUTPUT ["📤 输出"]
        JSONL[📄 JSONL文件]
        LOGS[📋 日志信息]
        STATS[📊 统计报告]
    end
    
    CMD --> PARSE
    DB --> QUERY
    PROMPT --> BUILD
    
    PARSE --> VALIDATE
    VALIDATE --> QUERY
    QUERY --> BUILD
    
    BUILD --> JSONL
    BUILD --> LOGS
    BUILD --> STATS

    %% 样式定义
    classDef input fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processing fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class CMD,DB,PROMPT input
    class PARSE,VALIDATE,QUERY,BUILD processing
    class JSONL,LOGS,STATS output
```

## 🔄 外部系统集成

### 与Google Cloud Platform的集成
- **输入**: 生成的JSONL批处理文件
- **处理**: Vertex AI批处理服务
- **输出**: AI审核结果文件

### 与看板式状态管理的集成
- **读取**: word_processing_queue表的状态信息
- **筛选**: 基于contentGenerated和contentAiReviewed状态
- **更新**: 后续脚本更新contentAiReviewed状态
---
# 看板式状态管理详解

## 📋 看板状态管理架构

```mermaid
flowchart TD
    subgraph KANBAN ["📋 看板式状态管理"]
        subgraph COLUMNS ["📊 看板列"]
            COL1[📝 内容生成<br/>contentGenerated]
            COL2[🔍 AI审核<br/>contentAiReviewed]
            COL3[🔊 TTS生成<br/>ttsIdGenerated]
            COL4[🎵 音频生成<br/>audioGenerated]
            COL5[🚀 准备发布<br/>readyForPublish]
        end
        
        subgraph STATES ["🎯 状态值"]
            FALSE1[❌ FALSE]
            TRUE1[✅ TRUE]
            FALSE2[❌ FALSE]
            TRUE2[✅ TRUE]
            FALSE3[❌ FALSE]
            TRUE3[✅ TRUE]
            FALSE4[❌ FALSE]
            TRUE4[✅ TRUE]
            FALSE5[❌ FALSE]
            TRUE5[✅ TRUE]
        end
    end
    
    subgraph SCRIPT_FOCUS ["🎯 脚本关注点"]
        TARGET[🔍 目标单词筛选]
        CONDITION[📊 筛选条件]
    end
    
    COL1 --> TRUE1
    COL2 --> FALSE2
    TRUE1 --> TARGET
    FALSE2 --> TARGET
    TARGET --> CONDITION

    %% 样式定义
    classDef column fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef stateTrue fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef stateFalse fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef focus fill:#E8F4FD,stroke:#000000,stroke-width:3px,color:#000000

    class COL1,COL2,COL3,COL4,COL5 column
    class TRUE1,TRUE2,TRUE3,TRUE4,TRUE5 stateTrue
    class FALSE1,FALSE2,FALSE3,FALSE4,FALSE5 stateFalse
    class TARGET,CONDITION focus
```

## 🎯 不同模式的筛选逻辑

```mermaid
flowchart TD
    START[🚀 开始筛选] --> MODE_CHECK{🎯 选择模式}
    
    MODE_CHECK -->|unaudited| UNAUDITED[📊 未审核模式]
    MODE_CHECK -->|regenerate| REGENERATE[🔄 重新生成模式]
    MODE_CHECK -->|all| ALL[📚 全部模式]
    
    UNAUDITED --> COND1[📋 contentGenerated = TRUE<br/>AND<br/>contentAiReviewed = FALSE]
    REGENERATE --> COND2[📋 aiAuditShouldRegenerate = 1]
    ALL --> COND3[📋 contentGenerated = TRUE]
    
    COND1 --> SQL1[📊 SQL查询1]
    COND2 --> SQL2[📊 SQL查询2]
    COND3 --> SQL3[📊 SQL查询3]
    
    SQL1 --> RESULT[📝 筛选结果]
    SQL2 --> RESULT
    SQL3 --> RESULT
    
    RESULT --> COUNT[📊 统计数量]
    COUNT --> OUTPUT[📤 输出到批处理]

    %% 样式定义
    classDef start fill:#E8F5E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef mode fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef condition fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef sql fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class START start
    class UNAUDITED,REGENERATE,ALL mode
    class COND1,COND2,COND3 condition
    class SQL1,SQL2,SQL3 sql
    class RESULT,COUNT,OUTPUT output
```

## 📊 真实数据状态示例

### 典型的状态分布
```mermaid
pie title 📊 单词处理状态分布
    "✅ 内容已生成" : 55703
    "🔍 已AI审核" : 55700
    "🔊 TTS已生成" : 55703
    "🎵 音频已生成" : 0
    "🚀 准备发布" : 0
```

### unaudited模式筛选结果
```mermaid
flowchart LR
    subgraph TOTAL ["📊 总数据"]
        T1[📚 总单词: 55,703]
        T2[✅ 内容已生成: 55,703]
        T3[🔍 已审核: 55,700]
    end
    
    subgraph FILTER ["🎯 筛选条件"]
        F1[contentGenerated = TRUE]
        F2[contentAiReviewed = FALSE]
    end
    
    subgraph RESULT ["📝 筛选结果"]
        R1[🎯 目标单词: 3个]
        R2[📋 需要审核的单词]
    end
    
    T2 --> F1
    T3 --> F2
    F1 --> R1
    F2 --> R1
    R1 --> R2

    %% 样式定义
    classDef total fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef filter fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef result fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000

    class T1,T2,T3 total
    class F1,F2 filter
    class R1,R2 result
```

## 🔄 状态转换流程

```mermaid
stateDiagram-v2
    [*] --> ContentGenerated : 内容生成完成
    
    ContentGenerated --> AiReviewing : 开始AI审核
    note right of AiReviewing : 01_generate_v4_audit_batch.py<br/>生成批处理任务
    
    AiReviewing --> AiReviewed : AI审核完成
    note right of AiReviewed : 03_update_audit_results_to_db.py<br/>更新审核结果
    
    AiReviewed --> TtsGenerated : TTS ID生成
    TtsGenerated --> AudioGenerated : 音频生成
    AudioGenerated --> ReadyForPublish : 准备发布
    
    AiReviewed --> ContentRegeneration : 需要重新生成
    note right of ContentRegeneration : aiAuditShouldRegenerate = 1
    
    ContentRegeneration --> ContentGenerated : 重新生成完成
    
    AiReviewed --> Removed : 标记删除
    note right of Removed : aiAuditShouldRemove = 1
```

## 📈 统计信息查询

### 获取审核统计的SQL查询
```sql
SELECT 
    COUNT(*) as total_words,
    COUNT(CASE WHEN q.contentGenerated = 1 THEN 1 END) as content_generated,
    COUNT(CASE WHEN q.contentAiReviewed = 1 THEN 1 END) as content_reviewed,
    COUNT(CASE WHEN w.aiAuditScore IS NOT NULL THEN 1 END) as audited_words,
    COUNT(CASE WHEN w.aiAuditShouldRegenerate = 1 THEN 1 END) as need_regenerate
FROM word_processing_queue q
LEFT JOIN words_for_publish w ON q.word = w.word 
    AND q.learningLanguage = w.learningLanguage 
    AND q.scaffoldingLanguage = w.scaffoldingLanguage
```

### 典型的统计输出
```
📊 数据库统计信息:
   - 总单词数: 55,703
   - 已生成内容: 55,703 (100%)
   - 已AI审核: 55,700 (99.99%)
   - 需要重新生成: 0 (0%)
```

## 🎯 脚本的核心价值

### 智能筛选
- **精确定位**: 只处理需要审核的单词
- **避免重复**: 不会重复审核已处理的内容
- **状态感知**: 基于看板状态做出智能决策

### 成本优化
- **按需处理**: 只为真正需要的单词生成AI请求
- **批量优化**: 支持限制数量，控制单次处理规模
- **资源节约**: 避免不必要的API调用和计算资源浪费
