# 03_extract_words.py AI审核单词数据提取器详细工作原理

## 🔄 完整工作流程图

```mermaid
flowchart TD
    START([🚀 脚本启动]) --> PARSE[📝 解析命令行参数]
    
    PARSE --> INIT[🏗️ 初始化AuditedWordsExtractor]
    
    INIT --> VALIDATE{🔍 验证数据库文件}
    VALIDATE -->|❌ 失败| ERROR1[❌ 数据库文件不存在]
    VALIDATE -->|✅ 成功| SCORE_CHECK{🎯 检查评分阈值}
    
    SCORE_CHECK -->|max_score >= 10| QUERY_ALL[📊 查询所有已审核单词]
    SCORE_CHECK -->|max_score < 10| QUERY_RANGE[📊 查询指定分数范围单词]
    
    QUERY_ALL --> EXTRACT_DATA[🔧 提取单词数据]
    QUERY_RANGE --> EXTRACT_DATA
    
    EXTRACT_DATA --> PARSE_JSON[📋 解析contentJson提取coreDefinition]
    PARSE_JSON --> BUILD_DATASET[📦 构建单词数据集]
    
    BUILD_DATASET --> ANALYZE[📈 分析单词分布和问题]
    
    ANALYZE --> SCORE_DIST[📊 评分分布分析]
    ANALYZE --> FREQ_DIST[📊 频率分布分析]
    ANALYZE --> POS_DIST[📊 词性分布分析]
    ANALYZE --> ISSUE_ANALYSIS[🔍 问题类型分析]
    ANALYZE --> QUALITY_ANALYSIS[🎯 质量特征分析]
    
    SCORE_DIST --> SAVE_CSV[💾 保存CSV报告]
    FREQ_DIST --> SAVE_CSV
    POS_DIST --> SAVE_CSV
    ISSUE_ANALYSIS --> SAVE_CSV
    QUALITY_ANALYSIS --> SAVE_CSV
    
    SAVE_CSV --> GENERATE_MD[📄 生成Markdown报告]
    GENERATE_MD --> OUTPUT_SUMMARY[📊 输出处理摘要]
    OUTPUT_SUMMARY --> END([✅ 脚本完成])
    
    ERROR1 --> END

    %% 样式定义
    classDef startEnd fill:#E8F5E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef error fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef analysis fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class START,END startEnd
    class PARSE,INIT,EXTRACT_DATA,PARSE_JSON,BUILD_DATASET,ANALYZE process
    class VALIDATE,SCORE_CHECK decision
    class ERROR1 error
    class SCORE_DIST,FREQ_DIST,POS_DIST,ISSUE_ANALYSIS,QUALITY_ANALYSIS,QUERY_ALL,QUERY_RANGE analysis
    class SAVE_CSV,GENERATE_MD,OUTPUT_SUMMARY output
```

## ⏱️ 详细时序图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant Main as 🚀 main()
    participant Extractor as 🏗️ AuditedWordsExtractor
    participant DB as 🗄️ SQLite数据库
    participant Analyzer as 📈 数据分析器
    participant FileSystem as 📁 文件系统

    User->>Main: 执行脚本 --max-score 6
    Main->>Extractor: 初始化提取器(max_score=6)
    
    Extractor->>Extractor: 设置数据库路径和报告目录
    Note over Extractor: 创建时间戳，确保目录存在
    
    Extractor->>DB: 验证数据库文件存在性
    DB-->>Extractor: 文件状态确认
    
    alt 数据库验证失败
        Extractor-->>Main: 返回错误
        Main-->>User: 输出错误信息
    end
    
    Extractor->>DB: 执行SQL查询
    Note over DB: 根据max_score决定查询范围
    
    alt max_score >= 10
        Note over DB: SELECT * WHERE aiAuditScore IS NOT NULL
    else max_score < 10
        Note over DB: SELECT * WHERE aiAuditScore <= max_score
    end
    
    DB-->>Extractor: 返回原始查询结果
    
    loop 处理每个单词记录
        Extractor->>Extractor: 解析contentJson
        Note over Extractor: 提取coreDefinition字段
        
        alt JSON解析成功
            Extractor->>Extractor: 提取coreDefinition
        else JSON解析失败
            Extractor->>Extractor: 标记为"解析失败"
        end
        
        Extractor->>Extractor: 构建单词数据对象
    end
    
    Extractor->>Analyzer: 开始数据分析
    
    Analyzer->>Analyzer: 评分分布统计
    Analyzer->>Analyzer: 频率分布统计
    Analyzer->>Analyzer: 词性分布统计
    Analyzer->>Analyzer: 单词长度分布统计
    Analyzer->>Analyzer: 问题类型分析
    
    loop 分析每个分数段
        Analyzer->>Analyzer: 生成质量特征分析
        Note over Analyzer: 样本单词、常见问题、百分比
    end
    
    Analyzer-->>Extractor: 返回完整分析结果
    
    Extractor->>FileSystem: 保存CSV报告
    Note over FileSystem: all_audited_words_{timestamp}.csv
    FileSystem-->>Extractor: CSV文件路径
    
    Extractor->>FileSystem: 生成Markdown报告
    Note over FileSystem: audited_words_analysis_report_{timestamp}.md
    FileSystem-->>Extractor: Markdown文件路径
    
    Extractor-->>Main: 返回处理结果
    Main->>User: 输出处理摘要
    
    Note over User: 📊 提取摘要:<br/>单词数量、报告文件、保存目录
```

## 🔄 数据结构转化过程

```mermaid
flowchart LR
    subgraph DATABASE ["🗄️ 数据库查询结果"]
        SQL_QUERY[📊 SQL查询]
        RAW_ROWS[📋 原始数据行]
    end
    
    subgraph EXTRACTION ["🔧 数据提取阶段"]
        JSON_PARSE[📝 JSON解析]
        CORE_DEF[📖 coreDefinition提取]
        WORD_OBJECT[📦 单词对象构建]
    end
    
    subgraph ANALYSIS ["📈 数据分析阶段"]
        SCORE_ANALYSIS[📊 评分分布]
        FREQ_ANALYSIS[📊 频率分布]
        POS_ANALYSIS[📊 词性分布]
        ISSUE_ANALYSIS[🔍 问题分析]
        QUALITY_ANALYSIS[🎯 质量分析]
    end
    
    subgraph OUTPUT ["📤 输出文件"]
        CSV_FILE[📄 CSV数据文件]
        MD_REPORT[📋 Markdown报告]
        SUMMARY[📊 处理摘要]
    end

    SQL_QUERY --> RAW_ROWS
    RAW_ROWS --> JSON_PARSE
    JSON_PARSE --> CORE_DEF
    CORE_DEF --> WORD_OBJECT
    
    WORD_OBJECT --> SCORE_ANALYSIS
    WORD_OBJECT --> FREQ_ANALYSIS
    WORD_OBJECT --> POS_ANALYSIS
    WORD_OBJECT --> ISSUE_ANALYSIS
    WORD_OBJECT --> QUALITY_ANALYSIS
    
    SCORE_ANALYSIS --> CSV_FILE
    FREQ_ANALYSIS --> MD_REPORT
    POS_ANALYSIS --> SUMMARY
    ISSUE_ANALYSIS --> CSV_FILE
    QUALITY_ANALYSIS --> MD_REPORT

    %% 样式定义
    classDef database fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef extraction fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef analysis fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class SQL_QUERY,RAW_ROWS database
    class JSON_PARSE,CORE_DEF,WORD_OBJECT extraction
    class SCORE_ANALYSIS,FREQ_ANALYSIS,POS_ANALYSIS,ISSUE_ANALYSIS,QUALITY_ANALYSIS analysis
    class CSV_FILE,MD_REPORT,SUMMARY output
```

## 📊 真实数据转换示例

### 数据库查询结果示例
```sql
-- 查询指定分数范围的单词
SELECT id, word, contentJson, aiAuditScore, aiAuditComment, frequency, partsOfSpeech
FROM words_for_publish 
WHERE aiAuditScore IS NOT NULL AND aiAuditScore <= 6
ORDER BY aiAuditScore ASC, id
```

**原始查询结果**:
```
(12345, "serendipity", '{"word":"serendipity","content":{"coreDefinition":"The occurrence of events by chance"}}', 4, "定义需要更详细", "low", "noun")
```

### JSON解析和数据提取
```mermaid
flowchart LR
    subgraph BEFORE ["📋 原始数据"]
        RAW_JSON["contentJson字符串:<br/>'{\\"word\\":\\"serendipity\\",\\"content\\":{\\"coreDefinition\\":\\"The occurrence...\\"}}'"]
    end
    
    subgraph PARSING ["🔧 解析过程"]
        JSON_DECODE["JSON.loads():<br/>解析为Python字典"]
        EXTRACT_DEF["提取路径:<br/>content.coreDefinition"]
    end
    
    subgraph AFTER ["📦 提取结果"]
        WORD_DATA["单词数据对象:<br/>{<br/>  'id': 12345,<br/>  'word': 'serendipity',<br/>  'coreDefinition': 'The occurrence of events by chance',<br/>  'aiAuditScore': 4,<br/>  'aiAuditComment': '定义需要更详细'<br/>}"]
    end
    
    RAW_JSON --> JSON_DECODE
    JSON_DECODE --> EXTRACT_DEF
    EXTRACT_DEF --> WORD_DATA

    %% 样式定义
    classDef before fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef parsing fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef after fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000

    class RAW_JSON before
    class JSON_DECODE,EXTRACT_DEF parsing
    class WORD_DATA after
```

## 📈 数据分析过程详解

```mermaid
flowchart TD
    START_ANALYSIS[📈 开始数据分析] --> INIT_ANALYSIS[🏗️ 初始化分析结构]

    INIT_ANALYSIS --> LOOP_START{🔄 遍历单词数据}

    LOOP_START -->|有数据| SCORE_STAT[📊 评分分布统计]
    LOOP_START -->|无数据| ANALYSIS_COMPLETE[✅ 分析完成]

    SCORE_STAT --> FREQ_STAT[📊 频率分布统计]
    FREQ_STAT --> POS_STAT[📊 词性分布统计]
    POS_STAT --> LENGTH_STAT[📊 单词长度统计]
    LENGTH_STAT --> VERSION_STAT[📊 内容版本统计]
    VERSION_STAT --> ISSUE_DETECT[🔍 问题类型检测]

    ISSUE_DETECT --> COMMENT_ANALYSIS[💬 评论关键词分析]
    COMMENT_ANALYSIS --> CATEGORIZE{🎯 问题分类}

    CATEGORIZE -->|心语问题| ISSUE_XINYU[📝 心语部分需要改进]
    CATEGORIZE -->|例句问题| ISSUE_EXAMPLE[📝 例句需要改进]
    CATEGORIZE -->|定义问题| ISSUE_DEFINITION[📝 定义需要改进]
    CATEGORIZE -->|搭配问题| ISSUE_COLLOCATION[📝 搭配需要改进]
    CATEGORIZE -->|深度问题| ISSUE_DEPTH[📝 内容深度不足]
    CATEGORIZE -->|生动性问题| ISSUE_VIVID[📝 内容不够生动]
    CATEGORIZE -->|准确性问题| ISSUE_ACCURACY[📝 准确性需要提升]
    CATEGORIZE -->|其他问题| ISSUE_OTHER[📝 其他质量问题]

    ISSUE_XINYU --> NEXT_WORD[➡️ 处理下一个单词]
    ISSUE_EXAMPLE --> NEXT_WORD
    ISSUE_DEFINITION --> NEXT_WORD
    ISSUE_COLLOCATION --> NEXT_WORD
    ISSUE_DEPTH --> NEXT_WORD
    ISSUE_VIVID --> NEXT_WORD
    ISSUE_ACCURACY --> NEXT_WORD
    ISSUE_OTHER --> NEXT_WORD

    NEXT_WORD --> LOOP_START

    ANALYSIS_COMPLETE --> SCORE_GROUP[🎯 按分数分组分析]
    SCORE_GROUP --> QUALITY_FEATURES[📋 生成质量特征]
    QUALITY_FEATURES --> REMOVAL_ANALYSIS[🗑️ 移除候选分析]
    REMOVAL_ANALYSIS --> ANALYSIS_RESULT[📊 返回分析结果]

    %% 样式定义
    classDef start fill:#E8F5E8,stroke:#000000,stroke-width:3px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef statistics fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef issues fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef result fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class START_ANALYSIS,ANALYSIS_COMPLETE start
    class INIT_ANALYSIS,COMMENT_ANALYSIS,NEXT_WORD,SCORE_GROUP,QUALITY_FEATURES,REMOVAL_ANALYSIS process
    class LOOP_START,CATEGORIZE decision
    class SCORE_STAT,FREQ_STAT,POS_STAT,LENGTH_STAT,VERSION_STAT,ISSUE_DETECT statistics
    class ISSUE_XINYU,ISSUE_EXAMPLE,ISSUE_DEFINITION,ISSUE_COLLOCATION,ISSUE_DEPTH,ISSUE_VIVID,ISSUE_ACCURACY,ISSUE_OTHER issues
    class ANALYSIS_RESULT result
```

## 🔍 问题类型检测机制

```mermaid
flowchart TD
    COMMENT_INPUT[💬 AI审核评论] --> NORMALIZE[🔧 转换为小写]

    NORMALIZE --> KEYWORD_CHECK{🔍 关键词检测}

    KEYWORD_CHECK -->|包含"心语"或"xinyu"| XINYU_ISSUE[📝 心语部分需要改进]
    KEYWORD_CHECK -->|包含"例句"或"example"| EXAMPLE_ISSUE[📝 例句需要改进]
    KEYWORD_CHECK -->|包含"定义"或"definition"| DEFINITION_ISSUE[📝 定义需要改进]
    KEYWORD_CHECK -->|包含"搭配"或"collocation"| COLLOCATION_ISSUE[📝 搭配需要改进]
    KEYWORD_CHECK -->|包含"深度"或"depth"| DEPTH_ISSUE[📝 内容深度不足]
    KEYWORD_CHECK -->|包含"生动"或"vivid"| VIVID_ISSUE[📝 内容不够生动]
    KEYWORD_CHECK -->|包含"准确"或"accuracy"| ACCURACY_ISSUE[📝 准确性需要提升]
    KEYWORD_CHECK -->|其他情况| OTHER_ISSUE[📝 其他质量问题]

    XINYU_ISSUE --> COUNT_UPDATE[📊 更新问题统计]
    EXAMPLE_ISSUE --> COUNT_UPDATE
    DEFINITION_ISSUE --> COUNT_UPDATE
    COLLOCATION_ISSUE --> COUNT_UPDATE
    DEPTH_ISSUE --> COUNT_UPDATE
    VIVID_ISSUE --> COUNT_UPDATE
    ACCURACY_ISSUE --> COUNT_UPDATE
    OTHER_ISSUE --> COUNT_UPDATE

    COUNT_UPDATE --> ANALYSIS_CONTINUE[➡️ 继续分析]

    %% 样式定义
    classDef input fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef process fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef issues fill:#FFE6E6,stroke:#000000,stroke-width:2px,color:#000000
    classDef output fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class COMMENT_INPUT input
    class NORMALIZE,COUNT_UPDATE,ANALYSIS_CONTINUE process
    class KEYWORD_CHECK decision
    class XINYU_ISSUE,EXAMPLE_ISSUE,DEFINITION_ISSUE,COLLOCATION_ISSUE,DEPTH_ISSUE,VIVID_ISSUE,ACCURACY_ISSUE,OTHER_ISSUE issues
```

## 📊 分析结果数据结构

### 完整分析结果示例
```json
{
  "total_count": 1250,
  "score_distribution": {
    "0分": 45,
    "1分": 123,
    "2分": 234,
    "3分": 345,
    "4分": 267,
    "5分": 156,
    "6分": 80
  },
  "frequency_distribution": {
    "high": 234,
    "medium": 567,
    "low": 449
  },
  "parts_of_speech_distribution": {
    "noun": 456,
    "verb": 345,
    "adjective": 234,
    "adverb": 123,
    "other": 92
  },
  "common_issues": {
    "定义需要改进": 345,
    "例句需要改进": 234,
    "心语部分需要改进": 123,
    "内容深度不足": 89,
    "其他质量问题": 459
  },
  "score_quality_analysis": {
    "0分": {
      "count": 45,
      "percentage": 3.6,
      "sample_words": ["problematic1", "error2", "invalid3"],
      "common_problems": ["拼写错误", "敏感内容"]
    },
    "4分": {
      "count": 267,
      "percentage": 21.4,
      "sample_words": ["serendipity", "ephemeral", "ubiquitous"],
      "common_problems": ["定义准确性问题", "例句质量问题"]
    }
  },
  "removal_candidates": {
    "0分及以下": 45,
    "1分及以下": 168,
    "2分及以下": 402,
    "3分及以下": 747
  }
}
```

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph USER_LAYER ["👤 用户层"]
        CLI[🖥️ 命令行接口]
        PARAMS[📝 参数配置]
    end

    subgraph MAIN_CLASS ["🏗️ 主处理类"]
        EXTRACTOR[📊 AuditedWordsExtractor]

        subgraph CORE_METHODS ["⚙️ 核心方法"]
            VALIDATOR[✅ validate_database]
            EXTRACTOR_METHOD[📊 extract_low_score_words]
            ANALYZER[📈 analyze_low_score_words]
            CSV_SAVER[💾 save_csv_report]
            MD_GENERATOR[📄 generate_markdown_report]
        end
    end

    subgraph DATA_LAYER ["📊 数据处理层"]
        JSON_PARSER[🔧 JSON解析器]
        ISSUE_DETECTOR[🔍 问题检测器]
        STATS_CALCULATOR[📊 统计计算器]
    end

    subgraph EXTERNAL ["🌐 外部系统"]
        DATABASE[(🗄️ SQLite数据库)]
        CSV_FILE[📄 CSV报告文件]
        MD_FILE[📋 Markdown报告文件]
        REPORTS_DIR[📁 报告目录]
    end

    %% 连接关系
    CLI --> EXTRACTOR
    PARAMS --> EXTRACTOR

    EXTRACTOR --> VALIDATOR
    EXTRACTOR --> EXTRACTOR_METHOD
    EXTRACTOR --> ANALYZER
    EXTRACTOR --> CSV_SAVER
    EXTRACTOR --> MD_GENERATOR

    EXTRACTOR_METHOD --> JSON_PARSER
    ANALYZER --> ISSUE_DETECTOR
    ANALYZER --> STATS_CALCULATOR

    VALIDATOR --> DATABASE
    EXTRACTOR_METHOD --> DATABASE
    CSV_SAVER --> CSV_FILE
    MD_GENERATOR --> MD_FILE
    CSV_FILE --> REPORTS_DIR
    MD_FILE --> REPORTS_DIR

    %% 样式定义
    classDef user fill:#E8F5E8,stroke:#000000,stroke-width:2px,color:#000000
    classDef main fill:#FFF2CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef core fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef data fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef external fill:#F0E6FF,stroke:#000000,stroke-width:2px,color:#000000

    class CLI,PARAMS user
    class EXTRACTOR main
    class VALIDATOR,EXTRACTOR_METHOD,ANALYZER,CSV_SAVER,MD_GENERATOR core
    class JSON_PARSER,ISSUE_DETECTOR,STATS_CALCULATOR data
    class DATABASE,CSV_FILE,MD_FILE,REPORTS_DIR external
```

## 🔧 关键技术要点详解

### 1. 📊 智能评分阈值处理

**动态查询策略：**
```python
# 评分阈值逻辑
if max_score >= 10:
    # 提取所有已审核单词
    query = "SELECT * FROM words_for_publish WHERE aiAuditScore IS NOT NULL"
    description = "所有已审核单词"
else:
    # 提取指定分数范围单词
    query = "SELECT * FROM words_for_publish WHERE aiAuditScore <= ?"
    description = f"0-{max_score}分单词"
```

**阈值设计原理：**
- 🎯 **10分阈值**: 当max_score>=10时，提取所有已审核单词进行全面分析
- 🎯 **范围筛选**: 当max_score<10时，只提取低分单词进行针对性分析
- 🎯 **默认值6分**: 基于经验设定，6分以下通常需要重点关注

### 2. 🔧 JSON解析和数据提取

**安全解析机制：**
```python
def extract_core_definition(content_json_str):
    try:
        content_json = json.loads(content_json_str)
        return content_json.get('content', {}).get('coreDefinition', '未找到定义')
    except json.JSONDecodeError:
        return '解析失败'
    except Exception:
        return '提取失败'
```

**数据完整性保障：**
- ✅ **异常处理**: 完整的JSON解析异常捕获
- ✅ **默认值**: 解析失败时提供有意义的默认值
- ✅ **数据验证**: 确保提取的字段存在且有效
- ✅ **编码处理**: 正确处理UTF-8编码的中文内容

### 3. 🔍 智能问题类型检测

**关键词匹配算法：**
```python
def detect_issue_types(comment):
    issues = []
    comment_lower = comment.lower()

    # 多语言关键词检测
    if '心语' in comment or 'xinyu' in comment_lower:
        issues.append('心语部分需要改进')
    if '例句' in comment or 'example' in comment_lower:
        issues.append('例句需要改进')
    if '定义' in comment or 'definition' in comment_lower:
        issues.append('定义需要改进')

    return issues if issues else ['其他质量问题']
```

**检测特色：**
- 🔍 **中英文支持**: 同时检测中文和英文关键词
- 🔍 **多问题识别**: 一个评论可能包含多种问题类型
- 🔍 **智能分类**: 基于关键词自动分类问题类型
- 🔍 **兜底机制**: 无法分类时归为"其他质量问题"

### 4. 📈 多维度数据分析

**分析维度：**
- 📊 **评分分布**: 0-10分的详细分布统计
- 📊 **频率分布**: high/medium/low频率单词分布
- 📊 **词性分布**: noun/verb/adjective等词性统计
- 📊 **长度分布**: 单词字符长度统计
- 📊 **问题分布**: 各类质量问题的数量统计

**质量特征分析：**
```python
def analyze_score_quality(words_by_score):
    for score, words in words_by_score.items():
        analysis = {
            'count': len(words),
            'percentage': len(words) / total_count * 100,
            'sample_words': words[:3],  # 前3个样本
            'common_problems': extract_common_problems(words)
        }
```

## 📊 输出文件格式详解

### CSV文件结构
```csv
id,word,coreDefinition,aiAuditScore,aiAuditComment,frequency,partsOfSpeech
12345,serendipity,"The occurrence of events by chance",4,"定义需要更详细",low,noun
12346,ephemeral,"Lasting for a very short time",3,"例句需要改进",medium,adjective
```

**字段说明：**
- `id`: 单词在数据库中的唯一标识
- `word`: 单词本身
- `coreDefinition`: 从contentJson中提取的核心定义
- `aiAuditScore`: AI审核评分（0-10分）
- `aiAuditComment`: AI审核评论
- `frequency`: 单词使用频率（high/medium/low）
- `partsOfSpeech`: 词性信息

### Markdown报告结构
```markdown
# AI审核单词分析报告 (0-6分)

## 📊 基本统计
- 生成时间: 2025-07-13 14:30:22
- 分析范围: 0-6分的单词
- 审核单词总数: 1,250

## 🎯 移除阈值分析
- 移除0分及以下: 45 个单词 (3.6%)
- 移除1分及以下: 168 个单词 (13.4%)

## 📈 详细分析
### 📊 评分分布
- 0分: 45 个单词 (3.6%)
- 1分: 123 个单词 (9.8%)

### 🔤 频率分布
- high: 234 个单词 (18.7%)
- medium: 567 个单词 (45.4%)
```

## 📈 性能特征分析

### 处理能力
- **数据量**: 支持处理数万个单词的分析
- **内存优化**: 流式处理，避免一次性加载所有数据
- **查询优化**: 基于索引的高效SQL查询

### 分析精度
- **多维统计**: 5个主要维度的详细分析
- **问题识别**: 8种常见问题类型的自动检测
- **质量评估**: 按分数段的质量特征分析

### 输出质量
- **数据完整性**: 包含coreDefinition字段便于深度分析
- **格式多样性**: CSV数据文件 + Markdown分析报告
- **可读性**: 清晰的统计图表和百分比展示

## 🎯 使用场景

### 1. 质量评估
- 了解整体内容质量分布
- 识别需要重点改进的分数段
- 分析常见质量问题类型

### 2. 数据清理
- 识别需要移除的低质量单词
- 为批量删除操作提供数据支持
- 评估不同移除阈值的影响

### 3. 内容改进
- 针对性改进特定问题类型
- 为内容重新生成提供参考
- 跟踪质量改进效果

---
*文档生成时间: 2025-07-13*
*版本: v4.0 - AI审核单词数据提取器详细工作原理*
```
