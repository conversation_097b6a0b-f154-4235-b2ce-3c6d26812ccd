# SenseWord 内容生产工作流程脚本系统

## 概述

这是SenseWord内容生产的统一工作流程脚本系统，采用手动控制的模块化设计，每个阶段都是独立的工具，支持按需调用和灵活组合。

## 🎯 设计理念

### 手动控制 vs 自动编排
- **手动控制**：每个workflow子目录都是独立的工具，通过命令行参数调用
- **自主决策**：人类根据当前需要选择使用哪个workflow
- **清晰文档**：每个workflow都有详细的README，说明用法、输入输出
- **关注点分离**：
  - `workflow/` = 脚本和算法层（语言无关）
  - `content-assets/` = 数据资产层（按语言分层）

## 📁 当前架构概览

```
senseword-content-factory/
├── workflow/                           # 🛠️ 脚本和算法层（语言无关）
│   ├── 01-单词提取/                    # 从词典中提取单词数据
│   ├── 02-文本过滤/                    # 基础文本过滤和质量检查
│   ├── 03-AI批处理筛选/                # 使用Vertex AI进行智能筛选
│   ├── 04-数据库初始化/                # 数据库架构创建和数据导入
│   ├── 05-内容生成/                    # AI内容生成和批处理
│   ├── 06-内容审核/                    # AI内容审核和自动修复
│   ├── 07-TTS标记/                     # TTS ID生成和内容标记
│   ├── 08-音频生成/                    # 音频资产生成和管理
│   ├── 09-状态管理/                    # 看板式状态管理
│   ├── Google_Cloud_Platfrom/          # 通用GCP批处理工具
│   ├── Prompts/                        # 全局提示词库
│   ├── utils/                          # 公共工具类和函数库
│   └── config/                         # 全局配置文件
└── content-assets/                     # 📊 数据资产层（按语言分层）
    └── 01_en/                          # 🇺🇸 学习语言：英语
        ├── 00_new/                     # 🆕 新语言教学模板
        ├── 01_zh/                      # 🇨🇳 教学语言：中文
        ├── 02_es/                      # 🇪🇸 教学语言：西班牙语
        ├── 03_ja/                      # 🇯🇵 教学语言：日语
        └── 04_pt/                      # 🇵🇹 教学语言：葡萄牙语
```

## 🏗️ 简化目录结构

每个workflow阶段都遵循简洁的目录结构：

```
{阶段编号}-{阶段名称}/
├── README.md                    # 📖 完整使用指南（核心文档）
├── scripts/                     # 🛠️ 脚本文件目录
│   ├── {功能模块}/              # 按功能分组的脚本
│   └── main_script.py           # 主要脚本文件
└── prompts/                     # 📝 提示词文件（AI相关阶段）
    └── prompt_template.md
```

## 📖 README驱动的设计理念

### 核心原则
- **� README是唯一的使用指南**: 所有使用方法、参数说明、示例都在README中
- **🎯 手动路径指定**: 通过命令行参数手动指定输入输出路径
- **🔧 脚本自包含**: 脚本内置合理默认值，无需外部配置文件
- **💡 示例驱动**: 通过丰富的使用示例指导人类和AI使用

### README文档标准

每个阶段的README.md应包含：

#### 1. **概述和功能说明**
- 阶段的核心功能
- 输入输出数据类型
- 处理流程说明

#### 2. **快速开始**
```bash
# 最简单的使用方式
python script_name.py --input input_file.json --output output_file.txt
```

#### 3. **完整参数说明**
| 参数 | 必需 | 默认值 | 说明 | 示例 |
|------|------|--------|------|------|
| `--input` | ✅ | - | 输入文件路径 | `data/words.json` |
| `--output` | ✅ | - | 输出文件路径 | `results/filtered.txt` |

#### 4. **输入输出格式示例**
```json
// 输入格式
{"words": ["example", "sample"]}

// 输出格式
{"filtered_words": ["example"], "count": 1}
```

#### 5. **常见使用场景**
- 典型的数据处理场景
- 不同参数组合的效果
- 故障排除指南

## � 各阶段详细分析

### 🔍 **阶段01: 单词提取** (`01-单词提取/`)

#### 目录结构
```
01-单词提取/
├── README.md                    # 📖 完整使用指南
└── scripts/
    └── dict_word_extractor/     # 词典提取模块
        ├── extract_words.py     # 主提取脚本
        ├── quick_test.py        # 快速测试脚本
        └── run.py              # 交互式界面脚本
```

#### 核心功能
- **词典数据提取**: 从多种格式的词典文件中提取单词
- **格式标准化**: 统一输出格式，为后续处理做准备
- **质量验证**: 基础的数据完整性检查

#### 使用方式
```bash
cd workflow/01-单词提取/scripts/dict_word_extractor/
python extract_words.py --input /path/to/dictionary.json --output /path/to/words.txt
```

---

### 🔍 **阶段02: 文本过滤** (`02-文本过滤/`)

#### 目录结构
```
02-文本过滤/
├── README.md                    # 使用说明
├── config/                      # 配置文件（待完善）
├── scripts/
│   ├── word_filtering/          # 单词过滤模块
│   │   ├── word_filter.py       # 基础过滤器
│   │   ├── strict_word_filter.py # 严格过滤器
│   │   ├── ultra_fine_filter.py # 超精细过滤器
│   │   ├── run_filter.py        # 过滤器执行接口
│   │   └── *_results/          # � 数据目录（应迁移）
│   └── seed_tasks.py           # 种子任务脚本
```

#### 核心功能
- **多级过滤**: 基础、严格、超精细三级过滤系统
- **规则引擎**: 可配置的过滤规则和条件
- **质量控制**: 确保单词列表的质量和一致性

#### 配置需求
- 过滤规则配置文件
- 黑名单和白名单管理
- 质量阈值设定

---

### 🔍 **阶段03: AI批处理筛选** (`03-AI批处理筛选/`)

#### 目录结构
```
03-AI批处理筛选/
├── README.md                    # 使用说明
├── config/                      # 配置文件（待完善）
├── scripts/
│   └── word_priority_filtering/ # 单词优先级过滤模块
│       ├── 01_direct_sqlite_to_jsonl.py      # 数据库到JSONL转换
│       ├── 02_generate_vertex_ai_batch.py    # 生成Vertex AI批处理
│       ├── 03_process_batch_results.py       # 处理批处理结果
│       ├── 04_update_database_with_ai_results.py # 更新数据库
│       └── 05_cleanup_anomalous_words.py     # 清理异常单词
└── prompts/                     # AI提示词（待完善）
```

#### 核心功能
- **智能筛选**: 使用Vertex AI进行单词质量评估
- **批处理管理**: 高效的大规模AI处理流程
- **结果集成**: 将AI评估结果回写到数据库
- **异常处理**: 识别和清理问题数据

#### 配置需求
- Vertex AI服务配置
- 批处理任务参数
- 同心圆筛选系统配置
- 质量评分阈值设定

---

## 🌐 通用组件

### 📁 **Google_Cloud_Platform/** - 通用GCP工具
```
Google_Cloud_Platfrom/
├── 01-Scripts/                  # 混合架构批处理脚本
│   ├── 01_submit_batch_hybrid.py    # 批处理任务提交
│   ├── 02_curl_monitor.sh           # 任务状态监控
│   ├── 03_batch_results_extractor.py # 结果提取
│   └── 04_download_results_gsutil.py # 结果下载
└── docs/                        # 技术文档
```

**特点**:
- ✅ **混合架构**: Python验证 + gsutil上传 + curl API
- ✅ **通用性**: 所有workflow都可以使用
- ✅ **稳定性**: 经过验证的批处理流程

### 📝 **Prompts/** - 全局提示词库
```
Prompts/
├── 01_单词筛选过滤同心圆系统.md    # 同心圆筛选提示词
├── 02_senseword-v10.0-Prompt.md    # 内容生成提示词
└── 03_SenseWord AI内容审核官_v4.md  # 内容审核提示词
```

**用途**:
- 🎯 **标准化**: 统一的AI提示词管理
- 🔄 **版本控制**: 提示词演进历史
- 📚 **复用性**: 多个阶段共享提示词

### 🛠️ **utils/** - 公共工具库
```
utils/
├── common/                      # 基础工具
│   ├── 2fa_manager.py          # 双因素认证管理
│   └── 2fa_accounts.json       # 认证配置
├── batch_processing/            # 批处理工具
├── monitoring/                  # 监控工具
└── recovery/                    # 错误恢复工具
```

**功能**:
- 🔧 **基础设施**: 数据库、文件、日志操作
- 📊 **监控**: 性能跟踪和错误检测
- 🔄 **恢复**: 错误恢复和数据修复

---

## 🚀 使用指南

### 手动执行示例

#### 1. 单词提取
```bash
cd workflow/01-单词提取/scripts/dict_word_extractor/
python extract_words.py --input /path/to/dictionary.json --output /path/to/words.txt
```

#### 2. 文本过滤
```bash
cd workflow/02-文本过滤/scripts/word_filtering/
python word_filter.py --input /path/to/words.txt --output /path/to/filtered.txt
```

#### 3. AI批处理筛选
```bash
cd workflow/03-AI批处理筛选/scripts/word_priority_filtering/
python 01_direct_sqlite_to_jsonl.py --db-path /path/to/database.db --output batch_input.jsonl
python 02_generate_vertex_ai_batch.py --input batch_input.jsonl --job-name word-filtering-001
```

### README驱动的使用方式

#### 查看具体使用方法
```bash
# 每个阶段都有详细的README文档
cat workflow/01-单词提取/README.md
cat workflow/02-文本过滤/README.md
cat workflow/03-AI批处理筛选/README.md
```

#### 获取脚本帮助信息
```bash
# 大多数脚本都支持 --help 参数
python extract_words.py --help
python word_filter.py --help
```

### 数据路径管理

#### 标准数据路径
```bash
# 输入数据路径
INPUT_PATH="content-assets/01_en/01_zh/txt/word_lists/"

# 输出数据路径
OUTPUT_PATH="content-assets/01_en/01_zh/csv/export_snapshots/"

# 配置数据路径
CONFIG_PATH="workflow/01-单词提取/config/"
```

#### 跨阶段数据传递
```bash
# 阶段01输出 → 阶段02输入
STAGE01_OUTPUT="content-assets/01_en/01_zh/txt/word_lists/extracted_words.txt"
STAGE02_INPUT="$STAGE01_OUTPUT"

# 执行阶段02
cd workflow/02-文本过滤/scripts/
python word_filter.py --input "$STAGE02_INPUT" --output filtered_words.txt
```

## 📋 最佳实践

### 1. README驱动开发
- ✅ **详细文档**: 每个阶段的README包含完整使用指南
- ✅ **示例优先**: 通过实际示例展示脚本用法
- ✅ **参数说明**: 清晰的参数表格和格式说明
- ✅ **故障排除**: 记录常见问题和解决方案

### 2. 数据管理
- ✅ **手动路径**: 通过命令行参数明确指定输入输出路径
- ✅ **备份策略**: 重要数据处理前创建备份
- ✅ **验证机制**: 每个阶段都要验证输入数据的完整性
- ✅ **清理策略**: 定期清理临时文件和中间结果

### 3. 脚本设计
- ✅ **自包含**: 脚本内置合理默认值，减少外部依赖
- ✅ **参数验证**: 脚本启动时验证所有必需参数
- ✅ **错误处理**: 实现完善的错误处理和日志记录
- ✅ **进度反馈**: 长时间运行的脚本提供进度信息

### 4. 使用方式
- ✅ **命令行优先**: 优先使用命令行参数而非配置文件
- ✅ **帮助信息**: 每个脚本都支持 --help 参数
- ✅ **示例驱动**: 通过README中的示例学习使用方法
- ✅ **路径明确**: 明确指定数据文件的完整路径

---

## 🔧 维护和扩展

### 添加新的workflow阶段

#### 1. 创建目录结构
```bash
# 创建新阶段目录
mkdir -p workflow/10-新阶段/{scripts,prompts}

# 创建README模板
cat > workflow/10-新阶段/README.md << EOF
# 阶段10: 新阶段

## 概述
[阶段描述和核心功能]

## 快速开始
\`\`\`bash
python script_name.py --input input_file --output output_file
\`\`\`

## 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| --input | ✅ | - | 输入文件路径 |
| --output | ✅ | - | 输出文件路径 |

## 使用示例
[详细的使用示例]

## 故障排除
[常见问题和解决方案]
EOF
```

#### 2. 实现标准接口
```python
# scripts/main_stage10.py
import argparse
import json
import logging

def main():
    parser = argparse.ArgumentParser(description='新阶段处理脚本')
    parser.add_argument('--input', required=True, help='输入数据路径')
    parser.add_argument('--output', required=True, help='输出数据路径')
    parser.add_argument('--batch-size', type=int, default=1000, help='批处理大小')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level)

    # 处理逻辑
    process_data(args.input, args.output, args.batch_size)

if __name__ == '__main__':
    main()
```

#### 3. README驱动的文档
```markdown
# 阶段10: 新阶段

## 快速开始
\`\`\`bash
python main_stage10.py --input data.json --output result.json
\`\`\`

## 参数说明
| 参数 | 必需 | 默认值 | 说明 |
|------|------|--------|------|
| --input | ✅ | - | 输入文件路径 |
| --output | ✅ | - | 输出文件路径 |
| --batch-size | ❌ | 1000 | 批处理大小 |
| --verbose | ❌ | False | 详细输出模式 |

## 使用示例
\`\`\`bash
# 基础使用
python main_stage10.py --input words.txt --output processed.json

# 调整批处理大小
python main_stage10.py --input large_file.json --output result.json --batch-size 5000

# 详细输出模式
python main_stage10.py --input data.json --output result.json --verbose
\`\`\`
```

### 脚本标准化

#### 命令行接口标准
```python
# 标准的命令行参数结构
parser = argparse.ArgumentParser(description='脚本描述')

# 必需参数
parser.add_argument('--input', required=True, help='输入文件路径')
parser.add_argument('--output', required=True, help='输出文件路径')

# 可选参数（带默认值）
parser.add_argument('--batch-size', type=int, default=1000, help='批处理大小')
parser.add_argument('--format', choices=['json', 'txt', 'csv'], default='json', help='输出格式')
parser.add_argument('--verbose', action='store_true', help='详细输出')

# 帮助信息
parser.add_argument('--help', action='help', help='显示帮助信息')
```

#### 错误处理标准
```python
import sys
import logging

def setup_logging(verbose=False):
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def validate_input_file(filepath):
    if not os.path.exists(filepath):
        logging.error(f"输入文件不存在: {filepath}")
        sys.exit(1)

    if not os.access(filepath, os.R_OK):
        logging.error(f"无法读取输入文件: {filepath}")
        sys.exit(1)
```

### 监控和日志

#### 统一日志格式
```python
import logging
import json
from datetime import datetime

def setup_logging(stage_name, log_level="INFO"):
    """设置统一的日志格式"""
    logging.basicConfig(
        level=getattr(logging, log_level),
        format=f'[{datetime.now().isoformat()}] [{stage_name}] %(levelname)s: %(message)s',
        handlers=[
            logging.FileHandler(f'logs/{stage_name}_{datetime.now().strftime("%Y%m%d")}.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(stage_name)
```

#### 性能监控
```python
import time
import functools

def monitor_performance(func):
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        logging.info(f"Function {func.__name__} executed in {end_time - start_time:.2f} seconds")
        return result
    return wrapper
```

## 📊 系统状态

### 当前完成度
- ✅ **架构设计**: 完成脚本与数据分离
- ✅ **目录结构**: 简化的workflow目录结构（移除config）
- ✅ **脚本迁移**: 核心脚本已迁移到workflow目录
- ✅ **文档系统**: README驱动的文档体系
- ✅ **设计理念**: 手动控制、README驱动的设计
- 🔄 **README完善**: 各阶段README文档待更新
- 🔄 **接口统一**: 脚本命令行接口待标准化
- 🔄 **使用验证**: 实际脚本功能与文档对齐

### 下一步计划
1. **更新各阶段README**: 确保文档与实际脚本功能一致
2. **验证脚本功能**: 测试每个脚本的实际工作状态
3. **标准化接口**: 统一命令行参数格式和错误处理
4. **完善文档**: 添加详细的使用示例和故障排除
5. **功能测试**: 端到端的工作流程验证

---

**维护信息**:
- **最后更新**: 2025-07-13
- **文档版本**: v3.0 (README驱动)
- **架构版本**: 手动控制、简化架构
- **设计理念**: README驱动、配置文件移除
- **维护者**: SenseWord开发团队

这个工作流程系统为SenseWord内容生产提供了简洁、实用的脚本管理架构，通过README驱动的设计确保易用性和可维护性，支持灵活的手动控制和路径指定。

---

### 🔍 **阶段04: 数据库初始化** (`04-数据库初始化/scripts/database_init/`)

#### 核心脚本 ⭐
- **`01_initialize_database.py`** - 数据库初始化
  - **功能**: 创建数据库表结构和初始化数据
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 数据库架构设置

- **`02_database_manager.py`** - 数据库管理器
  - **功能**: 数据库操作的统一接口
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 数据库操作封装

#### 维护脚本 🔧
- **`03_update_priority_scores.py`** - 更新优先级分数
  - **功能**: 更新单词的优先级评分
  - **状态**: 🟡 维护脚本，可选保留

- **`analyze_data_structure.py`** - 数据结构分析
  - **功能**: 分析数据库结构和数据质量
  - **状态**: 🟡 分析脚本，可选保留

---

### 🔍 **阶段05: 内容生成** (`05-内容生成/scripts/`)

#### 批处理任务队列模块 (`batch_task_queue/`)

##### 核心生成脚本 ⭐
- **`01_batch_processing_generator.py`** - 批处理任务生成器
  - **功能**: 生成AI内容生成的批处理任务
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 批处理任务创建

- **`02_extract_word_analysis_results.py`** - 单词解析结果提取
  - **功能**: 从批处理结果中提取单词解析数据
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 结果数据提取

- **`03_extract_example_sentences_results.py`** - 例句结果提取
  - **功能**: 从批处理结果中提取例句数据
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 例句数据提取

##### 数据处理脚本 🔄
- **`04_merge_extracted_results.py`** - 合并提取结果
  - **功能**: 合并不同类型的提取结果
  - **状态**: 🟢 核心脚本，保留

- **`05_fix_data_consistency.py`** - 修复数据一致性
  - **功能**: 修复数据中的一致性问题
  - **状态**: 🟡 维护脚本，可选保留

- **`06_generate_targeted_batch.py`** - 生成目标批处理
  - **功能**: 为特定单词生成批处理任务
  - **状态**: 🟢 核心脚本，保留

##### 网络分析脚本 🕸️
- **`07_extract_word_network_optimized.py`** - 单词网络提取（优化版）
  - **功能**: 提取单词间的关联网络
  - **状态**: 🟢 核心脚本，保留
  - **用途**: 关联分析

- **`08_analyze_word_frequency.py`** - 单词频率分析
  - **功能**: 分析单词使用频率
  - **状态**: 🟡 分析脚本，可选保留

- **`09_align_common_words.py`** - 对齐常用单词
  - **功能**: 对齐和标准化常用单词
  - **状态**: 🟡 维护脚本，可选保留

##### 同义词处理脚本 📝
- **`10_analyze_synonyms_standardization.py`** - 同义词标准化分析
  - **功能**: 分析同义词标准化需求
  - **状态**: 🟡 分析脚本，可选保留

- **`11_precise_synonyms_standardization.py`** - 精确同义词标准化
  - **功能**: 执行精确的同义词标准化
  - **状态**: 🟢 核心脚本，保留

##### 质量检测脚本 🔍
- **`12_data_quality_detector.py`** - 数据质量检测器
  - **功能**: 检测数据质量问题
  - **状态**: 🟡 质量检测，可选保留

- **`13_example_sentences_quality_detector.py`** - 例句质量检测器
  - **功能**: 检测例句质量问题
  - **状态**: 🟡 质量检测，可选保留

##### 数据修复脚本 🔧
- **`14_remove_problematic_words.py`** - 移除问题单词
  - **功能**: 移除有问题的单词记录
  - **状态**: 🟡 维护脚本，可选保留

- **`15_fix_contractions.py`** - 修复缩写
  - **功能**: 修复英语缩写形式
  - **状态**: 🟡 维护脚本，可选保留

- **`16_file_integrity_checker.py`** - 文件完整性检查器
  - **功能**: 检查文件完整性
  - **状态**: 🟡 维护脚本，可选保留

#### 内容合并模块 (`content_merging/`)
- **状态**: 🟡 需要进一步分析具体脚本内容

#### 数据目录 📁
- **`batch_task_word_list_raw/`**, **`frequency_analysis/`**, **`related_content_optimized/`**, **`vertex/`**
  - **状态**: 🔴 应移动到content-assets

---

### 🔍 **阶段06: 内容审核** (`06-内容审核/scripts/ai_audit/`)

#### 核心审核脚本 ⭐
- **`03_update_audit_results_to_db.py`** - 更新审核结果到数据库
  - **功能**: 将AI审核结果更新到数据库
  - **状态**: 🟢 核心脚本，保留

- **`05_extract_words.py`** - 提取单词
  - **功能**: 从数据库中提取需要审核的单词
  - **状态**: 🟢 核心脚本，保留

- **`08_apply_edit_instructions.py`** - 应用编辑指令
  - **功能**: 根据AI审核结果应用编辑修改
  - **状态**: 🟢 核心脚本，保留

- **`09_generate_v4_audit_batch.py`** - 生成V4审核批处理
  - **功能**: 生成新版本的审核批处理任务
  - **状态**: 🟢 核心脚本，保留

#### 数据处理脚本 🔄
- **`04_extract_words_to_remove.py`** - 提取待删除单词
  - **功能**: 提取需要删除的单词列表
  - **状态**: 🟡 维护脚本，可选保留

- **`06_remove_problematic_words.py`** - 移除问题单词
  - **功能**: 从数据库中移除有问题的单词
  - **状态**: 🟡 维护脚本，可选保留

- **`07_extract_words_to_regenerate.py`** - 提取待重新生成单词
  - **功能**: 提取需要重新生成内容的单词
  - **状态**: 🟡 维护脚本，可选保留

#### 验证脚本 ✅
- **`010_contentjson_structure_validator.py`** - ContentJSON结构验证器
  - **功能**: 验证内容JSON结构的正确性
  - **状态**: 🟡 验证脚本，可选保留

- **`011_add_cultural_risk_regions_field.py`** - 添加文化风险区域字段
  - **功能**: 为内容添加文化风险评估字段
  - **状态**: 🟡 特定功能，可选保留

---

### 🔍 **阶段07: TTS标记** (`07-TTS标记/scripts/tts_processing/`)

#### 核心TTS脚本 ⭐
- **`02_create_v4_database_structure.py`** - 创建V4数据库结构
  - **功能**: 创建支持TTS的数据库结构
  - **状态**: 🟢 核心脚本，保留

- **`03_migrate_v2_to_v4_content.py`** - V2到V4内容迁移
  - **功能**: 将旧版本内容迁移到新结构
  - **状态**: 🟢 核心脚本，保留

- **`07_fix_phonetic_types_v4.py`** - 修复音标类型V4
  - **功能**: 修复音标类型标准化问题
  - **状态**: 🟢 核心脚本，保留

- **`08_generate_tts_mapping_report.py`** - 生成TTS映射报告
  - **功能**: 生成TTS ID映射的详细报告
  - **状态**: 🟢 核心脚本，保留

#### 成本计算脚本 💰
- **`01_calculate_tts_cost.py`** - 计算TTS成本
  - **功能**: 计算TTS服务的预估成本
  - **状态**: 🟡 分析脚本，可选保留

#### 验证脚本 ✅
- **`04_validate_migration_integrity.py`** - 验证迁移完整性
  - **功能**: 验证数据迁移的完整性
  - **状态**: 🟡 验证脚本，可选保留

- **`06_verify_tts_consistency.py`** - 验证TTS一致性
  - **功能**: 验证TTS数据的一致性
  - **状态**: 🟡 验证脚本，可选保留

#### 可视化脚本 📊
- **`08_visualize_tts_mapping.py`** - 可视化TTS映射
  - **功能**: 可视化TTS映射关系
  - **状态**: 🟡 可视化工具，可选保留

#### 回滚脚本 ↩️
- **`05_rollback_migration.py`** - 回滚迁移
  - **功能**: 回滚数据迁移操作
  - **状态**: 🟡 应急脚本，可选保留

#### 运行脚本 🏃
- **`run_migration.py`** - 运行迁移
  - **功能**: 统一的迁移执行入口
  - **状态**: 🟡 辅助脚本，可整合

#### 工具目录 🛠️
- **`utils/`** - 工具函数
  - **状态**: 🟢 应移动到workflow/utils/

---

### 🔍 **阶段08: 音频生成**
- **状态**: ❌ 当前无脚本，需要开发

---

### 🔍 **阶段09: 状态管理**
- **状态**: ❌ 当前无脚本，需要开发

---

### 🔍 **工具类** (`utils/common/`)

#### 认证工具 🔐
- **`2fa_manager.py`** - 双因素认证管理器
  - **功能**: 管理双因素认证
  - **状态**: 🟢 工具脚本，保留

- **`2fa_accounts.json`** - 双因素认证账户配置
  - **功能**: 存储认证账户信息
  - **状态**: 🟢 配置文件，保留

---

## 📊 脚本分类统计

### 🟢 核心脚本（必须保留）- 22个
1. `01-单词提取/scripts/dict_word_extractor/extract_words.py`
2. `02-文本过滤/scripts/word_filtering/word_filter.py`
3. `02-文本过滤/scripts/word_filtering/strict_word_filter.py`
4. `02-文本过滤/scripts/word_filtering/ultra_fine_filter.py`
5. `03-AI批处理筛选/scripts/word_priority_filtering/01_direct_sqlite_to_jsonl.py`
6. `03-AI批处理筛选/scripts/word_priority_filtering/02_generate_vertex_ai_batch.py`
7. `03-AI批处理筛选/scripts/word_priority_filtering/03_process_batch_results.py`
8. `03-AI批处理筛选/scripts/word_priority_filtering/04_update_database_with_ai_results.py`
9. `04-数据库初始化/scripts/database_init/01_initialize_database.py`
10. `04-数据库初始化/scripts/database_init/02_database_manager.py`
11. `05-内容生成/scripts/batch_task_queue/01_batch_processing_generator.py`
12. `05-内容生成/scripts/batch_task_queue/02_extract_word_analysis_results.py`
13. `05-内容生成/scripts/batch_task_queue/03_extract_example_sentences_results.py`
14. `05-内容生成/scripts/batch_task_queue/04_merge_extracted_results.py`
15. `05-内容生成/scripts/batch_task_queue/06_generate_targeted_batch.py`
16. `05-内容生成/scripts/batch_task_queue/07_extract_word_network_optimized.py`
17. `05-内容生成/scripts/batch_task_queue/11_precise_synonyms_standardization.py`
18. `06-内容审核/scripts/ai_audit/03_update_audit_results_to_db.py`
19. `06-内容审核/scripts/ai_audit/05_extract_words.py`
20. `06-内容审核/scripts/ai_audit/08_apply_edit_instructions.py`
21. `06-内容审核/scripts/ai_audit/09_generate_v4_audit_batch.py`
22. `07-TTS标记/scripts/tts_processing/02_create_v4_database_structure.py`

### 🟡 可选脚本（根据需要保留）- 25个
**测试和验证脚本**:
- `01-单词提取/scripts/dict_word_extractor/quick_test.py`
- `04-数据库初始化/scripts/database_init/analyze_data_structure.py`
- `06-内容审核/scripts/ai_audit/010_contentjson_structure_validator.py`
- `07-TTS标记/scripts/tts_processing/04_validate_migration_integrity.py`
- `07-TTS标记/scripts/tts_processing/06_verify_tts_consistency.py`

**维护和修复脚本**:
- `03-AI批处理筛选/scripts/word_priority_filtering/05_cleanup_anomalous_words.py`
- `04-数据库初始化/scripts/database_init/03_update_priority_scores.py`
- `05-内容生成/scripts/batch_task_queue/05_fix_data_consistency.py`
- `05-内容生成/scripts/batch_task_queue/09_align_common_words.py`
- `05-内容生成/scripts/batch_task_queue/14_remove_problematic_words.py`
- `05-内容生成/scripts/batch_task_queue/15_fix_contractions.py`
- `06-内容审核/scripts/ai_audit/04_extract_words_to_remove.py`
- `06-内容审核/scripts/ai_audit/06_remove_problematic_words.py`
- `06-内容审核/scripts/ai_audit/07_extract_words_to_regenerate.py`

**分析和报告脚本**:
- `05-内容生成/scripts/batch_task_queue/08_analyze_word_frequency.py`
- `05-内容生成/scripts/batch_task_queue/10_analyze_synonyms_standardization.py`
- `07-TTS标记/scripts/tts_processing/01_calculate_tts_cost.py`
- `07-TTS标记/scripts/tts_processing/08_visualize_tts_mapping.py`

**辅助和界面脚本**:
- `01-单词提取/scripts/dict_word_extractor/run.py`
- `02-文本过滤/scripts/word_filtering/run_filter.py`
- `02-文本过滤/scripts/seed_tasks.py`
- `07-TTS标记/scripts/tts_processing/run_migration.py`

### 🔴 需要处理的问题 - 8个

#### 数据目录混入脚本目录
1. `01-单词提取/scripts/dict_word_extractor/output/` → 移动到content-assets
2. `01-单词提取/scripts/dict_word_extractor/test_output/` → 移动到content-assets
3. `02-文本过滤/scripts/word_filtering/first_round_results/` → 移动到content-assets
4. `02-文本过滤/scripts/word_filtering/strict_filtering_results/` → 移动到content-assets
5. `02-文本过滤/scripts/word_filtering/ultra_fine_results/` → 移动到content-assets
6. `03-AI批处理筛选/scripts/ai_vocabulary_curation/` → 包含大量数据目录，需分离
7. `05-内容生成/scripts/batch_task_queue/batch_task_word_list_raw/` → 移动到content-assets
8. `05-内容生成/scripts/batch_task_queue/frequency_analysis/` → 移动到content-assets

#### 缺失的核心脚本
1. **阶段08: 音频生成** - 完全缺失，需要开发
2. **阶段09: 状态管理** - 完全缺失，需要开发

---

## 🎯 优化建议

### 立即行动项 🚨

#### 1. 数据与脚本分离
```bash
# 移动数据目录到content-assets
mv workflow/01-单词提取/scripts/dict_word_extractor/output content-assets/en/zh/01-extracted-words/
mv workflow/02-文本过滤/scripts/word_filtering/*_results content-assets/en/zh/02-filtered-words/
# ... 其他数据目录
```

#### 2. 清理冗余脚本
- 删除明显的临时测试文件
- 合并功能重复的脚本
- 标准化脚本命名规范

#### 3. 创建缺失脚本
- 开发阶段08音频生成脚本
- 开发阶段09状态管理脚本
- 完善工具类库

### 中期优化项 📈

#### 1. 脚本标准化
- 统一命令行参数格式
- 统一错误处理机制
- 统一日志输出格式
- 统一配置文件格式

#### 2. 数据流设计
- 定义标准的输入输出格式
- 设计阶段间数据传递协议
- 实现数据验证机制
- 建立数据版本控制

#### 3. 文档完善
- 为每个核心脚本编写详细使用说明
- 创建端到端工作流程文档
- 建立故障排除指南
- 提供最佳实践指导

---

## 📋 下一步行动计划

### 第一阶段：清理和分离 (1-2天)
1. ✅ 移动所有数据目录到content-assets
2. ✅ 删除明显的临时和测试文件
3. ✅ 整理脚本目录结构

### 第二阶段：脚本优化 (3-5天)
1. 🔄 标准化核心脚本的命令行接口
2. 🔄 实现统一的配置管理
3. 🔄 添加详细的使用文档

### 第三阶段：补全缺失功能 (5-7天)
1. 🆕 开发音频生成脚本
2. 🆕 开发状态管理脚本
3. 🆕 完善工具类库

### 第四阶段：集成测试 (2-3天)
1. 🧪 端到端工作流程测试
2. 🧪 数据流验证
3. 🧪 性能优化

这个分析报告为SenseWord内容生产工作流程的优化提供了清晰的路线图，确保我们能够构建一个高效、可维护的脚本体系。

## 使用方法

### 单阶段执行
```bash
cd workflow/01-单词提取/scripts
python extract_from_dictionary.py
```

### 完整流水线执行
```bash
cd workflow/orchestrator
python main_pipeline.py --learning-lang en --teaching-lang zh
```

## 配置管理

- 全局配置: `config/global_config.json`
- 环境配置: `config/environment_config.json`
- 流水线配置: `config/pipeline_config.json`

## 监控和日志

- 流水线监控: `utils/monitoring/pipeline_monitor.py`
- 错误检测: `utils/monitoring/error_detector.py`
- 性能跟踪: `utils/monitoring/performance_tracker.py`

## 错误恢复

- 错误恢复: `utils/recovery/error_recovery.py`
- 数据修复: `utils/recovery/data_repair.py`
- 检查点管理: `utils/recovery/checkpoint_manager.py`

## 技术架构

### 混合架构优势
- **Python验证**: 数据验证和处理逻辑
- **gsutil上传**: 高效的文件上传，支持实时进度
- **curl API**: 稳定的任务提交和监控

### 数据流管理
- **阶段间传递**: 通过标准化数据目录
- **数据验证**: 每个阶段的输入输出验证
- **检查点机制**: 支持断点续传和错误恢复

### 多语言支持
- **学习语言**: 英语 (en)
- **教学语言**: 中文 (zh), 西班牙语 (es), 葡萄牙语 (pt), 日语 (ja)
- **线性处理**: 按照 zh → es → pt → ja 顺序处理

## 维护和扩展

### 添加新阶段
1. 在workflow目录下创建新的阶段目录
2. 实现标准的scripts、config、data结构
3. 更新orchestrator中的依赖关系
4. 添加相应的监控和错误处理

### 添加新语言
1. 在content-assets/en/下创建新的语言目录
2. 复制完整的9个阶段目录结构
3. 更新pipeline_config.json中的语言列表
4. 测试完整的流水线执行

## 迁移说明

本工作流程整合了以下现有脚本：
- Google Cloud Platform批处理脚本
- SQLite数据库工作流程脚本
- 批处理队列管理脚本
- 提示词和配置文件

详细的迁移文档请参考: `../legacy/README_MIGRATION.md`
