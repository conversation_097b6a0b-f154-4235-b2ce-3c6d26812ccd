# Google Cloud 服务账户创建步骤清单

## 前置准备

### 1. 确保 gcloud CLI 已安装并配置
```bash
# 检查 gcloud 版本
gcloud version

# 登录到主账户
gcloud auth login

# 设置项目
gcloud config set project [PROJECT_ID]

# 启用必要的 APIs
gcloud services enable cloudresourcemanager.googleapis.com
gcloud services enable iam.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable texttospeech.googleapis.com
gcloud services enable aiplatform.googleapis.com
gcloud services enable cloudbilling.googleapis.com
```

## 创建管理员服务账户 (Manager SA)

### 1. 创建服务账户
```bash
gcloud iam service-accounts create manager-sa \
  --display-name="Manager Service Account" \
  --description="Full administrative access service account for emergency management"
```

### 2. 分配核心管理权限
```bash
# 核心管理角色
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/owner"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/editor"
```

### 3. 分配专业管理角色
```bash
# IAM 管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/iam.serviceAccountAdmin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/iam.serviceAccountKeyAdmin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/resourcemanager.projectIamAdmin"

# 服务管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/serviceusage.serviceUsageAdmin"

# 计算资源管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/compute.admin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/compute.networkAdmin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/compute.securityAdmin"

# 容器和无服务器管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/container.admin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/cloudfunctions.admin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/run.admin"

# 数据库管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/cloudsql.admin"

# 存储管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/storage.admin"

# 网络服务管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/dns.admin"

# 监控和日志管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/monitoring.admin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/logging.admin"

# 密钥管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:manager-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/secretmanager.admin"
```

### 4. 生成密钥文件
```bash
# 创建密钥存储目录
mkdir -p ~/.config/gcloud-keys

# 生成密钥文件
gcloud iam service-accounts keys create ~/.config/gcloud-keys/manager-sa.json \
  --iam-account=manager-sa@[PROJECT_ID].iam.gserviceaccount.com
```

### 5. 配置 Billing 权限（通过 Web 控制台）
1. 访问 [Google Cloud Billing Console](https://console.cloud.google.com/billing)
2. 选择您的 Billing Account
3. 进入 "Account Management"
4. 点击 "Add Principal"
5. 添加 `manager-sa@[PROJECT_ID].iam.gserviceaccount.com`
6. 分配角色 "Billing Account Viewer"

## 创建业务服务账户 (Business SA)

### 1. 创建服务账户
```bash
gcloud iam service-accounts create business-sa \
  --display-name="Business Service Account" \
  --description="Service account for business operations with TTS, GCS, and Vertex AI capabilities"
```

### 2. 分配业务相关权限
```bash
# Vertex AI 权限（包含 Batch Prediction）
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:business-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/aiplatform.admin"

gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:business-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/aiplatform.user"

# ML 开发权限（包含 TTS）
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:business-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/ml.developer"

# GCS 存储管理
gcloud projects add-iam-policy-binding [PROJECT_ID] \
  --member="serviceAccount:business-sa@[PROJECT_ID].iam.gserviceaccount.com" \
  --role="roles/storage.admin"
```

### 3. 生成密钥文件
```bash
gcloud iam service-accounts keys create ~/.config/gcloud-keys/business-sa.json \
  --iam-account=business-sa@[PROJECT_ID].iam.gserviceaccount.com
```

## 验证配置

### 1. 验证管理员权限
```bash
# 使用管理员SA
gcloud auth activate-service-account --key-file="~/.config/gcloud-keys/manager-sa.json"

# 验证项目访问
gcloud projects describe [PROJECT_ID]

# 验证IAM权限
gcloud iam service-accounts list

# 验证计算权限
gcloud compute regions list

# 验证存储权限
gcloud storage buckets list

# 验证 billing 权限
gcloud beta billing accounts list
```

### 2. 验证业务权限
```bash
# 使用业务SA
gcloud auth activate-service-account --key-file="~/.config/gcloud-keys/business-sa.json"

# 验证 Vertex AI 权限
gcloud ai models list --region=us-central1

# 验证存储权限
gcloud storage buckets list

# 验证 ML 权限
gcloud ml models list
```

## 重要提示

1. **替换变量**: 将 `[PROJECT_ID]` 替换为您的实际项目ID
2. **密钥安全**: 密钥文件包含敏感信息，请妥善保管
3. **权限最小化**: 根据实际需求调整权限范围
4. **定期审计**: 定期检查服务账户权限和活动
5. **应急使用**: Manager SA 仅在主账户不可用时使用

## 故障排除

### 常见问题
1. **API 未启用**: 确保所有必要的 API 都已启用
2. **权限不足**: 检查当前用户是否有足够权限创建服务账户
3. **项目 ID 错误**: 确认项目 ID 正确
4. **密钥文件路径**: 确认密钥文件路径和权限正确

### 权限验证命令
```bash
# 检查当前认证状态
gcloud auth list

# 检查当前项目
gcloud config get-value project

# 检查服务账户权限
gcloud projects get-iam-policy [PROJECT_ID] --flatten="bindings[].members" \
  --filter="bindings.members:serviceAccount:*" \
  --format="table(bindings.role,bindings.members)"
```