#!/bin/bash

# Google Cloud 服务账户管理脚本
# 使用方法: ./gcloud-manager.sh [command] [options]

set -e

# 配置
KEYS_DIR="$HOME/.config/gcloud-keys"
MANAGER_KEY="$KEYS_DIR/manager-sa.json"
BUSINESS_KEY="$KEYS_DIR/business-sa.json"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 帮助信息
show_help() {
    echo -e "${BLUE}Google Cloud 服务账户管理脚本${NC}"
    echo
    echo "使用方法: $0 [命令] [选项]"
    echo
    echo "命令:"
    echo "  manager      切换到管理员服务账户"
    echo "  business     切换到业务服务账户"
    echo "  user         切换到用户账户"
    echo "  status       显示当前认证状态"
    echo "  test         测试当前服务账户权限"
    echo "  project      设置项目ID"
    echo "  resources    显示项目资源概览"
    echo "  emergency    应急切换到管理员账户"
    echo "  setup        设置 zsh 别名"
    echo "  install      安装脚本到系统路径"
    echo "  help         显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 manager           # 切换到管理员SA"
    echo "  $0 business          # 切换到业务SA"
    echo "  $0 project my-proj   # 设置项目ID"
    echo "  $0 test              # 测试当前权限"
}

# 检查文件是否存在
check_key_file() {
    local key_file=$1
    local desc=$2
    
    if [[ ! -f "$key_file" ]]; then
        echo -e "${RED}❌ 错误: $desc 密钥文件不存在: $key_file${NC}"
        echo -e "${YELLOW}请确保已正确配置服务账户密钥文件${NC}"
        exit 1
    fi
}

# 切换到管理员账户
switch_to_manager() {
    check_key_file "$MANAGER_KEY" "管理员SA"
    echo -e "${BLUE}🔄 切换到管理员服务账户...${NC}"
    gcloud auth activate-service-account --key-file="$MANAGER_KEY"
    echo -e "${GREEN}✅ 已切换到 Manager SA${NC}"
    echo -e "${YELLOW}⚠️  现在拥有完整管理权限，请谨慎操作！${NC}"
}

# 切换到业务账户
switch_to_business() {
    check_key_file "$BUSINESS_KEY" "业务SA"
    echo -e "${BLUE}🔄 切换到业务服务账户...${NC}"
    gcloud auth activate-service-account --key-file="$BUSINESS_KEY"
    echo -e "${GREEN}✅ 已切换到 Business SA${NC}"
}

# 切换到用户账户
switch_to_user() {
    echo -e "${BLUE}🔄 切换到用户账户...${NC}"
    gcloud auth login
    echo -e "${GREEN}✅ 已切换到用户账户${NC}"
}

# 显示当前状态
show_status() {
    echo -e "${BLUE}=== 当前认证状态 ===${NC}"
    gcloud auth list
    echo
    echo -e "${BLUE}当前项目:${NC} $(gcloud config get-value project 2>/dev/null || echo '未设置')"
}

# 测试权限
test_permissions() {
    echo -e "${BLUE}=== 权限测试 ===${NC}"
    
    local current_account=$(gcloud auth list --filter=status:ACTIVE --format="value(account)")
    echo -e "${BLUE}当前账户:${NC} $current_account"
    
    if [[ "$current_account" == *"manager-sa"* ]]; then
        echo -e "${BLUE}测试管理员权限...${NC}"
        echo "项目信息:"
        gcloud projects describe "$(gcloud config get-value project)" --format="value(name,projectId)" 2>/dev/null || echo "无法获取项目信息"
        echo "服务账户:"
        gcloud iam service-accounts list --format="value(email)" 2>/dev/null || echo "无法列出服务账户"
    elif [[ "$current_account" == *"business-sa"* ]]; then
        echo -e "${BLUE}测试业务权限...${NC}"
        echo "存储桶:"
        gcloud storage buckets list --format="value(name)" 2>/dev/null || echo "无存储桶或无权限"
        echo "Vertex AI 模型:"
        gcloud ai models list --region=us-central1 --format="value(name)" 2>/dev/null || echo "无模型或无权限"
    else
        echo -e "${YELLOW}当前为用户账户，跳过权限测试${NC}"
    fi
}

# 设置项目
set_project() {
    local project_id=$1
    if [[ -z "$project_id" ]]; then
        echo -e "${RED}❌ 请提供项目ID${NC}"
        echo "使用方法: $0 project <PROJECT_ID>"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 设置项目: $project_id${NC}"
    gcloud config set project "$project_id"
    echo -e "${GREEN}✅ 项目已设置为: $project_id${NC}"
}

# 显示资源概览
show_resources() {
    echo -e "${BLUE}=== 项目资源概览 ===${NC}"
    echo -e "${BLUE}项目:${NC} $(gcloud config get-value project 2>/dev/null || echo '未设置')"
    
    echo -e "${BLUE}服务账户:${NC}"
    gcloud iam service-accounts list --format="value(email)" 2>/dev/null || echo "无法获取服务账户列表"
    
    echo -e "${BLUE}存储桶:${NC}"
    gcloud storage buckets list --format="value(name)" 2>/dev/null || echo "无存储桶或无权限"
    
    echo -e "${BLUE}计算实例:${NC}"
    gcloud compute instances list --format="value(name,zone,status)" 2>/dev/null || echo "无实例或无权限"
}

# 应急切换
emergency_switch() {
    echo -e "${RED}🚨 应急切换到管理员服务账户${NC}"
    echo -e "${YELLOW}此操作将给予完整的项目管理权限${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        switch_to_manager
    else
        echo -e "${BLUE}操作已取消${NC}"
    fi
}

# 设置 zsh 别名
setup_aliases() {
    local zshrc="$HOME/.zshrc"
    local alias_content="
# Google Cloud 服务账户快速切换
alias gcm='gcloud auth activate-service-account --key-file=\"\$HOME/.config/gcloud-keys/manager-sa.json\" && echo \"✅ 已切换到 Manager SA\"'
alias gcb='gcloud auth activate-service-account --key-file=\"\$HOME/.config/gcloud-keys/business-sa.json\" && echo \"✅ 已切换到 Business SA\"'
alias gcu='gcloud auth login && echo \"✅ 已切换到用户账户\"'
alias gcs='gcloud auth list && echo \"当前项目: \$(gcloud config get-value project)\"'
alias gcp='gcloud config set project'
alias gctest-manager='gcm && gcloud projects describe \$(gcloud config get-value project) && gcloud iam service-accounts list'
alias gctest-business='gcb && gcloud storage buckets list && gcloud ai models list --region=us-central1 2>/dev/null || echo \"验证 Vertex AI 权限（需要模型存在）\"'
alias gcresources='echo \"=== 项目资源概览 ===\" && echo \"项目: \$(gcloud config get-value project)\" && echo \"服务账户:\" && gcloud iam service-accounts list --format=\"value(email)\" && echo \"存储桶:\" && gcloud storage buckets list --format=\"value(name)\" 2>/dev/null || echo \"无存储桶或无权限\" && echo \"计算实例:\" && gcloud compute instances list --format=\"value(name,zone,status)\" 2>/dev/null || echo \"无实例或无权限\"'
alias gcemergency='echo \"🚨 应急切换到 Manager SA\" && gcm && echo \"现在拥有完整管理权限，请谨慎操作！\"'
"

    echo -e "${BLUE}设置 zsh 别名...${NC}"
    
    # 检查是否已存在别名
    if grep -q "# Google Cloud 服务账户快速切换" "$zshrc" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  别名已存在，跳过设置${NC}"
    else
        echo "$alias_content" >> "$zshrc"
        echo -e "${GREEN}✅ 别名已添加到 $zshrc${NC}"
        echo -e "${BLUE}请运行 'source ~/.zshrc' 或重新打开终端以使用别名${NC}"
    fi
}

# 安装脚本
install_script() {
    local script_name="gcloud-manager"
    local install_dir="/usr/local/bin"
    local script_path="$install_dir/$script_name"
    
    echo -e "${BLUE}安装脚本到系统路径...${NC}"
    
    # 复制脚本
    sudo cp "$0" "$script_path"
    sudo chmod +x "$script_path"
    
    echo -e "${GREEN}✅ 脚本已安装为: $script_path${NC}"
    echo -e "${BLUE}现在可以在任何位置使用 '$script_name' 命令${NC}"
}

# 主函数
main() {
    case "${1:-help}" in
        manager|m)
            switch_to_manager
            ;;
        business|b)
            switch_to_business
            ;;
        user|u)
            switch_to_user
            ;;
        status|s)
            show_status
            ;;
        test|t)
            test_permissions
            ;;
        project|p)
            set_project "$2"
            ;;
        resources|r)
            show_resources
            ;;
        emergency|e)
            emergency_switch
            ;;
        setup)
            setup_aliases
            ;;
        install)
            install_script
            ;;
        help|h|--help|-h)
            show_help
            ;;
        *)
            echo -e "${RED}❌ 未知命令: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"