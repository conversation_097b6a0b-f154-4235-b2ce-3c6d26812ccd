#!/bin/bash

# Google Cloud zsh 别名安装脚本
# 自动将 gcloud 管理别名添加到 ~/.zshrc

set -e

ZSHRC="$HOME/.zshrc"
BACKUP_FILE="$HOME/.zshrc.backup.$(date +%Y%m%d_%H%M%S)"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 Google Cloud zsh 别名安装程序${NC}"
echo

# 备份现有的 .zshrc
if [[ -f "$ZSHRC" ]]; then
    echo -e "${BLUE}📋 备份现有 .zshrc 到: $BACKUP_FILE${NC}"
    cp "$ZSHRC" "$BACKUP_FILE"
fi

# 检查是否已存在别名
if grep -q "# Google Cloud 服务账户快速切换" "$ZSHRC" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  检测到已存在的 Google Cloud 别名${NC}"
    read -p "是否覆盖现有别名? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 删除现有的别名块
        sed -i '' '/# Google Cloud 服务账户快速切换/,/^$/d' "$ZSHRC"
        echo -e "${BLUE}🗑️  已删除现有别名${NC}"
    else
        echo -e "${BLUE}操作已取消${NC}"
        exit 0
    fi
fi

# 添加别名
cat >> "$ZSHRC" << 'EOF'

# Google Cloud 服务账户快速切换
alias gcm='gcloud auth activate-service-account --key-file="$HOME/.config/gcloud-keys/manager-sa.json" && echo "✅ 已切换到 Manager SA"'
alias gcb='gcloud auth activate-service-account --key-file="$HOME/.config/gcloud-keys/business-sa.json" && echo "✅ 已切换到 Business SA"'
alias gcu='gcloud auth login && echo "✅ 已切换到用户账户"'
alias gcs='gcloud auth list && echo "当前项目: $(gcloud config get-value project)"'
alias gcp='gcloud config set project'

# 权限验证快捷命令
alias gctest-manager='gcm && gcloud projects describe $(gcloud config get-value project) && gcloud iam service-accounts list'
alias gctest-business='gcb && gcloud storage buckets list && gcloud ai models list --region=us-central1 2>/dev/null || echo "验证 Vertex AI 权限（需要模型存在）"'

# 项目资源查看
alias gcresources='echo "=== 项目资源概览 ===" && echo "项目: $(gcloud config get-value project)" && echo "服务账户:" && gcloud iam service-accounts list --format="value(email)" && echo "存储桶:" && gcloud storage buckets list --format="value(name)" 2>/dev/null || echo "无存储桶或无权限" && echo "计算实例:" && gcloud compute instances list --format="value(name,zone,status)" 2>/dev/null || echo "无实例或无权限"'

# 应急切换（带确认）
alias gcemergency='echo "🚨 应急切换到 Manager SA" && gcm && echo "现在拥有完整管理权限，请谨慎操作！"'

# 辅助命令
alias gchelp='echo "Google Cloud 快速命令:
  gcm          - 切换到管理员SA  
  gcb          - 切换到业务SA
  gcu          - 切换到用户账户
  gcs          - 查看认证状态
  gcp <id>     - 设置项目ID
  gctest-*     - 测试权限
  gcresources  - 查看资源
  gcemergency  - 应急管理切换"'

EOF

echo -e "${GREEN}✅ Google Cloud 别名已成功添加到 $ZSHRC${NC}"
echo
echo -e "${BLUE}📝 可用的别名命令:${NC}"
echo "  gcm          - 切换到管理员SA"
echo "  gcb          - 切换到业务SA" 
echo "  gcu          - 切换到用户账户"
echo "  gcs          - 查看认证状态"
echo "  gcp <id>     - 设置项目ID"
echo "  gctest-*     - 测试权限"
echo "  gcresources  - 查看资源概览"
echo "  gcemergency  - 应急管理切换"
echo "  gchelp       - 显示帮助"
echo
echo -e "${YELLOW}🔄 请运行以下命令使别名生效:${NC}"
echo "  source ~/.zshrc"
echo
echo -e "${BLUE}或者重新打开终端窗口${NC}"