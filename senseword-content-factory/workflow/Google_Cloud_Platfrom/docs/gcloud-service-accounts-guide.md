# Google Cloud 服务账户管理指南

## 概述
本指南详细说明了两个功能完整的服务账户配置，为您提供完整的 Google Cloud 管理能力，包括应急管理场景。

## 快速开始
- **创建新服务账户**: 参考 [service-account-creation-checklist.md](./service-account-creation-checklist.md)
- **快速切换凭据**: 使用下方的 zsh 别名命令
- **应急管理**: Manager SA 提供完整后备管理能力

## 服务账户配置

### 1. 管理员服务账户 (Manager SA)
- **邮箱**: `manager-sa@[PROJECT_ID].iam.gserviceaccount.com`
- **密钥文件**: `~/.config/gcloud-keys/manager-sa.json`
- **功能**: 完整的项目管理权限 + Billing 查看权限
- **使用场景**: 应急管理、完整项目控制、主账户不可用时的后备方案

**核心角色**:
- `roles/owner` - 项目所有者权限
- `roles/editor` - 项目编辑者权限
- `Billing Account Viewer` - 账单查看权限（Billing 级别）

**专业管理角色**:
- `roles/cloudfunctions.admin` - Cloud Functions 管理
- `roles/cloudsql.admin` - Cloud SQL 管理
- `roles/compute.admin` - Compute Engine 管理
- `roles/compute.networkAdmin` - 网络管理
- `roles/compute.securityAdmin` - 安全管理
- `roles/container.admin` - GKE 管理
- `roles/dns.admin` - DNS 管理
- `roles/iam.serviceAccountAdmin` - 服务账户管理
- `roles/iam.serviceAccountKeyAdmin` - 密钥管理
- `roles/logging.admin` - 日志管理
- `roles/monitoring.admin` - 监控管理
- `roles/resourcemanager.projectIamAdmin` - IAM 管理
- `roles/run.admin` - Cloud Run 管理
- `roles/secretmanager.admin` - 密钥管理
- `roles/serviceusage.serviceUsageAdmin` - API 服务管理
- `roles/storage.admin` - 存储管理

### 2. 业务服务账户 (Business SA)
- **邮箱**: `business-sa@[PROJECT_ID].iam.gserviceaccount.com`
- **密钥文件**: `~/.config/gcloud-keys/business-sa.json`
- **功能**: TTS、GCS、Vertex AI 业务操作
- **使用场景**: 日常业务开发、AI/ML 模型训练和推理、数据处理

**业务角色**:
- `roles/aiplatform.admin` - Vertex AI 管理（包含 Batch Prediction）
  - 支持 `aiplatform.batchPredictionJobs.create/get/list/cancel/delete`
  - 完整的模型训练、部署、推理能力
- `roles/aiplatform.user` - Vertex AI 用户权限
- `roles/ml.developer` - ML 开发（包含 TTS）
  - Text-to-Speech API 完整访问权限
  - 机器学习模型开发能力
- `roles/storage.admin` - GCS 存储管理
  - 创建、删除、管理存储桶
  - 上传、下载、管理对象
  - 存储桶权限管理

## 使用方法

### 手动切换到管理员账户
```bash
gcloud auth activate-service-account --key-file="~/.config/gcloud-keys/manager-sa.json"
```

### 手动切换到业务账户
```bash
gcloud auth activate-service-account --key-file="~/.config/gcloud-keys/business-sa.json"
```

### 查看当前认证状态
```bash
gcloud auth list
```

### 验证权限
```bash
# 查看项目信息
gcloud projects describe magic-sw-465214

# 查看服务账户列表
gcloud iam service-accounts list

# 查看计算实例
gcloud compute instances list

# 查看存储桶
gcloud storage buckets list
```

## 应急管理场景

当主账户被风控时：

1. **立即切换到管理员SA**:
   ```bash
   gcloud auth activate-service-account --key-file="/Users/<USER>/.config/gcloud-keys/manager-sa.json"
   ```

2. **验证可以管理所有资源**:
   ```bash
   gcloud projects list
   gcloud iam service-accounts list
   gcloud compute instances list
   gcloud storage buckets list
   ```

3. **创建新的管理员用户**（如果需要）:
   ```bash
   gcloud iam service-accounts create backup-admin-sa \
     --display-name="Backup Admin SA"
   
   gcloud projects add-iam-policy-binding magic-sw-465214 \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/owner"
   ```

## 安全建议

1. **定期备份密钥文件**到安全位置
2. **定期轮换密钥**（每 90 天）
3. **监控服务账户活动**
4. **使用最小权限原则**为业务SA分配权限
5. **定期审计权限**

## 权限测试

运行以下命令测试权限：

```bash
# 测试管理员权限
gcloud auth activate-service-account --key-file="/Users/<USER>/.config/gcloud-keys/manager-sa.json"
gcloud projects get-iam-policy magic-sw-465214

# 测试业务权限
gcloud auth activate-service-account --key-file="/Users/<USER>/.config/gcloud-keys/business-sa.json"
gcloud ml models list
gcloud storage buckets list
```

## 故障排除

如果遇到权限问题：

1. 确认使用正确的密钥文件
2. 检查服务账户是否有所需权限
3. 验证项目ID是否正确
4. 确认API已启用

现在您拥有完整的 Google Cloud 管理权限，即使主账户被风控也能继续管理所有资源！