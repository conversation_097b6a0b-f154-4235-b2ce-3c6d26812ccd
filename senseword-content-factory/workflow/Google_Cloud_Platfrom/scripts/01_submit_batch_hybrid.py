#!/usr/bin/env python3
"""
混合批处理任务提交工具 (v6.0)

设计理念：
- Python: 格式验证、数据处理
- gcloud CLI: 文件上传（有进度显示）
- curl + Vertex AI API: 批处理任务提交（最稳定）

作者：AI Assistant
日期：2025-07-09
版本：v6.0
"""

import json
import os
import sys
import subprocess
import logging
from datetime import datetime
from pathlib import Path
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 项目配置
PROJECT_ID = "magic-sw-465214"
PROJECT_NUMBER = "189763113180"
LOCATION = "us-central1"
GCS_BUCKET = "senseword-batch-processing"
MODEL_NAME = f"projects/{PROJECT_ID}/locations/{LOCATION}/publishers/google/models/gemini-2.5-flash"

class HybridBatchSubmitter:
    """混合批处理任务提交器"""
    
    def __init__(self):
        """初始化提交器"""
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        logger.info("INFO - [HybridSubmitter][__init__001] 混合批处理提交器初始化完成")
        logger.info(f"INFO - [HybridSubmitter][__init__002] 项目ID: {PROJECT_ID}")
        logger.info(f"INFO - [HybridSubmitter][__init__003] 区域: {LOCATION}")
        logger.info(f"INFO - [HybridSubmitter][__init__004] 时间戳: {self.timestamp}")
    
    def validate_jsonl_file(self, file_path):
        """
        验证JSONL文件格式
        
        参数:
            file_path: JSONL文件路径
            
        返回:
            (is_valid, line_count, error_message)
        """
        try:
            logger.info("INFO - [HybridSubmitter][validate_jsonl_file_001] 开始验证JSONL文件格式")
            
            if not os.path.exists(file_path):
                return False, 0, f"文件不存在: {file_path}"
            
            line_count = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        json.loads(line)
                        line_count += 1
                    except json.JSONDecodeError as e:
                        return False, line_count, f"第{line_num}行JSON格式错误: {e}"
            
            if line_count == 0:
                return False, 0, "文件为空或没有有效的JSON行"
            
            logger.info(f"INFO - [HybridSubmitter][validate_jsonl_file_002] ✅ 文件验证通过，包含 {line_count} 行数据")
            return True, line_count, None
            
        except Exception as e:
            logger.error(f"ERROR - [HybridSubmitter][validate_jsonl_file_003] 验证失败: {e}")
            return False, 0, str(e)
    
    def check_gcloud_auth(self):
        """检查gcloud认证状态"""
        try:
            logger.info("INFO - [HybridSubmitter][check_gcloud_auth_001] 检查gcloud认证状态")
            
            # 检查访问令牌
            result = subprocess.run(
                ["gcloud", "auth", "print-access-token"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and result.stdout.strip():
                logger.info("INFO - [HybridSubmitter][check_gcloud_auth_002] ✅ gcloud认证正常")
                return True
            else:
                logger.error("ERROR - [HybridSubmitter][check_gcloud_auth_003] gcloud认证失败")
                logger.error(f"ERROR - [HybridSubmitter][check_gcloud_auth_004] 错误信息: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("ERROR - [HybridSubmitter][check_gcloud_auth_005] gcloud命令超时")
            return False
        except Exception as e:
            logger.error(f"ERROR - [HybridSubmitter][check_gcloud_auth_006] 检查认证失败: {e}")
            return False
    
    def upload_with_gsutil(self, local_file, gcs_path):
        """
        使用gsutil上传文件（实时进度显示，无超时限制）

        参数:
            local_file: 本地文件路径
            gcs_path: GCS路径

        返回:
            (success, gcs_uri)
        """
        try:
            import os

            gcs_uri = f"gs://{GCS_BUCKET}/{gcs_path}"

            # 获取文件大小
            file_size = os.path.getsize(local_file)
            file_size_mb = file_size / (1024 * 1024)
            file_size_gb = file_size_mb / 1024

            logger.info("INFO - [HybridSubmitter][upload_with_gsutil_001] 开始使用gsutil上传文件")
            logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_002] 本地文件: {local_file}")
            logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_003] 文件大小: {file_size_gb:.2f} GB ({file_size_mb:.2f} MB, {file_size:,} bytes)")
            logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_004] GCS路径: {gcs_uri}")

            # 使用gsutil cp命令上传，显示实时进度
            cmd = [
                "gsutil", "cp",
                local_file,
                gcs_uri
            ]

            logger.info("INFO - [HybridSubmitter][upload_with_gsutil_005] 执行gsutil上传命令...")
            logger.info("INFO - [HybridSubmitter][upload_with_gsutil_006] 📤 上传中（gsutil会显示实时进度）...")
            logger.info("INFO - [HybridSubmitter][upload_with_gsutil_007] 💡 提示：gsutil会显示进度条，包括百分比、速度和预计剩余时间")

            # 记录开始时间
            import time
            start_time = time.time()

            # 使用gsutil上传，不设置超时，让gsutil自己处理
            # gsutil会显示实时进度条，包括百分比、速度和预计剩余时间
            # 不捕获输出，让进度条直接显示在终端
            result = subprocess.run(cmd)

            end_time = time.time()
            upload_duration = end_time - start_time

            if result.returncode == 0:
                # 计算上传速度
                upload_speed_mbps = file_size_mb / upload_duration if upload_duration > 0 else 0
                upload_speed_gbps = file_size_gb / upload_duration if upload_duration > 0 else 0

                logger.info("INFO - [HybridSubmitter][upload_with_gsutil_008] ✅ 文件上传成功")
                logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_009] 📊 上传统计:")
                logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_010]   - 文件大小: {file_size_gb:.2f} GB ({file_size_mb:.2f} MB)")
                logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_011]   - 上传时间: {upload_duration:.2f} 秒 ({upload_duration/60:.1f} 分钟)")
                logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_012]   - 上传速度: {upload_speed_gbps:.3f} GB/s ({upload_speed_mbps:.2f} MB/s)")
                logger.info(f"INFO - [HybridSubmitter][upload_with_gsutil_013] GCS URI: {gcs_uri}")

                return True, gcs_uri
            else:
                logger.error("ERROR - [HybridSubmitter][upload_with_gsutil_014] 文件上传失败")
                logger.error(f"ERROR - [HybridSubmitter][upload_with_gsutil_015] 返回码: {result.returncode}")
                return False, None

        except Exception as e:
            logger.error(f"ERROR - [HybridSubmitter][upload_with_gsutil_016] 上传失败: {e}")
            return False, None
    
    def submit_with_curl(self, job_name, input_gcs_uri, output_gcs_uri):
        """
        使用curl + Vertex AI API提交批处理任务
        
        参数:
            job_name: 任务名称
            input_gcs_uri: 输入GCS URI
            output_gcs_uri: 输出GCS URI
            
        返回:
            (success, vertex_job_id, response_data)
        """
        try:
            logger.info("INFO - [HybridSubmitter][submit_with_curl_001] 使用curl API提交批处理任务")
            logger.info(f"INFO - [HybridSubmitter][submit_with_curl_002] 任务名称: {job_name}")
            logger.info(f"INFO - [HybridSubmitter][submit_with_curl_003] 输入: {input_gcs_uri}")
            logger.info(f"INFO - [HybridSubmitter][submit_with_curl_004] 输出: {output_gcs_uri}")
            
            # 获取访问令牌
            token_result = subprocess.run(
                ["gcloud", "auth", "print-access-token"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if token_result.returncode != 0:
                logger.error("ERROR - [HybridSubmitter][submit_with_curl_005] 获取访问令牌失败")
                return False, None, None
            
            access_token = token_result.stdout.strip()
            
            # 构建API请求数据
            api_data = {
                "displayName": job_name,
                "model": MODEL_NAME,
                "inputConfig": {
                    "instancesFormat": "jsonl",
                    "gcsSource": {
                        "uris": [input_gcs_uri]
                    }
                },
                "outputConfig": {
                    "predictionsFormat": "jsonl",
                    "gcsDestination": {
                        "outputUriPrefix": output_gcs_uri
                    }
                }
            }
            
            # API端点
            api_url = f"https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT_NUMBER}/locations/{LOCATION}/batchPredictionJobs"
            
            # 使用curl提交请求
            curl_cmd = [
                "curl", "-s", "-X", "POST",
                "-H", f"Authorization: Bearer {access_token}",
                "-H", "Content-Type: application/json",
                "-d", json.dumps(api_data),
                api_url
            ]
            
            logger.info("INFO - [HybridSubmitter][submit_with_curl_006] 发送API请求...")
            result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                try:
                    response_data = json.loads(result.stdout)
                    
                    if "name" in response_data:
                        vertex_job_id = response_data["name"].split("/")[-1]
                        logger.info("INFO - [HybridSubmitter][submit_with_curl_007] ✅ 批处理任务提交成功")
                        logger.info(f"INFO - [HybridSubmitter][submit_with_curl_008] Vertex任务ID: {vertex_job_id}")
                        logger.info(f"INFO - [HybridSubmitter][submit_with_curl_009] 任务状态: {response_data.get('state', 'UNKNOWN')}")
                        return True, vertex_job_id, response_data
                    else:
                        logger.error("ERROR - [HybridSubmitter][submit_with_curl_010] API响应中没有任务ID")
                        logger.error(f"ERROR - [HybridSubmitter][submit_with_curl_011] 响应内容: {result.stdout}")
                        return False, None, response_data
                        
                except json.JSONDecodeError as e:
                    logger.error(f"ERROR - [HybridSubmitter][submit_with_curl_012] API响应解析失败: {e}")
                    logger.error(f"ERROR - [HybridSubmitter][submit_with_curl_013] 响应内容: {result.stdout}")
                    return False, None, None
            else:
                logger.error("ERROR - [HybridSubmitter][submit_with_curl_014] curl请求失败")
                logger.error(f"ERROR - [HybridSubmitter][submit_with_curl_015] 错误信息: {result.stderr}")
                return False, None, None
                
        except subprocess.TimeoutExpired:
            logger.error("ERROR - [HybridSubmitter][submit_with_curl_016] API请求超时")
            return False, None, None
        except Exception as e:
            logger.error(f"ERROR - [HybridSubmitter][submit_with_curl_017] 提交失败: {e}")
            return False, None, None
    
    def submit_batch_job(self, input_file_path, job_name=None, output_dir=None):
        """
        提交批处理任务（混合方式）
        
        参数:
            input_file_path: 输入JSONL文件路径
            job_name: 自定义任务名称
            output_dir: 结果保存目录
            
        返回:
            任务信息字典
        """
        try:
            logger.info("=" * 60)
            logger.info("INFO - [HybridSubmitter][submit_batch_job_001] 🚀 开始混合批处理任务提交")
            logger.info("=" * 60)
            
            # 步骤1: 验证输入文件
            logger.info("INFO - [HybridSubmitter][submit_batch_job_002] 步骤1: 验证输入文件")
            is_valid, line_count, error_msg = self.validate_jsonl_file(input_file_path)
            if not is_valid:
                raise ValueError(f"输入文件验证失败: {error_msg}")
            
            # 步骤2: 检查认证
            logger.info("INFO - [HybridSubmitter][submit_batch_job_003] 步骤2: 检查gcloud认证")
            if not self.check_gcloud_auth():
                raise RuntimeError("gcloud认证失败，请先运行: gcloud auth login")
            
            # 步骤3: 生成任务信息
            logger.info("INFO - [HybridSubmitter][submit_batch_job_004] 步骤3: 生成任务信息")
            if job_name:
                final_job_name = f"{job_name}-{self.timestamp}"
            else:
                final_job_name = f"batch-job-{self.timestamp}"
            
            gcs_input_path = f"batch-input/{self.timestamp}/input.jsonl"
            gcs_output_path = f"batch-output/{self.timestamp}/"
            output_gcs_uri = f"gs://{GCS_BUCKET}/{gcs_output_path}"
            
            logger.info(f"INFO - [HybridSubmitter][submit_batch_job_005] 任务名称: {final_job_name}")
            logger.info(f"INFO - [HybridSubmitter][submit_batch_job_006] GCS输入路径: {gcs_input_path}")
            logger.info(f"INFO - [HybridSubmitter][submit_batch_job_007] GCS输出路径: {gcs_output_path}")
            
            # 步骤4: 使用gsutil上传文件
            logger.info("INFO - [HybridSubmitter][submit_batch_job_008] 步骤4: 使用gsutil上传文件")
            upload_success, input_gcs_uri = self.upload_with_gsutil(input_file_path, gcs_input_path)
            if not upload_success:
                raise RuntimeError("文件上传失败")
            
            # 步骤5: 使用curl API提交任务
            logger.info("INFO - [HybridSubmitter][submit_batch_job_009] 步骤5: 使用curl API提交批处理任务")
            submit_success, vertex_job_id, response_data = self.submit_with_curl(
                final_job_name, input_gcs_uri, output_gcs_uri
            )
            if not submit_success:
                raise RuntimeError("批处理任务提交失败")
            
            # 构建任务信息
            task_info = {
                "timestamp": self.timestamp,
                "job_name": final_job_name,
                "vertex_job_id": vertex_job_id,
                "input_file": input_file_path,
                "input_gcs_uri": input_gcs_uri,
                "output_gcs_uri": output_gcs_uri,
                "line_count": line_count,
                "project_id": PROJECT_ID,
                "location": LOCATION,
                "model": MODEL_NAME,
                "state": response_data.get("state", "UNKNOWN"),
                "resource_name": response_data.get("name", ""),
                "output_dir": output_dir
            }
            
            logger.info("=" * 60)
            logger.info("INFO - [HybridSubmitter][submit_batch_job_010] ✅ 批处理任务提交完成")
            logger.info("=" * 60)
            
            return task_info
            
        except Exception as e:
            logger.error(f"ERROR - [HybridSubmitter][submit_batch_job_011] 任务提交失败: {e}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="混合批处理任务提交工具")
    parser.add_argument("input_file", help="输入JSONL文件路径")
    parser.add_argument("--job-name", help="自定义任务名称")
    parser.add_argument("--output-dir", help="结果保存目录")
    
    args = parser.parse_args()
    
    try:
        submitter = HybridBatchSubmitter()
        task_info = submitter.submit_batch_job(
            args.input_file,
            args.job_name,
            args.output_dir
        )
        
        # 输出任务信息（JSON格式）
        print(json.dumps(task_info, ensure_ascii=False, indent=2))
        
        return 0
        
    except Exception as e:
        logger.error(f"ERROR - [main] 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
