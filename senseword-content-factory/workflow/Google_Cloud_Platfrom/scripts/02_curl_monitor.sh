#!/bin/bash
#
# Vertex AI 批处理任务监控脚本 - curl API版本
#
# 功能：
# - 使用curl + Vertex AI API进行快速、稳定的任务监控
# - 支持单次查询和连续监控
# - 提供详细的任务状态和统计信息
# - 自动检测任务完成并显示结果路径
#
# 使用方法：
#   ./02_curl_monitor.sh <vertex_job_id>                    # 单次状态查询
#   ./02_curl_monitor.sh <vertex_job_id> --watch           # 连续监控
#   ./02_curl_monitor.sh <vertex_job_id> --watch 60        # 自定义间隔(秒)
#   ./02_curl_monitor.sh <vertex_job_id> --detail          # 详细信息
#   ./02_curl_monitor.sh --help                            # 显示帮助
#
# 作者：AI Assistant
# 日期：2025-07-09
# 版本：v5.0 curl API优先
#

# 配置参数
PROJECT_NUMBER="189763113180"
LOCATION="us-central1"
API_BASE="https://us-central1-aiplatform.googleapis.com/v1"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "Vertex AI 批处理任务监控脚本 - curl API版本"
    echo ""
    echo "使用方法："
    echo "  $0 <vertex_job_id>                    # 单次状态查询"
    echo "  $0 <vertex_job_id> --watch           # 连续监控 (默认30秒间隔)"
    echo "  $0 <vertex_job_id> --watch 60        # 连续监控 (自定义间隔)"
    echo "  $0 <vertex_job_id> --detail          # 显示详细信息"
    echo "  $0 --help                            # 显示此帮助"
    echo ""
    echo "示例："
    echo "  $0 2893352839763984384"
    echo "  $0 2893352839763984384 --watch"
    echo "  $0 2893352839763984384 --watch 45"
    echo "  $0 2893352839763984384 --detail"
    echo ""
    echo "特性："
    echo "  - 使用curl + Vertex AI API，最高稳定性和速度"
    echo "  - 彩色输出，易于阅读"
    echo "  - 自动检测任务完成"
    echo "  - 显示详细的统计信息"
    echo "  - 支持自定义监控间隔"
}

# 获取访问令牌
get_access_token() {
    gcloud auth print-access-token 2>/dev/null
}

# 调用Vertex AI API
call_vertex_api() {
    local job_id="$1"
    local token="$2"
    
    curl -s -H "Authorization: Bearer $token" \
        "$API_BASE/projects/$PROJECT_NUMBER/locations/$LOCATION/batchPredictionJobs/$job_id"
}

# 格式化时间
format_time() {
    local timestamp="$1"
    if [ -n "$timestamp" ] && [ "$timestamp" != "null" ]; then
        # 转换ISO时间为本地时间
        date -d "$timestamp" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "$timestamp"
    else
        echo "N/A"
    fi
}

# 显示任务状态
show_status() {
    local job_id="$1"
    local detail_mode="$2"
    
    echo -e "${BLUE}INFO - [curl_monitor][show_status_001]${NC} 查询任务状态: $job_id"
    
    # 获取访问令牌
    local token=$(get_access_token)
    if [ -z "$token" ]; then
        echo -e "${RED}ERROR - [curl_monitor][show_status_002]${NC} 无法获取访问令牌，请检查gcloud认证"
        return 1
    fi
    
    # 调用API
    local response=$(call_vertex_api "$job_id" "$token")
    if [ -z "$response" ]; then
        echo -e "${RED}ERROR - [curl_monitor][show_status_003]${NC} API调用失败"
        return 1
    fi
    
    # 检查错误
    local error=$(echo "$response" | jq -r '.error.message // empty' 2>/dev/null)
    if [ -n "$error" ]; then
        echo -e "${RED}ERROR - [curl_monitor][show_status_004]${NC} API错误: $error"
        return 1
    fi
    
    # 提取基本信息
    local state=$(echo "$response" | jq -r '.state // "UNKNOWN"')
    local display_name=$(echo "$response" | jq -r '.displayName // "N/A"')
    local create_time=$(echo "$response" | jq -r '.createTime // null')
    local end_time=$(echo "$response" | jq -r '.endTime // null')
    local model=$(echo "$response" | jq -r '.model // "N/A"')
    
    # 提取统计信息
    local completion_stats=$(echo "$response" | jq -r '.completionStats // {}')
    local successful_count=$(echo "$completion_stats" | jq -r '.successfulCount // 0')
    local failed_count=$(echo "$completion_stats" | jq -r '.failedCount // 0')
    local incomplete_count=$(echo "$completion_stats" | jq -r '.incompleteCount // 0')
    
    # 提取输出信息
    local output_info=$(echo "$response" | jq -r '.outputInfo // {}')
    local gcs_output_dir=$(echo "$output_info" | jq -r '.gcsOutputDirectory // "N/A"')
    
    # 显示基本状态
    echo -e "${CYAN}==================== 任务状态 ====================${NC}"
    echo -e "${YELLOW}任务名称:${NC} $display_name"
    echo -e "${YELLOW}任务ID:${NC} $job_id"
    
    # 根据状态显示不同颜色
    case "$state" in
        "JOB_STATE_SUCCEEDED")
            echo -e "${YELLOW}状态:${NC} ${GREEN}$state ✅${NC}"
            ;;
        "JOB_STATE_FAILED")
            echo -e "${YELLOW}状态:${NC} ${RED}$state ❌${NC}"
            ;;
        "JOB_STATE_RUNNING")
            echo -e "${YELLOW}状态:${NC} ${BLUE}$state 🔄${NC}"
            ;;
        "JOB_STATE_PENDING")
            echo -e "${YELLOW}状态:${NC} ${PURPLE}$state ⏳${NC}"
            ;;
        *)
            echo -e "${YELLOW}状态:${NC} $state"
            ;;
    esac
    
    echo -e "${YELLOW}创建时间:${NC} $(format_time "$create_time")"
    if [ "$end_time" != "null" ]; then
        echo -e "${YELLOW}结束时间:${NC} $(format_time "$end_time")"
    fi
    
    # 显示统计信息
    if [ "$successful_count" != "0" ] || [ "$failed_count" != "0" ] || [ "$incomplete_count" != "0" ]; then
        echo -e "${CYAN}==================== 处理统计 ====================${NC}"
        echo -e "${YELLOW}成功:${NC} ${GREEN}$successful_count${NC}"
        echo -e "${YELLOW}失败:${NC} ${RED}$failed_count${NC}"
        echo -e "${YELLOW}未完成:${NC} ${PURPLE}$incomplete_count${NC}"
        
        local total=$((successful_count + failed_count + incomplete_count))
        if [ $total -gt 0 ]; then
            local success_rate=$((successful_count * 100 / total))
            echo -e "${YELLOW}成功率:${NC} ${GREEN}$success_rate%${NC}"
        fi
    fi
    
    # 显示输出信息
    if [ "$gcs_output_dir" != "N/A" ]; then
        echo -e "${CYAN}==================== 输出信息 ====================${NC}"
        echo -e "${YELLOW}输出路径:${NC} $gcs_output_dir"
        
        if [ "$state" = "JOB_STATE_SUCCEEDED" ]; then
            echo -e "${GREEN}💡 下载命令:${NC}"
            echo -e "   ${BLUE}gcloud storage cp $gcs_output_dir/predictions.jsonl ./results/${NC}"
        fi
    fi
    
    # 详细模式
    if [ "$detail_mode" = "true" ]; then
        echo -e "${CYAN}==================== 详细信息 ====================${NC}"
        echo -e "${YELLOW}模型:${NC} $model"
        echo "$response" | jq '{
            inputConfig: .inputConfig,
            outputConfig: .outputConfig,
            dedicatedResources: .dedicatedResources,
            serviceAccount: .serviceAccount
        }' 2>/dev/null
    fi
    
    echo -e "${CYAN}=================================================${NC}"
    
    return 0
}

# 连续监控
watch_job() {
    local job_id="$1"
    local interval="${2:-30}"
    
    echo -e "${GREEN}开始连续监控任务: $job_id (间隔: ${interval}秒)${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止监控${NC}"
    echo ""
    
    while true; do
        echo -e "${BLUE}$(date): 检查任务状态...${NC}"
        
        if show_status "$job_id" "false"; then
            # 获取状态判断是否继续
            local token=$(get_access_token)
            local response=$(call_vertex_api "$job_id" "$token")
            local state=$(echo "$response" | jq -r '.state // "UNKNOWN"')
            
            if [ "$state" = "JOB_STATE_SUCCEEDED" ] || [ "$state" = "JOB_STATE_FAILED" ]; then
                echo -e "${GREEN}任务已完成，停止监控${NC}"
                break
            fi
        else
            echo -e "${RED}状态查询失败，将在${interval}秒后重试${NC}"
        fi
        
        echo ""
        sleep "$interval"
    done
}

# 主函数
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}ERROR: curl 未安装${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}ERROR: jq 未安装，请安装: brew install jq${NC}"
        exit 1
    fi
    
    if ! command -v gcloud &> /dev/null; then
        echo -e "${RED}ERROR: gcloud 未安装${NC}"
        exit 1
    fi
    
    # 解析参数
    if [ $# -eq 0 ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        show_help
        exit 0
    fi
    
    local job_id="$1"
    local mode="$2"
    local interval="$3"
    
    # 验证job_id格式
    if ! [[ "$job_id" =~ ^[0-9]+$ ]]; then
        echo -e "${RED}ERROR: 无效的任务ID格式: $job_id${NC}"
        echo "任务ID应该是纯数字，例如: 2893352839763984384"
        exit 1
    fi
    
    case "$mode" in
        "--watch")
            watch_job "$job_id" "$interval"
            ;;
        "--detail")
            show_status "$job_id" "true"
            ;;
        "")
            show_status "$job_id" "false"
            ;;
        *)
            echo -e "${RED}ERROR: 未知参数: $mode${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
