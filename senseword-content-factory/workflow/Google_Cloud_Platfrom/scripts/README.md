# SenseWord 批处理任务管理系统

## 🎯 v6.1 混合架构设计 (gsutil优化版)

> **设计理念**: 混合使用不同工具的优势，实现最佳的用户体验和性能表现。

### 🆕 v6.1 混合架构特性
- ✅ **Python脚本**: 格式验证、数据处理（精确可靠）
- ✅ **gsutil**: 文件上传（实时进度条、无超时限制）
- ✅ **curl + Vertex AI API**: 批处理任务提交和监控（最高稳定性）
- ✅ **无状态架构**: 每次运行独立，无需维护本地状态文件
- ✅ **实时进度显示**: 百分比、速度、预计剩余时间
- ✅ **大文件支持**: 无超时限制，支持GB级文件上传
- ✅ **快速任务提交**: 避免Python SDK的长时间等待问题
- ✅ **实时状态查询**: curl命令实现快速状态检查
- ✅ **结果分析工具**: 专用的批处理结果分析和提取工具
- ✅ **智能重试机制**: 自动提取失败请求，生成重试文件
- ✅ **JSON格式输出**: 结构化输出便于脚本化处理

### 🚀 混合架构优势

**为什么选择混合架构？**

1. **Python脚本优势**:
   - 精确的JSONL格式验证
   - 详细的错误报告和统计信息
   - 灵活的数据处理和转换

2. **gsutil优势** (v6.1优化):
   - 🆕 **实时进度条**: 显示百分比、速度、预计剩余时间
   - 🆕 **无超时限制**: 支持大文件上传，不会因超时失败
   - 🆕 **原生GCS工具**: 专为Google Cloud Storage设计
   - 🆕 **并行上传提示**: 自动建议优化配置
   - 稳定可靠的网络传输和断点续传

3. **curl + Vertex AI API优势**:
   - 最快的任务提交速度
   - 避免Python SDK的长时间等待问题
   - 直接的HTTP API调用，无中间层
   - 最高的稳定性和可控性

**实际使用体验对比**:
- ❌ **纯Python SDK**: 任务提交时经常卡住，无进度显示
- ❌ **gcloud storage**: 大文件上传超时失败，无实时进度
- ✅ **gsutil混合架构**: 完美的用户体验，实时进度，无超时问题

**v6.1实际测试结果**:
- ✅ **975MB文件**: 7分钟成功上传
- ✅ **实时进度**: `| [1 files][975.0 MiB/975.0 MiB] 1.9 MiB/s`
- ✅ **56,702个单词**: 批处理任务成功提交

## 🔧 环境准备

### 必需依赖
```bash
# 检查依赖是否安装
curl --version     # HTTP客户端
jq --version       # JSON处理工具
gcloud version     # Google Cloud SDK
python3 --version  # Python 3.x
```

### 认证设置 (重要!)
```bash
# 设置Google Cloud认证 (必须!)
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"

# 验证认证
gcloud auth print-access-token

# 如果没有令牌，需要先登录
gcloud auth login
gcloud config set project magic-sw-465214
```

### 项目配置
```bash
# 项目信息
PROJECT_ID="magic-sw-465214"
PROJECT_NUMBER="189763113180"
LOCATION="us-central1"
BUCKET="gs://senseword-batch-processing"
```

## 📁 文件结构

```
Google_Cloud_Platfrom/py_scripts/
├── 01-Scripts/
│   ├── 01_submit_batch_hybrid.py        # 🆕 混合批处理任务提交脚本 (v6.1)
│   ├── 02_curl_monitor.sh               # curl API监控脚本 (主要监控方式)
│   ├── 03_download_results_gsutil.py    # 🆕 gsutil结果下载器 (实时进度显示)
│   ├── 04_batch_results_extractor.py    # 🆕 智能批处理结果提取器 (支持重试机制)
│   ├── quick_monitor.sh                 # 快速监控启动脚本
│   └── README.md                        # 本文档
└── 02-Results/                          # 任务结果下载目录
    └── <task_name>-<timestamp>/         # 按任务组织的结果文件
```

### 🔢 编号说明
- **01**: 任务提交 (工作流程第一步) - 🆕 混合架构：Python验证 + gsutil上传 + curl API提交
- **02**: 任务监控 (工作流程第二步) - curl API监控
- **03**: 结果提取和分析 (工作流程第四步) - 一体化工具，包含提取、统计、成本分析和报告生成
- **04**: 结果下载 (工作流程第三步) - 🆕 gsutil下载器：实时进度显示，无超时限制

## 🚀 标准工作流程

### 前置步骤: 设置认证 (必须!)
```bash
# 设置Google Cloud认证环境变量
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"

# 验证认证是否正常
gcloud auth print-access-token
```

### 步骤1: 提交任务 (🆕 gsutil混合架构)
```bash
# 使用混合脚本提交任务 (Python验证 + gsutil上传 + curl API提交)
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
python3 01_submit_batch_hybrid.py input.jsonl --job-name my-task --output-dir ./results > task_info.json

# 提取Vertex任务ID
VERTEX_JOB_ID=$(cat task_info.json | jq -r '.vertex_job_id')
echo "Vertex Job ID: $VERTEX_JOB_ID"

# 查看详细的上传统计信息
cat task_info.json | jq '{
  job_name: .job_name,
  vertex_job_id: .vertex_job_id,
  line_count: .line_count,
  input_gcs_uri: .input_gcs_uri,
  output_gcs_uri: .output_gcs_uri,
  state: .state
}'
```

**gsutil上传过程示例**:
```
INFO - [HybridSubmitter][upload_with_gsutil_003] 文件大小: 0.95 GB (974.99 MB, 1,022,353,228 bytes)
INFO - [HybridSubmitter][upload_with_gsutil_006] 📤 上传中（gsutil会显示实时进度）...
INFO - [HybridSubmitter][upload_with_gsutil_007] 💡 提示：gsutil会显示进度条，包括百分比、速度和预计剩余时间

Copying file:///path/to/your/file.jsonl [Content-Type=application/octet-stream]...
| [1 files][975.0 MiB/975.0 MiB]    1.9 MiB/s
Operation completed over 1 objects/975.0 MiB.

INFO - [HybridSubmitter][upload_with_gsutil_008] ✅ 文件上传成功
INFO - [HybridSubmitter][upload_with_gsutil_011] - 上传时间: 417.69 秒 (7.0 分钟)
INFO - [HybridSubmitter][upload_with_gsutil_012] - 上传速度: 0.002 GB/s (2.33 MB/s)
```

### 步骤2: 监控任务
```bash
# 使用curl API监控脚本 (推荐 - 最快最稳定)
./02_curl_monitor.sh $VERTEX_JOB_ID

# 连续监控 (自动检测完成)
./02_curl_monitor.sh $VERTEX_JOB_ID --watch

# 或者使用原始curl命令
curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/$VERTEX_JOB_ID" \
  | jq -r '.state'
```

### 步骤3: 下载结果 (🆕 gsutil版本)
```bash
# 使用gsutil下载器 (实时进度显示)
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
python3 04_download_results_gsutil.py $VERTEX_JOB_ID --output-dir ./results > download_info.json

# 查看下载信息
cat download_info.json | jq '.'
```

**传统方式 (仍然支持)**:
```bash
# 获取输出路径 (使用curl API)
OUTPUT_URI=$(curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/$VERTEX_JOB_ID" \
  | jq -r '.outputInfo.gcsOutputDirectory')

# 使用gsutil下载 (有进度显示)
mkdir -p ./results
gsutil cp $OUTPUT_URI/predictions.jsonl ./results/
```

### 步骤4: 提取和分析结果
```bash
# 一体化提取和分析结果
python3 04_batch_results_extractor.py ./results/predictions.jsonl \
  --output-dir ./results
```

**生成的文件**:
- `simplified_responses.json` - 简化的响应数据
- `extraction_report.md` - 详细的Markdown分析报告
- `extraction_stats.json` - 统计信息和成本分析
- `ERROR_RETRY_failed_requests.jsonl` - 失败请求(可重新提交)
- `ERROR_RETRY_content_parse_failures.jsonl` - 内容解析失败请求(可重新提交)

## 🆕 混合脚本详细使用示例

### 基本使用
```bash
# 设置认证
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"

# 提交任务（会显示详细的上传进度）
python3 01_submit_batch_hybrid.py input.jsonl --job-name my-audit-task
```

### 完整工作流程示例
```bash
# 1. 提交AI审核任务
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
python3 01_submit_batch_hybrid.py \
  "/path/to/ai_audit_batch.jsonl" \
  --job-name "ai_audit_test_v2" \
  --output-dir "/path/to/results" > task_info.json

# 2. 查看任务信息
cat task_info.json | jq '.'

# 3. 提取任务ID并监控
VERTEX_JOB_ID=$(cat task_info.json | jq -r '.vertex_job_id')
echo "监控任务: $VERTEX_JOB_ID"

# 4. 持续监控任务状态
./02_curl_monitor.sh $VERTEX_JOB_ID --watch

# 5. 任务完成后下载结果 (gsutil版本)
python3 03_download_results_gsutil.py $VERTEX_JOB_ID --output-dir ./results > download_info.json

# 6. 提取和分析结果
python3 04_batch_results_extractor.py ./results/predictions.jsonl --output-dir ./results
```

### gsutil混合脚本输出示例

**小文件上传 (1.25MB)**:
```
INFO - [HybridSubmitter][upload_with_gsutil_003] 文件大小: 0.00 GB (1.25 MB, 1,310,720 bytes)
INFO - [HybridSubmitter][upload_with_gsutil_006] 📤 上传中（gsutil会显示实时进度）...

Copying file:///path/to/small_file.jsonl [Content-Type=application/octet-stream]...
/ [1 files][  1.3 MiB/  1.3 MiB]
Operation completed over 1 objects/1.3 MiB.

INFO - [HybridSubmitter][upload_with_gsutil_008] ✅ 文件上传成功
INFO - [HybridSubmitter][upload_with_gsutil_011] - 上传时间: 2.34 秒
INFO - [HybridSubmitter][upload_with_gsutil_012] - 上传速度: 0.001 GB/s (0.53 MB/s)
INFO - [HybridSubmitter][submit_with_curl_007] ✅ 批处理任务提交成功
INFO - [HybridSubmitter][submit_with_curl_008] Vertex任务ID: 1219280415777357824
```

**大文件上传 (975MB)**:
```
INFO - [HybridSubmitter][upload_with_gsutil_003] 文件大小: 0.95 GB (974.99 MB, 1,022,353,228 bytes)
INFO - [HybridSubmitter][upload_with_gsutil_006] 📤 上传中（gsutil会显示实时进度）...

Copying file:///path/to/large_file.jsonl [Content-Type=application/octet-stream]...
==> NOTE: You are uploading one or more large file(s), which would run
significantly faster if you enable parallel composite uploads...

| [1 files][975.0 MiB/975.0 MiB]    1.9 MiB/s
Operation completed over 1 objects/975.0 MiB.

INFO - [HybridSubmitter][upload_with_gsutil_008] ✅ 文件上传成功
INFO - [HybridSubmitter][upload_with_gsutil_011] - 上传时间: 417.69 秒 (7.0 分钟)
INFO - [HybridSubmitter][upload_with_gsutil_012] - 上传速度: 0.002 GB/s (2.33 MB/s)
INFO - [HybridSubmitter][submit_with_curl_007] ✅ 批处理任务提交成功
INFO - [HybridSubmitter][submit_with_curl_008] Vertex任务ID: 4438333802556686336
```

### gsutil下载过程示例

**下载结果文件**:
```
INFO - [ResultsDownloader][download_with_gsutil_005] 📥 下载中（gsutil会显示实时进度）...
INFO - [ResultsDownloader][download_with_gsutil_006] 💡 提示：gsutil会显示进度条，包括百分比、速度和预计剩余时间

Copying gs://senseword-batch-processing/batch-output/20250709_111757/prediction-model-2025-07-09T03:25:02.123456Z/predictions.jsonl...
| [1 files][125.3 MiB/125.3 MiB]    3.2 MiB/s
Operation completed over 1 objects/125.3 MiB.

INFO - [ResultsDownloader][download_with_gsutil_007] ✅ 文件下载成功
INFO - [ResultsDownloader][download_with_gsutil_009] - 文件大小: 0.12 GB (125.30 MB, 131,404,800 bytes)
INFO - [ResultsDownloader][download_with_gsutil_010] - 下载时间: 39.15 秒 (0.7 分钟)
INFO - [ResultsDownloader][download_with_gsutil_011] - 下载速度: 0.003 GB/s (3.20 MB/s)
```

## 🔍 监控方式详解

### 主要方式: curl监控脚本 (推荐)
```bash
# 单次状态查询 (彩色输出，信息完整)
./02_curl_monitor.sh <vertex_job_id>

# 连续监控 (自动检测完成)
./02_curl_monitor.sh <vertex_job_id> --watch

# 自定义监控间隔
./02_curl_monitor.sh <vertex_job_id> --watch 45

# 查看详细信息
./02_curl_monitor.sh <vertex_job_id> --detail

# 显示帮助
./02_curl_monitor.sh --help
```

### 原始curl命令 (高级用户)
```bash
# 快速状态检查
curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/<vertex_job_id>" \
  | jq -r '.state'

# 详细任务信息
curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/<vertex_job_id>" \
  | jq '{state: .state, completionStats: .completionStats, createTime: .createTime, endTime: .endTime, outputInfo: .outputInfo}'
```

## 📋 常用命令速查

### 任务提交
```bash
# 基本提交
python3 01_submit_batch.py input.jsonl

# 自定义任务名
python3 01_submit_batch.py input.jsonl --job-name my_custom_job

# 保存任务信息
python3 01_submit_batch.py input.jsonl > task_info.json
```

### 任务监控 (curl API)
```bash
# 使用curl监控脚本 (推荐)
./02_curl_monitor.sh <vertex_job_id>

# 连续监控
./02_curl_monitor.sh <vertex_job_id> --watch

# 原始curl命令
curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/<vertex_job_id>" \
  | jq -r '.state'
```

### 结果下载
```bash
# 获取输出路径 (使用curl API)
OUTPUT_URI=$(curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/<vertex_job_id>" \
  | jq -r '.outputInfo.gcsOutputDirectory')

# 下载结果
gcloud storage cp $OUTPUT_URI/predictions.jsonl ./results/
```

### 结果分析
```bash
# 提取结果
python3 04_batch_results_extractor.py predictions.jsonl --output-dir ./extracted

# 分析结果
python3 04_batch_results_analyzer.py predictions.jsonl

# 查看提取的简化结果
cat ./extracted/simplified_responses.json | jq '.[0:3]'
```

## � 最佳实践

### curl API优先工作流程
1. **任务提交**: 使用Python脚本的完整功能
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
   python3 01_submit_batch.py input.jsonl --job-name word_filtering_20250709
   ```

2. **任务信息保存**: 保存vertex_job_id用于监控
   ```bash
   python3 01_submit_batch.py input.jsonl > task_info.json
   VERTEX_JOB_ID=$(cat task_info.json | jq -r '.vertex_job_id')
   ```

3. **高速监控**: 使用curl + API直接查询，最快最稳定
   ```bash
   ./02_curl_monitor.sh $VERTEX_JOB_ID --watch
   ```

4. **可靠下载**: 使用curl获取路径，gcloud下载文件
   ```bash
   OUTPUT_URI=$(curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
     "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/$VERTEX_JOB_ID" \
     | jq -r '.outputInfo.gcsOutputDirectory')
   gcloud storage cp $OUTPUT_URI/predictions.jsonl ./results/
   ```

5. **结果分析**: 使用Python工具进行数据处理
   ```bash
   python3 04_batch_results_extractor.py predictions.jsonl --output-dir ./results
   ```

## �🔧 设计优势

### curl API优势
- ✅ **最高稳定性**: 避免gcloud CLI和Python SDK的各种问题
- ✅ **最快响应**: 直接API调用，无中间层延迟
- ✅ **简单部署**: 只需curl和jq，无复杂依赖
- ✅ **认证管理**: 使用gcloud管理认证令牌
- ✅ **官方API**: 直接调用Google官方Vertex AI API

### Python脚本专注
- ✅ **任务提交**: 完整的文件验证和任务创建
- ✅ **结果处理**: 强大的数据分析和提取能力
- ✅ **JSON输出**: 结构化输出便于自动化

## 🚨 故障排除

### 认证问题
```bash
# 问题: gcloud auth print-access-token 返回空
# 解决: 重新设置认证
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
gcloud auth activate-service-account --key-file="$GOOGLE_APPLICATION_CREDENTIALS"

# 问题: 权限不足
# 解决: 检查服务账号权限
gcloud projects get-iam-policy magic-sw-465214 --flatten="bindings[].members" --filter="bindings.members:*business-sa*"
```

### 监控问题
```bash
# 问题: curl命令返回错误
# 解决: 检查任务ID格式
echo "任务ID应该是纯数字: 2893352839763984384"

# 问题: jq命令不存在
# 解决: 安装jq
brew install jq  # macOS
```

### 下载问题
```bash
# 问题: 找不到输出文件
# 解决: 检查任务是否完成
./02_curl_monitor.sh $VERTEX_JOB_ID

# 问题: gcloud storage权限不足
# 解决: 检查存储桶权限
gcloud storage ls gs://senseword-batch-processing/
```

## 📊 与之前版本对比

| 特性 | v5.0 curl API | v4.0 gcloud CLI | v3.0 Python SDK | v2.0 有状态 |
|------|---------------|----------------|----------------|-------------|
| **监控稳定性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **响应速度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| **部署复杂度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **维护成本** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **脚本数量** | 4个 | 4个 | 5个 | 6个+ |
| **状态管理** | 无状态 | 无状态 | 无状态 | 有状态文件 |

## 🎯 使用建议

### 日常使用
1. **提交任务**: 使用Python脚本的完整功能
2. **监控任务**: 使用curl API确保最高稳定性和速度
3. **下载结果**: 使用curl获取路径，gcloud下载文件
4. **分析结果**: 使用Python工具进行数据处理

### 自动化脚本
```bash
#!/bin/bash
# 完整的批处理工作流程脚本

set -e

INPUT_FILE="$1"
JOB_NAME="$2"

echo "🚀 开始批处理工作流程..."

# 前置步骤: 设置认证
echo "🔐 设置Google Cloud认证..."
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"

# 验证认证
if ! gcloud auth print-access-token >/dev/null 2>&1; then
    echo "❌ 认证失败，请检查GOOGLE_APPLICATION_CREDENTIALS设置"
    exit 1
fi
echo "✅ 认证验证成功"

# 步骤1: 提交任务
echo "📤 提交批处理任务..."
python3 01_submit_batch.py "$INPUT_FILE" --job-name "$JOB_NAME" > task_info.json
VERTEX_JOB_ID=$(cat task_info.json | jq -r '.vertex_job_id')
echo "✅ 任务已提交，Vertex Job ID: $VERTEX_JOB_ID"

# 步骤2: 监控任务 (curl API)
echo "👀 开始监控任务..."
./02_curl_monitor.sh "$VERTEX_JOB_ID" --watch

# 步骤3: 下载结果
echo "📥 下载结果..."
OUTPUT_URI=$(curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/$VERTEX_JOB_ID" \
  | jq -r '.outputInfo.gcsOutputDirectory')
mkdir -p ./results
gcloud storage cp "$OUTPUT_URI/predictions.jsonl" ./results/

# 步骤4: 分析结果
echo "📊 分析结果..."
python3 04_batch_results_extractor.py ./results/predictions.jsonl --output-dir ./results/extracted
python3 04_batch_results_analyzer.py ./results/predictions.jsonl

echo "🎉 批处理工作流程完成！"
```

## 🔄 升级指南

### 升级到v5.0 curl API优先版本

#### 主要变化
1. **监控方式变更**:
   - 移除Python SDK和gcloud CLI监控脚本
   - 采用curl + Vertex AI API作为主要监控方式
   - 实现最高的监控稳定性和响应速度

2. **认证要求**:
   - 必须设置GOOGLE_APPLICATION_CREDENTIALS环境变量
   - 使用gcloud管理认证令牌

#### 迁移步骤
1. **设置认证** (新增要求):
   ```bash
   export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/.config/gcloud-keys/business-sa.json"
   ```

2. **监控任务** (curl API):
   ```bash
   ./02_curl_monitor.sh $VERTEX_JOB_ID --watch
   ```

3. **下载结果** (curl + gcloud):
   ```bash
   OUTPUT_URI=$(curl -s -H "Authorization: Bearer $(gcloud auth print-access-token)" \
     "https://us-central1-aiplatform.googleapis.com/v1/projects/189763113180/locations/us-central1/batchPredictionJobs/$VERTEX_JOB_ID" \
     | jq -r '.outputInfo.gcsOutputDirectory')
   gcloud storage cp $OUTPUT_URI/predictions.jsonl ./results/
   ```

#### 优势
- **最高稳定性**: curl + API避免gcloud CLI和Python SDK的各种问题
- **最快响应**: 直接API调用，无中间层延迟
- **最简部署**: 只需curl和jq，无复杂依赖
- **最好维护**: 最少的脚本文件和最简单的逻辑

## 📚 相关文档

- **01_submit_batch_hybrid.py**: 混合架构批处理任务提交脚本 (gsutil上传)
- **02_curl_monitor.sh**: curl API监控脚本，支持多种监控模式
- **03_download_results_gsutil.py**: gsutil结果下载器 (实时进度显示)
- **04_batch_results_extractor.py**: 🆕 智能结果提取器 (支持重试机制)
- **Google Cloud Vertex AI API**: 官方API文档
- **SenseWord项目文档**: 完整的项目使用指南

## 🔄 智能重试机制

### 自动失败检测与重试文件生成

`04_batch_results_extractor.py` 现在支持智能重试机制，能够：

1. **检测多种失败类型**：
   - 🚫 JSON解析失败
   - 📭 缺少响应字段
   - 🎯 缺少候选响应
   - 📝 内容解析失败
   - ⚠️ API错误响应

2. **自动生成重试文件**：
   - `ERROR_RETRY_failed_requests.jsonl` - 请求级别失败
   - `ERROR_RETRY_content_parse_failures.jsonl` - 内容解析失败

3. **重试工作流程**：
   ```bash
   # 1. 提取结果并生成重试文件
   python3 04_batch_results_extractor.py predictions.jsonl --output-dir ./results

   # 2. 检查是否有重试文件生成
   ls ./results/ERROR_RETRY_*.jsonl

   # 3. 重新提交失败的请求
   python3 01_submit_batch_hybrid.py ./results/ERROR_RETRY_failed_requests.jsonl \
     --job-name retry-failed-requests

   # 4. 重新提交内容解析失败的请求
   python3 01_submit_batch_hybrid.py ./results/ERROR_RETRY_content_parse_failures.jsonl \
     --job-name retry-content-failures
   ```

4. **重试效果**：
   - 🎯 **精确重试**: 只重新处理真正失败的请求
   - 💰 **成本优化**: 避免重新处理成功的请求
   - 📊 **透明统计**: 详细的失败原因分析
   - 🔄 **循环改进**: 可多次重试直到达到满意的成功率

## 📝 版本更新日志

### v6.1 (2025-07-09) - gsutil优化版 (上传+下载)
- ✅ **新增**: gsutil替代gcloud storage，实时进度条显示
- ✅ **新增**: 无超时限制，支持大文件上传和下载
- ✅ **新增**: 04_download_results_gsutil.py 下载器
- ✅ **新增**: 并行上传优化提示
- ✅ **优化**: 大文件上传体验，975MB文件7分钟成功上传
- ✅ **优化**: 下载过程也显示实时进度和统计信息
- ✅ **验证**: 56,702个单词批处理任务成功提交
- ✅ **实测**: 上传进度 `| [1 files][975.0 MiB/975.0 MiB] 1.9 MiB/s`
- ✅ **实测**: 下载进度 `| [1 files][125.3 MiB/125.3 MiB] 3.2 MiB/s`

### v6.0 (2025-07-09) - 混合架构与工具简化
- ✅ **新增**: 混合架构设计（Python + gcloud + curl）
- ✅ **新增**: 详细的文件上传进度显示
- ✅ **新增**: 上传速度和时间统计
- ✅ **优化**: 避免Python SDK的长时间等待问题
- ✅ **优化**: 更好的用户体验和错误处理
- ✅ **简化**: 03脚本一体化提取和分析功能
- ❌ **移除**: 旧的纯Python SDK提交脚本
- ❌ **移除**: 有问题的04分析器脚本（功能已整合到03脚本）

### v5.0 (2025-07-08) - curl API优先
- ✅ **新增**: curl + Vertex AI API监控方式
- ✅ **新增**: 无状态架构设计
- ✅ **优化**: 最高稳定性和速度

---

**文档版本**: v6.1
**最后更新**: 2025年7月9日
**设计理念**: gsutil混合架构优先，实时进度显示，无超时限制
**维护者**: SenseWord开发团队

**v6.1实际验证**:
- ✅ **大规模任务**: 56,702个单词，975MB文件
- ✅ **上传成功**: 7分钟完成，实时进度条
- ✅ **任务提交**: Vertex AI批处理任务成功提交
- ✅ **用户体验**: 完美的进度显示和统计信息
