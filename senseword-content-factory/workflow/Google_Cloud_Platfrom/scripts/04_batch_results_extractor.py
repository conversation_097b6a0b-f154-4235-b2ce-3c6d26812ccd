#!/usr/bin/env python3
"""
通用批处理结果提取器 - 无状态版本

功能：
1. 从Vertex AI批处理结果JSONL文件中提取响应内容
2. 计算Token使用量和成本统计
3. 保存提取的结果到指定目录
4. 与具体业务逻辑无关，纯粹的数据提取和统计工具
"""

import json
import argparse
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 定价配置 (美元/百万tokens)
PRICING = {
    'input_tokens': 0.15,   # 输入tokens价格
    'output_tokens': 1.25   # 输出tokens价格
}

class BatchResultsExtractor:
    """通用批处理结果提取器"""
    
    def __init__(self, output_dir=None):
        """
        初始化提取器
        
        参数:
        - output_dir: 输出目录路径，如果不指定则使用当前目录
        """
        self.output_dir = Path(output_dir) if output_dir else Path.cwd()
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.extracted_responses = []
        self.failed_requests = []  # 存储失败的原始请求
        self.content_parse_failures = []  # 存储内容解析失败的请求
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'json_parse_failures': 0,
            'missing_response_failures': 0,
            'missing_candidates_failures': 0,
            'content_parse_failures': 0,
            'other_failures': 0,
            'total_input_tokens': 0,
            'total_output_tokens': 0,
            'total_cost': 0.0,
            'processing_start_time': datetime.now().isoformat(),
            'processing_end_time': None
        }
        logger.info(f"INFO - [batch_extractor][__init___001] 通用批处理结果提取器初始化完成，输出目录: {self.output_dir}")
    
    def extract_from_jsonl(self, file_path):
        """
        从批处理结果JSONL文件中提取响应内容
        
        参数:
        - file_path: JSONL文件路径
        """
        file_path = Path(file_path)
        if not file_path.exists():
            logger.error(f"ERROR - [batch_extractor][extract_from_jsonl_001] 文件不存在: {file_path}")
            return False
        
        logger.info(f"INFO - [batch_extractor][extract_from_jsonl_002] 开始提取文件: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            result = json.loads(line.strip())
                            self._extract_single_response(result, line_num, line.strip())
                        except json.JSONDecodeError as e:
                            logger.warning(f"WARN - [batch_extractor][extract_from_jsonl_003] 第{line_num}行JSON解析失败: {e}")
                            self.stats['failed_requests'] += 1
                            self.stats['json_parse_failures'] += 1
                            # 保存失败的原始行
                            self.failed_requests.append({
                                'line_number': line_num,
                                'error_type': 'JSON_PARSE_ERROR',
                                'error_message': str(e),
                                'raw_line': line.strip()
                            })
                        except Exception as e:
                            logger.warning(f"WARN - [batch_extractor][extract_from_jsonl_003] 第{line_num}行处理失败: {e}")
                            self.stats['failed_requests'] += 1
                            self.stats['other_failures'] += 1
                            # 保存失败的原始行
                            self.failed_requests.append({
                                'line_number': line_num,
                                'error_type': 'PROCESSING_ERROR',
                                'error_message': str(e),
                                'raw_line': line.strip()
                            })
            
            self.stats['processing_end_time'] = datetime.now().isoformat()
            logger.info(f"INFO - [batch_extractor][extract_from_jsonl_004] 文件提取完成，共处理 {self.stats['total_requests']} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][extract_from_jsonl_005] 文件读取失败: {e}")
            return False
    
    def _extract_single_response(self, result, line_num, raw_line):
        """提取单个响应记录"""
        self.stats['total_requests'] += 1

        try:
            # 检查是否有响应
            if 'response' not in result:
                logger.debug(f"DEBUG - [batch_extractor][_extract_single_response_001] 第{line_num}行缺少response字段")
                self.stats['failed_requests'] += 1
                self.stats['missing_response_failures'] += 1
                # 保存失败的原始请求
                self.failed_requests.append({
                    'line_number': line_num,
                    'error_type': 'MISSING_RESPONSE',
                    'error_message': '缺少response字段',
                    'raw_line': raw_line
                })
                return

            response = result['response']

            # 检查response是否为空或错误
            if not response or (isinstance(response, dict) and len(response) == 0):
                logger.debug(f"DEBUG - [batch_extractor][_extract_single_response_002] 第{line_num}行response为空")
                self.stats['failed_requests'] += 1
                self.stats['missing_response_failures'] += 1
                # 保存失败的原始请求
                self.failed_requests.append({
                    'line_number': line_num,
                    'error_type': 'EMPTY_RESPONSE',
                    'error_message': 'response字段为空',
                    'raw_line': raw_line
                })
                return

            # 检查是否有错误信息（Vertex AI可能返回错误而不是正常响应）
            if 'error' in response:
                error_info = response['error']
                logger.debug(f"DEBUG - [batch_extractor][_extract_single_response_003] 第{line_num}行包含错误: {error_info}")
                self.stats['failed_requests'] += 1
                self.stats['missing_response_failures'] += 1
                # 保存失败的原始请求
                self.failed_requests.append({
                    'line_number': line_num,
                    'error_type': 'API_ERROR',
                    'error_message': f"API错误: {error_info}",
                    'raw_line': raw_line
                })
                return
            
            # 提取Token使用量
            usage_metadata = response.get('usageMetadata', {})
            input_tokens = usage_metadata.get('promptTokenCount', 0)
            output_tokens = usage_metadata.get('candidatesTokenCount', 0)

            self.stats['total_input_tokens'] += input_tokens
            self.stats['total_output_tokens'] += output_tokens

            # 提取响应内容
            extracted_data = {
                'line_number': line_num,
                'response_id': response.get('responseId', ''),
                'create_time': response.get('createTime', ''),
                'model_version': response.get('modelVersion', ''),
                'input_tokens': input_tokens,
                'output_tokens': output_tokens,
                'candidates': [],
                'usage_metadata': usage_metadata
            }

            # 提取candidates内容
            if 'candidates' in response and response['candidates']:
                for i, candidate in enumerate(response['candidates']):
                    candidate_data = {
                        'index': i,
                        'finish_reason': candidate.get('finishReason', ''),
                        'avg_logprobs': candidate.get('avgLogprobs', 0.0),
                        'content_text': '',
                        'content_parts': []
                    }

                    # 提取content内容
                    if 'content' in candidate and 'parts' in candidate['content']:
                        for part in candidate['content']['parts']:
                            if 'text' in part:
                                candidate_data['content_parts'].append(part['text'])
                                candidate_data['content_text'] += part['text']

                    extracted_data['candidates'].append(candidate_data)

                # 记录成功处理
                self.stats['successful_requests'] += 1
                self.extracted_responses.append(extracted_data)
            else:
                # 缺少candidates
                logger.debug(f"DEBUG - [batch_extractor][_extract_single_response_003] 第{line_num}行缺少candidates")
                self.stats['failed_requests'] += 1
                self.stats['missing_candidates_failures'] += 1
                # 保存失败的原始请求
                self.failed_requests.append({
                    'line_number': line_num,
                    'error_type': 'MISSING_CANDIDATES',
                    'error_message': '缺少candidates字段或candidates为空',
                    'raw_line': raw_line
                })
            
        except Exception as e:
            logger.warning(f"WARN - [batch_extractor][_extract_single_response_004] 第{line_num}行处理异常: {e}")
            self.stats['failed_requests'] += 1
            self.stats['other_failures'] += 1
            # 保存失败的原始请求
            self.failed_requests.append({
                'line_number': line_num,
                'error_type': 'PROCESSING_EXCEPTION',
                'error_message': str(e),
                'raw_line': raw_line
            })
    
    def calculate_costs(self):
        """计算总成本"""
        input_cost = (self.stats['total_input_tokens'] / 1_000_000) * PRICING['input_tokens']
        output_cost = (self.stats['total_output_tokens'] / 1_000_000) * PRICING['output_tokens']
        total_cost = input_cost + output_cost
        
        self.stats['total_cost'] = total_cost
        
        return {
            'input_cost': input_cost,
            'output_cost': output_cost,
            'total_cost': total_cost
        }
    
    def save_failed_requests(self, filename="ERROR_RETRY_failed_requests.jsonl"):
        """保存失败的原始请求到JSONL文件，用于重新提交批处理任务"""
        output_file = self.output_dir / filename

        try:
            retry_count = 0
            with open(output_file, 'w', encoding='utf-8') as f:
                for failed_request in self.failed_requests:
                    # 提取纯request部分
                    pure_request = self._extract_pure_request(failed_request['raw_line'])
                    if pure_request:
                        f.write(pure_request + '\n')
                        retry_count += 1
                    else:
                        # 如果提取失败，记录错误但继续处理
                        logger.warning(f"WARN - [batch_extractor][save_failed_requests_001] 第{failed_request['line_number']}行request提取失败")

            logger.info(f"INFO - [batch_extractor][save_failed_requests_002] 失败请求已保存到: {output_file}")
            logger.info(f"INFO - [batch_extractor][save_failed_requests_003] 可重试请求数量: {retry_count}")
            return str(output_file)

        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][save_failed_requests_004] 保存失败: {e}")
            return None

    def save_content_parse_failures(self, filename="ERROR_RETRY_content_parse_failures.jsonl"):
        """保存内容解析失败的原始请求到JSONL文件，用于重新提交批处理任务"""
        if not self.content_parse_failures:
            logger.info(f"INFO - [batch_extractor][save_content_parse_failures_001] 没有内容解析失败的请求")
            return None

        output_file = self.output_dir / filename

        try:
            retry_count = 0
            with open(output_file, 'w', encoding='utf-8') as f:
                for failed_content in self.content_parse_failures:
                    # 从响应数据中重构原始请求
                    response_data = failed_content['response_data']

                    # 尝试从原始行中提取请求（如果有的话）
                    # 这里我们需要重构原始请求，因为我们只有响应数据
                    # 实际上，我们需要从原始JSONL文件中找到对应的请求
                    # 为了简化，我们先记录这些失败的行号，让用户手动处理
                    retry_request = {
                        "request": {
                            "contents": [
                                {
                                    "role": "user",
                                    "parts": [
                                        {
                                            "text": f"重新处理第{response_data['line_number']}行的请求，原始响应解析失败"
                                        }
                                    ]
                                }
                            ]
                        }
                    }

                    json.dump(retry_request, f, ensure_ascii=False)
                    f.write('\n')
                    retry_count += 1

            logger.info(f"INFO - [batch_extractor][save_content_parse_failures_002] 内容解析失败请求已保存到: {output_file}")
            logger.info(f"INFO - [batch_extractor][save_content_parse_failures_003] 可重试请求数量: {retry_count}")
            logger.warning(f"WARN - [batch_extractor][save_content_parse_failures_004] 注意：这些是内容解析失败的请求，需要手动检查原始请求内容")
            return str(output_file)

        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][save_content_parse_failures_005] 保存失败: {e}")
            return None

    def _extract_pure_request(self, raw_line):
        """
        从包含response的原始行中提取纯request部分，并重新包装为Vertex AI格式

        参数:
            raw_line: 包含request和response的原始JSON行

        返回:
            包含request字段的JSON字符串，如果提取失败返回None
        """
        try:
            # 解析原始JSON
            full_data = json.loads(raw_line)

            # 检查是否包含request字段
            if 'request' not in full_data:
                logger.warning(f"WARN - [batch_extractor][_extract_pure_request_001] 原始行中缺少request字段")
                return None

            # 提取request部分
            request_data = full_data['request']

            # 重新包装为Vertex AI需要的格式
            vertex_format = {
                "request": request_data
            }

            # 将完整对象转换为JSON字符串
            vertex_json = json.dumps(vertex_format, ensure_ascii=False, separators=(',', ':'))

            return vertex_json

        except json.JSONDecodeError as e:
            logger.warning(f"WARN - [batch_extractor][_extract_pure_request_002] JSON解析失败: {e}")
            return None
        except Exception as e:
            logger.warning(f"WARN - [batch_extractor][_extract_pure_request_003] request提取失败: {e}")
            return None

    def save_simplified_responses(self, filename="simplified_responses.json"):
        """保存极简化的响应到JSON数组文件，只包含解析后的content_text JSON对象"""
        output_file = self.output_dir / filename

        try:
            simplified_responses = []
            json_parse_failures = 0

            for response in self.extracted_responses:
                if response['candidates']:
                    content_text = response['candidates'][0]['content_text']

                    # 尝试解析content_text为JSON对象
                    try:
                        # 移除换行符并解析JSON
                        cleaned_content = content_text.replace('\n', '').strip()
                        parsed_content = json.loads(cleaned_content)
                        simplified_responses.append(parsed_content)
                    except json.JSONDecodeError as e:
                        # 如果解析失败，记录原始文本并加入重试队列
                        json_parse_failures += 1
                        self.stats['content_parse_failures'] += 1
                        logger.warning(f"WARN - [batch_extractor][save_simplified_responses_001] 第{response['line_number']}行JSON解析失败: {e}")

                        # 将内容解析失败的请求加入重试队列
                        self.content_parse_failures.append({
                            'line_number': response['line_number'],
                            'error_type': 'CONTENT_JSON_PARSE_FAILURE',
                            'error_message': f"内容JSON解析失败: {e}",
                            'response_data': response
                        })

                        simplified_responses.append({
                            "error": "JSON解析失败",
                            "error_details": str(e),
                            "line_number": response['line_number'],
                            "raw_content": content_text[:500] + "..." if len(content_text) > 500 else content_text
                        })
                else:
                    # 没有candidates的情况
                    simplified_responses.append({
                        "error": "无响应内容",
                        "line_number": response['line_number'],
                        "raw_content": ""
                    })

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(simplified_responses, f, ensure_ascii=False, indent=2)

            logger.info(f"INFO - [batch_extractor][save_simplified_responses_002] 极简化响应已保存到: {output_file}")
            logger.info(f"INFO - [batch_extractor][save_simplified_responses_003] 成功解析: {len(simplified_responses) - json_parse_failures}, JSON解析失败: {json_parse_failures}")
            return str(output_file)

        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][save_simplified_responses_004] 保存失败: {e}")
            return None
    
    def save_statistics(self, filename="extraction_stats.json"):
        """保存统计信息到JSON文件"""
        costs = self.calculate_costs()

        stats_with_costs = {
            **self.stats,
            'costs': costs,
            'pricing_info': PRICING
        }

        output_file = self.output_dir / filename

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(stats_with_costs, f, ensure_ascii=False, indent=2)

            logger.info(f"INFO - [batch_extractor][save_statistics_001] 统计信息已保存到: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][save_statistics_002] 保存失败: {e}")
            return None

    def save_markdown_report(self, filename="extraction_report.md"):
        """保存Markdown格式的可读性报告"""
        costs = self.calculate_costs()
        success_rate = (self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100
        avg_cost_per_request = costs['total_cost'] / max(self.stats['successful_requests'], 1)

        # 计算处理时间
        start_time = datetime.fromisoformat(self.stats['processing_start_time'])
        end_time = datetime.fromisoformat(self.stats['processing_end_time']) if self.stats['processing_end_time'] else datetime.now()
        processing_duration = end_time - start_time

        markdown_content = f"""# 📊 批处理结果提取报告

## 📈 处理统计

| 指标 | 数值 | 百分比 |
|------|------|--------|
| 📝 总请求数 | {self.stats['total_requests']:,} | 100.0% |
| ✅ 成功提取 | {self.stats['successful_requests']:,} | {success_rate:.1f}% |
| ❌ 失败提取 | {self.stats['failed_requests']:,} | {100-success_rate:.1f}% |

### 🔍 失败原因分析

| 失败类型 | 数量 | 占失败总数比例 |
|----------|------|----------------|
| 🚫 JSON解析失败 | {self.stats['json_parse_failures']:,} | {(self.stats['json_parse_failures']/max(self.stats['failed_requests'], 1)*100):.1f}% |
| 📭 缺少响应字段 | {self.stats['missing_response_failures']:,} | {(self.stats['missing_response_failures']/max(self.stats['failed_requests'], 1)*100):.1f}% |
| 🎯 缺少候选响应 | {self.stats['missing_candidates_failures']:,} | {(self.stats['missing_candidates_failures']/max(self.stats['failed_requests'], 1)*100):.1f}% |
| 📝 内容解析失败 | {self.stats['content_parse_failures']:,} | {(self.stats['content_parse_failures']/max(self.stats['total_requests'], 1)*100):.1f}% |
| ⚠️ 其他处理错误 | {self.stats['other_failures']:,} | {(self.stats['other_failures']/max(self.stats['failed_requests'], 1)*100):.1f}% |

## 💰 成本分析

| 项目 | Token数量 | 成本 (USD) |
|------|-----------|------------|
| 📥 输入Tokens | {self.stats['total_input_tokens']:,} | ${costs['input_cost']:.4f} |
| 📤 输出Tokens | {self.stats['total_output_tokens']:,} | ${costs['output_cost']:.4f} |
| 💵 **总成本** | **{self.stats['total_input_tokens'] + self.stats['total_output_tokens']:,}** | **${costs['total_cost']:.4f}** |

### 💡 成本详情

- 🔢 平均每请求成本: **${avg_cost_per_request:.6f}**
- 📊 输入Token单价: ${PRICING['input_tokens']}/百万tokens
- 📊 输出Token单价: ${PRICING['output_tokens']}/百万tokens

## ⏱️ 处理时间

| 时间点 | 时间戳 |
|--------|--------|
| 🚀 开始时间 | {self.stats['processing_start_time']} |
| 🏁 结束时间 | {self.stats['processing_end_time']} |
| ⏳ 处理耗时 | {str(processing_duration).split('.')[0]} |

## 📁 输出文件

| 文件类型 | 文件名 | 描述 |
|----------|--------|------|
| 📋 简化数据 | `simplified_responses.json` | 只包含解析后的content_text JSON对象数组 |
| 📖 可读报告 | `extraction_report.md` | 本Markdown格式报告 |
| 🚨 失败请求 | `ERROR_RETRY_failed_requests.jsonl` | 失败的原始请求，可用于重新提交批处理任务 |
| 🔄 内容解析失败 | `ERROR_RETRY_content_parse_failures.jsonl` | 内容解析失败的请求，可用于重新提交 |

## 🎯 数据质量

- ✅ **数据完整性**: {success_rate:.1f}% 的请求成功提取
- 🔍 **平均输入长度**: {self.stats['total_input_tokens'] / max(self.stats['successful_requests'], 1):.0f} tokens/请求
- 📝 **平均输出长度**: {self.stats['total_output_tokens'] / max(self.stats['successful_requests'], 1):.0f} tokens/请求

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""

        output_file = self.output_dir / filename

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            logger.info(f"INFO - [batch_extractor][save_markdown_report_001] Markdown报告已保存到: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"ERROR - [batch_extractor][save_markdown_report_002] 保存失败: {e}")
            return None
    
    def generate_summary_report(self):
        """生成摘要报告"""
        costs = self.calculate_costs()
        
        success_rate = (self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100
        avg_cost_per_request = costs['total_cost'] / max(self.stats['successful_requests'], 1)
        
        report = f"""
📊 批处理结果提取摘要报告
{'=' * 50}

📈 处理统计
  总请求数: {self.stats['total_requests']:,}
  成功提取: {self.stats['successful_requests']:,} ({success_rate:.1f}%)
  失败提取: {self.stats['failed_requests']:,} ({100-success_rate:.1f}%)

💰 成本分析
  输入Tokens: {self.stats['total_input_tokens']:,} (${costs['input_cost']:.4f})
  输出Tokens: {self.stats['total_output_tokens']:,} (${costs['output_cost']:.4f})
  总成本: ${costs['total_cost']:.4f}
  平均每请求成本: ${avg_cost_per_request:.6f}

📁 输出文件
  提取结果: {self.output_dir}/extracted_responses.jsonl
  统计信息: {self.output_dir}/extraction_stats.json

⏱️  处理时间
  开始时间: {self.stats['processing_start_time']}
  结束时间: {self.stats['processing_end_time']}
"""
        return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="通用批处理结果提取器")
    parser.add_argument("jsonl_file", help="批处理结果JSONL文件路径")
    parser.add_argument("--output-dir", "-o", help="输出目录路径", default="./extracted_results")
    parser.add_argument("--responses-file", help="提取的响应文件名", default="extracted_responses.jsonl")
    parser.add_argument("--stats-file", help="统计信息文件名", default="extraction_stats.json")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 初始化提取器
    extractor = BatchResultsExtractor(args.output_dir)
    
    # 提取文件
    if not extractor.extract_from_jsonl(args.jsonl_file):
        logger.error("ERROR - [main] 文件提取失败")
        return 1
    
    # 保存结果
    extractor.save_simplified_responses("simplified_responses.json")
    extractor.save_failed_requests("ERROR_RETRY_failed_requests.jsonl")
    extractor.save_content_parse_failures("ERROR_RETRY_content_parse_failures.jsonl")
    extractor.save_markdown_report("extraction_report.md")

    # 生成并显示报告
    report = extractor.generate_summary_report()
    print(report)
    
    return 0

if __name__ == "__main__":
    exit(main())
