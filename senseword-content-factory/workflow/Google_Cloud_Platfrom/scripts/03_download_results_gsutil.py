#!/usr/bin/env python3
"""
批处理结果下载工具 (gsutil版本)

功能：
- 使用curl API获取任务输出路径
- 使用gsutil下载结果文件（实时进度显示）
- 支持大文件下载，无超时限制
- 详细的下载统计信息

使用方法：
    python3 05_download_results_gsutil.py <vertex_job_id> --output-dir ./results

作者：AI Assistant
日期：2025-07-09
版本：v1.0
"""

import json
import os
import subprocess
import logging
import argparse
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 项目配置
PROJECT_NUMBER = "189763113180"
LOCATION = "us-central1"

class ResultsDownloader:
    """批处理结果下载器"""
    
    def __init__(self, vertex_job_id, output_dir):
        """
        初始化下载器
        
        参数:
            vertex_job_id: Vertex AI任务ID
            output_dir: 输出目录
        """
        self.vertex_job_id = vertex_job_id
        self.output_dir = Path(output_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        logger.info("INFO - [ResultsDownloader][__init__001] 批处理结果下载器初始化完成")
        logger.info(f"INFO - [ResultsDownloader][__init__002] Vertex任务ID: {self.vertex_job_id}")
        logger.info(f"INFO - [ResultsDownloader][__init__003] 输出目录: {self.output_dir}")
    
    def get_output_uri_with_curl(self):
        """
        使用curl API获取任务输出路径
        
        返回:
            (success, output_uri, job_state)
        """
        try:
            logger.info("INFO - [ResultsDownloader][get_output_uri_with_curl_001] 使用curl API获取输出路径")
            
            # 获取访问令牌
            token_result = subprocess.run(
                ["gcloud", "auth", "print-access-token"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if token_result.returncode != 0:
                logger.error("ERROR - [ResultsDownloader][get_output_uri_with_curl_002] 获取访问令牌失败")
                return False, None, None
            
            access_token = token_result.stdout.strip()
            
            # API端点
            api_url = f"https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT_NUMBER}/locations/{LOCATION}/batchPredictionJobs/{self.vertex_job_id}"
            
            # 使用curl获取任务信息
            curl_cmd = [
                "curl", "-s",
                "-H", f"Authorization: Bearer {access_token}",
                api_url
            ]
            
            logger.info("INFO - [ResultsDownloader][get_output_uri_with_curl_003] 发送API请求...")
            result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                try:
                    response_data = json.loads(result.stdout)
                    
                    job_state = response_data.get("state", "UNKNOWN")
                    output_info = response_data.get("outputInfo", {})
                    output_uri = output_info.get("gcsOutputDirectory", "")
                    
                    logger.info(f"INFO - [ResultsDownloader][get_output_uri_with_curl_004] 任务状态: {job_state}")
                    logger.info(f"INFO - [ResultsDownloader][get_output_uri_with_curl_005] 输出路径: {output_uri}")
                    
                    if output_uri:
                        return True, output_uri, job_state
                    else:
                        logger.error("ERROR - [ResultsDownloader][get_output_uri_with_curl_006] 未找到输出路径")
                        return False, None, job_state
                        
                except json.JSONDecodeError as e:
                    logger.error(f"ERROR - [ResultsDownloader][get_output_uri_with_curl_007] API响应解析失败: {e}")
                    return False, None, None
            else:
                logger.error("ERROR - [ResultsDownloader][get_output_uri_with_curl_008] curl请求失败")
                logger.error(f"ERROR - [ResultsDownloader][get_output_uri_with_curl_009] 错误信息: {result.stderr}")
                return False, None, None
                
        except subprocess.TimeoutExpired:
            logger.error("ERROR - [ResultsDownloader][get_output_uri_with_curl_010] API请求超时")
            return False, None, None
        except Exception as e:
            logger.error(f"ERROR - [ResultsDownloader][get_output_uri_with_curl_011] 获取输出路径失败: {e}")
            return False, None, None
    
    def download_with_gsutil(self, gcs_uri, local_path):
        """
        使用gsutil下载文件（实时进度显示）
        
        参数:
            gcs_uri: GCS文件URI
            local_path: 本地保存路径
            
        返回:
            (success, file_size, download_time)
        """
        try:
            logger.info("INFO - [ResultsDownloader][download_with_gsutil_001] 开始使用gsutil下载文件")
            logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_002] GCS URI: {gcs_uri}")
            logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_003] 本地路径: {local_path}")
            
            # 确保输出目录存在
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 使用gsutil cp命令下载，显示实时进度
            # 添加-o选项跳过完整性检查，避免crcmod性能问题
            cmd = [
                "gsutil", "-o", "GSUtil:check_hashes=never", "cp",
                gcs_uri,
                str(local_path)
            ]
            
            logger.info("INFO - [ResultsDownloader][download_with_gsutil_004] 执行gsutil下载命令...")
            logger.info("INFO - [ResultsDownloader][download_with_gsutil_005] 📥 下载中（gsutil会显示实时进度）...")
            logger.info("INFO - [ResultsDownloader][download_with_gsutil_006] 💡 提示：gsutil会显示进度条，包括百分比、速度和预计剩余时间")
            
            # 记录开始时间
            import time
            start_time = time.time()
            
            # 使用gsutil下载，不设置超时，让gsutil自己处理
            # gsutil会显示实时进度条，包括百分比、速度和预计剩余时间
            # 不捕获输出，让进度条直接显示在终端
            result = subprocess.run(cmd)
            
            end_time = time.time()
            download_duration = end_time - start_time
            
            if result.returncode == 0:
                # 获取下载文件大小
                file_size = local_path.stat().st_size if local_path.exists() else 0
                file_size_mb = file_size / (1024 * 1024)
                file_size_gb = file_size_mb / 1024
                
                # 计算下载速度
                download_speed_mbps = file_size_mb / download_duration if download_duration > 0 else 0
                download_speed_gbps = file_size_gb / download_duration if download_duration > 0 else 0
                
                logger.info("INFO - [ResultsDownloader][download_with_gsutil_007] ✅ 文件下载成功")
                logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_008] 📊 下载统计:")
                logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_009]   - 文件大小: {file_size_gb:.2f} GB ({file_size_mb:.2f} MB, {file_size:,} bytes)")
                logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_010]   - 下载时间: {download_duration:.2f} 秒 ({download_duration/60:.1f} 分钟)")
                logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_011]   - 下载速度: {download_speed_gbps:.3f} GB/s ({download_speed_mbps:.2f} MB/s)")
                logger.info(f"INFO - [ResultsDownloader][download_with_gsutil_012] 本地文件: {local_path}")
                
                return True, file_size, download_duration
            else:
                logger.error("ERROR - [ResultsDownloader][download_with_gsutil_013] 文件下载失败")
                logger.error(f"ERROR - [ResultsDownloader][download_with_gsutil_014] 返回码: {result.returncode}")
                return False, 0, 0
                
        except Exception as e:
            logger.error(f"ERROR - [ResultsDownloader][download_with_gsutil_015] 下载失败: {e}")
            return False, 0, 0
    
    def download_results(self):
        """
        下载批处理结果
        
        返回:
            下载信息字典
        """
        try:
            logger.info("=" * 60)
            logger.info("INFO - [ResultsDownloader][download_results_001] 🚀 开始下载批处理结果")
            logger.info("=" * 60)
            
            # 步骤1: 获取输出路径
            logger.info("INFO - [ResultsDownloader][download_results_002] 步骤1: 获取任务输出路径")
            success, output_uri, job_state = self.get_output_uri_with_curl()
            if not success:
                raise RuntimeError("获取输出路径失败")
            
            if job_state != "JOB_STATE_SUCCEEDED":
                logger.warning(f"WARN - [ResultsDownloader][download_results_003] 任务状态: {job_state}")
                if job_state in ["JOB_STATE_PENDING", "JOB_STATE_RUNNING"]:
                    raise RuntimeError(f"任务尚未完成，当前状态: {job_state}")
                elif job_state == "JOB_STATE_FAILED":
                    raise RuntimeError("任务执行失败")
            
            # 步骤2: 构建下载路径
            logger.info("INFO - [ResultsDownloader][download_results_004] 步骤2: 构建下载路径")
            predictions_uri = f"{output_uri.rstrip('/')}/predictions.jsonl"
            local_file = self.output_dir / "predictions.jsonl"
            
            # 步骤3: 使用gsutil下载
            logger.info("INFO - [ResultsDownloader][download_results_005] 步骤3: 使用gsutil下载文件")
            download_success, file_size, download_time = self.download_with_gsutil(predictions_uri, local_file)
            if not download_success:
                raise RuntimeError("文件下载失败")
            
            # 构建下载信息
            download_info = {
                "timestamp": self.timestamp,
                "vertex_job_id": self.vertex_job_id,
                "job_state": job_state,
                "output_uri": output_uri,
                "predictions_uri": predictions_uri,
                "local_file": str(local_file),
                "file_size": file_size,
                "download_time": download_time,
                "download_speed_mbps": (file_size / (1024 * 1024)) / download_time if download_time > 0 else 0
            }
            
            logger.info("=" * 60)
            logger.info("INFO - [ResultsDownloader][download_results_006] ✅ 批处理结果下载完成")
            logger.info("=" * 60)
            
            return download_info
            
        except Exception as e:
            logger.error(f"ERROR - [ResultsDownloader][download_results_007] 下载失败: {e}")
            raise

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批处理结果下载工具 (gsutil版本)")
    parser.add_argument("vertex_job_id", help="Vertex AI任务ID")
    parser.add_argument("--output-dir", default="./results", help="输出目录 (默认: ./results)")
    
    args = parser.parse_args()
    
    try:
        downloader = ResultsDownloader(args.vertex_job_id, args.output_dir)
        download_info = downloader.download_results()
        
        # 输出下载信息（JSON格式）
        print(json.dumps(download_info, ensure_ascii=False, indent=2))
        
        return 0
        
    except Exception as e:
        logger.error(f"ERROR - [main] 程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
