#!/usr/bin/env python3
"""
字段提取函数模块
用于从contentJson中提取各种字段数据，支持D1数据库迁移

基于实际contentJson结构设计：
{
  "word": "aback",
  "metadata": {
    "wordFrequency": "B2",
    "relatedConcepts": ["surprise", "shock", "disconcert", "bewilder", "retreat"],
    "culturalRiskRegions": []
  },
  "content": {
    "difficulty": "B2",
    "coreDefinition": "副词。感到惊讶或震惊地；向后地（主要用于短语 taken aback ）",
    "contextualExplanation": {...}
  }
}
"""

import json
import logging
from typing import Optional, List, Any, Dict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentJsonExtractor:
    """ContentJson字段提取器"""
    
    def __init__(self):
        self.extraction_stats = {
            'total_processed': 0,
            'successful_extractions': 0,
            'failed_extractions': 0,
            'field_success_rates': {}
        }
    
    def extract_core_definition(self, content_json: str) -> Optional[str]:
        """
        提取核心定义
        路径: content.coreDefinition
        """
        try:
            data = json.loads(content_json)
            core_definition = data.get('content', {}).get('coreDefinition')
            
            if core_definition:
                self._update_field_stats('coreDefinition', True)
                return core_definition
            else:
                self._update_field_stats('coreDefinition', False)
                logger.warning(f"coreDefinition not found in content")
                return None
                
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            self._update_field_stats('coreDefinition', False)
            logger.error(f"Error extracting coreDefinition: {e}")
            return None
    
    def extract_difficulty(self, content_json: str) -> Optional[str]:
        """
        提取难度等级
        路径: content.difficulty
        """
        try:
            data = json.loads(content_json)
            difficulty = data.get('content', {}).get('difficulty')
            
            if difficulty:
                self._update_field_stats('difficulty', True)
                return difficulty
            else:
                self._update_field_stats('difficulty', False)
                logger.warning(f"difficulty not found in content")
                return None
                
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            self._update_field_stats('difficulty', False)
            logger.error(f"Error extracting difficulty: {e}")
            return None
    
    def extract_word_frequency(self, content_json: str) -> Optional[str]:
        """
        提取词频等级
        路径: metadata.wordFrequency
        """
        try:
            data = json.loads(content_json)
            word_frequency = data.get('metadata', {}).get('wordFrequency')
            
            if word_frequency:
                self._update_field_stats('wordFrequency', True)
                return word_frequency
            else:
                self._update_field_stats('wordFrequency', False)
                logger.warning(f"wordFrequency not found in metadata")
                return None
                
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            self._update_field_stats('wordFrequency', False)
            logger.error(f"Error extracting wordFrequency: {e}")
            return None
    
    def extract_related_concepts(self, content_json: str) -> Optional[str]:
        """
        提取相关概念
        路径: metadata.relatedConcepts
        返回: JSON字符串格式的数组
        """
        try:
            data = json.loads(content_json)
            related_concepts = data.get('metadata', {}).get('relatedConcepts')
            
            if related_concepts and isinstance(related_concepts, list):
                self._update_field_stats('relatedConcepts', True)
                return json.dumps(related_concepts, ensure_ascii=False)
            else:
                self._update_field_stats('relatedConcepts', False)
                logger.warning(f"relatedConcepts not found or not a list in metadata")
                return None
                
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            self._update_field_stats('relatedConcepts', False)
            logger.error(f"Error extracting relatedConcepts: {e}")
            return None
    
    def extract_cultural_risk_regions(self, content_json: str) -> Optional[str]:
        """
        提取文化风险区域
        路径: metadata.culturalRiskRegions
        返回: JSON字符串格式的数组
        """
        try:
            data = json.loads(content_json)
            cultural_risk_regions = data.get('metadata', {}).get('culturalRiskRegions')
            
            if cultural_risk_regions is not None and isinstance(cultural_risk_regions, list):
                self._update_field_stats('culturalRiskRegions', True)
                return json.dumps(cultural_risk_regions, ensure_ascii=False)
            else:
                self._update_field_stats('culturalRiskRegions', False)
                logger.warning(f"culturalRiskRegions not found or not a list in metadata")
                return None
                
        except (json.JSONDecodeError, KeyError, AttributeError) as e:
            self._update_field_stats('culturalRiskRegions', False)
            logger.error(f"Error extracting culturalRiskRegions: {e}")
            return None
    
    def extract_all_fields(self, content_json: str) -> Dict[str, Optional[str]]:
        """
        提取所有字段
        返回: 包含所有提取字段的字典
        """
        self.extraction_stats['total_processed'] += 1
        
        extracted_fields = {
            'coreDefinition': self.extract_core_definition(content_json),
            'difficulty': self.extract_difficulty(content_json),
            'frequency': self.extract_word_frequency(content_json),
            'relatedConcepts': self.extract_related_concepts(content_json),
            'culturalRiskRegions': self.extract_cultural_risk_regions(content_json)
        }
        
        # 统计成功提取的字段数量
        successful_fields = sum(1 for value in extracted_fields.values() if value is not None)
        if successful_fields > 0:
            self.extraction_stats['successful_extractions'] += 1
        else:
            self.extraction_stats['failed_extractions'] += 1
        
        return extracted_fields
    
    def _update_field_stats(self, field_name: str, success: bool):
        """更新字段提取统计"""
        if field_name not in self.extraction_stats['field_success_rates']:
            self.extraction_stats['field_success_rates'][field_name] = {'success': 0, 'total': 0}
        
        self.extraction_stats['field_success_rates'][field_name]['total'] += 1
        if success:
            self.extraction_stats['field_success_rates'][field_name]['success'] += 1
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取统计信息"""
        stats = self.extraction_stats.copy()
        
        # 计算成功率
        for field_name, field_stats in stats['field_success_rates'].items():
            if field_stats['total'] > 0:
                field_stats['success_rate'] = field_stats['success'] / field_stats['total']
            else:
                field_stats['success_rate'] = 0.0
        
        return stats
    
    def print_extraction_report(self):
        """打印提取报告"""
        stats = self.get_extraction_stats()
        
        print("\n" + "="*60)
        print("ContentJson字段提取报告")
        print("="*60)
        print(f"总处理记录数: {stats['total_processed']}")
        print(f"成功提取记录数: {stats['successful_extractions']}")
        print(f"失败提取记录数: {stats['failed_extractions']}")
        
        if stats['total_processed'] > 0:
            overall_success_rate = stats['successful_extractions'] / stats['total_processed']
            print(f"总体成功率: {overall_success_rate:.2%}")
        
        print("\n字段级别成功率:")
        print("-" * 40)
        for field_name, field_stats in stats['field_success_rates'].items():
            print(f"{field_name:20}: {field_stats['success']:4d}/{field_stats['total']:4d} ({field_stats['success_rate']:.2%})")
        
        print("="*60)


# 使用示例
if __name__ == "__main__":
    # 测试提取器
    extractor = ContentJsonExtractor()
    
    # 测试数据
    test_content_json = '''
    {
      "word": "aback",
      "metadata": {
        "wordFrequency": "B2",
        "relatedConcepts": ["surprise", "shock", "disconcert", "bewilder", "retreat"],
        "culturalRiskRegions": []
      },
      "content": {
        "difficulty": "B2",
        "coreDefinition": "副词。感到惊讶或震惊地；向后地（主要用于短语 taken aback ）"
      }
    }
    '''
    
    # 提取所有字段
    extracted = extractor.extract_all_fields(test_content_json)
    
    print("提取结果:")
    for field, value in extracted.items():
        print(f"{field}: {value}")
    
    # 打印统计报告
    extractor.print_extraction_report()
