#!/usr/bin/env python3
"""
SenseWord Word Definitions 结构重构迁移脚本
基于TTS迁移脚本架构，实现新表结构的数据迁移

功能：
1. 从本地words_for_publish表读取数据
2. 按照新的字段映射关系进行数据转换
3. 生成批次SQL文件用于D1数据库迁移
4. 支持字段提取、数据验证和错误处理

使用方法：
    python word_definitions_restructure_migration.py --batch-size 50000 --database "senseword-word-db" --auto-confirm
"""

import sqlite3
import json
import argparse
import subprocess
import sys
import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from field_extraction_functions import ContentJsonExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class WordDefinitionsRestructureMigration:
    """Word Definitions表结构重构迁移器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = self.load_config(config_path)
        self.conn = None
        self.extractor = ContentJsonExtractor()
        self.logger = logging.getLogger(__name__)
        
        # 迁移统计
        self.migration_stats = {
            'total_records': 0,
            'successful_mappings': 0,
            'failed_mappings': 0,
            'generated_sql_files': 0
        }
    
    def load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "database": {
                "sqlitePath": "../../../content-assets/01_en/01_zh/sqlite/senseword_content_v4.db"
            },
            "migration": {
                "outputDir": "./migration_sql_files",
                "batchSize": 50000,
                "databaseName": "senseword-word-db"
            }
        }
        
        try:
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                return config
            else:
                self.logger.info(f"配置文件 {config_path} 不存在，使用默认配置")
                return default_config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return default_config
    
    def connect_database(self):
        """连接本地数据库"""
        try:
            db_path = self.config['database']['sqlitePath']
            self.conn = sqlite3.connect(db_path)
            self.conn.row_factory = sqlite3.Row
            self.logger.info(f"✅ 数据库连接成功: {db_path}")
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            sys.exit(1)
    
    def scan_words_for_publish(self) -> List[Dict]:
        """扫描本地words_for_publish表数据"""
        self.logger.info("🔍 开始扫描本地words_for_publish表...")
        
        query = """
        SELECT 
            word, 
            learningLanguage,
            scaffoldingLanguage,
            contentJson,
            partsOfSpeech,
            frequency as local_frequency,
            culturalRiskRegions as local_cultural_risk_regions
        FROM words_for_publish
        ORDER BY word
        """
        
        cursor = self.conn.execute(query)
        records = []
        
        for row in cursor.fetchall():
            record = {
                "word": row["word"],
                "learningLanguage": row["learningLanguage"],
                "scaffoldingLanguage": row["scaffoldingLanguage"],
                "contentJson": row["contentJson"],
                "partsOfSpeech": row["partsOfSpeech"],
                "local_frequency": row["local_frequency"],
                "local_cultural_risk_regions": row["local_cultural_risk_regions"]
            }
            records.append(record)
        
        self.migration_stats['total_records'] = len(records)
        self.logger.info(f"📊 扫描完成，找到 {len(records)} 条记录")
        return records
    
    def map_record_to_d1_format(self, record: Dict) -> Optional[Dict]:
        """将本地记录映射为D1格式"""
        try:
            # 提取contentJson字段
            extracted_fields = self.extractor.extract_all_fields(record["contentJson"])
            
            # 构建D1记录
            d1_record = {
                "word": record["word"],
                "learningLanguage": record["learningLanguage"],  # 修复：使用正确的字段名
                "scaffoldingLanguage": record["scaffoldingLanguage"],
                "contentJson": record["contentJson"],
                "coreDefinition": extracted_fields.get("coreDefinition"),
                "difficulty": extracted_fields.get("difficulty"),
                "frequency": record["local_frequency"] or extracted_fields.get("frequency"),  # 修复：优先本地frequency，fallback JSON
                "relatedConcepts": extracted_fields.get("relatedConcepts"),
                "partsOfSpeech": record["partsOfSpeech"],
                "culturalRiskRegions": extracted_fields.get("culturalRiskRegions") or record["local_cultural_risk_regions"]  # 优先JSON，fallback本地
            }
            
            self.migration_stats['successful_mappings'] += 1
            return d1_record
            
        except Exception as e:
            self.logger.error(f"❌ 记录映射失败 - 单词: {record.get('word', 'unknown')}, 错误: {e}")
            self.migration_stats['failed_mappings'] += 1
            return None
    
    def escape_sql(self, value: str) -> str:
        """SQL字符串转义"""
        if value is None:
            return ""
        return str(value).replace("'", "''")

    def check_sql_statement_size(self, sql: str) -> bool:
        """检查SQL语句是否超过Cloudflare D1的100KB限制"""
        return len(sql.encode('utf-8')) <= 100000  # 100KB limit
    
    def generate_sql_batches(self, records: List[Dict], batch_size: int) -> List[str]:
        """生成SQL批次文件"""
        output_dir = Path(self.config['migration']['outputDir'])
        output_dir.mkdir(exist_ok=True)
        
        total_batches = (len(records) + batch_size - 1) // batch_size
        sql_files = []
        
        self.logger.info(f"📝 开始生成SQL文件，总批次: {total_batches}")
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, len(records))
            batch_records = records[start_idx:end_idx]
            
            # 生成文件名
            filename = f"word_definitions_migration_batch_{batch_num + 1:03d}_of_{total_batches:03d}.sql"
            filepath = output_dir / filename
            sql_files.append(str(filepath))
            
            # 生成SQL内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"-- Word Definitions 结构重构迁移 - 批次 {batch_num + 1}/{total_batches}\n")
                f.write(f"-- 生成时间: {datetime.now().isoformat()}\n")
                f.write(f"-- 记录数量: {len(batch_records)}\n\n")
                
                for record in batch_records:
                    # 映射记录
                    d1_record = self.map_record_to_d1_format(record)
                    if not d1_record:
                        continue
                    
                    # 生成INSERT语句
                    sql = f"""INSERT INTO word_definitions (
    word, learningLanguage, scaffoldingLanguage, contentJson, coreDefinition,
    difficulty, frequency, relatedConcepts, partsOfSpeech, culturalRiskRegions
)
SELECT
    '{self.escape_sql(d1_record['word'])}',
    '{self.escape_sql(d1_record['learningLanguage'])}',
    '{self.escape_sql(d1_record['scaffoldingLanguage'])}',
    '{self.escape_sql(d1_record['contentJson'])}',
    '{self.escape_sql(d1_record['coreDefinition'])}',
    '{self.escape_sql(d1_record['difficulty'])}',
    '{self.escape_sql(d1_record['frequency'])}',
    '{self.escape_sql(d1_record['relatedConcepts'])}',
    '{self.escape_sql(d1_record['partsOfSpeech'])}',
    '{self.escape_sql(d1_record['culturalRiskRegions'])}'
WHERE NOT EXISTS (
    SELECT 1 FROM word_definitions
    WHERE word = '{self.escape_sql(d1_record['word'])}'
    AND learningLanguage = '{self.escape_sql(d1_record['learningLanguage'])}'
    AND scaffoldingLanguage = '{self.escape_sql(d1_record['scaffoldingLanguage'])}'
);

"""

                    # 检查SQL语句大小
                    if not self.check_sql_statement_size(sql):
                        self.logger.warning(f"⚠️ SQL语句过大，跳过单词: {d1_record['word']} (大小: {len(sql.encode('utf-8'))} bytes)")
                        continue

                    f.write(sql)
            
            self.migration_stats['generated_sql_files'] += 1
            self.logger.info(f"✅ 生成批次 {batch_num + 1}: {filename}")
        
        return sql_files
    
    def preview_migration(self, sql_files: List[str]):
        """预览迁移信息"""
        print("\n" + "="*60)
        print("📋 Word Definitions 结构重构迁移预览")
        print("="*60)
        print(f"📊 总记录数: {self.migration_stats['total_records']:,}")
        print(f"📊 成功映射: {self.migration_stats['successful_mappings']:,}")
        print(f"📊 失败映射: {self.migration_stats['failed_mappings']:,}")
        print(f"📊 生成SQL文件: {len(sql_files)} 个")
        print(f"📊 输出目录: {self.config['migration']['outputDir']}")
        
        if sql_files:
            print(f"\n📁 SQL文件列表:")
            for i, sql_file in enumerate(sql_files[:5], 1):
                filename = Path(sql_file).name
                print(f"  {i}. {filename}")
            if len(sql_files) > 5:
                print(f"  ... 还有 {len(sql_files) - 5} 个文件")
        
        # 显示字段提取统计
        print(f"\n📈 字段提取统计:")
        extraction_stats = self.extractor.get_extraction_stats()
        for field_name, field_stats in extraction_stats['field_success_rates'].items():
            print(f"  {field_name:20}: {field_stats['success']:5d}/{field_stats['total']:5d} ({field_stats['success_rate']:.1%})")
        
        print("="*60)
    
    def confirm_migration(self, auto_confirm: bool = False) -> bool:
        """确认迁移"""
        if auto_confirm:
            self.logger.info("🚀 自动确认模式，开始迁移")
            return True
        
        while True:
            response = input("\n❓ 确认执行迁移？(y/n): ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("请输入 y 或 n")
    
    def execute_migration(self, sql_files: List[str], database_name: str):
        """执行迁移"""
        self.logger.info(f"🚀 开始执行迁移到数据库: {database_name}")
        
        success_count = 0
        failed_files = []
        
        for i, sql_file in enumerate(sql_files, 1):
            filename = Path(sql_file).name
            self.logger.info(f"📤 执行批次 {i}/{len(sql_files)}: {filename}")
            
            try:
                cmd = [
                    "wrangler", "d1", "execute", database_name,
                    "--file", sql_file
                ]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode == 0:
                    success_count += 1
                    self.logger.info(f"✅ 批次 {i} 执行成功")
                else:
                    failed_files.append((sql_file, result.stderr))
                    self.logger.error(f"❌ 批次 {i} 执行失败: {result.stderr}")
                
            except subprocess.TimeoutExpired:
                failed_files.append((sql_file, "执行超时"))
                self.logger.error(f"❌ 批次 {i} 执行超时")
            except Exception as e:
                failed_files.append((sql_file, str(e)))
                self.logger.error(f"❌ 批次 {i} 执行异常: {e}")
        
        # 输出执行结果
        print(f"\n📊 迁移执行完成:")
        print(f"✅ 成功: {success_count}/{len(sql_files)}")
        print(f"❌ 失败: {len(failed_files)}/{len(sql_files)}")
        
        if failed_files:
            print(f"\n❌ 失败文件列表:")
            for sql_file, error in failed_files:
                filename = Path(sql_file).name
                print(f"  - {filename}: {error}")
    
    def run_migration(self, batch_size: int = 50000, database_name: str = "senseword-word-db", auto_confirm: bool = False):
        """运行SQL文件生成流程（不执行自动迁移）"""
        try:
            # 1. 连接数据库
            self.connect_database()

            # 2. 扫描本地数据
            records = self.scan_words_for_publish()
            if not records:
                self.logger.warning("⚠️ 没有找到本地记录")
                return

            # 3. 生成SQL文件
            sql_files = self.generate_sql_batches(records, batch_size)

            # 4. 预览迁移
            self.preview_migration(sql_files)

            # 5. 输出手动执行指令
            self.print_manual_execution_instructions(sql_files, database_name)

            # 6. 输出最终统计
            self.extractor.print_extraction_report()

        except Exception as e:
            self.logger.error(f"❌ 迁移过程发生错误: {e}")
        finally:
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
                self.logger.info("📝 数据库连接已关闭")

    def print_manual_execution_instructions(self, sql_files: List[str], database_name: str):
        """打印手动执行指令"""
        print("\n" + "="*60)
        print("📋 手动执行迁移指令")
        print("="*60)
        print(f"🔧 请从正确的目录执行以下命令:")
        print(f"cd /Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/cloudflare/workers/api")
        print()
        print(f"🚀 执行所有批次迁移:")

        for i, sql_file in enumerate(sql_files, 1):
            filename = Path(sql_file).name
            print(f"wrangler d1 execute {database_name} --local --file \"{sql_file}\"")

        print()
        print(f"🔄 或者使用循环批量执行:")
        total_batches = len(sql_files)
        batch_pattern = sql_files[0].replace("001", "{batch_num:03d}") if sql_files else ""

        print(f"for i in {{1..{total_batches}}}; do")
        print(f"  printf -v batch_num \"%03d\" $i")
        print(f"  echo \"执行批次 $i/{total_batches}\"")
        print(f"  wrangler d1 execute {database_name} --local --file \"{batch_pattern}\"")
        print(f"done")

        print()
        print(f"📊 验证迁移结果:")
        print(f"wrangler d1 execute {database_name} --local --command=\"SELECT COUNT(*) as total_records FROM word_definitions;\"")
        print("="*60)


def main():
    parser = argparse.ArgumentParser(description='Word Definitions 结构重构迁移脚本')
    parser.add_argument('--batch-size', type=int, default=50000, help='批次大小 (默认: 50000)')
    parser.add_argument('--database', type=str, default='senseword-word-db', help='D1数据库名称')
    parser.add_argument('--auto-confirm', action='store_true', help='自动确认执行')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    
    args = parser.parse_args()
    
    migrator = WordDefinitionsRestructureMigration(args.config)
    migrator.run_migration(
        batch_size=args.batch_size,
        database_name=args.database,
        auto_confirm=args.auto_confirm
    )


if __name__ == "__main__":
    main()
