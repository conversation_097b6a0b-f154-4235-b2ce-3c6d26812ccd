#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS成本分析器 - TTS成本计算和分析报告
整合TTS成本计算功能，提供详细的成本分析和预测

功能：
1. TTS字符数统计和成本计算
2. 按语言和类型的成本分析
3. TTS资产使用情况分析
4. 成本预测和优化建议
5. 详细的成本报告生成

作者: AI Assistant
日期: 2025-07-14
版本: v1.0
"""

import sqlite3
import json
import sys
import os
import logging
from datetime import datetime
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tts_cost_analysis.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TTSCostItem:
    """TTS成本项目"""
    tts_type: str
    language: str
    text_content: str
    character_count: int
    cost_usd: float

@dataclass
class CostAnalysisResult:
    """成本分析结果"""
    total_characters: int
    total_cost_usd: float
    cost_by_type: Dict[str, Dict[str, float]]  # {tts_type: {chars, cost}}
    cost_by_language: Dict[str, Dict[str, float]]  # {language: {chars, cost}}
    unique_assets: int
    duplicate_savings: float
    analysis_date: datetime

class TTSCostAnalyzer:
    """TTS成本分析器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        # Azure TTS定价：$16 USD per 1 million characters
        self.cost_per_million_chars = 16.0
    
    def analyze_tts_costs(self) -> CostAnalysisResult:
        """分析TTS成本"""
        logger.info("💰 开始TTS成本分析...")
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查必需的表
                if not self._check_required_tables(conn):
                    raise Exception("必需的数据库表不存在")
                
                # 获取所有TTS资产
                tts_assets = self._get_all_tts_assets(conn)
                
                if not tts_assets:
                    logger.warning("没有找到TTS资产")
                    return CostAnalysisResult(
                        total_characters=0,
                        total_cost_usd=0.0,
                        cost_by_type={},
                        cost_by_language={},
                        unique_assets=0,
                        duplicate_savings=0.0,
                        analysis_date=datetime.now()
                    )
                
                # 计算成本项目
                cost_items = self._calculate_cost_items(tts_assets)
                
                # 分析成本分布
                result = self._analyze_cost_distribution(cost_items)
                
                # 计算去重节省
                result.duplicate_savings = self._calculate_duplicate_savings(conn)
                
                self._print_cost_summary(result)
                return result
                
        except Exception as e:
            logger.error(f"❌ TTS成本分析失败: {str(e)}")
            raise
    
    def generate_cost_report(self, output_file: Optional[str] = None) -> bool:
        """生成成本分析报告"""
        logger.info("📊 生成TTS成本分析报告...")
        
        try:
            # 执行成本分析
            result = self.analyze_tts_costs()
            
            # 生成报告内容
            report_content = self._generate_cost_report_content(result)
            
            # 保存报告
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"tts_cost_analysis_report_{timestamp}.md"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"✅ TTS成本分析报告已生成: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 生成TTS成本分析报告失败: {str(e)}")
            return False
    
    def _check_required_tables(self, conn: sqlite3.Connection) -> bool:
        """检查必需的数据库表"""
        required_tables = ['tts_assets']
        
        for table in required_tables:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,)
            )
            if not cursor.fetchone():
                logger.error(f"❌ 必需的表 '{table}' 不存在")
                return False
        
        return True
    
    def _get_all_tts_assets(self, conn: sqlite3.Connection) -> List[Tuple]:
        """获取所有TTS资产"""
        cursor = conn.execute("""
            SELECT ttsId, textToSpeak, learningLanguage, ttsType, status
            FROM tts_assets
            ORDER BY ttsId
        """)
        
        return cursor.fetchall()
    
    def _calculate_cost_items(self, tts_assets: List[Tuple]) -> List[TTSCostItem]:
        """计算成本项目"""
        cost_items = []
        
        for tts_id, text_to_speak, language, tts_type, status in tts_assets:
            if not text_to_speak:
                continue
            
            char_count = len(text_to_speak)
            cost = (char_count / 1_000_000) * self.cost_per_million_chars
            
            cost_items.append(TTSCostItem(
                tts_type=tts_type,
                language=language,
                text_content=text_to_speak,
                character_count=char_count,
                cost_usd=cost
            ))
        
        return cost_items
    
    def _analyze_cost_distribution(self, cost_items: List[TTSCostItem]) -> CostAnalysisResult:
        """分析成本分布"""
        total_characters = sum(item.character_count for item in cost_items)
        total_cost = sum(item.cost_usd for item in cost_items)
        
        # 按类型分析
        cost_by_type = {}
        for item in cost_items:
            if item.tts_type not in cost_by_type:
                cost_by_type[item.tts_type] = {'chars': 0, 'cost': 0.0, 'count': 0}
            
            cost_by_type[item.tts_type]['chars'] += item.character_count
            cost_by_type[item.tts_type]['cost'] += item.cost_usd
            cost_by_type[item.tts_type]['count'] += 1
        
        # 按语言分析
        cost_by_language = {}
        for item in cost_items:
            if item.language not in cost_by_language:
                cost_by_language[item.language] = {'chars': 0, 'cost': 0.0, 'count': 0}
            
            cost_by_language[item.language]['chars'] += item.character_count
            cost_by_language[item.language]['cost'] += item.cost_usd
            cost_by_language[item.language]['count'] += 1
        
        return CostAnalysisResult(
            total_characters=total_characters,
            total_cost_usd=total_cost,
            cost_by_type=cost_by_type,
            cost_by_language=cost_by_language,
            unique_assets=len(cost_items),
            duplicate_savings=0.0,  # 将在后续计算
            analysis_date=datetime.now()
        )
    
    def _calculate_duplicate_savings(self, conn: sqlite3.Connection) -> float:
        """计算去重节省的成本"""
        # 获取重复文本的统计
        cursor = conn.execute("""
            SELECT textToSpeak, COUNT(*) as usage_count, LENGTH(textToSpeak) as char_count
            FROM tts_assets
            WHERE textToSpeak IS NOT NULL AND textToSpeak != ''
            GROUP BY textToSpeak
            HAVING COUNT(*) > 1
        """)
        
        total_savings = 0.0
        
        for text_to_speak, usage_count, char_count in cursor.fetchall():
            # 如果没有去重，需要的总字符数
            total_chars_without_dedup = char_count * usage_count
            # 实际使用的字符数（去重后）
            actual_chars = char_count
            # 节省的字符数
            saved_chars = total_chars_without_dedup - actual_chars
            # 节省的成本
            saved_cost = (saved_chars / 1_000_000) * self.cost_per_million_chars
            total_savings += saved_cost
        
        return total_savings
    
    def _generate_cost_report_content(self, result: CostAnalysisResult) -> str:
        """生成成本报告内容"""
        timestamp = result.analysis_date.strftime("%Y-%m-%d %H:%M:%S")
        
        report = f"""# TTS成本分析报告

**分析时间**: {timestamp}
**定价标准**: Azure TTS - $16 USD per 1 million characters

## 📊 总体成本概览

| 指标 | 数值 |
|------|------|
| 总字符数 | {result.total_characters:,} |
| 总成本 | ${result.total_cost_usd:.2f} USD |
| 唯一资产数 | {result.unique_assets:,} |
| 去重节省 | ${result.duplicate_savings:.2f} USD |
| 实际成本 | ${result.total_cost_usd - result.duplicate_savings:.2f} USD |

## 📈 按类型分析

| TTS类型 | 字符数 | 成本 (USD) | 资产数 | 平均字符/资产 |
|---------|--------|------------|--------|---------------|
"""
        
        for tts_type, data in sorted(result.cost_by_type.items()):
            avg_chars = data['chars'] / data['count'] if data['count'] > 0 else 0
            report += f"| {tts_type} | {data['chars']:,} | ${data['cost']:.2f} | {data['count']:,} | {avg_chars:.1f} |\n"
        
        report += f"""
## 🌍 按语言分析

| 语言 | 字符数 | 成本 (USD) | 资产数 | 平均字符/资产 |
|------|--------|------------|--------|---------------|
"""
        
        for language, data in sorted(result.cost_by_language.items()):
            avg_chars = data['chars'] / data['count'] if data['count'] > 0 else 0
            report += f"| {language} | {data['chars']:,} | ${data['cost']:.2f} | {data['count']:,} | {avg_chars:.1f} |\n"
        
        # 成本优化建议
        report += f"""
## 💡 成本优化建议

### 1. 去重效果
- 当前去重节省: **${result.duplicate_savings:.2f} USD**
- 节省比例: **{result.duplicate_savings/result.total_cost_usd*100:.1f}%**

### 2. 类型优化
"""
        
        # 找出成本最高的类型
        if result.cost_by_type:
            highest_cost_type = max(result.cost_by_type.items(), key=lambda x: x[1]['cost'])
            report += f"- 成本最高的类型: **{highest_cost_type[0]}** (${highest_cost_type[1]['cost']:.2f} USD)\n"
        
        # 找出平均字符数最高的类型
        if result.cost_by_type:
            type_avg_chars = {
                tts_type: data['chars'] / data['count'] 
                for tts_type, data in result.cost_by_type.items() 
                if data['count'] > 0
            }
            if type_avg_chars:
                longest_type = max(type_avg_chars.items(), key=lambda x: x[1])
                report += f"- 平均字符数最多的类型: **{longest_type[0]}** ({longest_type[1]:.1f} 字符/资产)\n"
        
        report += f"""
### 3. 预测分析
- 如果资产数量翻倍，预计成本: **${result.total_cost_usd * 2:.2f} USD**
- 如果字符数减少10%，预计节省: **${result.total_cost_usd * 0.1:.2f} USD**

### 4. 建议措施
1. **继续保持去重策略** - 已节省 ${result.duplicate_savings:.2f} USD
2. **优化长文本内容** - 重点关注平均字符数较高的类型
3. **定期清理无用资产** - 减少不必要的TTS生成
4. **批量处理** - 利用批量API可能的折扣优势

---
*报告生成时间: {timestamp}*
"""
        
        return report
    
    def _print_cost_summary(self, result: CostAnalysisResult):
        """打印成本摘要"""
        logger.info("=" * 60)
        logger.info("💰 TTS成本分析结果")
        logger.info("=" * 60)
        logger.info(f"📊 总字符数: {result.total_characters:,}")
        logger.info(f"💵 总成本: ${result.total_cost_usd:.2f} USD")
        logger.info(f"🎵 唯一资产数: {result.unique_assets:,}")
        logger.info(f"💎 去重节省: ${result.duplicate_savings:.2f} USD")
        logger.info(f"💰 实际成本: ${result.total_cost_usd - result.duplicate_savings:.2f} USD")
        
        if result.cost_by_type:
            logger.info("\n📈 按类型分布:")
            for tts_type, data in sorted(result.cost_by_type.items(), key=lambda x: x[1]['cost'], reverse=True):
                logger.info(f"  {tts_type}: {data['chars']:,} 字符, ${data['cost']:.2f} USD")
        
        if result.cost_by_language:
            logger.info("\n🌍 按语言分布:")
            for language, data in sorted(result.cost_by_language.items(), key=lambda x: x[1]['cost'], reverse=True):
                logger.info(f"  {language}: {data['chars']:,} 字符, ${data['cost']:.2f} USD")
        
        logger.info("=" * 60)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="TTS成本分析器 - TTS成本计算和分析报告",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析TTS成本
  python 03_tts_cost_analyzer.py --analyze

  # 生成成本分析报告
  python 03_tts_cost_analyzer.py --report

  # 同时执行分析和报告生成
  python 03_tts_cost_analyzer.py --analyze --report

  # 指定数据库路径和输出文件
  python 03_tts_cost_analyzer.py --analyze --report --db-path ./custom_db.db --output-file cost_report.md
        """
    )

    parser.add_argument(
        '--db-path',
        default="/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content_v4.db",
        help='数据库文件路径'
    )

    parser.add_argument(
        '--analyze',
        action='store_true',
        help='执行TTS成本分析'
    )

    parser.add_argument(
        '--report',
        action='store_true',
        help='生成TTS成本分析报告'
    )

    parser.add_argument(
        '--output-file',
        help='报告输出文件路径'
    )

    args = parser.parse_args()

    # 检查参数
    if not args.analyze and not args.report:
        parser.error("必须指定 --analyze 或 --report 或两者")

    # 检查数据库文件
    if not os.path.exists(args.db_path):
        logger.error(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)

    logger.info("💰 TTS成本分析器")
    logger.info("=" * 60)
    logger.info(f"📍 数据库路径: {args.db_path}")

    # 创建分析器
    analyzer = TTSCostAnalyzer(args.db_path)

    success = True

    try:
        # 执行成本分析
        if args.analyze:
            logger.info("💰 开始TTS成本分析...")
            result = analyzer.analyze_tts_costs()

        # 生成报告
        if args.report:
            logger.info("📊 开始生成TTS成本分析报告...")
            report_success = analyzer.generate_cost_report(args.output_file)
            if not report_success:
                success = False

        if success:
            logger.info("🎉 所有操作成功完成!")
            sys.exit(0)
        else:
            logger.error("❌ 操作过程中出现错误!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"❌ 执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
