#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS资产验证器 - TTS资产一致性检查和映射验证
整合TTS一致性验证和映射报告功能

功能：
1. TTS资产完整性验证
2. ContentJson与TTS资产映射验证
3. 音标类型一致性检查
4. TTS映射关系报告生成
5. 数据一致性分析

作者: AI Assistant
日期: 2025-07-14
版本: v1.0
"""

import sqlite3
import json
import sys
import os
import logging
import random
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple, Any

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tts_asset_validation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """验证结果"""
    total_words: int
    total_tts_assets: int
    valid_mappings: int
    invalid_mappings: int
    missing_assets: int
    orphaned_assets: int
    phonetic_type_issues: int
    success: bool
    errors: List[str]

@dataclass
class TTSMappingRecord:
    """TTS映射记录"""
    word: str
    word_id: int
    tts_id: str
    tts_original_text: str
    tts_normalized_text: str
    tts_text_to_speak: str
    tts_type: str
    content_text: str
    content_context_path: str
    content_tts_type: str
    text_match: bool
    type_match: bool
    mapping_valid: bool

class TTSAssetValidator:
    """TTS资产验证器"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.validation_errors = []
    
    def validate_tts_assets(self) -> ValidationResult:
        """验证TTS资产完整性"""
        logger.info("🔍 开始TTS资产验证...")
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查必需的表
                if not self._check_required_tables(conn):
                    return ValidationResult(
                        total_words=0, total_tts_assets=0, valid_mappings=0,
                        invalid_mappings=0, missing_assets=0, orphaned_assets=0,
                        phonetic_type_issues=0, success=False,
                        errors=["必需的数据库表不存在"]
                    )
                
                # 获取基础统计
                stats = self._get_basic_statistics(conn)
                
                # 验证映射关系
                mapping_results = self._validate_tts_mappings(conn)
                
                # 检查孤立资产
                orphaned_count = self._check_orphaned_assets(conn)
                
                # 检查音标类型一致性
                phonetic_issues = self._check_phonetic_type_consistency(conn)
                
                result = ValidationResult(
                    total_words=stats['total_words'],
                    total_tts_assets=stats['total_tts_assets'],
                    valid_mappings=mapping_results['valid'],
                    invalid_mappings=mapping_results['invalid'],
                    missing_assets=mapping_results['missing'],
                    orphaned_assets=orphaned_count,
                    phonetic_type_issues=phonetic_issues,
                    success=len(self.validation_errors) == 0,
                    errors=self.validation_errors.copy()
                )
                
                self._print_validation_summary(result)
                return result
                
        except Exception as e:
            logger.error(f"❌ TTS资产验证失败: {str(e)}")
            return ValidationResult(
                total_words=0, total_tts_assets=0, valid_mappings=0,
                invalid_mappings=0, missing_assets=0, orphaned_assets=0,
                phonetic_type_issues=0, success=False,
                errors=[str(e)]
            )
    
    def generate_tts_mapping_report(self, sample_size: int = 100, output_file: Optional[str] = None) -> bool:
        """生成TTS映射关系报告 - 随机抽检100个单词的映射表格"""
        logger.info(f"📊 生成TTS映射报告 (随机抽检 {sample_size} 个单词)...")

        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取随机样本
                mapping_records = self._get_tts_mapping_samples(conn, sample_size)

                if not mapping_records:
                    logger.warning("没有找到TTS映射记录")
                    return False

                # 生成报告
                report_content = self._generate_mapping_report_content(mapping_records, sample_size)

                # 保存报告
                if not output_file:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_file = f"tts_mapping_report_{timestamp}.md"

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                logger.info(f"✅ TTS映射报告已生成: {output_file}")
                logger.info(f"📊 报告包含 {len(mapping_records)} 个TTS映射记录")

                # 打印简要统计
                self._print_mapping_summary(mapping_records)

                return True

        except Exception as e:
            logger.error(f"❌ 生成TTS映射报告失败: {str(e)}")
            return False
    
    def _check_required_tables(self, conn: sqlite3.Connection) -> bool:
        """检查必需的数据库表"""
        required_tables = ['words_for_publish', 'tts_assets']
        
        for table in required_tables:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,)
            )
            if not cursor.fetchone():
                logger.error(f"❌ 必需的表 '{table}' 不存在")
                return False
        
        return True
    
    def _get_basic_statistics(self, conn: sqlite3.Connection) -> Dict[str, int]:
        """获取基础统计信息"""
        # 获取单词总数
        cursor = conn.execute("SELECT COUNT(*) FROM words_for_publish WHERE contentVersion = 'v4.0'")
        total_words = cursor.fetchone()[0]
        
        # 获取TTS资产总数
        cursor = conn.execute("SELECT COUNT(*) FROM tts_assets")
        total_tts_assets = cursor.fetchone()[0]
        
        return {
            'total_words': total_words,
            'total_tts_assets': total_tts_assets
        }
    
    def _validate_tts_mappings(self, conn: sqlite3.Connection) -> Dict[str, int]:
        """验证TTS映射关系 - 基于ttsHashList的验证逻辑"""
        logger.info("🔍 验证TTS映射关系...")

        valid_count = 0
        invalid_count = 0
        missing_count = 0

        # 获取所有v4.0单词及其ttsHashList
        cursor = conn.execute("""
            SELECT id, word, contentJson, ttsHashList
            FROM words_for_publish
            WHERE contentVersion = 'v4.0'
            AND ttsHashList IS NOT NULL
            AND ttsHashList != '[]'
        """)

        for word_id, word, content_json_str, tts_hash_list_str in cursor.fetchall():
            try:
                content_data = json.loads(content_json_str)
                tts_hash_list = json.loads(tts_hash_list_str) if tts_hash_list_str else []

                # 对每个ttsHashList中的ID进行验证
                for tts_id in tts_hash_list:
                    # 1. 检查TTS资产是否存在
                    tts_asset = self._get_tts_asset(conn, tts_id)
                    if not tts_asset:
                        invalid_count += 1
                        self.validation_errors.append(f"单词 {word} 的TTS ID {tts_id} 在tts_assets表中不存在")
                        continue

                    # 2. 在ContentJson中查找对应的TTS引用
                    content_info = self._find_tts_in_content(content_data, tts_id, word)
                    if not content_info:
                        invalid_count += 1
                        self.validation_errors.append(f"单词 {word} 的TTS ID {tts_id} 在ContentJson中未找到引用")
                        continue

                    # 3. 验证映射关系
                    content_text, context_path, content_tts_type = content_info
                    original_text, normalized_text, text_to_speak, tts_type, language = tts_asset

                    # 文本匹配验证
                    text_match = content_text == text_to_speak
                    # 类型匹配验证
                    type_match = content_tts_type == tts_type

                    if text_match and type_match:
                        valid_count += 1
                    else:
                        invalid_count += 1
                        if not text_match:
                            self.validation_errors.append(
                                f"单词 {word} TTS ID {tts_id} 文本不匹配: "
                                f"ContentJson='{content_text}' vs TTS='{text_to_speak}'"
                            )
                        if not type_match:
                            self.validation_errors.append(
                                f"单词 {word} TTS ID {tts_id} 类型不匹配: "
                                f"ContentJson='{content_tts_type}' vs TTS='{tts_type}'"
                            )

            except json.JSONDecodeError:
                self.validation_errors.append(f"单词 {word} 的ContentJson或ttsHashList格式无效")
                invalid_count += 1
            except Exception as e:
                self.validation_errors.append(f"验证单词 {word} 时出错: {str(e)}")
                invalid_count += 1

        return {
            'valid': valid_count,
            'invalid': invalid_count,
            'missing': missing_count
        }
    
    def _extract_tts_ids_from_content(self, content_data: dict) -> List[str]:
        """从ContentJson中提取所有ttsId"""
        tts_ids = []
        
        try:
            content = content_data.get('content', {})
            
            # 提取音标ttsId
            phonetic_symbols = content.get('phoneticSymbols', [])
            for phonetic in phonetic_symbols:
                tts_id = phonetic.get('ttsId')
                if tts_id:
                    tts_ids.append(tts_id)
            
            # 提取例句和短语ttsId
            usage_examples = content.get('usageExamples', [])
            for category in usage_examples:
                examples = category.get('examples', [])
                for example in examples:
                    # 例句ttsId
                    tts_id = example.get('ttsId')
                    if tts_id:
                        tts_ids.append(tts_id)
                    
                    # 短语分解ttsId
                    phrase_breakdown = example.get('phraseBreakdown', [])
                    for phrase in phrase_breakdown:
                        tts_id = phrase.get('ttsId')
                        if tts_id:
                            tts_ids.append(tts_id)
        
        except Exception as e:
            logger.warning(f"提取ttsId失败: {str(e)}")
        
        return tts_ids

    def _get_tts_asset(self, conn: sqlite3.Connection, tts_id: str) -> Optional[Tuple]:
        """获取TTS资产信息"""
        cursor = conn.execute("""
            SELECT originalText, normalizedText, textToSpeak, ttsType, learningLanguage
            FROM tts_assets
            WHERE ttsId = ?
        """, (tts_id,))
        return cursor.fetchone()

    def _find_tts_in_content(self, content_data: dict, target_tts_id: str, word: str) -> Optional[Tuple[str, str, str]]:
        """在ContentJson中查找指定的TTS ID - 与原有逻辑保持一致"""
        content = content_data.get('content', {})

        # 1. 检查音标
        phonetic_symbols = content.get('phoneticSymbols', [])
        for i, phonetic in enumerate(phonetic_symbols):
            if phonetic.get('ttsId') == target_tts_id:
                # 对于音标，content_text应该是单词本身（与原逻辑一致）
                phonetic_type = phonetic.get('type', 'name')
                return (
                    word,  # 音标的textToSpeak是单词本身
                    f"content.phoneticSymbols[{i}]",
                    f"phonetic_{phonetic_type}"
                )

        # 2. 检查例句
        usage_examples = content.get('usageExamples', [])
        for cat_i, category in enumerate(usage_examples):
            examples = category.get('examples', [])
            for ex_i, example in enumerate(examples):
                # 例句本身
                if example.get('ttsId') == target_tts_id:
                    learning_text = example.get('learningLanguage', '')
                    return (
                        learning_text,
                        f"content.usageExamples[{cat_i}].examples[{ex_i}].learningLanguage",
                        "example_sentence"
                    )

                # 短语分解
                phrase_breakdown = example.get('phraseBreakdown', [])
                for ph_i, phrase in enumerate(phrase_breakdown):
                    if phrase.get('ttsId') == target_tts_id:
                        phrase_text = phrase.get('phrase', '')
                        return (
                            phrase_text,
                            f"content.usageExamples[{cat_i}].examples[{ex_i}].phraseBreakdown[{ph_i}].phrase",
                            "phrase_breakdown"
                        )

        return None
    
    def _check_orphaned_assets(self, conn: sqlite3.Connection) -> int:
        """检查孤立的TTS资产"""
        logger.info("🔍 检查孤立的TTS资产...")
        
        # 获取所有TTS资产ID
        cursor = conn.execute("SELECT ttsId FROM tts_assets")
        all_tts_ids = {row[0] for row in cursor.fetchall()}
        
        # 获取ContentJson中引用的所有TTS ID
        referenced_tts_ids = set()
        
        cursor = conn.execute("SELECT contentJson FROM words_for_publish WHERE contentVersion = 'v4.0'")
        for (content_json_str,) in cursor.fetchall():
            try:
                content_data = json.loads(content_json_str)
                referenced_tts_ids.update(self._extract_tts_ids_from_content(content_data))
            except json.JSONDecodeError:
                continue
        
        # 找出孤立的资产
        orphaned_ids = all_tts_ids - referenced_tts_ids
        
        if orphaned_ids:
            logger.warning(f"发现 {len(orphaned_ids)} 个孤立的TTS资产")
            for tts_id in list(orphaned_ids)[:5]:  # 只显示前5个
                self.validation_errors.append(f"孤立的TTS资产: {tts_id}")
        
        return len(orphaned_ids)

    def _check_phonetic_type_consistency(self, conn: sqlite3.Connection) -> int:
        """检查音标类型一致性"""
        logger.info("🔍 检查音标类型一致性...")

        issues_count = 0
        standard_types = {'name', 'bre', 'ipa'}

        cursor = conn.execute("SELECT id, word, contentJson FROM words_for_publish WHERE contentVersion = 'v4.0'")

        for word_id, word, content_json_str in cursor.fetchall():
            try:
                content_data = json.loads(content_json_str)
                content = content_data.get('content', {})
                phonetic_symbols = content.get('phoneticSymbols', [])

                for i, phonetic in enumerate(phonetic_symbols):
                    phonetic_type = phonetic.get('type', '')
                    if phonetic_type not in standard_types:
                        issues_count += 1
                        self.validation_errors.append(
                            f"单词 {word} 的音标 [{i}] 类型非标准: '{phonetic_type}'"
                        )

            except json.JSONDecodeError:
                continue

        return issues_count

    def _get_tts_mapping_samples(self, conn: sqlite3.Connection, sample_size: int) -> List[TTSMappingRecord]:
        """获取TTS映射样本 - 基于随机单词的ttsHashList（与原逻辑一致）"""
        mapping_records = []

        # 1. 随机获取单词及其ttsHashList
        cursor = conn.execute("""
            SELECT id, word, contentJson, ttsHashList
            FROM words_for_publish
            WHERE contentVersion = 'v4.0'
            AND ttsHashList IS NOT NULL
            AND ttsHashList != '[]'
            ORDER BY RANDOM()
            LIMIT ?
        """, (sample_size,))

        words = cursor.fetchall()

        # 2. 处理每个单词的TTS映射
        for word_id, word, content_json_str, tts_hash_list_str in words:
            try:
                # 解析ttsHashList
                tts_hash_list = json.loads(tts_hash_list_str) if tts_hash_list_str else []
                content_data = json.loads(content_json_str)

                # 为每个TTS ID验证映射关系
                for tts_id in tts_hash_list:
                    # 获取TTS资产信息
                    tts_asset = self._get_tts_asset(conn, tts_id)
                    if not tts_asset:
                        continue

                    original_text, normalized_text, text_to_speak, tts_type, language = tts_asset

                    # 在ContentJson中查找对应的TTS引用
                    content_info = self._find_tts_in_content(content_data, tts_id, word)

                    if content_info:
                        content_text, context_path, content_tts_type = content_info

                        # 验证映射关系
                        text_match = content_text == text_to_speak
                        type_match = content_tts_type == tts_type
                        mapping_valid = text_match and type_match

                        mapping_records.append(TTSMappingRecord(
                            word=word,
                            word_id=word_id,
                            tts_id=tts_id,
                            tts_original_text=original_text,
                            tts_normalized_text=normalized_text,
                            tts_text_to_speak=text_to_speak,
                            tts_type=tts_type,
                            content_text=content_text,
                            content_context_path=context_path,
                            content_tts_type=content_tts_type,
                            text_match=text_match,
                            type_match=type_match,
                            mapping_valid=mapping_valid
                        ))

            except json.JSONDecodeError:
                continue

        return mapping_records

    def _generate_mapping_report_content(self, mapping_records: List[TTSMappingRecord], sample_size: int) -> str:
        """生成映射报告内容 - 包含详细的映射表格"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 统计信息
        total_records = len(mapping_records)
        valid_mappings = sum(1 for r in mapping_records if r.mapping_valid)
        text_matches = sum(1 for r in mapping_records if r.text_match)
        type_matches = sum(1 for r in mapping_records if r.type_match)

        # 按TTS类型分组统计
        type_stats = {}
        for record in mapping_records:
            tts_type = record.tts_type
            if tts_type not in type_stats:
                type_stats[tts_type] = {'total': 0, 'valid': 0}
            type_stats[tts_type]['total'] += 1
            if record.mapping_valid:
                type_stats[tts_type]['valid'] += 1

        report = f"""# TTS数据映射验证报告

**生成时间**: {timestamp}
**样本数量**: {sample_size}个单词，{total_records}个TTS资产
**数据库**: senseword_content_v4.db

## 📊 总体统计

| 指标 | 数量 | 比例 |
|------|------|------|
| 总样本数 | {total_records} | 100.0% |
| 有效映射 | {valid_mappings} | {valid_mappings/total_records*100:.1f}% |
| 文本匹配 | {text_matches} | {text_matches/total_records*100:.1f}% |
| 类型匹配 | {type_matches} | {type_matches/total_records*100:.1f}% |

## � 按TTS类型统计

| TTS类型 | 有效映射 | 总数 | 成功率 |
|---------|----------|------|--------|
"""

        for tts_type, stats in sorted(type_stats.items()):
            valid_rate = stats['valid'] / stats['total'] * 100 if stats['total'] > 0 else 0
            report += f"| {tts_type} | {stats['valid']} | {stats['total']} | {valid_rate:.1f}% |\n"

        report += f"""
## 🔍 详细映射数据

| 序号 | 单词 | TTS ID | TTS类型 | TTS原始文本 | ContentJson文本 | 上下文路径 | 文本匹配 | 类型匹配 | 映射有效 |
|------|------|--------|---------|-------------|----------------|------------|----------|----------|----------|
"""

        for i, record in enumerate(mapping_records, 1):
            # 转义Markdown特殊字符并截断长文本
            tts_original = self._escape_markdown(record.tts_original_text)
            content_text = self._escape_markdown(record.content_text)
            context_path = self._escape_markdown(record.content_context_path)

            # 状态图标
            text_icon = "✅" if record.text_match else "❌"
            type_icon = "✅" if record.type_match else "❌"
            valid_icon = "✅" if record.mapping_valid else "❌"

            report += f"| {i} | {record.word} | `{record.tts_id[:12]}...` | {record.tts_type} | {tts_original[:40]}{'...' if len(tts_original) > 40 else ''} | {content_text[:30]}{'...' if len(content_text) > 30 else ''} | {context_path[:35]}{'...' if len(context_path) > 35 else ''} | {text_icon} | {type_icon} | {valid_icon} |\n"

        # 问题记录
        problem_records = [r for r in mapping_records if not r.mapping_valid]
        if problem_records:
            report += f"""
## ⚠️ 问题记录详情

发现 {len(problem_records)} 个问题记录：

"""
            for i, record in enumerate(problem_records[:10], 1):  # 只显示前10个
                report += f"### {i}. 单词: {record.word}\n\n"
                report += f"- **TTS ID**: `{record.tts_id}`\n"
                report += f"- **上下文路径**: `{record.content_context_path}`\n"

                if not record.text_match:
                    report += f"- **文本不匹配**:\n"
                    report += f"  - ContentJson: `{record.content_text}`\n"
                    report += f"  - TTS资产: `{record.tts_text_to_speak}`\n"

                if not record.type_match:
                    report += f"- **类型不匹配**:\n"
                    report += f"  - ContentJson: `{record.content_tts_type}`\n"
                    report += f"  - TTS资产: `{record.tts_type}`\n"

                report += "\n"

            if len(problem_records) > 10:
                report += f"*... 还有 {len(problem_records) - 10} 个问题记录*\n\n"

        report += f"""
## 🎯 结论

{'✅ **映射验证通过**: ContentJson与TTS资产的映射关系完全有效' if valid_mappings == total_records else f'⚠️ **发现问题**: {total_records - valid_mappings}个映射存在问题，需要进一步检查'}

---
*报告生成时间: {timestamp}*
"""

        return report

    def _escape_markdown(self, text: str) -> str:
        """转义Markdown特殊字符"""
        if not text:
            return ""
        # 转义常见的Markdown特殊字符
        return text.replace('|', '\\|').replace('*', '\\*').replace('_', '\\_').replace('`', '\\`')

    def _print_mapping_summary(self, mapping_records: List[TTSMappingRecord]):
        """打印映射验证摘要"""
        total_records = len(mapping_records)
        valid_mappings = sum(1 for r in mapping_records if r.mapping_valid)

        logger.info("=" * 50)
        logger.info("📊 TTS映射验证摘要")
        logger.info("=" * 50)
        logger.info(f"📋 总映射记录: {total_records}")
        logger.info(f"✅ 有效映射: {valid_mappings}")
        logger.info(f"❌ 无效映射: {total_records - valid_mappings}")
        logger.info(f"📈 成功率: {valid_mappings/total_records*100:.1f}%")
        logger.info("=" * 50)

    def _print_validation_summary(self, result: ValidationResult):
        """打印验证摘要"""
        logger.info("=" * 60)
        logger.info("🔍 TTS资产验证结果")
        logger.info("=" * 60)
        logger.info(f"📊 总单词数: {result.total_words}")
        logger.info(f"📊 总TTS资产数: {result.total_tts_assets}")
        logger.info(f"✅ 有效映射: {result.valid_mappings}")
        logger.info(f"❌ 无效映射: {result.invalid_mappings}")
        logger.info(f"🔍 缺失资产: {result.missing_assets}")
        logger.info(f"🏝️ 孤立资产: {result.orphaned_assets}")
        logger.info(f"🔧 音标类型问题: {result.phonetic_type_issues}")

        if result.success:
            logger.info("🎉 验证通过!")
        else:
            logger.warning(f"⚠️ 发现 {len(result.errors)} 个问题")
            for error in result.errors[:10]:  # 只显示前10个错误
                logger.warning(f"  - {error}")

        logger.info("=" * 60)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="TTS资产验证器 - TTS资产一致性检查和映射验证",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 验证TTS资产完整性
  python 02_tts_asset_validator.py --validate

  # 生成TTS映射报告
  python 02_tts_asset_validator.py --report --sample-size 50

  # 同时执行验证和报告生成
  python 02_tts_asset_validator.py --validate --report

  # 指定数据库路径
  python 02_tts_asset_validator.py --validate --db-path ./custom_db.db
        """
    )

    parser.add_argument(
        '--db-path',
        default="/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content_v4.db",
        help='数据库文件路径'
    )

    parser.add_argument(
        '--validate',
        action='store_true',
        help='执行TTS资产验证'
    )

    parser.add_argument(
        '--report',
        action='store_true',
        help='生成TTS映射报告'
    )

    parser.add_argument(
        '--sample-size',
        type=int,
        default=100,
        help='映射报告的样本大小 (默认: 100)'
    )

    parser.add_argument(
        '--output-file',
        help='报告输出文件路径'
    )

    args = parser.parse_args()

    # 检查参数
    if not args.validate and not args.report:
        parser.error("必须指定 --validate 或 --report 或两者")

    # 检查数据库文件
    if not os.path.exists(args.db_path):
        logger.error(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)

    logger.info("🔍 TTS资产验证器")
    logger.info("=" * 60)
    logger.info(f"📍 数据库路径: {args.db_path}")

    # 创建验证器
    validator = TTSAssetValidator(args.db_path)

    success = True

    # 执行验证
    if args.validate:
        logger.info("🔍 开始TTS资产验证...")
        result = validator.validate_tts_assets()
        if not result.success:
            success = False

    # 生成报告
    if args.report:
        logger.info("📊 开始生成TTS映射报告...")
        report_success = validator.generate_tts_mapping_report(
            args.sample_size,
            args.output_file
        )
        if not report_success:
            success = False

    if success:
        logger.info("🎉 所有操作成功完成!")
        sys.exit(0)
    else:
        logger.error("❌ 操作过程中出现错误!")
        sys.exit(1)

if __name__ == "__main__":
    main()
