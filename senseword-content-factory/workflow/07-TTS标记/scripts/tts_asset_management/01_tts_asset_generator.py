#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS资产生成器 - 音标标准化 + TTS ID生成回写
整合音标类型标准化和TTS ID生成功能，基于看板式状态管理

功能：
1. 音标类型标准化（bre/name/ipa）
2. TTS内容提取（音标、例句、短语）
3. TTS ID生成和资产创建
4. ContentJson回写更新
5. 看板状态管理集成

作者: AI Assistant
日期: 2025-07-14
版本: v1.0
"""

import sqlite3
import json
import sys
import os
import logging
import re
import time
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Optional, Any, Tuple

# 添加utils目录到路径
current_dir = os.path.dirname(__file__)
utils_dir = os.path.join(current_dir, 'utils')
sys.path.append(utils_dir)

# 添加原始utils目录到路径
original_utils_dir = os.path.join(os.path.dirname(current_dir), 'tts_processing', 'utils')
sys.path.append(original_utils_dir)

from text_normalizer import TextNormalizer, TTSTextType
from hash_generator import HashGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tts_asset_generation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 实时进度条
def print_progress_bar(current, total, prefix='', suffix='', length=50, fill='█'):
    """打印实时进度条"""
    if total == 0:
        percent = 0
    else:
        percent = (current / total) * 100
    
    filled_length = int(length * current // total) if total > 0 else 0
    bar = fill * filled_length + '-' * (length - filled_length)
    
    print(f'\r{prefix} |{bar}| {current}/{total} ({percent:.1f}%) {suffix}', end='', flush=True)
    
    if current >= total:
        print()

@dataclass
class WordRecord:
    """单词记录"""
    id: int
    word: str
    learning_language: str
    scaffolding_language: str
    content_json: str
    content_version: str

@dataclass
class TTSContent:
    """TTS内容"""
    original_text: str
    normalized_text: str
    text_to_speak: str
    tts_type: str
    context_path: str
    word_id: int
    language: str

@dataclass
class TTSAsset:
    """TTS资产"""
    tts_id: str
    original_text: str
    normalized_text: str
    text_hash: str
    text_to_speak: str
    learning_language: str
    tts_type: str
    status: str

@dataclass
class ProcessingResult:
    """处理结果"""
    word_id: int
    word: str
    success: bool
    phonetic_fixes: int
    tts_assets_created: int
    error_message: Optional[str]

class TTSAssetGenerator:
    """TTS资产生成器"""
    
    def __init__(self, db_path: str, batch_size: int = 500):
        self.db_path = db_path
        self.batch_size = batch_size
        self.text_normalizer = TextNormalizer()
        self.hash_generator = HashGenerator()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'total_success': 0,
            'total_errors': 0,
            'total_phonetic_fixes': 0,
            'total_tts_assets': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 音标类型映射统计
        self.phonetic_type_mappings = {}
    
    def process_words_for_tts_generation(self, limit: Optional[int] = None) -> bool:
        """
        处理需要TTS生成的单词
        基于看板状态：contentAiReviewed=TRUE AND ttsIdGenerated=FALSE
        """
        logger.info("🚀 开始TTS资产生成...")
        self.stats['start_time'] = datetime.now()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 检查必需的表
                if not self._check_required_tables(conn):
                    return False
                
                # 获取需要处理的单词
                words_to_process = self._get_words_for_tts_generation(conn, limit)
                
                if not words_to_process:
                    logger.info("✅ 没有需要生成TTS资产的单词")
                    return True
                
                total_words = len(words_to_process)
                logger.info(f"📊 需要处理的单词数: {total_words}")
                
                # 分批处理
                batch_count = (total_words + self.batch_size - 1) // self.batch_size
                logger.info(f"将分 {batch_count} 个批次处理，每批次 {self.batch_size} 条记录")
                
                for batch_id in range(batch_count):
                    start_idx = batch_id * self.batch_size
                    end_idx = min(start_idx + self.batch_size, total_words)
                    batch_words = words_to_process[start_idx:end_idx]
                    
                    # 显示进度
                    print_progress_bar(
                        batch_id + 1, batch_count,
                        prefix='🔧 处理进度',
                        suffix=f'已处理: {self.stats["total_processed"]}条 | TTS资产: {self.stats["total_tts_assets"]}个'
                    )
                    
                    # 处理批次
                    self._process_batch(conn, batch_words)
                
                # 更新看板状态
                self._update_kanban_status(conn, [w.id for w in words_to_process if self._was_processed_successfully(w.id)])
                
                self.stats['end_time'] = datetime.now()
                self._print_final_statistics()
                
                return self.stats['total_errors'] == 0
                
        except Exception as e:
            logger.error(f"❌ TTS资产生成失败: {str(e)}")
            return False
    
    def _check_required_tables(self, conn: sqlite3.Connection) -> bool:
        """检查必需的数据库表"""
        required_tables = ['words_for_publish', 'word_processing_queue', 'tts_assets']
        
        for table in required_tables:
            cursor = conn.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                (table,)
            )
            if not cursor.fetchone():
                logger.error(f"❌ 必需的表 '{table}' 不存在")
                return False
        
        logger.info("✅ 所有必需的表都存在")
        return True
    
    def _get_words_for_tts_generation(self, conn: sqlite3.Connection, limit: Optional[int] = None) -> List[WordRecord]:
        """获取需要TTS生成的单词"""
        query = """
            SELECT w.id, w.word, w.learningLanguage, w.scaffoldingLanguage, 
                   w.contentJson, w.contentVersion
            FROM words_for_publish w
            JOIN word_processing_queue q ON w.id = q.id
            WHERE q.contentAiReviewed = TRUE 
              AND q.ttsIdGenerated = FALSE
              AND w.contentVersion = 'v4.0'
            ORDER BY w.id
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        cursor = conn.execute(query)
        records = cursor.fetchall()
        
        return [WordRecord(*record) for record in records]
    
    def _process_batch(self, conn: sqlite3.Connection, words: List[WordRecord]):
        """处理单个批次"""
        conn.execute("BEGIN TRANSACTION;")
        
        try:
            for word_record in words:
                result = self._process_single_word(conn, word_record)
                
                # 更新统计
                self.stats['total_processed'] += 1
                if result.success:
                    self.stats['total_success'] += 1
                    self.stats['total_phonetic_fixes'] += result.phonetic_fixes
                    self.stats['total_tts_assets'] += result.tts_assets_created
                else:
                    self.stats['total_errors'] += 1
                    logger.warning(f"⚠️ 处理单词 {result.word} 失败: {result.error_message}")
            
            conn.execute("COMMIT;")
            
        except Exception as e:
            conn.execute("ROLLBACK;")
            logger.error(f"❌ 批次处理失败，已回滚: {str(e)}")
            raise
    
    def _process_single_word(self, conn: sqlite3.Connection, word_record: WordRecord) -> ProcessingResult:
        """处理单个单词：音标标准化 + TTS ID生成"""
        try:
            # 1. 解析ContentJson
            content_data = json.loads(word_record.content_json)
            
            # 2. 音标类型标准化
            phonetic_fixes = self._standardize_phonetic_types_in_content(content_data)
            
            # 3. 提取TTS内容
            tts_contents = self._extract_tts_content_from_json(content_data, word_record)
            
            if not tts_contents:
                # 即使没有TTS内容，如果有音标修复也要更新
                if phonetic_fixes > 0:
                    updated_content_json = json.dumps(content_data, ensure_ascii=False)
                    conn.execute("""
                        UPDATE words_for_publish 
                        SET contentJson = ?, updatedAt = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (updated_content_json, word_record.id))
                
                return ProcessingResult(
                    word_id=word_record.id,
                    word=word_record.word,
                    success=True,
                    phonetic_fixes=phonetic_fixes,
                    tts_assets_created=0,
                    error_message=None
                )
            
            # 4. 创建TTS资产
            tts_assets, tts_id_mappings = self._create_tts_assets(tts_contents)
            
            # 5. 保存TTS资产到数据库
            self._save_tts_assets_to_db(conn, tts_assets)
            
            # 6. 更新ContentJson
            updated_content = self._update_content_json_with_tts_ids(content_data, tts_id_mappings)
            
            # 7. 更新数据库记录
            tts_hash_list = json.dumps([asset.tts_id for asset in tts_assets])
            
            conn.execute("""
                UPDATE words_for_publish 
                SET contentJson = ?, ttsHashList = ?, updatedAt = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (json.dumps(updated_content, ensure_ascii=False), tts_hash_list, word_record.id))
            
            return ProcessingResult(
                word_id=word_record.id,
                word=word_record.word,
                success=True,
                phonetic_fixes=phonetic_fixes,
                tts_assets_created=len(tts_assets),
                error_message=None
            )
            
        except Exception as e:
            return ProcessingResult(
                word_id=word_record.id,
                word=word_record.word,
                success=False,
                phonetic_fixes=0,
                tts_assets_created=0,
                error_message=str(e)
            )

    def _standardize_phonetic_types_in_content(self, content_data: dict) -> int:
        """标准化ContentJson中的音标类型，返回修复数量"""
        content = content_data.get('content', {})
        phonetic_symbols = content.get('phoneticSymbols', [])

        fixes_count = 0

        for phonetic in phonetic_symbols:
            if 'type' in phonetic:
                original_type = phonetic['type']
                standardized_type = self._standardize_phonetic_type(original_type)

                if original_type != standardized_type:
                    phonetic['type'] = standardized_type
                    fixes_count += 1

                    # 记录映射统计
                    if original_type not in self.phonetic_type_mappings:
                        self.phonetic_type_mappings[original_type] = {'target': standardized_type, 'count': 0}
                    self.phonetic_type_mappings[original_type]['count'] += 1

        return fixes_count

    def _standardize_phonetic_type(self, phonetic_type: str) -> str:
        """标准化音标类型为三种标准类型之一：name、bre、ipa"""
        if not phonetic_type:
            return "name"

        # 转换为小写并清理
        normalized_type = phonetic_type.lower().strip()

        # 移除括号及其内容，如 "(noun)", "(verb)" 等
        normalized_type = re.sub(r'\s*\([^)]*\)\s*', '', normalized_type)

        # 移除多余的空格和特殊字符
        normalized_type = re.sub(r'[^\w]', '', normalized_type)

        # 标准化映射
        if any(keyword in normalized_type for keyword in ['bre', 'british', 'br']):
            return "bre"
        elif any(keyword in normalized_type for keyword in ['name', 'nam', 'american', 'ame', 'us']):
            return "name"
        elif any(keyword in normalized_type for keyword in ['ipa', 'international']):
            return "ipa"
        else:
            return "name"  # 默认

    def _extract_tts_content_from_json(self, content_json: dict, word_record: WordRecord) -> List[TTSContent]:
        """从ContentJson中提取TTS内容"""
        tts_contents = []

        try:
            content = content_json.get('content', {})

            # 1. 提取音标 - 处理所有音标类型（bre, name, ipa等）
            phonetic_symbols = content.get('phoneticSymbols', [])
            for i, phonetic in enumerate(phonetic_symbols):
                symbol = phonetic.get('symbol', '')
                phonetic_type = phonetic.get('type', 'name')

                if symbol:
                    # 清理音标符号
                    cleaned_symbol = self.text_normalizer.clean_phonetic_symbols(symbol)

                    if cleaned_symbol:  # 确保清理后还有内容
                        # 构建TTS类型
                        tts_type = f"phonetic_{phonetic_type}"

                        # 标准化音标文本
                        normalized = self.text_normalizer.normalize_text_for_tts(
                            cleaned_symbol, word_record.learning_language,
                            tts_type,
                            word_record.word, "phonetic"
                        )

                        tts_contents.append(TTSContent(
                            original_text=normalized.context_key,
                            normalized_text=normalized.context_key,
                            text_to_speak=word_record.word,  # 音标的发音是单词本身
                            tts_type=tts_type,
                            context_path=f"content.phoneticSymbols[{i}]",
                            word_id=word_record.id,
                            language=word_record.learning_language
                        ))

            # 2. 提取例句 - 只处理learningLanguage
            usage_examples = content.get('usageExamples', [])
            for cat_i, category in enumerate(usage_examples):
                examples = category.get('examples', [])
                for ex_i, example in enumerate(examples):
                    learning_text = example.get('learningLanguage', '')

                    if learning_text:
                        # 标准化例句文本
                        normalized = self.text_normalizer.normalize_text_for_tts(
                            learning_text, word_record.learning_language,
                            "example_sentence", word_record.word, "example"
                        )

                        tts_contents.append(TTSContent(
                            original_text=normalized.context_key,
                            normalized_text=normalized.context_key,
                            text_to_speak=learning_text,
                            tts_type="example_sentence",
                            context_path=f"content.usageExamples[{cat_i}].examples[{ex_i}].learningLanguage",
                            word_id=word_record.id,
                            language=word_record.learning_language
                        ))

                    # 3. 提取短语分解 - 只处理phrase字段
                    phrase_breakdown = example.get('phraseBreakdown', [])
                    for ph_i, phrase in enumerate(phrase_breakdown):
                        phrase_text = phrase.get('phrase', '')

                        if phrase_text:
                            # 标准化短语文本
                            normalized = self.text_normalizer.normalize_text_for_tts(
                                phrase_text, word_record.learning_language,
                                "phrase_breakdown", word_record.word, "phrase"
                            )

                            tts_contents.append(TTSContent(
                                original_text=normalized.context_key,
                                normalized_text=normalized.context_key,
                                text_to_speak=phrase_text,
                                tts_type="phrase_breakdown",
                                context_path=f"content.usageExamples[{cat_i}].examples[{ex_i}].phraseBreakdown[{ph_i}].phrase",
                                word_id=word_record.id,
                                language=word_record.learning_language
                            ))

        except Exception as e:
            logger.warning(f"提取TTS内容失败 - 单词 {word_record.word}: {str(e)}")

        return tts_contents

    def _create_tts_assets(self, tts_contents: List[TTSContent]) -> Tuple[List[TTSAsset], Dict[str, str]]:
        """创建TTS资产"""
        tts_assets = []
        tts_id_mappings = {}  # {context_path: tts_id}

        for content in tts_contents:
            # 生成TTS ID
            tts_id = self.hash_generator.generate_text_hash(content.normalized_text)

            # 创建TTS资产
            tts_asset = TTSAsset(
                tts_id=tts_id,
                original_text=content.original_text,
                normalized_text=content.normalized_text,
                text_hash=tts_id,  # 与ttsId相同
                text_to_speak=content.text_to_speak,
                learning_language=content.language,
                tts_type=content.tts_type,
                status='pending'
            )

            tts_assets.append(tts_asset)
            tts_id_mappings[content.context_path] = tts_id

        return tts_assets, tts_id_mappings

    def _save_tts_assets_to_db(self, conn: sqlite3.Connection, tts_assets: List[TTSAsset]):
        """保存TTS资产到数据库"""
        for asset in tts_assets:
            # 使用INSERT OR IGNORE避免重复插入
            conn.execute("""
                INSERT OR IGNORE INTO tts_assets
                (ttsId, originalText, normalizedText, textHash, textToSpeak,
                 learningLanguage, ttsType, status, createdAt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (
                asset.tts_id, asset.original_text, asset.normalized_text,
                asset.text_hash, asset.text_to_speak, asset.learning_language,
                asset.tts_type, asset.status
            ))

    def _update_content_json_with_tts_ids(self, content_json: dict, tts_id_mappings: Dict[str, str]) -> dict:
        """更新ContentJson，添加ttsId字段"""
        import copy
        updated_content = copy.deepcopy(content_json)

        try:
            content = updated_content.get('content', {})

            # 1. 更新音标
            phonetic_symbols = content.get('phoneticSymbols', [])
            for i, phonetic in enumerate(phonetic_symbols):
                context_path = f"content.phoneticSymbols[{i}]"
                if context_path in tts_id_mappings:
                    phonetic['ttsId'] = tts_id_mappings[context_path]

            # 2. 更新例句
            usage_examples = content.get('usageExamples', [])
            for cat_i, category in enumerate(usage_examples):
                examples = category.get('examples', [])
                for ex_i, example in enumerate(examples):
                    # 更新例句ttsId
                    context_path = f"content.usageExamples[{cat_i}].examples[{ex_i}].learningLanguage"
                    if context_path in tts_id_mappings:
                        example['ttsId'] = tts_id_mappings[context_path]

                    # 更新短语分解ttsId
                    phrase_breakdown = example.get('phraseBreakdown', [])
                    for ph_i, phrase in enumerate(phrase_breakdown):
                        phrase_context_path = f"content.usageExamples[{cat_i}].examples[{ex_i}].phraseBreakdown[{ph_i}].phrase"
                        if phrase_context_path in tts_id_mappings:
                            phrase['ttsId'] = tts_id_mappings[phrase_context_path]

        except Exception as e:
            logger.warning(f"更新ContentJson失败: {str(e)}")

        return updated_content

    def _update_kanban_status(self, conn: sqlite3.Connection, successful_word_ids: List[int]):
        """更新看板状态：设置ttsIdGenerated=TRUE"""
        if not successful_word_ids:
            return

        placeholders = ','.join(['?' for _ in successful_word_ids])
        conn.execute(f"""
            UPDATE word_processing_queue
            SET ttsIdGenerated = TRUE, updatedAt = CURRENT_TIMESTAMP
            WHERE id IN ({placeholders})
        """, successful_word_ids)

        logger.info(f"✅ 更新了 {len(successful_word_ids)} 个单词的看板状态")

    def _was_processed_successfully(self, word_id: int) -> bool:
        """检查单词是否处理成功（简化实现）"""
        # 在实际实现中，这里应该检查处理结果
        # 为了简化，这里假设所有处理都成功
        return True

    def _print_final_statistics(self):
        """打印最终统计信息"""
        stats = self.stats
        duration = (stats['end_time'] - stats['start_time']).total_seconds()

        logger.info("=" * 60)
        logger.info("🎉 TTS资产生成完成!")
        logger.info("=" * 60)
        logger.info(f"📊 总处理记录: {stats['total_processed']}")
        logger.info(f"✅ 成功处理: {stats['total_success']}")
        logger.info(f"❌ 失败记录: {stats['total_errors']}")
        logger.info(f"🔧 音标修复: {stats['total_phonetic_fixes']}")
        logger.info(f"🎵 创建TTS资产: {stats['total_tts_assets']}")
        logger.info(f"⏱️ 总耗时: {duration:.2f}秒")

        if stats['total_processed'] > 0:
            success_rate = stats['total_success'] / stats['total_processed'] * 100
            speed = stats['total_processed'] / duration if duration > 0 else 0
            logger.info(f"📈 成功率: {success_rate:.2f}%")
            logger.info(f"⚡ 处理速度: {speed:.2f}条/秒")

        # 显示音标类型转换统计
        if self.phonetic_type_mappings:
            logger.info("\n📋 音标类型转换统计:")
            for original_type, mapping in self.phonetic_type_mappings.items():
                logger.info(f"  '{original_type}' -> '{mapping['target']}': {mapping['count']}次")

        logger.info("=" * 60)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description="TTS资产生成器 - 音标标准化 + TTS ID生成回写",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 处理所有需要TTS生成的单词
  python 01_tts_asset_generator.py

  # 限制处理数量
  python 01_tts_asset_generator.py --limit 100

  # 指定数据库路径
  python 01_tts_asset_generator.py --db-path ./custom_db.db

  # 自定义批次大小
  python 01_tts_asset_generator.py --batch-size 1000
        """
    )

    parser.add_argument(
        '--db-path',
        default="/Users/<USER>/Desktop/01-Projects/08-App/01-Senseword/02-Senseword-iOS/senseword-content-factory/01-EN/SQLite/senseword_content_v4.db",
        help='数据库文件路径'
    )

    parser.add_argument(
        '--limit',
        type=int,
        help='限制处理的单词数量'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=500,
        help='批处理大小 (默认: 500)'
    )

    parser.add_argument(
        '--stats-only',
        action='store_true',
        help='仅显示统计信息，不执行处理'
    )

    args = parser.parse_args()

    # 检查数据库文件
    if not os.path.exists(args.db_path):
        logger.error(f"❌ 数据库文件不存在: {args.db_path}")
        sys.exit(1)

    logger.info("🎯 TTS资产生成器")
    logger.info("=" * 60)
    logger.info(f"📍 数据库路径: {args.db_path}")

    # 创建生成器
    generator = TTSAssetGenerator(args.db_path, args.batch_size)

    if args.stats_only:
        # 仅显示统计信息
        try:
            with sqlite3.connect(args.db_path) as conn:
                if not generator._check_required_tables(conn):
                    sys.exit(1)

                words_to_process = generator._get_words_for_tts_generation(conn, args.limit)

                logger.info(f"📊 需要TTS生成的单词数: {len(words_to_process)}")

                # 显示语言分布
                language_dist = {}
                for word in words_to_process:
                    key = f"{word.learning_language}->{word.scaffolding_language}"
                    language_dist[key] = language_dist.get(key, 0) + 1

                if language_dist:
                    logger.info("🌐 语言对分布:")
                    for lang_pair, count in language_dist.items():
                        logger.info(f"  {lang_pair}: {count} 个单词")

        except Exception as e:
            logger.error(f"❌ 获取统计信息失败: {str(e)}")
            sys.exit(1)
    else:
        # 执行TTS资产生成
        success = generator.process_words_for_tts_generation(args.limit)

        if success:
            logger.info("🎉 TTS资产生成成功完成!")
            sys.exit(0)
        else:
            logger.error("❌ TTS资产生成过程中出现错误!")
            sys.exit(1)

if __name__ == "__main__":
    main()
