# TTS资产管理工作流程

## 概述

TTS资产管理工作流程是SenseWord v4.0的核心组件，负责TTS（文本转语音）资产的生成、验证和成本管理。本工作流程整合了音标标准化、TTS ID生成、资产验证和成本分析等功能。

## 🎯 核心功能

### 1. **音标标准化 + TTS ID生成回写**
- 统一音标类型为标准的 `bre`、`name`、`ipa` 三种类型
- 从ContentJson提取TTS内容（音标、例句、短语）
- 生成24位唯一TTS ID并回写到ContentJson
- 与看板式状态管理系统集成

### 2. **TTS资产验证**
- TTS资产完整性验证
- ContentJson与TTS资产映射验证
- 音标类型一致性检查
- 孤立资产检测

### 3. **TTS成本分析**
- 基于Azure TTS定价的成本计算
- 按类型和语言的成本分析
- 去重节省效果分析
- 成本优化建议

## 📁 脚本架构

```
tts_asset_management/
├── 01_tts_asset_generator.py      # TTS资产生成器
├── 02_tts_asset_validator.py      # TTS资产验证器
├── 03_tts_cost_analyzer.py        # TTS成本分析器
├── utils/                         # 工具类库
│   ├── __init__.py
│   ├── hash_generator.py          # 哈希生成器
│   └── text_normalizer.py         # 文本标准化器
└── README.md                      # 使用指南
```

## 🚀 使用指南

### 前置条件

1. **数据库要求**：
   - v4.0版本的SenseWord数据库
   - 包含 `words_for_publish`、`word_processing_queue`、`tts_assets` 表

2. **Python环境**：
   - Python 3.7+
   - 标准库（无需额外依赖）

### 基本使用流程

#### 1. TTS资产生成

```bash
# 处理所有需要TTS生成的单词
python 01_tts_asset_generator.py

# 限制处理数量（用于测试）
python 01_tts_asset_generator.py --limit 100

# 仅显示统计信息
python 01_tts_asset_generator.py --stats-only

# 自定义数据库路径
python 01_tts_asset_generator.py --db-path ./custom_db.db
```

#### 2. TTS资产验证

```bash
# 验证TTS资产完整性
python 02_tts_asset_validator.py --validate

# 生成TTS映射报告
python 02_tts_asset_validator.py --report --sample-size 50

# 同时执行验证和报告生成
python 02_tts_asset_validator.py --validate --report
```

#### 3. TTS成本分析

```bash
# 分析TTS成本
python 03_tts_cost_analyzer.py --analyze

# 生成成本分析报告
python 03_tts_cost_analyzer.py --report

# 同时执行分析和报告生成
python 03_tts_cost_analyzer.py --analyze --report
```

## 🔄 与看板状态管理集成

### 状态流转

```
contentAiReviewed=TRUE AND ttsIdGenerated=FALSE
                    ↓
            [TTS资产生成器处理]
                    ↓
            ttsIdGenerated=TRUE
```

### 数据流程

```
1. 读取待处理单词 → word_processing_queue表筛选
2. 音标类型标准化 → ContentJson更新
3. TTS内容提取 → 音标、例句、短语
4. TTS ID生成 → 24位唯一哈希
5. 资产创建 → tts_assets表插入
6. ContentJson回写 → 添加ttsId字段
7. 状态更新 → ttsIdGenerated=TRUE
```

## 📊 核心特性

### 音标标准化

- **支持的音标类型**：`bre`（英式）、`name`（美式）、`ipa`（国际音标）
- **自动转换**：将各种非标准格式转换为标准类型
- **兼容性处理**：处理括号、特殊字符等异常格式

### TTS ID生成

- **哈希算法**：SHA256
- **ID长度**：24位
- **唯一性保证**：微秒级时间戳确保唯一性
- **冲突处理**：多种冲突解决策略

### 成本计算

- **定价标准**：Azure TTS - $16 USD per 1 million characters
- **去重优化**：相同文本复用TTS资产
- **成本预测**：提供成本优化建议

## 📈 性能指标

### 预期处理速度
- **TTS资产生成**：150-200条记录/秒
- **资产验证**：1000条记录/秒
- **成本分析**：瞬时完成

### 预期结果
- **处理记录**：55,703条单词
- **生成TTS资产**：约100万个
- **成功率**：99.9%+

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败
```
错误: database is locked
解决: 关闭所有数据库连接，重新执行
```

#### 2. 必需表不存在
```
错误: 必需的表 'word_processing_queue' 不存在
解决: 运行数据库初始化脚本创建必需的表
```

#### 3. 内存不足
```
错误: MemoryError
解决: 减少批次大小，使用 --batch-size 参数
```

### 日志文件

| 日志文件 | 内容 |
|---------|------|
| `tts_asset_generation.log` | TTS资产生成详细日志 |
| `tts_asset_validation.log` | 资产验证过程日志 |
| `tts_cost_analysis.log` | 成本分析日志 |

## 🎉 架构优势

### 1. **统一整合**
- 将音标标准化和TTS ID生成整合为一个原子操作
- 避免数据不一致和重复处理

### 2. **看板集成**
- 与word_processing_queue表无缝集成
- 支持精细化的处理流程控制

### 3. **生产级设计**
- 完整的错误处理和事务安全
- 详细的统计信息和进度反馈
- 多种运行模式满足不同使用场景

### 4. **成本优化**
- 智能去重机制节省TTS成本
- 详细的成本分析和优化建议
- 透明的成本计算和预测

## 📝 技术支持

如遇到问题，请提供以下信息：
1. 错误日志文件
2. 数据库当前状态
3. 执行的具体命令
4. 系统环境信息

## 🔗 相关文档

- [SenseWord v4.0数据库架构](../../../04-数据库初始化/)
- [看板式状态管理系统](../../../09-状态管理/)
- [原始TTS处理脚本](../tts_processing/)

---

**版本**: v1.0  
**更新时间**: 2025-07-14  
**维护者**: AI Assistant
