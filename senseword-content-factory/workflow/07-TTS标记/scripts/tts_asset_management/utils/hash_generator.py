#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
哈希生成工具 - TTS资产唯一标识符生成
实现文本哈希生成算法，为TTS资产创建24位唯一标识符

作者: AI Assistant
日期: 2025-07-12
版本: v1.0
"""

import hashlib
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Set, Optional

class HashCollisionStrategy(Enum):
    """哈希冲突处理策略"""
    APPEND_COUNTER = "append_counter"
    REGENERATE_WITH_SALT = "regenerate_with_salt"
    USE_LONGER_HASH = "use_longer_hash"

@dataclass
class HashResult:
    """哈希生成结果"""
    original_text: str
    normalized_text: str
    hash_value: str
    algorithm: str
    collision_resolved: bool
    generation_time: datetime

class HashGenerator:
    """哈希生成器"""
    
    def __init__(self, hash_length: int = 24, collision_strategy: HashCollisionStrategy = HashCollisionStrategy.APPEND_COUNTER):
        self.hash_length = hash_length
        self.collision_strategy = collision_strategy
        self.generated_hashes: Set[str] = set()
        self.collision_count = 0
    
    def generate_text_hash(self, normalized_text: str, language: str = None) -> str:
        """
        生成文本哈希 - 使用时间戳确保唯一性

        Args:
            normalized_text: 标准化后的文本或完整的组合文本（如 "en|phrase|text"）
            language: 语言代码（可选，如果normalized_text已包含完整信息则不需要）

        Returns:
            str: 24位哈希值
        """
        if not normalized_text:
            raise ValueError("标准化文本不能为空")

        # 如果已经是完整的组合文本（包含|分隔符），直接使用
        # 否则构建哈希输入：文本 + 语言
        if language and '|' not in normalized_text:
            hash_input = f"{normalized_text}|{language}"
        else:
            hash_input = normalized_text

        # 添加微秒级时间戳确保唯一性
        import time
        timestamp = str(int(time.time() * 1000000))  # 微秒级时间戳
        unique_hash_input = f"{hash_input}#{timestamp}"

        # 计算SHA256哈希
        hash_object = hashlib.sha256(unique_hash_input.encode('utf-8'))
        full_hash = hash_object.hexdigest()

        # 取前24位
        hash_value = full_hash[:self.hash_length]

        # 记录生成的哈希（理论上不会有冲突，但保留记录）
        self.generated_hashes.add(hash_value)

        return hash_value
    
    def batch_generate_hashes(self, texts: List[tuple]) -> Dict[str, str]:
        """
        批量生成哈希

        Args:
            texts: [(normalized_text, language), ...] 的列表

        Returns:
            Dict[str, str]: {input_key: hash_value} 的映射
        """
        results = {}

        for normalized_text, language in texts:
            try:
                input_key = f"{normalized_text}|{language}" if language else normalized_text
                hash_value = self.generate_text_hash(normalized_text, language)
                results[input_key] = hash_value

                # 添加微小延迟确保时间戳不同
                import time
                time.sleep(0.000001)  # 1微秒延迟

            except Exception as e:
                print(f"警告: 生成哈希失败 - {normalized_text}: {str(e)}")
                continue

        return results
    
    def detect_hash_collision(self, hash_value: str, existing_hashes: Set[str]) -> bool:
        """检测哈希冲突"""
        return hash_value in existing_hashes
    
    def _resolve_collision(self, original_input: str, collision_hash: str) -> str:
        """解决哈希冲突"""
        self.collision_count += 1
        
        if self.collision_strategy == HashCollisionStrategy.APPEND_COUNTER:
            return self._resolve_with_counter(original_input, collision_hash)
        elif self.collision_strategy == HashCollisionStrategy.REGENERATE_WITH_SALT:
            return self._resolve_with_salt(original_input)
        elif self.collision_strategy == HashCollisionStrategy.USE_LONGER_HASH:
            return self._resolve_with_longer_hash(original_input)
        else:
            return self._resolve_with_counter(original_input, collision_hash)
    
    def _resolve_with_counter(self, original_input: str, collision_hash: str) -> str:
        """使用计数器解决冲突"""
        counter = 1
        while True:
            # 在原始输入后添加计数器
            modified_input = f"{original_input}#{counter}"
            hash_object = hashlib.sha256(modified_input.encode('utf-8'))
            new_hash = hash_object.hexdigest()[:self.hash_length]

            if new_hash not in self.generated_hashes:
                return new_hash

            counter += 1
            if counter > 100:  # 减少循环次数，更快失败
                # 使用时间戳作为后缀
                import time
                timestamp_suffix = str(int(time.time() * 1000000))[-8:]  # 取时间戳后8位
                fallback_input = f"{original_input}#{timestamp_suffix}"
                hash_object = hashlib.sha256(fallback_input.encode('utf-8'))
                fallback_hash = hash_object.hexdigest()[:self.hash_length]

                if fallback_hash not in self.generated_hashes:
                    return fallback_hash
                else:
                    # 最后的备选方案：使用更长的哈希
                    longer_hash = hash_object.hexdigest()[:self.hash_length + 4]
                    return longer_hash
    
    def _resolve_with_salt(self, original_input: str) -> str:
        """使用盐值解决冲突"""
        # 使用当前时间戳作为盐值
        salt = str(int(time.time() * 1000000))  # 微秒级时间戳
        salted_input = f"{original_input}#{salt}"
        
        hash_object = hashlib.sha256(salted_input.encode('utf-8'))
        new_hash = hash_object.hexdigest()[:self.hash_length]
        
        # 如果仍然冲突，递归调用
        if new_hash in self.generated_hashes:
            time.sleep(0.001)  # 等待1毫秒确保时间戳不同
            return self._resolve_with_salt(original_input)
        
        return new_hash
    
    def _resolve_with_longer_hash(self, original_input: str) -> str:
        """使用更长的哈希解决冲突"""
        hash_object = hashlib.sha256(original_input.encode('utf-8'))
        full_hash = hash_object.hexdigest()
        
        # 尝试不同长度的哈希
        for length in range(self.hash_length + 1, 64):
            longer_hash = full_hash[:length]
            if longer_hash not in self.generated_hashes:
                return longer_hash
        
        # 如果64位仍然冲突，使用盐值方法
        return self._resolve_with_salt(original_input)
    
    def generate_hash_result(self, original_text: str, normalized_text: str, language: str = 'en') -> HashResult:
        """生成完整的哈希结果"""
        start_time = datetime.now()
        
        hash_value = self.generate_text_hash(normalized_text, language)
        collision_resolved = self.collision_count > 0
        
        return HashResult(
            original_text=original_text,
            normalized_text=normalized_text,
            hash_value=hash_value,
            algorithm=f"SHA256-{self.hash_length}",
            collision_resolved=collision_resolved,
            generation_time=start_time
        )
    
    def get_statistics(self) -> Dict[str, int]:
        """获取哈希生成统计信息"""
        return {
            "total_generated": len(self.generated_hashes),
            "collision_count": self.collision_count,
            "collision_rate": self.collision_count / max(len(self.generated_hashes), 1) * 100
        }
    
    def load_existing_hashes(self, existing_hashes: Set[str]):
        """加载已存在的哈希值"""
        self.generated_hashes.update(existing_hashes)
    
    def clear_cache(self):
        """清空哈希缓存"""
        self.generated_hashes.clear()
        self.collision_count = 0

# 便捷函数
def generate_tts_id(text: str, language: str = 'en') -> str:
    """便捷的TTS ID生成函数"""
    generator = HashGenerator()
    return generator.generate_text_hash(text, language)

def generate_batch_tts_ids(texts: List[tuple]) -> Dict[str, str]:
    """便捷的批量TTS ID生成函数"""
    generator = HashGenerator()
    return generator.batch_generate_hashes(texts)

# 测试函数
def test_hash_generator():
    """测试哈希生成器"""
    generator = HashGenerator()
    
    test_texts = [
        ("hello world", "en"),
        ("你好世界", "zh"),
        ("/həˈləʊ/", "en"),
        ("Bonjour monde", "fr"),
        ("hello world", "en"),  # 重复测试冲突检测
    ]
    
    print("=== 哈希生成测试 ===")
    for text, lang in test_texts:
        hash_value = generator.generate_text_hash(text, lang)
        print(f"文本: {text} ({lang}) -> 哈希: {hash_value}")
    
    print("\n=== 统计信息 ===")
    stats = generator.get_statistics()
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    print("\n=== 批量生成测试 ===")
    batch_results = generator.batch_generate_hashes([
        ("example sentence", "en"),
        ("phrase breakdown", "en"),
        ("phonetic symbol", "en")
    ])
    
    for input_key, hash_value in batch_results.items():
        print(f"{input_key} -> {hash_value}")

def test_collision_handling():
    """测试冲突处理"""
    print("\n=== 冲突处理测试 ===")
    
    # 创建一个短哈希生成器来增加冲突概率
    generator = HashGenerator(hash_length=4)  # 4位哈希，容易冲突
    
    # 生成大量哈希来触发冲突
    for i in range(20):
        text = f"test text {i}"
        hash_value = generator.generate_text_hash(text, "en")
        print(f"文本: {text} -> 哈希: {hash_value}")
    
    stats = generator.get_statistics()
    print(f"\n冲突统计: {stats}")

if __name__ == "__main__":
    test_hash_generator()
    test_collision_handling()
