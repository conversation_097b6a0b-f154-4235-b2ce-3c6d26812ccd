#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本标准化工具 - TTS文本预处理
实现多语言文本标准化算法，确保TTS文本的一致性

作者: AI Assistant
日期: 2025-07-12
版本: v1.0
"""

import re
import unicodedata
from dataclasses import dataclass
from enum import Enum
from typing import Optional

class TTSTextType(Enum):
    """TTS文本类型 - 支持多种音标类型"""
    PHONETIC_BRE = "phonetic_bre"
    PHONETIC_NAME = "phonetic_name"
    PHONETIC_IPA = "phonetic_ipa"
    EXAMPLE_SENTENCE = "example_sentence"
    PHRASE_BREAKDOWN = "phrase_breakdown"

@dataclass
class NormalizedText:
    """标准化文本结果"""
    original: str
    normalized: str
    tts_ready: str
    language: str
    tts_type: str
    context_key: str

class TextNormalizer:
    """文本标准化器"""
    
    def __init__(self):
        # 语言特定的处理器
        self.language_processors = {
            'en': self._process_english,
            'zh': self._process_chinese,
            'ja': self._process_japanese,
            'ko': self._process_korean,
            'es': self._process_spanish,
            'fr': self._process_french
        }
    
    def normalize_text_for_tts(self, text: str, language: str, tts_type: str, word: str = "", context_type: str = "") -> NormalizedText:
        """
        标准化文本用于TTS处理
        
        Args:
            text: 原始文本
            language: 语言代码
            tts_type: TTS类型
            word: 关联的单词
            context_type: 上下文类型
            
        Returns:
            NormalizedText: 标准化结果
        """
        if not text or not isinstance(text, str):
            raise ValueError("文本不能为空或格式无效")
        
        if len(text) > 1000:
            raise ValueError("文本长度不能超过1000个字符")
        
        # 1. 基础清理
        cleaned = self._basic_cleanup(text)
        
        # 2. 语言特定处理
        processor = self.language_processors.get(language, self.language_processors['en'])
        normalized = processor(cleaned)
        
        # 3. TTS类型特定处理
        tts_ready = self._process_by_tts_type(normalized, tts_type)
        
        # 4. 生成上下文键
        context_key = self._generate_context_key(tts_ready, word, context_type, language, tts_type)
        
        return NormalizedText(
            original=text,
            normalized=normalized,
            tts_ready=tts_ready,
            language=language,
            tts_type=tts_type,
            context_key=context_key
        )
    
    def _basic_cleanup(self, text: str) -> str:
        """基础文本清理"""
        # 移除控制字符
        text = ''.join(char for char in text if unicodedata.category(char)[0] != 'C')
        
        # 标准化Unicode
        text = unicodedata.normalize('NFKC', text)
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def _process_english(self, text: str) -> str:
        """英语文本处理"""
        # 转换为小写
        text = text.lower()
        
        # 移除特殊字符，保留字母、数字、空格、撇号、连字符
        text = re.sub(r'[^\w\s\'-]', '', text)
        
        # 标准化空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def _process_chinese(self, text: str) -> str:
        """中文文本处理"""
        # 移除非中文字符和标点
        text = re.sub(r'[^\u4e00-\u9fff\w]', '', text)
        
        # 移除空格（中文通常不需要空格）
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def _process_japanese(self, text: str) -> str:
        """日语文本处理"""
        # 保留平假名、片假名、汉字
        text = re.sub(r'[^\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff\w]', '', text)
        
        # 移除空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def _process_korean(self, text: str) -> str:
        """韩语文本处理"""
        # 保留韩文字符
        text = re.sub(r'[^\uac00-\ud7af\w]', '', text)
        
        # 移除空格
        text = re.sub(r'\s+', '', text)
        
        return text
    
    def _process_spanish(self, text: str) -> str:
        """西班牙语文本处理"""
        # 转换为小写
        text = text.lower()
        
        # 保留字母、数字、空格、重音符号
        text = re.sub(r'[^\w\sñáéíóúü\'-]', '', text)
        
        # 标准化空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def _process_french(self, text: str) -> str:
        """法语文本处理"""
        # 转换为小写
        text = text.lower()
        
        # 保留字母、数字、空格、重音符号
        text = re.sub(r'[^\w\sàâäéèêëïîôöùûüÿç\'-]', '', text)
        
        # 标准化空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def _process_by_tts_type(self, text: str, tts_type: str) -> str:
        """根据TTS类型进行特定处理"""
        if tts_type == TTSTextType.PHONETIC_BRE.value or tts_type == TTSTextType.PHONETIC_NAME.value:
            # 音标处理：移除音标符号的斜杠
            text = text.replace('/', '').replace('\\', '')
            # 保留音标特殊字符
            return text
        
        elif tts_type == TTSTextType.EXAMPLE_SENTENCE.value:
            # 例句处理：确保句子结构完整
            text = text.strip()
            if text and not text[-1] in '.!?':
                text += '.'
            return text
        
        elif tts_type == TTSTextType.PHRASE_BREAKDOWN.value:
            # 短语处理：移除多余标点
            text = re.sub(r'[^\w\s\'-]', '', text)
            return text.strip()
        
        return text
    
    def _generate_context_key(self, text: str, word: str, context_type: str, language: str, tts_type: str = "") -> str:
        """生成TTS上下文标识键"""
        # 根据文档格式: language|type|word|subtype|text
        if tts_type.startswith('phonetic_'):
            # 音标格式: en|phonetic|hello|bre|/həˈləʊ/
            phonetic_type = tts_type.replace('phonetic_', '')
            components = [language, 'phonetic', word.lower(), phonetic_type, text]
        elif tts_type == 'example_sentence':
            # 例句格式: en|example|Hello, how are you?
            components = [language, 'example', text]
        elif tts_type == 'phrase_breakdown':
            # 短语格式: en|phrase|say hello
            components = [language, 'phrase', text]
        else:
            # 默认格式
            components = [language, context_type, word.lower() if word else "unknown", text]

        # 移除空组件
        components = [comp for comp in components if comp]

        return '|'.join(components)
    
    def normalize_phonetic_type(self, phonetic_type: str) -> str:
        """
        标准化音标类型，只允许三种标准类型：name、bre、ipa
        处理各种异常格式，如 'phonetic_bre (noun)'、'BrE'、'NAmE' 等
        """
        if not phonetic_type:
            return "name"  # 默认为name

        # 转换为小写并清理，移除括号内容和特殊字符
        normalized_type = phonetic_type.lower().strip()

        # 移除括号及其内容，如 "(noun)", "(verb)" 等
        import re
        normalized_type = re.sub(r'\s*\([^)]*\)\s*', '', normalized_type)

        # 移除多余的空格和特殊字符
        normalized_type = re.sub(r'[^\w]', '', normalized_type)

        # 标准化映射 - 只允许三种标准类型
        if any(keyword in normalized_type for keyword in ['bre', 'british', 'br']):
            return "bre"
        elif any(keyword in normalized_type for keyword in ['name', 'nam', 'american', 'ame', 'us']):
            return "name"  # NAmE -> name
        elif any(keyword in normalized_type for keyword in ['ipa', 'international']):
            return "ipa"
        else:
            # 对于完全未知的类型，默认为name
            return "name"

    def clean_phonetic_symbols(self, symbol: str) -> str:
        """清理音标符号"""
        if not symbol:
            return ""

        # 移除音标边界符号
        symbol = symbol.strip('/')

        # 移除括号内容 (如 Name(xxx) 中的 (xxx))
        symbol = re.sub(r'\([^)]*\)', '', symbol)

        # 标准化空格
        symbol = re.sub(r'\s+', ' ', symbol.strip())

        return symbol
    
    def standardize_example_sentence(self, sentence: str, language: str) -> str:
        """标准化例句"""
        if not sentence:
            return ""
        
        # 基础清理
        sentence = self._basic_cleanup(sentence)
        
        # 语言特定处理
        processor = self.language_processors.get(language, self.language_processors['en'])
        sentence = processor(sentence)
        
        # 确保句子结构
        if language == 'en' and sentence and not sentence[-1] in '.!?':
            sentence += '.'
        
        return sentence

# 便捷函数
def normalize_text(text: str, language: str = 'en', tts_type: str = 'example_sentence', word: str = "", context_type: str = "") -> NormalizedText:
    """便捷的文本标准化函数"""
    normalizer = TextNormalizer()
    return normalizer.normalize_text_for_tts(text, language, tts_type, word, context_type)

def clean_phonetic(symbol: str) -> str:
    """便捷的音标清理函数"""
    normalizer = TextNormalizer()
    return normalizer.clean_phonetic_symbols(symbol)

# 测试函数
def test_normalizer():
    """测试标准化器"""
    normalizer = TextNormalizer()
    
    test_cases = [
        ("Hello, world!", "en", "example_sentence", "hello", "example"),
        ("/həˈləʊ/", "en", "phonetic_bre", "hello", "phonetic"),
        ("你好世界！", "zh", "example_sentence", "hello", "translation"),
        ("Bonjour, monde!", "fr", "example_sentence", "hello", "example")
    ]
    
    for text, lang, tts_type, word, context in test_cases:
        result = normalizer.normalize_text_for_tts(text, lang, tts_type, word, context)
        print(f"原文: {result.original}")
        print(f"标准化: {result.normalized}")
        print(f"TTS就绪: {result.tts_ready}")
        print(f"上下文键: {result.context_key}")
        print("-" * 50)

if __name__ == "__main__":
    test_normalizer()
