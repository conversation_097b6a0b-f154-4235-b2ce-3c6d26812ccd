# TTS资产管理工作流程可视化文档

## 📋 概述

本文档通过详细的Mermaid图表和真实数据示例，全面展示TTS资产管理工作流程的工作原理，重点关注音标标准化、哈希ID生成和验证逻辑。

## 🏗️ 系统架构图

```mermaid
graph TB
    subgraph "🎯 TTS资产管理系统架构"
        subgraph "📊 数据层"
            DB[(🗄️ SQLite数据库)]
            WFP[📝 words_for_publish]
            WPQ[📋 word_processing_queue]
            TTS[🎵 tts_assets]
            
            DB --> WFP
            DB --> WPQ
            DB --> TTS
        end
        
        subgraph "🔧 核心处理层"
            GEN[🎯 01_tts_asset_generator.py<br/>音标标准化 + TTS ID生成]
            VAL[🔍 02_tts_asset_validator.py<br/>资产验证器]
            COST[💰 03_tts_cost_analyzer.py<br/>成本分析器]
        end
        
        subgraph "🛠️ 工具层"
            HASH[🔑 HashGenerator<br/>哈希生成器]
            NORM[📝 TextNormalizer<br/>文本标准化器]
        end
        
        subgraph "📤 输出层"
            LOG[📋 日志文件]
            RPT[📊 分析报告]
            JSON[📄 更新的ContentJson]
        end
    end
    
    %% 数据流连接
    WFP --> GEN
    WPQ --> GEN
    GEN --> TTS
    GEN --> JSON
    
    TTS --> VAL
    TTS --> COST
    
    VAL --> RPT
    COST --> RPT
    
    HASH --> GEN
    NORM --> GEN
    
    GEN --> LOG
    VAL --> LOG
    COST --> LOG
    
    %% 样式定义
    classDef dataLayer fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processLayer fill:#FFF2CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef toolLayer fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputLayer fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    
    class DB,WFP,WPQ,TTS dataLayer
    class GEN,VAL,COST processLayer
    class HASH,NORM toolLayer
    class LOG,RPT,JSON outputLayer
```

## 🔄 完整工作流程图

```mermaid
flowchart TD
    START([🚀 开始TTS资产管理])
    
    subgraph "📋 数据筛选阶段"
        QUERY[🔍 查询待处理单词<br/>contentAiReviewed=TRUE<br/>AND ttsIdGenerated=FALSE]
        CHECK{📊 是否有待处理单词?}
        BATCH[📦 分批处理<br/>默认500条/批次]
    end
    
    subgraph "🔧 音标标准化阶段"
        PARSE[📄 解析ContentJson]
        PHONETIC[🎯 音标类型标准化]
        STANDARD{🔍 是否需要标准化?}
        FIX[🔧 修复音标类型<br/>BrE→bre, NAmE→name]
        COUNT[📊 统计修复数量]
    end
    
    subgraph "🎵 TTS内容提取阶段"
        EXTRACT[🎯 提取TTS内容]
        PHONE[🔤 音标内容<br/>phoneticSymbols]
        EXAMPLE[💬 例句内容<br/>usageExamples]
        PHRASE[📝 短语内容<br/>phraseBreakdown]
    end
    
    subgraph "🔑 TTS ID生成阶段"
        NORMALIZE[📝 文本标准化]
        HASH[🔑 生成24位哈希ID]
        CREATE[🎵 创建TTS资产记录]
        MAPPING[🗺️ 建立ID映射关系]
    end
    
    subgraph "📝 数据回写阶段"
        UPDATE[📄 更新ContentJson<br/>添加ttsId字段]
        SAVE[💾 保存到数据库]
        STATUS[✅ 更新看板状态<br/>ttsIdGenerated=TRUE]
    end
    
    COMPLETE([🎉 处理完成])
    
    %% 主流程连接
    START --> QUERY
    QUERY --> CHECK
    CHECK -->|有数据| BATCH
    CHECK -->|无数据| COMPLETE
    
    BATCH --> PARSE
    PARSE --> PHONETIC
    PHONETIC --> STANDARD
    STANDARD -->|需要| FIX
    STANDARD -->|不需要| EXTRACT
    FIX --> COUNT
    COUNT --> EXTRACT
    
    EXTRACT --> PHONE
    EXTRACT --> EXAMPLE
    EXTRACT --> PHRASE
    
    PHONE --> NORMALIZE
    EXAMPLE --> NORMALIZE
    PHRASE --> NORMALIZE
    
    NORMALIZE --> HASH
    HASH --> CREATE
    CREATE --> MAPPING
    
    MAPPING --> UPDATE
    UPDATE --> SAVE
    SAVE --> STATUS
    STATUS --> COMPLETE
    
    %% 样式定义
    classDef startEnd fill:#FFE6CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef dataProcess fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef phoneticProcess fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef ttsProcess fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef hashProcess fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef saveProcess fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000
    
    class START,COMPLETE startEnd
    class QUERY,BATCH,PARSE dataProcess
    class PHONETIC,STANDARD,FIX,COUNT phoneticProcess
    class EXTRACT,PHONE,EXAMPLE,PHRASE ttsProcess
    class NORMALIZE,HASH,CREATE,MAPPING hashProcess
    class UPDATE,SAVE,STATUS saveProcess
    class CHECK,STANDARD decision
```

## ⏱️ 时序图：单词处理流程

```mermaid
sequenceDiagram
    participant U as 👤 用户
    participant G as 🎯 TTS生成器
    participant DB as 🗄️ 数据库
    participant H as 🔑 哈希生成器
    participant N as 📝 文本标准化器
    
    Note over U,N: 🎯 处理单词 "hello" 的完整流程
    
    U->>G: 🚀 启动TTS资产生成
    G->>DB: 🔍 查询待处理单词
    
    Note over DB: contentAiReviewed=TRUE<br/>AND ttsIdGenerated=FALSE
    
    DB-->>G: 📋 返回单词列表<br/>[{id:1, word:"hello", ...}]
    
    loop 📦 批处理每个单词
        G->>DB: 📄 获取ContentJson
        DB-->>G: 📝 返回内容数据
        
        Note over G: 🔧 音标标准化阶段
        G->>G: 🎯 检查音标类型<br/>BrE → bre
        G->>G: 📊 统计修复: +1
        
        Note over G: 🎵 TTS内容提取阶段
        G->>G: 🔤 提取音标: /həˈləʊ/
        G->>G: 💬 提取例句: "Hello, world!"
        G->>G: 📝 提取短语: "say hello"
        
        Note over G: 🔑 TTS ID生成阶段
        G->>N: 📝 标准化文本<br/>"en|phonetic|hello|bre|/həˈləʊ/"
        N-->>G: ✅ 返回标准化文本
        
        G->>H: 🔑 生成哈希ID
        H-->>G: 🎯 返回24位ID<br/>"a1b2c3d4e5f6g7h8i9j0k1l2"
        
        G->>DB: 🎵 创建TTS资产记录
        G->>G: 🗺️ 建立映射关系
        
        Note over G: 📝 数据回写阶段
        G->>G: 📄 更新ContentJson<br/>添加ttsId字段
        G->>DB: 💾 保存更新的内容
        G->>DB: ✅ 更新状态<br/>ttsIdGenerated=TRUE
    end
    
    G-->>U: 🎉 处理完成<br/>统计信息报告
```
## 🔄 数据结构转化流程

### 📊 音标标准化转化过程

```mermaid
graph LR
    subgraph "🎯 音标标准化转化"
        subgraph "📥 输入数据"
            INPUT["📝 原始音标数据<br/>symbol: /həˈləʊ/<br/>type: BrE"]
        end

        subgraph "🔧 处理过程"
            DETECT["🔍 检测非标准类型<br/>BrE ≠ 标准类型"]
            CLEAN["🧹 清理特殊字符<br/>移除括号、空格"]
            MAP["🗺️ 类型映射<br/>BrE → bre<br/>NAmE → name<br/>IPA → ipa"]
            VALIDATE["✅ 验证结果<br/>确保为标准类型"]
        end

        subgraph "📤 输出数据"
            OUTPUT["📝 标准化音标数据<br/>symbol: /həˈləʊ/<br/>type: bre<br/>ttsId: a1b2c3..."]
        end

        INPUT --> DETECT
        DETECT --> CLEAN
        CLEAN --> MAP
        MAP --> VALIDATE
        VALIDATE --> OUTPUT
    end

    %% 样式定义
    classDef inputStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputStyle fill:#D5E8D4,stroke:#000000,stroke-width:3px,color:#000000

    class INPUT inputStyle
    class DETECT,CLEAN,MAP,VALIDATE processStyle
    class OUTPUT outputStyle
```

### 🔑 哈希ID生成转化过程

```mermaid
graph TD
    subgraph "🔑 TTS ID生成流程"
        subgraph "📥 输入内容"
            WORD["🔤 单词: hello"]
            SYMBOL["🎵 音标: /həˈləʊ/"]
            TYPE["🏷️ 类型: bre"]
            LANG["🌍 语言: en"]
        end

        subgraph "📝 文本标准化"
            COMBINE["🔗 组合文本<br/>en|phonetic|hello|bre|/həˈləʊ/"]
            NORMALIZE["🧹 标准化处理<br/>清理特殊字符"]
            CONTEXT["📋 生成上下文键<br/>en_phonetic_hello_bre_həˈləʊ"]
        end

        subgraph "🔑 哈希生成"
            TIMESTAMP["⏰ 微秒时间戳<br/>1642678901234567"]
            SALT["🧂 添加盐值<br/>确保唯一性"]
            SHA256["🔐 SHA256哈希<br/>计算摘要"]
            TRUNCATE["✂️ 截取24位<br/>前24个字符"]
        end

        subgraph "📤 输出结果"
            TTSID["🎯 TTS ID<br/>a1b2c3d4e5f6g7h8i9j0k1l2"]
            ASSET["🎵 TTS资产记录<br/>ttsId: a1b2...<br/>textToSpeak: hello<br/>normalizedText: en_phonetic...<br/>ttsType: phonetic_bre"]
        end

        WORD --> COMBINE
        SYMBOL --> COMBINE
        TYPE --> COMBINE
        LANG --> COMBINE

        COMBINE --> NORMALIZE
        NORMALIZE --> CONTEXT

        CONTEXT --> TIMESTAMP
        TIMESTAMP --> SALT
        SALT --> SHA256
        SHA256 --> TRUNCATE

        TRUNCATE --> TTSID
        TTSID --> ASSET
    end

    %% 样式定义
    classDef inputStyle fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef normalizeStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef hashStyle fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputStyle fill:#D5E8D4,stroke:#000000,stroke-width:3px,color:#000000

    class WORD,SYMBOL,TYPE,LANG inputStyle
    class COMBINE,NORMALIZE,CONTEXT normalizeStyle
    class TIMESTAMP,SALT,SHA256,TRUNCATE hashStyle
    class TTSID,ASSET outputStyle
```

## 🔍 验证逻辑流程图

```mermaid
flowchart TD
    subgraph "🔍 TTS资产验证系统"
        START_VAL([🚀 开始验证])

        subgraph "📊 完整性验证"
            CHECK_TABLES[🔍 检查必需表<br/>words_for_publish<br/>tts_assets<br/>word_processing_queue]
            GET_STATS[📊 获取基础统计<br/>总单词数、总TTS资产数]
        end

        subgraph "🗺️ 映射关系验证"
            GET_WORDS[📋 获取有ttsHashList的单词]
            PROCESS_HASHLIST[🔍 处理ttsHashList中的每个ID]
            GET_TTS_ASSET[🎵 获取TTS资产信息]
            FIND_IN_CONTENT[🔍 在ContentJson中查找引用]
            VERIFY_MAPPING{🔍 验证文本和类型匹配?}
            COUNT_VALID[✅ 统计有效映射]
            COUNT_INVALID[❌ 统计无效映射]
        end

        subgraph "🏝️ 孤立资产检测"
            GET_ALL_IDS[📋 获取所有TTS资产ID]
            GET_REF_IDS[🔗 获取被引用的ID]
            FIND_ORPHANED[🏝️ 找出孤立资产<br/>all_ids - referenced_ids]
        end

        subgraph "🎯 音标类型检查"
            CHECK_PHONETIC[🔍 检查音标类型]
            STANDARD_TYPES{📋 是否为标准类型?<br/>bre/name/ipa}
            COUNT_ISSUES[📊 统计类型问题]
        end

        subgraph "📊 生成验证报告"
            COMPILE_RESULTS[📋 编译验证结果]
            GENERATE_REPORT[📄 生成Markdown报告]
            PRINT_SUMMARY[📺 打印验证摘要]
        end

        END_VAL([✅ 验证完成])

        %% 流程连接
        START_VAL --> CHECK_TABLES
        CHECK_TABLES --> GET_STATS
        GET_STATS --> GET_WORDS

        GET_WORDS --> PROCESS_HASHLIST
        PROCESS_HASHLIST --> GET_TTS_ASSET
        GET_TTS_ASSET --> FIND_IN_CONTENT
        FIND_IN_CONTENT --> VERIFY_MAPPING
        VERIFY_MAPPING -->|匹配| COUNT_VALID
        VERIFY_MAPPING -->|不匹配| COUNT_INVALID
        COUNT_VALID --> GET_ALL_IDS
        COUNT_INVALID --> GET_ALL_IDS

        GET_ALL_IDS --> GET_REF_IDS
        GET_REF_IDS --> FIND_ORPHANED

        FIND_ORPHANED --> CHECK_PHONETIC
        CHECK_PHONETIC --> STANDARD_TYPES
        STANDARD_TYPES -->|标准| COMPILE_RESULTS
        STANDARD_TYPES -->|非标准| COUNT_ISSUES
        COUNT_ISSUES --> COMPILE_RESULTS

        COMPILE_RESULTS --> GENERATE_REPORT
        GENERATE_REPORT --> PRINT_SUMMARY
        PRINT_SUMMARY --> END_VAL
    end

    %% 样式定义
    classDef startEnd fill:#FFE6CC,stroke:#000000,stroke-width:3px,color:#000000
    classDef checkProcess fill:#E8F4FD,stroke:#000000,stroke-width:2px,color:#000000
    classDef mapProcess fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef orphanProcess fill:#E1D5E7,stroke:#000000,stroke-width:2px,color:#000000
    classDef phoneticProcess fill:#F8CECC,stroke:#000000,stroke-width:2px,color:#000000
    classDef reportProcess fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000
    classDef decision fill:#FFCCCC,stroke:#000000,stroke-width:2px,color:#000000

    class START_VAL,END_VAL startEnd
    class CHECK_TABLES,GET_STATS checkProcess
    class GET_WORDS,PROCESS_HASHLIST,GET_TTS_ASSET,FIND_IN_CONTENT,COUNT_VALID,COUNT_INVALID mapProcess
    class GET_ALL_IDS,GET_REF_IDS,FIND_ORPHANED orphanProcess
    class CHECK_PHONETIC,COUNT_ISSUES phoneticProcess
    class COMPILE_RESULTS,GENERATE_REPORT,PRINT_SUMMARY reportProcess
    class VERIFY_MAPPING,STANDARD_TYPES decision
```
## 📋 真实数据示例

### 🎯 单词 "hello" 的完整处理过程

#### 1. 📥 输入数据（ContentJson v4.0）

```json
{
  "content": {
    "phoneticSymbols": [
      {
        "symbol": "/həˈləʊ/",
        "type": "BrE"  // ❌ 非标准类型
      },
      {
        "symbol": "/həˈloʊ/",
        "type": "NAmE"  // ❌ 非标准类型
      }
    ],
    "usageExamples": [
      {
        "category": "greeting",
        "examples": [
          {
            "learningLanguage": "Hello, how are you?",
            "scaffoldingLanguage": "你好，你好吗？",
            "phraseBreakdown": [
              {
                "phrase": "how are you",
                "meaning": "你好吗"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 2. 🔧 音标标准化处理

```mermaid
graph LR
    subgraph "🎯 音标类型标准化示例"
        INPUT1["📝 输入<br/>BrE → 检测为非标准"]
        PROCESS1["🔧 处理<br/>清理 → 转换 → 验证"]
        OUTPUT1["✅ 输出<br/>bre"]

        INPUT2["📝 输入<br/>NAmE → 检测为非标准"]
        PROCESS2["🔧 处理<br/>清理 → 转换 → 验证"]
        OUTPUT2["✅ 输出<br/>name"]

        INPUT1 --> PROCESS1 --> OUTPUT1
        INPUT2 --> PROCESS2 --> OUTPUT2
    end

    classDef inputStyle fill:#FFE6CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef processStyle fill:#FFF2CC,stroke:#000000,stroke-width:2px,color:#000000
    classDef outputStyle fill:#D5E8D4,stroke:#000000,stroke-width:2px,color:#000000

    class INPUT1,INPUT2 inputStyle
    class PROCESS1,PROCESS2 processStyle
    class OUTPUT1,OUTPUT2 outputStyle
```

#### 3. 🔑 TTS ID生成示例

| TTS内容类型 | 原始文本 | 标准化文本 | 发音文本 | 生成的TTS ID |
|------------|----------|------------|----------|-------------|
| 🔤 英式音标 | `/həˈləʊ/` | `en\|phonetic\|hello\|bre\|/həˈləʊ/` | `hello` | `a1b2c3d4e5f6g7h8i9j0k1l2` |
| 🔤 美式音标 | `/həˈloʊ/` | `en\|phonetic\|hello\|name\|/həˈloʊ/` | `hello` | `b2c3d4e5f6g7h8i9j0k1l2m3` |
| 💬 例句 | `Hello, how are you?` | `en\|example_sentence\|hello\|Hello, how are you?` | `Hello, how are you?` | `c3d4e5f6g7h8i9j0k1l2m3n4` |
| 📝 短语 | `how are you` | `en\|phrase_breakdown\|hello\|how are you` | `how are you` | `d4e5f6g7h8i9j0k1l2m3n4o5` |

#### 4. 📤 输出数据（更新后的ContentJson）

```json
{
  "content": {
    "phoneticSymbols": [
      {
        "symbol": "/həˈləʊ/",
        "type": "bre",  // ✅ 已标准化
        "ttsId": "a1b2c3d4e5f6g7h8i9j0k1l2"  // ✅ 新增TTS ID
      },
      {
        "symbol": "/həˈloʊ/",
        "type": "name",  // ✅ 已标准化
        "ttsId": "b2c3d4e5f6g7h8i9j0k1l2m3"  // ✅ 新增TTS ID
      }
    ],
    "usageExamples": [
      {
        "category": "greeting",
        "examples": [
          {
            "learningLanguage": "Hello, how are you?",
            "scaffoldingLanguage": "你好，你好吗？",
            "ttsId": "c3d4e5f6g7h8i9j0k1l2m3n4",  // ✅ 新增TTS ID
            "phraseBreakdown": [
              {
                "phrase": "how are you",
                "meaning": "你好吗",
                "ttsId": "d4e5f6g7h8i9j0k1l2m3n4o5"  // ✅ 新增TTS ID
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 5. 🎵 创建的TTS资产记录

```json
[
  {
    "ttsId": "a1b2c3d4e5f6g7h8i9j0k1l2",
    "originalText": "en|phonetic|hello|bre|/həˈləʊ/",
    "normalizedText": "en|phonetic|hello|bre|/həˈləʊ/",
    "textToSpeak": "hello",
    "learningLanguage": "en",
    "ttsType": "phonetic_bre",
    "status": "pending"
  },
  {
    "ttsId": "b2c3d4e5f6g7h8i9j0k1l2m3",
    "originalText": "en|phonetic|hello|name|/həˈloʊ/",
    "normalizedText": "en|phonetic|hello|name|/həˈloʊ/",
    "textToSpeak": "hello",
    "learningLanguage": "en",
    "ttsType": "phonetic_name",
    "status": "pending"
  },
  {
    "ttsId": "c3d4e5f6g7h8i9j0k1l2m3n4",
    "originalText": "en|example_sentence|hello|Hello, how are you?",
    "normalizedText": "en|example_sentence|hello|Hello, how are you?",
    "textToSpeak": "Hello, how are you?",
    "learningLanguage": "en",
    "ttsType": "example_sentence",
    "status": "pending"
  },
  {
    "ttsId": "d4e5f6g7h8i9j0k1l2m3n4o5",
    "originalText": "en|phrase_breakdown|hello|how are you",
    "normalizedText": "en|phrase_breakdown|hello|how are you",
    "textToSpeak": "how are you",
    "learningLanguage": "en",
    "ttsType": "phrase_breakdown",
    "status": "pending"
  }
]
```

## 📝 详细工作原理说明

### 🎯 1. 音标标准化逻辑详解

#### 🔍 **检测阶段**
- **目标**: 识别非标准的音标类型格式
- **检测规则**:
  - 标准类型：`bre`、`name`、`ipa`（小写）
  - 非标准类型：`BrE`、`NAmE`、`IPA`、`British`、`American`等
- **处理策略**: 使用正则表达式和关键词匹配

#### 🧹 **清理阶段**
- **移除括号内容**: `"BrE (noun)"` → `"BrE"`
- **清理特殊字符**: 移除空格、标点符号
- **转换为小写**: `"BrE"` → `"bre"`

#### 🗺️ **映射阶段**
```python
# 映射规则示例
mapping_rules = {
    'bre': ['bre', 'british', 'br'],
    'name': ['name', 'nam', 'american', 'ame', 'us'],
    'ipa': ['ipa', 'international']
}
```

#### ✅ **验证阶段**
- 确保结果为三种标准类型之一
- 默认值：如果无法识别，默认为`"name"`
- 统计转换次数和映射关系

### 🔑 2. 哈希ID生成逻辑详解

#### 📝 **文本标准化过程**
1. **组合关键信息**: `语言|类型|单词|音标类型|音标内容`
2. **示例**: `"en|phonetic|hello|bre|/həˈləʊ/"`
3. **清理处理**: 移除特殊字符，保持一致性
4. **生成上下文键**: 用于哈希计算的最终字符串

#### 🔐 **哈希生成过程**
1. **时间戳**: 获取微秒级时间戳确保唯一性
2. **盐值添加**: 结合时间戳和内容生成盐值
3. **SHA256计算**: 对组合字符串进行哈希计算
4. **截取24位**: 取哈希结果的前24个字符作为TTS ID

#### 🎯 **唯一性保证**
- **微秒时间戳**: 确保同一时刻的唯一性
- **内容差异**: 不同内容产生不同哈希
- **冲突处理**: 极低概率的冲突通过重新生成解决

### 🔍 3. 验证逻辑详解

#### 📊 **完整性验证**
- **表存在性**: 检查必需的数据库表是否存在
- **数据一致性**: 验证ContentJson中的ttsId在tts_assets表中存在
- **引用完整性**: 确保ttsHashList与实际引用的ID一致

#### 🗺️ **映射关系验证**
- **基于ttsHashList**: 从单词的ttsHashList字段获取TTS ID列表
- **TTS资产验证**: 验证每个TTS ID在tts_assets表中存在
- **ContentJson引用验证**: 在ContentJson中查找对应的TTS引用
- **文本匹配验证**: 验证ContentJson文本与TTS资产的textToSpeak一致
- **类型匹配验证**: 验证TTS类型与ContentJson上下文类型一致

#### 🎯 **类型一致性验证**
- **音标类型**: 确保所有音标类型为标准格式
- **TTS类型**: 验证TTS资产的类型字段正确性
- **语言一致性**: 检查语言字段的一致性

## 🌟 核心优势总结

### ⚡ **性能优势**
- **批处理设计**: 500条记录/批次，减少数据库连接开销
- **事务安全**: 批次级别的事务管理，确保数据一致性
- **内存优化**: 流式处理，避免大量数据同时加载到内存

### 🔒 **数据安全**
- **原子操作**: 音标标准化和TTS ID生成在同一事务中完成
- **回滚机制**: 出错时自动回滚，保护数据完整性
- **验证机制**: 多层验证确保数据质量

### 🎯 **业务价值**
- **成本优化**: 智能去重机制节省TTS生成成本
- **质量保证**: 标准化的音标类型提高数据质量
- **可维护性**: 清晰的架构和完整的日志记录

### 🔄 **扩展性**
- **模块化设计**: 三个独立脚本，职责清晰
- **配置灵活**: 支持多种运行模式和参数配置
- **集成友好**: 与看板式状态管理系统无缝集成

## 🎉 总结

TTS资产管理工作流程通过精心设计的三个核心脚本，实现了从音标标准化到TTS资产生成的完整自动化流程。该系统不仅提高了数据处理效率，还通过智能的验证和成本分析机制，为SenseWord的TTS系统提供了强大的基础设施支持。

**关键成果**:
- 🎯 **统一标准**: 音标类型标准化为bre/name/ipa三种格式
- 🔑 **唯一标识**: 24位哈希ID确保TTS资产的唯一性
- 🔍 **质量保证**: 多层验证机制确保数据完整性和一致性
- 💰 **成本优化**: 智能去重和成本分析提供优化建议

---

**文档版本**: v1.0
**创建时间**: 2025-07-14
**维护者**: AI Assistant
```
```
